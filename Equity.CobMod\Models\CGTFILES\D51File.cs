using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D51File Data Structure

public class D51File
{
    private static int _size = 128;
    // [DEBUG] Class: D51File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler219, is_external=, is_static_class=False, static_prefix=
    private string _Filler219 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD51FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler219.PadRight(128));
        
        return result.ToString();
    }
    
    public void SetD51FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 128 <= data.Length)
        {
            string extracted = data.Substring(offset, 128).Trim();
            SetFiller219(extracted);
        }
        offset += 128;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD51FileAsString();
    }
    // Set<>String Override function
    public void SetD51File(string value)
    {
        SetD51FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller219()
    {
        return _Filler219;
    }
    
    // Standard Setter
    public void SetFiller219(string value)
    {
        _Filler219 = value;
    }
    
    // Get<>AsString()
    public string GetFiller219AsString()
    {
        return _Filler219.PadRight(128);
    }
    
    // Set<>AsString()
    public void SetFiller219AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler219 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}