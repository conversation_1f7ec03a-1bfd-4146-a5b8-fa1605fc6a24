using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D71File Data Structure

public class D71File
{
    private static int _size = 12;
    // [DEBUG] Class: D71File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler224, is_external=, is_static_class=False, static_prefix=
    private string _Filler224 ="$";
    
    
    
    
    // [DEBUG] Field: D71UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D71UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler225, is_external=, is_static_class=False, static_prefix=
    private string _Filler225 ="PRL.REP";
    
    
    
    
    
    // Serialization methods
    public string GetD71FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler224.PadRight(1));
        result.Append(_D71UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler225.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD71FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller224(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD71UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller225(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD71FileAsString();
    }
    // Set<>String Override function
    public void SetD71File(string value)
    {
        SetD71FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller224()
    {
        return _Filler224;
    }
    
    // Standard Setter
    public void SetFiller224(string value)
    {
        _Filler224 = value;
    }
    
    // Get<>AsString()
    public string GetFiller224AsString()
    {
        return _Filler224.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller224AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler224 = value;
    }
    
    // Standard Getter
    public int GetD71UserNo()
    {
        return _D71UserNo;
    }
    
    // Standard Setter
    public void SetD71UserNo(int value)
    {
        _D71UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD71UserNoAsString()
    {
        return _D71UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD71UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D71UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller225()
    {
        return _Filler225;
    }
    
    // Standard Setter
    public void SetFiller225(string value)
    {
        _Filler225 = value;
    }
    
    // Get<>AsString()
    public string GetFiller225AsString()
    {
        return _Filler225.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller225AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler225 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}