using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdailyDTO
{// DTO class representing D13BalAcqDispRecord Data Structure

public class D13BalAcqDispRecord
{
    private static int _size = 3884;
    // [DEBUG] Class: D13BalAcqDispRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D13BalAcqDispCommon, is_external=, is_static_class=False, static_prefix=
    private D13BalAcqDispCommon _D13BalAcqDispCommon = new D13BalAcqDispCommon();
    
    
    
    
    // [DEBUG] Field: Filler42, is_external=, is_static_class=False, static_prefix=
    private Filler42 _Filler42 = new Filler42();
    
    
    
    
    
    // Serialization methods
    public string GetD13BalAcqDispRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D13BalAcqDispCommon.GetD13BalAcqDispCommonAsString());
        result.Append(_Filler42.GetFiller42AsString());
        
        return result.ToString();
    }
    
    public void SetD13BalAcqDispRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 203 <= data.Length)
        {
            _D13BalAcqDispCommon.SetD13BalAcqDispCommonAsString(data.Substring(offset, 203));
        }
        else
        {
            _D13BalAcqDispCommon.SetD13BalAcqDispCommonAsString(data.Substring(offset));
        }
        offset += 203;
        if (offset + 3681 <= data.Length)
        {
            _Filler42.SetFiller42AsString(data.Substring(offset, 3681));
        }
        else
        {
            _Filler42.SetFiller42AsString(data.Substring(offset));
        }
        offset += 3681;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD13BalAcqDispRecordAsString();
    }
    // Set<>String Override function
    public void SetD13BalAcqDispRecord(string value)
    {
        SetD13BalAcqDispRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D13BalAcqDispCommon GetD13BalAcqDispCommon()
    {
        return _D13BalAcqDispCommon;
    }
    
    // Standard Setter
    public void SetD13BalAcqDispCommon(D13BalAcqDispCommon value)
    {
        _D13BalAcqDispCommon = value;
    }
    
    // Get<>AsString()
    public string GetD13BalAcqDispCommonAsString()
    {
        return _D13BalAcqDispCommon != null ? _D13BalAcqDispCommon.GetD13BalAcqDispCommonAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD13BalAcqDispCommonAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D13BalAcqDispCommon == null)
        {
            _D13BalAcqDispCommon = new D13BalAcqDispCommon();
        }
        _D13BalAcqDispCommon.SetD13BalAcqDispCommonAsString(value);
    }
    
    // Standard Getter
    public Filler42 GetFiller42()
    {
        return _Filler42;
    }
    
    // Standard Setter
    public void SetFiller42(Filler42 value)
    {
        _Filler42 = value;
    }
    
    // Get<>AsString()
    public string GetFiller42AsString()
    {
        return _Filler42 != null ? _Filler42.GetFiller42AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller42AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler42 == null)
        {
            _Filler42 = new Filler42();
        }
        _Filler42.SetFiller42AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD13BalAcqDispCommon(string value)
    {
        _D13BalAcqDispCommon.SetD13BalAcqDispCommonAsString(value);
    }
    // Nested Class: D13BalAcqDispCommon
    public class D13BalAcqDispCommon
    {
        private static int _size = 203;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D132Key, is_external=, is_static_class=False, static_prefix=
        private D13BalAcqDispCommon.D132Key _D132Key = new D13BalAcqDispCommon.D132Key();
        
        
        
        
        // [DEBUG] Field: D132TransactionCategory, is_external=, is_static_class=False, static_prefix=
        private string _D132TransactionCategory ="";
        
        
        // 88-level condition checks for D132TransactionCategory
        public bool IsD132IdentifiableTran()
        {
            if (this._D132TransactionCategory == "'00'") return true;
            if (this._D132TransactionCategory == "'SP'") return true;
            if (this._D132TransactionCategory == "'PP'") return true;
            if (this._D132TransactionCategory == "'TB'") return true;
            if (this._D132TransactionCategory == "'NI'") return true;
            if (this._D132TransactionCategory == "'RP'") return true;
            if (this._D132TransactionCategory == "'UL'") return true;
            if (this._D132TransactionCategory == "'01'") return true;
            if (this._D132TransactionCategory == "'SX'") return true;
            return false;
        }
        public bool IsD132ApportionedTran()
        {
            if (this._D132TransactionCategory == "'RG'") return true;
            if (this._D132TransactionCategory == "'CN'") return true;
            if (this._D132TransactionCategory == "'TR'") return true;
            if (this._D132TransactionCategory == "'RO'") return true;
            if (this._D132TransactionCategory == "'TD'") return true;
            if (this._D132TransactionCategory == "'TS'") return true;
            if (this._D132TransactionCategory == "'CS'") return true;
            if (this._D132TransactionCategory == "'CD'") return true;
            if (this._D132TransactionCategory == "'RF'") return true;
            return false;
        }
        public bool IsD132ApportionedTransfer()
        {
            if (this._D132TransactionCategory == "'RG'") return true;
            if (this._D132TransactionCategory == "'CN'") return true;
            if (this._D132TransactionCategory == "'TR'") return true;
            if (this._D132TransactionCategory == "'TS'") return true;
            if (this._D132TransactionCategory == "'CS'") return true;
            if (this._D132TransactionCategory == "'TD'") return true;
            if (this._D132TransactionCategory == "'RO'") return true;
            return false;
        }
        public bool IsD132TransToBeConsolidated()
        {
            if (this._D132TransactionCategory == "'RG'") return true;
            if (this._D132TransactionCategory == "'CN'") return true;
            if (this._D132TransactionCategory == "'TR'") return true;
            if (this._D132TransactionCategory == "'TS'") return true;
            if (this._D132TransactionCategory == "'CS'") return true;
            if (this._D132TransactionCategory == "'RO'") return true;
            if (this._D132TransactionCategory == "'TD'") return true;
            if (this._D132TransactionCategory == "'TP'") return true;
            if (this._D132TransactionCategory == "'CP'") return true;
            if (this._D132TransactionCategory == "'GP'") return true;
            if (this._D132TransactionCategory == "'GS'") return true;
            if (this._D132TransactionCategory == "'RS'") return true;
            if (this._D132TransactionCategory == "'RL'") return true;
            if (this._D132TransactionCategory == "'RD'") return true;
            if (this._D132TransactionCategory == "'CD'") return true;
            if (this._D132TransactionCategory == "'LQ'") return true;
            if (this._D132TransactionCategory == "'RF'") return true;
            if (this._D132TransactionCategory == "'SS'") return true;
            return false;
        }
        public bool IsD132DisposalTran()
        {
            if (this._D132TransactionCategory == "'RS'") return true;
            if (this._D132TransactionCategory == "'RL'") return true;
            if (this._D132TransactionCategory == "'RD'") return true;
            if (this._D132TransactionCategory == "'CD'") return true;
            if (this._D132TransactionCategory == "'LQ'") return true;
            if (this._D132TransactionCategory == "'RF'") return true;
            if (this._D132TransactionCategory == "'SS'") return true;
            return false;
        }
        public bool IsD132TransLinkedByBargain()
        {
            if (this._D132TransactionCategory == "'TS'") return true;
            if (this._D132TransactionCategory == "'CS'") return true;
            if (this._D132TransactionCategory == "'TP'") return true;
            if (this._D132TransactionCategory == "'CP'") return true;
            if (this._D132TransactionCategory == "'GP'") return true;
            if (this._D132TransactionCategory == "'GS'") return true;
            return false;
        }
        public bool IsD132TransLinkedByPrevRef()
        {
            if (this._D132TransactionCategory == "'RG'") return true;
            if (this._D132TransactionCategory == "'CN'") return true;
            if (this._D132TransactionCategory == "'TR'") return true;
            if (this._D132TransactionCategory == "'RO'") return true;
            if (this._D132TransactionCategory == "'TD'") return true;
            if (this._D132TransactionCategory == "'CL'") return true;
            if (this._D132TransactionCategory == "'RC'") return true;
            if (this._D132TransactionCategory == "'RR'") return true;
            return false;
        }
        public bool IsD132Balance()
        {
            if (this._D132TransactionCategory == "'00'") return true;
            if (this._D132TransactionCategory == "'PP'") return true;
            if (this._D132TransactionCategory == "'TB'") return true;
            if (this._D132TransactionCategory == "'01'") return true;
            return false;
        }
        public bool IsD132SingleAcqnTrans()
        {
            if (this._D132TransactionCategory == "'SP'") return true;
            if (this._D132TransactionCategory == "'SX'") return true;
            if (this._D132TransactionCategory == "'NI'") return true;
            if (this._D132TransactionCategory == "'NP'") return true;
            if (this._D132TransactionCategory == "'PI'") return true;
            if (this._D132TransactionCategory == "'RP'") return true;
            if (this._D132TransactionCategory == "'BA'") return true;
            if (this._D132TransactionCategory == "'RA'") return true;
            if (this._D132TransactionCategory == "'AC'") return true;
            return false;
        }
        public bool IsD132SingleDispTrans()
        {
            if (this._D132TransactionCategory == "'SS'") return true;
            if (this._D132TransactionCategory == "'RD'") return true;
            if (this._D132TransactionCategory == "'CD'") return true;
            if (this._D132TransactionCategory == "'LQ'") return true;
            if (this._D132TransactionCategory == "'RS'") return true;
            if (this._D132TransactionCategory == "'RL'") return true;
            if (this._D132TransactionCategory == "'RF'") return true;
            if (this._D132TransactionCategory == "'DS'") return true;
            if (this._D132TransactionCategory == "'EQ'") return true;
            if (this._D132TransactionCategory == "'NS'") return true;
            return false;
        }
        public bool IsD132DualTransNonEx()
        {
            if (this._D132TransactionCategory == "'RC'") return true;
            if (this._D132TransactionCategory == "'CL'") return true;
            if (this._D132TransactionCategory == "'RR'") return true;
            if (this._D132TransactionCategory == "'GT'") return true;
            if (this._D132TransactionCategory == "'CT'") return true;
            return false;
        }
        public bool IsD132DualTransExE()
        {
            if (this._D132TransactionCategory == "'EC'") return true;
            if (this._D132TransactionCategory == "'EP'") return true;
            return false;
        }
        public bool IsD132DualTransExW()
        {
            if (this._D132TransactionCategory == "'WC'") return true;
            if (this._D132TransactionCategory == "'WP'") return true;
            return false;
        }
        public bool IsD132MultiTrans()
        {
            if (this._D132TransactionCategory == "'RG'") return true;
            if (this._D132TransactionCategory == "'CN'") return true;
            if (this._D132TransactionCategory == "'CO'") return true;
            if (this._D132TransactionCategory == "'TD'") return true;
            if (this._D132TransactionCategory == "'TO'") return true;
            if (this._D132TransactionCategory == "'TR'") return true;
            if (this._D132TransactionCategory == "'RO'") return true;
            return false;
        }
        public bool IsD132DualTransExEp()
        {
            if (this._D132TransactionCategory == "'EP'") return true;
            return false;
        }
        public bool IsD132DualTransExWc()
        {
            if (this._D132TransactionCategory == "'WC'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: D132DateTimeStamp, is_external=, is_static_class=False, static_prefix=
        private D13BalAcqDispCommon.D132DateTimeStamp _D132DateTimeStamp = new D13BalAcqDispCommon.D132DateTimeStamp();
        
        
        
        
        // [DEBUG] Field: Filler41, is_external=, is_static_class=False, static_prefix=
        private D13BalAcqDispCommon.Filler41 _Filler41 = new D13BalAcqDispCommon.Filler41();
        
        
        
        
        // [DEBUG] Field: D132CalParentSedolCode, is_external=, is_static_class=False, static_prefix=
        private D13BalAcqDispCommon.D132CalParentSedolCode _D132CalParentSedolCode = new D13BalAcqDispCommon.D132CalParentSedolCode();
        
        
        
        
        // [DEBUG] Field: D132CalPreviousSedolCode, is_external=, is_static_class=False, static_prefix=
        private D13BalAcqDispCommon.D132CalPreviousSedolCode _D132CalPreviousSedolCode = new D13BalAcqDispCommon.D132CalPreviousSedolCode();
        
        
        
        
        // [DEBUG] Field: D132OriginalBargainNo, is_external=, is_static_class=False, static_prefix=
        private D13BalAcqDispCommon.D132OriginalBargainNo _D132OriginalBargainNo = new D13BalAcqDispCommon.D132OriginalBargainNo();
        
        
        
        
        // [DEBUG] Field: D132BargainDate, is_external=, is_static_class=False, static_prefix=
        private D13BalAcqDispCommon.D132BargainDate _D132BargainDate = new D13BalAcqDispCommon.D132BargainDate();
        
        
        
        
        // [DEBUG] Field: D132BargainDate9, is_external=, is_static_class=False, static_prefix=
        private int _D132BargainDate9 =0;
        
        
        
        
        // [DEBUG] Field: D132SettlementDate, is_external=, is_static_class=False, static_prefix=
        private D13BalAcqDispCommon.D132SettlementDate _D132SettlementDate = new D13BalAcqDispCommon.D132SettlementDate();
        
        
        
        
        // [DEBUG] Field: D132CurrencyCode, is_external=, is_static_class=False, static_prefix=
        private string _D132CurrencyCode ="";
        
        
        
        
        // [DEBUG] Field: D132NotesComments, is_external=, is_static_class=False, static_prefix=
        private string _D132NotesComments ="";
        
        
        
        
        // [DEBUG] Field: D132BfNiPiFlag, is_external=, is_static_class=False, static_prefix=
        private string _D132BfNiPiFlag ="";
        
        
        
        
        // [DEBUG] Field: D132NiPiFlagYtd, is_external=, is_static_class=False, static_prefix=
        private string _D132NiPiFlagYtd ="";
        
        
        
        
        // [DEBUG] Field: D132TransactionExported, is_external=, is_static_class=False, static_prefix=
        private string _D132TransactionExported ="";
        
        
        
        
        // [DEBUG] Field: D132CtLinkFund, is_external=, is_static_class=False, static_prefix=
        private string _D132CtLinkFund ="";
        
        
        
        
        // [DEBUG] Field: D132SubFund, is_external=, is_static_class=False, static_prefix=
        private string _D132SubFund ="";
        
        
        
        
        // [DEBUG] Field: D132TransactionOverride, is_external=, is_static_class=False, static_prefix=
        private string _D132TransactionOverride ="";
        
        
        
        
        // [DEBUG] Field: D132ParentMarketPrice, is_external=, is_static_class=False, static_prefix=
        private decimal _D132ParentMarketPrice =0;
        
        
        
        
        // [DEBUG] Field: D132TransUnitPrice, is_external=, is_static_class=False, static_prefix=
        private decimal _D132TransUnitPrice =0;
        
        
        
        
        // [DEBUG] Field: D132PricePctIndicator, is_external=, is_static_class=False, static_prefix=
        private string _D132PricePctIndicator ="";
        
        
        
        
        // [DEBUG] Field: D132LiabilityPerShare, is_external=, is_static_class=False, static_prefix=
        private decimal _D132LiabilityPerShare =0;
        
        
        
        
        // [DEBUG] Field: D132OutstandingLiability, is_external=, is_static_class=False, static_prefix=
        private decimal _D132OutstandingLiability =0;
        
        
        
        
        // [DEBUG] Field: D132StockExchIndicator, is_external=, is_static_class=False, static_prefix=
        private string _D132StockExchIndicator ="";
        
        
        
        
    public D13BalAcqDispCommon() {}
    
    public D13BalAcqDispCommon(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _D132Key.SetD132KeyAsString(data.Substring(offset, D132Key.GetSize()));
        offset += 25;
        SetD132TransactionCategory(data.Substring(offset, 2).Trim());
        offset += 2;
        _D132DateTimeStamp.SetD132DateTimeStampAsString(data.Substring(offset, D132DateTimeStamp.GetSize()));
        offset += 14;
        _Filler41.SetFiller41AsString(data.Substring(offset, Filler41.GetSize()));
        offset += 14;
        _D132CalParentSedolCode.SetD132CalParentSedolCodeAsString(data.Substring(offset, D132CalParentSedolCode.GetSize()));
        offset += 11;
        _D132CalPreviousSedolCode.SetD132CalPreviousSedolCodeAsString(data.Substring(offset, D132CalPreviousSedolCode.GetSize()));
        offset += 11;
        _D132OriginalBargainNo.SetD132OriginalBargainNoAsString(data.Substring(offset, D132OriginalBargainNo.GetSize()));
        offset += 10;
        _D132BargainDate.SetD132BargainDateAsString(data.Substring(offset, D132BargainDate.GetSize()));
        offset += 6;
        SetD132BargainDate9(int.Parse(data.Substring(offset, 6).Trim()));
        offset += 6;
        _D132SettlementDate.SetD132SettlementDateAsString(data.Substring(offset, D132SettlementDate.GetSize()));
        offset += 6;
        SetD132CurrencyCode(data.Substring(offset, 3).Trim());
        offset += 3;
        SetD132NotesComments(data.Substring(offset, 36).Trim());
        offset += 36;
        SetD132BfNiPiFlag(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD132NiPiFlagYtd(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD132TransactionExported(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD132CtLinkFund(data.Substring(offset, 4).Trim());
        offset += 4;
        SetD132SubFund(data.Substring(offset, 4).Trim());
        offset += 4;
        SetD132TransactionOverride(data.Substring(offset, 1).Trim());
        offset += 1;
        SetD132ParentMarketPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetD132TransUnitPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
        offset += 11;
        SetD132PricePctIndicator(data.Substring(offset, 1).Trim());
        offset += 1;
        SetD132LiabilityPerShare(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
        offset += 11;
        SetD132OutstandingLiability(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
        offset += 11;
        SetD132StockExchIndicator(data.Substring(offset, 1).Trim());
        offset += 1;
        
    }
    
    // Serialization methods
    public string GetD13BalAcqDispCommonAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D132Key.GetD132KeyAsString());
        result.Append(_D132TransactionCategory.PadRight(2));
        result.Append(_D132DateTimeStamp.GetD132DateTimeStampAsString());
        result.Append(_Filler41.GetFiller41AsString());
        result.Append(_D132CalParentSedolCode.GetD132CalParentSedolCodeAsString());
        result.Append(_D132CalPreviousSedolCode.GetD132CalPreviousSedolCodeAsString());
        result.Append(_D132OriginalBargainNo.GetD132OriginalBargainNoAsString());
        result.Append(_D132BargainDate.GetD132BargainDateAsString());
        result.Append(_D132BargainDate9.ToString().PadLeft(6, '0'));
        result.Append(_D132SettlementDate.GetD132SettlementDateAsString());
        result.Append(_D132CurrencyCode.PadRight(3));
        result.Append(_D132NotesComments.PadRight(36));
        result.Append(_D132BfNiPiFlag.PadRight(0));
        result.Append(_D132NiPiFlagYtd.PadRight(0));
        result.Append(_D132TransactionExported.PadRight(0));
        result.Append(_D132CtLinkFund.PadRight(4));
        result.Append(_D132SubFund.PadRight(4));
        result.Append(_D132TransactionOverride.PadRight(1));
        result.Append(_D132ParentMarketPrice.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D132TransUnitPrice.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D132PricePctIndicator.PadRight(1));
        result.Append(_D132LiabilityPerShare.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D132OutstandingLiability.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D132StockExchIndicator.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetD13BalAcqDispCommonAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 25 <= data.Length)
        {
            _D132Key.SetD132KeyAsString(data.Substring(offset, 25));
        }
        else
        {
            _D132Key.SetD132KeyAsString(data.Substring(offset));
        }
        offset += 25;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD132TransactionCategory(extracted);
        }
        offset += 2;
        if (offset + 14 <= data.Length)
        {
            _D132DateTimeStamp.SetD132DateTimeStampAsString(data.Substring(offset, 14));
        }
        else
        {
            _D132DateTimeStamp.SetD132DateTimeStampAsString(data.Substring(offset));
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            _Filler41.SetFiller41AsString(data.Substring(offset, 14));
        }
        else
        {
            _Filler41.SetFiller41AsString(data.Substring(offset));
        }
        offset += 14;
        if (offset + 11 <= data.Length)
        {
            _D132CalParentSedolCode.SetD132CalParentSedolCodeAsString(data.Substring(offset, 11));
        }
        else
        {
            _D132CalParentSedolCode.SetD132CalParentSedolCodeAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            _D132CalPreviousSedolCode.SetD132CalPreviousSedolCodeAsString(data.Substring(offset, 11));
        }
        else
        {
            _D132CalPreviousSedolCode.SetD132CalPreviousSedolCodeAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 10 <= data.Length)
        {
            _D132OriginalBargainNo.SetD132OriginalBargainNoAsString(data.Substring(offset, 10));
        }
        else
        {
            _D132OriginalBargainNo.SetD132OriginalBargainNoAsString(data.Substring(offset));
        }
        offset += 10;
        if (offset + 6 <= data.Length)
        {
            _D132BargainDate.SetD132BargainDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _D132BargainDate.SetD132BargainDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD132BargainDate9(parsedInt);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            _D132SettlementDate.SetD132SettlementDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _D132SettlementDate.SetD132SettlementDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD132CurrencyCode(extracted);
        }
        offset += 3;
        if (offset + 36 <= data.Length)
        {
            string extracted = data.Substring(offset, 36).Trim();
            SetD132NotesComments(extracted);
        }
        offset += 36;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD132BfNiPiFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD132NiPiFlagYtd(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD132TransactionExported(extracted);
        }
        offset += 0;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD132CtLinkFund(extracted);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD132SubFund(extracted);
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD132TransactionOverride(extracted);
        }
        offset += 1;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD132ParentMarketPrice(parsedDec);
        }
        offset += 15;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD132TransUnitPrice(parsedDec);
        }
        offset += 11;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD132PricePctIndicator(extracted);
        }
        offset += 1;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD132LiabilityPerShare(parsedDec);
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD132OutstandingLiability(parsedDec);
        }
        offset += 11;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD132StockExchIndicator(extracted);
        }
        offset += 1;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public D132Key GetD132Key()
    {
        return _D132Key;
    }
    
    // Standard Setter
    public void SetD132Key(D132Key value)
    {
        _D132Key = value;
    }
    
    // Get<>AsString()
    public string GetD132KeyAsString()
    {
        return _D132Key != null ? _D132Key.GetD132KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD132KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D132Key == null)
        {
            _D132Key = new D132Key();
        }
        _D132Key.SetD132KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD132TransactionCategory()
    {
        return _D132TransactionCategory;
    }
    
    // Standard Setter
    public void SetD132TransactionCategory(string value)
    {
        _D132TransactionCategory = value;
    }
    
    // Get<>AsString()
    public string GetD132TransactionCategoryAsString()
    {
        return _D132TransactionCategory.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD132TransactionCategoryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D132TransactionCategory = value;
    }
    
    // Standard Getter
    public D132DateTimeStamp GetD132DateTimeStamp()
    {
        return _D132DateTimeStamp;
    }
    
    // Standard Setter
    public void SetD132DateTimeStamp(D132DateTimeStamp value)
    {
        _D132DateTimeStamp = value;
    }
    
    // Get<>AsString()
    public string GetD132DateTimeStampAsString()
    {
        return _D132DateTimeStamp != null ? _D132DateTimeStamp.GetD132DateTimeStampAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD132DateTimeStampAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D132DateTimeStamp == null)
        {
            _D132DateTimeStamp = new D132DateTimeStamp();
        }
        _D132DateTimeStamp.SetD132DateTimeStampAsString(value);
    }
    
    // Standard Getter
    public Filler41 GetFiller41()
    {
        return _Filler41;
    }
    
    // Standard Setter
    public void SetFiller41(Filler41 value)
    {
        _Filler41 = value;
    }
    
    // Get<>AsString()
    public string GetFiller41AsString()
    {
        return _Filler41 != null ? _Filler41.GetFiller41AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller41AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler41 == null)
        {
            _Filler41 = new Filler41();
        }
        _Filler41.SetFiller41AsString(value);
    }
    
    // Standard Getter
    public D132CalParentSedolCode GetD132CalParentSedolCode()
    {
        return _D132CalParentSedolCode;
    }
    
    // Standard Setter
    public void SetD132CalParentSedolCode(D132CalParentSedolCode value)
    {
        _D132CalParentSedolCode = value;
    }
    
    // Get<>AsString()
    public string GetD132CalParentSedolCodeAsString()
    {
        return _D132CalParentSedolCode != null ? _D132CalParentSedolCode.GetD132CalParentSedolCodeAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD132CalParentSedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D132CalParentSedolCode == null)
        {
            _D132CalParentSedolCode = new D132CalParentSedolCode();
        }
        _D132CalParentSedolCode.SetD132CalParentSedolCodeAsString(value);
    }
    
    // Standard Getter
    public D132CalPreviousSedolCode GetD132CalPreviousSedolCode()
    {
        return _D132CalPreviousSedolCode;
    }
    
    // Standard Setter
    public void SetD132CalPreviousSedolCode(D132CalPreviousSedolCode value)
    {
        _D132CalPreviousSedolCode = value;
    }
    
    // Get<>AsString()
    public string GetD132CalPreviousSedolCodeAsString()
    {
        return _D132CalPreviousSedolCode != null ? _D132CalPreviousSedolCode.GetD132CalPreviousSedolCodeAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD132CalPreviousSedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D132CalPreviousSedolCode == null)
        {
            _D132CalPreviousSedolCode = new D132CalPreviousSedolCode();
        }
        _D132CalPreviousSedolCode.SetD132CalPreviousSedolCodeAsString(value);
    }
    
    // Standard Getter
    public D132OriginalBargainNo GetD132OriginalBargainNo()
    {
        return _D132OriginalBargainNo;
    }
    
    // Standard Setter
    public void SetD132OriginalBargainNo(D132OriginalBargainNo value)
    {
        _D132OriginalBargainNo = value;
    }
    
    // Get<>AsString()
    public string GetD132OriginalBargainNoAsString()
    {
        return _D132OriginalBargainNo != null ? _D132OriginalBargainNo.GetD132OriginalBargainNoAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD132OriginalBargainNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D132OriginalBargainNo == null)
        {
            _D132OriginalBargainNo = new D132OriginalBargainNo();
        }
        _D132OriginalBargainNo.SetD132OriginalBargainNoAsString(value);
    }
    
    // Standard Getter
    public D132BargainDate GetD132BargainDate()
    {
        return _D132BargainDate;
    }
    
    // Standard Setter
    public void SetD132BargainDate(D132BargainDate value)
    {
        _D132BargainDate = value;
    }
    
    // Get<>AsString()
    public string GetD132BargainDateAsString()
    {
        return _D132BargainDate != null ? _D132BargainDate.GetD132BargainDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD132BargainDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D132BargainDate == null)
        {
            _D132BargainDate = new D132BargainDate();
        }
        _D132BargainDate.SetD132BargainDateAsString(value);
    }
    
    // Standard Getter
    public int GetD132BargainDate9()
    {
        return _D132BargainDate9;
    }
    
    // Standard Setter
    public void SetD132BargainDate9(int value)
    {
        _D132BargainDate9 = value;
    }
    
    // Get<>AsString()
    public string GetD132BargainDate9AsString()
    {
        return _D132BargainDate9.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD132BargainDate9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D132BargainDate9 = parsed;
    }
    
    // Standard Getter
    public D132SettlementDate GetD132SettlementDate()
    {
        return _D132SettlementDate;
    }
    
    // Standard Setter
    public void SetD132SettlementDate(D132SettlementDate value)
    {
        _D132SettlementDate = value;
    }
    
    // Get<>AsString()
    public string GetD132SettlementDateAsString()
    {
        return _D132SettlementDate != null ? _D132SettlementDate.GetD132SettlementDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD132SettlementDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D132SettlementDate == null)
        {
            _D132SettlementDate = new D132SettlementDate();
        }
        _D132SettlementDate.SetD132SettlementDateAsString(value);
    }
    
    // Standard Getter
    public string GetD132CurrencyCode()
    {
        return _D132CurrencyCode;
    }
    
    // Standard Setter
    public void SetD132CurrencyCode(string value)
    {
        _D132CurrencyCode = value;
    }
    
    // Get<>AsString()
    public string GetD132CurrencyCodeAsString()
    {
        return _D132CurrencyCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD132CurrencyCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D132CurrencyCode = value;
    }
    
    // Standard Getter
    public string GetD132NotesComments()
    {
        return _D132NotesComments;
    }
    
    // Standard Setter
    public void SetD132NotesComments(string value)
    {
        _D132NotesComments = value;
    }
    
    // Get<>AsString()
    public string GetD132NotesCommentsAsString()
    {
        return _D132NotesComments.PadRight(36);
    }
    
    // Set<>AsString()
    public void SetD132NotesCommentsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D132NotesComments = value;
    }
    
    // Standard Getter
    public string GetD132BfNiPiFlag()
    {
        return _D132BfNiPiFlag;
    }
    
    // Standard Setter
    public void SetD132BfNiPiFlag(string value)
    {
        _D132BfNiPiFlag = value;
    }
    
    // Get<>AsString()
    public string GetD132BfNiPiFlagAsString()
    {
        return _D132BfNiPiFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD132BfNiPiFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D132BfNiPiFlag = value;
    }
    
    // Standard Getter
    public string GetD132NiPiFlagYtd()
    {
        return _D132NiPiFlagYtd;
    }
    
    // Standard Setter
    public void SetD132NiPiFlagYtd(string value)
    {
        _D132NiPiFlagYtd = value;
    }
    
    // Get<>AsString()
    public string GetD132NiPiFlagYtdAsString()
    {
        return _D132NiPiFlagYtd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD132NiPiFlagYtdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D132NiPiFlagYtd = value;
    }
    
    // Standard Getter
    public string GetD132TransactionExported()
    {
        return _D132TransactionExported;
    }
    
    // Standard Setter
    public void SetD132TransactionExported(string value)
    {
        _D132TransactionExported = value;
    }
    
    // Get<>AsString()
    public string GetD132TransactionExportedAsString()
    {
        return _D132TransactionExported.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD132TransactionExportedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D132TransactionExported = value;
    }
    
    // Standard Getter
    public string GetD132CtLinkFund()
    {
        return _D132CtLinkFund;
    }
    
    // Standard Setter
    public void SetD132CtLinkFund(string value)
    {
        _D132CtLinkFund = value;
    }
    
    // Get<>AsString()
    public string GetD132CtLinkFundAsString()
    {
        return _D132CtLinkFund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD132CtLinkFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D132CtLinkFund = value;
    }
    
    // Standard Getter
    public string GetD132SubFund()
    {
        return _D132SubFund;
    }
    
    // Standard Setter
    public void SetD132SubFund(string value)
    {
        _D132SubFund = value;
    }
    
    // Get<>AsString()
    public string GetD132SubFundAsString()
    {
        return _D132SubFund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD132SubFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D132SubFund = value;
    }
    
    // Standard Getter
    public string GetD132TransactionOverride()
    {
        return _D132TransactionOverride;
    }
    
    // Standard Setter
    public void SetD132TransactionOverride(string value)
    {
        _D132TransactionOverride = value;
    }
    
    // Get<>AsString()
    public string GetD132TransactionOverrideAsString()
    {
        return _D132TransactionOverride.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD132TransactionOverrideAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D132TransactionOverride = value;
    }
    
    // Standard Getter
    public decimal GetD132ParentMarketPrice()
    {
        return _D132ParentMarketPrice;
    }
    
    // Standard Setter
    public void SetD132ParentMarketPrice(decimal value)
    {
        _D132ParentMarketPrice = value;
    }
    
    // Get<>AsString()
    public string GetD132ParentMarketPriceAsString()
    {
        return _D132ParentMarketPrice.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD132ParentMarketPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132ParentMarketPrice = parsed;
    }
    
    // Standard Getter
    public decimal GetD132TransUnitPrice()
    {
        return _D132TransUnitPrice;
    }
    
    // Standard Setter
    public void SetD132TransUnitPrice(decimal value)
    {
        _D132TransUnitPrice = value;
    }
    
    // Get<>AsString()
    public string GetD132TransUnitPriceAsString()
    {
        return _D132TransUnitPrice.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD132TransUnitPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132TransUnitPrice = parsed;
    }
    
    // Standard Getter
    public string GetD132PricePctIndicator()
    {
        return _D132PricePctIndicator;
    }
    
    // Standard Setter
    public void SetD132PricePctIndicator(string value)
    {
        _D132PricePctIndicator = value;
    }
    
    // Get<>AsString()
    public string GetD132PricePctIndicatorAsString()
    {
        return _D132PricePctIndicator.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD132PricePctIndicatorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D132PricePctIndicator = value;
    }
    
    // Standard Getter
    public decimal GetD132LiabilityPerShare()
    {
        return _D132LiabilityPerShare;
    }
    
    // Standard Setter
    public void SetD132LiabilityPerShare(decimal value)
    {
        _D132LiabilityPerShare = value;
    }
    
    // Get<>AsString()
    public string GetD132LiabilityPerShareAsString()
    {
        return _D132LiabilityPerShare.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD132LiabilityPerShareAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132LiabilityPerShare = parsed;
    }
    
    // Standard Getter
    public decimal GetD132OutstandingLiability()
    {
        return _D132OutstandingLiability;
    }
    
    // Standard Setter
    public void SetD132OutstandingLiability(decimal value)
    {
        _D132OutstandingLiability = value;
    }
    
    // Get<>AsString()
    public string GetD132OutstandingLiabilityAsString()
    {
        return _D132OutstandingLiability.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD132OutstandingLiabilityAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132OutstandingLiability = parsed;
    }
    
    // Standard Getter
    public string GetD132StockExchIndicator()
    {
        return _D132StockExchIndicator;
    }
    
    // Standard Setter
    public void SetD132StockExchIndicator(string value)
    {
        _D132StockExchIndicator = value;
    }
    
    // Get<>AsString()
    public string GetD132StockExchIndicatorAsString()
    {
        return _D132StockExchIndicator.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD132StockExchIndicatorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D132StockExchIndicator = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D132Key
    public class D132Key
    {
        private static int _size = 25;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D132CalSedol, is_external=, is_static_class=False, static_prefix=
        private D132Key.D132CalSedol _D132CalSedol = new D132Key.D132CalSedol();
        
        
        
        
        // [DEBUG] Field: D132ContractNo, is_external=, is_static_class=False, static_prefix=
        private D132Key.D132ContractNo _D132ContractNo = new D132Key.D132ContractNo();
        
        
        
        
        // [DEBUG] Field: D132RecordCode, is_external=, is_static_class=False, static_prefix=
        private int _D132RecordCode =0;
        
        
        
        
        // [DEBUG] Field: D132RecordCodeX, is_external=, is_static_class=False, static_prefix=
        private D132Key.D132RecordCodeX _D132RecordCodeX = new D132Key.D132RecordCodeX();
        
        
        
        
    public D132Key() {}
    
    public D132Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _D132CalSedol.SetD132CalSedolAsString(data.Substring(offset, D132CalSedol.GetSize()));
        offset += 11;
        _D132ContractNo.SetD132ContractNoAsString(data.Substring(offset, D132ContractNo.GetSize()));
        offset += 10;
        SetD132RecordCode(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        _D132RecordCodeX.SetD132RecordCodeXAsString(data.Substring(offset, D132RecordCodeX.GetSize()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetD132KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D132CalSedol.GetD132CalSedolAsString());
        result.Append(_D132ContractNo.GetD132ContractNoAsString());
        result.Append(_D132RecordCode.ToString().PadLeft(2, '0'));
        result.Append(_D132RecordCodeX.GetD132RecordCodeXAsString());
        
        return result.ToString();
    }
    
    public void SetD132KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 11 <= data.Length)
        {
            _D132CalSedol.SetD132CalSedolAsString(data.Substring(offset, 11));
        }
        else
        {
            _D132CalSedol.SetD132CalSedolAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 10 <= data.Length)
        {
            _D132ContractNo.SetD132ContractNoAsString(data.Substring(offset, 10));
        }
        else
        {
            _D132ContractNo.SetD132ContractNoAsString(data.Substring(offset));
        }
        offset += 10;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD132RecordCode(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            _D132RecordCodeX.SetD132RecordCodeXAsString(data.Substring(offset, 2));
        }
        else
        {
            _D132RecordCodeX.SetD132RecordCodeXAsString(data.Substring(offset));
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public D132CalSedol GetD132CalSedol()
    {
        return _D132CalSedol;
    }
    
    // Standard Setter
    public void SetD132CalSedol(D132CalSedol value)
    {
        _D132CalSedol = value;
    }
    
    // Get<>AsString()
    public string GetD132CalSedolAsString()
    {
        return _D132CalSedol != null ? _D132CalSedol.GetD132CalSedolAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD132CalSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D132CalSedol == null)
        {
            _D132CalSedol = new D132CalSedol();
        }
        _D132CalSedol.SetD132CalSedolAsString(value);
    }
    
    // Standard Getter
    public D132ContractNo GetD132ContractNo()
    {
        return _D132ContractNo;
    }
    
    // Standard Setter
    public void SetD132ContractNo(D132ContractNo value)
    {
        _D132ContractNo = value;
    }
    
    // Get<>AsString()
    public string GetD132ContractNoAsString()
    {
        return _D132ContractNo != null ? _D132ContractNo.GetD132ContractNoAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD132ContractNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D132ContractNo == null)
        {
            _D132ContractNo = new D132ContractNo();
        }
        _D132ContractNo.SetD132ContractNoAsString(value);
    }
    
    // Standard Getter
    public int GetD132RecordCode()
    {
        return _D132RecordCode;
    }
    
    // Standard Setter
    public void SetD132RecordCode(int value)
    {
        _D132RecordCode = value;
    }
    
    // Get<>AsString()
    public string GetD132RecordCodeAsString()
    {
        return _D132RecordCode.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD132RecordCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D132RecordCode = parsed;
    }
    
    // Standard Getter
    public D132RecordCodeX GetD132RecordCodeX()
    {
        return _D132RecordCodeX;
    }
    
    // Standard Setter
    public void SetD132RecordCodeX(D132RecordCodeX value)
    {
        _D132RecordCodeX = value;
    }
    
    // Get<>AsString()
    public string GetD132RecordCodeXAsString()
    {
        return _D132RecordCodeX != null ? _D132RecordCodeX.GetD132RecordCodeXAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD132RecordCodeXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D132RecordCodeX == null)
        {
            _D132RecordCodeX = new D132RecordCodeX();
        }
        _D132RecordCodeX.SetD132RecordCodeXAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D132CalSedol
    public class D132CalSedol
    {
        private static int _size = 11;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D132CoAcLk, is_external=, is_static_class=False, static_prefix=
        private string _D132CoAcLk ="";
        
        
        
        
        // [DEBUG] Field: D132Sedol, is_external=, is_static_class=False, static_prefix=
        private string _D132Sedol ="";
        
        
        
        
    public D132CalSedol() {}
    
    public D132CalSedol(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD132CoAcLk(data.Substring(offset, 4).Trim());
        offset += 4;
        SetD132Sedol(data.Substring(offset, 7).Trim());
        offset += 7;
        
    }
    
    // Serialization methods
    public string GetD132CalSedolAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D132CoAcLk.PadRight(4));
        result.Append(_D132Sedol.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD132CalSedolAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD132CoAcLk(extracted);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetD132Sedol(extracted);
        }
        offset += 7;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD132CoAcLk()
    {
        return _D132CoAcLk;
    }
    
    // Standard Setter
    public void SetD132CoAcLk(string value)
    {
        _D132CoAcLk = value;
    }
    
    // Get<>AsString()
    public string GetD132CoAcLkAsString()
    {
        return _D132CoAcLk.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD132CoAcLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D132CoAcLk = value;
    }
    
    // Standard Getter
    public string GetD132Sedol()
    {
        return _D132Sedol;
    }
    
    // Standard Setter
    public void SetD132Sedol(string value)
    {
        _D132Sedol = value;
    }
    
    // Get<>AsString()
    public string GetD132SedolAsString()
    {
        return _D132Sedol.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetD132SedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D132Sedol = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: D132ContractNo
public class D132ContractNo
{
    private static int _size = 10;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132ContractPrefix, is_external=, is_static_class=False, static_prefix=
    private string _D132ContractPrefix ="";
    
    
    
    
    // [DEBUG] Field: D132ContractSuffix, is_external=, is_static_class=False, static_prefix=
    private string _D132ContractSuffix ="";
    
    
    
    
public D132ContractNo() {}

public D132ContractNo(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD132ContractPrefix(data.Substring(offset, 7).Trim());
    offset += 7;
    SetD132ContractSuffix(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetD132ContractNoAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132ContractPrefix.PadRight(7));
    result.Append(_D132ContractSuffix.PadRight(3));
    
    return result.ToString();
}

public void SetD132ContractNoAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetD132ContractPrefix(extracted);
    }
    offset += 7;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetD132ContractSuffix(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetD132ContractPrefix()
{
    return _D132ContractPrefix;
}

// Standard Setter
public void SetD132ContractPrefix(string value)
{
    _D132ContractPrefix = value;
}

// Get<>AsString()
public string GetD132ContractPrefixAsString()
{
    return _D132ContractPrefix.PadRight(7);
}

// Set<>AsString()
public void SetD132ContractPrefixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132ContractPrefix = value;
}

// Standard Getter
public string GetD132ContractSuffix()
{
    return _D132ContractSuffix;
}

// Standard Setter
public void SetD132ContractSuffix(string value)
{
    _D132ContractSuffix = value;
}

// Get<>AsString()
public string GetD132ContractSuffixAsString()
{
    return _D132ContractSuffix.PadRight(3);
}

// Set<>AsString()
public void SetD132ContractSuffixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132ContractSuffix = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D132RecordCodeX
public class D132RecordCodeX
{
    private static int _size = 2;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132RecordCodeX1, is_external=, is_static_class=False, static_prefix=
    private string _D132RecordCodeX1 ="";
    
    
    
    
    // [DEBUG] Field: D132RecordCodeX2, is_external=, is_static_class=False, static_prefix=
    private string _D132RecordCodeX2 ="";
    
    
    
    
public D132RecordCodeX() {}

public D132RecordCodeX(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD132RecordCodeX1(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD132RecordCodeX2(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetD132RecordCodeXAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132RecordCodeX1.PadRight(1));
    result.Append(_D132RecordCodeX2.PadRight(1));
    
    return result.ToString();
}

public void SetD132RecordCodeXAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD132RecordCodeX1(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD132RecordCodeX2(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetD132RecordCodeX1()
{
    return _D132RecordCodeX1;
}

// Standard Setter
public void SetD132RecordCodeX1(string value)
{
    _D132RecordCodeX1 = value;
}

// Get<>AsString()
public string GetD132RecordCodeX1AsString()
{
    return _D132RecordCodeX1.PadRight(1);
}

// Set<>AsString()
public void SetD132RecordCodeX1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132RecordCodeX1 = value;
}

// Standard Getter
public string GetD132RecordCodeX2()
{
    return _D132RecordCodeX2;
}

// Standard Setter
public void SetD132RecordCodeX2(string value)
{
    _D132RecordCodeX2 = value;
}

// Get<>AsString()
public string GetD132RecordCodeX2AsString()
{
    return _D132RecordCodeX2.PadRight(1);
}

// Set<>AsString()
public void SetD132RecordCodeX2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132RecordCodeX2 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: D132DateTimeStamp
public class D132DateTimeStamp
{
    private static int _size = 14;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132DateStamp, is_external=, is_static_class=False, static_prefix=
    private string _D132DateStamp ="";
    
    
    
    
    // [DEBUG] Field: D132TimeStamp, is_external=, is_static_class=False, static_prefix=
    private string _D132TimeStamp ="";
    
    
    
    
public D132DateTimeStamp() {}

public D132DateTimeStamp(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD132DateStamp(data.Substring(offset, 6).Trim());
    offset += 6;
    SetD132TimeStamp(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetD132DateTimeStampAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132DateStamp.PadRight(6));
    result.Append(_D132TimeStamp.PadRight(8));
    
    return result.ToString();
}

public void SetD132DateTimeStampAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetD132DateStamp(extracted);
    }
    offset += 6;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD132TimeStamp(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetD132DateStamp()
{
    return _D132DateStamp;
}

// Standard Setter
public void SetD132DateStamp(string value)
{
    _D132DateStamp = value;
}

// Get<>AsString()
public string GetD132DateStampAsString()
{
    return _D132DateStamp.PadRight(6);
}

// Set<>AsString()
public void SetD132DateStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132DateStamp = value;
}

// Standard Getter
public string GetD132TimeStamp()
{
    return _D132TimeStamp;
}

// Standard Setter
public void SetD132TimeStamp(string value)
{
    _D132TimeStamp = value;
}

// Get<>AsString()
public string GetD132TimeStampAsString()
{
    return _D132TimeStamp.PadRight(8);
}

// Set<>AsString()
public void SetD132TimeStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132TimeStamp = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler41
public class Filler41
{
    private static int _size = 14;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132Stamp, is_external=, is_static_class=False, static_prefix=
    private string _D132Stamp ="";
    
    
    
    
    // [DEBUG] Field: D132Partly, is_external=, is_static_class=False, static_prefix=
    private string _D132Partly ="";
    
    
    
    
public Filler41() {}

public Filler41(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD132Stamp(data.Substring(offset, 13).Trim());
    offset += 13;
    SetD132Partly(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetFiller41AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132Stamp.PadRight(13));
    result.Append(_D132Partly.PadRight(1));
    
    return result.ToString();
}

public void SetFiller41AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetD132Stamp(extracted);
    }
    offset += 13;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD132Partly(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetD132Stamp()
{
    return _D132Stamp;
}

// Standard Setter
public void SetD132Stamp(string value)
{
    _D132Stamp = value;
}

// Get<>AsString()
public string GetD132StampAsString()
{
    return _D132Stamp.PadRight(13);
}

// Set<>AsString()
public void SetD132StampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132Stamp = value;
}

// Standard Getter
public string GetD132Partly()
{
    return _D132Partly;
}

// Standard Setter
public void SetD132Partly(string value)
{
    _D132Partly = value;
}

// Get<>AsString()
public string GetD132PartlyAsString()
{
    return _D132Partly.PadRight(1);
}

// Set<>AsString()
public void SetD132PartlyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132Partly = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D132CalParentSedolCode
public class D132CalParentSedolCode
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132ParentCal, is_external=, is_static_class=False, static_prefix=
    private string _D132ParentCal ="";
    
    
    
    
    // [DEBUG] Field: D132ParentSedolCode, is_external=, is_static_class=False, static_prefix=
    private string _D132ParentSedolCode ="";
    
    
    
    
public D132CalParentSedolCode() {}

public D132CalParentSedolCode(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD132ParentCal(data.Substring(offset, 4).Trim());
    offset += 4;
    SetD132ParentSedolCode(data.Substring(offset, 7).Trim());
    offset += 7;
    
}

// Serialization methods
public string GetD132CalParentSedolCodeAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132ParentCal.PadRight(4));
    result.Append(_D132ParentSedolCode.PadRight(7));
    
    return result.ToString();
}

public void SetD132CalParentSedolCodeAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetD132ParentCal(extracted);
    }
    offset += 4;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetD132ParentSedolCode(extracted);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetD132ParentCal()
{
    return _D132ParentCal;
}

// Standard Setter
public void SetD132ParentCal(string value)
{
    _D132ParentCal = value;
}

// Get<>AsString()
public string GetD132ParentCalAsString()
{
    return _D132ParentCal.PadRight(4);
}

// Set<>AsString()
public void SetD132ParentCalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132ParentCal = value;
}

// Standard Getter
public string GetD132ParentSedolCode()
{
    return _D132ParentSedolCode;
}

// Standard Setter
public void SetD132ParentSedolCode(string value)
{
    _D132ParentSedolCode = value;
}

// Get<>AsString()
public string GetD132ParentSedolCodeAsString()
{
    return _D132ParentSedolCode.PadRight(7);
}

// Set<>AsString()
public void SetD132ParentSedolCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132ParentSedolCode = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D132CalPreviousSedolCode
public class D132CalPreviousSedolCode
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132PreviousCal, is_external=, is_static_class=False, static_prefix=
    private string _D132PreviousCal ="";
    
    
    
    
    // [DEBUG] Field: D132PreviousSedolCode, is_external=, is_static_class=False, static_prefix=
    private string _D132PreviousSedolCode ="";
    
    
    
    
public D132CalPreviousSedolCode() {}

public D132CalPreviousSedolCode(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD132PreviousCal(data.Substring(offset, 4).Trim());
    offset += 4;
    SetD132PreviousSedolCode(data.Substring(offset, 7).Trim());
    offset += 7;
    
}

// Serialization methods
public string GetD132CalPreviousSedolCodeAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132PreviousCal.PadRight(4));
    result.Append(_D132PreviousSedolCode.PadRight(7));
    
    return result.ToString();
}

public void SetD132CalPreviousSedolCodeAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetD132PreviousCal(extracted);
    }
    offset += 4;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetD132PreviousSedolCode(extracted);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetD132PreviousCal()
{
    return _D132PreviousCal;
}

// Standard Setter
public void SetD132PreviousCal(string value)
{
    _D132PreviousCal = value;
}

// Get<>AsString()
public string GetD132PreviousCalAsString()
{
    return _D132PreviousCal.PadRight(4);
}

// Set<>AsString()
public void SetD132PreviousCalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132PreviousCal = value;
}

// Standard Getter
public string GetD132PreviousSedolCode()
{
    return _D132PreviousSedolCode;
}

// Standard Setter
public void SetD132PreviousSedolCode(string value)
{
    _D132PreviousSedolCode = value;
}

// Get<>AsString()
public string GetD132PreviousSedolCodeAsString()
{
    return _D132PreviousSedolCode.PadRight(7);
}

// Set<>AsString()
public void SetD132PreviousSedolCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132PreviousSedolCode = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D132OriginalBargainNo
public class D132OriginalBargainNo
{
    private static int _size = 10;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132BargainPrefix, is_external=, is_static_class=False, static_prefix=
    private string _D132BargainPrefix ="";
    
    
    
    
    // [DEBUG] Field: D132BargainSuffix, is_external=, is_static_class=False, static_prefix=
    private string _D132BargainSuffix ="";
    
    
    
    
public D132OriginalBargainNo() {}

public D132OriginalBargainNo(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD132BargainPrefix(data.Substring(offset, 7).Trim());
    offset += 7;
    SetD132BargainSuffix(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetD132OriginalBargainNoAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132BargainPrefix.PadRight(7));
    result.Append(_D132BargainSuffix.PadRight(3));
    
    return result.ToString();
}

public void SetD132OriginalBargainNoAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetD132BargainPrefix(extracted);
    }
    offset += 7;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetD132BargainSuffix(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetD132BargainPrefix()
{
    return _D132BargainPrefix;
}

// Standard Setter
public void SetD132BargainPrefix(string value)
{
    _D132BargainPrefix = value;
}

// Get<>AsString()
public string GetD132BargainPrefixAsString()
{
    return _D132BargainPrefix.PadRight(7);
}

// Set<>AsString()
public void SetD132BargainPrefixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132BargainPrefix = value;
}

// Standard Getter
public string GetD132BargainSuffix()
{
    return _D132BargainSuffix;
}

// Standard Setter
public void SetD132BargainSuffix(string value)
{
    _D132BargainSuffix = value;
}

// Get<>AsString()
public string GetD132BargainSuffixAsString()
{
    return _D132BargainSuffix.PadRight(3);
}

// Set<>AsString()
public void SetD132BargainSuffixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132BargainSuffix = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D132BargainDate
public class D132BargainDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132BargainDateYymm, is_external=, is_static_class=False, static_prefix=
    private D132BargainDate.D132BargainDateYymm _D132BargainDateYymm = new D132BargainDate.D132BargainDateYymm();
    
    
    
    
    // [DEBUG] Field: D132BargainDateDd, is_external=, is_static_class=False, static_prefix=
    private int _D132BargainDateDd =0;
    
    
    
    
public D132BargainDate() {}

public D132BargainDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D132BargainDateYymm.SetD132BargainDateYymmAsString(data.Substring(offset, D132BargainDateYymm.GetSize()));
    offset += 4;
    SetD132BargainDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD132BargainDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132BargainDateYymm.GetD132BargainDateYymmAsString());
    result.Append(_D132BargainDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD132BargainDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        _D132BargainDateYymm.SetD132BargainDateYymmAsString(data.Substring(offset, 4));
    }
    else
    {
        _D132BargainDateYymm.SetD132BargainDateYymmAsString(data.Substring(offset));
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD132BargainDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public D132BargainDateYymm GetD132BargainDateYymm()
{
    return _D132BargainDateYymm;
}

// Standard Setter
public void SetD132BargainDateYymm(D132BargainDateYymm value)
{
    _D132BargainDateYymm = value;
}

// Get<>AsString()
public string GetD132BargainDateYymmAsString()
{
    return _D132BargainDateYymm != null ? _D132BargainDateYymm.GetD132BargainDateYymmAsString() : "";
}

// Set<>AsString()
public void SetD132BargainDateYymmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D132BargainDateYymm == null)
    {
        _D132BargainDateYymm = new D132BargainDateYymm();
    }
    _D132BargainDateYymm.SetD132BargainDateYymmAsString(value);
}

// Standard Getter
public int GetD132BargainDateDd()
{
    return _D132BargainDateDd;
}

// Standard Setter
public void SetD132BargainDateDd(int value)
{
    _D132BargainDateDd = value;
}

// Get<>AsString()
public string GetD132BargainDateDdAsString()
{
    return _D132BargainDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD132BargainDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D132BargainDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D132BargainDateYymm
public class D132BargainDateYymm
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132BargainDateYy, is_external=, is_static_class=False, static_prefix=
    private int _D132BargainDateYy =0;
    
    
    
    
    // [DEBUG] Field: D132BargainDateMm, is_external=, is_static_class=False, static_prefix=
    private int _D132BargainDateMm =0;
    
    
    
    
public D132BargainDateYymm() {}

public D132BargainDateYymm(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD132BargainDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD132BargainDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD132BargainDateYymmAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132BargainDateYy.ToString().PadLeft(2, '0'));
    result.Append(_D132BargainDateMm.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD132BargainDateYymmAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD132BargainDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD132BargainDateMm(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetD132BargainDateYy()
{
    return _D132BargainDateYy;
}

// Standard Setter
public void SetD132BargainDateYy(int value)
{
    _D132BargainDateYy = value;
}

// Get<>AsString()
public string GetD132BargainDateYyAsString()
{
    return _D132BargainDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD132BargainDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D132BargainDateYy = parsed;
}

// Standard Getter
public int GetD132BargainDateMm()
{
    return _D132BargainDateMm;
}

// Standard Setter
public void SetD132BargainDateMm(int value)
{
    _D132BargainDateMm = value;
}

// Get<>AsString()
public string GetD132BargainDateMmAsString()
{
    return _D132BargainDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD132BargainDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D132BargainDateMm = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: D132SettlementDate
public class D132SettlementDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132SettlementDateYy, is_external=, is_static_class=False, static_prefix=
    private int _D132SettlementDateYy =0;
    
    
    
    
    // [DEBUG] Field: D132SettlementDateMm, is_external=, is_static_class=False, static_prefix=
    private int _D132SettlementDateMm =0;
    
    
    
    
    // [DEBUG] Field: D132SettlementDateDd, is_external=, is_static_class=False, static_prefix=
    private int _D132SettlementDateDd =0;
    
    
    
    
public D132SettlementDate() {}

public D132SettlementDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD132SettlementDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD132SettlementDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD132SettlementDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD132SettlementDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132SettlementDateYy.ToString().PadLeft(2, '0'));
    result.Append(_D132SettlementDateMm.ToString().PadLeft(2, '0'));
    result.Append(_D132SettlementDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD132SettlementDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD132SettlementDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD132SettlementDateMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD132SettlementDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetD132SettlementDateYy()
{
    return _D132SettlementDateYy;
}

// Standard Setter
public void SetD132SettlementDateYy(int value)
{
    _D132SettlementDateYy = value;
}

// Get<>AsString()
public string GetD132SettlementDateYyAsString()
{
    return _D132SettlementDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD132SettlementDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D132SettlementDateYy = parsed;
}

// Standard Getter
public int GetD132SettlementDateMm()
{
    return _D132SettlementDateMm;
}

// Standard Setter
public void SetD132SettlementDateMm(int value)
{
    _D132SettlementDateMm = value;
}

// Get<>AsString()
public string GetD132SettlementDateMmAsString()
{
    return _D132SettlementDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD132SettlementDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D132SettlementDateMm = parsed;
}

// Standard Getter
public int GetD132SettlementDateDd()
{
    return _D132SettlementDateDd;
}

// Standard Setter
public void SetD132SettlementDateDd(int value)
{
    _D132SettlementDateDd = value;
}

// Get<>AsString()
public string GetD132SettlementDateDdAsString()
{
    return _D132SettlementDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD132SettlementDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D132SettlementDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetFiller42(string value)
{
    _Filler42.SetFiller42AsString(value);
}
// Nested Class: Filler42
public class Filler42
{
    private static int _size = 3681;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132Remainder, is_external=, is_static_class=False, static_prefix=
    private string _D132Remainder ="";
    
    
    
    
    // [DEBUG] Field: D132Record02Fields, is_external=, is_static_class=False, static_prefix=
    private Filler42.D132Record02Fields _D132Record02Fields = new Filler42.D132Record02Fields();
    
    
    
    
    // [DEBUG] Field: D132Record03Fields, is_external=, is_static_class=False, static_prefix=
    private Filler42.D132Record03Fields _D132Record03Fields = new Filler42.D132Record03Fields();
    
    
    
    
public Filler42() {}

public Filler42(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD132Remainder(data.Substring(offset, 1708).Trim());
    offset += 1708;
    _D132Record02Fields.SetD132Record02FieldsAsString(data.Substring(offset, D132Record02Fields.GetSize()));
    offset += 214;
    _D132Record03Fields.SetD132Record03FieldsAsString(data.Substring(offset, D132Record03Fields.GetSize()));
    offset += 1759;
    
}

// Serialization methods
public string GetFiller42AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132Remainder.PadRight(1708));
    result.Append(_D132Record02Fields.GetD132Record02FieldsAsString());
    result.Append(_D132Record03Fields.GetD132Record03FieldsAsString());
    
    return result.ToString();
}

public void SetFiller42AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1708 <= data.Length)
    {
        string extracted = data.Substring(offset, 1708).Trim();
        SetD132Remainder(extracted);
    }
    offset += 1708;
    if (offset + 214 <= data.Length)
    {
        _D132Record02Fields.SetD132Record02FieldsAsString(data.Substring(offset, 214));
    }
    else
    {
        _D132Record02Fields.SetD132Record02FieldsAsString(data.Substring(offset));
    }
    offset += 214;
    if (offset + 1759 <= data.Length)
    {
        _D132Record03Fields.SetD132Record03FieldsAsString(data.Substring(offset, 1759));
    }
    else
    {
        _D132Record03Fields.SetD132Record03FieldsAsString(data.Substring(offset));
    }
    offset += 1759;
}

// Getter and Setter methods

// Standard Getter
public string GetD132Remainder()
{
    return _D132Remainder;
}

// Standard Setter
public void SetD132Remainder(string value)
{
    _D132Remainder = value;
}

// Get<>AsString()
public string GetD132RemainderAsString()
{
    return _D132Remainder.PadRight(1708);
}

// Set<>AsString()
public void SetD132RemainderAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132Remainder = value;
}

// Standard Getter
public D132Record02Fields GetD132Record02Fields()
{
    return _D132Record02Fields;
}

// Standard Setter
public void SetD132Record02Fields(D132Record02Fields value)
{
    _D132Record02Fields = value;
}

// Get<>AsString()
public string GetD132Record02FieldsAsString()
{
    return _D132Record02Fields != null ? _D132Record02Fields.GetD132Record02FieldsAsString() : "";
}

// Set<>AsString()
public void SetD132Record02FieldsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D132Record02Fields == null)
    {
        _D132Record02Fields = new D132Record02Fields();
    }
    _D132Record02Fields.SetD132Record02FieldsAsString(value);
}

// Standard Getter
public D132Record03Fields GetD132Record03Fields()
{
    return _D132Record03Fields;
}

// Standard Setter
public void SetD132Record03Fields(D132Record03Fields value)
{
    _D132Record03Fields = value;
}

// Get<>AsString()
public string GetD132Record03FieldsAsString()
{
    return _D132Record03Fields != null ? _D132Record03Fields.GetD132Record03FieldsAsString() : "";
}

// Set<>AsString()
public void SetD132Record03FieldsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D132Record03Fields == null)
    {
        _D132Record03Fields = new D132Record03Fields();
    }
    _D132Record03Fields.SetD132Record03FieldsAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D132Record02Fields
public class D132Record02Fields
{
    private static int _size = 214;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132BalAcqExtra, is_external=, is_static_class=False, static_prefix=
    private D132Record02Fields.D132BalAcqExtra _D132BalAcqExtra = new D132Record02Fields.D132BalAcqExtra();
    
    
    
    
    // [DEBUG] Field: D132BalCostList, is_external=, is_static_class=False, static_prefix=
    private D132Record02Fields.D132BalCostList[] _D132BalCostList = new D132Record02Fields.D132BalCostList[200];
    
    public void InitializeD132BalCostListArray()
    {
        for (int i = 0; i < 200; i++)
        {
            _D132BalCostList[i] = new D132Record02Fields.D132BalCostList();
        }
    }
    
    
    
public D132Record02Fields() {}

public D132Record02Fields(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D132BalAcqExtra.SetD132BalAcqExtraAsString(data.Substring(offset, D132BalAcqExtra.GetSize()));
    offset += 201;
    InitializeD132BalCostListArray();
    for (int i = 0; i < 200; i++)
    {
        _D132BalCostList[i].SetD132BalCostListAsString(data.Substring(offset, 13));
        offset += 13;
    }
    
}

// Serialization methods
public string GetD132Record02FieldsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132BalAcqExtra.GetD132BalAcqExtraAsString());
    for (int i = 0; i < 200; i++)
    {
        result.Append(_D132BalCostList[i].GetD132BalCostListAsString());
    }
    
    return result.ToString();
}

public void SetD132Record02FieldsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 201 <= data.Length)
    {
        _D132BalAcqExtra.SetD132BalAcqExtraAsString(data.Substring(offset, 201));
    }
    else
    {
        _D132BalAcqExtra.SetD132BalAcqExtraAsString(data.Substring(offset));
    }
    offset += 201;
    for (int i = 0; i < 200; i++)
    {
        if (offset + 13 > data.Length) break;
        string val = data.Substring(offset, 13);
        
        _D132BalCostList[i].SetD132BalCostListAsString(val);
        offset += 13;
    }
}

// Getter and Setter methods

// Standard Getter
public D132BalAcqExtra GetD132BalAcqExtra()
{
    return _D132BalAcqExtra;
}

// Standard Setter
public void SetD132BalAcqExtra(D132BalAcqExtra value)
{
    _D132BalAcqExtra = value;
}

// Get<>AsString()
public string GetD132BalAcqExtraAsString()
{
    return _D132BalAcqExtra != null ? _D132BalAcqExtra.GetD132BalAcqExtraAsString() : "";
}

// Set<>AsString()
public void SetD132BalAcqExtraAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D132BalAcqExtra == null)
    {
        _D132BalAcqExtra = new D132BalAcqExtra();
    }
    _D132BalAcqExtra.SetD132BalAcqExtraAsString(value);
}

// Array Accessors for D132BalCostList
public D132BalCostList GetD132BalCostListAt(int index)
{
    return _D132BalCostList[index];
}

public void SetD132BalCostListAt(int index, D132BalCostList value)
{
    _D132BalCostList[index] = value;
}

// Flattened accessors (index 0)
public D132BalCostList GetD132BalCostList()
{
    return _D132BalCostList != null && _D132BalCostList.Length > 0
    ? _D132BalCostList[0]
    : new D132BalCostList();
}

public void SetD132BalCostList(D132BalCostList value)
{
    if (_D132BalCostList == null || _D132BalCostList.Length == 0)
    _D132BalCostList = new D132BalCostList[1];
    _D132BalCostList[0] = value;
}





public static int GetSize()
{
    return _size;
}

// Nested Class: D132BalAcqExtra
public class D132BalAcqExtra
{
    private static int _size = 201;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132BfTrancheTotalUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _D132BfTrancheTotalUnits =0;
    
    
    
    
    // [DEBUG] Field: D132TrancheTotalUnitsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D132TrancheTotalUnitsYtd =0;
    
    
    
    
    // [DEBUG] Field: D132BfDispUnitsReac, is_external=, is_static_class=False, static_prefix=
    private decimal _D132BfDispUnitsReac =0;
    
    
    
    
    // [DEBUG] Field: D132DispUnitsReacYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D132DispUnitsReacYtd =0;
    
    
    
    
    // [DEBUG] Field: D132UnderwritingCommission, is_external=, is_static_class=False, static_prefix=
    private decimal _D132UnderwritingCommission =0;
    
    
    
    
    // [DEBUG] Field: D132BookCost, is_external=, is_static_class=False, static_prefix=
    private decimal _D132BookCost =0;
    
    
    
    
    // [DEBUG] Field: D132IssueSameClass, is_external=, is_static_class=False, static_prefix=
    private string _D132IssueSameClass ="";
    
    
    
    
    // [DEBUG] Field: D132BfDatePrevOpEventR, is_external=, is_static_class=False, static_prefix=
    private D132BalAcqExtra.D132BfDatePrevOpEventR _D132BfDatePrevOpEventR = new D132BalAcqExtra.D132BfDatePrevOpEventR();
    
    
    
    
    // [DEBUG] Field: D132BfDatePrevOpEvent, is_external=, is_static_class=False, static_prefix=
    private int _D132BfDatePrevOpEvent =0;
    
    
    
    
    // [DEBUG] Field: D132DatePrevOpEventYtd, is_external=, is_static_class=False, static_prefix=
    private D132BalAcqExtra.D132DatePrevOpEventYtd _D132DatePrevOpEventYtd = new D132BalAcqExtra.D132DatePrevOpEventYtd();
    
    
    
    
    // [DEBUG] Field: D132FirstDayDealingPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _D132FirstDayDealingPrice =0;
    
    
    
    
    // [DEBUG] Field: D132BfIndexedCostBalance, is_external=, is_static_class=False, static_prefix=
    private decimal _D132BfIndexedCostBalance =0;
    
    
    
    
    // [DEBUG] Field: D132IndexedCostBalanceYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D132IndexedCostBalanceYtd =0;
    
    
    
    
    // [DEBUG] Field: D132UnindexableFlag, is_external=, is_static_class=False, static_prefix=
    private string _D132UnindexableFlag ="";
    
    
    // 88-level condition checks for D132UnindexableFlag
    public bool IsD132UnindexableHolding()
    {
        if (this._D132UnindexableFlag == "'1'") return true;
        return false;
    }
    public bool IsD132Merged1982Pool()
    {
        if (this._D132UnindexableFlag == "'2'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D132GroupTransferFlag, is_external=, is_static_class=False, static_prefix=
    private string _D132GroupTransferFlag ="";
    
    
    // 88-level condition checks for D132GroupTransferFlag
    public bool IsD132GroupTransferBalance()
    {
        if (this._D132GroupTransferFlag == "'1'") return true;
        return false;
    }
    public bool IsD132ReorgBalance()
    {
        if (this._D132GroupTransferFlag == "'2'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D132BfLinkRecordIndic, is_external=, is_static_class=False, static_prefix=
    private int _D132BfLinkRecordIndic =0;
    
    
    
    
    // [DEBUG] Field: D132LinkRecordIndicYtd, is_external=, is_static_class=False, static_prefix=
    private int _D132LinkRecordIndicYtd =0;
    
    
    
    
    // [DEBUG] Field: D132BfIndex85CostBalance, is_external=, is_static_class=False, static_prefix=
    private decimal _D132BfIndex85CostBalance =0;
    
    
    
    
    // [DEBUG] Field: D132Index85CostBalanceYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D132Index85CostBalanceYtd =0;
    
    
    
    
    // [DEBUG] Field: D132TrancheFlag, is_external=, is_static_class=False, static_prefix=
    private string _D132TrancheFlag ="";
    
    
    
    
    // [DEBUG] Field: D132ResetTaperDate, is_external=, is_static_class=False, static_prefix=
    private string _D132ResetTaperDate ="";
    
    
    
    
    // [DEBUG] Field: D132NoOfInitialBookCosts, is_external=, is_static_class=False, static_prefix=
    private int _D132NoOfInitialBookCosts =0;
    
    
    
    
    // [DEBUG] Field: D132BfNoOfCostsHeld, is_external=, is_static_class=False, static_prefix=
    private int _D132BfNoOfCostsHeld =0;
    
    
    
    
    // [DEBUG] Field: D132NoOfCostsHeldYtd, is_external=, is_static_class=False, static_prefix=
    private int _D132NoOfCostsHeldYtd =0;
    
    
    
    
    // [DEBUG] Field: D132CapitalGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D132CapitalGainLoss =0;
    
    
    
    
    // [DEBUG] Field: D132ProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D132ProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: D132ProceedsOfDisposal, is_external=, is_static_class=False, static_prefix=
    private decimal _D132ProceedsOfDisposal =0;
    
    
    
    
public D132BalAcqExtra() {}

public D132BalAcqExtra(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD132BfTrancheTotalUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetD132TrancheTotalUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetD132BfDispUnitsReac(PackedDecimalConverter.ToDecimal(data.Substring(offset, 7)));
    offset += 7;
    SetD132DispUnitsReacYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 7)));
    offset += 7;
    SetD132UnderwritingCommission(PackedDecimalConverter.ToDecimal(data.Substring(offset, 7)));
    offset += 7;
    SetD132BookCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 6)));
    offset += 6;
    SetD132IssueSameClass(data.Substring(offset, 1).Trim());
    offset += 1;
    _D132BfDatePrevOpEventR.SetD132BfDatePrevOpEventRAsString(data.Substring(offset, D132BfDatePrevOpEventR.GetSize()));
    offset += 6;
    SetD132BfDatePrevOpEvent(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    _D132DatePrevOpEventYtd.SetD132DatePrevOpEventYtdAsString(data.Substring(offset, D132DatePrevOpEventYtd.GetSize()));
    offset += 6;
    SetD132FirstDayDealingPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
    offset += 11;
    SetD132BfIndexedCostBalance(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetD132IndexedCostBalanceYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetD132UnindexableFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD132GroupTransferFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD132BfLinkRecordIndic(int.Parse(data.Substring(offset, 1).Trim()));
    offset += 1;
    SetD132LinkRecordIndicYtd(int.Parse(data.Substring(offset, 1).Trim()));
    offset += 1;
    SetD132BfIndex85CostBalance(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetD132Index85CostBalanceYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetD132TrancheFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD132ResetTaperDate(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD132NoOfInitialBookCosts(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD132BfNoOfCostsHeld(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD132NoOfCostsHeldYtd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD132CapitalGainLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetD132ProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetD132ProceedsOfDisposal(PackedDecimalConverter.ToDecimal(data.Substring(offset, 8)));
    offset += 8;
    
}

// Serialization methods
public string GetD132BalAcqExtraAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132BfTrancheTotalUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132TrancheTotalUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132BfDispUnitsReac.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132DispUnitsReacYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132UnderwritingCommission.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132BookCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132IssueSameClass.PadRight(1));
    result.Append(_D132BfDatePrevOpEventR.GetD132BfDatePrevOpEventRAsString());
    result.Append(_D132BfDatePrevOpEvent.ToString().PadLeft(6, '0'));
    result.Append(_D132DatePrevOpEventYtd.GetD132DatePrevOpEventYtdAsString());
    result.Append(_D132FirstDayDealingPrice.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132BfIndexedCostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132IndexedCostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132UnindexableFlag.PadRight(1));
    result.Append(_D132GroupTransferFlag.PadRight(1));
    result.Append(_D132BfLinkRecordIndic.ToString().PadLeft(1, '0'));
    result.Append(_D132LinkRecordIndicYtd.ToString().PadLeft(1, '0'));
    result.Append(_D132BfIndex85CostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132Index85CostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132TrancheFlag.PadRight(1));
    result.Append(_D132ResetTaperDate.PadRight(1));
    result.Append(_D132NoOfInitialBookCosts.ToString().PadLeft(2, '0'));
    result.Append(_D132BfNoOfCostsHeld.ToString().PadLeft(2, '0'));
    result.Append(_D132NoOfCostsHeldYtd.ToString().PadLeft(2, '0'));
    result.Append(_D132CapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132ProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132ProceedsOfDisposal.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetD132BalAcqExtraAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132BfTrancheTotalUnits(parsedDec);
    }
    offset += 13;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132TrancheTotalUnitsYtd(parsedDec);
    }
    offset += 13;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132BfDispUnitsReac(parsedDec);
    }
    offset += 7;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132DispUnitsReacYtd(parsedDec);
    }
    offset += 7;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132UnderwritingCommission(parsedDec);
    }
    offset += 7;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132BookCost(parsedDec);
    }
    offset += 6;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD132IssueSameClass(extracted);
    }
    offset += 1;
    if (offset + 6 <= data.Length)
    {
        _D132BfDatePrevOpEventR.SetD132BfDatePrevOpEventRAsString(data.Substring(offset, 6));
    }
    else
    {
        _D132BfDatePrevOpEventR.SetD132BfDatePrevOpEventRAsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD132BfDatePrevOpEvent(parsedInt);
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        _D132DatePrevOpEventYtd.SetD132DatePrevOpEventYtdAsString(data.Substring(offset, 6));
    }
    else
    {
        _D132DatePrevOpEventYtd.SetD132DatePrevOpEventYtdAsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 11 <= data.Length)
    {
        string extracted = data.Substring(offset, 11).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132FirstDayDealingPrice(parsedDec);
    }
    offset += 11;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132BfIndexedCostBalance(parsedDec);
    }
    offset += 17;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132IndexedCostBalanceYtd(parsedDec);
    }
    offset += 17;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD132UnindexableFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD132GroupTransferFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD132BfLinkRecordIndic(parsedInt);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD132LinkRecordIndicYtd(parsedInt);
    }
    offset += 1;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132BfIndex85CostBalance(parsedDec);
    }
    offset += 17;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132Index85CostBalanceYtd(parsedDec);
    }
    offset += 17;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD132TrancheFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD132ResetTaperDate(extracted);
    }
    offset += 1;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD132NoOfInitialBookCosts(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD132BfNoOfCostsHeld(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD132NoOfCostsHeldYtd(parsedInt);
    }
    offset += 2;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132CapitalGainLoss(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132ProfitLoss(parsedDec);
    }
    offset += 15;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132ProceedsOfDisposal(parsedDec);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public decimal GetD132BfTrancheTotalUnits()
{
    return _D132BfTrancheTotalUnits;
}

// Standard Setter
public void SetD132BfTrancheTotalUnits(decimal value)
{
    _D132BfTrancheTotalUnits = value;
}

// Get<>AsString()
public string GetD132BfTrancheTotalUnitsAsString()
{
    return _D132BfTrancheTotalUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132BfTrancheTotalUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132BfTrancheTotalUnits = parsed;
}

// Standard Getter
public decimal GetD132TrancheTotalUnitsYtd()
{
    return _D132TrancheTotalUnitsYtd;
}

// Standard Setter
public void SetD132TrancheTotalUnitsYtd(decimal value)
{
    _D132TrancheTotalUnitsYtd = value;
}

// Get<>AsString()
public string GetD132TrancheTotalUnitsYtdAsString()
{
    return _D132TrancheTotalUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132TrancheTotalUnitsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132TrancheTotalUnitsYtd = parsed;
}

// Standard Getter
public decimal GetD132BfDispUnitsReac()
{
    return _D132BfDispUnitsReac;
}

// Standard Setter
public void SetD132BfDispUnitsReac(decimal value)
{
    _D132BfDispUnitsReac = value;
}

// Get<>AsString()
public string GetD132BfDispUnitsReacAsString()
{
    return _D132BfDispUnitsReac.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132BfDispUnitsReacAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132BfDispUnitsReac = parsed;
}

// Standard Getter
public decimal GetD132DispUnitsReacYtd()
{
    return _D132DispUnitsReacYtd;
}

// Standard Setter
public void SetD132DispUnitsReacYtd(decimal value)
{
    _D132DispUnitsReacYtd = value;
}

// Get<>AsString()
public string GetD132DispUnitsReacYtdAsString()
{
    return _D132DispUnitsReacYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132DispUnitsReacYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132DispUnitsReacYtd = parsed;
}

// Standard Getter
public decimal GetD132UnderwritingCommission()
{
    return _D132UnderwritingCommission;
}

// Standard Setter
public void SetD132UnderwritingCommission(decimal value)
{
    _D132UnderwritingCommission = value;
}

// Get<>AsString()
public string GetD132UnderwritingCommissionAsString()
{
    return _D132UnderwritingCommission.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132UnderwritingCommissionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132UnderwritingCommission = parsed;
}

// Standard Getter
public decimal GetD132BookCost()
{
    return _D132BookCost;
}

// Standard Setter
public void SetD132BookCost(decimal value)
{
    _D132BookCost = value;
}

// Get<>AsString()
public string GetD132BookCostAsString()
{
    return _D132BookCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132BookCostAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132BookCost = parsed;
}

// Standard Getter
public string GetD132IssueSameClass()
{
    return _D132IssueSameClass;
}

// Standard Setter
public void SetD132IssueSameClass(string value)
{
    _D132IssueSameClass = value;
}

// Get<>AsString()
public string GetD132IssueSameClassAsString()
{
    return _D132IssueSameClass.PadRight(1);
}

// Set<>AsString()
public void SetD132IssueSameClassAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132IssueSameClass = value;
}

// Standard Getter
public D132BfDatePrevOpEventR GetD132BfDatePrevOpEventR()
{
    return _D132BfDatePrevOpEventR;
}

// Standard Setter
public void SetD132BfDatePrevOpEventR(D132BfDatePrevOpEventR value)
{
    _D132BfDatePrevOpEventR = value;
}

// Get<>AsString()
public string GetD132BfDatePrevOpEventRAsString()
{
    return _D132BfDatePrevOpEventR != null ? _D132BfDatePrevOpEventR.GetD132BfDatePrevOpEventRAsString() : "";
}

// Set<>AsString()
public void SetD132BfDatePrevOpEventRAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D132BfDatePrevOpEventR == null)
    {
        _D132BfDatePrevOpEventR = new D132BfDatePrevOpEventR();
    }
    _D132BfDatePrevOpEventR.SetD132BfDatePrevOpEventRAsString(value);
}

// Standard Getter
public int GetD132BfDatePrevOpEvent()
{
    return _D132BfDatePrevOpEvent;
}

// Standard Setter
public void SetD132BfDatePrevOpEvent(int value)
{
    _D132BfDatePrevOpEvent = value;
}

// Get<>AsString()
public string GetD132BfDatePrevOpEventAsString()
{
    return _D132BfDatePrevOpEvent.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetD132BfDatePrevOpEventAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D132BfDatePrevOpEvent = parsed;
}

// Standard Getter
public D132DatePrevOpEventYtd GetD132DatePrevOpEventYtd()
{
    return _D132DatePrevOpEventYtd;
}

// Standard Setter
public void SetD132DatePrevOpEventYtd(D132DatePrevOpEventYtd value)
{
    _D132DatePrevOpEventYtd = value;
}

// Get<>AsString()
public string GetD132DatePrevOpEventYtdAsString()
{
    return _D132DatePrevOpEventYtd != null ? _D132DatePrevOpEventYtd.GetD132DatePrevOpEventYtdAsString() : "";
}

// Set<>AsString()
public void SetD132DatePrevOpEventYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D132DatePrevOpEventYtd == null)
    {
        _D132DatePrevOpEventYtd = new D132DatePrevOpEventYtd();
    }
    _D132DatePrevOpEventYtd.SetD132DatePrevOpEventYtdAsString(value);
}

// Standard Getter
public decimal GetD132FirstDayDealingPrice()
{
    return _D132FirstDayDealingPrice;
}

// Standard Setter
public void SetD132FirstDayDealingPrice(decimal value)
{
    _D132FirstDayDealingPrice = value;
}

// Get<>AsString()
public string GetD132FirstDayDealingPriceAsString()
{
    return _D132FirstDayDealingPrice.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132FirstDayDealingPriceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132FirstDayDealingPrice = parsed;
}

// Standard Getter
public decimal GetD132BfIndexedCostBalance()
{
    return _D132BfIndexedCostBalance;
}

// Standard Setter
public void SetD132BfIndexedCostBalance(decimal value)
{
    _D132BfIndexedCostBalance = value;
}

// Get<>AsString()
public string GetD132BfIndexedCostBalanceAsString()
{
    return _D132BfIndexedCostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132BfIndexedCostBalanceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132BfIndexedCostBalance = parsed;
}

// Standard Getter
public decimal GetD132IndexedCostBalanceYtd()
{
    return _D132IndexedCostBalanceYtd;
}

// Standard Setter
public void SetD132IndexedCostBalanceYtd(decimal value)
{
    _D132IndexedCostBalanceYtd = value;
}

// Get<>AsString()
public string GetD132IndexedCostBalanceYtdAsString()
{
    return _D132IndexedCostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132IndexedCostBalanceYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132IndexedCostBalanceYtd = parsed;
}

// Standard Getter
public string GetD132UnindexableFlag()
{
    return _D132UnindexableFlag;
}

// Standard Setter
public void SetD132UnindexableFlag(string value)
{
    _D132UnindexableFlag = value;
}

// Get<>AsString()
public string GetD132UnindexableFlagAsString()
{
    return _D132UnindexableFlag.PadRight(1);
}

// Set<>AsString()
public void SetD132UnindexableFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132UnindexableFlag = value;
}

// Standard Getter
public string GetD132GroupTransferFlag()
{
    return _D132GroupTransferFlag;
}

// Standard Setter
public void SetD132GroupTransferFlag(string value)
{
    _D132GroupTransferFlag = value;
}

// Get<>AsString()
public string GetD132GroupTransferFlagAsString()
{
    return _D132GroupTransferFlag.PadRight(1);
}

// Set<>AsString()
public void SetD132GroupTransferFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132GroupTransferFlag = value;
}

// Standard Getter
public int GetD132BfLinkRecordIndic()
{
    return _D132BfLinkRecordIndic;
}

// Standard Setter
public void SetD132BfLinkRecordIndic(int value)
{
    _D132BfLinkRecordIndic = value;
}

// Get<>AsString()
public string GetD132BfLinkRecordIndicAsString()
{
    return _D132BfLinkRecordIndic.ToString().PadLeft(1, '0');
}

// Set<>AsString()
public void SetD132BfLinkRecordIndicAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D132BfLinkRecordIndic = parsed;
}

// Standard Getter
public int GetD132LinkRecordIndicYtd()
{
    return _D132LinkRecordIndicYtd;
}

// Standard Setter
public void SetD132LinkRecordIndicYtd(int value)
{
    _D132LinkRecordIndicYtd = value;
}

// Get<>AsString()
public string GetD132LinkRecordIndicYtdAsString()
{
    return _D132LinkRecordIndicYtd.ToString().PadLeft(1, '0');
}

// Set<>AsString()
public void SetD132LinkRecordIndicYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D132LinkRecordIndicYtd = parsed;
}

// Standard Getter
public decimal GetD132BfIndex85CostBalance()
{
    return _D132BfIndex85CostBalance;
}

// Standard Setter
public void SetD132BfIndex85CostBalance(decimal value)
{
    _D132BfIndex85CostBalance = value;
}

// Get<>AsString()
public string GetD132BfIndex85CostBalanceAsString()
{
    return _D132BfIndex85CostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132BfIndex85CostBalanceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132BfIndex85CostBalance = parsed;
}

// Standard Getter
public decimal GetD132Index85CostBalanceYtd()
{
    return _D132Index85CostBalanceYtd;
}

// Standard Setter
public void SetD132Index85CostBalanceYtd(decimal value)
{
    _D132Index85CostBalanceYtd = value;
}

// Get<>AsString()
public string GetD132Index85CostBalanceYtdAsString()
{
    return _D132Index85CostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132Index85CostBalanceYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132Index85CostBalanceYtd = parsed;
}

// Standard Getter
public string GetD132TrancheFlag()
{
    return _D132TrancheFlag;
}

// Standard Setter
public void SetD132TrancheFlag(string value)
{
    _D132TrancheFlag = value;
}

// Get<>AsString()
public string GetD132TrancheFlagAsString()
{
    return _D132TrancheFlag.PadRight(1);
}

// Set<>AsString()
public void SetD132TrancheFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132TrancheFlag = value;
}

// Standard Getter
public string GetD132ResetTaperDate()
{
    return _D132ResetTaperDate;
}

// Standard Setter
public void SetD132ResetTaperDate(string value)
{
    _D132ResetTaperDate = value;
}

// Get<>AsString()
public string GetD132ResetTaperDateAsString()
{
    return _D132ResetTaperDate.PadRight(1);
}

// Set<>AsString()
public void SetD132ResetTaperDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132ResetTaperDate = value;
}

// Standard Getter
public int GetD132NoOfInitialBookCosts()
{
    return _D132NoOfInitialBookCosts;
}

// Standard Setter
public void SetD132NoOfInitialBookCosts(int value)
{
    _D132NoOfInitialBookCosts = value;
}

// Get<>AsString()
public string GetD132NoOfInitialBookCostsAsString()
{
    return _D132NoOfInitialBookCosts.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD132NoOfInitialBookCostsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D132NoOfInitialBookCosts = parsed;
}

// Standard Getter
public int GetD132BfNoOfCostsHeld()
{
    return _D132BfNoOfCostsHeld;
}

// Standard Setter
public void SetD132BfNoOfCostsHeld(int value)
{
    _D132BfNoOfCostsHeld = value;
}

// Get<>AsString()
public string GetD132BfNoOfCostsHeldAsString()
{
    return _D132BfNoOfCostsHeld.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD132BfNoOfCostsHeldAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D132BfNoOfCostsHeld = parsed;
}

// Standard Getter
public int GetD132NoOfCostsHeldYtd()
{
    return _D132NoOfCostsHeldYtd;
}

// Standard Setter
public void SetD132NoOfCostsHeldYtd(int value)
{
    _D132NoOfCostsHeldYtd = value;
}

// Get<>AsString()
public string GetD132NoOfCostsHeldYtdAsString()
{
    return _D132NoOfCostsHeldYtd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD132NoOfCostsHeldYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D132NoOfCostsHeldYtd = parsed;
}

// Standard Getter
public decimal GetD132CapitalGainLoss()
{
    return _D132CapitalGainLoss;
}

// Standard Setter
public void SetD132CapitalGainLoss(decimal value)
{
    _D132CapitalGainLoss = value;
}

// Get<>AsString()
public string GetD132CapitalGainLossAsString()
{
    return _D132CapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132CapitalGainLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132CapitalGainLoss = parsed;
}

// Standard Getter
public decimal GetD132ProfitLoss()
{
    return _D132ProfitLoss;
}

// Standard Setter
public void SetD132ProfitLoss(decimal value)
{
    _D132ProfitLoss = value;
}

// Get<>AsString()
public string GetD132ProfitLossAsString()
{
    return _D132ProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132ProfitLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132ProfitLoss = parsed;
}

// Standard Getter
public decimal GetD132ProceedsOfDisposal()
{
    return _D132ProceedsOfDisposal;
}

// Standard Setter
public void SetD132ProceedsOfDisposal(decimal value)
{
    _D132ProceedsOfDisposal = value;
}

// Get<>AsString()
public string GetD132ProceedsOfDisposalAsString()
{
    return _D132ProceedsOfDisposal.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132ProceedsOfDisposalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132ProceedsOfDisposal = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D132BfDatePrevOpEventR
public class D132BfDatePrevOpEventR
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132BfDatePrevOpEventYy, is_external=, is_static_class=False, static_prefix=
    private string _D132BfDatePrevOpEventYy ="";
    
    
    
    
    // [DEBUG] Field: D132BfDatePrevOpEventMm, is_external=, is_static_class=False, static_prefix=
    private string _D132BfDatePrevOpEventMm ="";
    
    
    
    
    // [DEBUG] Field: D132BfDatePrevOpEventDd, is_external=, is_static_class=False, static_prefix=
    private string _D132BfDatePrevOpEventDd ="";
    
    
    
    
public D132BfDatePrevOpEventR() {}

public D132BfDatePrevOpEventR(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD132BfDatePrevOpEventYy(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD132BfDatePrevOpEventMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD132BfDatePrevOpEventDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD132BfDatePrevOpEventRAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132BfDatePrevOpEventYy.PadRight(2));
    result.Append(_D132BfDatePrevOpEventMm.PadRight(2));
    result.Append(_D132BfDatePrevOpEventDd.PadRight(2));
    
    return result.ToString();
}

public void SetD132BfDatePrevOpEventRAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD132BfDatePrevOpEventYy(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD132BfDatePrevOpEventMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD132BfDatePrevOpEventDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD132BfDatePrevOpEventYy()
{
    return _D132BfDatePrevOpEventYy;
}

// Standard Setter
public void SetD132BfDatePrevOpEventYy(string value)
{
    _D132BfDatePrevOpEventYy = value;
}

// Get<>AsString()
public string GetD132BfDatePrevOpEventYyAsString()
{
    return _D132BfDatePrevOpEventYy.PadRight(2);
}

// Set<>AsString()
public void SetD132BfDatePrevOpEventYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132BfDatePrevOpEventYy = value;
}

// Standard Getter
public string GetD132BfDatePrevOpEventMm()
{
    return _D132BfDatePrevOpEventMm;
}

// Standard Setter
public void SetD132BfDatePrevOpEventMm(string value)
{
    _D132BfDatePrevOpEventMm = value;
}

// Get<>AsString()
public string GetD132BfDatePrevOpEventMmAsString()
{
    return _D132BfDatePrevOpEventMm.PadRight(2);
}

// Set<>AsString()
public void SetD132BfDatePrevOpEventMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132BfDatePrevOpEventMm = value;
}

// Standard Getter
public string GetD132BfDatePrevOpEventDd()
{
    return _D132BfDatePrevOpEventDd;
}

// Standard Setter
public void SetD132BfDatePrevOpEventDd(string value)
{
    _D132BfDatePrevOpEventDd = value;
}

// Get<>AsString()
public string GetD132BfDatePrevOpEventDdAsString()
{
    return _D132BfDatePrevOpEventDd.PadRight(2);
}

// Set<>AsString()
public void SetD132BfDatePrevOpEventDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132BfDatePrevOpEventDd = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D132DatePrevOpEventYtd
public class D132DatePrevOpEventYtd
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132YymmPrevOpEventYtd, is_external=, is_static_class=False, static_prefix=
    private D132DatePrevOpEventYtd.D132YymmPrevOpEventYtd _D132YymmPrevOpEventYtd = new D132DatePrevOpEventYtd.D132YymmPrevOpEventYtd();
    
    
    
    
    // [DEBUG] Field: D132DdPrevOpEventYtd, is_external=, is_static_class=False, static_prefix=
    private int _D132DdPrevOpEventYtd =0;
    
    
    
    
public D132DatePrevOpEventYtd() {}

public D132DatePrevOpEventYtd(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D132YymmPrevOpEventYtd.SetD132YymmPrevOpEventYtdAsString(data.Substring(offset, D132YymmPrevOpEventYtd.GetSize()));
    offset += 4;
    SetD132DdPrevOpEventYtd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD132DatePrevOpEventYtdAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132YymmPrevOpEventYtd.GetD132YymmPrevOpEventYtdAsString());
    result.Append(_D132DdPrevOpEventYtd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD132DatePrevOpEventYtdAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        _D132YymmPrevOpEventYtd.SetD132YymmPrevOpEventYtdAsString(data.Substring(offset, 4));
    }
    else
    {
        _D132YymmPrevOpEventYtd.SetD132YymmPrevOpEventYtdAsString(data.Substring(offset));
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD132DdPrevOpEventYtd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public D132YymmPrevOpEventYtd GetD132YymmPrevOpEventYtd()
{
    return _D132YymmPrevOpEventYtd;
}

// Standard Setter
public void SetD132YymmPrevOpEventYtd(D132YymmPrevOpEventYtd value)
{
    _D132YymmPrevOpEventYtd = value;
}

// Get<>AsString()
public string GetD132YymmPrevOpEventYtdAsString()
{
    return _D132YymmPrevOpEventYtd != null ? _D132YymmPrevOpEventYtd.GetD132YymmPrevOpEventYtdAsString() : "";
}

// Set<>AsString()
public void SetD132YymmPrevOpEventYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D132YymmPrevOpEventYtd == null)
    {
        _D132YymmPrevOpEventYtd = new D132YymmPrevOpEventYtd();
    }
    _D132YymmPrevOpEventYtd.SetD132YymmPrevOpEventYtdAsString(value);
}

// Standard Getter
public int GetD132DdPrevOpEventYtd()
{
    return _D132DdPrevOpEventYtd;
}

// Standard Setter
public void SetD132DdPrevOpEventYtd(int value)
{
    _D132DdPrevOpEventYtd = value;
}

// Get<>AsString()
public string GetD132DdPrevOpEventYtdAsString()
{
    return _D132DdPrevOpEventYtd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD132DdPrevOpEventYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D132DdPrevOpEventYtd = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D132YymmPrevOpEventYtd
public class D132YymmPrevOpEventYtd
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132YyPrevOpEventYtd, is_external=, is_static_class=False, static_prefix=
    private int _D132YyPrevOpEventYtd =0;
    
    
    
    
    // [DEBUG] Field: D132MmPrevOpEventYtd, is_external=, is_static_class=False, static_prefix=
    private int _D132MmPrevOpEventYtd =0;
    
    
    
    
public D132YymmPrevOpEventYtd() {}

public D132YymmPrevOpEventYtd(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD132YyPrevOpEventYtd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD132MmPrevOpEventYtd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD132YymmPrevOpEventYtdAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132YyPrevOpEventYtd.ToString().PadLeft(2, '0'));
    result.Append(_D132MmPrevOpEventYtd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD132YymmPrevOpEventYtdAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD132YyPrevOpEventYtd(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD132MmPrevOpEventYtd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetD132YyPrevOpEventYtd()
{
    return _D132YyPrevOpEventYtd;
}

// Standard Setter
public void SetD132YyPrevOpEventYtd(int value)
{
    _D132YyPrevOpEventYtd = value;
}

// Get<>AsString()
public string GetD132YyPrevOpEventYtdAsString()
{
    return _D132YyPrevOpEventYtd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD132YyPrevOpEventYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D132YyPrevOpEventYtd = parsed;
}

// Standard Getter
public int GetD132MmPrevOpEventYtd()
{
    return _D132MmPrevOpEventYtd;
}

// Standard Setter
public void SetD132MmPrevOpEventYtd(int value)
{
    _D132MmPrevOpEventYtd = value;
}

// Get<>AsString()
public string GetD132MmPrevOpEventYtdAsString()
{
    return _D132MmPrevOpEventYtd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD132MmPrevOpEventYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D132MmPrevOpEventYtd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
}
// Nested Class: D132BalCostList
public class D132BalCostList
{
    private static int _size = 13;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132BalCost, is_external=, is_static_class=False, static_prefix=
    private decimal _D132BalCost =0;
    
    
    
    
public D132BalCostList() {}

public D132BalCostList(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD132BalCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    
}

// Serialization methods
public string GetD132BalCostListAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132BalCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetD132BalCostListAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132BalCost(parsedDec);
    }
    offset += 13;
}

// Getter and Setter methods

// Standard Getter
public decimal GetD132BalCost()
{
    return _D132BalCost;
}

// Standard Setter
public void SetD132BalCost(decimal value)
{
    _D132BalCost = value;
}

// Get<>AsString()
public string GetD132BalCostAsString()
{
    return _D132BalCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132BalCostAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132BalCost = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: D132Record03Fields
public class D132Record03Fields
{
    private static int _size = 1759;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132DispExtra, is_external=, is_static_class=False, static_prefix=
    private D132Record03Fields.D132DispExtra _D132DispExtra = new D132Record03Fields.D132DispExtra();
    
    
    
    
    // [DEBUG] Field: Filler43, is_external=, is_static_class=False, static_prefix=
    private D132Record03Fields.Filler43 _Filler43 = new D132Record03Fields.Filler43();
    
    
    
    
public D132Record03Fields() {}

public D132Record03Fields(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D132DispExtra.SetD132DispExtraAsString(data.Substring(offset, D132DispExtra.GetSize()));
    offset += 160;
    _Filler43.SetFiller43AsString(data.Substring(offset, Filler43.GetSize()));
    offset += 1599;
    
}

// Serialization methods
public string GetD132Record03FieldsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132DispExtra.GetD132DispExtraAsString());
    result.Append(_Filler43.GetFiller43AsString());
    
    return result.ToString();
}

public void SetD132Record03FieldsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 160 <= data.Length)
    {
        _D132DispExtra.SetD132DispExtraAsString(data.Substring(offset, 160));
    }
    else
    {
        _D132DispExtra.SetD132DispExtraAsString(data.Substring(offset));
    }
    offset += 160;
    if (offset + 1599 <= data.Length)
    {
        _Filler43.SetFiller43AsString(data.Substring(offset, 1599));
    }
    else
    {
        _Filler43.SetFiller43AsString(data.Substring(offset));
    }
    offset += 1599;
}

// Getter and Setter methods

// Standard Getter
public D132DispExtra GetD132DispExtra()
{
    return _D132DispExtra;
}

// Standard Setter
public void SetD132DispExtra(D132DispExtra value)
{
    _D132DispExtra = value;
}

// Get<>AsString()
public string GetD132DispExtraAsString()
{
    return _D132DispExtra != null ? _D132DispExtra.GetD132DispExtraAsString() : "";
}

// Set<>AsString()
public void SetD132DispExtraAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D132DispExtra == null)
    {
        _D132DispExtra = new D132DispExtra();
    }
    _D132DispExtra.SetD132DispExtraAsString(value);
}

// Standard Getter
public Filler43 GetFiller43()
{
    return _Filler43;
}

// Standard Setter
public void SetFiller43(Filler43 value)
{
    _Filler43 = value;
}

// Get<>AsString()
public string GetFiller43AsString()
{
    return _Filler43 != null ? _Filler43.GetFiller43AsString() : "";
}

// Set<>AsString()
public void SetFiller43AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler43 == null)
    {
        _Filler43 = new Filler43();
    }
    _Filler43.SetFiller43AsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D132DispExtra
public class D132DispExtra
{
    private static int _size = 160;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132NumberOfUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _D132NumberOfUnits =0;
    
    
    
    
    // [DEBUG] Field: D132Proceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _D132Proceeds =0;
    
    
    
    
    // [DEBUG] Field: D132CapitalGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D132CapitalGainLoss =0;
    
    
    
    
    // [DEBUG] Field: D132Force2PctMatchFlag, is_external=, is_static_class=False, static_prefix=
    private string _D132Force2PctMatchFlag ="";
    
    
    
    
    // [DEBUG] Field: D132CgtCost, is_external=, is_static_class=False, static_prefix=
    private decimal _D132CgtCost =0;
    
    
    
    
    // [DEBUG] Field: D132ProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D132ProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: D132IndexationUsed, is_external=, is_static_class=False, static_prefix=
    private decimal _D132IndexationUsed =0;
    
    
    
    
    // [DEBUG] Field: D132BondDisposal, is_external=, is_static_class=False, static_prefix=
    private string _D132BondDisposal ="";
    
    
    
    
    // [DEBUG] Field: D132ConsolidatedFlag, is_external=, is_static_class=False, static_prefix=
    private string _D132ConsolidatedFlag ="";
    
    
    
    
    // [DEBUG] Field: D132StoreUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _D132StoreUnits =0;
    
    
    
    
    // [DEBUG] Field: D132StoreProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _D132StoreProceeds =0;
    
    
    
    
    // [DEBUG] Field: D132ConsolidateKey, is_external=, is_static_class=False, static_prefix=
    private string _D132ConsolidateKey ="";
    
    
    
    
    // [DEBUG] Field: D132Fa2003Exemption, is_external=, is_static_class=False, static_prefix=
    private string _D132Fa2003Exemption ="";
    
    
    
    
    // [DEBUG] Field: D132GtProRataMatch, is_external=, is_static_class=False, static_prefix=
    private string _D132GtProRataMatch ="";
    
    
    
    
    // [DEBUG] Field: D132GtUseOriginalDates, is_external=, is_static_class=False, static_prefix=
    private string _D132GtUseOriginalDates ="";
    
    
    
    
    // [DEBUG] Field: D132NumberOfUnitsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D132NumberOfUnitsYtd =0;
    
    
    
    
    // [DEBUG] Field: D132ProceedsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D132ProceedsYtd =0;
    
    
    
    
public D132DispExtra() {}

public D132DispExtra(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD132NumberOfUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetD132Proceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetD132CapitalGainLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetD132Force2PctMatchFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD132CgtCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetD132ProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetD132IndexationUsed(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetD132BondDisposal(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD132ConsolidatedFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD132StoreUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetD132StoreProceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetD132ConsolidateKey(data.Substring(offset, 23).Trim());
    offset += 23;
    SetD132Fa2003Exemption(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD132GtProRataMatch(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD132GtUseOriginalDates(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD132NumberOfUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 9)));
    offset += 9;
    SetD132ProceedsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 9)));
    offset += 9;
    
}

// Serialization methods
public string GetD132DispExtraAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132NumberOfUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132Proceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132CapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132Force2PctMatchFlag.PadRight(1));
    result.Append(_D132CgtCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132ProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132IndexationUsed.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132BondDisposal.PadRight(1));
    result.Append(_D132ConsolidatedFlag.PadRight(1));
    result.Append(_D132StoreUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132StoreProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132ConsolidateKey.PadRight(23));
    result.Append(_D132Fa2003Exemption.PadRight(0));
    result.Append(_D132GtProRataMatch.PadRight(0));
    result.Append(_D132GtUseOriginalDates.PadRight(0));
    result.Append(_D132NumberOfUnitsYtd.ToString("F3", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D132ProceedsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetD132DispExtraAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132NumberOfUnits(parsedDec);
    }
    offset += 13;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132Proceeds(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132CapitalGainLoss(parsedDec);
    }
    offset += 15;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD132Force2PctMatchFlag(extracted);
    }
    offset += 1;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132CgtCost(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132ProfitLoss(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132IndexationUsed(parsedDec);
    }
    offset += 15;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD132BondDisposal(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD132ConsolidatedFlag(extracted);
    }
    offset += 1;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132StoreUnits(parsedDec);
    }
    offset += 13;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132StoreProceeds(parsedDec);
    }
    offset += 15;
    if (offset + 23 <= data.Length)
    {
        string extracted = data.Substring(offset, 23).Trim();
        SetD132ConsolidateKey(extracted);
    }
    offset += 23;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD132Fa2003Exemption(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD132GtProRataMatch(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD132GtUseOriginalDates(extracted);
    }
    offset += 0;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132NumberOfUnitsYtd(parsedDec);
    }
    offset += 9;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD132ProceedsYtd(parsedDec);
    }
    offset += 9;
}

// Getter and Setter methods

// Standard Getter
public decimal GetD132NumberOfUnits()
{
    return _D132NumberOfUnits;
}

// Standard Setter
public void SetD132NumberOfUnits(decimal value)
{
    _D132NumberOfUnits = value;
}

// Get<>AsString()
public string GetD132NumberOfUnitsAsString()
{
    return _D132NumberOfUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132NumberOfUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132NumberOfUnits = parsed;
}

// Standard Getter
public decimal GetD132Proceeds()
{
    return _D132Proceeds;
}

// Standard Setter
public void SetD132Proceeds(decimal value)
{
    _D132Proceeds = value;
}

// Get<>AsString()
public string GetD132ProceedsAsString()
{
    return _D132Proceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132ProceedsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132Proceeds = parsed;
}

// Standard Getter
public decimal GetD132CapitalGainLoss()
{
    return _D132CapitalGainLoss;
}

// Standard Setter
public void SetD132CapitalGainLoss(decimal value)
{
    _D132CapitalGainLoss = value;
}

// Get<>AsString()
public string GetD132CapitalGainLossAsString()
{
    return _D132CapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132CapitalGainLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132CapitalGainLoss = parsed;
}

// Standard Getter
public string GetD132Force2PctMatchFlag()
{
    return _D132Force2PctMatchFlag;
}

// Standard Setter
public void SetD132Force2PctMatchFlag(string value)
{
    _D132Force2PctMatchFlag = value;
}

// Get<>AsString()
public string GetD132Force2PctMatchFlagAsString()
{
    return _D132Force2PctMatchFlag.PadRight(1);
}

// Set<>AsString()
public void SetD132Force2PctMatchFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132Force2PctMatchFlag = value;
}

// Standard Getter
public decimal GetD132CgtCost()
{
    return _D132CgtCost;
}

// Standard Setter
public void SetD132CgtCost(decimal value)
{
    _D132CgtCost = value;
}

// Get<>AsString()
public string GetD132CgtCostAsString()
{
    return _D132CgtCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132CgtCostAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132CgtCost = parsed;
}

// Standard Getter
public decimal GetD132ProfitLoss()
{
    return _D132ProfitLoss;
}

// Standard Setter
public void SetD132ProfitLoss(decimal value)
{
    _D132ProfitLoss = value;
}

// Get<>AsString()
public string GetD132ProfitLossAsString()
{
    return _D132ProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132ProfitLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132ProfitLoss = parsed;
}

// Standard Getter
public decimal GetD132IndexationUsed()
{
    return _D132IndexationUsed;
}

// Standard Setter
public void SetD132IndexationUsed(decimal value)
{
    _D132IndexationUsed = value;
}

// Get<>AsString()
public string GetD132IndexationUsedAsString()
{
    return _D132IndexationUsed.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132IndexationUsedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132IndexationUsed = parsed;
}

// Standard Getter
public string GetD132BondDisposal()
{
    return _D132BondDisposal;
}

// Standard Setter
public void SetD132BondDisposal(string value)
{
    _D132BondDisposal = value;
}

// Get<>AsString()
public string GetD132BondDisposalAsString()
{
    return _D132BondDisposal.PadRight(1);
}

// Set<>AsString()
public void SetD132BondDisposalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132BondDisposal = value;
}

// Standard Getter
public string GetD132ConsolidatedFlag()
{
    return _D132ConsolidatedFlag;
}

// Standard Setter
public void SetD132ConsolidatedFlag(string value)
{
    _D132ConsolidatedFlag = value;
}

// Get<>AsString()
public string GetD132ConsolidatedFlagAsString()
{
    return _D132ConsolidatedFlag.PadRight(1);
}

// Set<>AsString()
public void SetD132ConsolidatedFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132ConsolidatedFlag = value;
}

// Standard Getter
public decimal GetD132StoreUnits()
{
    return _D132StoreUnits;
}

// Standard Setter
public void SetD132StoreUnits(decimal value)
{
    _D132StoreUnits = value;
}

// Get<>AsString()
public string GetD132StoreUnitsAsString()
{
    return _D132StoreUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132StoreUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132StoreUnits = parsed;
}

// Standard Getter
public decimal GetD132StoreProceeds()
{
    return _D132StoreProceeds;
}

// Standard Setter
public void SetD132StoreProceeds(decimal value)
{
    _D132StoreProceeds = value;
}

// Get<>AsString()
public string GetD132StoreProceedsAsString()
{
    return _D132StoreProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132StoreProceedsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132StoreProceeds = parsed;
}

// Standard Getter
public string GetD132ConsolidateKey()
{
    return _D132ConsolidateKey;
}

// Standard Setter
public void SetD132ConsolidateKey(string value)
{
    _D132ConsolidateKey = value;
}

// Get<>AsString()
public string GetD132ConsolidateKeyAsString()
{
    return _D132ConsolidateKey.PadRight(23);
}

// Set<>AsString()
public void SetD132ConsolidateKeyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132ConsolidateKey = value;
}

// Standard Getter
public string GetD132Fa2003Exemption()
{
    return _D132Fa2003Exemption;
}

// Standard Setter
public void SetD132Fa2003Exemption(string value)
{
    _D132Fa2003Exemption = value;
}

// Get<>AsString()
public string GetD132Fa2003ExemptionAsString()
{
    return _D132Fa2003Exemption.PadRight(0);
}

// Set<>AsString()
public void SetD132Fa2003ExemptionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132Fa2003Exemption = value;
}

// Standard Getter
public string GetD132GtProRataMatch()
{
    return _D132GtProRataMatch;
}

// Standard Setter
public void SetD132GtProRataMatch(string value)
{
    _D132GtProRataMatch = value;
}

// Get<>AsString()
public string GetD132GtProRataMatchAsString()
{
    return _D132GtProRataMatch.PadRight(0);
}

// Set<>AsString()
public void SetD132GtProRataMatchAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132GtProRataMatch = value;
}

// Standard Getter
public string GetD132GtUseOriginalDates()
{
    return _D132GtUseOriginalDates;
}

// Standard Setter
public void SetD132GtUseOriginalDates(string value)
{
    _D132GtUseOriginalDates = value;
}

// Get<>AsString()
public string GetD132GtUseOriginalDatesAsString()
{
    return _D132GtUseOriginalDates.PadRight(0);
}

// Set<>AsString()
public void SetD132GtUseOriginalDatesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132GtUseOriginalDates = value;
}

// Standard Getter
public decimal GetD132NumberOfUnitsYtd()
{
    return _D132NumberOfUnitsYtd;
}

// Standard Setter
public void SetD132NumberOfUnitsYtd(decimal value)
{
    _D132NumberOfUnitsYtd = value;
}

// Get<>AsString()
public string GetD132NumberOfUnitsYtdAsString()
{
    return _D132NumberOfUnitsYtd.ToString("F3", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132NumberOfUnitsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132NumberOfUnitsYtd = parsed;
}

// Standard Getter
public decimal GetD132ProceedsYtd()
{
    return _D132ProceedsYtd;
}

// Standard Setter
public void SetD132ProceedsYtd(decimal value)
{
    _D132ProceedsYtd = value;
}

// Get<>AsString()
public string GetD132ProceedsYtdAsString()
{
    return _D132ProceedsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD132ProceedsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D132ProceedsYtd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler43
public class Filler43
{
    private static int _size = 1599;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler44, is_external=, is_static_class=False, static_prefix=
    private string _Filler44 ="";
    
    
    
    
public Filler43() {}

public Filler43(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller44(data.Substring(offset, 1599).Trim());
    offset += 1599;
    
}

// Serialization methods
public string GetFiller43AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler44.PadRight(1599));
    
    return result.ToString();
}

public void SetFiller43AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1599 <= data.Length)
    {
        string extracted = data.Substring(offset, 1599).Trim();
        SetFiller44(extracted);
    }
    offset += 1599;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller44()
{
    return _Filler44;
}

// Standard Setter
public void SetFiller44(string value)
{
    _Filler44 = value;
}

// Get<>AsString()
public string GetFiller44AsString()
{
    return _Filler44.PadRight(1599);
}

// Set<>AsString()
public void SetFiller44AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler44 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}

}}