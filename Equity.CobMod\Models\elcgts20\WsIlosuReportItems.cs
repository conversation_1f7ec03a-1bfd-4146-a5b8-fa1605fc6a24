using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WsIlosuReportItems Data Structure

public class WsIlosuReportItems
{
    private static int _size = 1545;
    // [DEBUG] Class: WsIlosuReportItems, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsIlosuHd1, is_external=, is_static_class=False, static_prefix=
    private WsIlosuHd1 _WsIlosuHd1 = new WsIlosuHd1();
    
    
    
    
    // [DEBUG] Field: WsIlosuHd2, is_external=, is_static_class=False, static_prefix=
    private WsIlosuHd2 _WsIlosuHd2 = new WsIlosuHd2();
    
    
    
    
    // [DEBUG] Field: WsIlosuHd3, is_external=, is_static_class=False, static_prefix=
    private WsIlosuHd3 _WsIlosuHd3 = new WsIlosuHd3();
    
    
    
    
    // [DEBUG] Field: WsIlosuHd4, is_external=, is_static_class=False, static_prefix=
    private WsIlosuHd4 _WsIlosuHd4 = new WsIlosuHd4();
    
    
    
    
    // [DEBUG] Field: WsIlosuHd5, is_external=, is_static_class=False, static_prefix=
    private WsIlosuHd5 _WsIlosuHd5 = new WsIlosuHd5();
    
    
    
    
    // [DEBUG] Field: WsIlosuDtl, is_external=, is_static_class=False, static_prefix=
    private WsIlosuDtl _WsIlosuDtl = new WsIlosuDtl();
    
    
    
    
    // [DEBUG] Field: WsIlosuTotLine, is_external=, is_static_class=False, static_prefix=
    private WsIlosuTotLine _WsIlosuTotLine = new WsIlosuTotLine();
    
    
    
    
    // [DEBUG] Field: WsIlosuOldFund, is_external=, is_static_class=False, static_prefix=
    private string _WsIlosuOldFund ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuOldStock, is_external=, is_static_class=False, static_prefix=
    private string _WsIlosuOldStock ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuOldCompFund, is_external=, is_static_class=False, static_prefix=
    private string _WsIlosuOldCompFund ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuOldFromDate, is_external=, is_static_class=False, static_prefix=
    private int _WsIlosuOldFromDate =0;
    
    
    
    
    // [DEBUG] Field: WsIlosuOldToDate, is_external=, is_static_class=False, static_prefix=
    private int _WsIlosuOldToDate =0;
    
    
    
    
    // [DEBUG] Field: WsIlosuLineCount, is_external=, is_static_class=False, static_prefix=
    private int _WsIlosuLineCount =0;
    
    
    
    
    // [DEBUG] Field: WsIlosuPageCount, is_external=, is_static_class=False, static_prefix=
    private int _WsIlosuPageCount =0;
    
    
    
    
    // [DEBUG] Field: WsIlosuNegHoldings, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuNegHoldings =0;
    
    
    
    
    // [DEBUG] Field: WsIlosuNegCgt, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuNegCgt =0;
    
    
    
    
    // [DEBUG] Field: WsLostIndexationU, is_external=, is_static_class=False, static_prefix=
    private decimal _WsLostIndexationU =0;
    
    
    
    
    // [DEBUG] Field: WsIlosuIndexation, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuIndexation =0;
    
    
    
    
    // [DEBUG] Field: WsIlosuTotIndxn, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuTotIndxn =0;
    
    
    
    
    // [DEBUG] Field: WsIlosuFundHoldTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuFundHoldTot =0;
    
    
    
    
    // [DEBUG] Field: WsIlosuFundCgtTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuFundCgtTot =0;
    
    
    
    
    // [DEBUG] Field: WsIlosuFundProcTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuFundProcTot =0;
    
    
    
    
    // [DEBUG] Field: WsIlosuFundGainTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuFundGainTot =0;
    
    
    
    
    // [DEBUG] Field: WsIlosuFundIndxTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuFundIndxTot =0;
    
    
    
    
    // [DEBUG] Field: WsIlosuFundIlosuTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuFundIlosuTot =0;
    
    
    
    
    // [DEBUG] Field: WsIlosuFundTotIndxnTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuFundTotIndxnTot =0;
    
    
    
    
    // [DEBUG] Field: WsIlosuFirstTime, is_external=, is_static_class=False, static_prefix=
    private string _WsIlosuFirstTime ="Y";
    
    
    // 88-level condition checks for WsIlosuFirstTime
    public bool IsIlosuFirstTime()
    {
        if (this._WsIlosuFirstTime == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsIlosuRecordWritten, is_external=, is_static_class=False, static_prefix=
    private string _WsIlosuRecordWritten ="Y";
    
    
    // 88-level condition checks for WsIlosuRecordWritten
    public bool IsIlosuRecordWritten()
    {
        if (this._WsIlosuRecordWritten == "'Y'") return true;
        return false;
    }
    
    
    
    // Serialization methods
    public string GetWsIlosuReportItemsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsIlosuHd1.GetWsIlosuHd1AsString());
        result.Append(_WsIlosuHd2.GetWsIlosuHd2AsString());
        result.Append(_WsIlosuHd3.GetWsIlosuHd3AsString());
        result.Append(_WsIlosuHd4.GetWsIlosuHd4AsString());
        result.Append(_WsIlosuHd5.GetWsIlosuHd5AsString());
        result.Append(_WsIlosuDtl.GetWsIlosuDtlAsString());
        result.Append(_WsIlosuTotLine.GetWsIlosuTotLineAsString());
        result.Append(_WsIlosuOldFund.PadRight(4));
        result.Append(_WsIlosuOldStock.PadRight(7));
        result.Append(_WsIlosuOldCompFund.PadRight(60));
        result.Append(_WsIlosuOldFromDate.ToString().PadLeft(6, '0'));
        result.Append(_WsIlosuOldToDate.ToString().PadLeft(6, '0'));
        result.Append(_WsIlosuLineCount.ToString().PadLeft(4, '0'));
        result.Append(_WsIlosuPageCount.ToString().PadLeft(4, '0'));
        result.Append(_WsIlosuNegHoldings.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlosuNegCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsLostIndexationU.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlosuIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlosuTotIndxn.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlosuFundHoldTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlosuFundCgtTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlosuFundProcTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlosuFundGainTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlosuFundIndxTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlosuFundIlosuTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlosuFundTotIndxnTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlosuFirstTime.PadRight(1));
        result.Append(_WsIlosuRecordWritten.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWsIlosuReportItemsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 109 <= data.Length)
        {
            _WsIlosuHd1.SetWsIlosuHd1AsString(data.Substring(offset, 109));
        }
        else
        {
            _WsIlosuHd1.SetWsIlosuHd1AsString(data.Substring(offset));
        }
        offset += 109;
        if (offset + 174 <= data.Length)
        {
            _WsIlosuHd2.SetWsIlosuHd2AsString(data.Substring(offset, 174));
        }
        else
        {
            _WsIlosuHd2.SetWsIlosuHd2AsString(data.Substring(offset));
        }
        offset += 174;
        if (offset + 174 <= data.Length)
        {
            _WsIlosuHd3.SetWsIlosuHd3AsString(data.Substring(offset, 174));
        }
        else
        {
            _WsIlosuHd3.SetWsIlosuHd3AsString(data.Substring(offset));
        }
        offset += 174;
        if (offset + 174 <= data.Length)
        {
            _WsIlosuHd4.SetWsIlosuHd4AsString(data.Substring(offset, 174));
        }
        else
        {
            _WsIlosuHd4.SetWsIlosuHd4AsString(data.Substring(offset));
        }
        offset += 174;
        if (offset + 185 <= data.Length)
        {
            _WsIlosuHd5.SetWsIlosuHd5AsString(data.Substring(offset, 185));
        }
        else
        {
            _WsIlosuHd5.SetWsIlosuHd5AsString(data.Substring(offset));
        }
        offset += 185;
        if (offset + 291 <= data.Length)
        {
            _WsIlosuDtl.SetWsIlosuDtlAsString(data.Substring(offset, 291));
        }
        else
        {
            _WsIlosuDtl.SetWsIlosuDtlAsString(data.Substring(offset));
        }
        offset += 291;
        if (offset + 141 <= data.Length)
        {
            _WsIlosuTotLine.SetWsIlosuTotLineAsString(data.Substring(offset, 141));
        }
        else
        {
            _WsIlosuTotLine.SetWsIlosuTotLineAsString(data.Substring(offset));
        }
        offset += 141;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWsIlosuOldFund(extracted);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetWsIlosuOldStock(extracted);
        }
        offset += 7;
        if (offset + 60 <= data.Length)
        {
            string extracted = data.Substring(offset, 60).Trim();
            SetWsIlosuOldCompFund(extracted);
        }
        offset += 60;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsIlosuOldFromDate(parsedInt);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsIlosuOldToDate(parsedInt);
        }
        offset += 6;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsIlosuLineCount(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsIlosuPageCount(parsedInt);
        }
        offset += 4;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlosuNegHoldings(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlosuNegCgt(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsLostIndexationU(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlosuIndexation(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlosuTotIndxn(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlosuFundHoldTot(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlosuFundCgtTot(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlosuFundProcTot(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlosuFundGainTot(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlosuFundIndxTot(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlosuFundIlosuTot(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlosuFundTotIndxnTot(parsedDec);
        }
        offset += 17;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsIlosuFirstTime(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsIlosuRecordWritten(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsIlosuReportItemsAsString();
    }
    // Set<>String Override function
    public void SetWsIlosuReportItems(string value)
    {
        SetWsIlosuReportItemsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public WsIlosuHd1 GetWsIlosuHd1()
    {
        return _WsIlosuHd1;
    }
    
    // Standard Setter
    public void SetWsIlosuHd1(WsIlosuHd1 value)
    {
        _WsIlosuHd1 = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuHd1AsString()
    {
        return _WsIlosuHd1 != null ? _WsIlosuHd1.GetWsIlosuHd1AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsIlosuHd1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsIlosuHd1 == null)
        {
            _WsIlosuHd1 = new WsIlosuHd1();
        }
        _WsIlosuHd1.SetWsIlosuHd1AsString(value);
    }
    
    // Standard Getter
    public WsIlosuHd2 GetWsIlosuHd2()
    {
        return _WsIlosuHd2;
    }
    
    // Standard Setter
    public void SetWsIlosuHd2(WsIlosuHd2 value)
    {
        _WsIlosuHd2 = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuHd2AsString()
    {
        return _WsIlosuHd2 != null ? _WsIlosuHd2.GetWsIlosuHd2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsIlosuHd2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsIlosuHd2 == null)
        {
            _WsIlosuHd2 = new WsIlosuHd2();
        }
        _WsIlosuHd2.SetWsIlosuHd2AsString(value);
    }
    
    // Standard Getter
    public WsIlosuHd3 GetWsIlosuHd3()
    {
        return _WsIlosuHd3;
    }
    
    // Standard Setter
    public void SetWsIlosuHd3(WsIlosuHd3 value)
    {
        _WsIlosuHd3 = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuHd3AsString()
    {
        return _WsIlosuHd3 != null ? _WsIlosuHd3.GetWsIlosuHd3AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsIlosuHd3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsIlosuHd3 == null)
        {
            _WsIlosuHd3 = new WsIlosuHd3();
        }
        _WsIlosuHd3.SetWsIlosuHd3AsString(value);
    }
    
    // Standard Getter
    public WsIlosuHd4 GetWsIlosuHd4()
    {
        return _WsIlosuHd4;
    }
    
    // Standard Setter
    public void SetWsIlosuHd4(WsIlosuHd4 value)
    {
        _WsIlosuHd4 = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuHd4AsString()
    {
        return _WsIlosuHd4 != null ? _WsIlosuHd4.GetWsIlosuHd4AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsIlosuHd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsIlosuHd4 == null)
        {
            _WsIlosuHd4 = new WsIlosuHd4();
        }
        _WsIlosuHd4.SetWsIlosuHd4AsString(value);
    }
    
    // Standard Getter
    public WsIlosuHd5 GetWsIlosuHd5()
    {
        return _WsIlosuHd5;
    }
    
    // Standard Setter
    public void SetWsIlosuHd5(WsIlosuHd5 value)
    {
        _WsIlosuHd5 = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuHd5AsString()
    {
        return _WsIlosuHd5 != null ? _WsIlosuHd5.GetWsIlosuHd5AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsIlosuHd5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsIlosuHd5 == null)
        {
            _WsIlosuHd5 = new WsIlosuHd5();
        }
        _WsIlosuHd5.SetWsIlosuHd5AsString(value);
    }
    
    // Standard Getter
    public WsIlosuDtl GetWsIlosuDtl()
    {
        return _WsIlosuDtl;
    }
    
    // Standard Setter
    public void SetWsIlosuDtl(WsIlosuDtl value)
    {
        _WsIlosuDtl = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuDtlAsString()
    {
        return _WsIlosuDtl != null ? _WsIlosuDtl.GetWsIlosuDtlAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsIlosuDtlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsIlosuDtl == null)
        {
            _WsIlosuDtl = new WsIlosuDtl();
        }
        _WsIlosuDtl.SetWsIlosuDtlAsString(value);
    }
    
    // Standard Getter
    public WsIlosuTotLine GetWsIlosuTotLine()
    {
        return _WsIlosuTotLine;
    }
    
    // Standard Setter
    public void SetWsIlosuTotLine(WsIlosuTotLine value)
    {
        _WsIlosuTotLine = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuTotLineAsString()
    {
        return _WsIlosuTotLine != null ? _WsIlosuTotLine.GetWsIlosuTotLineAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsIlosuTotLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsIlosuTotLine == null)
        {
            _WsIlosuTotLine = new WsIlosuTotLine();
        }
        _WsIlosuTotLine.SetWsIlosuTotLineAsString(value);
    }
    
    // Standard Getter
    public string GetWsIlosuOldFund()
    {
        return _WsIlosuOldFund;
    }
    
    // Standard Setter
    public void SetWsIlosuOldFund(string value)
    {
        _WsIlosuOldFund = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuOldFundAsString()
    {
        return _WsIlosuOldFund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWsIlosuOldFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsIlosuOldFund = value;
    }
    
    // Standard Getter
    public string GetWsIlosuOldStock()
    {
        return _WsIlosuOldStock;
    }
    
    // Standard Setter
    public void SetWsIlosuOldStock(string value)
    {
        _WsIlosuOldStock = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuOldStockAsString()
    {
        return _WsIlosuOldStock.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetWsIlosuOldStockAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsIlosuOldStock = value;
    }
    
    // Standard Getter
    public string GetWsIlosuOldCompFund()
    {
        return _WsIlosuOldCompFund;
    }
    
    // Standard Setter
    public void SetWsIlosuOldCompFund(string value)
    {
        _WsIlosuOldCompFund = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuOldCompFundAsString()
    {
        return _WsIlosuOldCompFund.PadRight(60);
    }
    
    // Set<>AsString()
    public void SetWsIlosuOldCompFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsIlosuOldCompFund = value;
    }
    
    // Standard Getter
    public int GetWsIlosuOldFromDate()
    {
        return _WsIlosuOldFromDate;
    }
    
    // Standard Setter
    public void SetWsIlosuOldFromDate(int value)
    {
        _WsIlosuOldFromDate = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuOldFromDateAsString()
    {
        return _WsIlosuOldFromDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetWsIlosuOldFromDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsIlosuOldFromDate = parsed;
    }
    
    // Standard Getter
    public int GetWsIlosuOldToDate()
    {
        return _WsIlosuOldToDate;
    }
    
    // Standard Setter
    public void SetWsIlosuOldToDate(int value)
    {
        _WsIlosuOldToDate = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuOldToDateAsString()
    {
        return _WsIlosuOldToDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetWsIlosuOldToDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsIlosuOldToDate = parsed;
    }
    
    // Standard Getter
    public int GetWsIlosuLineCount()
    {
        return _WsIlosuLineCount;
    }
    
    // Standard Setter
    public void SetWsIlosuLineCount(int value)
    {
        _WsIlosuLineCount = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuLineCountAsString()
    {
        return _WsIlosuLineCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsIlosuLineCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsIlosuLineCount = parsed;
    }
    
    // Standard Getter
    public int GetWsIlosuPageCount()
    {
        return _WsIlosuPageCount;
    }
    
    // Standard Setter
    public void SetWsIlosuPageCount(int value)
    {
        _WsIlosuPageCount = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuPageCountAsString()
    {
        return _WsIlosuPageCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsIlosuPageCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsIlosuPageCount = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlosuNegHoldings()
    {
        return _WsIlosuNegHoldings;
    }
    
    // Standard Setter
    public void SetWsIlosuNegHoldings(decimal value)
    {
        _WsIlosuNegHoldings = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuNegHoldingsAsString()
    {
        return _WsIlosuNegHoldings.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlosuNegHoldingsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuNegHoldings = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlosuNegCgt()
    {
        return _WsIlosuNegCgt;
    }
    
    // Standard Setter
    public void SetWsIlosuNegCgt(decimal value)
    {
        _WsIlosuNegCgt = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuNegCgtAsString()
    {
        return _WsIlosuNegCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlosuNegCgtAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuNegCgt = parsed;
    }
    
    // Standard Getter
    public decimal GetWsLostIndexationU()
    {
        return _WsLostIndexationU;
    }
    
    // Standard Setter
    public void SetWsLostIndexationU(decimal value)
    {
        _WsLostIndexationU = value;
    }
    
    // Get<>AsString()
    public string GetWsLostIndexationUAsString()
    {
        return _WsLostIndexationU.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsLostIndexationUAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsLostIndexationU = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlosuIndexation()
    {
        return _WsIlosuIndexation;
    }
    
    // Standard Setter
    public void SetWsIlosuIndexation(decimal value)
    {
        _WsIlosuIndexation = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuIndexationAsString()
    {
        return _WsIlosuIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlosuIndexationAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuIndexation = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlosuTotIndxn()
    {
        return _WsIlosuTotIndxn;
    }
    
    // Standard Setter
    public void SetWsIlosuTotIndxn(decimal value)
    {
        _WsIlosuTotIndxn = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuTotIndxnAsString()
    {
        return _WsIlosuTotIndxn.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlosuTotIndxnAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuTotIndxn = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlosuFundHoldTot()
    {
        return _WsIlosuFundHoldTot;
    }
    
    // Standard Setter
    public void SetWsIlosuFundHoldTot(decimal value)
    {
        _WsIlosuFundHoldTot = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuFundHoldTotAsString()
    {
        return _WsIlosuFundHoldTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlosuFundHoldTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuFundHoldTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlosuFundCgtTot()
    {
        return _WsIlosuFundCgtTot;
    }
    
    // Standard Setter
    public void SetWsIlosuFundCgtTot(decimal value)
    {
        _WsIlosuFundCgtTot = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuFundCgtTotAsString()
    {
        return _WsIlosuFundCgtTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlosuFundCgtTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuFundCgtTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlosuFundProcTot()
    {
        return _WsIlosuFundProcTot;
    }
    
    // Standard Setter
    public void SetWsIlosuFundProcTot(decimal value)
    {
        _WsIlosuFundProcTot = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuFundProcTotAsString()
    {
        return _WsIlosuFundProcTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlosuFundProcTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuFundProcTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlosuFundGainTot()
    {
        return _WsIlosuFundGainTot;
    }
    
    // Standard Setter
    public void SetWsIlosuFundGainTot(decimal value)
    {
        _WsIlosuFundGainTot = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuFundGainTotAsString()
    {
        return _WsIlosuFundGainTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlosuFundGainTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuFundGainTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlosuFundIndxTot()
    {
        return _WsIlosuFundIndxTot;
    }
    
    // Standard Setter
    public void SetWsIlosuFundIndxTot(decimal value)
    {
        _WsIlosuFundIndxTot = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuFundIndxTotAsString()
    {
        return _WsIlosuFundIndxTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlosuFundIndxTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuFundIndxTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlosuFundIlosuTot()
    {
        return _WsIlosuFundIlosuTot;
    }
    
    // Standard Setter
    public void SetWsIlosuFundIlosuTot(decimal value)
    {
        _WsIlosuFundIlosuTot = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuFundIlosuTotAsString()
    {
        return _WsIlosuFundIlosuTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlosuFundIlosuTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuFundIlosuTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlosuFundTotIndxnTot()
    {
        return _WsIlosuFundTotIndxnTot;
    }
    
    // Standard Setter
    public void SetWsIlosuFundTotIndxnTot(decimal value)
    {
        _WsIlosuFundTotIndxnTot = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuFundTotIndxnTotAsString()
    {
        return _WsIlosuFundTotIndxnTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlosuFundTotIndxnTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuFundTotIndxnTot = parsed;
    }
    
    // Standard Getter
    public string GetWsIlosuFirstTime()
    {
        return _WsIlosuFirstTime;
    }
    
    // Standard Setter
    public void SetWsIlosuFirstTime(string value)
    {
        _WsIlosuFirstTime = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuFirstTimeAsString()
    {
        return _WsIlosuFirstTime.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsIlosuFirstTimeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsIlosuFirstTime = value;
    }
    
    // Standard Getter
    public string GetWsIlosuRecordWritten()
    {
        return _WsIlosuRecordWritten;
    }
    
    // Standard Setter
    public void SetWsIlosuRecordWritten(string value)
    {
        _WsIlosuRecordWritten = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuRecordWrittenAsString()
    {
        return _WsIlosuRecordWritten.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsIlosuRecordWrittenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsIlosuRecordWritten = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWsIlosuHd1(string value)
    {
        _WsIlosuHd1.SetWsIlosuHd1AsString(value);
    }
    // Nested Class: WsIlosuHd1
    public class WsIlosuHd1
    {
        private static int _size = 109;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WsIlosuCompName, is_external=, is_static_class=False, static_prefix=
        private string _WsIlosuCompName ="";
        
        
        
        
        // [DEBUG] Field: Filler291, is_external=, is_static_class=False, static_prefix=
        private string _Filler291 ="Schedule of Unrealised Indexation Disallowed";
        
        
        
        
    public WsIlosuHd1() {}
    
    public WsIlosuHd1(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWsIlosuCompName(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller291(data.Substring(offset, 109).Trim());
        offset += 109;
        
    }
    
    // Serialization methods
    public string GetWsIlosuHd1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsIlosuCompName.PadRight(0));
        result.Append(_Filler291.PadRight(109));
        
        return result.ToString();
    }
    
    public void SetWsIlosuHd1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWsIlosuCompName(extracted);
        }
        offset += 0;
        if (offset + 109 <= data.Length)
        {
            string extracted = data.Substring(offset, 109).Trim();
            SetFiller291(extracted);
        }
        offset += 109;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsIlosuCompName()
    {
        return _WsIlosuCompName;
    }
    
    // Standard Setter
    public void SetWsIlosuCompName(string value)
    {
        _WsIlosuCompName = value;
    }
    
    // Get<>AsString()
    public string GetWsIlosuCompNameAsString()
    {
        return _WsIlosuCompName.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWsIlosuCompNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsIlosuCompName = value;
    }
    
    // Standard Getter
    public string GetFiller291()
    {
        return _Filler291;
    }
    
    // Standard Setter
    public void SetFiller291(string value)
    {
        _Filler291 = value;
    }
    
    // Get<>AsString()
    public string GetFiller291AsString()
    {
        return _Filler291.PadRight(109);
    }
    
    // Set<>AsString()
    public void SetFiller291AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler291 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetWsIlosuHd2(string value)
{
    _WsIlosuHd2.SetWsIlosuHd2AsString(value);
}
// Nested Class: WsIlosuHd2
public class WsIlosuHd2
{
    private static int _size = 174;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler292, is_external=, is_static_class=False, static_prefix=
    private string _Filler292 ="";
    
    
    
    
    // [DEBUG] Field: Filler293, is_external=, is_static_class=False, static_prefix=
    private string _Filler293 ="Fund";
    
    
    
    
    // [DEBUG] Field: Filler294, is_external=, is_static_class=False, static_prefix=
    private string _Filler294 ="Period";
    
    
    
    
    // [DEBUG] Field: Filler295, is_external=, is_static_class=False, static_prefix=
    private string _Filler295 ="Page";
    
    
    
    
public WsIlosuHd2() {}

public WsIlosuHd2(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller292(data.Substring(offset, 140).Trim());
    offset += 140;
    SetFiller293(data.Substring(offset, 15).Trim());
    offset += 15;
    SetFiller294(data.Substring(offset, 15).Trim());
    offset += 15;
    SetFiller295(data.Substring(offset, 4).Trim());
    offset += 4;
    
}

// Serialization methods
public string GetWsIlosuHd2AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler292.PadRight(140));
    result.Append(_Filler293.PadRight(15));
    result.Append(_Filler294.PadRight(15));
    result.Append(_Filler295.PadRight(4));
    
    return result.ToString();
}

public void SetWsIlosuHd2AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 140 <= data.Length)
    {
        string extracted = data.Substring(offset, 140).Trim();
        SetFiller292(extracted);
    }
    offset += 140;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetFiller293(extracted);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetFiller294(extracted);
    }
    offset += 15;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetFiller295(extracted);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller292()
{
    return _Filler292;
}

// Standard Setter
public void SetFiller292(string value)
{
    _Filler292 = value;
}

// Get<>AsString()
public string GetFiller292AsString()
{
    return _Filler292.PadRight(140);
}

// Set<>AsString()
public void SetFiller292AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler292 = value;
}

// Standard Getter
public string GetFiller293()
{
    return _Filler293;
}

// Standard Setter
public void SetFiller293(string value)
{
    _Filler293 = value;
}

// Get<>AsString()
public string GetFiller293AsString()
{
    return _Filler293.PadRight(15);
}

// Set<>AsString()
public void SetFiller293AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler293 = value;
}

// Standard Getter
public string GetFiller294()
{
    return _Filler294;
}

// Standard Setter
public void SetFiller294(string value)
{
    _Filler294 = value;
}

// Get<>AsString()
public string GetFiller294AsString()
{
    return _Filler294.PadRight(15);
}

// Set<>AsString()
public void SetFiller294AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler294 = value;
}

// Standard Getter
public string GetFiller295()
{
    return _Filler295;
}

// Standard Setter
public void SetFiller295(string value)
{
    _Filler295 = value;
}

// Get<>AsString()
public string GetFiller295AsString()
{
    return _Filler295.PadRight(4);
}

// Set<>AsString()
public void SetFiller295AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler295 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsIlosuHd3(string value)
{
    _WsIlosuHd3.SetWsIlosuHd3AsString(value);
}
// Nested Class: WsIlosuHd3
public class WsIlosuHd3
{
    private static int _size = 174;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler296, is_external=, is_static_class=False, static_prefix=
    private string _Filler296 ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuCompFund, is_external=, is_static_class=False, static_prefix=
    private string _WsIlosuCompFund ="";
    
    
    
    
    // [DEBUG] Field: Filler297, is_external=, is_static_class=False, static_prefix=
    private string _Filler297 =" ";
    
    
    
    
    // [DEBUG] Field: WsIlosuFund, is_external=, is_static_class=False, static_prefix=
    private string _WsIlosuFund ="";
    
    
    
    
    // [DEBUG] Field: Filler298, is_external=, is_static_class=False, static_prefix=
    private string _Filler298 =" ";
    
    
    
    
    // [DEBUG] Field: WsIlosuPeriod, is_external=, is_static_class=False, static_prefix=
    private WsIlosuHd3.WsIlosuPeriod _WsIlosuPeriod = new WsIlosuHd3.WsIlosuPeriod();
    
    
    
    
    // [DEBUG] Field: Filler300, is_external=, is_static_class=False, static_prefix=
    private string _Filler300 =" ";
    
    
    
    
    // [DEBUG] Field: WsIlosuPageNo, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuPageNo =0;
    
    
    
    
public WsIlosuHd3() {}

public WsIlosuHd3(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller296(data.Substring(offset, 56).Trim());
    offset += 56;
    SetWsIlosuCompFund(data.Substring(offset, 60).Trim());
    offset += 60;
    SetFiller297(data.Substring(offset, 24).Trim());
    offset += 24;
    SetWsIlosuFund(data.Substring(offset, 4).Trim());
    offset += 4;
    SetFiller298(data.Substring(offset, 7).Trim());
    offset += 7;
    _WsIlosuPeriod.SetWsIlosuPeriodAsString(data.Substring(offset, WsIlosuPeriod.GetSize()));
    offset += 15;
    SetFiller300(data.Substring(offset, 4).Trim());
    offset += 4;
    SetWsIlosuPageNo(PackedDecimalConverter.ToDecimal(data.Substring(offset, 4)));
    offset += 4;
    
}

// Serialization methods
public string GetWsIlosuHd3AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler296.PadRight(56));
    result.Append(_WsIlosuCompFund.PadRight(60));
    result.Append(_Filler297.PadRight(24));
    result.Append(_WsIlosuFund.PadRight(4));
    result.Append(_Filler298.PadRight(7));
    result.Append(_WsIlosuPeriod.GetWsIlosuPeriodAsString());
    result.Append(_Filler300.PadRight(4));
    result.Append(_WsIlosuPageNo.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetWsIlosuHd3AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 56 <= data.Length)
    {
        string extracted = data.Substring(offset, 56).Trim();
        SetFiller296(extracted);
    }
    offset += 56;
    if (offset + 60 <= data.Length)
    {
        string extracted = data.Substring(offset, 60).Trim();
        SetWsIlosuCompFund(extracted);
    }
    offset += 60;
    if (offset + 24 <= data.Length)
    {
        string extracted = data.Substring(offset, 24).Trim();
        SetFiller297(extracted);
    }
    offset += 24;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWsIlosuFund(extracted);
    }
    offset += 4;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetFiller298(extracted);
    }
    offset += 7;
    if (offset + 15 <= data.Length)
    {
        _WsIlosuPeriod.SetWsIlosuPeriodAsString(data.Substring(offset, 15));
    }
    else
    {
        _WsIlosuPeriod.SetWsIlosuPeriodAsString(data.Substring(offset));
    }
    offset += 15;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetFiller300(extracted);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlosuPageNo(parsedDec);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller296()
{
    return _Filler296;
}

// Standard Setter
public void SetFiller296(string value)
{
    _Filler296 = value;
}

// Get<>AsString()
public string GetFiller296AsString()
{
    return _Filler296.PadRight(56);
}

// Set<>AsString()
public void SetFiller296AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler296 = value;
}

// Standard Getter
public string GetWsIlosuCompFund()
{
    return _WsIlosuCompFund;
}

// Standard Setter
public void SetWsIlosuCompFund(string value)
{
    _WsIlosuCompFund = value;
}

// Get<>AsString()
public string GetWsIlosuCompFundAsString()
{
    return _WsIlosuCompFund.PadRight(60);
}

// Set<>AsString()
public void SetWsIlosuCompFundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlosuCompFund = value;
}

// Standard Getter
public string GetFiller297()
{
    return _Filler297;
}

// Standard Setter
public void SetFiller297(string value)
{
    _Filler297 = value;
}

// Get<>AsString()
public string GetFiller297AsString()
{
    return _Filler297.PadRight(24);
}

// Set<>AsString()
public void SetFiller297AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler297 = value;
}

// Standard Getter
public string GetWsIlosuFund()
{
    return _WsIlosuFund;
}

// Standard Setter
public void SetWsIlosuFund(string value)
{
    _WsIlosuFund = value;
}

// Get<>AsString()
public string GetWsIlosuFundAsString()
{
    return _WsIlosuFund.PadRight(4);
}

// Set<>AsString()
public void SetWsIlosuFundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlosuFund = value;
}

// Standard Getter
public string GetFiller298()
{
    return _Filler298;
}

// Standard Setter
public void SetFiller298(string value)
{
    _Filler298 = value;
}

// Get<>AsString()
public string GetFiller298AsString()
{
    return _Filler298.PadRight(7);
}

// Set<>AsString()
public void SetFiller298AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler298 = value;
}

// Standard Getter
public WsIlosuPeriod GetWsIlosuPeriod()
{
    return _WsIlosuPeriod;
}

// Standard Setter
public void SetWsIlosuPeriod(WsIlosuPeriod value)
{
    _WsIlosuPeriod = value;
}

// Get<>AsString()
public string GetWsIlosuPeriodAsString()
{
    return _WsIlosuPeriod != null ? _WsIlosuPeriod.GetWsIlosuPeriodAsString() : "";
}

// Set<>AsString()
public void SetWsIlosuPeriodAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsIlosuPeriod == null)
    {
        _WsIlosuPeriod = new WsIlosuPeriod();
    }
    _WsIlosuPeriod.SetWsIlosuPeriodAsString(value);
}

// Standard Getter
public string GetFiller300()
{
    return _Filler300;
}

// Standard Setter
public void SetFiller300(string value)
{
    _Filler300 = value;
}

// Get<>AsString()
public string GetFiller300AsString()
{
    return _Filler300.PadRight(4);
}

// Set<>AsString()
public void SetFiller300AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler300 = value;
}

// Standard Getter
public decimal GetWsIlosuPageNo()
{
    return _WsIlosuPageNo;
}

// Standard Setter
public void SetWsIlosuPageNo(decimal value)
{
    _WsIlosuPageNo = value;
}

// Get<>AsString()
public string GetWsIlosuPageNoAsString()
{
    return _WsIlosuPageNo.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlosuPageNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuPageNo = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WsIlosuPeriod
public class WsIlosuPeriod
{
    private static int _size = 15;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsIlosuFromDate, is_external=, is_static_class=False, static_prefix=
    private string _WsIlosuFromDate ="";
    
    
    
    
    // [DEBUG] Field: Filler299, is_external=, is_static_class=False, static_prefix=
    private string _Filler299 ="-";
    
    
    
    
    // [DEBUG] Field: WsIlosuToDate, is_external=, is_static_class=False, static_prefix=
    private string _WsIlosuToDate ="";
    
    
    
    
public WsIlosuPeriod() {}

public WsIlosuPeriod(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsIlosuFromDate(data.Substring(offset, 7).Trim());
    offset += 7;
    SetFiller299(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlosuToDate(data.Substring(offset, 7).Trim());
    offset += 7;
    
}

// Serialization methods
public string GetWsIlosuPeriodAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsIlosuFromDate.PadRight(7));
    result.Append(_Filler299.PadRight(1));
    result.Append(_WsIlosuToDate.PadRight(7));
    
    return result.ToString();
}

public void SetWsIlosuPeriodAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetWsIlosuFromDate(extracted);
    }
    offset += 7;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller299(extracted);
    }
    offset += 1;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetWsIlosuToDate(extracted);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetWsIlosuFromDate()
{
    return _WsIlosuFromDate;
}

// Standard Setter
public void SetWsIlosuFromDate(string value)
{
    _WsIlosuFromDate = value;
}

// Get<>AsString()
public string GetWsIlosuFromDateAsString()
{
    return _WsIlosuFromDate.PadRight(7);
}

// Set<>AsString()
public void SetWsIlosuFromDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlosuFromDate = value;
}

// Standard Getter
public string GetFiller299()
{
    return _Filler299;
}

// Standard Setter
public void SetFiller299(string value)
{
    _Filler299 = value;
}

// Get<>AsString()
public string GetFiller299AsString()
{
    return _Filler299.PadRight(1);
}

// Set<>AsString()
public void SetFiller299AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler299 = value;
}

// Standard Getter
public string GetWsIlosuToDate()
{
    return _WsIlosuToDate;
}

// Standard Setter
public void SetWsIlosuToDate(string value)
{
    _WsIlosuToDate = value;
}

// Get<>AsString()
public string GetWsIlosuToDateAsString()
{
    return _WsIlosuToDate.PadRight(7);
}

// Set<>AsString()
public void SetWsIlosuToDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlosuToDate = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetWsIlosuHd4(string value)
{
    _WsIlosuHd4.SetWsIlosuHd4AsString(value);
}
// Nested Class: WsIlosuHd4
public class WsIlosuHd4
{
    private static int _size = 174;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler301, is_external=, is_static_class=False, static_prefix=
    private string _Filler301 ="Security Code";
    
    
    
    
    // [DEBUG] Field: Filler302, is_external=, is_static_class=False, static_prefix=
    private string _Filler302 ="Type ";
    
    
    
    
    // [DEBUG] Field: Filler303, is_external=, is_static_class=False, static_prefix=
    private string _Filler303 ="Bargain  ";
    
    
    
    
    // [DEBUG] Field: Filler304, is_external=, is_static_class=False, static_prefix=
    private string _Filler304 ="     Holdings";
    
    
    
    
    // [DEBUG] Field: Filler305, is_external=, is_static_class=False, static_prefix=
    private string _Filler305 ="      \"CGT\"";
    
    
    
    
    // [DEBUG] Field: Filler306, is_external=, is_static_class=False, static_prefix=
    private string _Filler306 ="  Proceeds of";
    
    
    
    
    // [DEBUG] Field: Filler307, is_external=, is_static_class=False, static_prefix=
    private string _Filler307 ="   Capital";
    
    
    
    
    // [DEBUG] Field: Filler308, is_external=, is_static_class=False, static_prefix=
    private string _Filler308 ="Indexation";
    
    
    
    
    // [DEBUG] Field: Filler309, is_external=, is_static_class=False, static_prefix=
    private string _Filler309 ="Indexation";
    
    
    
    
    // [DEBUG] Field: Filler310, is_external=, is_static_class=False, static_prefix=
    private string _Filler310 ="     Total";
    
    
    
    
public WsIlosuHd4() {}

public WsIlosuHd4(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller301(data.Substring(offset, 15).Trim());
    offset += 15;
    SetFiller302(data.Substring(offset, 6).Trim());
    offset += 6;
    SetFiller303(data.Substring(offset, 14).Trim());
    offset += 14;
    SetFiller304(data.Substring(offset, 23).Trim());
    offset += 23;
    SetFiller305(data.Substring(offset, 19).Trim());
    offset += 19;
    SetFiller306(data.Substring(offset, 24).Trim());
    offset += 24;
    SetFiller307(data.Substring(offset, 21).Trim());
    offset += 21;
    SetFiller308(data.Substring(offset, 21).Trim());
    offset += 21;
    SetFiller309(data.Substring(offset, 21).Trim());
    offset += 21;
    SetFiller310(data.Substring(offset, 10).Trim());
    offset += 10;
    
}

// Serialization methods
public string GetWsIlosuHd4AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler301.PadRight(15));
    result.Append(_Filler302.PadRight(6));
    result.Append(_Filler303.PadRight(14));
    result.Append(_Filler304.PadRight(23));
    result.Append(_Filler305.PadRight(19));
    result.Append(_Filler306.PadRight(24));
    result.Append(_Filler307.PadRight(21));
    result.Append(_Filler308.PadRight(21));
    result.Append(_Filler309.PadRight(21));
    result.Append(_Filler310.PadRight(10));
    
    return result.ToString();
}

public void SetWsIlosuHd4AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetFiller301(extracted);
    }
    offset += 15;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetFiller302(extracted);
    }
    offset += 6;
    if (offset + 14 <= data.Length)
    {
        string extracted = data.Substring(offset, 14).Trim();
        SetFiller303(extracted);
    }
    offset += 14;
    if (offset + 23 <= data.Length)
    {
        string extracted = data.Substring(offset, 23).Trim();
        SetFiller304(extracted);
    }
    offset += 23;
    if (offset + 19 <= data.Length)
    {
        string extracted = data.Substring(offset, 19).Trim();
        SetFiller305(extracted);
    }
    offset += 19;
    if (offset + 24 <= data.Length)
    {
        string extracted = data.Substring(offset, 24).Trim();
        SetFiller306(extracted);
    }
    offset += 24;
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller307(extracted);
    }
    offset += 21;
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller308(extracted);
    }
    offset += 21;
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller309(extracted);
    }
    offset += 21;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetFiller310(extracted);
    }
    offset += 10;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller301()
{
    return _Filler301;
}

// Standard Setter
public void SetFiller301(string value)
{
    _Filler301 = value;
}

// Get<>AsString()
public string GetFiller301AsString()
{
    return _Filler301.PadRight(15);
}

// Set<>AsString()
public void SetFiller301AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler301 = value;
}

// Standard Getter
public string GetFiller302()
{
    return _Filler302;
}

// Standard Setter
public void SetFiller302(string value)
{
    _Filler302 = value;
}

// Get<>AsString()
public string GetFiller302AsString()
{
    return _Filler302.PadRight(6);
}

// Set<>AsString()
public void SetFiller302AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler302 = value;
}

// Standard Getter
public string GetFiller303()
{
    return _Filler303;
}

// Standard Setter
public void SetFiller303(string value)
{
    _Filler303 = value;
}

// Get<>AsString()
public string GetFiller303AsString()
{
    return _Filler303.PadRight(14);
}

// Set<>AsString()
public void SetFiller303AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler303 = value;
}

// Standard Getter
public string GetFiller304()
{
    return _Filler304;
}

// Standard Setter
public void SetFiller304(string value)
{
    _Filler304 = value;
}

// Get<>AsString()
public string GetFiller304AsString()
{
    return _Filler304.PadRight(23);
}

// Set<>AsString()
public void SetFiller304AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler304 = value;
}

// Standard Getter
public string GetFiller305()
{
    return _Filler305;
}

// Standard Setter
public void SetFiller305(string value)
{
    _Filler305 = value;
}

// Get<>AsString()
public string GetFiller305AsString()
{
    return _Filler305.PadRight(19);
}

// Set<>AsString()
public void SetFiller305AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler305 = value;
}

// Standard Getter
public string GetFiller306()
{
    return _Filler306;
}

// Standard Setter
public void SetFiller306(string value)
{
    _Filler306 = value;
}

// Get<>AsString()
public string GetFiller306AsString()
{
    return _Filler306.PadRight(24);
}

// Set<>AsString()
public void SetFiller306AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler306 = value;
}

// Standard Getter
public string GetFiller307()
{
    return _Filler307;
}

// Standard Setter
public void SetFiller307(string value)
{
    _Filler307 = value;
}

// Get<>AsString()
public string GetFiller307AsString()
{
    return _Filler307.PadRight(21);
}

// Set<>AsString()
public void SetFiller307AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler307 = value;
}

// Standard Getter
public string GetFiller308()
{
    return _Filler308;
}

// Standard Setter
public void SetFiller308(string value)
{
    _Filler308 = value;
}

// Get<>AsString()
public string GetFiller308AsString()
{
    return _Filler308.PadRight(21);
}

// Set<>AsString()
public void SetFiller308AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler308 = value;
}

// Standard Getter
public string GetFiller309()
{
    return _Filler309;
}

// Standard Setter
public void SetFiller309(string value)
{
    _Filler309 = value;
}

// Get<>AsString()
public string GetFiller309AsString()
{
    return _Filler309.PadRight(21);
}

// Set<>AsString()
public void SetFiller309AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler309 = value;
}

// Standard Getter
public string GetFiller310()
{
    return _Filler310;
}

// Standard Setter
public void SetFiller310(string value)
{
    _Filler310 = value;
}

// Get<>AsString()
public string GetFiller310AsString()
{
    return _Filler310.PadRight(10);
}

// Set<>AsString()
public void SetFiller310AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler310 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsIlosuHd5(string value)
{
    _WsIlosuHd5.SetWsIlosuHd5AsString(value);
}
// Nested Class: WsIlosuHd5
public class WsIlosuHd5
{
    private static int _size = 185;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler311, is_external=, is_static_class=False, static_prefix=
    private string _Filler311 =" ";
    
    
    
    
    // [DEBUG] Field: Filler312, is_external=, is_static_class=False, static_prefix=
    private string _Filler312 =" Date  ";
    
    
    
    
    // [DEBUG] Field: Filler313, is_external=, is_static_class=False, static_prefix=
    private string _Filler313 ="     Disposed";
    
    
    
    
    // [DEBUG] Field: Filler314, is_external=, is_static_class=False, static_prefix=
    private string _Filler314 ="      Costs";
    
    
    
    
    // [DEBUG] Field: Filler315, is_external=, is_static_class=False, static_prefix=
    private string _Filler315 ="     Disposal ";
    
    
    
    
    // [DEBUG] Field: Filler316, is_external=, is_static_class=False, static_prefix=
    private string _Filler316 =" Gain/Loss";
    
    
    
    
    // [DEBUG] Field: Filler317, is_external=, is_static_class=False, static_prefix=
    private string _Filler317 ="      Used";
    
    
    
    
    // [DEBUG] Field: Filler318, is_external=, is_static_class=False, static_prefix=
    private string _Filler318 ="Disallowed";
    
    
    
    
    // [DEBUG] Field: Filler319, is_external=, is_static_class=False, static_prefix=
    private string _Filler319 ="Indexation";
    
    
    
    
public WsIlosuHd5() {}

public WsIlosuHd5(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller311(data.Substring(offset, 21).Trim());
    offset += 21;
    SetFiller312(data.Substring(offset, 14).Trim());
    offset += 14;
    SetFiller313(data.Substring(offset, 23).Trim());
    offset += 23;
    SetFiller314(data.Substring(offset, 19).Trim());
    offset += 19;
    SetFiller315(data.Substring(offset, 24).Trim());
    offset += 24;
    SetFiller316(data.Substring(offset, 21).Trim());
    offset += 21;
    SetFiller317(data.Substring(offset, 21).Trim());
    offset += 21;
    SetFiller318(data.Substring(offset, 21).Trim());
    offset += 21;
    SetFiller319(data.Substring(offset, 21).Trim());
    offset += 21;
    
}

// Serialization methods
public string GetWsIlosuHd5AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler311.PadRight(21));
    result.Append(_Filler312.PadRight(14));
    result.Append(_Filler313.PadRight(23));
    result.Append(_Filler314.PadRight(19));
    result.Append(_Filler315.PadRight(24));
    result.Append(_Filler316.PadRight(21));
    result.Append(_Filler317.PadRight(21));
    result.Append(_Filler318.PadRight(21));
    result.Append(_Filler319.PadRight(21));
    
    return result.ToString();
}

public void SetWsIlosuHd5AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller311(extracted);
    }
    offset += 21;
    if (offset + 14 <= data.Length)
    {
        string extracted = data.Substring(offset, 14).Trim();
        SetFiller312(extracted);
    }
    offset += 14;
    if (offset + 23 <= data.Length)
    {
        string extracted = data.Substring(offset, 23).Trim();
        SetFiller313(extracted);
    }
    offset += 23;
    if (offset + 19 <= data.Length)
    {
        string extracted = data.Substring(offset, 19).Trim();
        SetFiller314(extracted);
    }
    offset += 19;
    if (offset + 24 <= data.Length)
    {
        string extracted = data.Substring(offset, 24).Trim();
        SetFiller315(extracted);
    }
    offset += 24;
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller316(extracted);
    }
    offset += 21;
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller317(extracted);
    }
    offset += 21;
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller318(extracted);
    }
    offset += 21;
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller319(extracted);
    }
    offset += 21;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller311()
{
    return _Filler311;
}

// Standard Setter
public void SetFiller311(string value)
{
    _Filler311 = value;
}

// Get<>AsString()
public string GetFiller311AsString()
{
    return _Filler311.PadRight(21);
}

// Set<>AsString()
public void SetFiller311AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler311 = value;
}

// Standard Getter
public string GetFiller312()
{
    return _Filler312;
}

// Standard Setter
public void SetFiller312(string value)
{
    _Filler312 = value;
}

// Get<>AsString()
public string GetFiller312AsString()
{
    return _Filler312.PadRight(14);
}

// Set<>AsString()
public void SetFiller312AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler312 = value;
}

// Standard Getter
public string GetFiller313()
{
    return _Filler313;
}

// Standard Setter
public void SetFiller313(string value)
{
    _Filler313 = value;
}

// Get<>AsString()
public string GetFiller313AsString()
{
    return _Filler313.PadRight(23);
}

// Set<>AsString()
public void SetFiller313AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler313 = value;
}

// Standard Getter
public string GetFiller314()
{
    return _Filler314;
}

// Standard Setter
public void SetFiller314(string value)
{
    _Filler314 = value;
}

// Get<>AsString()
public string GetFiller314AsString()
{
    return _Filler314.PadRight(19);
}

// Set<>AsString()
public void SetFiller314AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler314 = value;
}

// Standard Getter
public string GetFiller315()
{
    return _Filler315;
}

// Standard Setter
public void SetFiller315(string value)
{
    _Filler315 = value;
}

// Get<>AsString()
public string GetFiller315AsString()
{
    return _Filler315.PadRight(24);
}

// Set<>AsString()
public void SetFiller315AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler315 = value;
}

// Standard Getter
public string GetFiller316()
{
    return _Filler316;
}

// Standard Setter
public void SetFiller316(string value)
{
    _Filler316 = value;
}

// Get<>AsString()
public string GetFiller316AsString()
{
    return _Filler316.PadRight(21);
}

// Set<>AsString()
public void SetFiller316AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler316 = value;
}

// Standard Getter
public string GetFiller317()
{
    return _Filler317;
}

// Standard Setter
public void SetFiller317(string value)
{
    _Filler317 = value;
}

// Get<>AsString()
public string GetFiller317AsString()
{
    return _Filler317.PadRight(21);
}

// Set<>AsString()
public void SetFiller317AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler317 = value;
}

// Standard Getter
public string GetFiller318()
{
    return _Filler318;
}

// Standard Setter
public void SetFiller318(string value)
{
    _Filler318 = value;
}

// Get<>AsString()
public string GetFiller318AsString()
{
    return _Filler318.PadRight(21);
}

// Set<>AsString()
public void SetFiller318AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler318 = value;
}

// Standard Getter
public string GetFiller319()
{
    return _Filler319;
}

// Standard Setter
public void SetFiller319(string value)
{
    _Filler319 = value;
}

// Get<>AsString()
public string GetFiller319AsString()
{
    return _Filler319.PadRight(21);
}

// Set<>AsString()
public void SetFiller319AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler319 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsIlosuDtl(string value)
{
    _WsIlosuDtl.SetWsIlosuDtlAsString(value);
}
// Nested Class: WsIlosuDtl
public class WsIlosuDtl
{
    private static int _size = 291;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsIlosuSedol, is_external=, is_static_class=False, static_prefix=
    private string _WsIlosuSedol ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuDetail1, is_external=, is_static_class=False, static_prefix=
    private WsIlosuDtl.WsIlosuDetail1 _WsIlosuDetail1 = new WsIlosuDtl.WsIlosuDetail1();
    
    
    
    
    // [DEBUG] Field: WsIlosuDetail2, is_external=, is_static_class=False, static_prefix=
    private WsIlosuDtl.WsIlosuDetail2 _WsIlosuDetail2 = new WsIlosuDtl.WsIlosuDetail2();
    
    
    
    
public WsIlosuDtl() {}

public WsIlosuDtl(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsIlosuSedol(data.Substring(offset, 10).Trim());
    offset += 10;
    _WsIlosuDetail1.SetWsIlosuDetail1AsString(data.Substring(offset, WsIlosuDetail1.GetSize()));
    offset += 144;
    _WsIlosuDetail2.SetWsIlosuDetail2AsString(data.Substring(offset, WsIlosuDetail2.GetSize()));
    offset += 137;
    
}

// Serialization methods
public string GetWsIlosuDtlAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsIlosuSedol.PadRight(10));
    result.Append(_WsIlosuDetail1.GetWsIlosuDetail1AsString());
    result.Append(_WsIlosuDetail2.GetWsIlosuDetail2AsString());
    
    return result.ToString();
}

public void SetWsIlosuDtlAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetWsIlosuSedol(extracted);
    }
    offset += 10;
    if (offset + 144 <= data.Length)
    {
        _WsIlosuDetail1.SetWsIlosuDetail1AsString(data.Substring(offset, 144));
    }
    else
    {
        _WsIlosuDetail1.SetWsIlosuDetail1AsString(data.Substring(offset));
    }
    offset += 144;
    if (offset + 137 <= data.Length)
    {
        _WsIlosuDetail2.SetWsIlosuDetail2AsString(data.Substring(offset, 137));
    }
    else
    {
        _WsIlosuDetail2.SetWsIlosuDetail2AsString(data.Substring(offset));
    }
    offset += 137;
}

// Getter and Setter methods

// Standard Getter
public string GetWsIlosuSedol()
{
    return _WsIlosuSedol;
}

// Standard Setter
public void SetWsIlosuSedol(string value)
{
    _WsIlosuSedol = value;
}

// Get<>AsString()
public string GetWsIlosuSedolAsString()
{
    return _WsIlosuSedol.PadRight(10);
}

// Set<>AsString()
public void SetWsIlosuSedolAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlosuSedol = value;
}

// Standard Getter
public WsIlosuDetail1 GetWsIlosuDetail1()
{
    return _WsIlosuDetail1;
}

// Standard Setter
public void SetWsIlosuDetail1(WsIlosuDetail1 value)
{
    _WsIlosuDetail1 = value;
}

// Get<>AsString()
public string GetWsIlosuDetail1AsString()
{
    return _WsIlosuDetail1 != null ? _WsIlosuDetail1.GetWsIlosuDetail1AsString() : "";
}

// Set<>AsString()
public void SetWsIlosuDetail1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsIlosuDetail1 == null)
    {
        _WsIlosuDetail1 = new WsIlosuDetail1();
    }
    _WsIlosuDetail1.SetWsIlosuDetail1AsString(value);
}

// Standard Getter
public WsIlosuDetail2 GetWsIlosuDetail2()
{
    return _WsIlosuDetail2;
}

// Standard Setter
public void SetWsIlosuDetail2(WsIlosuDetail2 value)
{
    _WsIlosuDetail2 = value;
}

// Get<>AsString()
public string GetWsIlosuDetail2AsString()
{
    return _WsIlosuDetail2 != null ? _WsIlosuDetail2.GetWsIlosuDetail2AsString() : "";
}

// Set<>AsString()
public void SetWsIlosuDetail2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsIlosuDetail2 == null)
    {
        _WsIlosuDetail2 = new WsIlosuDetail2();
    }
    _WsIlosuDetail2.SetWsIlosuDetail2AsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WsIlosuDetail1
public class WsIlosuDetail1
{
    private static int _size = 144;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsIlosuSecurity, is_external=, is_static_class=False, static_prefix=
    private string _WsIlosuSecurity ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuIssuer, is_external=, is_static_class=False, static_prefix=
    private string _WsIlosuIssuer ="";
    
    
    
    
    // [DEBUG] Field: Filler320, is_external=, is_static_class=False, static_prefix=
    private string _Filler320 ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuDescription, is_external=, is_static_class=False, static_prefix=
    private string _WsIlosuDescription ="";
    
    
    
    
    // [DEBUG] Field: Filler321, is_external=, is_static_class=False, static_prefix=
    private string _Filler321 ="";
    
    
    
    
public WsIlosuDetail1() {}

public WsIlosuDetail1(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsIlosuSecurity(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWsIlosuIssuer(data.Substring(offset, 35).Trim());
    offset += 35;
    SetFiller320(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlosuDescription(data.Substring(offset, 35).Trim());
    offset += 35;
    SetFiller321(data.Substring(offset, 71).Trim());
    offset += 71;
    
}

// Serialization methods
public string GetWsIlosuDetail1AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsIlosuSecurity.PadRight(2));
    result.Append(_WsIlosuIssuer.PadRight(35));
    result.Append(_Filler320.PadRight(1));
    result.Append(_WsIlosuDescription.PadRight(35));
    result.Append(_Filler321.PadRight(71));
    
    return result.ToString();
}

public void SetWsIlosuDetail1AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWsIlosuSecurity(extracted);
    }
    offset += 2;
    if (offset + 35 <= data.Length)
    {
        string extracted = data.Substring(offset, 35).Trim();
        SetWsIlosuIssuer(extracted);
    }
    offset += 35;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller320(extracted);
    }
    offset += 1;
    if (offset + 35 <= data.Length)
    {
        string extracted = data.Substring(offset, 35).Trim();
        SetWsIlosuDescription(extracted);
    }
    offset += 35;
    if (offset + 71 <= data.Length)
    {
        string extracted = data.Substring(offset, 71).Trim();
        SetFiller321(extracted);
    }
    offset += 71;
}

// Getter and Setter methods

// Standard Getter
public string GetWsIlosuSecurity()
{
    return _WsIlosuSecurity;
}

// Standard Setter
public void SetWsIlosuSecurity(string value)
{
    _WsIlosuSecurity = value;
}

// Get<>AsString()
public string GetWsIlosuSecurityAsString()
{
    return _WsIlosuSecurity.PadRight(2);
}

// Set<>AsString()
public void SetWsIlosuSecurityAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlosuSecurity = value;
}

// Standard Getter
public string GetWsIlosuIssuer()
{
    return _WsIlosuIssuer;
}

// Standard Setter
public void SetWsIlosuIssuer(string value)
{
    _WsIlosuIssuer = value;
}

// Get<>AsString()
public string GetWsIlosuIssuerAsString()
{
    return _WsIlosuIssuer.PadRight(35);
}

// Set<>AsString()
public void SetWsIlosuIssuerAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlosuIssuer = value;
}

// Standard Getter
public string GetFiller320()
{
    return _Filler320;
}

// Standard Setter
public void SetFiller320(string value)
{
    _Filler320 = value;
}

// Get<>AsString()
public string GetFiller320AsString()
{
    return _Filler320.PadRight(1);
}

// Set<>AsString()
public void SetFiller320AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler320 = value;
}

// Standard Getter
public string GetWsIlosuDescription()
{
    return _WsIlosuDescription;
}

// Standard Setter
public void SetWsIlosuDescription(string value)
{
    _WsIlosuDescription = value;
}

// Get<>AsString()
public string GetWsIlosuDescriptionAsString()
{
    return _WsIlosuDescription.PadRight(35);
}

// Set<>AsString()
public void SetWsIlosuDescriptionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlosuDescription = value;
}

// Standard Getter
public string GetFiller321()
{
    return _Filler321;
}

// Standard Setter
public void SetFiller321(string value)
{
    _Filler321 = value;
}

// Get<>AsString()
public string GetFiller321AsString()
{
    return _Filler321.PadRight(71);
}

// Set<>AsString()
public void SetFiller321AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler321 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WsIlosuDetail2
public class WsIlosuDetail2
{
    private static int _size = 137;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler322, is_external=, is_static_class=False, static_prefix=
    private string _Filler322 ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuType, is_external=, is_static_class=False, static_prefix=
    private string _WsIlosuType ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuBargDate, is_external=, is_static_class=False, static_prefix=
    private string _WsIlosuBargDate ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuHoldings, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuHoldings =0;
    
    
    
    
    // [DEBUG] Field: Filler323, is_external=, is_static_class=False, static_prefix=
    private string _Filler323 ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuCgt, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuCgt =0;
    
    
    
    
    // [DEBUG] Field: Filler324, is_external=, is_static_class=False, static_prefix=
    private string _Filler324 ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuProc, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuProc =0;
    
    
    
    
    // [DEBUG] Field: Filler325, is_external=, is_static_class=False, static_prefix=
    private string _Filler325 ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuGain, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuGain =0;
    
    
    
    
    // [DEBUG] Field: Filler326, is_external=, is_static_class=False, static_prefix=
    private string _Filler326 ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuIndx, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuIndx =0;
    
    
    
    
    // [DEBUG] Field: Filler327, is_external=, is_static_class=False, static_prefix=
    private string _Filler327 ="";
    
    
    
    
    // [DEBUG] Field: WsIlosu, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosu =0;
    
    
    
    
    // [DEBUG] Field: Filler328, is_external=, is_static_class=False, static_prefix=
    private string _Filler328 ="";
    
    
    
    
    // [DEBUG] Field: WsIlostuTotIndxn, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostuTotIndxn =0;
    
    
    
    
public WsIlosuDetail2() {}

public WsIlosuDetail2(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller322(data.Substring(offset, 6).Trim());
    offset += 6;
    SetWsIlosuType(data.Substring(offset, 5).Trim());
    offset += 5;
    SetWsIlosuBargDate(data.Substring(offset, 8).Trim());
    offset += 8;
    SetWsIlosuHoldings(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller323(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlosuCgt(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller324(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlosuProc(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller325(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlosuGain(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller326(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlosuIndx(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller327(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlosu(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller328(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlostuTotIndxn(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    
}

// Serialization methods
public string GetWsIlosuDetail2AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler322.PadRight(6));
    result.Append(_WsIlosuType.PadRight(5));
    result.Append(_WsIlosuBargDate.PadRight(8));
    result.Append(_WsIlosuHoldings.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler323.PadRight(1));
    result.Append(_WsIlosuCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler324.PadRight(1));
    result.Append(_WsIlosuProc.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler325.PadRight(1));
    result.Append(_WsIlosuGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler326.PadRight(1));
    result.Append(_WsIlosuIndx.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler327.PadRight(1));
    result.Append(_WsIlosu.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler328.PadRight(1));
    result.Append(_WsIlostuTotIndxn.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetWsIlosuDetail2AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetFiller322(extracted);
    }
    offset += 6;
    if (offset + 5 <= data.Length)
    {
        string extracted = data.Substring(offset, 5).Trim();
        SetWsIlosuType(extracted);
    }
    offset += 5;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWsIlosuBargDate(extracted);
    }
    offset += 8;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlosuHoldings(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller323(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlosuCgt(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller324(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlosuProc(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller325(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlosuGain(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller326(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlosuIndx(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller327(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlosu(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller328(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlostuTotIndxn(parsedDec);
    }
    offset += 16;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller322()
{
    return _Filler322;
}

// Standard Setter
public void SetFiller322(string value)
{
    _Filler322 = value;
}

// Get<>AsString()
public string GetFiller322AsString()
{
    return _Filler322.PadRight(6);
}

// Set<>AsString()
public void SetFiller322AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler322 = value;
}

// Standard Getter
public string GetWsIlosuType()
{
    return _WsIlosuType;
}

// Standard Setter
public void SetWsIlosuType(string value)
{
    _WsIlosuType = value;
}

// Get<>AsString()
public string GetWsIlosuTypeAsString()
{
    return _WsIlosuType.PadRight(5);
}

// Set<>AsString()
public void SetWsIlosuTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlosuType = value;
}

// Standard Getter
public string GetWsIlosuBargDate()
{
    return _WsIlosuBargDate;
}

// Standard Setter
public void SetWsIlosuBargDate(string value)
{
    _WsIlosuBargDate = value;
}

// Get<>AsString()
public string GetWsIlosuBargDateAsString()
{
    return _WsIlosuBargDate.PadRight(8);
}

// Set<>AsString()
public void SetWsIlosuBargDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlosuBargDate = value;
}

// Standard Getter
public decimal GetWsIlosuHoldings()
{
    return _WsIlosuHoldings;
}

// Standard Setter
public void SetWsIlosuHoldings(decimal value)
{
    _WsIlosuHoldings = value;
}

// Get<>AsString()
public string GetWsIlosuHoldingsAsString()
{
    return _WsIlosuHoldings.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlosuHoldingsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuHoldings = parsed;
}

// Standard Getter
public string GetFiller323()
{
    return _Filler323;
}

// Standard Setter
public void SetFiller323(string value)
{
    _Filler323 = value;
}

// Get<>AsString()
public string GetFiller323AsString()
{
    return _Filler323.PadRight(1);
}

// Set<>AsString()
public void SetFiller323AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler323 = value;
}

// Standard Getter
public decimal GetWsIlosuCgt()
{
    return _WsIlosuCgt;
}

// Standard Setter
public void SetWsIlosuCgt(decimal value)
{
    _WsIlosuCgt = value;
}

// Get<>AsString()
public string GetWsIlosuCgtAsString()
{
    return _WsIlosuCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlosuCgtAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuCgt = parsed;
}

// Standard Getter
public string GetFiller324()
{
    return _Filler324;
}

// Standard Setter
public void SetFiller324(string value)
{
    _Filler324 = value;
}

// Get<>AsString()
public string GetFiller324AsString()
{
    return _Filler324.PadRight(1);
}

// Set<>AsString()
public void SetFiller324AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler324 = value;
}

// Standard Getter
public decimal GetWsIlosuProc()
{
    return _WsIlosuProc;
}

// Standard Setter
public void SetWsIlosuProc(decimal value)
{
    _WsIlosuProc = value;
}

// Get<>AsString()
public string GetWsIlosuProcAsString()
{
    return _WsIlosuProc.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlosuProcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuProc = parsed;
}

// Standard Getter
public string GetFiller325()
{
    return _Filler325;
}

// Standard Setter
public void SetFiller325(string value)
{
    _Filler325 = value;
}

// Get<>AsString()
public string GetFiller325AsString()
{
    return _Filler325.PadRight(1);
}

// Set<>AsString()
public void SetFiller325AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler325 = value;
}

// Standard Getter
public decimal GetWsIlosuGain()
{
    return _WsIlosuGain;
}

// Standard Setter
public void SetWsIlosuGain(decimal value)
{
    _WsIlosuGain = value;
}

// Get<>AsString()
public string GetWsIlosuGainAsString()
{
    return _WsIlosuGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlosuGainAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuGain = parsed;
}

// Standard Getter
public string GetFiller326()
{
    return _Filler326;
}

// Standard Setter
public void SetFiller326(string value)
{
    _Filler326 = value;
}

// Get<>AsString()
public string GetFiller326AsString()
{
    return _Filler326.PadRight(1);
}

// Set<>AsString()
public void SetFiller326AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler326 = value;
}

// Standard Getter
public decimal GetWsIlosuIndx()
{
    return _WsIlosuIndx;
}

// Standard Setter
public void SetWsIlosuIndx(decimal value)
{
    _WsIlosuIndx = value;
}

// Get<>AsString()
public string GetWsIlosuIndxAsString()
{
    return _WsIlosuIndx.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlosuIndxAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuIndx = parsed;
}

// Standard Getter
public string GetFiller327()
{
    return _Filler327;
}

// Standard Setter
public void SetFiller327(string value)
{
    _Filler327 = value;
}

// Get<>AsString()
public string GetFiller327AsString()
{
    return _Filler327.PadRight(1);
}

// Set<>AsString()
public void SetFiller327AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler327 = value;
}

// Standard Getter
public decimal GetWsIlosu()
{
    return _WsIlosu;
}

// Standard Setter
public void SetWsIlosu(decimal value)
{
    _WsIlosu = value;
}

// Get<>AsString()
public string GetWsIlosuAsString()
{
    return _WsIlosu.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlosuAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosu = parsed;
}

// Standard Getter
public string GetFiller328()
{
    return _Filler328;
}

// Standard Setter
public void SetFiller328(string value)
{
    _Filler328 = value;
}

// Get<>AsString()
public string GetFiller328AsString()
{
    return _Filler328.PadRight(1);
}

// Set<>AsString()
public void SetFiller328AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler328 = value;
}

// Standard Getter
public decimal GetWsIlostuTotIndxn()
{
    return _WsIlostuTotIndxn;
}

// Standard Setter
public void SetWsIlostuTotIndxn(decimal value)
{
    _WsIlostuTotIndxn = value;
}

// Get<>AsString()
public string GetWsIlostuTotIndxnAsString()
{
    return _WsIlostuTotIndxn.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlostuTotIndxnAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostuTotIndxn = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetWsIlosuTotLine(string value)
{
    _WsIlosuTotLine.SetWsIlosuTotLineAsString(value);
}
// Nested Class: WsIlosuTotLine
public class WsIlosuTotLine
{
    private static int _size = 141;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler329, is_external=, is_static_class=False, static_prefix=
    private string _Filler329 ="";
    
    
    
    
    // [DEBUG] Field: Filler330, is_external=, is_static_class=False, static_prefix=
    private string _Filler330 ="TOTAL ";
    
    
    
    
    // [DEBUG] Field: WsIlosuHoldTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuHoldTot =0;
    
    
    
    
    // [DEBUG] Field: Filler331, is_external=, is_static_class=False, static_prefix=
    private string _Filler331 ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuCgtTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuCgtTot =0;
    
    
    
    
    // [DEBUG] Field: Filler332, is_external=, is_static_class=False, static_prefix=
    private string _Filler332 ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuProcTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuProcTot =0;
    
    
    
    
    // [DEBUG] Field: Filler333, is_external=, is_static_class=False, static_prefix=
    private string _Filler333 ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuGainTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuGainTot =0;
    
    
    
    
    // [DEBUG] Field: Filler334, is_external=, is_static_class=False, static_prefix=
    private string _Filler334 ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuIndxTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuIndxTot =0;
    
    
    
    
    // [DEBUG] Field: Filler335, is_external=, is_static_class=False, static_prefix=
    private string _Filler335 ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuTot =0;
    
    
    
    
    // [DEBUG] Field: Filler336, is_external=, is_static_class=False, static_prefix=
    private string _Filler336 ="";
    
    
    
    
    // [DEBUG] Field: WsIlosuTotIndxnTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlosuTotIndxnTot =0;
    
    
    
    
public WsIlosuTotLine() {}

public WsIlosuTotLine(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller329(data.Substring(offset, 23).Trim());
    offset += 23;
    SetFiller330(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWsIlosuHoldTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller331(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlosuCgtTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller332(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlosuProcTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller333(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlosuGainTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller334(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlosuIndxTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller335(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlosuTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller336(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlosuTotIndxnTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    
}

// Serialization methods
public string GetWsIlosuTotLineAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler329.PadRight(23));
    result.Append(_Filler330.PadRight(0));
    result.Append(_WsIlosuHoldTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler331.PadRight(1));
    result.Append(_WsIlosuCgtTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler332.PadRight(1));
    result.Append(_WsIlosuProcTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler333.PadRight(1));
    result.Append(_WsIlosuGainTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler334.PadRight(1));
    result.Append(_WsIlosuIndxTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler335.PadRight(1));
    result.Append(_WsIlosuTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler336.PadRight(1));
    result.Append(_WsIlosuTotIndxnTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetWsIlosuTotLineAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 23 <= data.Length)
    {
        string extracted = data.Substring(offset, 23).Trim();
        SetFiller329(extracted);
    }
    offset += 23;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller330(extracted);
    }
    offset += 0;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlosuHoldTot(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller331(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlosuCgtTot(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller332(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlosuProcTot(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller333(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlosuGainTot(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller334(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlosuIndxTot(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller335(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlosuTot(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller336(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlosuTotIndxnTot(parsedDec);
    }
    offset += 16;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller329()
{
    return _Filler329;
}

// Standard Setter
public void SetFiller329(string value)
{
    _Filler329 = value;
}

// Get<>AsString()
public string GetFiller329AsString()
{
    return _Filler329.PadRight(23);
}

// Set<>AsString()
public void SetFiller329AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler329 = value;
}

// Standard Getter
public string GetFiller330()
{
    return _Filler330;
}

// Standard Setter
public void SetFiller330(string value)
{
    _Filler330 = value;
}

// Get<>AsString()
public string GetFiller330AsString()
{
    return _Filler330.PadRight(0);
}

// Set<>AsString()
public void SetFiller330AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler330 = value;
}

// Standard Getter
public decimal GetWsIlosuHoldTot()
{
    return _WsIlosuHoldTot;
}

// Standard Setter
public void SetWsIlosuHoldTot(decimal value)
{
    _WsIlosuHoldTot = value;
}

// Get<>AsString()
public string GetWsIlosuHoldTotAsString()
{
    return _WsIlosuHoldTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlosuHoldTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuHoldTot = parsed;
}

// Standard Getter
public string GetFiller331()
{
    return _Filler331;
}

// Standard Setter
public void SetFiller331(string value)
{
    _Filler331 = value;
}

// Get<>AsString()
public string GetFiller331AsString()
{
    return _Filler331.PadRight(1);
}

// Set<>AsString()
public void SetFiller331AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler331 = value;
}

// Standard Getter
public decimal GetWsIlosuCgtTot()
{
    return _WsIlosuCgtTot;
}

// Standard Setter
public void SetWsIlosuCgtTot(decimal value)
{
    _WsIlosuCgtTot = value;
}

// Get<>AsString()
public string GetWsIlosuCgtTotAsString()
{
    return _WsIlosuCgtTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlosuCgtTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuCgtTot = parsed;
}

// Standard Getter
public string GetFiller332()
{
    return _Filler332;
}

// Standard Setter
public void SetFiller332(string value)
{
    _Filler332 = value;
}

// Get<>AsString()
public string GetFiller332AsString()
{
    return _Filler332.PadRight(1);
}

// Set<>AsString()
public void SetFiller332AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler332 = value;
}

// Standard Getter
public decimal GetWsIlosuProcTot()
{
    return _WsIlosuProcTot;
}

// Standard Setter
public void SetWsIlosuProcTot(decimal value)
{
    _WsIlosuProcTot = value;
}

// Get<>AsString()
public string GetWsIlosuProcTotAsString()
{
    return _WsIlosuProcTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlosuProcTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuProcTot = parsed;
}

// Standard Getter
public string GetFiller333()
{
    return _Filler333;
}

// Standard Setter
public void SetFiller333(string value)
{
    _Filler333 = value;
}

// Get<>AsString()
public string GetFiller333AsString()
{
    return _Filler333.PadRight(1);
}

// Set<>AsString()
public void SetFiller333AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler333 = value;
}

// Standard Getter
public decimal GetWsIlosuGainTot()
{
    return _WsIlosuGainTot;
}

// Standard Setter
public void SetWsIlosuGainTot(decimal value)
{
    _WsIlosuGainTot = value;
}

// Get<>AsString()
public string GetWsIlosuGainTotAsString()
{
    return _WsIlosuGainTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlosuGainTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuGainTot = parsed;
}

// Standard Getter
public string GetFiller334()
{
    return _Filler334;
}

// Standard Setter
public void SetFiller334(string value)
{
    _Filler334 = value;
}

// Get<>AsString()
public string GetFiller334AsString()
{
    return _Filler334.PadRight(1);
}

// Set<>AsString()
public void SetFiller334AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler334 = value;
}

// Standard Getter
public decimal GetWsIlosuIndxTot()
{
    return _WsIlosuIndxTot;
}

// Standard Setter
public void SetWsIlosuIndxTot(decimal value)
{
    _WsIlosuIndxTot = value;
}

// Get<>AsString()
public string GetWsIlosuIndxTotAsString()
{
    return _WsIlosuIndxTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlosuIndxTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuIndxTot = parsed;
}

// Standard Getter
public string GetFiller335()
{
    return _Filler335;
}

// Standard Setter
public void SetFiller335(string value)
{
    _Filler335 = value;
}

// Get<>AsString()
public string GetFiller335AsString()
{
    return _Filler335.PadRight(1);
}

// Set<>AsString()
public void SetFiller335AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler335 = value;
}

// Standard Getter
public decimal GetWsIlosuTot()
{
    return _WsIlosuTot;
}

// Standard Setter
public void SetWsIlosuTot(decimal value)
{
    _WsIlosuTot = value;
}

// Get<>AsString()
public string GetWsIlosuTotAsString()
{
    return _WsIlosuTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlosuTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuTot = parsed;
}

// Standard Getter
public string GetFiller336()
{
    return _Filler336;
}

// Standard Setter
public void SetFiller336(string value)
{
    _Filler336 = value;
}

// Get<>AsString()
public string GetFiller336AsString()
{
    return _Filler336.PadRight(1);
}

// Set<>AsString()
public void SetFiller336AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler336 = value;
}

// Standard Getter
public decimal GetWsIlosuTotIndxnTot()
{
    return _WsIlosuTotIndxnTot;
}

// Standard Setter
public void SetWsIlosuTotIndxnTot(decimal value)
{
    _WsIlosuTotIndxnTot = value;
}

// Get<>AsString()
public string GetWsIlosuTotIndxnTotAsString()
{
    return _WsIlosuTotIndxnTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlosuTotIndxnTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlosuTotIndxnTot = parsed;
}



public static int GetSize()
{
    return _size;
}

}

}}
