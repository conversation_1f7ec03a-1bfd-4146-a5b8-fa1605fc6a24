using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing LValidDate Data Structure

public class LValidDate
{
    private static int _size = 7;
    // [DEBUG] Class: LValidDate, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: LDate, is_external=, is_static_class=False, static_prefix=
    private LDate _LDate = new LDate();
    
    
    
    
    // [DEBUG] Field: LDateReturnCode, is_external=, is_static_class=False, static_prefix=
    private int _LDateReturnCode =0;
    
    
    
    
    
    // Serialization methods
    public string GetLValidDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LDate.GetLDateAsString());
        result.Append(_LDateReturnCode.ToString().PadLeft(1, '0'));
        
        return result.ToString();
    }
    
    public void SetLValidDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 6 <= data.Length)
        {
            _LDate.SetLDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _LDate.SetLDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetLDateReturnCode(parsedInt);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetLValidDateAsString();
    }
    // Set<>String Override function
    public void SetLValidDate(string value)
    {
        SetLValidDateAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public LDate GetLDate()
    {
        return _LDate;
    }
    
    // Standard Setter
    public void SetLDate(LDate value)
    {
        _LDate = value;
    }
    
    // Get<>AsString()
    public string GetLDateAsString()
    {
        return _LDate != null ? _LDate.GetLDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetLDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_LDate == null)
        {
            _LDate = new LDate();
        }
        _LDate.SetLDateAsString(value);
    }
    
    // Standard Getter
    public int GetLDateReturnCode()
    {
        return _LDateReturnCode;
    }
    
    // Standard Setter
    public void SetLDateReturnCode(int value)
    {
        _LDateReturnCode = value;
    }
    
    // Get<>AsString()
    public string GetLDateReturnCodeAsString()
    {
        return _LDateReturnCode.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetLDateReturnCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _LDateReturnCode = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetLDate(string value)
    {
        _LDate.SetLDateAsString(value);
    }
    // Nested Class: LDate
    public class LDate
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: LDd, is_external=, is_static_class=False, static_prefix=
        private int _LDd =0;
        
        
        
        
        // [DEBUG] Field: LMm, is_external=, is_static_class=False, static_prefix=
        private int _LMm =0;
        
        
        // 88-level condition checks for LMm
        public bool IsValidMonth()
        {
            if (this._LMm >= 1 && this._LMm <= 12) return true;
            return false;
        }
        
        
        // [DEBUG] Field: LYy, is_external=, is_static_class=False, static_prefix=
        private int _LYy =0;
        
        
        
        
    public LDate() {}
    
    public LDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetLDd(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetLMm(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetLYy(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetLDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LDd.ToString().PadLeft(2, '0'));
        result.Append(_LMm.ToString().PadLeft(2, '0'));
        result.Append(_LYy.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetLDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetLDd(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetLMm(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetLYy(parsedInt);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetLDd()
    {
        return _LDd;
    }
    
    // Standard Setter
    public void SetLDd(int value)
    {
        _LDd = value;
    }
    
    // Get<>AsString()
    public string GetLDdAsString()
    {
        return _LDd.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetLDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _LDd = parsed;
    }
    
    // Standard Getter
    public int GetLMm()
    {
        return _LMm;
    }
    
    // Standard Setter
    public void SetLMm(int value)
    {
        _LMm = value;
    }
    
    // Get<>AsString()
    public string GetLMmAsString()
    {
        return _LMm.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetLMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _LMm = parsed;
    }
    
    // Standard Getter
    public int GetLYy()
    {
        return _LYy;
    }
    
    // Standard Setter
    public void SetLYy(int value)
    {
        _LYy = value;
    }
    
    // Get<>AsString()
    public string GetLYyAsString()
    {
        return _LYy.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetLYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _LYy = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}
