using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D80Record Data Structure

public class D80Record
{
    private static int _size = 31;
    // [DEBUG] Class: D80Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D80Key, is_external=, is_static_class=False, static_prefix=
    private D80Key _D80Key = new D80Key();
    
    
    
    
    
    // Serialization methods
    public string GetD80RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D80Key.GetD80KeyAsString());
        
        return result.ToString();
    }
    
    public void SetD80RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 31 <= data.Length)
        {
            _D80Key.SetD80KeyAsString(data.Substring(offset, 31));
        }
        else
        {
            _D80Key.SetD80KeyAsString(data.Substring(offset));
        }
        offset += 31;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD80RecordAsString();
    }
    // Set<>String Override function
    public void SetD80Record(string value)
    {
        SetD80RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D80Key GetD80Key()
    {
        return _D80Key;
    }
    
    // Standard Setter
    public void SetD80Key(D80Key value)
    {
        _D80Key = value;
    }
    
    // Get<>AsString()
    public string GetD80KeyAsString()
    {
        return _D80Key != null ? _D80Key.GetD80KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD80KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D80Key == null)
        {
            _D80Key = new D80Key();
        }
        _D80Key.SetD80KeyAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD80Key(string value)
    {
        _D80Key.SetD80KeyAsString(value);
    }
    // Nested Class: D80Key
    public class D80Key
    {
        private static int _size = 31;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D80Fund, is_external=, is_static_class=False, static_prefix=
        private string _D80Fund ="";
        
        
        
        
        // [DEBUG] Field: D80BargainDate, is_external=, is_static_class=False, static_prefix=
        private string _D80BargainDate ="";
        
        
        
        
        // [DEBUG] Field: D80Sedol, is_external=, is_static_class=False, static_prefix=
        private string _D80Sedol ="";
        
        
        
        
        // [DEBUG] Field: D80ContractNumber, is_external=, is_static_class=False, static_prefix=
        private string _D80ContractNumber ="";
        
        
        
        
        // [DEBUG] Field: D80SummaryFund, is_external=, is_static_class=False, static_prefix=
        private string _D80SummaryFund ="";
        
        
        
        
    public D80Key() {}
    
    public D80Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD80Fund(data.Substring(offset, 4).Trim());
        offset += 4;
        SetD80BargainDate(data.Substring(offset, 6).Trim());
        offset += 6;
        SetD80Sedol(data.Substring(offset, 7).Trim());
        offset += 7;
        SetD80ContractNumber(data.Substring(offset, 10).Trim());
        offset += 10;
        SetD80SummaryFund(data.Substring(offset, 4).Trim());
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetD80KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D80Fund.PadRight(4));
        result.Append(_D80BargainDate.PadRight(6));
        result.Append(_D80Sedol.PadRight(7));
        result.Append(_D80ContractNumber.PadRight(10));
        result.Append(_D80SummaryFund.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD80KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD80Fund(extracted);
        }
        offset += 4;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetD80BargainDate(extracted);
        }
        offset += 6;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetD80Sedol(extracted);
        }
        offset += 7;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetD80ContractNumber(extracted);
        }
        offset += 10;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD80SummaryFund(extracted);
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD80Fund()
    {
        return _D80Fund;
    }
    
    // Standard Setter
    public void SetD80Fund(string value)
    {
        _D80Fund = value;
    }
    
    // Get<>AsString()
    public string GetD80FundAsString()
    {
        return _D80Fund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD80FundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D80Fund = value;
    }
    
    // Standard Getter
    public string GetD80BargainDate()
    {
        return _D80BargainDate;
    }
    
    // Standard Setter
    public void SetD80BargainDate(string value)
    {
        _D80BargainDate = value;
    }
    
    // Get<>AsString()
    public string GetD80BargainDateAsString()
    {
        return _D80BargainDate.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetD80BargainDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D80BargainDate = value;
    }
    
    // Standard Getter
    public string GetD80Sedol()
    {
        return _D80Sedol;
    }
    
    // Standard Setter
    public void SetD80Sedol(string value)
    {
        _D80Sedol = value;
    }
    
    // Get<>AsString()
    public string GetD80SedolAsString()
    {
        return _D80Sedol.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetD80SedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D80Sedol = value;
    }
    
    // Standard Getter
    public string GetD80ContractNumber()
    {
        return _D80ContractNumber;
    }
    
    // Standard Setter
    public void SetD80ContractNumber(string value)
    {
        _D80ContractNumber = value;
    }
    
    // Get<>AsString()
    public string GetD80ContractNumberAsString()
    {
        return _D80ContractNumber.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetD80ContractNumberAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D80ContractNumber = value;
    }
    
    // Standard Getter
    public string GetD80SummaryFund()
    {
        return _D80SummaryFund;
    }
    
    // Standard Setter
    public void SetD80SummaryFund(string value)
    {
        _D80SummaryFund = value;
    }
    
    // Get<>AsString()
    public string GetD80SummaryFundAsString()
    {
        return _D80SummaryFund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD80SummaryFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D80SummaryFund = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}