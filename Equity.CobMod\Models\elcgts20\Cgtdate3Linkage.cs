using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing Cgtdate3Linkage Data Structure

public class Cgtdate3Linkage
{
    private static int _size = 42;
    // [DEBUG] Class: Cgtdate3Linkage, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate3FromDate, is_external=, is_static_class=False, static_prefix=
    private string _Cgtdate3FromDate ="";
    
    
    
    
    // [DEBUG] Field: Filler60, is_external=, is_static_class=False, static_prefix=
    private Filler60 _Filler60 = new Filler60();
    
    
    
    
    // [DEBUG] Field: Filler62, is_external=, is_static_class=False, static_prefix=
    private Filler62 _Filler62 = new Filler62();
    
    
    
    
    // [DEBUG] Field: Filler64, is_external=, is_static_class=False, static_prefix=
    private Filler64 _Filler64 = new Filler64();
    
    
    
    
    // [DEBUG] Field: Cgtdate3ToDate, is_external=, is_static_class=False, static_prefix=
    private string _Cgtdate3ToDate ="";
    
    
    
    
    // [DEBUG] Field: Filler67, is_external=, is_static_class=False, static_prefix=
    private Filler67 _Filler67 = new Filler67();
    
    
    
    
    // [DEBUG] Field: Filler69, is_external=, is_static_class=False, static_prefix=
    private Filler69 _Filler69 = new Filler69();
    
    
    
    
    // [DEBUG] Field: Filler71, is_external=, is_static_class=False, static_prefix=
    private Filler71 _Filler71 = new Filler71();
    
    
    
    
    // [DEBUG] Field: Cgtdate3TotalDays, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate3TotalDays =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate3ReturnCode, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate3ReturnCode =0;
    
    
    // 88-level condition checks for Cgtdate3ReturnCode
    public bool IsCgtdate3Valid()
    {
        if (this._Cgtdate3ReturnCode == 0) return true;
        return false;
    }
    public bool IsCgtdate3InvalidFromDate()
    {
        if (this._Cgtdate3ReturnCode == 1) return true;
        return false;
    }
    public bool IsCgtdate3InvalidToDate()
    {
        if (this._Cgtdate3ReturnCode == 2) return true;
        return false;
    }
    public bool IsCgtdate3InvalidDateRange()
    {
        if (this._Cgtdate3ReturnCode == 4) return true;
        return false;
    }
    public bool IsCgtdate3InvalidNoOfDays()
    {
        if (this._Cgtdate3ReturnCode == 5) return true;
        return false;
    }
    public bool IsCgtdate3DateOutOfRange()
    {
        if (this._Cgtdate3ReturnCode == 6) return true;
        return false;
    }
    
    
    // [DEBUG] Field: Cgtdate3ActionCode, is_external=, is_static_class=False, static_prefix=
    private string _Cgtdate3ActionCode =" ";
    
    
    // 88-level condition checks for Cgtdate3ActionCode
    public bool IsCgtdate3ActionDaysDiff()
    {
        if (this._Cgtdate3ActionCode == "SPACES") return true;
        return false;
    }
    public bool IsCgtdate3ActionAddDays()
    {
        if (this._Cgtdate3ActionCode == "'A'") return true;
        return false;
    }
    public bool IsCgtdate3ActionSubtractDays()
    {
        if (this._Cgtdate3ActionCode == "'S'") return true;
        return false;
    }
    
    
    
    // Serialization methods
    public string GetCgtdate3LinkageAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate3FromDate.PadRight(0));
        result.Append(_Filler60.GetFiller60AsString());
        result.Append(_Filler62.GetFiller62AsString());
        result.Append(_Filler64.GetFiller64AsString());
        result.Append(_Cgtdate3ToDate.PadRight(0));
        result.Append(_Filler67.GetFiller67AsString());
        result.Append(_Filler69.GetFiller69AsString());
        result.Append(_Filler71.GetFiller71AsString());
        result.Append(_Cgtdate3TotalDays.ToString().PadLeft(8, '0'));
        result.Append(_Cgtdate3ReturnCode.ToString().PadLeft(1, '0'));
        result.Append(_Cgtdate3ActionCode.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetCgtdate3LinkageAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCgtdate3FromDate(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _Filler60.SetFiller60AsString(data.Substring(offset, 0));
        }
        else
        {
            _Filler60.SetFiller60AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _Filler62.SetFiller62AsString(data.Substring(offset, 0));
        }
        else
        {
            _Filler62.SetFiller62AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 16 <= data.Length)
        {
            _Filler64.SetFiller64AsString(data.Substring(offset, 16));
        }
        else
        {
            _Filler64.SetFiller64AsString(data.Substring(offset));
        }
        offset += 16;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCgtdate3ToDate(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _Filler67.SetFiller67AsString(data.Substring(offset, 0));
        }
        else
        {
            _Filler67.SetFiller67AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _Filler69.SetFiller69AsString(data.Substring(offset, 0));
        }
        else
        {
            _Filler69.SetFiller69AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 16 <= data.Length)
        {
            _Filler71.SetFiller71AsString(data.Substring(offset, 16));
        }
        else
        {
            _Filler71.SetFiller71AsString(data.Substring(offset));
        }
        offset += 16;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate3TotalDays(parsedInt);
        }
        offset += 8;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate3ReturnCode(parsedInt);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetCgtdate3ActionCode(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate3LinkageAsString();
    }
    // Set<>String Override function
    public void SetCgtdate3Linkage(string value)
    {
        SetCgtdate3LinkageAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate3FromDate()
    {
        return _Cgtdate3FromDate;
    }
    
    // Standard Setter
    public void SetCgtdate3FromDate(string value)
    {
        _Cgtdate3FromDate = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate3FromDateAsString()
    {
        return _Cgtdate3FromDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCgtdate3FromDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate3FromDate = value;
    }
    
    // Standard Getter
    public Filler60 GetFiller60()
    {
        return _Filler60;
    }
    
    // Standard Setter
    public void SetFiller60(Filler60 value)
    {
        _Filler60 = value;
    }
    
    // Get<>AsString()
    public string GetFiller60AsString()
    {
        return _Filler60 != null ? _Filler60.GetFiller60AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller60AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler60 == null)
        {
            _Filler60 = new Filler60();
        }
        _Filler60.SetFiller60AsString(value);
    }
    
    // Standard Getter
    public Filler62 GetFiller62()
    {
        return _Filler62;
    }
    
    // Standard Setter
    public void SetFiller62(Filler62 value)
    {
        _Filler62 = value;
    }
    
    // Get<>AsString()
    public string GetFiller62AsString()
    {
        return _Filler62 != null ? _Filler62.GetFiller62AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller62AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler62 == null)
        {
            _Filler62 = new Filler62();
        }
        _Filler62.SetFiller62AsString(value);
    }
    
    // Standard Getter
    public Filler64 GetFiller64()
    {
        return _Filler64;
    }
    
    // Standard Setter
    public void SetFiller64(Filler64 value)
    {
        _Filler64 = value;
    }
    
    // Get<>AsString()
    public string GetFiller64AsString()
    {
        return _Filler64 != null ? _Filler64.GetFiller64AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller64AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler64 == null)
        {
            _Filler64 = new Filler64();
        }
        _Filler64.SetFiller64AsString(value);
    }
    
    // Standard Getter
    public string GetCgtdate3ToDate()
    {
        return _Cgtdate3ToDate;
    }
    
    // Standard Setter
    public void SetCgtdate3ToDate(string value)
    {
        _Cgtdate3ToDate = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate3ToDateAsString()
    {
        return _Cgtdate3ToDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCgtdate3ToDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate3ToDate = value;
    }
    
    // Standard Getter
    public Filler67 GetFiller67()
    {
        return _Filler67;
    }
    
    // Standard Setter
    public void SetFiller67(Filler67 value)
    {
        _Filler67 = value;
    }
    
    // Get<>AsString()
    public string GetFiller67AsString()
    {
        return _Filler67 != null ? _Filler67.GetFiller67AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller67AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler67 == null)
        {
            _Filler67 = new Filler67();
        }
        _Filler67.SetFiller67AsString(value);
    }
    
    // Standard Getter
    public Filler69 GetFiller69()
    {
        return _Filler69;
    }
    
    // Standard Setter
    public void SetFiller69(Filler69 value)
    {
        _Filler69 = value;
    }
    
    // Get<>AsString()
    public string GetFiller69AsString()
    {
        return _Filler69 != null ? _Filler69.GetFiller69AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller69AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler69 == null)
        {
            _Filler69 = new Filler69();
        }
        _Filler69.SetFiller69AsString(value);
    }
    
    // Standard Getter
    public Filler71 GetFiller71()
    {
        return _Filler71;
    }
    
    // Standard Setter
    public void SetFiller71(Filler71 value)
    {
        _Filler71 = value;
    }
    
    // Get<>AsString()
    public string GetFiller71AsString()
    {
        return _Filler71 != null ? _Filler71.GetFiller71AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller71AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler71 == null)
        {
            _Filler71 = new Filler71();
        }
        _Filler71.SetFiller71AsString(value);
    }
    
    // Standard Getter
    public int GetCgtdate3TotalDays()
    {
        return _Cgtdate3TotalDays;
    }
    
    // Standard Setter
    public void SetCgtdate3TotalDays(int value)
    {
        _Cgtdate3TotalDays = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate3TotalDaysAsString()
    {
        return _Cgtdate3TotalDays.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate3TotalDaysAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate3TotalDays = parsed;
    }
    
    // Standard Getter
    public int GetCgtdate3ReturnCode()
    {
        return _Cgtdate3ReturnCode;
    }
    
    // Standard Setter
    public void SetCgtdate3ReturnCode(int value)
    {
        _Cgtdate3ReturnCode = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate3ReturnCodeAsString()
    {
        return _Cgtdate3ReturnCode.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate3ReturnCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate3ReturnCode = parsed;
    }
    
    // Standard Getter
    public string GetCgtdate3ActionCode()
    {
        return _Cgtdate3ActionCode;
    }
    
    // Standard Setter
    public void SetCgtdate3ActionCode(string value)
    {
        _Cgtdate3ActionCode = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate3ActionCodeAsString()
    {
        return _Cgtdate3ActionCode.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetCgtdate3ActionCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate3ActionCode = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller60(string value)
    {
        _Filler60.SetFiller60AsString(value);
    }
    // Nested Class: Filler60
    public class Filler60
    {
        private static int _size = 0;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate3FromCcyymm, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate3FromCcyymm ="";
        
        
        
        
        // [DEBUG] Field: Filler61, is_external=, is_static_class=False, static_prefix=
        private string _Filler61 ="";
        
        
        
        
    public Filler60() {}
    
    public Filler60(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate3FromCcyymm(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller61(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetFiller60AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate3FromCcyymm.PadRight(0));
        result.Append(_Filler61.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetFiller60AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCgtdate3FromCcyymm(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller61(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate3FromCcyymm()
    {
        return _Cgtdate3FromCcyymm;
    }
    
    // Standard Setter
    public void SetCgtdate3FromCcyymm(string value)
    {
        _Cgtdate3FromCcyymm = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate3FromCcyymmAsString()
    {
        return _Cgtdate3FromCcyymm.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCgtdate3FromCcyymmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate3FromCcyymm = value;
    }
    
    // Standard Getter
    public string GetFiller61()
    {
        return _Filler61;
    }
    
    // Standard Setter
    public void SetFiller61(string value)
    {
        _Filler61 = value;
    }
    
    // Get<>AsString()
    public string GetFiller61AsString()
    {
        return _Filler61.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller61AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler61 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetFiller62(string value)
{
    _Filler62.SetFiller62AsString(value);
}
// Nested Class: Filler62
public class Filler62
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler63, is_external=, is_static_class=False, static_prefix=
    private string _Filler63 ="";
    
    
    
    
    // [DEBUG] Field: Cgtdate3FromYymmdd, is_external=, is_static_class=False, static_prefix=
    private string _Cgtdate3FromYymmdd ="";
    
    
    
    
public Filler62() {}

public Filler62(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller63(data.Substring(offset, 0).Trim());
    offset += 0;
    SetCgtdate3FromYymmdd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetFiller62AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler63.PadRight(0));
    result.Append(_Cgtdate3FromYymmdd.PadRight(0));
    
    return result.ToString();
}

public void SetFiller62AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller63(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetCgtdate3FromYymmdd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller63()
{
    return _Filler63;
}

// Standard Setter
public void SetFiller63(string value)
{
    _Filler63 = value;
}

// Get<>AsString()
public string GetFiller63AsString()
{
    return _Filler63.PadRight(0);
}

// Set<>AsString()
public void SetFiller63AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler63 = value;
}

// Standard Getter
public string GetCgtdate3FromYymmdd()
{
    return _Cgtdate3FromYymmdd;
}

// Standard Setter
public void SetCgtdate3FromYymmdd(string value)
{
    _Cgtdate3FromYymmdd = value;
}

// Get<>AsString()
public string GetCgtdate3FromYymmddAsString()
{
    return _Cgtdate3FromYymmdd.PadRight(0);
}

// Set<>AsString()
public void SetCgtdate3FromYymmddAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Cgtdate3FromYymmdd = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller64(string value)
{
    _Filler64.SetFiller64AsString(value);
}
// Nested Class: Filler64
public class Filler64
{
    private static int _size = 16;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate3FromCcyy, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate3FromCcyy =0;
    
    
    
    
    // [DEBUG] Field: Filler65, is_external=, is_static_class=False, static_prefix=
    private Filler64.Filler65 _Filler65 = new Filler64.Filler65();
    
    
    
    
    // [DEBUG] Field: Cgtdate3FromMmdd, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate3FromMmdd =0;
    
    
    
    
    // [DEBUG] Field: Filler66, is_external=, is_static_class=False, static_prefix=
    private Filler64.Filler66 _Filler66 = new Filler64.Filler66();
    
    
    
    
public Filler64() {}

public Filler64(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate3FromCcyy(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    _Filler65.SetFiller65AsString(data.Substring(offset, Filler65.GetSize()));
    offset += 4;
    SetCgtdate3FromMmdd(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    _Filler66.SetFiller66AsString(data.Substring(offset, Filler66.GetSize()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller64AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate3FromCcyy.ToString().PadLeft(4, '0'));
    result.Append(_Filler65.GetFiller65AsString());
    result.Append(_Cgtdate3FromMmdd.ToString().PadLeft(4, '0'));
    result.Append(_Filler66.GetFiller66AsString());
    
    return result.ToString();
}

public void SetFiller64AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate3FromCcyy(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        _Filler65.SetFiller65AsString(data.Substring(offset, 4));
    }
    else
    {
        _Filler65.SetFiller65AsString(data.Substring(offset));
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate3FromMmdd(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        _Filler66.SetFiller66AsString(data.Substring(offset, 4));
    }
    else
    {
        _Filler66.SetFiller66AsString(data.Substring(offset));
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate3FromCcyy()
{
    return _Cgtdate3FromCcyy;
}

// Standard Setter
public void SetCgtdate3FromCcyy(int value)
{
    _Cgtdate3FromCcyy = value;
}

// Get<>AsString()
public string GetCgtdate3FromCcyyAsString()
{
    return _Cgtdate3FromCcyy.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate3FromCcyyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate3FromCcyy = parsed;
}

// Standard Getter
public Filler65 GetFiller65()
{
    return _Filler65;
}

// Standard Setter
public void SetFiller65(Filler65 value)
{
    _Filler65 = value;
}

// Get<>AsString()
public string GetFiller65AsString()
{
    return _Filler65 != null ? _Filler65.GetFiller65AsString() : "";
}

// Set<>AsString()
public void SetFiller65AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler65 == null)
    {
        _Filler65 = new Filler65();
    }
    _Filler65.SetFiller65AsString(value);
}

// Standard Getter
public int GetCgtdate3FromMmdd()
{
    return _Cgtdate3FromMmdd;
}

// Standard Setter
public void SetCgtdate3FromMmdd(int value)
{
    _Cgtdate3FromMmdd = value;
}

// Get<>AsString()
public string GetCgtdate3FromMmddAsString()
{
    return _Cgtdate3FromMmdd.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate3FromMmddAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate3FromMmdd = parsed;
}

// Standard Getter
public Filler66 GetFiller66()
{
    return _Filler66;
}

// Standard Setter
public void SetFiller66(Filler66 value)
{
    _Filler66 = value;
}

// Get<>AsString()
public string GetFiller66AsString()
{
    return _Filler66 != null ? _Filler66.GetFiller66AsString() : "";
}

// Set<>AsString()
public void SetFiller66AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler66 == null)
    {
        _Filler66 = new Filler66();
    }
    _Filler66.SetFiller66AsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: Filler65
public class Filler65
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate3FromCc, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate3FromCc =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate3FromYy, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate3FromYy =0;
    
    
    
    
public Filler65() {}

public Filler65(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate3FromCc(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate3FromYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller65AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate3FromCc.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate3FromYy.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller65AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate3FromCc(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate3FromYy(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate3FromCc()
{
    return _Cgtdate3FromCc;
}

// Standard Setter
public void SetCgtdate3FromCc(int value)
{
    _Cgtdate3FromCc = value;
}

// Get<>AsString()
public string GetCgtdate3FromCcAsString()
{
    return _Cgtdate3FromCc.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate3FromCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate3FromCc = parsed;
}

// Standard Getter
public int GetCgtdate3FromYy()
{
    return _Cgtdate3FromYy;
}

// Standard Setter
public void SetCgtdate3FromYy(int value)
{
    _Cgtdate3FromYy = value;
}

// Get<>AsString()
public string GetCgtdate3FromYyAsString()
{
    return _Cgtdate3FromYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate3FromYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate3FromYy = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler66
public class Filler66
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate3FromMm, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate3FromMm =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate3FromDd, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate3FromDd =0;
    
    
    
    
public Filler66() {}

public Filler66(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate3FromMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate3FromDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller66AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate3FromMm.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate3FromDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller66AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate3FromMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate3FromDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate3FromMm()
{
    return _Cgtdate3FromMm;
}

// Standard Setter
public void SetCgtdate3FromMm(int value)
{
    _Cgtdate3FromMm = value;
}

// Get<>AsString()
public string GetCgtdate3FromMmAsString()
{
    return _Cgtdate3FromMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate3FromMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate3FromMm = parsed;
}

// Standard Getter
public int GetCgtdate3FromDd()
{
    return _Cgtdate3FromDd;
}

// Standard Setter
public void SetCgtdate3FromDd(int value)
{
    _Cgtdate3FromDd = value;
}

// Get<>AsString()
public string GetCgtdate3FromDdAsString()
{
    return _Cgtdate3FromDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate3FromDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate3FromDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetFiller67(string value)
{
    _Filler67.SetFiller67AsString(value);
}
// Nested Class: Filler67
public class Filler67
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate3ToCcyymm, is_external=, is_static_class=False, static_prefix=
    private string _Cgtdate3ToCcyymm ="";
    
    
    
    
    // [DEBUG] Field: Filler68, is_external=, is_static_class=False, static_prefix=
    private string _Filler68 ="";
    
    
    
    
public Filler67() {}

public Filler67(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate3ToCcyymm(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller68(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetFiller67AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate3ToCcyymm.PadRight(0));
    result.Append(_Filler68.PadRight(0));
    
    return result.ToString();
}

public void SetFiller67AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetCgtdate3ToCcyymm(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller68(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetCgtdate3ToCcyymm()
{
    return _Cgtdate3ToCcyymm;
}

// Standard Setter
public void SetCgtdate3ToCcyymm(string value)
{
    _Cgtdate3ToCcyymm = value;
}

// Get<>AsString()
public string GetCgtdate3ToCcyymmAsString()
{
    return _Cgtdate3ToCcyymm.PadRight(0);
}

// Set<>AsString()
public void SetCgtdate3ToCcyymmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Cgtdate3ToCcyymm = value;
}

// Standard Getter
public string GetFiller68()
{
    return _Filler68;
}

// Standard Setter
public void SetFiller68(string value)
{
    _Filler68 = value;
}

// Get<>AsString()
public string GetFiller68AsString()
{
    return _Filler68.PadRight(0);
}

// Set<>AsString()
public void SetFiller68AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler68 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller69(string value)
{
    _Filler69.SetFiller69AsString(value);
}
// Nested Class: Filler69
public class Filler69
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler70, is_external=, is_static_class=False, static_prefix=
    private string _Filler70 ="";
    
    
    
    
    // [DEBUG] Field: Cgtdate3ToYymmdd, is_external=, is_static_class=False, static_prefix=
    private string _Cgtdate3ToYymmdd ="";
    
    
    
    
public Filler69() {}

public Filler69(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller70(data.Substring(offset, 0).Trim());
    offset += 0;
    SetCgtdate3ToYymmdd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetFiller69AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler70.PadRight(0));
    result.Append(_Cgtdate3ToYymmdd.PadRight(0));
    
    return result.ToString();
}

public void SetFiller69AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller70(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetCgtdate3ToYymmdd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller70()
{
    return _Filler70;
}

// Standard Setter
public void SetFiller70(string value)
{
    _Filler70 = value;
}

// Get<>AsString()
public string GetFiller70AsString()
{
    return _Filler70.PadRight(0);
}

// Set<>AsString()
public void SetFiller70AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler70 = value;
}

// Standard Getter
public string GetCgtdate3ToYymmdd()
{
    return _Cgtdate3ToYymmdd;
}

// Standard Setter
public void SetCgtdate3ToYymmdd(string value)
{
    _Cgtdate3ToYymmdd = value;
}

// Get<>AsString()
public string GetCgtdate3ToYymmddAsString()
{
    return _Cgtdate3ToYymmdd.PadRight(0);
}

// Set<>AsString()
public void SetCgtdate3ToYymmddAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Cgtdate3ToYymmdd = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller71(string value)
{
    _Filler71.SetFiller71AsString(value);
}
// Nested Class: Filler71
public class Filler71
{
    private static int _size = 16;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate3ToCcyy, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate3ToCcyy =0;
    
    
    
    
    // [DEBUG] Field: Filler72, is_external=, is_static_class=False, static_prefix=
    private Filler71.Filler72 _Filler72 = new Filler71.Filler72();
    
    
    
    
    // [DEBUG] Field: Cgtdate3ToMmdd, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate3ToMmdd =0;
    
    
    
    
    // [DEBUG] Field: Filler73, is_external=, is_static_class=False, static_prefix=
    private Filler71.Filler73 _Filler73 = new Filler71.Filler73();
    
    
    
    
public Filler71() {}

public Filler71(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate3ToCcyy(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    _Filler72.SetFiller72AsString(data.Substring(offset, Filler72.GetSize()));
    offset += 4;
    SetCgtdate3ToMmdd(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    _Filler73.SetFiller73AsString(data.Substring(offset, Filler73.GetSize()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller71AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate3ToCcyy.ToString().PadLeft(4, '0'));
    result.Append(_Filler72.GetFiller72AsString());
    result.Append(_Cgtdate3ToMmdd.ToString().PadLeft(4, '0'));
    result.Append(_Filler73.GetFiller73AsString());
    
    return result.ToString();
}

public void SetFiller71AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate3ToCcyy(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        _Filler72.SetFiller72AsString(data.Substring(offset, 4));
    }
    else
    {
        _Filler72.SetFiller72AsString(data.Substring(offset));
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate3ToMmdd(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        _Filler73.SetFiller73AsString(data.Substring(offset, 4));
    }
    else
    {
        _Filler73.SetFiller73AsString(data.Substring(offset));
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate3ToCcyy()
{
    return _Cgtdate3ToCcyy;
}

// Standard Setter
public void SetCgtdate3ToCcyy(int value)
{
    _Cgtdate3ToCcyy = value;
}

// Get<>AsString()
public string GetCgtdate3ToCcyyAsString()
{
    return _Cgtdate3ToCcyy.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate3ToCcyyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate3ToCcyy = parsed;
}

// Standard Getter
public Filler72 GetFiller72()
{
    return _Filler72;
}

// Standard Setter
public void SetFiller72(Filler72 value)
{
    _Filler72 = value;
}

// Get<>AsString()
public string GetFiller72AsString()
{
    return _Filler72 != null ? _Filler72.GetFiller72AsString() : "";
}

// Set<>AsString()
public void SetFiller72AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler72 == null)
    {
        _Filler72 = new Filler72();
    }
    _Filler72.SetFiller72AsString(value);
}

// Standard Getter
public int GetCgtdate3ToMmdd()
{
    return _Cgtdate3ToMmdd;
}

// Standard Setter
public void SetCgtdate3ToMmdd(int value)
{
    _Cgtdate3ToMmdd = value;
}

// Get<>AsString()
public string GetCgtdate3ToMmddAsString()
{
    return _Cgtdate3ToMmdd.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate3ToMmddAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate3ToMmdd = parsed;
}

// Standard Getter
public Filler73 GetFiller73()
{
    return _Filler73;
}

// Standard Setter
public void SetFiller73(Filler73 value)
{
    _Filler73 = value;
}

// Get<>AsString()
public string GetFiller73AsString()
{
    return _Filler73 != null ? _Filler73.GetFiller73AsString() : "";
}

// Set<>AsString()
public void SetFiller73AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler73 == null)
    {
        _Filler73 = new Filler73();
    }
    _Filler73.SetFiller73AsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: Filler72
public class Filler72
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate3ToCc, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate3ToCc =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate3ToYy, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate3ToYy =0;
    
    
    
    
public Filler72() {}

public Filler72(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate3ToCc(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate3ToYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller72AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate3ToCc.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate3ToYy.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller72AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate3ToCc(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate3ToYy(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate3ToCc()
{
    return _Cgtdate3ToCc;
}

// Standard Setter
public void SetCgtdate3ToCc(int value)
{
    _Cgtdate3ToCc = value;
}

// Get<>AsString()
public string GetCgtdate3ToCcAsString()
{
    return _Cgtdate3ToCc.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate3ToCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate3ToCc = parsed;
}

// Standard Getter
public int GetCgtdate3ToYy()
{
    return _Cgtdate3ToYy;
}

// Standard Setter
public void SetCgtdate3ToYy(int value)
{
    _Cgtdate3ToYy = value;
}

// Get<>AsString()
public string GetCgtdate3ToYyAsString()
{
    return _Cgtdate3ToYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate3ToYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate3ToYy = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler73
public class Filler73
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate3ToMm, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate3ToMm =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate3ToDd, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate3ToDd =0;
    
    
    
    
public Filler73() {}

public Filler73(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate3ToMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate3ToDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller73AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate3ToMm.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate3ToDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller73AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate3ToMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate3ToDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate3ToMm()
{
    return _Cgtdate3ToMm;
}

// Standard Setter
public void SetCgtdate3ToMm(int value)
{
    _Cgtdate3ToMm = value;
}

// Get<>AsString()
public string GetCgtdate3ToMmAsString()
{
    return _Cgtdate3ToMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate3ToMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate3ToMm = parsed;
}

// Standard Getter
public int GetCgtdate3ToDd()
{
    return _Cgtdate3ToDd;
}

// Standard Setter
public void SetCgtdate3ToDd(int value)
{
    _Cgtdate3ToDd = value;
}

// Get<>AsString()
public string GetCgtdate3ToDdAsString()
{
    return _Cgtdate3ToDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate3ToDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate3ToDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}

}}
