using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D93TrailerRecord Data Structure

public class D93TrailerRecord
{
    private static int _size = 28;
    // [DEBUG] Class: D93TrailerRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler133, is_external=, is_static_class=False, static_prefix=
    private string _Filler133 ="";
    
    
    // 88-level condition checks for Filler133
    public bool IsD93TrailerFound()
    {
        if (this._Filler133 == "'9999EQUITY RPI TRAILER'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D93TrailerCount, is_external=, is_static_class=False, static_prefix=
    private int _D93TrailerCount =0;
    
    
    
    
    
    // Serialization methods
    public string GetD93TrailerRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler133.PadRight(22));
        result.Append(_D93TrailerCount.ToString().PadLeft(6, '0'));
        
        return result.ToString();
    }
    
    public void SetD93TrailerRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 22 <= data.Length)
        {
            string extracted = data.Substring(offset, 22).Trim();
            SetFiller133(extracted);
        }
        offset += 22;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD93TrailerCount(parsedInt);
        }
        offset += 6;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD93TrailerRecordAsString();
    }
    // Set<>String Override function
    public void SetD93TrailerRecord(string value)
    {
        SetD93TrailerRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller133()
    {
        return _Filler133;
    }
    
    // Standard Setter
    public void SetFiller133(string value)
    {
        _Filler133 = value;
    }
    
    // Get<>AsString()
    public string GetFiller133AsString()
    {
        return _Filler133.PadRight(22);
    }
    
    // Set<>AsString()
    public void SetFiller133AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler133 = value;
    }
    
    // Standard Getter
    public int GetD93TrailerCount()
    {
        return _D93TrailerCount;
    }
    
    // Standard Setter
    public void SetD93TrailerCount(int value)
    {
        _D93TrailerCount = value;
    }
    
    // Get<>AsString()
    public string GetD93TrailerCountAsString()
    {
        return _D93TrailerCount.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD93TrailerCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D93TrailerCount = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}