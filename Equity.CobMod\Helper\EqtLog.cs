﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using EquityProject.EqtlogDTO;
using Legacy4.Equity.CobMod.FIleHandlers;

namespace EquityProject.EqtlogPGM
{
    // Eqtlog Class Definition

    //Eqtlog Class Constructor
    public class Eqtlog
    {
        // Declare Eqtlog Class private variables
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();

        // Declare Eqtlog Class getters setters
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }



        // Run() method
        public void Run(Gvar gvar, Ivar ivar)
        {
            // Implement main program logic here

        }

        // CallSub() method
        public void CallSub(Ivar ivar)
        {           
            // Implement subroutine call logic here
            AControl(_gvar, ivar);
        }

        // Methods representing paras under procedure division

        // Methods representing sections
        public void AControl(Gvar gvar, Ivar ivar)
        {
            LogMessage logMessage = new LogMessage();

            // Set log message details
            logMessage.SetLogCode(ivar.GetLinkageArea1().GetLStatus());
            logMessage.SetLogAction(ivar.GetLinkageArea1().GetLAction());
            logMessage.SetLogString(ivar.GetLinkageArea2().GetLMessage());

            RunLogDAL runLogDAL = new RunLogDAL();
            runLogDAL.ProcessLogRequest(logMessage);

            ivar.GetLinkageArea1().SetLStatus("00");
        }
        // Methods representing paras under sections
        public void AExit(Gvar gvar, Ivar ivar)
        {

        }
    }
}
