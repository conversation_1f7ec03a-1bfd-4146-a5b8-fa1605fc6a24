using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WlLiterals Data Structure

public class WlLiterals
{
    private static int _size = 44;
    // [DEBUG] Class: WlLiterals, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler356, is_external=, is_static_class=False, static_prefix=
    private string _Filler356 ="LITERALS";
    
    
    
    
    // [DEBUG] Field: Cannotbe, is_external=, is_static_class=False, static_prefix=
    private string _Cannotbe ="CANNOTBE";
    
    
    
    
    // [DEBUG] Field: DisplayPgm, is_external=, is_static_class=False, static_prefix=
    private string _DisplayPgm ="ELCGTMFG";
    
    
    
    
    // [DEBUG] Field: Ws1988Date, is_external=, is_static_class=False, static_prefix=
    private string _Ws1988Date ="880406";
    
    
    
    
    // [DEBUG] Field: Ws1989Date, is_external=, is_static_class=False, static_prefix=
    private string _Ws1989Date ="890315";
    
    
    
    
    
    // Serialization methods
    public string GetWlLiteralsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler356.PadRight(16));
        result.Append(_Cannotbe.PadRight(8));
        result.Append(_DisplayPgm.PadRight(8));
        result.Append(_Ws1988Date.PadRight(6));
        result.Append(_Ws1989Date.PadRight(6));
        
        return result.ToString();
    }
    
    public void SetWlLiteralsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller356(extracted);
        }
        offset += 16;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetCannotbe(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetDisplayPgm(extracted);
        }
        offset += 8;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetWs1988Date(extracted);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetWs1989Date(extracted);
        }
        offset += 6;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWlLiteralsAsString();
    }
    // Set<>String Override function
    public void SetWlLiterals(string value)
    {
        SetWlLiteralsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller356()
    {
        return _Filler356;
    }
    
    // Standard Setter
    public void SetFiller356(string value)
    {
        _Filler356 = value;
    }
    
    // Get<>AsString()
    public string GetFiller356AsString()
    {
        return _Filler356.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller356AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler356 = value;
    }
    
    // Standard Getter
    public string GetCannotbe()
    {
        return _Cannotbe;
    }
    
    // Standard Setter
    public void SetCannotbe(string value)
    {
        _Cannotbe = value;
    }
    
    // Get<>AsString()
    public string GetCannotbeAsString()
    {
        return _Cannotbe.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetCannotbeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cannotbe = value;
    }
    
    // Standard Getter
    public string GetDisplayPgm()
    {
        return _DisplayPgm;
    }
    
    // Standard Setter
    public void SetDisplayPgm(string value)
    {
        _DisplayPgm = value;
    }
    
    // Get<>AsString()
    public string GetDisplayPgmAsString()
    {
        return _DisplayPgm.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetDisplayPgmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _DisplayPgm = value;
    }
    
    // Standard Getter
    public string GetWs1988Date()
    {
        return _Ws1988Date;
    }
    
    // Standard Setter
    public void SetWs1988Date(string value)
    {
        _Ws1988Date = value;
    }
    
    // Get<>AsString()
    public string GetWs1988DateAsString()
    {
        return _Ws1988Date.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetWs1988DateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Ws1988Date = value;
    }
    
    // Standard Getter
    public string GetWs1989Date()
    {
        return _Ws1989Date;
    }
    
    // Standard Setter
    public void SetWs1989Date(string value)
    {
        _Ws1989Date = value;
    }
    
    // Get<>AsString()
    public string GetWs1989DateAsString()
    {
        return _Ws1989Date.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetWs1989DateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Ws1989Date = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}