﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using EquityProject.ElcggioDTO;

namespace Equity.CobMod.FIleHandlers
{
    public class Elcggio
    {
        // Declare Elcggio Class private variables
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();

        // Declare Elcggio Class getters setters
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        // Placeholder methods for external COBOL calls
        // In a real migration, these would be calls to other C# classes or services.
        private void CallCgtLog(Gvar gvar, Ivar ivar)
        {
            // Simulate call to CGTLOG
            Console.WriteLine("Calling CGTLOG...");
            // Add actual logic or call to CgtLog class here if it exists
        }

        private void CallCgtSched(CgtfilesLinkage cgtFilesLinkage, LFileRecordArea lFileRecordArea, CommonLinkage commonLinkage)
        {
            // Simulate call to CGTSCHED
            Console.WriteLine("Calling CGTSCHED...");
            // Add actual logic or call to CgtSched class here if it exists
        }

        private void CallCgtFiles(CgtfilesLinkage cgtFilesLinkage, LFileRecordArea lFileRecordArea, CommonLinkage commonLinkage)
        {
            // Simulate call to CGTFILES
            Console.WriteLine("Calling CGTFILES...");
            // Add actual logic or call to CgtFiles class here if it exists
        }


        // Run() method - Corresponds to COBOL PROCEDURE DIVISION
        public void Run(Gvar gvar, Ivar ivar)
        {
            // Implement main program logic here
            // Corresponds to COBOL A-CONTROL SECTION.

            // ELCGGIO-ACTION corresponds to ivar.GetElcggioLink1().GetLink11().GetElcggioAction()
            switch (ivar.GetElcggioLink1().GetLink11().GetElcggioAction())
            {
                case "FN ":
                    // MOVE ELCGGIO-USER-NUMBER TO L-USER-NO
                    // ELCGGIO-USER-NUMBER is in ivar.GetElcggioLink2().GetFiller5().GetLUser().GetElcggioUserNumber()
                    // L-USER-NO is in gvar.GetCommonLinkage()
                    gvar.GetCommonLinkage().SetLUserNoAsString(ivar.GetElcggioLink2().GetFiller5().GetLUser().GetElcggioUserNumber());

                    // MOVE ELCGGIO-REPORT-NUMBER TO L-REPORT-NO
                    // ELCGGIO-REPORT-NUMBER is in ivar.GetElcggioLink2().GetFiller5().GetElcggioReportNumber()
                    // L-REPORT-NO is in gvar.GetCgtfilesLinkage()
                    gvar.GetCgtfilesLinkage().SetLReportNo(ivar.GetElcggioLink2().GetFiller5().GetElcggioReportNumber());
                    break;

                default: // WHEN OTHER
                    // ELCGGIO-FILE-NAME corresponds to ivar.GetElcggioLink1().GetLink11().GetElcggioFileName()
                    switch (ivar.GetElcggioLink1().GetLink11().GetElcggioFileName())
                    {
                        case Gvar.MESSAGE_FILE: // "CGTMSG  "
                            // CALL "CGTLOG" USING ELCGGIO-LINK-1 ELCGGIO-LINK-2
                            CallCgtLog(gvar, ivar);
                            break;

                        // WHEN REALISED-SCHEDULE-FILE
                        // WHEN UNREALISED-SCHEDULE-FILE
                        // WHEN NOTIONAL-SALE-SCHEDULE-FILE
                        // WHEN REALISED-TAX-SCHEDULE-FILE
                        // WHEN UNREALISED-TAX-SCHEDULE-FILE
                        case Gvar.REALISED_SCHEDULE_FILE:
                        case Gvar.UNREALISED_SCHEDULE_FILE:
                        case Gvar.NOTIONAL_SALE_SCHEDULE_FILE:
                        case Gvar.REALISED_TAX_SCHEDULE_FILE:
                        case Gvar.UNREALISED_TAX_SCHEDULE_FILE:
                            // PERFORM B-MOVE-IN
                            B_MOVE_IN(gvar, ivar);
                            // CALL "CGTSCHED" USING CGTFILES-LINKAGE L-FILE-RECORD-AREA COMMON-LINKAGE
                            CallCgtSched(gvar.GetCgtfilesLinkage(), gvar.GetLFileRecordArea(), gvar.GetCommonLinkage());
                            // PERFORM C-MOVE-OUT
                            C_MOVE_OUT(gvar, ivar);
                            break;

                        default: // WHEN OTHER
                            // PERFORM B-MOVE-IN
                            B_MOVE_IN(gvar, ivar);
                            // CALL "CGTFILES" USING CGTFILES-LINKAGE L-FILE-RECORD-AREA COMMON-LINKAGE
                            CallCgtFiles(gvar.GetCgtfilesLinkage(), gvar.GetLFileRecordArea(), gvar.GetCommonLinkage());
                            // PERFORM C-MOVE-OUT
                            C_MOVE_OUT(gvar, ivar);
                            break;
                    }
                    break;
            }

            // EXIT PROGRAM. - Implicitly handled by method end
        }

        // B-MOVE-IN SECTION.
        private void B_MOVE_IN(Gvar gvar, Ivar ivar)
        {
            // MOVE ELCGGIO-FILE-NAME TO L-FILE-NAME.
            gvar.GetCgtfilesLinkage().SetLFileName(ivar.GetElcggioLink1().GetLink11().GetElcggioFileName());
            // MOVE ELCGGIO-ACTION TO L-FILE-ACTION.
            gvar.GetCgtfilesLinkage().SetLFileAction(ivar.GetElcggioLink1().GetLink11().GetElcggioAction());
            // MOVE ELCGGIO-RETURN-CODE TO L-FILE-RETURN-CODE.
            gvar.GetCgtfilesLinkage().SetLFileReturnCode(ivar.GetElcggioLink1().GetLink11().GetElcggioReturnCode());

            // EVALUATE ELCGGIO-FILE-NAME
            switch (ivar.GetElcggioLink1().GetLink11().GetElcggioFileName())
            {
                case Gvar.COUNTRY_FILE:
                    // MOVE L-COUNTRY-FILE TO L-FILE-RECORD-AREA
                    gvar.GetLFileRecordArea().SetLFileRecordAreaAsString(ivar.GetElcggioLink2().GetLCountryFileAsString());
                    break;
                case Gvar.GROUP_FILE:
                    // MOVE L-GROUP-FILE TO L-FILE-RECORD-AREA
                    gvar.GetLFileRecordArea().SetLFileRecordAreaAsString(ivar.GetElcggioLink2().GetLGroupFileAsString());
                    break;
                case Gvar.STOCK_FILE:
                    // MOVE L-STOCK-FILE TO L-FILE-RECORD-AREA
                    gvar.GetLFileRecordArea().SetLFileRecordAreaAsString(ivar.GetElcggioLink2().GetLStockFileAsString());
                    break;
                case Gvar.USER_FUND_FILE: // Net03
                    // MOVE L-USER-FUND-FILE TO L-FILE-RECORD-AREA
                    gvar.GetLFileRecordArea().SetLFileRecordAreaAsString(ivar.GetElcggioLink2().GetLUserFundFileAsString());
                    break;
                case Gvar.RPI_FILE:
                    // MOVE L-RPI-FILE TO L-FILE-RECORD-AREA
                    gvar.GetLFileRecordArea().SetLFileRecordAreaAsString(ivar.GetElcggioLink2().GetLRpiFileAsString());
                    break;
                // WHEN REALISED-DATA-FILE
                // WHEN UNREALISED-DATA-FILE
                // WHEN NOTIONAL-SALE-DATA-FILE // BB3
                // WHEN REALISED-TAX-DATA-FILE // BB5
                // WHEN UNREALISED-TAX-DATA-FILE // BB5
                case Gvar.REALISED_DATA_FILE:
                case Gvar.UNREALISED_DATA_FILE:
                case Gvar.NOTIONAL_SALE_DATA_FILE:
                case Gvar.REALISED_TAX_DATA_FILE:
                case Gvar.UNREALISED_TAX_DATA_FILE:
                    // MOVE L-SCHEDULE-DATA-FILE TO L-FILE-RECORD-AREA
                    gvar.GetLFileRecordArea().SetLFileRecordAreaAsString(ivar.GetElcggioLink2().GetLScheduleDataFileAsString());
                    break;
                // WHEN REALISED-SCHEDULE-FILE
                // WHEN UNREALISED-SCHEDULE-FILE
                // WHEN NOTIONAL-SALE-SCHEDULE-FILE // BB3
                // WHEN REALISED-TAX-SCHEDULE-FILE // BB5
                // WHEN UNREALISED-TAX-SCHEDULE-FILE // BB5
                case Gvar.REALISED_SCHEDULE_FILE:
                case Gvar.UNREALISED_SCHEDULE_FILE:
                case Gvar.NOTIONAL_SALE_SCHEDULE_FILE:
                case Gvar.REALISED_TAX_SCHEDULE_FILE:
                case Gvar.UNREALISED_TAX_SCHEDULE_FILE:
                    // MOVE L-SCHEDULE-REPORT-FILE TO L-FILE-RECORD-AREA
                    gvar.GetLFileRecordArea().SetLFileRecordAreaAsString(ivar.GetElcggioLink2().GetLScheduleReportFileAsString());
                    break;
                case Gvar.ERROR_DATA_FILE:
                    // MOVE L-ED-FILE TO L-FILE-RECORD-AREA
                    gvar.GetLFileRecordArea().SetLFileRecordAreaAsString(ivar.GetElcggioLink2().GetLEdFileAsString());
                    break;
                case Gvar.YE_REC2_DATA_FILE:
                    // MOVE L-YER2D-FILE TO L-FILE-RECORD-AREA
                    gvar.GetLFileRecordArea().SetLFileRecordAreaAsString(ivar.GetElcggioLink2().GetLYer2DFileAsString());
                    break;
                case Gvar.SEQ_BALANCE_FILE:
                    // MOVE L-BALANCE-FILE TO L-FILE-RECORD-AREA
                    gvar.GetLFileRecordArea().SetLFileRecordAreaAsString(ivar.GetElcggioLink2().GetLBalanceFileAsString());
                    break;
                // BB2*****WHEN SEDOL-HEADERS-FILE - Commented out in COBOL
                // BB2*****     MOVE L-SEDOL-H-FILE TO L-FILE-RECORD-AREA
                // case Gvar.SEDOL_HEADERS_FILE: // Assuming SEDOL_HEADERS_FILE constant exists
                //     gvar.GetLFileRecordArea().SetLFileRecordAreaAsString(ivar.GetElcggioLink2().GetLSedolHFileAsString());
                //     break;
                case Gvar.RCF_FILE: // MJ1
                    // MOVE L-RCF-H-FILE TO L-FILE-RECORD-AREA
                    gvar.GetLFileRecordArea().SetLFileRecordAreaAsString(ivar.GetElcggioLink2().GetLRcfHFileAsString());
                    break;
                case Gvar.GAINLOSS_DATA_FILE: // BB4
                    // MOVE L-GAINLOSS-FILE TO L-FILE-RECORD-AREA
                    gvar.GetLFileRecordArea().SetLFileRecordAreaAsString(ivar.GetElcggioLink2().GetLGainlossFileAsString());
                    break;
                default: // BB1 WHEN OTHER
                    // MOVE ELCGGIO-RECORD-AREA TO L-FILE-RECORD-AREA
                    gvar.GetLFileRecordArea().SetLFileRecordAreaAsString(ivar.GetElcggioLink2().GetElcggioRecordAreaAsString());
                    break;
            }
        }

        // C-MOVE-OUT SECTION.
        private void C_MOVE_OUT(Gvar gvar, Ivar ivar)
        {
            // MOVE L-FILE-NAME TO ELCGGIO-FILE-NAME.
            ivar.GetElcggioLink1().GetLink11().SetElcggioFileName(gvar.GetCgtfilesLinkage().GetLFileName());
            // MOVE L-FILE-ACTION TO ELCGGIO-ACTION.
            ivar.GetElcggioLink1().GetLink11().SetElcggioAction(gvar.GetCgtfilesLinkage().GetLFileAction());

            // EVALUATE L-FILE-RETURN-CODE
            string lFileReturnCode = gvar.GetCgtfilesLinkage().GetLFileReturnCode();
            if (lFileReturnCode == "05")
            {
                // MOVE "00" TO ELCGGIO-RETURN-CODE
                ivar.GetElcggioLink1().GetLink11().SetElcggioReturnCode("00");
            }
            else
            {
                // MOVE L-FILE-RETURN-CODE TO ELCGGIO-RETURN-CODE
                ivar.GetElcggioLink1().GetLink11().SetElcggioReturnCode(lFileReturnCode);
            }

            // EVALUATE ELCGGIO-FILE-NAME (Note: ELCGGIO-FILE-NAME was set above from L-FILE-NAME)
            switch (ivar.GetElcggioLink1().GetLink11().GetElcggioFileName())
            {
                case Gvar.COUNTRY_FILE:
                    // MOVE L-FILE-RECORD-AREA TO L-COUNTRY-FILE
                    ivar.GetElcggioLink2().GetLCountryFile().SetLCountryFileAsString(gvar.GetLFileRecordAreaAsString());
                    break;
                case Gvar.GROUP_FILE:
                    // MOVE L-FILE-RECORD-AREA TO L-GROUP-FILE
                    ivar.GetElcggioLink2().GetLGroupFile().SetLGroupFileAsString(gvar.GetLFileRecordAreaAsString());
                    break;
                case Gvar.STOCK_FILE:
                    // MOVE L-FILE-RECORD-AREA TO L-STOCK-FILE
                    ivar.GetElcggioLink2().GetLStockFile().SetLStockFileAsString(gvar.GetLFileRecordAreaAsString());
                    break;
                case Gvar.USER_FUND_FILE: // Net03
                    // MOVE L-FILE-RECORD-AREA TO L-USER-FUND-FILE
                    ivar.GetElcggioLink2().GetLUserFundFile().SetLUserFundFileAsString(gvar.GetLFileRecordAreaAsString());
                    break;
                case Gvar.RPI_FILE:
                    // MOVE L-FILE-RECORD-AREA TO L-RPI-FILE
                    ivar.GetElcggioLink2().GetLRpiFile().SetLRpiFileAsString(gvar.GetLFileRecordAreaAsString());
                    break;
                // WHEN REALISED-DATA-FILE // BB5
                // WHEN REALISED-TAX-DATA-FILE // BB5
                // WHEN UNREALISED-DATA-FILE
                // WHEN UNREALISED-TAX-DATA-FILE // BB5
                // WHEN NOTIONAL-SALE-DATA-FILE // BB3
                case Gvar.REALISED_DATA_FILE:
                case Gvar.REALISED_TAX_DATA_FILE:
                case Gvar.UNREALISED_DATA_FILE:
                case Gvar.UNREALISED_TAX_DATA_FILE:
                case Gvar.NOTIONAL_SALE_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO L-SCHEDULE-DATA-FILE
                    ivar.GetElcggioLink2().GetLScheduleDataFile().SetLScheduleDataFileAsString(gvar.GetLFileRecordAreaAsString());
                    break;
                // WHEN REALISED-SCHEDULE-FILE
                // WHEN REALISED-TAX-SCHEDULE-FILE // BB5
                // WHEN UNREALISED-SCHEDULE-FILE
                // WHEN UNREALISED-TAX-SCHEDULE-FILE // BB5
                // WHEN NOTIONAL-SALE-SCHEDULE-FILE // BB3
                case Gvar.REALISED_SCHEDULE_FILE:
                case Gvar.REALISED_TAX_SCHEDULE_FILE:
                case Gvar.UNREALISED_SCHEDULE_FILE:
                case Gvar.UNREALISED_TAX_SCHEDULE_FILE:
                case Gvar.NOTIONAL_SALE_SCHEDULE_FILE:
                    // MOVE L-FILE-RECORD-AREA TO L-SCHEDULE-REPORT-FILE
                    ivar.GetElcggioLink2().GetLScheduleReportFile().SetLScheduleReportFileAsString(gvar.GetLFileRecordAreaAsString());
                    break;
                case Gvar.ERROR_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO L-ED-FILE
                    ivar.GetElcggioLink2().GetLEdFile().SetLEdFileAsString(gvar.GetLFileRecordAreaAsString());
                    break;
                case Gvar.YE_REC2_DATA_FILE:
                    // MOVE L-FILE-RECORD-AREA TO L-YER2D-FILE
                    ivar.GetElcggioLink2().GetLYer2DFile().SetLYer2DFileAsString(gvar.GetLFileRecordAreaAsString());
                    break;
                case Gvar.SEQ_BALANCE_FILE:
                    // MOVE L-FILE-RECORD-AREA TO L-BALANCE-FILE
                    ivar.GetElcggioLink2().GetLBalanceFile().SetLBalanceFileAsString(gvar.GetLFileRecordAreaAsString());
                    break;
                // BB2*****WHEN SEDOL-HEADERS-FILE - Commented out in COBOL
                // BB2*****     MOVE L-FILE-RECORD-AREA TO L-SEDOL-H-FILE
                // case Gvar.SEDOL_HEADERS_FILE: // Assuming SEDOL_HEADERS_FILE constant exists
                //     ivar.GetElcggioLink2().GetLSedolHFile().SetLSedolHFileAsString(gvar.GetLFileRecordAreaAsString());
                //     break;
                case Gvar.RCF_FILE: // MJ1
                    // MOVE L-FILE-RECORD-AREA TO L-RCF-H-FILE
                    ivar.GetElcggioLink2().GetLRcfHFile().SetLRcfHFileAsString(gvar.GetLFileRecordAreaAsString());
                    break;
                case Gvar.GAINLOSS_DATA_FILE: // BB4
                    // MOVE L-FILE-RECORD-AREA TO L-GAINLOSS-FILE
                    ivar.GetElcggioLink2().GetLGainlossFile().SetLGainlossFileAsString(gvar.GetLFileRecordAreaAsString());
                    break;
                default: // BB1 WHEN OTHER
                    // MOVE L-FILE-RECORD-AREA TO ELCGGIO-RECORD-AREA
                    ivar.GetElcggioLink2().SetElcggioRecordArea(gvar.GetLFileRecordAreaAsString());
                    break;
            }
        }
    }
}
