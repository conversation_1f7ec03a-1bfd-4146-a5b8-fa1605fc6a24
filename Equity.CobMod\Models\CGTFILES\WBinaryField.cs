using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing WBinaryField Data Structure

public class WBinaryField
{
    private static int _size = 2;
    // [DEBUG] Class: WBinaryField, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler157, is_external=, is_static_class=False, static_prefix=
    private string _Filler157 ="LOW-VALUE";
    
    
    
    
    // [DEBUG] Field: WBinaryChar, is_external=, is_static_class=False, static_prefix=
    private string _WBinaryChar ="";
    
    
    
    
    
    // Serialization methods
    public string GetWBinaryFieldAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler157.PadRight(1));
        result.Append(_WBinaryChar.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWBinaryFieldAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller157(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWBinaryChar(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWBinaryFieldAsString();
    }
    // Set<>String Override function
    public void SetWBinaryField(string value)
    {
        SetWBinaryFieldAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller157()
    {
        return _Filler157;
    }
    
    // Standard Setter
    public void SetFiller157(string value)
    {
        _Filler157 = value;
    }
    
    // Get<>AsString()
    public string GetFiller157AsString()
    {
        return _Filler157.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller157AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler157 = value;
    }
    
    // Standard Getter
    public string GetWBinaryChar()
    {
        return _WBinaryChar;
    }
    
    // Standard Setter
    public void SetWBinaryChar(string value)
    {
        _WBinaryChar = value;
    }
    
    // Get<>AsString()
    public string GetWBinaryCharAsString()
    {
        return _WBinaryChar.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWBinaryCharAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WBinaryChar = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}