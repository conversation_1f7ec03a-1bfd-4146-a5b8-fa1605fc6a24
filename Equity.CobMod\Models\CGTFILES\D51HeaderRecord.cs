using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D51HeaderRecord Data Structure

public class D51HeaderRecord
{
    private static int _size = 82;
    // [DEBUG] Class: D51HeaderRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler75, is_external=, is_static_class=False, static_prefix=
    private string _Filler75 ="";
    
    
    // 88-level condition checks for Filler75
    public bool IsHeaderFound()
    {
        if (this._Filler75 == "'0000000MICRO CGT STOCK HEADER'") return true;
        return false;
    }
    
    
    
    // Serialization methods
    public string GetD51HeaderRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler75.PadRight(82));
        
        return result.ToString();
    }
    
    public void SetD51HeaderRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 82 <= data.Length)
        {
            string extracted = data.Substring(offset, 82).Trim();
            SetFiller75(extracted);
        }
        offset += 82;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD51HeaderRecordAsString();
    }
    // Set<>String Override function
    public void SetD51HeaderRecord(string value)
    {
        SetD51HeaderRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller75()
    {
        return _Filler75;
    }
    
    // Standard Setter
    public void SetFiller75(string value)
    {
        _Filler75 = value;
    }
    
    // Get<>AsString()
    public string GetFiller75AsString()
    {
        return _Filler75.PadRight(82);
    }
    
    // Set<>AsString()
    public void SetFiller75AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler75 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}