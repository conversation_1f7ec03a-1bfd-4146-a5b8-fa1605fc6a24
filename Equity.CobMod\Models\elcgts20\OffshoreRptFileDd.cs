using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing OffshoreRptFileDd Data Structure

public class OffshoreRptFileDd
{
    private static int _size = 12;
    // [DEBUG] Class: OffshoreRptFileDd, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: OffshUserNoDd, is_external=, is_static_class=False, static_prefix=
    private string _OffshUserNoDd ="";
    
    
    
    
    // [DEBUG] Field: Filler104, is_external=, is_static_class=False, static_prefix=
    private string _Filler104 ="KR";
    
    
    
    
    // [DEBUG] Field: OffshReportNoDd, is_external=, is_static_class=False, static_prefix=
    private int _OffshReportNoDd =0;
    
    
    
    
    // [DEBUG] Field: Filler105, is_external=, is_static_class=False, static_prefix=
    private string _Filler105 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetOffshoreRptFileDdAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_OffshUserNoDd.PadRight(5));
        result.Append(_Filler104.PadRight(2));
        result.Append(_OffshReportNoDd.ToString().PadLeft(1, '0'));
        result.Append(_Filler105.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetOffshoreRptFileDdAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            SetOffshUserNoDd(extracted);
        }
        offset += 5;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller104(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetOffshReportNoDd(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller105(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetOffshoreRptFileDdAsString();
    }
    // Set<>String Override function
    public void SetOffshoreRptFileDd(string value)
    {
        SetOffshoreRptFileDdAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetOffshUserNoDd()
    {
        return _OffshUserNoDd;
    }
    
    // Standard Setter
    public void SetOffshUserNoDd(string value)
    {
        _OffshUserNoDd = value;
    }
    
    // Get<>AsString()
    public string GetOffshUserNoDdAsString()
    {
        return _OffshUserNoDd.PadRight(5);
    }
    
    // Set<>AsString()
    public void SetOffshUserNoDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _OffshUserNoDd = value;
    }
    
    // Standard Getter
    public string GetFiller104()
    {
        return _Filler104;
    }
    
    // Standard Setter
    public void SetFiller104(string value)
    {
        _Filler104 = value;
    }
    
    // Get<>AsString()
    public string GetFiller104AsString()
    {
        return _Filler104.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller104AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler104 = value;
    }
    
    // Standard Getter
    public int GetOffshReportNoDd()
    {
        return _OffshReportNoDd;
    }
    
    // Standard Setter
    public void SetOffshReportNoDd(int value)
    {
        _OffshReportNoDd = value;
    }
    
    // Get<>AsString()
    public string GetOffshReportNoDdAsString()
    {
        return _OffshReportNoDd.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetOffshReportNoDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _OffshReportNoDd = parsed;
    }
    
    // Standard Getter
    public string GetFiller105()
    {
        return _Filler105;
    }
    
    // Standard Setter
    public void SetFiller105(string value)
    {
        _Filler105 = value;
    }
    
    // Get<>AsString()
    public string GetFiller105AsString()
    {
        return _Filler105.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller105AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler105 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
