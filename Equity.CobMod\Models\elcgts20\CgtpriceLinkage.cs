using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing CgtpriceLinkage Data Structure

public class CgtpriceLinkage
{
    private static int _size = 30;
    // [DEBUG] Class: CgtpriceLinkage, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: CgtpriceParametersIn, is_external=, is_static_class=False, static_prefix=
    private CgtpriceParametersIn _CgtpriceParametersIn = new CgtpriceParametersIn();
    
    
    
    
    // [DEBUG] Field: CgtpriceParametersOut, is_external=, is_static_class=False, static_prefix=
    private CgtpriceParametersOut _CgtpriceParametersOut = new CgtpriceParametersOut();
    
    
    
    
    
    // Serialization methods
    public string GetCgtpriceLinkageAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_CgtpriceParametersIn.GetCgtpriceParametersInAsString());
        result.Append(_CgtpriceParametersOut.GetCgtpriceParametersOutAsString());
        
        return result.ToString();
    }
    
    public void SetCgtpriceLinkageAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            _CgtpriceParametersIn.SetCgtpriceParametersInAsString(data.Substring(offset, 0));
        }
        else
        {
            _CgtpriceParametersIn.SetCgtpriceParametersInAsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 30 <= data.Length)
        {
            _CgtpriceParametersOut.SetCgtpriceParametersOutAsString(data.Substring(offset, 30));
        }
        else
        {
            _CgtpriceParametersOut.SetCgtpriceParametersOutAsString(data.Substring(offset));
        }
        offset += 30;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtpriceLinkageAsString();
    }
    // Set<>String Override function
    public void SetCgtpriceLinkage(string value)
    {
        SetCgtpriceLinkageAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public CgtpriceParametersIn GetCgtpriceParametersIn()
    {
        return _CgtpriceParametersIn;
    }
    
    // Standard Setter
    public void SetCgtpriceParametersIn(CgtpriceParametersIn value)
    {
        _CgtpriceParametersIn = value;
    }
    
    // Get<>AsString()
    public string GetCgtpriceParametersInAsString()
    {
        return _CgtpriceParametersIn != null ? _CgtpriceParametersIn.GetCgtpriceParametersInAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtpriceParametersInAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CgtpriceParametersIn == null)
        {
            _CgtpriceParametersIn = new CgtpriceParametersIn();
        }
        _CgtpriceParametersIn.SetCgtpriceParametersInAsString(value);
    }
    
    // Standard Getter
    public CgtpriceParametersOut GetCgtpriceParametersOut()
    {
        return _CgtpriceParametersOut;
    }
    
    // Standard Setter
    public void SetCgtpriceParametersOut(CgtpriceParametersOut value)
    {
        _CgtpriceParametersOut = value;
    }
    
    // Get<>AsString()
    public string GetCgtpriceParametersOutAsString()
    {
        return _CgtpriceParametersOut != null ? _CgtpriceParametersOut.GetCgtpriceParametersOutAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtpriceParametersOutAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CgtpriceParametersOut == null)
        {
            _CgtpriceParametersOut = new CgtpriceParametersOut();
        }
        _CgtpriceParametersOut.SetCgtpriceParametersOutAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetCgtpriceParametersIn(string value)
    {
        _CgtpriceParametersIn.SetCgtpriceParametersInAsString(value);
    }
    // Nested Class: CgtpriceParametersIn
    public class CgtpriceParametersIn
    {
        private static int _size = 0;
        
        // Fields in the class
        
        
        // [DEBUG] Field: CgtpriceStockId, is_external=, is_static_class=False, static_prefix=
        private string _CgtpriceStockId ="";
        
        
        
        
        // [DEBUG] Field: CgtpricePriceTypeId, is_external=, is_static_class=False, static_prefix=
        private string _CgtpricePriceTypeId ="";
        
        
        
        
        // [DEBUG] Field: CgtpricePriceDate, is_external=, is_static_class=False, static_prefix=
        private string _CgtpricePriceDate ="";
        
        
        
        
        // [DEBUG] Field: CgtpriceUseEarlierPrice, is_external=, is_static_class=False, static_prefix=
        private string _CgtpriceUseEarlierPrice ="";
        
        
        
        
        // [DEBUG] Field: CgtpriceAction, is_external=, is_static_class=False, static_prefix=
        private string _CgtpriceAction ="";
        
        
        // 88-level condition checks for CgtpriceAction
        public bool IsCgtpriceActionInitialise()
        {
            if (this._CgtpriceAction == "'I'") return true;
            return false;
        }
        public bool IsCgtpriceActionGetPrice()
        {
            if (this._CgtpriceAction == "'P'") return true;
            return false;
        }
        public bool IsCgtpriceActionTerminate()
        {
            if (this._CgtpriceAction == "'T'") return true;
            return false;
        }
        
        
    public CgtpriceParametersIn() {}
    
    public CgtpriceParametersIn(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtpriceStockId(data.Substring(offset, 0).Trim());
        offset += 0;
        SetCgtpricePriceTypeId(data.Substring(offset, 0).Trim());
        offset += 0;
        SetCgtpricePriceDate(data.Substring(offset, 0).Trim());
        offset += 0;
        SetCgtpriceUseEarlierPrice(data.Substring(offset, 0).Trim());
        offset += 0;
        SetCgtpriceAction(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetCgtpriceParametersInAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_CgtpriceStockId.PadRight(0));
        result.Append(_CgtpricePriceTypeId.PadRight(0));
        result.Append(_CgtpricePriceDate.PadRight(0));
        result.Append(_CgtpriceUseEarlierPrice.PadRight(0));
        result.Append(_CgtpriceAction.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetCgtpriceParametersInAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCgtpriceStockId(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCgtpricePriceTypeId(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCgtpricePriceDate(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCgtpriceUseEarlierPrice(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCgtpriceAction(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtpriceStockId()
    {
        return _CgtpriceStockId;
    }
    
    // Standard Setter
    public void SetCgtpriceStockId(string value)
    {
        _CgtpriceStockId = value;
    }
    
    // Get<>AsString()
    public string GetCgtpriceStockIdAsString()
    {
        return _CgtpriceStockId.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCgtpriceStockIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtpriceStockId = value;
    }
    
    // Standard Getter
    public string GetCgtpricePriceTypeId()
    {
        return _CgtpricePriceTypeId;
    }
    
    // Standard Setter
    public void SetCgtpricePriceTypeId(string value)
    {
        _CgtpricePriceTypeId = value;
    }
    
    // Get<>AsString()
    public string GetCgtpricePriceTypeIdAsString()
    {
        return _CgtpricePriceTypeId.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCgtpricePriceTypeIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtpricePriceTypeId = value;
    }
    
    // Standard Getter
    public string GetCgtpricePriceDate()
    {
        return _CgtpricePriceDate;
    }
    
    // Standard Setter
    public void SetCgtpricePriceDate(string value)
    {
        _CgtpricePriceDate = value;
    }
    
    // Get<>AsString()
    public string GetCgtpricePriceDateAsString()
    {
        return _CgtpricePriceDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCgtpricePriceDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtpricePriceDate = value;
    }
    
    // Standard Getter
    public string GetCgtpriceUseEarlierPrice()
    {
        return _CgtpriceUseEarlierPrice;
    }
    
    // Standard Setter
    public void SetCgtpriceUseEarlierPrice(string value)
    {
        _CgtpriceUseEarlierPrice = value;
    }
    
    // Get<>AsString()
    public string GetCgtpriceUseEarlierPriceAsString()
    {
        return _CgtpriceUseEarlierPrice.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCgtpriceUseEarlierPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtpriceUseEarlierPrice = value;
    }
    
    // Standard Getter
    public string GetCgtpriceAction()
    {
        return _CgtpriceAction;
    }
    
    // Standard Setter
    public void SetCgtpriceAction(string value)
    {
        _CgtpriceAction = value;
    }
    
    // Get<>AsString()
    public string GetCgtpriceActionAsString()
    {
        return _CgtpriceAction.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCgtpriceActionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtpriceAction = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetCgtpriceParametersOut(string value)
{
    _CgtpriceParametersOut.SetCgtpriceParametersOutAsString(value);
}
// Nested Class: CgtpriceParametersOut
public class CgtpriceParametersOut
{
    private static int _size = 30;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtpriceMarketPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _CgtpriceMarketPrice =0;
    
    
    
    
    // [DEBUG] Field: CgtpriceMarketPriceX, is_external=, is_static_class=False, static_prefix=
    private string _CgtpriceMarketPriceX ="";
    
    
    
    
    // [DEBUG] Field: CgtpriceReturnCode, is_external=, is_static_class=False, static_prefix=
    private string _CgtpriceReturnCode ="";
    
    
    // 88-level condition checks for CgtpriceReturnCode
    public bool IsPPriceFound()
    {
        if (!string.IsNullOrEmpty(this.GetCgtpriceReturnCode()) && this.GetCgtpriceReturnCode().Length == 1)
        {
            var ch = this.GetCgtpriceReturnCode()[0];
            if (ch >= '0' && ch <= '2') return true;
        }
        return false;
    }
    public bool IsPSameDayPriceFound()
    {
        if (this._CgtpriceReturnCode == "'0'") return true;
        return false;
    }
    public bool IsPPrevDayPriceFound()
    {
        if (this._CgtpriceReturnCode == "'1'") return true;
        return false;
    }
    public bool IsPPrevSessionPriceFound()
    {
        if (this._CgtpriceReturnCode == "'2'") return true;
        return false;
    }
    public bool IsPPriceNotFound()
    {
        if (!string.IsNullOrEmpty(this.GetCgtpriceReturnCode()) && this.GetCgtpriceReturnCode().Length == 1)
        {
            var ch = this.GetCgtpriceReturnCode()[0];
            if (ch >= '3' && ch <= '7') return true;
        }
        return false;
    }
    public bool IsPSameDayPriceNotFound()
    {
        if (this._CgtpriceReturnCode == "'3'") return true;
        return false;
    }
    public bool IsPPrevDayPriceNotFound()
    {
        if (this._CgtpriceReturnCode == "'4'") return true;
        return false;
    }
    public bool IsPPrevSessionPriceNotFound()
    {
        if (this._CgtpriceReturnCode == "'5'") return true;
        return false;
    }
    public bool IsPFundRecordNotFound()
    {
        if (this._CgtpriceReturnCode == "'6'") return true;
        return false;
    }
    public bool IsPPriceTypeRecordNotFound()
    {
        if (this._CgtpriceReturnCode == "'7'") return true;
        return false;
    }
    public bool IsPInitialisationSuccessful()
    {
        if (this._CgtpriceReturnCode == "'A'") return true;
        return false;
    }
    public bool IsPInitialisationFailed()
    {
        if (this._CgtpriceReturnCode == "'B'") return true;
        return false;
    }
    
    
public CgtpriceParametersOut() {}

public CgtpriceParametersOut(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtpriceMarketPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetCgtpriceMarketPriceX(data.Substring(offset, 15).Trim());
    offset += 15;
    SetCgtpriceReturnCode(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetCgtpriceParametersOutAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtpriceMarketPrice.ToString("F7", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_CgtpriceMarketPriceX.PadRight(15));
    result.Append(_CgtpriceReturnCode.PadRight(0));
    
    return result.ToString();
}

public void SetCgtpriceParametersOutAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetCgtpriceMarketPrice(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetCgtpriceMarketPriceX(extracted);
    }
    offset += 15;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetCgtpriceReturnCode(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public decimal GetCgtpriceMarketPrice()
{
    return _CgtpriceMarketPrice;
}

// Standard Setter
public void SetCgtpriceMarketPrice(decimal value)
{
    _CgtpriceMarketPrice = value;
}

// Get<>AsString()
public string GetCgtpriceMarketPriceAsString()
{
    return _CgtpriceMarketPrice.ToString("F7", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetCgtpriceMarketPriceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _CgtpriceMarketPrice = parsed;
}

// Standard Getter
public string GetCgtpriceMarketPriceX()
{
    return _CgtpriceMarketPriceX;
}

// Standard Setter
public void SetCgtpriceMarketPriceX(string value)
{
    _CgtpriceMarketPriceX = value;
}

// Get<>AsString()
public string GetCgtpriceMarketPriceXAsString()
{
    return _CgtpriceMarketPriceX.PadRight(15);
}

// Set<>AsString()
public void SetCgtpriceMarketPriceXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtpriceMarketPriceX = value;
}

// Standard Getter
public string GetCgtpriceReturnCode()
{
    return _CgtpriceReturnCode;
}

// Standard Setter
public void SetCgtpriceReturnCode(string value)
{
    _CgtpriceReturnCode = value;
}

// Get<>AsString()
public string GetCgtpriceReturnCodeAsString()
{
    return _CgtpriceReturnCode.PadRight(0);
}

// Set<>AsString()
public void SetCgtpriceReturnCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtpriceReturnCode = value;
}



public static int GetSize()
{
    return _size;
}

}

}}
