using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D94File Data Structure

public class D94File
{
    private static int _size = 12;
    // [DEBUG] Class: D94File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler273, is_external=, is_static_class=False, static_prefix=
    private string _Filler273 ="$";
    
    
    
    
    // [DEBUG] Field: D94UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D94UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler274, is_external=, is_static_class=False, static_prefix=
    private string _Filler274 ="DL";
    
    
    
    
    // [DEBUG] Field: D94ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D94ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler275, is_external=, is_static_class=False, static_prefix=
    private string _Filler275 =".DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD94FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler273.PadRight(1));
        result.Append(_D94UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler274.PadRight(2));
        result.Append(_D94ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler275.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD94FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller273(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD94UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller274(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD94ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller275(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD94FileAsString();
    }
    // Set<>String Override function
    public void SetD94File(string value)
    {
        SetD94FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller273()
    {
        return _Filler273;
    }
    
    // Standard Setter
    public void SetFiller273(string value)
    {
        _Filler273 = value;
    }
    
    // Get<>AsString()
    public string GetFiller273AsString()
    {
        return _Filler273.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller273AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler273 = value;
    }
    
    // Standard Getter
    public int GetD94UserNo()
    {
        return _D94UserNo;
    }
    
    // Standard Setter
    public void SetD94UserNo(int value)
    {
        _D94UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD94UserNoAsString()
    {
        return _D94UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD94UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D94UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller274()
    {
        return _Filler274;
    }
    
    // Standard Setter
    public void SetFiller274(string value)
    {
        _Filler274 = value;
    }
    
    // Get<>AsString()
    public string GetFiller274AsString()
    {
        return _Filler274.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller274AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler274 = value;
    }
    
    // Standard Getter
    public int GetD94ReportNo()
    {
        return _D94ReportNo;
    }
    
    // Standard Setter
    public void SetD94ReportNo(int value)
    {
        _D94ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD94ReportNoAsString()
    {
        return _D94ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD94ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D94ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller275()
    {
        return _Filler275;
    }
    
    // Standard Setter
    public void SetFiller275(string value)
    {
        _Filler275 = value;
    }
    
    // Get<>AsString()
    public string GetFiller275AsString()
    {
        return _Filler275.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller275AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler275 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}