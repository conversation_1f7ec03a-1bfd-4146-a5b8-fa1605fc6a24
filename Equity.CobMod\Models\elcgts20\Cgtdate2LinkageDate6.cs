using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing Cgtdate2LinkageDate6 Data Structure

public class Cgtdate2LinkageDate6
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate6, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd6 =0;
    
    
    
    
    // [DEBUG] Field: Filler35, is_external=, is_static_class=False, static_prefix=
    private Filler35 _Filler35 = new Filler35();
    
    
    
    
    // [DEBUG] Field: Filler36, is_external=, is_static_class=False, static_prefix=
    private Filler36 _Filler36 = new Filler36();
    
    
    
    
    // [DEBUG] Field: Filler37, is_external=, is_static_class=False, static_prefix=
    private Filler37 _Filler37 = new Filler37();
    
    
    
    
    // [DEBUG] Field: Filler38, is_external=, is_static_class=False, static_prefix=
    private Filler38 _Filler38 = new Filler38();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate6AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd6.ToString().PadLeft(8, '0'));
        result.Append(_Filler35.GetFiller35AsString());
        result.Append(_Filler36.GetFiller36AsString());
        result.Append(_Filler37.GetFiller37AsString());
        result.Append(_Filler38.GetFiller38AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate6AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd6(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler35.SetFiller35AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler35.SetFiller35AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler36.SetFiller36AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler36.SetFiller36AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler37.SetFiller37AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler37.SetFiller37AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler38.SetFiller38AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler38.SetFiller38AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate6AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate6(string value)
    {
        SetCgtdate2LinkageDate6AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd6()
    {
        return _Cgtdate2Ccyymmdd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd6(int value)
    {
        _Cgtdate2Ccyymmdd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd6AsString()
    {
        return _Cgtdate2Ccyymmdd6.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd6 = parsed;
    }
    
    // Standard Getter
    public Filler35 GetFiller35()
    {
        return _Filler35;
    }
    
    // Standard Setter
    public void SetFiller35(Filler35 value)
    {
        _Filler35 = value;
    }
    
    // Get<>AsString()
    public string GetFiller35AsString()
    {
        return _Filler35 != null ? _Filler35.GetFiller35AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller35AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler35 == null)
        {
            _Filler35 = new Filler35();
        }
        _Filler35.SetFiller35AsString(value);
    }
    
    // Standard Getter
    public Filler36 GetFiller36()
    {
        return _Filler36;
    }
    
    // Standard Setter
    public void SetFiller36(Filler36 value)
    {
        _Filler36 = value;
    }
    
    // Get<>AsString()
    public string GetFiller36AsString()
    {
        return _Filler36 != null ? _Filler36.GetFiller36AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller36AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler36 == null)
        {
            _Filler36 = new Filler36();
        }
        _Filler36.SetFiller36AsString(value);
    }
    
    // Standard Getter
    public Filler37 GetFiller37()
    {
        return _Filler37;
    }
    
    // Standard Setter
    public void SetFiller37(Filler37 value)
    {
        _Filler37 = value;
    }
    
    // Get<>AsString()
    public string GetFiller37AsString()
    {
        return _Filler37 != null ? _Filler37.GetFiller37AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller37AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler37 == null)
        {
            _Filler37 = new Filler37();
        }
        _Filler37.SetFiller37AsString(value);
    }
    
    // Standard Getter
    public Filler38 GetFiller38()
    {
        return _Filler38;
    }
    
    // Standard Setter
    public void SetFiller38(Filler38 value)
    {
        _Filler38 = value;
    }
    
    // Get<>AsString()
    public string GetFiller38AsString()
    {
        return _Filler38 != null ? _Filler38.GetFiller38AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller38AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler38 == null)
        {
            _Filler38 = new Filler38();
        }
        _Filler38.SetFiller38AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller35(string value)
    {
        _Filler35.SetFiller35AsString(value);
    }
    // Nested Class: Filler35
    public class Filler35
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc6 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd6, is_external=, is_static_class=False, static_prefix=
        private Filler35.Cgtdate2Yymmdd6 _Cgtdate2Yymmdd6 = new Filler35.Cgtdate2Yymmdd6();
        
        
        
        
    public Filler35() {}
    
    public Filler35(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc6(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(data.Substring(offset, Cgtdate2Yymmdd6.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller35AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc6.PadRight(2));
        result.Append(_Cgtdate2Yymmdd6.GetCgtdate2Yymmdd6AsString());
        
        return result.ToString();
    }
    
    public void SetFiller35AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc6(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc6()
    {
        return _Cgtdate2Cc6;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc6(string value)
    {
        _Cgtdate2Cc6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc6AsString()
    {
        return _Cgtdate2Cc6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc6 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd6 GetCgtdate2Yymmdd6()
    {
        return _Cgtdate2Yymmdd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd6(Cgtdate2Yymmdd6 value)
    {
        _Cgtdate2Yymmdd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd6AsString()
    {
        return _Cgtdate2Yymmdd6 != null ? _Cgtdate2Yymmdd6.GetCgtdate2Yymmdd6AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd6 == null)
        {
            _Cgtdate2Yymmdd6 = new Cgtdate2Yymmdd6();
        }
        _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd6
    public class Cgtdate2Yymmdd6
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy6 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd6, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd6.Cgtdate2Mmdd6 _Cgtdate2Mmdd6 = new Cgtdate2Yymmdd6.Cgtdate2Mmdd6();
        
        
        
        
    public Cgtdate2Yymmdd6() {}
    
    public Cgtdate2Yymmdd6(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy6(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(data.Substring(offset, Cgtdate2Mmdd6.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd6AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy6.PadRight(2));
        result.Append(_Cgtdate2Mmdd6.GetCgtdate2Mmdd6AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd6AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy6(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy6()
    {
        return _Cgtdate2Yy6;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy6(string value)
    {
        _Cgtdate2Yy6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy6AsString()
    {
        return _Cgtdate2Yy6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy6 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd6 GetCgtdate2Mmdd6()
    {
        return _Cgtdate2Mmdd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd6(Cgtdate2Mmdd6 value)
    {
        _Cgtdate2Mmdd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd6AsString()
    {
        return _Cgtdate2Mmdd6 != null ? _Cgtdate2Mmdd6.GetCgtdate2Mmdd6AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd6 == null)
        {
            _Cgtdate2Mmdd6 = new Cgtdate2Mmdd6();
        }
        _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd6
    public class Cgtdate2Mmdd6
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm6 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd6 ="";
        
        
        
        
    public Cgtdate2Mmdd6() {}
    
    public Cgtdate2Mmdd6(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm6(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd6(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd6AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm6.PadRight(2));
        result.Append(_Cgtdate2Dd6.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd6AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm6(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd6(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm6()
    {
        return _Cgtdate2Mm6;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm6(string value)
    {
        _Cgtdate2Mm6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm6AsString()
    {
        return _Cgtdate2Mm6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm6 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd6()
    {
        return _Cgtdate2Dd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd6(string value)
    {
        _Cgtdate2Dd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd6AsString()
    {
        return _Cgtdate2Dd6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd6 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller36(string value)
{
    _Filler36.SetFiller36AsString(value);
}
// Nested Class: Filler36
public class Filler36
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd6 =0;
    
    
    
    
public Filler36() {}

public Filler36(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy6(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd6(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller36AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy6.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd6.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller36AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy6(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd6(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy6()
{
    return _Cgtdate2CCcyy6;
}

// Standard Setter
public void SetCgtdate2CCcyy6(int value)
{
    _Cgtdate2CCcyy6 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy6AsString()
{
    return _Cgtdate2CCcyy6.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy6 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd6()
{
    return _Cgtdate2CMmdd6;
}

// Standard Setter
public void SetCgtdate2CMmdd6(int value)
{
    _Cgtdate2CMmdd6 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd6AsString()
{
    return _Cgtdate2CMmdd6.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd6 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller37(string value)
{
    _Filler37.SetFiller37AsString(value);
}
// Nested Class: Filler37
public class Filler37
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd6 =0;
    
    
    
    
public Filler37() {}

public Filler37(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller37AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc6.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy6.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm6.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd6.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller37AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc6(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy6(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm6(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd6(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc6()
{
    return _Cgtdate2CCc6;
}

// Standard Setter
public void SetCgtdate2CCc6(int value)
{
    _Cgtdate2CCc6 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc6AsString()
{
    return _Cgtdate2CCc6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc6 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy6()
{
    return _Cgtdate2CYy6;
}

// Standard Setter
public void SetCgtdate2CYy6(int value)
{
    _Cgtdate2CYy6 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy6AsString()
{
    return _Cgtdate2CYy6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy6 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm6()
{
    return _Cgtdate2CMm6;
}

// Standard Setter
public void SetCgtdate2CMm6(int value)
{
    _Cgtdate2CMm6 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm6AsString()
{
    return _Cgtdate2CMm6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm6 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd6()
{
    return _Cgtdate2CDd6;
}

// Standard Setter
public void SetCgtdate2CDd6(int value)
{
    _Cgtdate2CDd6 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd6AsString()
{
    return _Cgtdate2CDd6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd6 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller38(string value)
{
    _Filler38.SetFiller38AsString(value);
}
// Nested Class: Filler38
public class Filler38
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm6 =0;
    
    
    
    
    // [DEBUG] Field: Filler39, is_external=, is_static_class=False, static_prefix=
    private string _Filler39 ="";
    
    
    
    
public Filler38() {}

public Filler38(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm6(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller39(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller38AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm6.ToString().PadLeft(6, '0'));
    result.Append(_Filler39.PadRight(2));
    
    return result.ToString();
}

public void SetFiller38AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm6(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller39(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm6()
{
    return _Cgtdate2CCcyymm6;
}

// Standard Setter
public void SetCgtdate2CCcyymm6(int value)
{
    _Cgtdate2CCcyymm6 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm6AsString()
{
    return _Cgtdate2CCcyymm6.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm6 = parsed;
}

// Standard Getter
public string GetFiller39()
{
    return _Filler39;
}

// Standard Setter
public void SetFiller39(string value)
{
    _Filler39 = value;
}

// Get<>AsString()
public string GetFiller39AsString()
{
    return _Filler39.PadRight(2);
}

// Set<>AsString()
public void SetFiller39AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler39 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}
