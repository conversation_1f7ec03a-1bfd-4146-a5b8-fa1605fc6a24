using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt179HsDTO
{// DTO class representing ReportFile Data Structure

public class ReportFile
{
    private static int _size = 12;
    // [DEBUG] Class: ReportFile, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler15, is_external=, is_static_class=False, static_prefix=
    private string _Filler15 ="$";
    
    
    
    
    // [DEBUG] Field: ReportUserNo, is_external=, is_static_class=False, static_prefix=
    private string _ReportUserNo ="9999";
    
    
    
    
    // [DEBUG] Field: Filler16, is_external=, is_static_class=False, static_prefix=
    private string _Filler16 ="FG";
    
    
    
    
    // [DEBUG] Field: ReportGenNo, is_external=, is_static_class=False, static_prefix=
    private string _ReportGenNo ="G";
    
    
    
    
    // [DEBUG] Field: Filler17, is_external=, is_static_class=False, static_prefix=
    private string _Filler17 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetReportFileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler15.PadRight(1));
        result.Append(_ReportUserNo.PadRight(4));
        result.Append(_Filler16.PadRight(2));
        result.Append(_ReportGenNo.PadRight(1));
        result.Append(_Filler17.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetReportFileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller15(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetReportUserNo(extracted);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller16(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetReportGenNo(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller17(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportFileAsString();
    }
    // Set<>String Override function
    public void SetReportFile(string value)
    {
        SetReportFileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller15()
    {
        return _Filler15;
    }
    
    // Standard Setter
    public void SetFiller15(string value)
    {
        _Filler15 = value;
    }
    
    // Get<>AsString()
    public string GetFiller15AsString()
    {
        return _Filler15.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller15AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler15 = value;
    }
    
    // Standard Getter
    public string GetReportUserNo()
    {
        return _ReportUserNo;
    }
    
    // Standard Setter
    public void SetReportUserNo(string value)
    {
        _ReportUserNo = value;
    }
    
    // Get<>AsString()
    public string GetReportUserNoAsString()
    {
        return _ReportUserNo.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetReportUserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportUserNo = value;
    }
    
    // Standard Getter
    public string GetFiller16()
    {
        return _Filler16;
    }
    
    // Standard Setter
    public void SetFiller16(string value)
    {
        _Filler16 = value;
    }
    
    // Get<>AsString()
    public string GetFiller16AsString()
    {
        return _Filler16.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller16AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler16 = value;
    }
    
    // Standard Getter
    public string GetReportGenNo()
    {
        return _ReportGenNo;
    }
    
    // Standard Setter
    public void SetReportGenNo(string value)
    {
        _ReportGenNo = value;
    }
    
    // Get<>AsString()
    public string GetReportGenNoAsString()
    {
        return _ReportGenNo.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetReportGenNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportGenNo = value;
    }
    
    // Standard Getter
    public string GetFiller17()
    {
        return _Filler17;
    }
    
    // Standard Setter
    public void SetFiller17(string value)
    {
        _Filler17 = value;
    }
    
    // Get<>AsString()
    public string GetFiller17AsString()
    {
        return _Filler17.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller17AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler17 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}