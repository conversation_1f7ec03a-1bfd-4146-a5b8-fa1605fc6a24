using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D83TrailerRecord Data Structure

public class D83TrailerRecord
{
    private static int _size = 137;
    // [DEBUG] Class: D83TrailerRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler121, is_external=, is_static_class=False, static_prefix=
    private string _Filler121 ="";
    
    
    // 88-level condition checks for Filler121
    public bool IsD83TrailerFound()
    {
        if (this._Filler121 == "'99999999999MICRO CGT BALANCES TRAILER   '") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D83TrailerCount, is_external=, is_static_class=False, static_prefix=
    private int _D83TrailerCount =0;
    
    
    
    
    // [DEBUG] Field: Filler122, is_external=, is_static_class=False, static_prefix=
    private string _Filler122 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD83TrailerRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler121.PadRight(40));
        result.Append(_D83TrailerCount.ToString().PadLeft(6, '0'));
        result.Append(_Filler122.PadRight(91));
        
        return result.ToString();
    }
    
    public void SetD83TrailerRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller121(extracted);
        }
        offset += 40;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD83TrailerCount(parsedInt);
        }
        offset += 6;
        if (offset + 91 <= data.Length)
        {
            string extracted = data.Substring(offset, 91).Trim();
            SetFiller122(extracted);
        }
        offset += 91;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD83TrailerRecordAsString();
    }
    // Set<>String Override function
    public void SetD83TrailerRecord(string value)
    {
        SetD83TrailerRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller121()
    {
        return _Filler121;
    }
    
    // Standard Setter
    public void SetFiller121(string value)
    {
        _Filler121 = value;
    }
    
    // Get<>AsString()
    public string GetFiller121AsString()
    {
        return _Filler121.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller121AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler121 = value;
    }
    
    // Standard Getter
    public int GetD83TrailerCount()
    {
        return _D83TrailerCount;
    }
    
    // Standard Setter
    public void SetD83TrailerCount(int value)
    {
        _D83TrailerCount = value;
    }
    
    // Get<>AsString()
    public string GetD83TrailerCountAsString()
    {
        return _D83TrailerCount.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD83TrailerCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D83TrailerCount = parsed;
    }
    
    // Standard Getter
    public string GetFiller122()
    {
        return _Filler122;
    }
    
    // Standard Setter
    public void SetFiller122(string value)
    {
        _Filler122 = value;
    }
    
    // Get<>AsString()
    public string GetFiller122AsString()
    {
        return _Filler122.PadRight(91);
    }
    
    // Set<>AsString()
    public void SetFiller122AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler122 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}