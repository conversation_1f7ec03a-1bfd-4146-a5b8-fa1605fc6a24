using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing Filler334 Data Structure

public class Filler334
{
    private static int _size = 1938;
    // [DEBUG] Class: Filler334, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: SSchedRecElement, is_external=, is_static_class=False, static_prefix=
    private SSchedRecElement[] _SSchedRecElement = new SSchedRecElement[6];
    
    public void InitializeSSchedRecElementArray()
    {
        for (int i = 0; i < 6; i++)
        {
            _SSchedRecElement[i] = new SSchedRecElement();
        }
    }
    
    
    
    
    // Serialization methods
    public string GetFiller334AsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 6; i++)
        {
            result.Append(_SSchedRecElement[i].GetSSchedRecElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetFiller334AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 6; i++)
        {
            if (offset + 323 > data.Length) break;
            string val = data.Substring(offset, 323);
            
            _SSchedRecElement[i].SetSSchedRecElementAsString(val);
            offset += 323;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetFiller334AsString();
    }
    // Set<>String Override function
    public void SetFiller334(string value)
    {
        SetFiller334AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Array Accessors for SSchedRecElement
    public SSchedRecElement GetSSchedRecElementAt(int index)
    {
        return _SSchedRecElement[index];
    }
    
    public void SetSSchedRecElementAt(int index, SSchedRecElement value)
    {
        _SSchedRecElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public SSchedRecElement GetSSchedRecElement()
    {
        return _SSchedRecElement != null && _SSchedRecElement.Length > 0
        ? _SSchedRecElement[0]
        : new SSchedRecElement();
    }
    
    public void SetSSchedRecElement(SSchedRecElement value)
    {
        if (_SSchedRecElement == null || _SSchedRecElement.Length == 0)
        _SSchedRecElement = new SSchedRecElement[1];
        _SSchedRecElement[0] = value;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetSSchedRecElement(string value)
    {
        if (!string.IsNullOrEmpty(value) && value.Length < SSchedRecElement.GetSize() * _SSchedRecElement.Length)
        {
            value = value.PadRight(SSchedRecElement.GetSize() * _SSchedRecElement.Length);
        }
        
        int offset = 0;
        for (int i = 0; i < _SSchedRecElement.Length; i++)
        {
            if (offset + SSchedRecElement.GetSize() > value.Length) break;
            string chunk = value.Substring(offset, SSchedRecElement.GetSize());
            _SSchedRecElement[i].SetSSchedRecElementAsString(chunk);
            offset += SSchedRecElement.GetSize();
        }
    }
    // Nested Class: SSchedRecElement
    public class SSchedRecElement
    {
        private static int _size = 323;
        
        // Fields in the class
        
        
        // [DEBUG] Field: SRecordCached, is_external=, is_static_class=False, static_prefix=
        private int _SRecordCached =0;
        
        
        // 88-level condition checks for SRecordCached
        public bool IsRecordNotCached()
        {
            if (this._SRecordCached == 0) return true;
            return false;
        }
        public bool IsRecordCached()
        {
            if (this._SRecordCached == 1) return true;
            return false;
        }
        
        
        // [DEBUG] Field: SRecord, is_external=, is_static_class=False, static_prefix=
        private SSchedRecElement.SRecord _SRecord = new SSchedRecElement.SRecord();
        
        
        
        
    public SSchedRecElement() {}
    
    public SSchedRecElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetSRecordCached(int.Parse(data.Substring(offset, 4).Trim()));
        offset += 4;
        _SRecord.SetSRecordAsString(data.Substring(offset, SRecord.GetSize()));
        offset += 319;
        
    }
    
    // Serialization methods
    public string GetSSchedRecElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_SRecordCached.ToString().PadLeft(4, '0'));
        result.Append(_SRecord.GetSRecordAsString());
        
        return result.ToString();
    }
    
    public void SetSSchedRecElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetSRecordCached(parsedInt);
        }
        offset += 4;
        if (offset + 319 <= data.Length)
        {
            _SRecord.SetSRecordAsString(data.Substring(offset, 319));
        }
        else
        {
            _SRecord.SetSRecordAsString(data.Substring(offset));
        }
        offset += 319;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetSRecordCached()
    {
        return _SRecordCached;
    }
    
    // Standard Setter
    public void SetSRecordCached(int value)
    {
        _SRecordCached = value;
    }
    
    // Get<>AsString()
    public string GetSRecordCachedAsString()
    {
        return _SRecordCached.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetSRecordCachedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _SRecordCached = parsed;
    }
    
    // Standard Getter
    public SRecord GetSRecord()
    {
        return _SRecord;
    }
    
    // Standard Setter
    public void SetSRecord(SRecord value)
    {
        _SRecord = value;
    }
    
    // Get<>AsString()
    public string GetSRecordAsString()
    {
        return _SRecord != null ? _SRecord.GetSRecordAsString() : "";
    }
    
    // Set<>AsString()
    public void SetSRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_SRecord == null)
        {
            _SRecord = new SRecord();
        }
        _SRecord.SetSRecordAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: SRecord
    public class SRecord
    {
        private static int _size = 319;
        
        // Fields in the class
        
        
        // [DEBUG] Field: SSort, is_external=, is_static_class=False, static_prefix=
        private SRecord.SSort _SSort = new SRecord.SSort();
        
        
        
        
        // [DEBUG] Field: SRecordType, is_external=, is_static_class=False, static_prefix=
        private string _SRecordType ="";
        
        
        // 88-level condition checks for SRecordType
        public bool IsSSedolRecord()
        {
            if (this._SRecordType == "'1'") return true;
            return false;
        }
        public bool IsSDetailRecord()
        {
            if (this._SRecordType == "'2'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: SData, is_external=, is_static_class=False, static_prefix=
        private SRecord.SData _SData = new SRecord.SData();
        
        
        
        
        // [DEBUG] Field: SSedolData, is_external=, is_static_class=False, static_prefix=
        private SRecord.SSedolData _SSedolData = new SRecord.SSedolData();
        
        
        
        
    public SRecord() {}
    
    public SRecord(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _SSort.SetSSortAsString(data.Substring(offset, SSort.GetSize()));
        offset += 34;
        SetSRecordType(data.Substring(offset, 1).Trim());
        offset += 1;
        _SData.SetSDataAsString(data.Substring(offset, SData.GetSize()));
        offset += 130;
        _SSedolData.SetSSedolDataAsString(data.Substring(offset, SSedolData.GetSize()));
        offset += 154;
        
    }
    
    // Serialization methods
    public string GetSRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_SSort.GetSSortAsString());
        result.Append(_SRecordType.PadRight(1));
        result.Append(_SData.GetSDataAsString());
        result.Append(_SSedolData.GetSSedolDataAsString());
        
        return result.ToString();
    }
    
    public void SetSRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 34 <= data.Length)
        {
            _SSort.SetSSortAsString(data.Substring(offset, 34));
        }
        else
        {
            _SSort.SetSSortAsString(data.Substring(offset));
        }
        offset += 34;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetSRecordType(extracted);
        }
        offset += 1;
        if (offset + 130 <= data.Length)
        {
            _SData.SetSDataAsString(data.Substring(offset, 130));
        }
        else
        {
            _SData.SetSDataAsString(data.Substring(offset));
        }
        offset += 130;
        if (offset + 154 <= data.Length)
        {
            _SSedolData.SetSSedolDataAsString(data.Substring(offset, 154));
        }
        else
        {
            _SSedolData.SetSSedolDataAsString(data.Substring(offset));
        }
        offset += 154;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public SSort GetSSort()
    {
        return _SSort;
    }
    
    // Standard Setter
    public void SetSSort(SSort value)
    {
        _SSort = value;
    }
    
    // Get<>AsString()
    public string GetSSortAsString()
    {
        return _SSort != null ? _SSort.GetSSortAsString() : "";
    }
    
    // Set<>AsString()
    public void SetSSortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_SSort == null)
        {
            _SSort = new SSort();
        }
        _SSort.SetSSortAsString(value);
    }
    
    // Standard Getter
    public string GetSRecordType()
    {
        return _SRecordType;
    }
    
    // Standard Setter
    public void SetSRecordType(string value)
    {
        _SRecordType = value;
    }
    
    // Get<>AsString()
    public string GetSRecordTypeAsString()
    {
        return _SRecordType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetSRecordTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SRecordType = value;
    }
    
    // Standard Getter
    public SData GetSData()
    {
        return _SData;
    }
    
    // Standard Setter
    public void SetSData(SData value)
    {
        _SData = value;
    }
    
    // Get<>AsString()
    public string GetSDataAsString()
    {
        return _SData != null ? _SData.GetSDataAsString() : "";
    }
    
    // Set<>AsString()
    public void SetSDataAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_SData == null)
        {
            _SData = new SData();
        }
        _SData.SetSDataAsString(value);
    }
    
    // Standard Getter
    public SSedolData GetSSedolData()
    {
        return _SSedolData;
    }
    
    // Standard Setter
    public void SetSSedolData(SSedolData value)
    {
        _SSedolData = value;
    }
    
    // Get<>AsString()
    public string GetSSedolDataAsString()
    {
        return _SSedolData != null ? _SSedolData.GetSSedolDataAsString() : "";
    }
    
    // Set<>AsString()
    public void SetSSedolDataAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_SSedolData == null)
        {
            _SSedolData = new SSedolData();
        }
        _SSedolData.SetSSedolDataAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: SSort
    public class SSort
    {
        private static int _size = 34;
        
        // Fields in the class
        
        
        // [DEBUG] Field: SCurrencySort, is_external=, is_static_class=False, static_prefix=
        private int _SCurrencySort =0;
        
        
        // 88-level condition checks for SCurrencySort
        public bool IsSCurrencySterling()
        {
            if (this._SCurrencySort == 0) return true;
            return false;
        }
        public bool IsSCurrencyEuro()
        {
            if (this._SCurrencySort == 1) return true;
            return false;
        }
        
        
        // [DEBUG] Field: SCoAcLk, is_external=, is_static_class=False, static_prefix=
        private string _SCoAcLk ="";
        
        
        
        
        // [DEBUG] Field: SCountryCode, is_external=, is_static_class=False, static_prefix=
        private string _SCountryCode ="";
        
        
        
        
        // [DEBUG] Field: SMainGroup, is_external=, is_static_class=False, static_prefix=
        private string _SMainGroup ="";
        
        
        
        
        // [DEBUG] Field: SSecurityType, is_external=, is_static_class=False, static_prefix=
        private string _SSecurityType ="";
        
        
        
        
        // [DEBUG] Field: SSecuritySortCode, is_external=, is_static_class=False, static_prefix=
        private string _SSecuritySortCode ="";
        
        
        
        
        // [DEBUG] Field: SSedolSort, is_external=, is_static_class=False, static_prefix=
        private string _SSedolSort ="";
        
        
        
        
    public SSort() {}
    
    public SSort(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetSCurrencySort(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetSCoAcLk(data.Substring(offset, 4).Trim());
        offset += 4;
        SetSCountryCode(data.Substring(offset, 3).Trim());
        offset += 3;
        SetSMainGroup(data.Substring(offset, 3).Trim());
        offset += 3;
        SetSSecurityType(data.Substring(offset, 1).Trim());
        offset += 1;
        SetSSecuritySortCode(data.Substring(offset, 15).Trim());
        offset += 15;
        SetSSedolSort(data.Substring(offset, 7).Trim());
        offset += 7;
        
    }
    
    // Serialization methods
    public string GetSSortAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_SCurrencySort.ToString().PadLeft(1, '0'));
        result.Append(_SCoAcLk.PadRight(4));
        result.Append(_SCountryCode.PadRight(3));
        result.Append(_SMainGroup.PadRight(3));
        result.Append(_SSecurityType.PadRight(1));
        result.Append(_SSecuritySortCode.PadRight(15));
        result.Append(_SSedolSort.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetSSortAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetSCurrencySort(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetSCoAcLk(extracted);
        }
        offset += 4;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetSCountryCode(extracted);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetSMainGroup(extracted);
        }
        offset += 3;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetSSecurityType(extracted);
        }
        offset += 1;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            SetSSecuritySortCode(extracted);
        }
        offset += 15;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetSSedolSort(extracted);
        }
        offset += 7;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetSCurrencySort()
    {
        return _SCurrencySort;
    }
    
    // Standard Setter
    public void SetSCurrencySort(int value)
    {
        _SCurrencySort = value;
    }
    
    // Get<>AsString()
    public string GetSCurrencySortAsString()
    {
        return _SCurrencySort.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetSCurrencySortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _SCurrencySort = parsed;
    }
    
    // Standard Getter
    public string GetSCoAcLk()
    {
        return _SCoAcLk;
    }
    
    // Standard Setter
    public void SetSCoAcLk(string value)
    {
        _SCoAcLk = value;
    }
    
    // Get<>AsString()
    public string GetSCoAcLkAsString()
    {
        return _SCoAcLk.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetSCoAcLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SCoAcLk = value;
    }
    
    // Standard Getter
    public string GetSCountryCode()
    {
        return _SCountryCode;
    }
    
    // Standard Setter
    public void SetSCountryCode(string value)
    {
        _SCountryCode = value;
    }
    
    // Get<>AsString()
    public string GetSCountryCodeAsString()
    {
        return _SCountryCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetSCountryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SCountryCode = value;
    }
    
    // Standard Getter
    public string GetSMainGroup()
    {
        return _SMainGroup;
    }
    
    // Standard Setter
    public void SetSMainGroup(string value)
    {
        _SMainGroup = value;
    }
    
    // Get<>AsString()
    public string GetSMainGroupAsString()
    {
        return _SMainGroup.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetSMainGroupAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SMainGroup = value;
    }
    
    // Standard Getter
    public string GetSSecurityType()
    {
        return _SSecurityType;
    }
    
    // Standard Setter
    public void SetSSecurityType(string value)
    {
        _SSecurityType = value;
    }
    
    // Get<>AsString()
    public string GetSSecurityTypeAsString()
    {
        return _SSecurityType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetSSecurityTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SSecurityType = value;
    }
    
    // Standard Getter
    public string GetSSecuritySortCode()
    {
        return _SSecuritySortCode;
    }
    
    // Standard Setter
    public void SetSSecuritySortCode(string value)
    {
        _SSecuritySortCode = value;
    }
    
    // Get<>AsString()
    public string GetSSecuritySortCodeAsString()
    {
        return _SSecuritySortCode.PadRight(15);
    }
    
    // Set<>AsString()
    public void SetSSecuritySortCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SSecuritySortCode = value;
    }
    
    // Standard Getter
    public string GetSSedolSort()
    {
        return _SSedolSort;
    }
    
    // Standard Setter
    public void SetSSedolSort(string value)
    {
        _SSedolSort = value;
    }
    
    // Get<>AsString()
    public string GetSSedolSortAsString()
    {
        return _SSedolSort.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetSSedolSortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SSedolSort = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: SData
public class SData
{
    private static int _size = 130;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SCompanyName, is_external=, is_static_class=False, static_prefix=
    private string _SCompanyName ="";
    
    
    
    
    // [DEBUG] Field: STitle, is_external=, is_static_class=False, static_prefix=
    private string _STitle ="";
    
    
    
    
    // [DEBUG] Field: Filler335, is_external=, is_static_class=False, static_prefix=
    private string _Filler335 ="";
    
    
    
    
    // [DEBUG] Field: Filler336, is_external=, is_static_class=False, static_prefix=
    private string _Filler336 ="";
    
    
    
    
    // [DEBUG] Field: Filler337, is_external=, is_static_class=False, static_prefix=
    private string _Filler337 ="";
    
    
    
    
    // [DEBUG] Field: Filler338, is_external=, is_static_class=False, static_prefix=
    private string _Filler338 ="";
    
    
    
    
    // [DEBUG] Field: Filler339, is_external=, is_static_class=False, static_prefix=
    private string _Filler339 ="";
    
    
    
    
    // [DEBUG] Field: Filler340, is_external=, is_static_class=False, static_prefix=
    private string _Filler340 ="";
    
    
    
    
    // [DEBUG] Field: Filler341, is_external=, is_static_class=False, static_prefix=
    private string _Filler341 ="";
    
    
    
    
    // [DEBUG] Field: Filler342, is_external=, is_static_class=False, static_prefix=
    private string _Filler342 ="";
    
    
    
    
    // [DEBUG] Field: Filler343, is_external=, is_static_class=False, static_prefix=
    private string _Filler343 ="";
    
    
    
    
    // [DEBUG] Field: Filler344, is_external=, is_static_class=False, static_prefix=
    private string _Filler344 ="";
    
    
    
    
    // [DEBUG] Field: Filler345, is_external=, is_static_class=False, static_prefix=
    private string _Filler345 ="";
    
    
    
    
    // [DEBUG] Field: Filler346, is_external=, is_static_class=False, static_prefix=
    private string _Filler346 ="";
    
    
    
    
    // [DEBUG] Field: Filler347, is_external=, is_static_class=False, static_prefix=
    private string _Filler347 ="";
    
    
    
    
public SData() {}

public SData(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSCompanyName(data.Substring(offset, 51).Trim());
    offset += 51;
    SetSTitle(data.Substring(offset, 60).Trim());
    offset += 60;
    SetFiller335(data.Substring(offset, 19).Trim());
    offset += 19;
    SetFiller336(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller337(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller338(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller339(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller340(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller341(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller342(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller343(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller344(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller345(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller346(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller347(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetSDataAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SCompanyName.PadRight(51));
    result.Append(_STitle.PadRight(60));
    result.Append(_Filler335.PadRight(19));
    result.Append(_Filler336.PadRight(0));
    result.Append(_Filler337.PadRight(0));
    result.Append(_Filler338.PadRight(0));
    result.Append(_Filler339.PadRight(0));
    result.Append(_Filler340.PadRight(0));
    result.Append(_Filler341.PadRight(0));
    result.Append(_Filler342.PadRight(0));
    result.Append(_Filler343.PadRight(0));
    result.Append(_Filler344.PadRight(0));
    result.Append(_Filler345.PadRight(0));
    result.Append(_Filler346.PadRight(0));
    result.Append(_Filler347.PadRight(0));
    
    return result.ToString();
}

public void SetSDataAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 51 <= data.Length)
    {
        string extracted = data.Substring(offset, 51).Trim();
        SetSCompanyName(extracted);
    }
    offset += 51;
    if (offset + 60 <= data.Length)
    {
        string extracted = data.Substring(offset, 60).Trim();
        SetSTitle(extracted);
    }
    offset += 60;
    if (offset + 19 <= data.Length)
    {
        string extracted = data.Substring(offset, 19).Trim();
        SetFiller335(extracted);
    }
    offset += 19;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller336(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller337(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller338(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller339(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller340(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller341(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller342(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller343(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller344(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller345(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller346(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller347(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetSCompanyName()
{
    return _SCompanyName;
}

// Standard Setter
public void SetSCompanyName(string value)
{
    _SCompanyName = value;
}

// Get<>AsString()
public string GetSCompanyNameAsString()
{
    return _SCompanyName.PadRight(51);
}

// Set<>AsString()
public void SetSCompanyNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SCompanyName = value;
}

// Standard Getter
public string GetSTitle()
{
    return _STitle;
}

// Standard Setter
public void SetSTitle(string value)
{
    _STitle = value;
}

// Get<>AsString()
public string GetSTitleAsString()
{
    return _STitle.PadRight(60);
}

// Set<>AsString()
public void SetSTitleAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STitle = value;
}

// Standard Getter
public string GetFiller335()
{
    return _Filler335;
}

// Standard Setter
public void SetFiller335(string value)
{
    _Filler335 = value;
}

// Get<>AsString()
public string GetFiller335AsString()
{
    return _Filler335.PadRight(19);
}

// Set<>AsString()
public void SetFiller335AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler335 = value;
}

// Standard Getter
public string GetFiller336()
{
    return _Filler336;
}

// Standard Setter
public void SetFiller336(string value)
{
    _Filler336 = value;
}

// Get<>AsString()
public string GetFiller336AsString()
{
    return _Filler336.PadRight(0);
}

// Set<>AsString()
public void SetFiller336AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler336 = value;
}

// Standard Getter
public string GetFiller337()
{
    return _Filler337;
}

// Standard Setter
public void SetFiller337(string value)
{
    _Filler337 = value;
}

// Get<>AsString()
public string GetFiller337AsString()
{
    return _Filler337.PadRight(0);
}

// Set<>AsString()
public void SetFiller337AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler337 = value;
}

// Standard Getter
public string GetFiller338()
{
    return _Filler338;
}

// Standard Setter
public void SetFiller338(string value)
{
    _Filler338 = value;
}

// Get<>AsString()
public string GetFiller338AsString()
{
    return _Filler338.PadRight(0);
}

// Set<>AsString()
public void SetFiller338AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler338 = value;
}

// Standard Getter
public string GetFiller339()
{
    return _Filler339;
}

// Standard Setter
public void SetFiller339(string value)
{
    _Filler339 = value;
}

// Get<>AsString()
public string GetFiller339AsString()
{
    return _Filler339.PadRight(0);
}

// Set<>AsString()
public void SetFiller339AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler339 = value;
}

// Standard Getter
public string GetFiller340()
{
    return _Filler340;
}

// Standard Setter
public void SetFiller340(string value)
{
    _Filler340 = value;
}

// Get<>AsString()
public string GetFiller340AsString()
{
    return _Filler340.PadRight(0);
}

// Set<>AsString()
public void SetFiller340AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler340 = value;
}

// Standard Getter
public string GetFiller341()
{
    return _Filler341;
}

// Standard Setter
public void SetFiller341(string value)
{
    _Filler341 = value;
}

// Get<>AsString()
public string GetFiller341AsString()
{
    return _Filler341.PadRight(0);
}

// Set<>AsString()
public void SetFiller341AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler341 = value;
}

// Standard Getter
public string GetFiller342()
{
    return _Filler342;
}

// Standard Setter
public void SetFiller342(string value)
{
    _Filler342 = value;
}

// Get<>AsString()
public string GetFiller342AsString()
{
    return _Filler342.PadRight(0);
}

// Set<>AsString()
public void SetFiller342AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler342 = value;
}

// Standard Getter
public string GetFiller343()
{
    return _Filler343;
}

// Standard Setter
public void SetFiller343(string value)
{
    _Filler343 = value;
}

// Get<>AsString()
public string GetFiller343AsString()
{
    return _Filler343.PadRight(0);
}

// Set<>AsString()
public void SetFiller343AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler343 = value;
}

// Standard Getter
public string GetFiller344()
{
    return _Filler344;
}

// Standard Setter
public void SetFiller344(string value)
{
    _Filler344 = value;
}

// Get<>AsString()
public string GetFiller344AsString()
{
    return _Filler344.PadRight(0);
}

// Set<>AsString()
public void SetFiller344AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler344 = value;
}

// Standard Getter
public string GetFiller345()
{
    return _Filler345;
}

// Standard Setter
public void SetFiller345(string value)
{
    _Filler345 = value;
}

// Get<>AsString()
public string GetFiller345AsString()
{
    return _Filler345.PadRight(0);
}

// Set<>AsString()
public void SetFiller345AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler345 = value;
}

// Standard Getter
public string GetFiller346()
{
    return _Filler346;
}

// Standard Setter
public void SetFiller346(string value)
{
    _Filler346 = value;
}

// Get<>AsString()
public string GetFiller346AsString()
{
    return _Filler346.PadRight(0);
}

// Set<>AsString()
public void SetFiller346AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler346 = value;
}

// Standard Getter
public string GetFiller347()
{
    return _Filler347;
}

// Standard Setter
public void SetFiller347(string value)
{
    _Filler347 = value;
}

// Get<>AsString()
public string GetFiller347AsString()
{
    return _Filler347.PadRight(0);
}

// Set<>AsString()
public void SetFiller347AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler347 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: SSedolData
public class SSedolData
{
    private static int _size = 154;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler348, is_external=, is_static_class=False, static_prefix=
    private string _Filler348 ="";
    
    
    
    
    // [DEBUG] Field: SSedolNumber, is_external=, is_static_class=False, static_prefix=
    private string _SSedolNumber ="";
    
    
    
    
    // [DEBUG] Field: SIssuersName, is_external=, is_static_class=False, static_prefix=
    private SSedolData.SIssuersName _SIssuersName = new SSedolData.SIssuersName();
    
    
    
    
    // [DEBUG] Field: SStockDescription, is_external=, is_static_class=False, static_prefix=
    private SSedolData.SStockDescription _SStockDescription = new SSedolData.SStockDescription();
    
    
    
    
    // [DEBUG] Field: SLastTrancheContractNo, is_external=, is_static_class=False, static_prefix=
    private string _SLastTrancheContractNo ="";
    
    
    
    
    // [DEBUG] Field: SProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _SProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: SSedolRecordCount, is_external=, is_static_class=False, static_prefix=
    private int _SSedolRecordCount =0;
    
    
    
    
    // [DEBUG] Field: STrancheCount, is_external=, is_static_class=False, static_prefix=
    private int _STrancheCount =0;
    
    
    
    
    // [DEBUG] Field: SPrintFlag, is_external=, is_static_class=False, static_prefix=
    private string _SPrintFlag ="";
    
    
    
    
    // [DEBUG] Field: SHoldingFlag, is_external=, is_static_class=False, static_prefix=
    private string _SHoldingFlag ="";
    
    
    
    
    // [DEBUG] Field: Filler349, is_external=, is_static_class=False, static_prefix=
    private string _Filler349 ="";
    
    
    
    
    // [DEBUG] Field: Filler350, is_external=, is_static_class=False, static_prefix=
    private string _Filler350 ="";
    
    
    
    
    // [DEBUG] Field: SReitSecurityType, is_external=, is_static_class=False, static_prefix=
    private string _SReitSecurityType ="";
    
    
    
    
    // [DEBUG] Field: SQuotedIndicator, is_external=, is_static_class=False, static_prefix=
    private string _SQuotedIndicator ="";
    
    
    
    
    // [DEBUG] Field: Filler351, is_external=, is_static_class=False, static_prefix=
    private string _Filler351 ="";
    
    
    
    
    // [DEBUG] Field: SLostIndexation, is_external=, is_static_class=False, static_prefix=
    private decimal _SLostIndexation =0;
    
    
    
    
    // [DEBUG] Field: SOffshoreIncomeGain, is_external=, is_static_class=False, static_prefix=
    private decimal _SOffshoreIncomeGain =0;
    
    
    
    
    // [DEBUG] Field: Filler352, is_external=, is_static_class=False, static_prefix=
    private string _Filler352 ="";
    
    
    
    
    // [DEBUG] Field: Filler353, is_external=, is_static_class=False, static_prefix=
    private string _Filler353 ="";
    
    
    
    
    // [DEBUG] Field: Filler354, is_external=, is_static_class=False, static_prefix=
    private string _Filler354 ="";
    
    
    
    
    // [DEBUG] Field: Filler355, is_external=, is_static_class=False, static_prefix=
    private string _Filler355 ="";
    
    
    
    
    // [DEBUG] Field: Filler356, is_external=, is_static_class=False, static_prefix=
    private string _Filler356 ="";
    
    
    
    
    // [DEBUG] Field: Filler357, is_external=, is_static_class=False, static_prefix=
    private string _Filler357 ="";
    
    
    
    
public SSedolData() {}

public SSedolData(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller348(data.Substring(offset, 19).Trim());
    offset += 19;
    SetSSedolNumber(data.Substring(offset, 7).Trim());
    offset += 7;
    _SIssuersName.SetSIssuersNameAsString(data.Substring(offset, SIssuersName.GetSize()));
    offset += 35;
    _SStockDescription.SetSStockDescriptionAsString(data.Substring(offset, SStockDescription.GetSize()));
    offset += 40;
    SetSLastTrancheContractNo(data.Substring(offset, 10).Trim());
    offset += 10;
    SetSProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
    offset += 11;
    SetSSedolRecordCount(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetSTrancheCount(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetSPrintFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetSHoldingFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetFiller349(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller350(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSReitSecurityType(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSQuotedIndicator(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller351(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSLostIndexation(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSOffshoreIncomeGain(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetFiller352(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller353(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller354(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller355(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller356(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller357(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetSSedolDataAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler348.PadRight(19));
    result.Append(_SSedolNumber.PadRight(7));
    result.Append(_SIssuersName.GetSIssuersNameAsString());
    result.Append(_SStockDescription.GetSStockDescriptionAsString());
    result.Append(_SLastTrancheContractNo.PadRight(10));
    result.Append(_SProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SSedolRecordCount.ToString().PadLeft(3, '0'));
    result.Append(_STrancheCount.ToString().PadLeft(3, '0'));
    result.Append(_SPrintFlag.PadRight(1));
    result.Append(_SHoldingFlag.PadRight(1));
    result.Append(_Filler349.PadRight(0));
    result.Append(_Filler350.PadRight(0));
    result.Append(_SReitSecurityType.PadRight(0));
    result.Append(_SQuotedIndicator.PadRight(0));
    result.Append(_Filler351.PadRight(0));
    result.Append(_SLostIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SOffshoreIncomeGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler352.PadRight(0));
    result.Append(_Filler353.PadRight(0));
    result.Append(_Filler354.PadRight(0));
    result.Append(_Filler355.PadRight(0));
    result.Append(_Filler356.PadRight(0));
    result.Append(_Filler357.PadRight(0));
    
    return result.ToString();
}

public void SetSSedolDataAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 19 <= data.Length)
    {
        string extracted = data.Substring(offset, 19).Trim();
        SetFiller348(extracted);
    }
    offset += 19;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetSSedolNumber(extracted);
    }
    offset += 7;
    if (offset + 35 <= data.Length)
    {
        _SIssuersName.SetSIssuersNameAsString(data.Substring(offset, 35));
    }
    else
    {
        _SIssuersName.SetSIssuersNameAsString(data.Substring(offset));
    }
    offset += 35;
    if (offset + 40 <= data.Length)
    {
        _SStockDescription.SetSStockDescriptionAsString(data.Substring(offset, 40));
    }
    else
    {
        _SStockDescription.SetSStockDescriptionAsString(data.Substring(offset));
    }
    offset += 40;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetSLastTrancheContractNo(extracted);
    }
    offset += 10;
    if (offset + 11 <= data.Length)
    {
        string extracted = data.Substring(offset, 11).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSProfitLoss(parsedDec);
    }
    offset += 11;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSSedolRecordCount(parsedInt);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSTrancheCount(parsedInt);
    }
    offset += 3;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetSPrintFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetSHoldingFlag(extracted);
    }
    offset += 1;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller349(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller350(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSReitSecurityType(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSQuotedIndicator(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller351(extracted);
    }
    offset += 0;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSLostIndexation(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSOffshoreIncomeGain(parsedDec);
    }
    offset += 12;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller352(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller353(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller354(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller355(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller356(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller357(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller348()
{
    return _Filler348;
}

// Standard Setter
public void SetFiller348(string value)
{
    _Filler348 = value;
}

// Get<>AsString()
public string GetFiller348AsString()
{
    return _Filler348.PadRight(19);
}

// Set<>AsString()
public void SetFiller348AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler348 = value;
}

// Standard Getter
public string GetSSedolNumber()
{
    return _SSedolNumber;
}

// Standard Setter
public void SetSSedolNumber(string value)
{
    _SSedolNumber = value;
}

// Get<>AsString()
public string GetSSedolNumberAsString()
{
    return _SSedolNumber.PadRight(7);
}

// Set<>AsString()
public void SetSSedolNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SSedolNumber = value;
}

// Standard Getter
public SIssuersName GetSIssuersName()
{
    return _SIssuersName;
}

// Standard Setter
public void SetSIssuersName(SIssuersName value)
{
    _SIssuersName = value;
}

// Get<>AsString()
public string GetSIssuersNameAsString()
{
    return _SIssuersName != null ? _SIssuersName.GetSIssuersNameAsString() : "";
}

// Set<>AsString()
public void SetSIssuersNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_SIssuersName == null)
    {
        _SIssuersName = new SIssuersName();
    }
    _SIssuersName.SetSIssuersNameAsString(value);
}

// Standard Getter
public SStockDescription GetSStockDescription()
{
    return _SStockDescription;
}

// Standard Setter
public void SetSStockDescription(SStockDescription value)
{
    _SStockDescription = value;
}

// Get<>AsString()
public string GetSStockDescriptionAsString()
{
    return _SStockDescription != null ? _SStockDescription.GetSStockDescriptionAsString() : "";
}

// Set<>AsString()
public void SetSStockDescriptionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_SStockDescription == null)
    {
        _SStockDescription = new SStockDescription();
    }
    _SStockDescription.SetSStockDescriptionAsString(value);
}

// Standard Getter
public string GetSLastTrancheContractNo()
{
    return _SLastTrancheContractNo;
}

// Standard Setter
public void SetSLastTrancheContractNo(string value)
{
    _SLastTrancheContractNo = value;
}

// Get<>AsString()
public string GetSLastTrancheContractNoAsString()
{
    return _SLastTrancheContractNo.PadRight(10);
}

// Set<>AsString()
public void SetSLastTrancheContractNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SLastTrancheContractNo = value;
}

// Standard Getter
public decimal GetSProfitLoss()
{
    return _SProfitLoss;
}

// Standard Setter
public void SetSProfitLoss(decimal value)
{
    _SProfitLoss = value;
}

// Get<>AsString()
public string GetSProfitLossAsString()
{
    return _SProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSProfitLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SProfitLoss = parsed;
}

// Standard Getter
public int GetSSedolRecordCount()
{
    return _SSedolRecordCount;
}

// Standard Setter
public void SetSSedolRecordCount(int value)
{
    _SSedolRecordCount = value;
}

// Get<>AsString()
public string GetSSedolRecordCountAsString()
{
    return _SSedolRecordCount.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetSSedolRecordCountAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SSedolRecordCount = parsed;
}

// Standard Getter
public int GetSTrancheCount()
{
    return _STrancheCount;
}

// Standard Setter
public void SetSTrancheCount(int value)
{
    _STrancheCount = value;
}

// Get<>AsString()
public string GetSTrancheCountAsString()
{
    return _STrancheCount.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetSTrancheCountAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _STrancheCount = parsed;
}

// Standard Getter
public string GetSPrintFlag()
{
    return _SPrintFlag;
}

// Standard Setter
public void SetSPrintFlag(string value)
{
    _SPrintFlag = value;
}

// Get<>AsString()
public string GetSPrintFlagAsString()
{
    return _SPrintFlag.PadRight(1);
}

// Set<>AsString()
public void SetSPrintFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SPrintFlag = value;
}

// Standard Getter
public string GetSHoldingFlag()
{
    return _SHoldingFlag;
}

// Standard Setter
public void SetSHoldingFlag(string value)
{
    _SHoldingFlag = value;
}

// Get<>AsString()
public string GetSHoldingFlagAsString()
{
    return _SHoldingFlag.PadRight(1);
}

// Set<>AsString()
public void SetSHoldingFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SHoldingFlag = value;
}

// Standard Getter
public string GetFiller349()
{
    return _Filler349;
}

// Standard Setter
public void SetFiller349(string value)
{
    _Filler349 = value;
}

// Get<>AsString()
public string GetFiller349AsString()
{
    return _Filler349.PadRight(0);
}

// Set<>AsString()
public void SetFiller349AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler349 = value;
}

// Standard Getter
public string GetFiller350()
{
    return _Filler350;
}

// Standard Setter
public void SetFiller350(string value)
{
    _Filler350 = value;
}

// Get<>AsString()
public string GetFiller350AsString()
{
    return _Filler350.PadRight(0);
}

// Set<>AsString()
public void SetFiller350AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler350 = value;
}

// Standard Getter
public string GetSReitSecurityType()
{
    return _SReitSecurityType;
}

// Standard Setter
public void SetSReitSecurityType(string value)
{
    _SReitSecurityType = value;
}

// Get<>AsString()
public string GetSReitSecurityTypeAsString()
{
    return _SReitSecurityType.PadRight(0);
}

// Set<>AsString()
public void SetSReitSecurityTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SReitSecurityType = value;
}

// Standard Getter
public string GetSQuotedIndicator()
{
    return _SQuotedIndicator;
}

// Standard Setter
public void SetSQuotedIndicator(string value)
{
    _SQuotedIndicator = value;
}

// Get<>AsString()
public string GetSQuotedIndicatorAsString()
{
    return _SQuotedIndicator.PadRight(0);
}

// Set<>AsString()
public void SetSQuotedIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SQuotedIndicator = value;
}

// Standard Getter
public string GetFiller351()
{
    return _Filler351;
}

// Standard Setter
public void SetFiller351(string value)
{
    _Filler351 = value;
}

// Get<>AsString()
public string GetFiller351AsString()
{
    return _Filler351.PadRight(0);
}

// Set<>AsString()
public void SetFiller351AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler351 = value;
}

// Standard Getter
public decimal GetSLostIndexation()
{
    return _SLostIndexation;
}

// Standard Setter
public void SetSLostIndexation(decimal value)
{
    _SLostIndexation = value;
}

// Get<>AsString()
public string GetSLostIndexationAsString()
{
    return _SLostIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSLostIndexationAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SLostIndexation = parsed;
}

// Standard Getter
public decimal GetSOffshoreIncomeGain()
{
    return _SOffshoreIncomeGain;
}

// Standard Setter
public void SetSOffshoreIncomeGain(decimal value)
{
    _SOffshoreIncomeGain = value;
}

// Get<>AsString()
public string GetSOffshoreIncomeGainAsString()
{
    return _SOffshoreIncomeGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSOffshoreIncomeGainAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SOffshoreIncomeGain = parsed;
}

// Standard Getter
public string GetFiller352()
{
    return _Filler352;
}

// Standard Setter
public void SetFiller352(string value)
{
    _Filler352 = value;
}

// Get<>AsString()
public string GetFiller352AsString()
{
    return _Filler352.PadRight(0);
}

// Set<>AsString()
public void SetFiller352AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler352 = value;
}

// Standard Getter
public string GetFiller353()
{
    return _Filler353;
}

// Standard Setter
public void SetFiller353(string value)
{
    _Filler353 = value;
}

// Get<>AsString()
public string GetFiller353AsString()
{
    return _Filler353.PadRight(0);
}

// Set<>AsString()
public void SetFiller353AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler353 = value;
}

// Standard Getter
public string GetFiller354()
{
    return _Filler354;
}

// Standard Setter
public void SetFiller354(string value)
{
    _Filler354 = value;
}

// Get<>AsString()
public string GetFiller354AsString()
{
    return _Filler354.PadRight(0);
}

// Set<>AsString()
public void SetFiller354AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler354 = value;
}

// Standard Getter
public string GetFiller355()
{
    return _Filler355;
}

// Standard Setter
public void SetFiller355(string value)
{
    _Filler355 = value;
}

// Get<>AsString()
public string GetFiller355AsString()
{
    return _Filler355.PadRight(0);
}

// Set<>AsString()
public void SetFiller355AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler355 = value;
}

// Standard Getter
public string GetFiller356()
{
    return _Filler356;
}

// Standard Setter
public void SetFiller356(string value)
{
    _Filler356 = value;
}

// Get<>AsString()
public string GetFiller356AsString()
{
    return _Filler356.PadRight(0);
}

// Set<>AsString()
public void SetFiller356AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler356 = value;
}

// Standard Getter
public string GetFiller357()
{
    return _Filler357;
}

// Standard Setter
public void SetFiller357(string value)
{
    _Filler357 = value;
}

// Get<>AsString()
public string GetFiller357AsString()
{
    return _Filler357.PadRight(0);
}

// Set<>AsString()
public void SetFiller357AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler357 = value;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: SIssuersName
public class SIssuersName
{
    private static int _size = 35;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SIssuersName1, is_external=, is_static_class=False, static_prefix=
    private string _SIssuersName1 ="";
    
    
    
    
    // [DEBUG] Field: SIssuersName2, is_external=, is_static_class=False, static_prefix=
    private string _SIssuersName2 ="";
    
    
    
    
public SIssuersName() {}

public SIssuersName(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSIssuersName1(data.Substring(offset, 20).Trim());
    offset += 20;
    SetSIssuersName2(data.Substring(offset, 15).Trim());
    offset += 15;
    
}

// Serialization methods
public string GetSIssuersNameAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SIssuersName1.PadRight(20));
    result.Append(_SIssuersName2.PadRight(15));
    
    return result.ToString();
}

public void SetSIssuersNameAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetSIssuersName1(extracted);
    }
    offset += 20;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetSIssuersName2(extracted);
    }
    offset += 15;
}

// Getter and Setter methods

// Standard Getter
public string GetSIssuersName1()
{
    return _SIssuersName1;
}

// Standard Setter
public void SetSIssuersName1(string value)
{
    _SIssuersName1 = value;
}

// Get<>AsString()
public string GetSIssuersName1AsString()
{
    return _SIssuersName1.PadRight(20);
}

// Set<>AsString()
public void SetSIssuersName1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SIssuersName1 = value;
}

// Standard Getter
public string GetSIssuersName2()
{
    return _SIssuersName2;
}

// Standard Setter
public void SetSIssuersName2(string value)
{
    _SIssuersName2 = value;
}

// Get<>AsString()
public string GetSIssuersName2AsString()
{
    return _SIssuersName2.PadRight(15);
}

// Set<>AsString()
public void SetSIssuersName2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SIssuersName2 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: SStockDescription
public class SStockDescription
{
    private static int _size = 40;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SStockDescription1, is_external=, is_static_class=False, static_prefix=
    private string _SStockDescription1 ="";
    
    
    
    
    // [DEBUG] Field: SStockDescription2, is_external=, is_static_class=False, static_prefix=
    private string _SStockDescription2 ="";
    
    
    
    
public SStockDescription() {}

public SStockDescription(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSStockDescription1(data.Substring(offset, 20).Trim());
    offset += 20;
    SetSStockDescription2(data.Substring(offset, 20).Trim());
    offset += 20;
    
}

// Serialization methods
public string GetSStockDescriptionAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SStockDescription1.PadRight(20));
    result.Append(_SStockDescription2.PadRight(20));
    
    return result.ToString();
}

public void SetSStockDescriptionAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetSStockDescription1(extracted);
    }
    offset += 20;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetSStockDescription2(extracted);
    }
    offset += 20;
}

// Getter and Setter methods

// Standard Getter
public string GetSStockDescription1()
{
    return _SStockDescription1;
}

// Standard Setter
public void SetSStockDescription1(string value)
{
    _SStockDescription1 = value;
}

// Get<>AsString()
public string GetSStockDescription1AsString()
{
    return _SStockDescription1.PadRight(20);
}

// Set<>AsString()
public void SetSStockDescription1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SStockDescription1 = value;
}

// Standard Getter
public string GetSStockDescription2()
{
    return _SStockDescription2;
}

// Standard Setter
public void SetSStockDescription2(string value)
{
    _SStockDescription2 = value;
}

// Get<>AsString()
public string GetSStockDescription2AsString()
{
    return _SStockDescription2.PadRight(20);
}

// Set<>AsString()
public void SetSStockDescription2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SStockDescription2 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}
}

}}