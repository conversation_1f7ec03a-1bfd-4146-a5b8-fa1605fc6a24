using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtGtMatchedTranches Data Structure

public class WtGtMatchedTranches
{
    private static int _size = 45;
    // [DEBUG] Class: WtGtMatchedTranches, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler446, is_external=, is_static_class=False, static_prefix=
    private string _Filler446 ="GT-MATCH-TRANCHS";
    
    
    
    
    // [DEBUG] Field: WtmgtMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtmgtMaxTableSize =4000;
    
    
    
    
    // [DEBUG] Field: WtmgtOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtmgtOccurs =4000;
    
    
    
    
    // [DEBUG] Field: WtmgtTable, is_external=, is_static_class=False, static_prefix=
    private WtmgtTable _WtmgtTable = new WtmgtTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtGtMatchedTranchesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler446.PadRight(16));
        result.Append(_WtmgtMaxTableSize.ToString().PadLeft(4, '0'));
        result.Append(_WtmgtOccurs.ToString().PadLeft(4, '0'));
        result.Append(_WtmgtTable.GetWtmgtTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtGtMatchedTranchesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller446(extracted);
        }
        offset += 16;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtmgtMaxTableSize(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtmgtOccurs(parsedInt);
        }
        offset += 4;
        if (offset + 21 <= data.Length)
        {
            _WtmgtTable.SetWtmgtTableAsString(data.Substring(offset, 21));
        }
        else
        {
            _WtmgtTable.SetWtmgtTableAsString(data.Substring(offset));
        }
        offset += 21;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtGtMatchedTranchesAsString();
    }
    // Set<>String Override function
    public void SetWtGtMatchedTranches(string value)
    {
        SetWtGtMatchedTranchesAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller446()
    {
        return _Filler446;
    }
    
    // Standard Setter
    public void SetFiller446(string value)
    {
        _Filler446 = value;
    }
    
    // Get<>AsString()
    public string GetFiller446AsString()
    {
        return _Filler446.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller446AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler446 = value;
    }
    
    // Standard Getter
    public int GetWtmgtMaxTableSize()
    {
        return _WtmgtMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtmgtMaxTableSize(int value)
    {
        _WtmgtMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtmgtMaxTableSizeAsString()
    {
        return _WtmgtMaxTableSize.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtmgtMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtmgtMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtmgtOccurs()
    {
        return _WtmgtOccurs;
    }
    
    // Standard Setter
    public void SetWtmgtOccurs(int value)
    {
        _WtmgtOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtmgtOccursAsString()
    {
        return _WtmgtOccurs.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtmgtOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtmgtOccurs = parsed;
    }
    
    // Standard Getter
    public WtmgtTable GetWtmgtTable()
    {
        return _WtmgtTable;
    }
    
    // Standard Setter
    public void SetWtmgtTable(WtmgtTable value)
    {
        _WtmgtTable = value;
    }
    
    // Get<>AsString()
    public string GetWtmgtTableAsString()
    {
        return _WtmgtTable != null ? _WtmgtTable.GetWtmgtTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtmgtTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtmgtTable == null)
        {
            _WtmgtTable = new WtmgtTable();
        }
        _WtmgtTable.SetWtmgtTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtmgtTable(string value)
    {
        _WtmgtTable.SetWtmgtTableAsString(value);
    }
    // Nested Class: WtmgtTable
    public class WtmgtTable
    {
        private static int _size = 21;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtmgtElement, is_external=, is_static_class=False, static_prefix=
        private WtmgtTable.WtmgtElement[] _WtmgtElement = new WtmgtTable.WtmgtElement[4000];
        
        public void InitializeWtmgtElementArray()
        {
            for (int i = 0; i < 4000; i++)
            {
                _WtmgtElement[i] = new WtmgtTable.WtmgtElement();
            }
        }
        
        
        
    public WtmgtTable() {}
    
    public WtmgtTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWtmgtElementArray();
        for (int i = 0; i < 4000; i++)
        {
            _WtmgtElement[i].SetWtmgtElementAsString(data.Substring(offset, 21));
            offset += 21;
        }
        
    }
    
    // Serialization methods
    public string GetWtmgtTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 4000; i++)
        {
            result.Append(_WtmgtElement[i].GetWtmgtElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtmgtTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 4000; i++)
        {
            if (offset + 21 > data.Length) break;
            string val = data.Substring(offset, 21);
            
            _WtmgtElement[i].SetWtmgtElementAsString(val);
            offset += 21;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtmgtElement
    public WtmgtElement GetWtmgtElementAt(int index)
    {
        return _WtmgtElement[index];
    }
    
    public void SetWtmgtElementAt(int index, WtmgtElement value)
    {
        _WtmgtElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtmgtElement GetWtmgtElement()
    {
        return _WtmgtElement != null && _WtmgtElement.Length > 0
        ? _WtmgtElement[0]
        : new WtmgtElement();
    }
    
    public void SetWtmgtElement(WtmgtElement value)
    {
        if (_WtmgtElement == null || _WtmgtElement.Length == 0)
        _WtmgtElement = new WtmgtElement[1];
        _WtmgtElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtmgtElement
    public class WtmgtElement
    {
        private static int _size = 21;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtmgtAcqnIndex, is_external=, is_static_class=False, static_prefix=
        private string _WtmgtAcqnIndex ="";
        
        
        
        
        // [DEBUG] Field: WtmgtDispIndex, is_external=, is_static_class=False, static_prefix=
        private string _WtmgtDispIndex ="";
        
        
        
        
        // [DEBUG] Field: WtmgtTaperUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WtmgtTaperUnits =0;
        
        
        
        
    public WtmgtElement() {}
    
    public WtmgtElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtmgtAcqnIndex(data.Substring(offset, 4).Trim());
        offset += 4;
        SetWtmgtDispIndex(data.Substring(offset, 4).Trim());
        offset += 4;
        SetWtmgtTaperUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        
    }
    
    // Serialization methods
    public string GetWtmgtElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtmgtAcqnIndex.PadRight(4));
        result.Append(_WtmgtDispIndex.PadRight(4));
        result.Append(_WtmgtTaperUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        
        return result.ToString();
    }
    
    public void SetWtmgtElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtmgtAcqnIndex(extracted);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtmgtDispIndex(extracted);
        }
        offset += 4;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtmgtTaperUnits(parsedDec);
        }
        offset += 13;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtmgtAcqnIndex()
    {
        return _WtmgtAcqnIndex;
    }
    
    // Standard Setter
    public void SetWtmgtAcqnIndex(string value)
    {
        _WtmgtAcqnIndex = value;
    }
    
    // Get<>AsString()
    public string GetWtmgtAcqnIndexAsString()
    {
        return _WtmgtAcqnIndex.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtmgtAcqnIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtmgtAcqnIndex = value;
    }
    
    // Standard Getter
    public string GetWtmgtDispIndex()
    {
        return _WtmgtDispIndex;
    }
    
    // Standard Setter
    public void SetWtmgtDispIndex(string value)
    {
        _WtmgtDispIndex = value;
    }
    
    // Get<>AsString()
    public string GetWtmgtDispIndexAsString()
    {
        return _WtmgtDispIndex.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtmgtDispIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtmgtDispIndex = value;
    }
    
    // Standard Getter
    public decimal GetWtmgtTaperUnits()
    {
        return _WtmgtTaperUnits;
    }
    
    // Standard Setter
    public void SetWtmgtTaperUnits(decimal value)
    {
        _WtmgtTaperUnits = value;
    }
    
    // Get<>AsString()
    public string GetWtmgtTaperUnitsAsString()
    {
        return _WtmgtTaperUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtmgtTaperUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtmgtTaperUnits = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}

}}
