using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D72Record Data Structure

public class D72Record
{
    private static int _size = 151;
    // [DEBUG] Class: D72Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D72PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D72PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D72ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D72ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD72RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D72PrintControl.PadRight(1));
        result.Append(_D72ReportLine.PadRight(150));
        
        return result.ToString();
    }
    
    public void SetD72RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD72PrintControl(extracted);
        }
        offset += 1;
        if (offset + 150 <= data.Length)
        {
            string extracted = data.Substring(offset, 150).Trim();
            SetD72ReportLine(extracted);
        }
        offset += 150;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD72RecordAsString();
    }
    // Set<>String Override function
    public void SetD72Record(string value)
    {
        SetD72RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD72PrintControl()
    {
        return _D72PrintControl;
    }
    
    // Standard Setter
    public void SetD72PrintControl(string value)
    {
        _D72PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD72PrintControlAsString()
    {
        return _D72PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD72PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D72PrintControl = value;
    }
    
    // Standard Getter
    public string GetD72ReportLine()
    {
        return _D72ReportLine;
    }
    
    // Standard Setter
    public void SetD72ReportLine(string value)
    {
        _D72ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD72ReportLineAsString()
    {
        return _D72ReportLine.PadRight(150);
    }
    
    // Set<>AsString()
    public void SetD72ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D72ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}