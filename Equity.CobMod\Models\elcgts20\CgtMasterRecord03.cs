using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing CgtMasterRecord03 Data Structure

public class CgtMasterRecord03
{
    private static int _size = 253;
    // [DEBUG] Class: CgtMasterRecord03, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler185, is_external=, is_static_class=False, static_prefix=
    private string _Filler185 ="";
    
    
    
    
    // [DEBUG] Field: CgtStockPpi, is_external=, is_static_class=False, static_prefix=
    private string _CgtStockPpi ="";
    
    
    
    
    // [DEBUG] Field: Filler186, is_external=, is_static_class=False, static_prefix=
    private string _Filler186 ="";
    
    
    
    
    
    // Serialization methods
    public string GetCgtMasterRecord03AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler185.PadRight(93));
        result.Append(_CgtStockPpi.PadRight(1));
        result.Append(_Filler186.PadRight(159));
        
        return result.ToString();
    }
    
    public void SetCgtMasterRecord03AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 93 <= data.Length)
        {
            string extracted = data.Substring(offset, 93).Trim();
            SetFiller185(extracted);
        }
        offset += 93;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetCgtStockPpi(extracted);
        }
        offset += 1;
        if (offset + 159 <= data.Length)
        {
            string extracted = data.Substring(offset, 159).Trim();
            SetFiller186(extracted);
        }
        offset += 159;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtMasterRecord03AsString();
    }
    // Set<>String Override function
    public void SetCgtMasterRecord03(string value)
    {
        SetCgtMasterRecord03AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller185()
    {
        return _Filler185;
    }
    
    // Standard Setter
    public void SetFiller185(string value)
    {
        _Filler185 = value;
    }
    
    // Get<>AsString()
    public string GetFiller185AsString()
    {
        return _Filler185.PadRight(93);
    }
    
    // Set<>AsString()
    public void SetFiller185AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler185 = value;
    }
    
    // Standard Getter
    public string GetCgtStockPpi()
    {
        return _CgtStockPpi;
    }
    
    // Standard Setter
    public void SetCgtStockPpi(string value)
    {
        _CgtStockPpi = value;
    }
    
    // Get<>AsString()
    public string GetCgtStockPpiAsString()
    {
        return _CgtStockPpi.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetCgtStockPpiAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtStockPpi = value;
    }
    
    // Standard Getter
    public string GetFiller186()
    {
        return _Filler186;
    }
    
    // Standard Setter
    public void SetFiller186(string value)
    {
        _Filler186 = value;
    }
    
    // Get<>AsString()
    public string GetFiller186AsString()
    {
        return _Filler186.PadRight(159);
    }
    
    // Set<>AsString()
    public void SetFiller186AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler186 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}