using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D6Record Data Structure

public class D6Record
{
    private static int _size = 133;
    // [DEBUG] Class: D6Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D6PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D6PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D6ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D6ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD6RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D6PrintControl.PadRight(1));
        result.Append(_D6ReportLine.PadRight(132));
        
        return result.ToString();
    }
    
    public void SetD6RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD6PrintControl(extracted);
        }
        offset += 1;
        if (offset + 132 <= data.Length)
        {
            string extracted = data.Substring(offset, 132).Trim();
            SetD6ReportLine(extracted);
        }
        offset += 132;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD6RecordAsString();
    }
    // Set<>String Override function
    public void SetD6Record(string value)
    {
        SetD6RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD6PrintControl()
    {
        return _D6PrintControl;
    }
    
    // Standard Setter
    public void SetD6PrintControl(string value)
    {
        _D6PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD6PrintControlAsString()
    {
        return _D6PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD6PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D6PrintControl = value;
    }
    
    // Standard Getter
    public string GetD6ReportLine()
    {
        return _D6ReportLine;
    }
    
    // Standard Setter
    public void SetD6ReportLine(string value)
    {
        _D6ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD6ReportLineAsString()
    {
        return _D6ReportLine.PadRight(132);
    }
    
    // Set<>AsString()
    public void SetD6ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D6ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}