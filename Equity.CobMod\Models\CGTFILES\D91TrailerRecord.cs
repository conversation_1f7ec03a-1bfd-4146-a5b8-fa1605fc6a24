using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D91TrailerRecord Data Structure

public class D91TrailerRecord
{
    private static int _size = 43;
    // [DEBUG] Class: D91TrailerRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler129, is_external=, is_static_class=False, static_prefix=
    private string _Filler129 ="";
    
    
    // 88-level condition checks for Filler129
    public bool IsD91TrailerFound()
    {
        if (this._Filler129 == "'zzzEQUITY COUNTRY TRAILER'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D91TrailerCount, is_external=, is_static_class=False, static_prefix=
    private int _D91TrailerCount =0;
    
    
    
    
    // [DEBUG] Field: Filler130, is_external=, is_static_class=False, static_prefix=
    private string _Filler130 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD91TrailerRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler129.PadRight(25));
        result.Append(_D91TrailerCount.ToString().PadLeft(6, '0'));
        result.Append(_Filler130.PadRight(12));
        
        return result.ToString();
    }
    
    public void SetD91TrailerRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller129(extracted);
        }
        offset += 25;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD91TrailerCount(parsedInt);
        }
        offset += 6;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            SetFiller130(extracted);
        }
        offset += 12;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD91TrailerRecordAsString();
    }
    // Set<>String Override function
    public void SetD91TrailerRecord(string value)
    {
        SetD91TrailerRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller129()
    {
        return _Filler129;
    }
    
    // Standard Setter
    public void SetFiller129(string value)
    {
        _Filler129 = value;
    }
    
    // Get<>AsString()
    public string GetFiller129AsString()
    {
        return _Filler129.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller129AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler129 = value;
    }
    
    // Standard Getter
    public int GetD91TrailerCount()
    {
        return _D91TrailerCount;
    }
    
    // Standard Setter
    public void SetD91TrailerCount(int value)
    {
        _D91TrailerCount = value;
    }
    
    // Get<>AsString()
    public string GetD91TrailerCountAsString()
    {
        return _D91TrailerCount.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD91TrailerCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D91TrailerCount = parsed;
    }
    
    // Standard Getter
    public string GetFiller130()
    {
        return _Filler130;
    }
    
    // Standard Setter
    public void SetFiller130(string value)
    {
        _Filler130 = value;
    }
    
    // Get<>AsString()
    public string GetFiller130AsString()
    {
        return _Filler130.PadRight(12);
    }
    
    // Set<>AsString()
    public void SetFiller130AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler130 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}