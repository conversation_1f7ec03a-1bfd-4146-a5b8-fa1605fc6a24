using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing BalanceIds Data Structure

public class BalanceIds
{
    private static int _size = 81;
    // [DEBUG] Class: BalanceIds, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: MbdaBalanceId, is_external=, is_static_class=False, static_prefix=
    private string _MbdaBalanceId ="";
    
    
    
    
    // [DEBUG] Field: MbdaHoldingId, is_external=, is_static_class=False, static_prefix=
    private string _MbdaHoldingId ="";
    
    
    
    
    // [DEBUG] Field: MbdaParentStockId, is_external=, is_static_class=False, static_prefix=
    private string _MbdaParentStockId ="";
    
    
    
    
    // [DEBUG] Field: Filler85, is_external=, is_static_class=False, static_prefix=
    private string _Filler85 ="";
    
    
    
    
    // [DEBUG] Field: Filler86, is_external=, is_static_class=False, static_prefix=
    private string _Filler86 ="";
    
    
    
    
    // [DEBUG] Field: Filler87, is_external=, is_static_class=False, static_prefix=
    private string _Filler87 ="";
    
    
    
    
    // [DEBUG] Field: Filler88, is_external=, is_static_class=False, static_prefix=
    private string _Filler88 ="";
    
    
    
    
    // [DEBUG] Field: MbdaDbTimestamp, is_external=, is_static_class=False, static_prefix=
    private string _MbdaDbTimestamp ="";
    
    
    
    
    // [DEBUG] Field: Filler89, is_external=, is_static_class=False, static_prefix=
    private string _Filler89 ="";
    
    
    
    
    
    // Serialization methods
    public string GetBalanceIdsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_MbdaBalanceId.PadRight(8));
        result.Append(_MbdaHoldingId.PadRight(8));
        result.Append(_MbdaParentStockId.PadRight(8));
        result.Append(_Filler85.PadRight(8));
        result.Append(_Filler86.PadRight(8));
        result.Append(_Filler87.PadRight(8));
        result.Append(_Filler88.PadRight(8));
        result.Append(_MbdaDbTimestamp.PadRight(24));
        result.Append(_Filler89.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetBalanceIdsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMbdaBalanceId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMbdaHoldingId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMbdaParentStockId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller85(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller86(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller87(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller88(extracted);
        }
        offset += 8;
        if (offset + 24 <= data.Length)
        {
            string extracted = data.Substring(offset, 24).Trim();
            SetMbdaDbTimestamp(extracted);
        }
        offset += 24;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller89(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetBalanceIdsAsString();
    }
    // Set<>String Override function
    public void SetBalanceIds(string value)
    {
        SetBalanceIdsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetMbdaBalanceId()
    {
        return _MbdaBalanceId;
    }
    
    // Standard Setter
    public void SetMbdaBalanceId(string value)
    {
        _MbdaBalanceId = value;
    }
    
    // Get<>AsString()
    public string GetMbdaBalanceIdAsString()
    {
        return _MbdaBalanceId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMbdaBalanceIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MbdaBalanceId = value;
    }
    
    // Standard Getter
    public string GetMbdaHoldingId()
    {
        return _MbdaHoldingId;
    }
    
    // Standard Setter
    public void SetMbdaHoldingId(string value)
    {
        _MbdaHoldingId = value;
    }
    
    // Get<>AsString()
    public string GetMbdaHoldingIdAsString()
    {
        return _MbdaHoldingId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMbdaHoldingIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MbdaHoldingId = value;
    }
    
    // Standard Getter
    public string GetMbdaParentStockId()
    {
        return _MbdaParentStockId;
    }
    
    // Standard Setter
    public void SetMbdaParentStockId(string value)
    {
        _MbdaParentStockId = value;
    }
    
    // Get<>AsString()
    public string GetMbdaParentStockIdAsString()
    {
        return _MbdaParentStockId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMbdaParentStockIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MbdaParentStockId = value;
    }
    
    // Standard Getter
    public string GetFiller85()
    {
        return _Filler85;
    }
    
    // Standard Setter
    public void SetFiller85(string value)
    {
        _Filler85 = value;
    }
    
    // Get<>AsString()
    public string GetFiller85AsString()
    {
        return _Filler85.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller85AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler85 = value;
    }
    
    // Standard Getter
    public string GetFiller86()
    {
        return _Filler86;
    }
    
    // Standard Setter
    public void SetFiller86(string value)
    {
        _Filler86 = value;
    }
    
    // Get<>AsString()
    public string GetFiller86AsString()
    {
        return _Filler86.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller86AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler86 = value;
    }
    
    // Standard Getter
    public string GetFiller87()
    {
        return _Filler87;
    }
    
    // Standard Setter
    public void SetFiller87(string value)
    {
        _Filler87 = value;
    }
    
    // Get<>AsString()
    public string GetFiller87AsString()
    {
        return _Filler87.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller87AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler87 = value;
    }
    
    // Standard Getter
    public string GetFiller88()
    {
        return _Filler88;
    }
    
    // Standard Setter
    public void SetFiller88(string value)
    {
        _Filler88 = value;
    }
    
    // Get<>AsString()
    public string GetFiller88AsString()
    {
        return _Filler88.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller88AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler88 = value;
    }
    
    // Standard Getter
    public string GetMbdaDbTimestamp()
    {
        return _MbdaDbTimestamp;
    }
    
    // Standard Setter
    public void SetMbdaDbTimestamp(string value)
    {
        _MbdaDbTimestamp = value;
    }
    
    // Get<>AsString()
    public string GetMbdaDbTimestampAsString()
    {
        return _MbdaDbTimestamp.PadRight(24);
    }
    
    // Set<>AsString()
    public void SetMbdaDbTimestampAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MbdaDbTimestamp = value;
    }
    
    // Standard Getter
    public string GetFiller89()
    {
        return _Filler89;
    }
    
    // Standard Setter
    public void SetFiller89(string value)
    {
        _Filler89 = value;
    }
    
    // Get<>AsString()
    public string GetFiller89AsString()
    {
        return _Filler89.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller89AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler89 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
