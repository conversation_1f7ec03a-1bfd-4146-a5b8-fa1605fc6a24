using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdailyDTO
{// DTO class representing Cgtdate2LinkageDate4 Data Structure

public class Cgtdate2LinkageDate4
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate4, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd4 =0;
    
    
    
    
    // [DEBUG] Field: Filler65, is_external=, is_static_class=False, static_prefix=
    private Filler65 _Filler65 = new Filler65();
    
    
    
    
    // [DEBUG] Field: Filler66, is_external=, is_static_class=False, static_prefix=
    private Filler66 _Filler66 = new Filler66();
    
    
    
    
    // [DEBUG] Field: Filler67, is_external=, is_static_class=False, static_prefix=
    private Filler67 _Filler67 = new Filler67();
    
    
    
    
    // [DEBUG] Field: Filler68, is_external=, is_static_class=False, static_prefix=
    private Filler68 _Filler68 = new Filler68();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate4AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd4.ToString().PadLeft(8, '0'));
        result.Append(_Filler65.GetFiller65AsString());
        result.Append(_Filler66.GetFiller66AsString());
        result.Append(_Filler67.GetFiller67AsString());
        result.Append(_Filler68.GetFiller68AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate4AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd4(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler65.SetFiller65AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler65.SetFiller65AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler66.SetFiller66AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler66.SetFiller66AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler67.SetFiller67AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler67.SetFiller67AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler68.SetFiller68AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler68.SetFiller68AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate4AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate4(string value)
    {
        SetCgtdate2LinkageDate4AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd4()
    {
        return _Cgtdate2Ccyymmdd4;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd4(int value)
    {
        _Cgtdate2Ccyymmdd4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd4AsString()
    {
        return _Cgtdate2Ccyymmdd4.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd4 = parsed;
    }
    
    // Standard Getter
    public Filler65 GetFiller65()
    {
        return _Filler65;
    }
    
    // Standard Setter
    public void SetFiller65(Filler65 value)
    {
        _Filler65 = value;
    }
    
    // Get<>AsString()
    public string GetFiller65AsString()
    {
        return _Filler65 != null ? _Filler65.GetFiller65AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller65AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler65 == null)
        {
            _Filler65 = new Filler65();
        }
        _Filler65.SetFiller65AsString(value);
    }
    
    // Standard Getter
    public Filler66 GetFiller66()
    {
        return _Filler66;
    }
    
    // Standard Setter
    public void SetFiller66(Filler66 value)
    {
        _Filler66 = value;
    }
    
    // Get<>AsString()
    public string GetFiller66AsString()
    {
        return _Filler66 != null ? _Filler66.GetFiller66AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller66AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler66 == null)
        {
            _Filler66 = new Filler66();
        }
        _Filler66.SetFiller66AsString(value);
    }
    
    // Standard Getter
    public Filler67 GetFiller67()
    {
        return _Filler67;
    }
    
    // Standard Setter
    public void SetFiller67(Filler67 value)
    {
        _Filler67 = value;
    }
    
    // Get<>AsString()
    public string GetFiller67AsString()
    {
        return _Filler67 != null ? _Filler67.GetFiller67AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller67AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler67 == null)
        {
            _Filler67 = new Filler67();
        }
        _Filler67.SetFiller67AsString(value);
    }
    
    // Standard Getter
    public Filler68 GetFiller68()
    {
        return _Filler68;
    }
    
    // Standard Setter
    public void SetFiller68(Filler68 value)
    {
        _Filler68 = value;
    }
    
    // Get<>AsString()
    public string GetFiller68AsString()
    {
        return _Filler68 != null ? _Filler68.GetFiller68AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller68AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler68 == null)
        {
            _Filler68 = new Filler68();
        }
        _Filler68.SetFiller68AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller65(string value)
    {
        _Filler65.SetFiller65AsString(value);
    }
    // Nested Class: Filler65
    public class Filler65
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc4, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc4 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd4, is_external=, is_static_class=False, static_prefix=
        private Filler65.Cgtdate2Yymmdd4 _Cgtdate2Yymmdd4 = new Filler65.Cgtdate2Yymmdd4();
        
        
        
        
    public Filler65() {}
    
    public Filler65(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc4(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd4.SetCgtdate2Yymmdd4AsString(data.Substring(offset, Cgtdate2Yymmdd4.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller65AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc4.PadRight(2));
        result.Append(_Cgtdate2Yymmdd4.GetCgtdate2Yymmdd4AsString());
        
        return result.ToString();
    }
    
    public void SetFiller65AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc4(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd4.SetCgtdate2Yymmdd4AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd4.SetCgtdate2Yymmdd4AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc4()
    {
        return _Cgtdate2Cc4;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc4(string value)
    {
        _Cgtdate2Cc4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc4AsString()
    {
        return _Cgtdate2Cc4.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc4 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd4 GetCgtdate2Yymmdd4()
    {
        return _Cgtdate2Yymmdd4;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd4(Cgtdate2Yymmdd4 value)
    {
        _Cgtdate2Yymmdd4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd4AsString()
    {
        return _Cgtdate2Yymmdd4 != null ? _Cgtdate2Yymmdd4.GetCgtdate2Yymmdd4AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd4 == null)
        {
            _Cgtdate2Yymmdd4 = new Cgtdate2Yymmdd4();
        }
        _Cgtdate2Yymmdd4.SetCgtdate2Yymmdd4AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd4
    public class Cgtdate2Yymmdd4
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy4, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy4 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd4, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd4.Cgtdate2Mmdd4 _Cgtdate2Mmdd4 = new Cgtdate2Yymmdd4.Cgtdate2Mmdd4();
        
        
        
        
    public Cgtdate2Yymmdd4() {}
    
    public Cgtdate2Yymmdd4(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy4(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd4.SetCgtdate2Mmdd4AsString(data.Substring(offset, Cgtdate2Mmdd4.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd4AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy4.PadRight(2));
        result.Append(_Cgtdate2Mmdd4.GetCgtdate2Mmdd4AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd4AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy4(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd4.SetCgtdate2Mmdd4AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd4.SetCgtdate2Mmdd4AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy4()
    {
        return _Cgtdate2Yy4;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy4(string value)
    {
        _Cgtdate2Yy4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy4AsString()
    {
        return _Cgtdate2Yy4.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy4 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd4 GetCgtdate2Mmdd4()
    {
        return _Cgtdate2Mmdd4;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd4(Cgtdate2Mmdd4 value)
    {
        _Cgtdate2Mmdd4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd4AsString()
    {
        return _Cgtdate2Mmdd4 != null ? _Cgtdate2Mmdd4.GetCgtdate2Mmdd4AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd4 == null)
        {
            _Cgtdate2Mmdd4 = new Cgtdate2Mmdd4();
        }
        _Cgtdate2Mmdd4.SetCgtdate2Mmdd4AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd4
    public class Cgtdate2Mmdd4
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm4, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm4 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd4, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd4 ="";
        
        
        
        
    public Cgtdate2Mmdd4() {}
    
    public Cgtdate2Mmdd4(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm4(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd4(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd4AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm4.PadRight(2));
        result.Append(_Cgtdate2Dd4.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd4AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm4(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd4(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm4()
    {
        return _Cgtdate2Mm4;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm4(string value)
    {
        _Cgtdate2Mm4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm4AsString()
    {
        return _Cgtdate2Mm4.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm4 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd4()
    {
        return _Cgtdate2Dd4;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd4(string value)
    {
        _Cgtdate2Dd4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd4AsString()
    {
        return _Cgtdate2Dd4.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd4 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller66(string value)
{
    _Filler66.SetFiller66AsString(value);
}
// Nested Class: Filler66
public class Filler66
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy4 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd4 =0;
    
    
    
    
public Filler66() {}

public Filler66(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy4(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd4(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller66AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy4.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd4.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller66AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy4(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd4(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy4()
{
    return _Cgtdate2CCcyy4;
}

// Standard Setter
public void SetCgtdate2CCcyy4(int value)
{
    _Cgtdate2CCcyy4 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy4AsString()
{
    return _Cgtdate2CCcyy4.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy4 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd4()
{
    return _Cgtdate2CMmdd4;
}

// Standard Setter
public void SetCgtdate2CMmdd4(int value)
{
    _Cgtdate2CMmdd4 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd4AsString()
{
    return _Cgtdate2CMmdd4.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd4 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller67(string value)
{
    _Filler67.SetFiller67AsString(value);
}
// Nested Class: Filler67
public class Filler67
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc4 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy4 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm4 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd4 =0;
    
    
    
    
public Filler67() {}

public Filler67(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc4(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy4(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm4(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd4(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller67AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc4.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy4.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm4.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd4.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller67AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc4(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy4(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm4(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd4(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc4()
{
    return _Cgtdate2CCc4;
}

// Standard Setter
public void SetCgtdate2CCc4(int value)
{
    _Cgtdate2CCc4 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc4AsString()
{
    return _Cgtdate2CCc4.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc4 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy4()
{
    return _Cgtdate2CYy4;
}

// Standard Setter
public void SetCgtdate2CYy4(int value)
{
    _Cgtdate2CYy4 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy4AsString()
{
    return _Cgtdate2CYy4.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy4 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm4()
{
    return _Cgtdate2CMm4;
}

// Standard Setter
public void SetCgtdate2CMm4(int value)
{
    _Cgtdate2CMm4 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm4AsString()
{
    return _Cgtdate2CMm4.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm4 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd4()
{
    return _Cgtdate2CDd4;
}

// Standard Setter
public void SetCgtdate2CDd4(int value)
{
    _Cgtdate2CDd4 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd4AsString()
{
    return _Cgtdate2CDd4.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd4 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller68(string value)
{
    _Filler68.SetFiller68AsString(value);
}
// Nested Class: Filler68
public class Filler68
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm4 =0;
    
    
    
    
    // [DEBUG] Field: Filler69, is_external=, is_static_class=False, static_prefix=
    private string _Filler69 ="";
    
    
    
    
public Filler68() {}

public Filler68(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm4(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller69(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller68AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm4.ToString().PadLeft(6, '0'));
    result.Append(_Filler69.PadRight(2));
    
    return result.ToString();
}

public void SetFiller68AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm4(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller69(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm4()
{
    return _Cgtdate2CCcyymm4;
}

// Standard Setter
public void SetCgtdate2CCcyymm4(int value)
{
    _Cgtdate2CCcyymm4 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm4AsString()
{
    return _Cgtdate2CCcyymm4.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm4 = parsed;
}

// Standard Getter
public string GetFiller69()
{
    return _Filler69;
}

// Standard Setter
public void SetFiller69(string value)
{
    _Filler69 = value;
}

// Get<>AsString()
public string GetFiller69AsString()
{
    return _Filler69.PadRight(2);
}

// Set<>AsString()
public void SetFiller69AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler69 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}