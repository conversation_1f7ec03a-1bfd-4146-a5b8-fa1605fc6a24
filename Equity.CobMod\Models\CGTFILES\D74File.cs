using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D74File Data Structure

public class D74File
{
    private static int _size = 12;
    // [DEBUG] Class: D74File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler232, is_external=, is_static_class=False, static_prefix=
    private string _Filler232 ="$";
    
    
    
    
    // [DEBUG] Field: D74UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D74UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler233, is_external=, is_static_class=False, static_prefix=
    private string _Filler233 ="RE";
    
    
    
    
    // [DEBUG] Field: D74ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D74ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler234, is_external=, is_static_class=False, static_prefix=
    private string _Filler234 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD74FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler232.PadRight(1));
        result.Append(_D74UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler233.PadRight(2));
        result.Append(_D74ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler234.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD74FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller232(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD74UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller233(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD74ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller234(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD74FileAsString();
    }
    // Set<>String Override function
    public void SetD74File(string value)
    {
        SetD74FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller232()
    {
        return _Filler232;
    }
    
    // Standard Setter
    public void SetFiller232(string value)
    {
        _Filler232 = value;
    }
    
    // Get<>AsString()
    public string GetFiller232AsString()
    {
        return _Filler232.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller232AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler232 = value;
    }
    
    // Standard Getter
    public int GetD74UserNo()
    {
        return _D74UserNo;
    }
    
    // Standard Setter
    public void SetD74UserNo(int value)
    {
        _D74UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD74UserNoAsString()
    {
        return _D74UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD74UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D74UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller233()
    {
        return _Filler233;
    }
    
    // Standard Setter
    public void SetFiller233(string value)
    {
        _Filler233 = value;
    }
    
    // Get<>AsString()
    public string GetFiller233AsString()
    {
        return _Filler233.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller233AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler233 = value;
    }
    
    // Standard Getter
    public int GetD74ReportNo()
    {
        return _D74ReportNo;
    }
    
    // Standard Setter
    public void SetD74ReportNo(int value)
    {
        _D74ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD74ReportNoAsString()
    {
        return _D74ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD74ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D74ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller234()
    {
        return _Filler234;
    }
    
    // Standard Setter
    public void SetFiller234(string value)
    {
        _Filler234 = value;
    }
    
    // Get<>AsString()
    public string GetFiller234AsString()
    {
        return _Filler234.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller234AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler234 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}