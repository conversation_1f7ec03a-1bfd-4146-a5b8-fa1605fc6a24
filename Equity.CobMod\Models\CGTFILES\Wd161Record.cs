using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing Wd161Record Data Structure

public class Wd161Record
{
    private static int _size = 500;
    // [DEBUG] Class: Wd161Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WD161Char, is_external=, is_static_class=False, static_prefix=
    private string[] _WD161Char = new string[500];
    
    
    
    
    
    // Serialization methods
    public string GetWd161RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 500; i++)
        {
            result.Append(_WD161Char[i].PadRight(1));
        }
        
        return result.ToString();
    }
    
    public void SetWd161RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 500; i++)
        {
            if (offset + 1 > data.Length) break;
            string val = data.Substring(offset, 1);
            
            _WD161Char[i] = val.Trim();
            offset += 1;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWd161RecordAsString();
    }
    // Set<>String Override function
    public void SetWd161Record(string value)
    {
        SetWd161RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Array Accessors for WD161Char
    public string GetWd161CharAt(int index)
    {
        return _WD161Char[index];
    }
    
    public void SetWd161CharAt(int index, string value)
    {
        _WD161Char[index] = value;
    }
    
    public string GetWd161CharAsStringAt(int index)
    {
        return _WD161Char[index].PadRight(1);
    }
    
    public void SetWd161CharAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _WD161Char[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetWd161Char()
    {
        return _WD161Char != null && _WD161Char.Length > 0
        ? _WD161Char[0]
        : default(string);
    }
    
    public void SetWd161Char(string value)
    {
        if (_WD161Char == null || _WD161Char.Length == 0)
        _WD161Char = new string[1];
        _WD161Char[0] = value;
    }
    
    public string GetWd161CharAsString()
    {
        return _WD161Char != null && _WD161Char.Length > 0
        ? _WD161Char[0].ToString()
        : string.Empty;
    }
    
    public void SetWd161CharAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_WD161Char == null || _WD161Char.Length == 0)
        _WD161Char = new string[1];
        
        _WD161Char[0] = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}