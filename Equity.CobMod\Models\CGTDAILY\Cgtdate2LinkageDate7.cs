using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdailyDTO
{// DTO class representing Cgtdate2LinkageDate7 Data Structure

public class Cgtdate2LinkageDate7
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate7, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd7 =0;
    
    
    
    
    // [DEBUG] Field: Filler80, is_external=, is_static_class=False, static_prefix=
    private Filler80 _Filler80 = new Filler80();
    
    
    
    
    // [DEBUG] Field: Filler81, is_external=, is_static_class=False, static_prefix=
    private Filler81 _Filler81 = new Filler81();
    
    
    
    
    // [DEBUG] Field: Filler82, is_external=, is_static_class=False, static_prefix=
    private Filler82 _Filler82 = new Filler82();
    
    
    
    
    // [DEBUG] Field: Filler83, is_external=, is_static_class=False, static_prefix=
    private Filler83 _Filler83 = new Filler83();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate7AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd7.ToString().PadLeft(8, '0'));
        result.Append(_Filler80.GetFiller80AsString());
        result.Append(_Filler81.GetFiller81AsString());
        result.Append(_Filler82.GetFiller82AsString());
        result.Append(_Filler83.GetFiller83AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate7AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd7(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler80.SetFiller80AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler80.SetFiller80AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler81.SetFiller81AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler81.SetFiller81AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler82.SetFiller82AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler82.SetFiller82AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler83.SetFiller83AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler83.SetFiller83AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate7AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate7(string value)
    {
        SetCgtdate2LinkageDate7AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd7()
    {
        return _Cgtdate2Ccyymmdd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd7(int value)
    {
        _Cgtdate2Ccyymmdd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd7AsString()
    {
        return _Cgtdate2Ccyymmdd7.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd7 = parsed;
    }
    
    // Standard Getter
    public Filler80 GetFiller80()
    {
        return _Filler80;
    }
    
    // Standard Setter
    public void SetFiller80(Filler80 value)
    {
        _Filler80 = value;
    }
    
    // Get<>AsString()
    public string GetFiller80AsString()
    {
        return _Filler80 != null ? _Filler80.GetFiller80AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller80AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler80 == null)
        {
            _Filler80 = new Filler80();
        }
        _Filler80.SetFiller80AsString(value);
    }
    
    // Standard Getter
    public Filler81 GetFiller81()
    {
        return _Filler81;
    }
    
    // Standard Setter
    public void SetFiller81(Filler81 value)
    {
        _Filler81 = value;
    }
    
    // Get<>AsString()
    public string GetFiller81AsString()
    {
        return _Filler81 != null ? _Filler81.GetFiller81AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller81AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler81 == null)
        {
            _Filler81 = new Filler81();
        }
        _Filler81.SetFiller81AsString(value);
    }
    
    // Standard Getter
    public Filler82 GetFiller82()
    {
        return _Filler82;
    }
    
    // Standard Setter
    public void SetFiller82(Filler82 value)
    {
        _Filler82 = value;
    }
    
    // Get<>AsString()
    public string GetFiller82AsString()
    {
        return _Filler82 != null ? _Filler82.GetFiller82AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller82AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler82 == null)
        {
            _Filler82 = new Filler82();
        }
        _Filler82.SetFiller82AsString(value);
    }
    
    // Standard Getter
    public Filler83 GetFiller83()
    {
        return _Filler83;
    }
    
    // Standard Setter
    public void SetFiller83(Filler83 value)
    {
        _Filler83 = value;
    }
    
    // Get<>AsString()
    public string GetFiller83AsString()
    {
        return _Filler83 != null ? _Filler83.GetFiller83AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller83AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler83 == null)
        {
            _Filler83 = new Filler83();
        }
        _Filler83.SetFiller83AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller80(string value)
    {
        _Filler80.SetFiller80AsString(value);
    }
    // Nested Class: Filler80
    public class Filler80
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc7 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd7, is_external=, is_static_class=False, static_prefix=
        private Filler80.Cgtdate2Yymmdd7 _Cgtdate2Yymmdd7 = new Filler80.Cgtdate2Yymmdd7();
        
        
        
        
    public Filler80() {}
    
    public Filler80(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc7(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(data.Substring(offset, Cgtdate2Yymmdd7.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller80AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc7.PadRight(2));
        result.Append(_Cgtdate2Yymmdd7.GetCgtdate2Yymmdd7AsString());
        
        return result.ToString();
    }
    
    public void SetFiller80AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc7(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc7()
    {
        return _Cgtdate2Cc7;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc7(string value)
    {
        _Cgtdate2Cc7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc7AsString()
    {
        return _Cgtdate2Cc7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc7 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd7 GetCgtdate2Yymmdd7()
    {
        return _Cgtdate2Yymmdd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd7(Cgtdate2Yymmdd7 value)
    {
        _Cgtdate2Yymmdd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd7AsString()
    {
        return _Cgtdate2Yymmdd7 != null ? _Cgtdate2Yymmdd7.GetCgtdate2Yymmdd7AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd7 == null)
        {
            _Cgtdate2Yymmdd7 = new Cgtdate2Yymmdd7();
        }
        _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd7
    public class Cgtdate2Yymmdd7
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy7 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd7, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd7.Cgtdate2Mmdd7 _Cgtdate2Mmdd7 = new Cgtdate2Yymmdd7.Cgtdate2Mmdd7();
        
        
        
        
    public Cgtdate2Yymmdd7() {}
    
    public Cgtdate2Yymmdd7(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy7(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(data.Substring(offset, Cgtdate2Mmdd7.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd7AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy7.PadRight(2));
        result.Append(_Cgtdate2Mmdd7.GetCgtdate2Mmdd7AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd7AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy7(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy7()
    {
        return _Cgtdate2Yy7;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy7(string value)
    {
        _Cgtdate2Yy7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy7AsString()
    {
        return _Cgtdate2Yy7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy7 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd7 GetCgtdate2Mmdd7()
    {
        return _Cgtdate2Mmdd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd7(Cgtdate2Mmdd7 value)
    {
        _Cgtdate2Mmdd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd7AsString()
    {
        return _Cgtdate2Mmdd7 != null ? _Cgtdate2Mmdd7.GetCgtdate2Mmdd7AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd7 == null)
        {
            _Cgtdate2Mmdd7 = new Cgtdate2Mmdd7();
        }
        _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd7
    public class Cgtdate2Mmdd7
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm7 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd7 ="";
        
        
        
        
    public Cgtdate2Mmdd7() {}
    
    public Cgtdate2Mmdd7(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm7(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd7(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd7AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm7.PadRight(2));
        result.Append(_Cgtdate2Dd7.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd7AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm7(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd7(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm7()
    {
        return _Cgtdate2Mm7;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm7(string value)
    {
        _Cgtdate2Mm7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm7AsString()
    {
        return _Cgtdate2Mm7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm7 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd7()
    {
        return _Cgtdate2Dd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd7(string value)
    {
        _Cgtdate2Dd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd7AsString()
    {
        return _Cgtdate2Dd7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd7 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller81(string value)
{
    _Filler81.SetFiller81AsString(value);
}
// Nested Class: Filler81
public class Filler81
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd7 =0;
    
    
    
    
public Filler81() {}

public Filler81(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy7(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd7(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller81AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy7.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd7.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller81AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy7(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd7(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy7()
{
    return _Cgtdate2CCcyy7;
}

// Standard Setter
public void SetCgtdate2CCcyy7(int value)
{
    _Cgtdate2CCcyy7 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy7AsString()
{
    return _Cgtdate2CCcyy7.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy7 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd7()
{
    return _Cgtdate2CMmdd7;
}

// Standard Setter
public void SetCgtdate2CMmdd7(int value)
{
    _Cgtdate2CMmdd7 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd7AsString()
{
    return _Cgtdate2CMmdd7.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd7 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller82(string value)
{
    _Filler82.SetFiller82AsString(value);
}
// Nested Class: Filler82
public class Filler82
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd7 =0;
    
    
    
    
public Filler82() {}

public Filler82(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller82AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc7.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy7.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm7.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd7.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller82AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc7(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy7(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm7(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd7(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc7()
{
    return _Cgtdate2CCc7;
}

// Standard Setter
public void SetCgtdate2CCc7(int value)
{
    _Cgtdate2CCc7 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc7AsString()
{
    return _Cgtdate2CCc7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc7 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy7()
{
    return _Cgtdate2CYy7;
}

// Standard Setter
public void SetCgtdate2CYy7(int value)
{
    _Cgtdate2CYy7 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy7AsString()
{
    return _Cgtdate2CYy7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy7 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm7()
{
    return _Cgtdate2CMm7;
}

// Standard Setter
public void SetCgtdate2CMm7(int value)
{
    _Cgtdate2CMm7 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm7AsString()
{
    return _Cgtdate2CMm7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm7 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd7()
{
    return _Cgtdate2CDd7;
}

// Standard Setter
public void SetCgtdate2CDd7(int value)
{
    _Cgtdate2CDd7 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd7AsString()
{
    return _Cgtdate2CDd7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd7 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller83(string value)
{
    _Filler83.SetFiller83AsString(value);
}
// Nested Class: Filler83
public class Filler83
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm7 =0;
    
    
    
    
    // [DEBUG] Field: Filler84, is_external=, is_static_class=False, static_prefix=
    private string _Filler84 ="";
    
    
    
    
public Filler83() {}

public Filler83(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm7(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller84(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller83AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm7.ToString().PadLeft(6, '0'));
    result.Append(_Filler84.PadRight(2));
    
    return result.ToString();
}

public void SetFiller83AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm7(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller84(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm7()
{
    return _Cgtdate2CCcyymm7;
}

// Standard Setter
public void SetCgtdate2CCcyymm7(int value)
{
    _Cgtdate2CCcyymm7 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm7AsString()
{
    return _Cgtdate2CCcyymm7.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm7 = parsed;
}

// Standard Getter
public string GetFiller84()
{
    return _Filler84;
}

// Standard Setter
public void SetFiller84(string value)
{
    _Filler84 = value;
}

// Get<>AsString()
public string GetFiller84AsString()
{
    return _Filler84.PadRight(2);
}

// Set<>AsString()
public void SetFiller84AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler84 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}