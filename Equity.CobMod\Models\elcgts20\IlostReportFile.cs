using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing IlostReportFile Data Structure

public class IlostReportFile
{
    private static int _size = 12;
    // [DEBUG] Class: IlostReportFile, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: IlostUserNo, is_external=, is_static_class=False, static_prefix=
    private string _IlostUserNo ="";
    
    
    
    
    // [DEBUG] Field: Filler106, is_external=, is_static_class=False, static_prefix=
    private string _Filler106 ="RM";
    
    
    
    
    // [DEBUG] Field: IlostReportNo, is_external=, is_static_class=False, static_prefix=
    private int _IlostReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler107, is_external=, is_static_class=False, static_prefix=
    private string _Filler107 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetIlostReportFileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_IlostUserNo.PadRight(5));
        result.Append(_Filler106.PadRight(2));
        result.Append(_IlostReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler107.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetIlostReportFileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            SetIlostUserNo(extracted);
        }
        offset += 5;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller106(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetIlostReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller107(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetIlostReportFileAsString();
    }
    // Set<>String Override function
    public void SetIlostReportFile(string value)
    {
        SetIlostReportFileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetIlostUserNo()
    {
        return _IlostUserNo;
    }
    
    // Standard Setter
    public void SetIlostUserNo(string value)
    {
        _IlostUserNo = value;
    }
    
    // Get<>AsString()
    public string GetIlostUserNoAsString()
    {
        return _IlostUserNo.PadRight(5);
    }
    
    // Set<>AsString()
    public void SetIlostUserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _IlostUserNo = value;
    }
    
    // Standard Getter
    public string GetFiller106()
    {
        return _Filler106;
    }
    
    // Standard Setter
    public void SetFiller106(string value)
    {
        _Filler106 = value;
    }
    
    // Get<>AsString()
    public string GetFiller106AsString()
    {
        return _Filler106.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller106AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler106 = value;
    }
    
    // Standard Getter
    public int GetIlostReportNo()
    {
        return _IlostReportNo;
    }
    
    // Standard Setter
    public void SetIlostReportNo(int value)
    {
        _IlostReportNo = value;
    }
    
    // Get<>AsString()
    public string GetIlostReportNoAsString()
    {
        return _IlostReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetIlostReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _IlostReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller107()
    {
        return _Filler107;
    }
    
    // Standard Setter
    public void SetFiller107(string value)
    {
        _Filler107 = value;
    }
    
    // Get<>AsString()
    public string GetFiller107AsString()
    {
        return _Filler107.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller107AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler107 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
