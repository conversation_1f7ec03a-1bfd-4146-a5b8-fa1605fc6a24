using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WsRunControl Data Structure

public class WsRunControl
{
    private static int _size = 21;
    // [DEBUG] Class: WsRunControl, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsParmChars, is_external=, is_static_class=False, static_prefix=
    private WsParmChars _WsParmChars = new WsParmChars();
    
    
    
    
    // [DEBUG] Field: Filler482, is_external=, is_static_class=False, static_prefix=
    private Filler482 _Filler482 = new Filler482();
    
    
    
    
    
    // Serialization methods
    public string GetWsRunControlAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsParmChars.GetWsParmCharsAsString());
        result.Append(_Filler482.GetFiller482AsString());
        
        return result.ToString();
    }
    
    public void SetWsRunControlAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 20 <= data.Length)
        {
            _WsParmChars.SetWsParmCharsAsString(data.Substring(offset, 20));
        }
        else
        {
            _WsParmChars.SetWsParmCharsAsString(data.Substring(offset));
        }
        offset += 20;
        if (offset + 1 <= data.Length)
        {
            _Filler482.SetFiller482AsString(data.Substring(offset, 1));
        }
        else
        {
            _Filler482.SetFiller482AsString(data.Substring(offset));
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsRunControlAsString();
    }
    // Set<>String Override function
    public void SetWsRunControl(string value)
    {
        SetWsRunControlAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public WsParmChars GetWsParmChars()
    {
        return _WsParmChars;
    }
    
    // Standard Setter
    public void SetWsParmChars(WsParmChars value)
    {
        _WsParmChars = value;
    }
    
    // Get<>AsString()
    public string GetWsParmCharsAsString()
    {
        return _WsParmChars != null ? _WsParmChars.GetWsParmCharsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsParmCharsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsParmChars == null)
        {
            _WsParmChars = new WsParmChars();
        }
        _WsParmChars.SetWsParmCharsAsString(value);
    }
    
    // Standard Getter
    public Filler482 GetFiller482()
    {
        return _Filler482;
    }
    
    // Standard Setter
    public void SetFiller482(Filler482 value)
    {
        _Filler482 = value;
    }
    
    // Get<>AsString()
    public string GetFiller482AsString()
    {
        return _Filler482 != null ? _Filler482.GetFiller482AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller482AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler482 == null)
        {
            _Filler482 = new Filler482();
        }
        _Filler482.SetFiller482AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWsParmChars(string value)
    {
        _WsParmChars.SetWsParmCharsAsString(value);
    }
    // Nested Class: WsParmChars
    public class WsParmChars
    {
        private static int _size = 20;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WsParmChar1, is_external=, is_static_class=False, static_prefix=
        private string _WsParmChar1 =" ";
        
        
        // 88-level condition checks for WsParmChar1
        public bool IsNormalRun()
        {
            if (this._WsParmChar1 == "' '") return true;
            if (this._WsParmChar1 == "'P'") return true;
            if (this._WsParmChar1 == "'S'") return true;
            if (this._WsParmChar1 == "'T'") return true;
            if (this._WsParmChar1 == "'X'") return true;
            return false;
        }
        public bool IsSuppressBalances()
        {
            if (this._WsParmChar1 == "'S'") return true;
            if (this._WsParmChar1 == "'X'") return true;
            return false;
        }
        public bool IsSuppressTranches()
        {
            if (this._WsParmChar1 == "'S'") return true;
            return false;
        }
        public bool IsPartialRun()
        {
            if (this._WsParmChar1 == "'P'") return true;
            if (this._WsParmChar1 == "'4'") return true;
            return false;
        }
        public bool IsTestRun()
        {
            if (this._WsParmChar1 == "'T'") return true;
            return false;
        }
        public bool IsSpeedyRun()
        {
            if (this._WsParmChar1 == "' '") return true;
            return false;
        }
        public bool IsFirstCall()
        {
            if (this._WsParmChar1 == "'1'") return true;
            return false;
        }
        public bool IsCalcCall()
        {
            if (this._WsParmChar1 == "'2'") return true;
            if (this._WsParmChar1 == "'3'") return true;
            if (this._WsParmChar1 == "'4'") return true;
            if (this._WsParmChar1 == "'5'") return true;
            if (this._WsParmChar1 == "'6'") return true;
            return false;
        }
        public bool IsSingleSedolCalc()
        {
            if (this._WsParmChar1 == "'2'") return true;
            return false;
        }
        public bool IsCalcWithoutPrint()
        {
            if (this._WsParmChar1 == "'3'") return true;
            return false;
        }
        public bool IsCalcEvery()
        {
            if (this._WsParmChar1 == "'4'") return true;
            return false;
        }
        public bool IsCalcTrace()
        {
            if (this._WsParmChar1 == "'5'") return true;
            return false;
        }
        public bool IsXFundCalc()
        {
            if (this._WsParmChar1 == "'6'") return true;
            return false;
        }
        public bool IsOpenRpt()
        {
            if (this._WsParmChar1 == "'7'") return true;
            return false;
        }
        public bool IsCloseRpt()
        {
            if (this._WsParmChar1 == "'8'") return true;
            return false;
        }
        public bool IsLastCall()
        {
            if (this._WsParmChar1 == "'9'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WsParmChar2, is_external=, is_static_class=False, static_prefix=
        private string _WsParmChar2 =" ";
        
        
        // 88-level condition checks for WsParmChar2
        public bool IsPrintRealSchedule()
        {
            if (this._WsParmChar2 == "' '") return true;
            if (this._WsParmChar2 == "'R'") return true;
            return false;
        }
        public bool IsPrintUnrealSchedule()
        {
            if (this._WsParmChar2 == "' '") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WsParmChar3, is_external=, is_static_class=False, static_prefix=
        private string _WsParmChar3 ="X";
        
        
        // 88-level condition checks for WsParmChar3
        public bool IsNoBkvals()
        {
            if (this._WsParmChar3 == "'N'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WsParmChar4, is_external=, is_static_class=False, static_prefix=
        private string _WsParmChar4 =" ";
        
        
        // 88-level condition checks for WsParmChar4
        public bool IsRightsPartialDisposal()
        {
            if (this._WsParmChar4 == "'R'") return true;
            if (this._WsParmChar4 == "'X'") return true;
            return false;
        }
        public bool IsPoolAlwaysIndexed()
        {
            if (this._WsParmChar4 == "'P'") return true;
            if (this._WsParmChar4 == "'X'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WsParmChar5, is_external=, is_static_class=False, static_prefix=
        private string _WsParmChar5 =" ";
        
        
        // 88-level condition checks for WsParmChar5
        public bool IsTestPricePartDisposal()
        {
            if (this._WsParmChar5 == "'T'") return true;
            return false;
        }
        public bool IsIndexPoolNotRounded()
        {
            if (this._WsParmChar5 == "'I'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WsParmChar6, is_external=, is_static_class=False, static_prefix=
        private string _WsParmChar6 =" ";
        
        
        // 88-level condition checks for WsParmChar6
        public bool IsMicroIo()
        {
            if (this._WsParmChar6 == "'M'") return true;
            return false;
        }
        public bool IsTrialIo()
        {
            if (this._WsParmChar6 == "'T'") return true;
            return false;
        }
        public bool IsStandardIo()
        {
            if (this._WsParmChar6 == "'S'") return true;
            return false;
        }
        public bool IsStandardMf()
        {
            if (this._WsParmChar6 == "'I'") return true;
            if (this._WsParmChar6 == "'S'") return true;
            if (this._WsParmChar6 == "'T'") return true;
            return false;
        }
        public bool IsIndexedFundFile()
        {
            if (this._WsParmChar6 == "'I'") return true;
            if (this._WsParmChar6 == "'M'") return true;
            if (this._WsParmChar6 == "'T'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WsParmChar7, is_external=, is_static_class=False, static_prefix=
        private string _WsParmChar7 =" ";
        
        
        // 88-level condition checks for WsParmChar7
        public bool IsGlReportRequested()
        {
            if (this._WsParmChar7 == "'Y'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WsParmChar8, is_external=, is_static_class=False, static_prefix=
        private string _WsParmChar8 =" ";
        
        
        // 88-level condition checks for WsParmChar8
        public bool IsDeemedDisposalsOn()
        {
            if (this._WsParmChar8 == "'Y'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WsParmChar9, is_external=, is_static_class=False, static_prefix=
        private string _WsParmChar9 =" ";
        
        
        // 88-level condition checks for WsParmChar9
        public bool IsAddLiabToNipiProceedsFlag()
        {
            if (this._WsParmChar9 == "'Y'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WsParmChar10, is_external=, is_static_class=False, static_prefix=
        private string _WsParmChar10 =" ";
        
        
        // 88-level condition checks for WsParmChar10
        public bool IsDerivativesLicensed()
        {
            if (this._WsParmChar10 == "'Y'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WsParmChar11, is_external=, is_static_class=False, static_prefix=
        private string _WsParmChar11 =" ";
        
        
        // 88-level condition checks for WsParmChar11
        public bool IsIrishCgtLicensed()
        {
            if (this._WsParmChar11 == "'Y'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WsParmChar12, is_external=, is_static_class=False, static_prefix=
        private string _WsParmChar12 =" ";
        
        
        // 88-level condition checks for WsParmChar12
        public bool IsIrishCgtProRata()
        {
            if (this._WsParmChar12 == "'Y'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: Filler481, is_external=, is_static_class=False, static_prefix=
        private string _Filler481 ="";
        
        
        
        
        // [DEBUG] Field: WsParmFn, is_external=, is_static_class=False, static_prefix=
        private WsParmChars.WsParmFn _WsParmFn = new WsParmChars.WsParmFn();
        
        
        
        
    public WsParmChars() {}
    
    public WsParmChars(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWsParmChar1(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsParmChar2(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsParmChar3(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsParmChar4(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsParmChar5(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsParmChar6(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsParmChar7(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsParmChar8(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsParmChar9(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsParmChar10(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsParmChar11(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsParmChar12(data.Substring(offset, 1).Trim());
        offset += 1;
        SetFiller481(data.Substring(offset, 0).Trim());
        offset += 0;
        _WsParmFn.SetWsParmFnAsString(data.Substring(offset, WsParmFn.GetSize()));
        offset += 8;
        
    }
    
    // Serialization methods
    public string GetWsParmCharsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsParmChar1.PadRight(1));
        result.Append(_WsParmChar2.PadRight(1));
        result.Append(_WsParmChar3.PadRight(1));
        result.Append(_WsParmChar4.PadRight(1));
        result.Append(_WsParmChar5.PadRight(1));
        result.Append(_WsParmChar6.PadRight(1));
        result.Append(_WsParmChar7.PadRight(1));
        result.Append(_WsParmChar8.PadRight(1));
        result.Append(_WsParmChar9.PadRight(1));
        result.Append(_WsParmChar10.PadRight(1));
        result.Append(_WsParmChar11.PadRight(1));
        result.Append(_WsParmChar12.PadRight(1));
        result.Append(_Filler481.PadRight(0));
        result.Append(_WsParmFn.GetWsParmFnAsString());
        
        return result.ToString();
    }
    
    public void SetWsParmCharsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsParmChar1(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsParmChar2(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsParmChar3(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsParmChar4(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsParmChar5(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsParmChar6(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsParmChar7(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsParmChar8(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsParmChar9(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsParmChar10(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsParmChar11(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsParmChar12(extracted);
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller481(extracted);
        }
        offset += 0;
        if (offset + 8 <= data.Length)
        {
            _WsParmFn.SetWsParmFnAsString(data.Substring(offset, 8));
        }
        else
        {
            _WsParmFn.SetWsParmFnAsString(data.Substring(offset));
        }
        offset += 8;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsParmChar1()
    {
        return _WsParmChar1;
    }
    
    // Standard Setter
    public void SetWsParmChar1(string value)
    {
        _WsParmChar1 = value;
    }
    
    // Get<>AsString()
    public string GetWsParmChar1AsString()
    {
        return _WsParmChar1.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsParmChar1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsParmChar1 = value;
    }
    
    // Standard Getter
    public string GetWsParmChar2()
    {
        return _WsParmChar2;
    }
    
    // Standard Setter
    public void SetWsParmChar2(string value)
    {
        _WsParmChar2 = value;
    }
    
    // Get<>AsString()
    public string GetWsParmChar2AsString()
    {
        return _WsParmChar2.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsParmChar2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsParmChar2 = value;
    }
    
    // Standard Getter
    public string GetWsParmChar3()
    {
        return _WsParmChar3;
    }
    
    // Standard Setter
    public void SetWsParmChar3(string value)
    {
        _WsParmChar3 = value;
    }
    
    // Get<>AsString()
    public string GetWsParmChar3AsString()
    {
        return _WsParmChar3.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsParmChar3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsParmChar3 = value;
    }
    
    // Standard Getter
    public string GetWsParmChar4()
    {
        return _WsParmChar4;
    }
    
    // Standard Setter
    public void SetWsParmChar4(string value)
    {
        _WsParmChar4 = value;
    }
    
    // Get<>AsString()
    public string GetWsParmChar4AsString()
    {
        return _WsParmChar4.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsParmChar4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsParmChar4 = value;
    }
    
    // Standard Getter
    public string GetWsParmChar5()
    {
        return _WsParmChar5;
    }
    
    // Standard Setter
    public void SetWsParmChar5(string value)
    {
        _WsParmChar5 = value;
    }
    
    // Get<>AsString()
    public string GetWsParmChar5AsString()
    {
        return _WsParmChar5.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsParmChar5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsParmChar5 = value;
    }
    
    // Standard Getter
    public string GetWsParmChar6()
    {
        return _WsParmChar6;
    }
    
    // Standard Setter
    public void SetWsParmChar6(string value)
    {
        _WsParmChar6 = value;
    }
    
    // Get<>AsString()
    public string GetWsParmChar6AsString()
    {
        return _WsParmChar6.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsParmChar6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsParmChar6 = value;
    }
    
    // Standard Getter
    public string GetWsParmChar7()
    {
        return _WsParmChar7;
    }
    
    // Standard Setter
    public void SetWsParmChar7(string value)
    {
        _WsParmChar7 = value;
    }
    
    // Get<>AsString()
    public string GetWsParmChar7AsString()
    {
        return _WsParmChar7.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsParmChar7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsParmChar7 = value;
    }
    
    // Standard Getter
    public string GetWsParmChar8()
    {
        return _WsParmChar8;
    }
    
    // Standard Setter
    public void SetWsParmChar8(string value)
    {
        _WsParmChar8 = value;
    }
    
    // Get<>AsString()
    public string GetWsParmChar8AsString()
    {
        return _WsParmChar8.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsParmChar8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsParmChar8 = value;
    }
    
    // Standard Getter
    public string GetWsParmChar9()
    {
        return _WsParmChar9;
    }
    
    // Standard Setter
    public void SetWsParmChar9(string value)
    {
        _WsParmChar9 = value;
    }
    
    // Get<>AsString()
    public string GetWsParmChar9AsString()
    {
        return _WsParmChar9.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsParmChar9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsParmChar9 = value;
    }
    
    // Standard Getter
    public string GetWsParmChar10()
    {
        return _WsParmChar10;
    }
    
    // Standard Setter
    public void SetWsParmChar10(string value)
    {
        _WsParmChar10 = value;
    }
    
    // Get<>AsString()
    public string GetWsParmChar10AsString()
    {
        return _WsParmChar10.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsParmChar10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsParmChar10 = value;
    }
    
    // Standard Getter
    public string GetWsParmChar11()
    {
        return _WsParmChar11;
    }
    
    // Standard Setter
    public void SetWsParmChar11(string value)
    {
        _WsParmChar11 = value;
    }
    
    // Get<>AsString()
    public string GetWsParmChar11AsString()
    {
        return _WsParmChar11.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsParmChar11AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsParmChar11 = value;
    }
    
    // Standard Getter
    public string GetWsParmChar12()
    {
        return _WsParmChar12;
    }
    
    // Standard Setter
    public void SetWsParmChar12(string value)
    {
        _WsParmChar12 = value;
    }
    
    // Get<>AsString()
    public string GetWsParmChar12AsString()
    {
        return _WsParmChar12.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsParmChar12AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsParmChar12 = value;
    }
    
    // Standard Getter
    public string GetFiller481()
    {
        return _Filler481;
    }
    
    // Standard Setter
    public void SetFiller481(string value)
    {
        _Filler481 = value;
    }
    
    // Get<>AsString()
    public string GetFiller481AsString()
    {
        return _Filler481.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller481AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler481 = value;
    }
    
    // Standard Getter
    public WsParmFn GetWsParmFn()
    {
        return _WsParmFn;
    }
    
    // Standard Setter
    public void SetWsParmFn(WsParmFn value)
    {
        _WsParmFn = value;
    }
    
    // Get<>AsString()
    public string GetWsParmFnAsString()
    {
        return _WsParmFn != null ? _WsParmFn.GetWsParmFnAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsParmFnAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsParmFn == null)
        {
            _WsParmFn = new WsParmFn();
        }
        _WsParmFn.SetWsParmFnAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WsParmFn
    public class WsParmFn
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WsParmFn1To5, is_external=, is_static_class=False, static_prefix=
        private string _WsParmFn1To5 ="[USER";
        
        
        
        
        // [DEBUG] Field: WsParmFn8, is_external=, is_static_class=False, static_prefix=
        private string _WsParmFn8 ="N";
        
        
        
        
        // [DEBUG] Field: WsParmYy, is_external=, is_static_class=False, static_prefix=
        private string _WsParmYy ="YY";
        
        
        
        
    public WsParmFn() {}
    
    public WsParmFn(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWsParmFn1To5(data.Substring(offset, 5).Trim());
        offset += 5;
        SetWsParmFn8(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsParmYy(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetWsParmFnAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsParmFn1To5.PadRight(5));
        result.Append(_WsParmFn8.PadRight(1));
        result.Append(_WsParmYy.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetWsParmFnAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            SetWsParmFn1To5(extracted);
        }
        offset += 5;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsParmFn8(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetWsParmYy(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsParmFn1To5()
    {
        return _WsParmFn1To5;
    }
    
    // Standard Setter
    public void SetWsParmFn1To5(string value)
    {
        _WsParmFn1To5 = value;
    }
    
    // Get<>AsString()
    public string GetWsParmFn1To5AsString()
    {
        return _WsParmFn1To5.PadRight(5);
    }
    
    // Set<>AsString()
    public void SetWsParmFn1To5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsParmFn1To5 = value;
    }
    
    // Standard Getter
    public string GetWsParmFn8()
    {
        return _WsParmFn8;
    }
    
    // Standard Setter
    public void SetWsParmFn8(string value)
    {
        _WsParmFn8 = value;
    }
    
    // Get<>AsString()
    public string GetWsParmFn8AsString()
    {
        return _WsParmFn8.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsParmFn8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsParmFn8 = value;
    }
    
    // Standard Getter
    public string GetWsParmYy()
    {
        return _WsParmYy;
    }
    
    // Standard Setter
    public void SetWsParmYy(string value)
    {
        _WsParmYy = value;
    }
    
    // Get<>AsString()
    public string GetWsParmYyAsString()
    {
        return _WsParmYy.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetWsParmYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsParmYy = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
// Set<>String Override function (Nested)
public void SetFiller482(string value)
{
    _Filler482.SetFiller482AsString(value);
}
// Nested Class: Filler482
public class Filler482
{
    private static int _size = 1;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsParmCh, is_external=, is_static_class=False, static_prefix=
    private string[] _WsParmCh = new string[24];
    
    
    
    
public Filler482() {}

public Filler482(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    for (int i = 0; i < 24; i++)
    {
        string value = data.Substring(offset, 1);
        _WsParmCh[i] = value.Trim();
        offset += 1;
    }
    
}

// Serialization methods
public string GetFiller482AsString()
{
    StringBuilder result = new StringBuilder();
    
    for (int i = 0; i < 24; i++)
    {
        result.Append(_WsParmCh[i].PadRight(1));
    }
    
    return result.ToString();
}

public void SetFiller482AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    for (int i = 0; i < 24; i++)
    {
        if (offset + 1 > data.Length) break;
        string val = data.Substring(offset, 1);
        
        _WsParmCh[i] = val.Trim();
        offset += 1;
    }
}

// Getter and Setter methods

// Array Accessors for WsParmCh
public string GetWsParmChAt(int index)
{
    return _WsParmCh[index];
}

public void SetWsParmChAt(int index, string value)
{
    _WsParmCh[index] = value;
}

public string GetWsParmChAsStringAt(int index)
{
    return _WsParmCh[index].PadRight(1);
}

public void SetWsParmChAsStringAt(int index, string value)
{
    if (string.IsNullOrEmpty(value)) return;
    _WsParmCh[index] = value;
}

// Flattened accessors (index 0)
public string GetWsParmCh()
{
    return _WsParmCh != null && _WsParmCh.Length > 0
    ? _WsParmCh[0]
    : default(string);
}

public void SetWsParmCh(string value)
{
    if (_WsParmCh == null || _WsParmCh.Length == 0)
    _WsParmCh = new string[1];
    _WsParmCh[0] = value;
}

public string GetWsParmChAsString()
{
    return _WsParmCh != null && _WsParmCh.Length > 0
    ? _WsParmCh[0].ToString()
    : string.Empty;
}

public void SetWsParmChAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    if (_WsParmCh == null || _WsParmCh.Length == 0)
    _WsParmCh = new string[1];
    
    _WsParmCh[0] = value;
}




public static int GetSize()
{
    return _size;
}

}

}}