using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtIndexCost Data Structure

public class WtIndexCost
{
    private static int _size = 28;
    // [DEBUG] Class: WtIndexCost, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler443, is_external=, is_static_class=False, static_prefix=
    private string _Filler443 ="INDEX-COST======";
    
    
    
    
    // [DEBUG] Field: WticMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WticMaxTableSize =4000;
    
    
    
    
    // [DEBUG] Field: WticOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WticOccurs =4000;
    
    
    
    
    // [DEBUG] Field: WticTable, is_external=, is_static_class=False, static_prefix=
    private WticTable _WticTable = new WticTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtIndexCostAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler443.PadRight(16));
        result.Append(_WticMaxTableSize.ToString().PadLeft(4, '0'));
        result.Append(_WticOccurs.ToString().PadLeft(4, '0'));
        result.Append(_WticTable.GetWticTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtIndexCostAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller443(extracted);
        }
        offset += 16;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWticMaxTableSize(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWticOccurs(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            _WticTable.SetWticTableAsString(data.Substring(offset, 4));
        }
        else
        {
            _WticTable.SetWticTableAsString(data.Substring(offset));
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtIndexCostAsString();
    }
    // Set<>String Override function
    public void SetWtIndexCost(string value)
    {
        SetWtIndexCostAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller443()
    {
        return _Filler443;
    }
    
    // Standard Setter
    public void SetFiller443(string value)
    {
        _Filler443 = value;
    }
    
    // Get<>AsString()
    public string GetFiller443AsString()
    {
        return _Filler443.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller443AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler443 = value;
    }
    
    // Standard Getter
    public int GetWticMaxTableSize()
    {
        return _WticMaxTableSize;
    }
    
    // Standard Setter
    public void SetWticMaxTableSize(int value)
    {
        _WticMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWticMaxTableSizeAsString()
    {
        return _WticMaxTableSize.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWticMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WticMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWticOccurs()
    {
        return _WticOccurs;
    }
    
    // Standard Setter
    public void SetWticOccurs(int value)
    {
        _WticOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWticOccursAsString()
    {
        return _WticOccurs.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWticOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WticOccurs = parsed;
    }
    
    // Standard Getter
    public WticTable GetWticTable()
    {
        return _WticTable;
    }
    
    // Standard Setter
    public void SetWticTable(WticTable value)
    {
        _WticTable = value;
    }
    
    // Get<>AsString()
    public string GetWticTableAsString()
    {
        return _WticTable != null ? _WticTable.GetWticTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWticTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WticTable == null)
        {
            _WticTable = new WticTable();
        }
        _WticTable.SetWticTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWticTable(string value)
    {
        _WticTable.SetWticTableAsString(value);
    }
    // Nested Class: WticTable
    public class WticTable
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WticIndex, is_external=, is_static_class=False, static_prefix=
        private string[] _WticIndex = new string[4000];
        
        
        
        
    public WticTable() {}
    
    public WticTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        for (int i = 0; i < 4000; i++)
        {
            string value = data.Substring(offset, 4);
            _WticIndex[i] = value.Trim();
            offset += 4;
        }
        
    }
    
    // Serialization methods
    public string GetWticTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 4000; i++)
        {
            result.Append(_WticIndex[i].PadRight(4));
        }
        
        return result.ToString();
    }
    
    public void SetWticTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 4000; i++)
        {
            if (offset + 4 > data.Length) break;
            string val = data.Substring(offset, 4);
            
            _WticIndex[i] = val.Trim();
            offset += 4;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WticIndex
    public string GetWticIndexAt(int index)
    {
        return _WticIndex[index];
    }
    
    public void SetWticIndexAt(int index, string value)
    {
        _WticIndex[index] = value;
    }
    
    public string GetWticIndexAsStringAt(int index)
    {
        return _WticIndex[index].PadRight(4);
    }
    
    public void SetWticIndexAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _WticIndex[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetWticIndex()
    {
        return _WticIndex != null && _WticIndex.Length > 0
        ? _WticIndex[0]
        : default(string);
    }
    
    public void SetWticIndex(string value)
    {
        if (_WticIndex == null || _WticIndex.Length == 0)
        _WticIndex = new string[1];
        _WticIndex[0] = value;
    }
    
    public string GetWticIndexAsString()
    {
        return _WticIndex != null && _WticIndex.Length > 0
        ? _WticIndex[0].ToString()
        : string.Empty;
    }
    
    public void SetWticIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_WticIndex == null || _WticIndex.Length == 0)
        _WticIndex = new string[1];
        
        _WticIndex[0] = value;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}
