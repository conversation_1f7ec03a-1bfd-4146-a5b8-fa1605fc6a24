using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtFundRecords Data Structure

public class WtFundRecords
{
    private static int _size = 104;
    // [DEBUG] Class: WtFundRecords, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler477, is_external=, is_static_class=False, static_prefix=
    private string _Filler477 ="FUND RECORD TABLE===============";
    
    
    
    
    // [DEBUG] Field: WtfrMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtfrMaxTableSize =800;
    
    
    
    
    // [DEBUG] Field: WtfrOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtfrOccurs =0;
    
    
    
    
    // [DEBUG] Field: WtfrFundRecordTable, is_external=, is_static_class=False, static_prefix=
    private WtfrFundRecordTable _WtfrFundRecordTable = new WtfrFundRecordTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtFundRecordsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler477.PadRight(32));
        result.Append(_WtfrMaxTableSize.ToString().PadLeft(4, '0'));
        result.Append(_WtfrOccurs.ToString().PadLeft(4, '0'));
        result.Append(_WtfrFundRecordTable.GetWtfrFundRecordTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtFundRecordsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 32 <= data.Length)
        {
            string extracted = data.Substring(offset, 32).Trim();
            SetFiller477(extracted);
        }
        offset += 32;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtfrMaxTableSize(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtfrOccurs(parsedInt);
        }
        offset += 4;
        if (offset + 64 <= data.Length)
        {
            _WtfrFundRecordTable.SetWtfrFundRecordTableAsString(data.Substring(offset, 64));
        }
        else
        {
            _WtfrFundRecordTable.SetWtfrFundRecordTableAsString(data.Substring(offset));
        }
        offset += 64;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtFundRecordsAsString();
    }
    // Set<>String Override function
    public void SetWtFundRecords(string value)
    {
        SetWtFundRecordsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller477()
    {
        return _Filler477;
    }
    
    // Standard Setter
    public void SetFiller477(string value)
    {
        _Filler477 = value;
    }
    
    // Get<>AsString()
    public string GetFiller477AsString()
    {
        return _Filler477.PadRight(32);
    }
    
    // Set<>AsString()
    public void SetFiller477AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler477 = value;
    }
    
    // Standard Getter
    public int GetWtfrMaxTableSize()
    {
        return _WtfrMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtfrMaxTableSize(int value)
    {
        _WtfrMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtfrMaxTableSizeAsString()
    {
        return _WtfrMaxTableSize.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtfrMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtfrMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtfrOccurs()
    {
        return _WtfrOccurs;
    }
    
    // Standard Setter
    public void SetWtfrOccurs(int value)
    {
        _WtfrOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtfrOccursAsString()
    {
        return _WtfrOccurs.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtfrOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtfrOccurs = parsed;
    }
    
    // Standard Getter
    public WtfrFundRecordTable GetWtfrFundRecordTable()
    {
        return _WtfrFundRecordTable;
    }
    
    // Standard Setter
    public void SetWtfrFundRecordTable(WtfrFundRecordTable value)
    {
        _WtfrFundRecordTable = value;
    }
    
    // Get<>AsString()
    public string GetWtfrFundRecordTableAsString()
    {
        return _WtfrFundRecordTable != null ? _WtfrFundRecordTable.GetWtfrFundRecordTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtfrFundRecordTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtfrFundRecordTable == null)
        {
            _WtfrFundRecordTable = new WtfrFundRecordTable();
        }
        _WtfrFundRecordTable.SetWtfrFundRecordTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtfrFundRecordTable(string value)
    {
        _WtfrFundRecordTable.SetWtfrFundRecordTableAsString(value);
    }
    // Nested Class: WtfrFundRecordTable
    public class WtfrFundRecordTable
    {
        private static int _size = 64;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtfrElement, is_external=, is_static_class=False, static_prefix=
        private WtfrFundRecordTable.WtfrElement[] _WtfrElement = new WtfrFundRecordTable.WtfrElement[800];
        
        public void InitializeWtfrElementArray()
        {
            for (int i = 0; i < 800; i++)
            {
                _WtfrElement[i] = new WtfrFundRecordTable.WtfrElement();
            }
        }
        
        
        
    public WtfrFundRecordTable() {}
    
    public WtfrFundRecordTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWtfrElementArray();
        for (int i = 0; i < 800; i++)
        {
            _WtfrElement[i].SetWtfrElementAsString(data.Substring(offset, 64));
            offset += 64;
        }
        
    }
    
    // Serialization methods
    public string GetWtfrFundRecordTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 800; i++)
        {
            result.Append(_WtfrElement[i].GetWtfrElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtfrFundRecordTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 800; i++)
        {
            if (offset + 64 > data.Length) break;
            string val = data.Substring(offset, 64);
            
            _WtfrElement[i].SetWtfrElementAsString(val);
            offset += 64;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtfrElement
    public WtfrElement GetWtfrElementAt(int index)
    {
        return _WtfrElement[index];
    }
    
    public void SetWtfrElementAt(int index, WtfrElement value)
    {
        _WtfrElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtfrElement GetWtfrElement()
    {
        return _WtfrElement != null && _WtfrElement.Length > 0
        ? _WtfrElement[0]
        : new WtfrElement();
    }
    
    public void SetWtfrElement(WtfrElement value)
    {
        if (_WtfrElement == null || _WtfrElement.Length == 0)
        _WtfrElement = new WtfrElement[1];
        _WtfrElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtfrElement
    public class WtfrElement
    {
        private static int _size = 64;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtfrKey, is_external=, is_static_class=False, static_prefix=
        private WtfrElement.WtfrKey _WtfrKey = new WtfrElement.WtfrKey();
        
        
        
        
        // [DEBUG] Field: WtfrFundName, is_external=, is_static_class=False, static_prefix=
        private string _WtfrFundName ="";
        
        
        
        
        // [DEBUG] Field: WtfrKey2, is_external=, is_static_class=False, static_prefix=
        private WtfrElement.WtfrKey2 _WtfrKey2 = new WtfrElement.WtfrKey2();
        
        
        
        
        // [DEBUG] Field: WtfrFundType, is_external=, is_static_class=False, static_prefix=
        private string _WtfrFundType ="";
        
        
        // 88-level condition checks for WtfrFundType
        public bool IsWtfrCompanyFund()
        {
            if (this._WtfrFundType == "'C'") return true;
            return false;
        }
        public bool IsWtfrIndividualFund()
        {
            if (this._WtfrFundType == "'I'") return true;
            if (this._WtfrFundType == "'J'") return true;
            if (this._WtfrFundType == "'Z'") return true;
            if (this._WtfrFundType == "'T'") return true;
            return false;
        }
        public bool IsWtfrLifeFund()
        {
            if (this._WtfrFundType == "'L'") return true;
            return false;
        }
        public bool IsWtfrTrustFund()
        {
            if (this._WtfrFundType == "'T'") return true;
            return false;
        }
        public bool IsWtfrIrishLifeFund()
        {
            if (this._WtfrFundType == "'X'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WtfrElection1965Ord, is_external=, is_static_class=False, static_prefix=
        private string _WtfrElection1965Ord ="";
        
        
        
        
        // [DEBUG] Field: WtfrElection1965Fixed, is_external=, is_static_class=False, static_prefix=
        private string _WtfrElection1965Fixed ="";
        
        
        
        
        // [DEBUG] Field: WtfrElection1982, is_external=, is_static_class=False, static_prefix=
        private string _WtfrElection1982 ="";
        
        
        
        
        // [DEBUG] Field: WtfrPeriodStartDate, is_external=, is_static_class=False, static_prefix=
        private WtfrElement.WtfrPeriodStartDate _WtfrPeriodStartDate = new WtfrElement.WtfrPeriodStartDate();
        
        
        
        
        // [DEBUG] Field: WtfrPeriodEndDate, is_external=, is_static_class=False, static_prefix=
        private WtfrElement.WtfrPeriodEndDate _WtfrPeriodEndDate = new WtfrElement.WtfrPeriodEndDate();
        
        
        
        
        // [DEBUG] Field: WtfrUpdateCount, is_external=, is_static_class=False, static_prefix=
        private int _WtfrUpdateCount =0;
        
        
        
        
        // [DEBUG] Field: WtfrInverseKey, is_external=, is_static_class=False, static_prefix=
        private WtfrElement.WtfrInverseKey _WtfrInverseKey = new WtfrElement.WtfrInverseKey();
        
        
        
        
        // [DEBUG] Field: WtfrKey3, is_external=, is_static_class=False, static_prefix=
        private WtfrElement.WtfrKey3 _WtfrKey3 = new WtfrElement.WtfrKey3();
        
        
        
        
        // [DEBUG] Field: WtfrOlabFund, is_external=, is_static_class=False, static_prefix=
        private string _WtfrOlabFund ="";
        
        
        // 88-level condition checks for WtfrOlabFund
        public bool IsOlabFund()
        {
            if (this._WtfrOlabFund == "'Y'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WtfrLifeSummaryFund, is_external=, is_static_class=False, static_prefix=
        private string _WtfrLifeSummaryFund ="";
        
        
        // 88-level condition checks for WtfrLifeSummaryFund
        public bool IsLifeSummaryFund()
        {
            if (this._WtfrLifeSummaryFund == "'Y'") return true;
            return false;
        }
        
        
    public WtfrElement() {}
    
    public WtfrElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WtfrKey.SetWtfrKeyAsString(data.Substring(offset, WtfrKey.GetSize()));
        offset += 4;
        SetWtfrFundName(data.Substring(offset, 30).Trim());
        offset += 30;
        _WtfrKey2.SetWtfrKey2AsString(data.Substring(offset, WtfrKey2.GetSize()));
        offset += 4;
        SetWtfrFundType(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWtfrElection1965Ord(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWtfrElection1965Fixed(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWtfrElection1982(data.Substring(offset, 1).Trim());
        offset += 1;
        _WtfrPeriodStartDate.SetWtfrPeriodStartDateAsString(data.Substring(offset, WtfrPeriodStartDate.GetSize()));
        offset += 4;
        _WtfrPeriodEndDate.SetWtfrPeriodEndDateAsString(data.Substring(offset, WtfrPeriodEndDate.GetSize()));
        offset += 4;
        SetWtfrUpdateCount(int.Parse(data.Substring(offset, 4).Trim()));
        offset += 4;
        _WtfrInverseKey.SetWtfrInverseKeyAsString(data.Substring(offset, WtfrInverseKey.GetSize()));
        offset += 4;
        _WtfrKey3.SetWtfrKey3AsString(data.Substring(offset, WtfrKey3.GetSize()));
        offset += 4;
        SetWtfrOlabFund(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWtfrLifeSummaryFund(data.Substring(offset, 1).Trim());
        offset += 1;
        
    }
    
    // Serialization methods
    public string GetWtfrElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtfrKey.GetWtfrKeyAsString());
        result.Append(_WtfrFundName.PadRight(30));
        result.Append(_WtfrKey2.GetWtfrKey2AsString());
        result.Append(_WtfrFundType.PadRight(1));
        result.Append(_WtfrElection1965Ord.PadRight(1));
        result.Append(_WtfrElection1965Fixed.PadRight(1));
        result.Append(_WtfrElection1982.PadRight(1));
        result.Append(_WtfrPeriodStartDate.GetWtfrPeriodStartDateAsString());
        result.Append(_WtfrPeriodEndDate.GetWtfrPeriodEndDateAsString());
        result.Append(_WtfrUpdateCount.ToString().PadLeft(4, '0'));
        result.Append(_WtfrInverseKey.GetWtfrInverseKeyAsString());
        result.Append(_WtfrKey3.GetWtfrKey3AsString());
        result.Append(_WtfrOlabFund.PadRight(1));
        result.Append(_WtfrLifeSummaryFund.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWtfrElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            _WtfrKey.SetWtfrKeyAsString(data.Substring(offset, 4));
        }
        else
        {
            _WtfrKey.SetWtfrKeyAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 30 <= data.Length)
        {
            string extracted = data.Substring(offset, 30).Trim();
            SetWtfrFundName(extracted);
        }
        offset += 30;
        if (offset + 4 <= data.Length)
        {
            _WtfrKey2.SetWtfrKey2AsString(data.Substring(offset, 4));
        }
        else
        {
            _WtfrKey2.SetWtfrKey2AsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtfrFundType(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtfrElection1965Ord(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtfrElection1965Fixed(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtfrElection1982(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            _WtfrPeriodStartDate.SetWtfrPeriodStartDateAsString(data.Substring(offset, 4));
        }
        else
        {
            _WtfrPeriodStartDate.SetWtfrPeriodStartDateAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            _WtfrPeriodEndDate.SetWtfrPeriodEndDateAsString(data.Substring(offset, 4));
        }
        else
        {
            _WtfrPeriodEndDate.SetWtfrPeriodEndDateAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtfrUpdateCount(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            _WtfrInverseKey.SetWtfrInverseKeyAsString(data.Substring(offset, 4));
        }
        else
        {
            _WtfrInverseKey.SetWtfrInverseKeyAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            _WtfrKey3.SetWtfrKey3AsString(data.Substring(offset, 4));
        }
        else
        {
            _WtfrKey3.SetWtfrKey3AsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtfrOlabFund(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtfrLifeSummaryFund(extracted);
        }
        offset += 1;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WtfrKey GetWtfrKey()
    {
        return _WtfrKey;
    }
    
    // Standard Setter
    public void SetWtfrKey(WtfrKey value)
    {
        _WtfrKey = value;
    }
    
    // Get<>AsString()
    public string GetWtfrKeyAsString()
    {
        return _WtfrKey != null ? _WtfrKey.GetWtfrKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtfrKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtfrKey == null)
        {
            _WtfrKey = new WtfrKey();
        }
        _WtfrKey.SetWtfrKeyAsString(value);
    }
    
    // Standard Getter
    public string GetWtfrFundName()
    {
        return _WtfrFundName;
    }
    
    // Standard Setter
    public void SetWtfrFundName(string value)
    {
        _WtfrFundName = value;
    }
    
    // Get<>AsString()
    public string GetWtfrFundNameAsString()
    {
        return _WtfrFundName.PadRight(30);
    }
    
    // Set<>AsString()
    public void SetWtfrFundNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtfrFundName = value;
    }
    
    // Standard Getter
    public WtfrKey2 GetWtfrKey2()
    {
        return _WtfrKey2;
    }
    
    // Standard Setter
    public void SetWtfrKey2(WtfrKey2 value)
    {
        _WtfrKey2 = value;
    }
    
    // Get<>AsString()
    public string GetWtfrKey2AsString()
    {
        return _WtfrKey2 != null ? _WtfrKey2.GetWtfrKey2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtfrKey2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtfrKey2 == null)
        {
            _WtfrKey2 = new WtfrKey2();
        }
        _WtfrKey2.SetWtfrKey2AsString(value);
    }
    
    // Standard Getter
    public string GetWtfrFundType()
    {
        return _WtfrFundType;
    }
    
    // Standard Setter
    public void SetWtfrFundType(string value)
    {
        _WtfrFundType = value;
    }
    
    // Get<>AsString()
    public string GetWtfrFundTypeAsString()
    {
        return _WtfrFundType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtfrFundTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtfrFundType = value;
    }
    
    // Standard Getter
    public string GetWtfrElection1965Ord()
    {
        return _WtfrElection1965Ord;
    }
    
    // Standard Setter
    public void SetWtfrElection1965Ord(string value)
    {
        _WtfrElection1965Ord = value;
    }
    
    // Get<>AsString()
    public string GetWtfrElection1965OrdAsString()
    {
        return _WtfrElection1965Ord.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtfrElection1965OrdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtfrElection1965Ord = value;
    }
    
    // Standard Getter
    public string GetWtfrElection1965Fixed()
    {
        return _WtfrElection1965Fixed;
    }
    
    // Standard Setter
    public void SetWtfrElection1965Fixed(string value)
    {
        _WtfrElection1965Fixed = value;
    }
    
    // Get<>AsString()
    public string GetWtfrElection1965FixedAsString()
    {
        return _WtfrElection1965Fixed.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtfrElection1965FixedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtfrElection1965Fixed = value;
    }
    
    // Standard Getter
    public string GetWtfrElection1982()
    {
        return _WtfrElection1982;
    }
    
    // Standard Setter
    public void SetWtfrElection1982(string value)
    {
        _WtfrElection1982 = value;
    }
    
    // Get<>AsString()
    public string GetWtfrElection1982AsString()
    {
        return _WtfrElection1982.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtfrElection1982AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtfrElection1982 = value;
    }
    
    // Standard Getter
    public WtfrPeriodStartDate GetWtfrPeriodStartDate()
    {
        return _WtfrPeriodStartDate;
    }
    
    // Standard Setter
    public void SetWtfrPeriodStartDate(WtfrPeriodStartDate value)
    {
        _WtfrPeriodStartDate = value;
    }
    
    // Get<>AsString()
    public string GetWtfrPeriodStartDateAsString()
    {
        return _WtfrPeriodStartDate != null ? _WtfrPeriodStartDate.GetWtfrPeriodStartDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtfrPeriodStartDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtfrPeriodStartDate == null)
        {
            _WtfrPeriodStartDate = new WtfrPeriodStartDate();
        }
        _WtfrPeriodStartDate.SetWtfrPeriodStartDateAsString(value);
    }
    
    // Standard Getter
    public WtfrPeriodEndDate GetWtfrPeriodEndDate()
    {
        return _WtfrPeriodEndDate;
    }
    
    // Standard Setter
    public void SetWtfrPeriodEndDate(WtfrPeriodEndDate value)
    {
        _WtfrPeriodEndDate = value;
    }
    
    // Get<>AsString()
    public string GetWtfrPeriodEndDateAsString()
    {
        return _WtfrPeriodEndDate != null ? _WtfrPeriodEndDate.GetWtfrPeriodEndDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtfrPeriodEndDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtfrPeriodEndDate == null)
        {
            _WtfrPeriodEndDate = new WtfrPeriodEndDate();
        }
        _WtfrPeriodEndDate.SetWtfrPeriodEndDateAsString(value);
    }
    
    // Standard Getter
    public int GetWtfrUpdateCount()
    {
        return _WtfrUpdateCount;
    }
    
    // Standard Setter
    public void SetWtfrUpdateCount(int value)
    {
        _WtfrUpdateCount = value;
    }
    
    // Get<>AsString()
    public string GetWtfrUpdateCountAsString()
    {
        return _WtfrUpdateCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtfrUpdateCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtfrUpdateCount = parsed;
    }
    
    // Standard Getter
    public WtfrInverseKey GetWtfrInverseKey()
    {
        return _WtfrInverseKey;
    }
    
    // Standard Setter
    public void SetWtfrInverseKey(WtfrInverseKey value)
    {
        _WtfrInverseKey = value;
    }
    
    // Get<>AsString()
    public string GetWtfrInverseKeyAsString()
    {
        return _WtfrInverseKey != null ? _WtfrInverseKey.GetWtfrInverseKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtfrInverseKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtfrInverseKey == null)
        {
            _WtfrInverseKey = new WtfrInverseKey();
        }
        _WtfrInverseKey.SetWtfrInverseKeyAsString(value);
    }
    
    // Standard Getter
    public WtfrKey3 GetWtfrKey3()
    {
        return _WtfrKey3;
    }
    
    // Standard Setter
    public void SetWtfrKey3(WtfrKey3 value)
    {
        _WtfrKey3 = value;
    }
    
    // Get<>AsString()
    public string GetWtfrKey3AsString()
    {
        return _WtfrKey3 != null ? _WtfrKey3.GetWtfrKey3AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtfrKey3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtfrKey3 == null)
        {
            _WtfrKey3 = new WtfrKey3();
        }
        _WtfrKey3.SetWtfrKey3AsString(value);
    }
    
    // Standard Getter
    public string GetWtfrOlabFund()
    {
        return _WtfrOlabFund;
    }
    
    // Standard Setter
    public void SetWtfrOlabFund(string value)
    {
        _WtfrOlabFund = value;
    }
    
    // Get<>AsString()
    public string GetWtfrOlabFundAsString()
    {
        return _WtfrOlabFund.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtfrOlabFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtfrOlabFund = value;
    }
    
    // Standard Getter
    public string GetWtfrLifeSummaryFund()
    {
        return _WtfrLifeSummaryFund;
    }
    
    // Standard Setter
    public void SetWtfrLifeSummaryFund(string value)
    {
        _WtfrLifeSummaryFund = value;
    }
    
    // Get<>AsString()
    public string GetWtfrLifeSummaryFundAsString()
    {
        return _WtfrLifeSummaryFund.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtfrLifeSummaryFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtfrLifeSummaryFund = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtfrKey
    public class WtfrKey
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtfrFundCode, is_external=, is_static_class=False, static_prefix=
        private string _WtfrFundCode ="";
        
        
        
        
    public WtfrKey() {}
    
    public WtfrKey(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtfrFundCode(data.Substring(offset, 4).Trim());
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetWtfrKeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtfrFundCode.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetWtfrKeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtfrFundCode(extracted);
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtfrFundCode()
    {
        return _WtfrFundCode;
    }
    
    // Standard Setter
    public void SetWtfrFundCode(string value)
    {
        _WtfrFundCode = value;
    }
    
    // Get<>AsString()
    public string GetWtfrFundCodeAsString()
    {
        return _WtfrFundCode.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtfrFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtfrFundCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: WtfrKey2
public class WtfrKey2
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtfrSummaryFund, is_external=, is_static_class=False, static_prefix=
    private string _WtfrSummaryFund ="";
    
    
    
    
public WtfrKey2() {}

public WtfrKey2(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtfrSummaryFund(data.Substring(offset, 4).Trim());
    offset += 4;
    
}

// Serialization methods
public string GetWtfrKey2AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtfrSummaryFund.PadRight(4));
    
    return result.ToString();
}

public void SetWtfrKey2AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWtfrSummaryFund(extracted);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public string GetWtfrSummaryFund()
{
    return _WtfrSummaryFund;
}

// Standard Setter
public void SetWtfrSummaryFund(string value)
{
    _WtfrSummaryFund = value;
}

// Get<>AsString()
public string GetWtfrSummaryFundAsString()
{
    return _WtfrSummaryFund.PadRight(4);
}

// Set<>AsString()
public void SetWtfrSummaryFundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtfrSummaryFund = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WtfrPeriodStartDate
public class WtfrPeriodStartDate
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtfrPeriodStartDateMm, is_external=, is_static_class=False, static_prefix=
    private string _WtfrPeriodStartDateMm ="";
    
    
    
    
    // [DEBUG] Field: WtfrPeriodStartDateDd, is_external=, is_static_class=False, static_prefix=
    private string _WtfrPeriodStartDateDd ="";
    
    
    
    
public WtfrPeriodStartDate() {}

public WtfrPeriodStartDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtfrPeriodStartDateMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWtfrPeriodStartDateDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetWtfrPeriodStartDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtfrPeriodStartDateMm.PadRight(2));
    result.Append(_WtfrPeriodStartDateDd.PadRight(2));
    
    return result.ToString();
}

public void SetWtfrPeriodStartDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWtfrPeriodStartDateMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWtfrPeriodStartDateDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetWtfrPeriodStartDateMm()
{
    return _WtfrPeriodStartDateMm;
}

// Standard Setter
public void SetWtfrPeriodStartDateMm(string value)
{
    _WtfrPeriodStartDateMm = value;
}

// Get<>AsString()
public string GetWtfrPeriodStartDateMmAsString()
{
    return _WtfrPeriodStartDateMm.PadRight(2);
}

// Set<>AsString()
public void SetWtfrPeriodStartDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtfrPeriodStartDateMm = value;
}

// Standard Getter
public string GetWtfrPeriodStartDateDd()
{
    return _WtfrPeriodStartDateDd;
}

// Standard Setter
public void SetWtfrPeriodStartDateDd(string value)
{
    _WtfrPeriodStartDateDd = value;
}

// Get<>AsString()
public string GetWtfrPeriodStartDateDdAsString()
{
    return _WtfrPeriodStartDateDd.PadRight(2);
}

// Set<>AsString()
public void SetWtfrPeriodStartDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtfrPeriodStartDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WtfrPeriodEndDate
public class WtfrPeriodEndDate
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtfrPeriodEndDateMm, is_external=, is_static_class=False, static_prefix=
    private string _WtfrPeriodEndDateMm ="";
    
    
    
    
    // [DEBUG] Field: WtfrPeriodEndDateDd, is_external=, is_static_class=False, static_prefix=
    private string _WtfrPeriodEndDateDd ="";
    
    
    
    
public WtfrPeriodEndDate() {}

public WtfrPeriodEndDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtfrPeriodEndDateMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWtfrPeriodEndDateDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetWtfrPeriodEndDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtfrPeriodEndDateMm.PadRight(2));
    result.Append(_WtfrPeriodEndDateDd.PadRight(2));
    
    return result.ToString();
}

public void SetWtfrPeriodEndDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWtfrPeriodEndDateMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWtfrPeriodEndDateDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetWtfrPeriodEndDateMm()
{
    return _WtfrPeriodEndDateMm;
}

// Standard Setter
public void SetWtfrPeriodEndDateMm(string value)
{
    _WtfrPeriodEndDateMm = value;
}

// Get<>AsString()
public string GetWtfrPeriodEndDateMmAsString()
{
    return _WtfrPeriodEndDateMm.PadRight(2);
}

// Set<>AsString()
public void SetWtfrPeriodEndDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtfrPeriodEndDateMm = value;
}

// Standard Getter
public string GetWtfrPeriodEndDateDd()
{
    return _WtfrPeriodEndDateDd;
}

// Standard Setter
public void SetWtfrPeriodEndDateDd(string value)
{
    _WtfrPeriodEndDateDd = value;
}

// Get<>AsString()
public string GetWtfrPeriodEndDateDdAsString()
{
    return _WtfrPeriodEndDateDd.PadRight(2);
}

// Set<>AsString()
public void SetWtfrPeriodEndDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtfrPeriodEndDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WtfrInverseKey
public class WtfrInverseKey
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtfrInverseFundCode, is_external=, is_static_class=False, static_prefix=
    private string _WtfrInverseFundCode ="";
    
    
    
    
public WtfrInverseKey() {}

public WtfrInverseKey(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtfrInverseFundCode(data.Substring(offset, 4).Trim());
    offset += 4;
    
}

// Serialization methods
public string GetWtfrInverseKeyAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtfrInverseFundCode.PadRight(4));
    
    return result.ToString();
}

public void SetWtfrInverseKeyAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWtfrInverseFundCode(extracted);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public string GetWtfrInverseFundCode()
{
    return _WtfrInverseFundCode;
}

// Standard Setter
public void SetWtfrInverseFundCode(string value)
{
    _WtfrInverseFundCode = value;
}

// Get<>AsString()
public string GetWtfrInverseFundCodeAsString()
{
    return _WtfrInverseFundCode.PadRight(4);
}

// Set<>AsString()
public void SetWtfrInverseFundCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtfrInverseFundCode = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WtfrKey3
public class WtfrKey3
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtfrNewSummaryFund, is_external=, is_static_class=False, static_prefix=
    private string _WtfrNewSummaryFund ="";
    
    
    
    
public WtfrKey3() {}

public WtfrKey3(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtfrNewSummaryFund(data.Substring(offset, 4).Trim());
    offset += 4;
    
}

// Serialization methods
public string GetWtfrKey3AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtfrNewSummaryFund.PadRight(4));
    
    return result.ToString();
}

public void SetWtfrKey3AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWtfrNewSummaryFund(extracted);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public string GetWtfrNewSummaryFund()
{
    return _WtfrNewSummaryFund;
}

// Standard Setter
public void SetWtfrNewSummaryFund(string value)
{
    _WtfrNewSummaryFund = value;
}

// Get<>AsString()
public string GetWtfrNewSummaryFundAsString()
{
    return _WtfrNewSummaryFund.PadRight(4);
}

// Set<>AsString()
public void SetWtfrNewSummaryFundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtfrNewSummaryFund = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}

}}