using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WIds Data Structure

public class WIds
{
    private static int _size = 81;
    // [DEBUG] Class: WIds, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler74, is_external=, is_static_class=False, static_prefix=
    private string _Filler74 ="";
    
    
    
    
    // [DEBUG] Field: Filler75, is_external=, is_static_class=False, static_prefix=
    private string _Filler75 ="";
    
    
    
    
    // [DEBUG] Field: Filler76, is_external=, is_static_class=False, static_prefix=
    private string _Filler76 ="";
    
    
    
    
    // [DEBUG] Field: Filler77, is_external=, is_static_class=False, static_prefix=
    private string _Filler77 ="";
    
    
    
    
    // [DEBUG] Field: Filler78, is_external=, is_static_class=False, static_prefix=
    private string _Filler78 ="";
    
    
    
    
    // [DEBUG] Field: Filler79, is_external=, is_static_class=False, static_prefix=
    private string _Filler79 ="";
    
    
    
    
    // [DEBUG] Field: Filler80, is_external=, is_static_class=False, static_prefix=
    private string _Filler80 ="";
    
    
    
    
    // [DEBUG] Field: Filler81, is_external=, is_static_class=False, static_prefix=
    private string _Filler81 ="";
    
    
    
    
    // [DEBUG] Field: Filler82, is_external=, is_static_class=False, static_prefix=
    private string _Filler82 ="";
    
    
    
    
    
    // Serialization methods
    public string GetWIdsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler74.PadRight(8));
        result.Append(_Filler75.PadRight(8));
        result.Append(_Filler76.PadRight(8));
        result.Append(_Filler77.PadRight(8));
        result.Append(_Filler78.PadRight(8));
        result.Append(_Filler79.PadRight(8));
        result.Append(_Filler80.PadRight(8));
        result.Append(_Filler81.PadRight(24));
        result.Append(_Filler82.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWIdsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller74(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller75(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller76(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller77(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller78(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller79(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller80(extracted);
        }
        offset += 8;
        if (offset + 24 <= data.Length)
        {
            string extracted = data.Substring(offset, 24).Trim();
            SetFiller81(extracted);
        }
        offset += 24;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller82(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWIdsAsString();
    }
    // Set<>String Override function
    public void SetWIds(string value)
    {
        SetWIdsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller74()
    {
        return _Filler74;
    }
    
    // Standard Setter
    public void SetFiller74(string value)
    {
        _Filler74 = value;
    }
    
    // Get<>AsString()
    public string GetFiller74AsString()
    {
        return _Filler74.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller74AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler74 = value;
    }
    
    // Standard Getter
    public string GetFiller75()
    {
        return _Filler75;
    }
    
    // Standard Setter
    public void SetFiller75(string value)
    {
        _Filler75 = value;
    }
    
    // Get<>AsString()
    public string GetFiller75AsString()
    {
        return _Filler75.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller75AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler75 = value;
    }
    
    // Standard Getter
    public string GetFiller76()
    {
        return _Filler76;
    }
    
    // Standard Setter
    public void SetFiller76(string value)
    {
        _Filler76 = value;
    }
    
    // Get<>AsString()
    public string GetFiller76AsString()
    {
        return _Filler76.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller76AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler76 = value;
    }
    
    // Standard Getter
    public string GetFiller77()
    {
        return _Filler77;
    }
    
    // Standard Setter
    public void SetFiller77(string value)
    {
        _Filler77 = value;
    }
    
    // Get<>AsString()
    public string GetFiller77AsString()
    {
        return _Filler77.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller77AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler77 = value;
    }
    
    // Standard Getter
    public string GetFiller78()
    {
        return _Filler78;
    }
    
    // Standard Setter
    public void SetFiller78(string value)
    {
        _Filler78 = value;
    }
    
    // Get<>AsString()
    public string GetFiller78AsString()
    {
        return _Filler78.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller78AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler78 = value;
    }
    
    // Standard Getter
    public string GetFiller79()
    {
        return _Filler79;
    }
    
    // Standard Setter
    public void SetFiller79(string value)
    {
        _Filler79 = value;
    }
    
    // Get<>AsString()
    public string GetFiller79AsString()
    {
        return _Filler79.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller79AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler79 = value;
    }
    
    // Standard Getter
    public string GetFiller80()
    {
        return _Filler80;
    }
    
    // Standard Setter
    public void SetFiller80(string value)
    {
        _Filler80 = value;
    }
    
    // Get<>AsString()
    public string GetFiller80AsString()
    {
        return _Filler80.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller80AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler80 = value;
    }
    
    // Standard Getter
    public string GetFiller81()
    {
        return _Filler81;
    }
    
    // Standard Setter
    public void SetFiller81(string value)
    {
        _Filler81 = value;
    }
    
    // Get<>AsString()
    public string GetFiller81AsString()
    {
        return _Filler81.PadRight(24);
    }
    
    // Set<>AsString()
    public void SetFiller81AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler81 = value;
    }
    
    // Standard Getter
    public string GetFiller82()
    {
        return _Filler82;
    }
    
    // Standard Setter
    public void SetFiller82(string value)
    {
        _Filler82 = value;
    }
    
    // Get<>AsString()
    public string GetFiller82AsString()
    {
        return _Filler82.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller82AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler82 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}