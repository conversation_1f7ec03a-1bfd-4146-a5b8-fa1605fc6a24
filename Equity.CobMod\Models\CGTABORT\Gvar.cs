using System;
using System.Text;
using EquityProject.CommonDTO;
using Legacy4.Equity.CobMod.Models;
namespace EquityProject.CgtabortDTO
{// <Section> Class for Gvar
public class Gvar
{
public Gvar() {}

// Fields in the class


// [DEBUG] Field: WErrorMessage1, is_external=, is_static_class=False, static_prefix=
private string _WErrorMessage1 ="";




// [DEBUG] Field: WErrorMessage2, is_external=, is_static_class=False, static_prefix=
private string _WErrorMessage2 ="";




// [DEBUG] Field: WErrorMessage3, is_external=, is_static_class=False, static_prefix=
private string _WErrorMessage3 ="";




// [DEBUG] Field: EquityStatusRecord, is_external=, is_static_class=False, static_prefix=
private EquityStatusRecord _EquityStatusRecord = new EquityStatusRecord();




// [DEBUG] Field: EquityParameters, is_external=, is_static_class=False, static_prefix=
private EquityParameters _EquityParameters = new EquityParameters();




// [DEBUG] Field: EQUITY_SUCCESSFUL, is_external=, is_static_class=False, static_prefix=
public const int EQUITY_SUCCESSFUL = 0;




// [DEBUG] Field: SUCCESSFUL_PREVIOUS_CRASH, is_external=, is_static_class=False, static_prefix=
public const int SUCCESSFUL_PREVIOUS_CRASH = 1;




// [DEBUG] Field: USERID_ALREADY_IN_USE, is_external=, is_static_class=False, static_prefix=
public const int USERID_ALREADY_IN_USE = 2;




// [DEBUG] Field: USERID_DOESNT_EXIST, is_external=, is_static_class=False, static_prefix=
public const int USERID_DOESNT_EXIST = 3;




// [DEBUG] Field: INVALID_USERID_PW, is_external=, is_static_class=False, static_prefix=
public const int INVALID_USERID_PW = 4;




// [DEBUG] Field: SEE_ERROR_LOG, is_external=, is_static_class=False, static_prefix=
public const int SEE_ERROR_LOG = 5;




// [DEBUG] Field: LOAD_RECORDS_REJECTED, is_external=, is_static_class=False, static_prefix=
public const int LOAD_RECORDS_REJECTED = 6;




// [DEBUG] Field: SEE_OUTPUT_REPORTS, is_external=, is_static_class=False, static_prefix=
public const int SEE_OUTPUT_REPORTS = 7;




// [DEBUG] Field: SEE_RUN_LOG, is_external=, is_static_class=False, static_prefix=
public const int SEE_RUN_LOG = 8;




// [DEBUG] Field: USER_NOT_LOGGED_ON, is_external=, is_static_class=False, static_prefix=
public const int USER_NOT_LOGGED_ON = 9;




// [DEBUG] Field: TOO_MANY_USERS, is_external=, is_static_class=False, static_prefix=
public const int TOO_MANY_USERS = 10;




// [DEBUG] Field: VERSION_2_1_OR_LOWER, is_external=, is_static_class=False, static_prefix=
public const int VERSION_2_1_OR_LOWER = 11;




// [DEBUG] Field: LOAD_FUNDS, is_external=, is_static_class=False, static_prefix=
public const int LOAD_FUNDS = 3001;




// [DEBUG] Field: LOAD_COUNTRIES, is_external=, is_static_class=False, static_prefix=
public const int LOAD_COUNTRIES = 3002;




// [DEBUG] Field: LOAD_GROUPS, is_external=, is_static_class=False, static_prefix=
public const int LOAD_GROUPS = 3003;




// [DEBUG] Field: LOAD_RPI, is_external=, is_static_class=False, static_prefix=
public const int LOAD_RPI = 3004;




// [DEBUG] Field: LOAD_STOCKS, is_external=, is_static_class=False, static_prefix=
public const int LOAD_STOCKS = 3005;




// [DEBUG] Field: LOAD_PRICES, is_external=, is_static_class=False, static_prefix=
public const int LOAD_PRICES = 3006;




// [DEBUG] Field: LOAD_BALANCES, is_external=, is_static_class=False, static_prefix=
public const int LOAD_BALANCES = 3007;




// [DEBUG] Field: LOAD_TRANSACTIONS, is_external=, is_static_class=False, static_prefix=
public const int LOAD_TRANSACTIONS = 3008;




// [DEBUG] Field: FULL_CALCULATION, is_external=, is_static_class=False, static_prefix=
public const int FULL_CALCULATION = 3009;




// [DEBUG] Field: ERROR_REPORT, is_external=, is_static_class=False, static_prefix=
public const int ERROR_REPORT = 3010;




// [DEBUG] Field: SCHEDULE, is_external=, is_static_class=False, static_prefix=
public const int SCHEDULE = 3011;




// [DEBUG] Field: YEAR_END, is_external=, is_static_class=False, static_prefix=
public const int YEAR_END = 3012;




// [DEBUG] Field: PARTIAL_CALCULATION, is_external=, is_static_class=False, static_prefix=
public const int PARTIAL_CALCULATION = 3013;




// [DEBUG] Field: SEDOL_WHATIF_CALCULATION, is_external=, is_static_class=False, static_prefix=
public const int SEDOL_WHATIF_CALCULATION = 3014;




// [DEBUG] Field: XFUND_WHATIF_CALCULATION, is_external=, is_static_class=False, static_prefix=
public const int XFUND_WHATIF_CALCULATION = 3015;




// [DEBUG] Field: ON_LINE_CALCULATION, is_external=, is_static_class=False, static_prefix=
public const int ON_LINE_CALCULATION = 3016;




// [DEBUG] Field: CALCULATION_ERROR, is_external=, is_static_class=False, static_prefix=
public const int CALCULATION_ERROR = 3017;




// [DEBUG] Field: FORMAT_SCHEDULE, is_external=, is_static_class=False, static_prefix=
public const int FORMAT_SCHEDULE = 3018;




// [DEBUG] Field: TAPERED_GAINS_SCHD, is_external=, is_static_class=False, static_prefix=
public const int TAPERED_GAINS_SCHD = 3019;




// [DEBUG] Field: U_PERCENTAGE_GAIN_LOSS, is_external=, is_static_class=False, static_prefix=
public const int U_PERCENTAGE_GAIN_LOSS = 3020;




// [DEBUG] Field: U_EXPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const int U_EXPORT_FILE = 3021;




// [DEBUG] Field: U_FULL_EXTRACT, is_external=, is_static_class=False, static_prefix=
public const int U_FULL_EXTRACT = 3022;




// [DEBUG] Field: TRANCHE_EXPORT, is_external=, is_static_class=False, static_prefix=
public const int TRANCHE_EXPORT = 3023;




// [DEBUG] Field: GAIN_LOSS_SUMMARY, is_external=, is_static_class=False, static_prefix=
public const int GAIN_LOSS_SUMMARY = 3024;




// [DEBUG] Field: CH_GAIN_LOSS_SUMMARY, is_external=, is_static_class=False, static_prefix=
public const int CH_GAIN_LOSS_SUMMARY = 3025;




// [DEBUG] Field: SCHEDULE_D_BF_GAINS, is_external=, is_static_class=False, static_prefix=
public const int SCHEDULE_D_BF_GAINS = 3026;




// [DEBUG] Field: GAIN_LOSS_EXTRACT, is_external=, is_static_class=False, static_prefix=
public const int GAIN_LOSS_EXTRACT = 3027;




// [DEBUG] Field: REALISED_REPORT_CG01, is_external=, is_static_class=False, static_prefix=
public const int REALISED_REPORT_CG01 = 3028;




// [DEBUG] Field: CONFIGURABLE_REALISED_REPORT, is_external=, is_static_class=False, static_prefix=
public const int CONFIGURABLE_REALISED_REPORT = 3029;




// [DEBUG] Field: GAINS_LOSSES_REALISED_REPORT, is_external=, is_static_class=False, static_prefix=
public const int GAINS_LOSSES_REALISED_REPORT = 3030;




// [DEBUG] Field: UNREALISED_REPORT_CG02, is_external=, is_static_class=False, static_prefix=
public const int UNREALISED_REPORT_CG02 = 3031;




// [DEBUG] Field: FULL_UNREALISED_REPORT, is_external=, is_static_class=False, static_prefix=
public const int FULL_UNREALISED_REPORT = 3032;




// [DEBUG] Field: HOLDINGS_ANALYSIS, is_external=, is_static_class=False, static_prefix=
public const int HOLDINGS_ANALYSIS = 3033;




// [DEBUG] Field: INDEXATION_ANALYSIS, is_external=, is_static_class=False, static_prefix=
public const int INDEXATION_ANALYSIS = 3034;




// [DEBUG] Field: TRANSACTION_SUMMARY, is_external=, is_static_class=False, static_prefix=
public const int TRANSACTION_SUMMARY = 3035;




// [DEBUG] Field: GAINS_TODAY_EXPORT, is_external=, is_static_class=False, static_prefix=
public const int GAINS_TODAY_EXPORT = 3036;




// [DEBUG] Field: HOLDINGS_EXPORT, is_external=, is_static_class=False, static_prefix=
public const int HOLDINGS_EXPORT = 3037;




// [DEBUG] Field: SF_GAINLOSS_EXPORT, is_external=, is_static_class=False, static_prefix=
public const int SF_GAINLOSS_EXPORT = 3038;




// [DEBUG] Field: TSB_GAINLOSS_EXPORT, is_external=, is_static_class=False, static_prefix=
public const int TSB_GAINLOSS_EXPORT = 3039;




// [DEBUG] Field: ACQUISITION_EXPORT, is_external=, is_static_class=False, static_prefix=
public const int ACQUISITION_EXPORT = 3040;




// [DEBUG] Field: PRICING_EXPORT, is_external=, is_static_class=False, static_prefix=
public const int PRICING_EXPORT = 3041;




// [DEBUG] Field: MASTERFILE_EXPORTER, is_external=, is_static_class=False, static_prefix=
public const int MASTERFILE_EXPORTER = 3042;




// [DEBUG] Field: TRANSACTION_HISTORY_EXPORTER, is_external=, is_static_class=False, static_prefix=
public const int TRANSACTION_HISTORY_EXPORTER = 3043;




// [DEBUG] Field: ADMIN_EXPORTER, is_external=, is_static_class=False, static_prefix=
public const int ADMIN_EXPORTER = 3044;




// [DEBUG] Field: FULL_HOLDINGS_EXPORT, is_external=, is_static_class=False, static_prefix=
public const int FULL_HOLDINGS_EXPORT = 3045;




// [DEBUG] Field: RESULTS_DB, is_external=, is_static_class=False, static_prefix=
public const int RESULTS_DB = 3055;




// [DEBUG] Field: INTERCONNECTED_FUNDS, is_external=, is_static_class=False, static_prefix=
public const int INTERCONNECTED_FUNDS = 3056;




// [DEBUG] Field: DAILY_TRANSACTION_EXPORT, is_external=, is_static_class=False, static_prefix=
public const int DAILY_TRANSACTION_EXPORT = 3060;




// [DEBUG] Field: GAIN_LOSS_SUMMARY_LR, is_external=, is_static_class=False, static_prefix=
public const int GAIN_LOSS_SUMMARY_LR = 3061;




// [DEBUG] Field: EquityErrorMessages, is_external=, is_static_class=False, static_prefix=
private EquityErrorMessages _EquityErrorMessages = new EquityErrorMessages();




// [DEBUG] Field: Filler21, is_external=, is_static_class=False, static_prefix=
private Filler21 _Filler21 = new Filler21();




// [DEBUG] Field: EquityTime, is_external=, is_static_class=False, static_prefix=
private int _EquityTime =0;




// [DEBUG] Field: EquityDisplayTime, is_external=, is_static_class=False, static_prefix=
private int _EquityDisplayTime =0;




// [DEBUG] Field: EquityLower, is_external=, is_static_class=False, static_prefix=
private string _EquityLower ="abcdefghijklmnopqrstuvwxyz";




// [DEBUG] Field: EquityUpper, is_external=, is_static_class=False, static_prefix=
private string _EquityUpper ="ABCDEFGHIJKLMNOPQRSTUVWXYZ";




// [DEBUG] Field: CgtstatLinkage, is_external=, is_static_class=False, static_prefix=
private CgtstatLinkage _CgtstatLinkage = new CgtstatLinkage();




// [DEBUG] Field: MAX_NO_OF_COSTS, is_external=, is_static_class=False, static_prefix=
public const int MAX_NO_OF_COSTS = 200;




// [DEBUG] Field: MAX_MASTER_RECORD_LEN, is_external=, is_static_class=False, static_prefix=
public const int MAX_MASTER_RECORD_LEN = 16270;




// [DEBUG] Field: CgtfilesLinkage, is_external=, is_static_class=False, static_prefix=
private CgtfilesLinkage _CgtfilesLinkage = new CgtfilesLinkage();




// [DEBUG] Field: LFileRecordArea, is_external=, is_static_class=False, static_prefix=
private LFileRecordArea _LFileRecordArea = new LFileRecordArea();




// [DEBUG] Field: EqtdebugLinkage, is_external=, is_static_class=False, static_prefix=
private EqtdebugLinkage _EqtdebugLinkage = new EqtdebugLinkage();




// [DEBUG] Field: ElcgmioLinkage1, is_external=, is_static_class=False, static_prefix=
private ElcgmioLinkage1 _ElcgmioLinkage1 = new ElcgmioLinkage1();




// [DEBUG] Field: FULL_COMP, is_external=, is_static_class=False, static_prefix=
public const int FULL_COMP = 6;




// [DEBUG] Field: PARTIAL_COMP, is_external=, is_static_class=False, static_prefix=
public const int PARTIAL_COMP = 9;




// [DEBUG] Field: SEDOL_WHATIF_COMP, is_external=, is_static_class=False, static_prefix=
public const int SEDOL_WHATIF_COMP = 7;




// [DEBUG] Field: SINGLE_SEDOL_COMP, is_external=, is_static_class=False, static_prefix=
public const int SINGLE_SEDOL_COMP = 7;




// [DEBUG] Field: YEAR_END_COMP, is_external=, is_static_class=False, static_prefix=
public const int YEAR_END_COMP = 8;




// [DEBUG] Field: ElcgmioLinkage2, is_external=, is_static_class=False, static_prefix=
private ElcgmioLinkage2 _ElcgmioLinkage2 = new ElcgmioLinkage2();




// Getter and Setter methods

// Standard Getter
public string GetWErrorMessage1()
{
    return _WErrorMessage1;
}

// Standard Setter
public void SetWErrorMessage1(string value)
{
    _WErrorMessage1 = value;
}

// Get<>AsString()
public string GetWErrorMessage1AsString()
{
    return _WErrorMessage1.PadRight(0);
}

// Set<>AsString()
public void SetWErrorMessage1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WErrorMessage1 = value;
}

// Standard Getter
public string GetWErrorMessage2()
{
    return _WErrorMessage2;
}

// Standard Setter
public void SetWErrorMessage2(string value)
{
    _WErrorMessage2 = value;
}

// Get<>AsString()
public string GetWErrorMessage2AsString()
{
    return _WErrorMessage2.PadRight(0);
}

// Set<>AsString()
public void SetWErrorMessage2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WErrorMessage2 = value;
}

// Standard Getter
public string GetWErrorMessage3()
{
    return _WErrorMessage3;
}

// Standard Setter
public void SetWErrorMessage3(string value)
{
    _WErrorMessage3 = value;
}

// Get<>AsString()
public string GetWErrorMessage3AsString()
{
    return _WErrorMessage3.PadRight(160);
}

// Set<>AsString()
public void SetWErrorMessage3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WErrorMessage3 = value;
}

// Standard Getter
public EquityStatusRecord GetEquityStatusRecord()
{
    return _EquityStatusRecord;
}

// Standard Setter
public void SetEquityStatusRecord(EquityStatusRecord value)
{
    _EquityStatusRecord = value;
}

// Get<>AsString()
public string GetEquityStatusRecordAsString()
{
    return _EquityStatusRecord != null ? _EquityStatusRecord.GetEquityStatusRecordAsString() : "";
}

// Set<>AsString()
public void SetEquityStatusRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_EquityStatusRecord == null)
    {
        _EquityStatusRecord = new EquityStatusRecord();
    }
    _EquityStatusRecord.SetEquityStatusRecordAsString(value);
}

// Standard Getter
public EquityParameters GetEquityParameters()
{
    return _EquityParameters;
}

// Standard Setter
public void SetEquityParameters(EquityParameters value)
{
    _EquityParameters = value;
}

// Get<>AsString()
public string GetEquityParametersAsString()
{
    return _EquityParameters != null ? _EquityParameters.GetEquityParametersAsString() : "";
}

// Set<>AsString()
public void SetEquityParametersAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_EquityParameters == null)
    {
        _EquityParameters = new EquityParameters();
    }
    _EquityParameters.SetEquityParametersAsString(value);
}

// Standard Getter
public EquityErrorMessages GetEquityErrorMessages()
{
    return _EquityErrorMessages;
}

// Standard Setter
public void SetEquityErrorMessages(EquityErrorMessages value)
{
    _EquityErrorMessages = value;
}

// Get<>AsString()
public string GetEquityErrorMessagesAsString()
{
    return _EquityErrorMessages != null ? _EquityErrorMessages.GetEquityErrorMessagesAsString() : "";
}

// Set<>AsString()
public void SetEquityErrorMessagesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_EquityErrorMessages == null)
    {
        _EquityErrorMessages = new EquityErrorMessages();
    }
    _EquityErrorMessages.SetEquityErrorMessagesAsString(value);
}

// Standard Getter
public Filler21 GetFiller21()
{
    return _Filler21;
}

// Standard Setter
public void SetFiller21(Filler21 value)
{
    _Filler21 = value;
}

// Get<>AsString()
public string GetFiller21AsString()
{
    return _Filler21 != null ? _Filler21.GetFiller21AsString() : "";
}

// Set<>AsString()
public void SetFiller21AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler21 == null)
    {
        _Filler21 = new Filler21();
    }
    _Filler21.SetFiller21AsString(value);
}

// Standard Getter
public int GetEquityTime()
{
    return _EquityTime;
}

// Standard Setter
public void SetEquityTime(int value)
{
    _EquityTime = value;
}

// Get<>AsString()
public string GetEquityTimeAsString()
{
    return _EquityTime.ToString().PadLeft(8, '0');
}

// Set<>AsString()
public void SetEquityTimeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _EquityTime = parsed;
}

// Standard Getter
public int GetEquityDisplayTime()
{
    return _EquityDisplayTime;
}

// Standard Setter
public void SetEquityDisplayTime(int value)
{
    _EquityDisplayTime = value;
}

// Get<>AsString()
public string GetEquityDisplayTimeAsString()
{
    return _EquityDisplayTime.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetEquityDisplayTimeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _EquityDisplayTime = parsed;
}

// Standard Getter
public string GetEquityLower()
{
    return _EquityLower;
}

// Standard Setter
public void SetEquityLower(string value)
{
    _EquityLower = value;
}

// Get<>AsString()
public string GetEquityLowerAsString()
{
    return _EquityLower.PadRight(26);
}

// Set<>AsString()
public void SetEquityLowerAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityLower = value;
}

// Standard Getter
public string GetEquityUpper()
{
    return _EquityUpper;
}

// Standard Setter
public void SetEquityUpper(string value)
{
    _EquityUpper = value;
}

// Get<>AsString()
public string GetEquityUpperAsString()
{
    return _EquityUpper.PadRight(26);
}

// Set<>AsString()
public void SetEquityUpperAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _EquityUpper = value;
}

// Standard Getter
public CgtstatLinkage GetCgtstatLinkage()
{
    return _CgtstatLinkage;
}

// Standard Setter
public void SetCgtstatLinkage(CgtstatLinkage value)
{
    _CgtstatLinkage = value;
}

// Get<>AsString()
public string GetCgtstatLinkageAsString()
{
    return _CgtstatLinkage != null ? _CgtstatLinkage.GetCgtstatLinkageAsString() : "";
}

// Set<>AsString()
public void SetCgtstatLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtstatLinkage == null)
    {
        _CgtstatLinkage = new CgtstatLinkage();
    }
    _CgtstatLinkage.SetCgtstatLinkageAsString(value);
}

// Standard Getter
public CgtfilesLinkage GetCgtfilesLinkage()
{
    return _CgtfilesLinkage;
}

// Standard Setter
public void SetCgtfilesLinkage(CgtfilesLinkage value)
{
    _CgtfilesLinkage = value;
}

// Get<>AsString()
public string GetCgtfilesLinkageAsString()
{
    return _CgtfilesLinkage != null ? _CgtfilesLinkage.GetCgtfilesLinkageAsString() : "";
}

// Set<>AsString()
public void SetCgtfilesLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtfilesLinkage == null)
    {
        _CgtfilesLinkage = new CgtfilesLinkage();
    }
    _CgtfilesLinkage.SetCgtfilesLinkageAsString(value);
}

// Standard Getter
public LFileRecordArea GetLFileRecordArea()
{
    return _LFileRecordArea;
}

// Standard Setter
public void SetLFileRecordArea(LFileRecordArea value)
{
    _LFileRecordArea = value;
}

// Get<>AsString()
public string GetLFileRecordAreaAsString()
{
    return _LFileRecordArea != null ? _LFileRecordArea.GetLFileRecordAreaAsString() : "";
}

// Set<>AsString()
public void SetLFileRecordAreaAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_LFileRecordArea == null)
    {
        _LFileRecordArea = new LFileRecordArea();
    }
    _LFileRecordArea.SetLFileRecordAreaAsString(value);
}

// Standard Getter
public EqtdebugLinkage GetEqtdebugLinkage()
{
    return _EqtdebugLinkage;
}

// Standard Setter
public void SetEqtdebugLinkage(EqtdebugLinkage value)
{
    _EqtdebugLinkage = value;
}

// Get<>AsString()
public string GetEqtdebugLinkageAsString()
{
    return _EqtdebugLinkage != null ? _EqtdebugLinkage.GetEqtdebugLinkageAsString() : "";
}

// Set<>AsString()
public void SetEqtdebugLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_EqtdebugLinkage == null)
    {
        _EqtdebugLinkage = new EqtdebugLinkage();
    }
    _EqtdebugLinkage.SetEqtdebugLinkageAsString(value);
}

// Standard Getter
public ElcgmioLinkage1 GetElcgmioLinkage1()
{
    return _ElcgmioLinkage1;
}

// Standard Setter
public void SetElcgmioLinkage1(ElcgmioLinkage1 value)
{
    _ElcgmioLinkage1 = value;
}

// Get<>AsString()
public string GetElcgmioLinkage1AsString()
{
    return _ElcgmioLinkage1 != null ? _ElcgmioLinkage1.GetElcgmioLinkage1AsString() : "";
}

// Set<>AsString()
public void SetElcgmioLinkage1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ElcgmioLinkage1 == null)
    {
        _ElcgmioLinkage1 = new ElcgmioLinkage1();
    }
    _ElcgmioLinkage1.SetElcgmioLinkage1AsString(value);
}

// Standard Getter
public ElcgmioLinkage2 GetElcgmioLinkage2()
{
    return _ElcgmioLinkage2;
}

// Standard Setter
public void SetElcgmioLinkage2(ElcgmioLinkage2 value)
{
    _ElcgmioLinkage2 = value;
}

// Get<>AsString()
public string GetElcgmioLinkage2AsString()
{
    return _ElcgmioLinkage2 != null ? _ElcgmioLinkage2.GetElcgmioLinkage2AsString() : "";
}

// Set<>AsString()
public void SetElcgmioLinkage2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ElcgmioLinkage2 == null)
    {
        _ElcgmioLinkage2 = new ElcgmioLinkage2();
    }
    _ElcgmioLinkage2.SetElcgmioLinkage2AsString(value);
}

public void SyncEquityErrorMessagesFromFiller21()
{
    this.SetEquityErrorMessagesAsString(this.GetFiller21AsString());
}

public void SyncFiller21FromEquityErrorMessages()
{
    this.SetFiller21AsString(this.GetEquityErrorMessagesAsString());
}


}}
