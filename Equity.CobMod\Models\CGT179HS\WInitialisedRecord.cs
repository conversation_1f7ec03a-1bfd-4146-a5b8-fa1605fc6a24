using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt179HsDTO
{// DTO class representing WInitialisedRecord Data Structure

public class WInitialisedRecord
{
    private static int _size = 94;
    // [DEBUG] Class: WInitialisedRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WNewInitialisedRecord, is_external=, is_static_class=False, static_prefix=
    private WNewInitialisedRecord _WNewInitialisedRecord = new WNewInitialisedRecord();
    
    
    
    
    
    // Serialization methods
    public string GetWInitialisedRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WNewInitialisedRecord.GetWNewInitialisedRecordAsString());
        
        return result.ToString();
    }
    
    public void SetWInitialisedRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 94 <= data.Length)
        {
            _WNewInitialisedRecord.SetWNewInitialisedRecordAsString(data.Substring(offset, 94));
        }
        else
        {
            _WNewInitialisedRecord.SetWNewInitialisedRecordAsString(data.Substring(offset));
        }
        offset += 94;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWInitialisedRecordAsString();
    }
    // Set<>String Override function
    public void SetWInitialisedRecord(string value)
    {
        SetWInitialisedRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public WNewInitialisedRecord GetWNewInitialisedRecord()
    {
        return _WNewInitialisedRecord;
    }
    
    // Standard Setter
    public void SetWNewInitialisedRecord(WNewInitialisedRecord value)
    {
        _WNewInitialisedRecord = value;
    }
    
    // Get<>AsString()
    public string GetWNewInitialisedRecordAsString()
    {
        return _WNewInitialisedRecord != null ? _WNewInitialisedRecord.GetWNewInitialisedRecordAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWNewInitialisedRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WNewInitialisedRecord == null)
        {
            _WNewInitialisedRecord = new WNewInitialisedRecord();
        }
        _WNewInitialisedRecord.SetWNewInitialisedRecordAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWNewInitialisedRecord(string value)
    {
        _WNewInitialisedRecord.SetWNewInitialisedRecordAsString(value);
    }
    // Nested Class: WNewInitialisedRecord
    public class WNewInitialisedRecord
    {
        private static int _size = 94;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WOldInitialisedRecord, is_external=, is_static_class=False, static_prefix=
        private WNewInitialisedRecord.WOldInitialisedRecord _WOldInitialisedRecord = new WNewInitialisedRecord.WOldInitialisedRecord();
        
        
        
        
        // [DEBUG] Field: Filler30, is_external=, is_static_class=False, static_prefix=
        private WNewInitialisedRecord.Filler30 _Filler30 = new WNewInitialisedRecord.Filler30();
        
        
        
        
    public WNewInitialisedRecord() {}
    
    public WNewInitialisedRecord(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WOldInitialisedRecord.SetWOldInitialisedRecordAsString(data.Substring(offset, WOldInitialisedRecord.GetSize()));
        offset += 94;
        _Filler30.SetFiller30AsString(data.Substring(offset, Filler30.GetSize()));
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetWNewInitialisedRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WOldInitialisedRecord.GetWOldInitialisedRecordAsString());
        result.Append(_Filler30.GetFiller30AsString());
        
        return result.ToString();
    }
    
    public void SetWNewInitialisedRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 94 <= data.Length)
        {
            _WOldInitialisedRecord.SetWOldInitialisedRecordAsString(data.Substring(offset, 94));
        }
        else
        {
            _WOldInitialisedRecord.SetWOldInitialisedRecordAsString(data.Substring(offset));
        }
        offset += 94;
        if (offset + 0 <= data.Length)
        {
            _Filler30.SetFiller30AsString(data.Substring(offset, 0));
        }
        else
        {
            _Filler30.SetFiller30AsString(data.Substring(offset));
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WOldInitialisedRecord GetWOldInitialisedRecord()
    {
        return _WOldInitialisedRecord;
    }
    
    // Standard Setter
    public void SetWOldInitialisedRecord(WOldInitialisedRecord value)
    {
        _WOldInitialisedRecord = value;
    }
    
    // Get<>AsString()
    public string GetWOldInitialisedRecordAsString()
    {
        return _WOldInitialisedRecord != null ? _WOldInitialisedRecord.GetWOldInitialisedRecordAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWOldInitialisedRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WOldInitialisedRecord == null)
        {
            _WOldInitialisedRecord = new WOldInitialisedRecord();
        }
        _WOldInitialisedRecord.SetWOldInitialisedRecordAsString(value);
    }
    
    // Standard Getter
    public Filler30 GetFiller30()
    {
        return _Filler30;
    }
    
    // Standard Setter
    public void SetFiller30(Filler30 value)
    {
        _Filler30 = value;
    }
    
    // Get<>AsString()
    public string GetFiller30AsString()
    {
        return _Filler30 != null ? _Filler30.GetFiller30AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller30AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler30 == null)
        {
            _Filler30 = new Filler30();
        }
        _Filler30.SetFiller30AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WOldInitialisedRecord
    public class WOldInitialisedRecord
    {
        private static int _size = 94;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler18, is_external=, is_static_class=False, static_prefix=
        private string _Filler18 ="";
        
        
        
        
        // [DEBUG] Field: IFundCode, is_external=, is_static_class=False, static_prefix=
        private string _IFundCode ="";
        
        
        
        
        // [DEBUG] Field: Filler19, is_external=, is_static_class=False, static_prefix=
        private string _Filler19 ="\",";
        
        
        
        
        // [DEBUG] Field: ISedolCode, is_external=, is_static_class=False, static_prefix=
        private string _ISedolCode ="";
        
        
        
        
        // [DEBUG] Field: Filler20, is_external=, is_static_class=False, static_prefix=
        private string _Filler20 =",";
        
        
        
        
        // [DEBUG] Field: IHolding, is_external=, is_static_class=False, static_prefix=
        private decimal _IHolding =0;
        
        
        
        
        // [DEBUG] Field: Filler21, is_external=, is_static_class=False, static_prefix=
        private string _Filler21 =",\"";
        
        
        
        
        // [DEBUG] Field: IDisposalDate, is_external=, is_static_class=False, static_prefix=
        private string _IDisposalDate ="";
        
        
        
        
        // [DEBUG] Field: Filler22, is_external=, is_static_class=False, static_prefix=
        private string _Filler22 =",";
        
        
        
        
        // [DEBUG] Field: ITodaysDate, is_external=, is_static_class=False, static_prefix=
        private string _ITodaysDate ="";
        
        
        
        
        // [DEBUG] Field: Filler23, is_external=, is_static_class=False, static_prefix=
        private string _Filler23 ="\",";
        
        
        
        
        // [DEBUG] Field: ICapitalGain, is_external=, is_static_class=False, static_prefix=
        private decimal _ICapitalGain =0;
        
        
        
        
        // [DEBUG] Field: Filler24, is_external=, is_static_class=False, static_prefix=
        private string _Filler24 =",";
        
        
        
        
        // [DEBUG] Field: ICapitalLoss, is_external=, is_static_class=False, static_prefix=
        private decimal _ICapitalLoss =0;
        
        
        
        
        // [DEBUG] Field: Filler25, is_external=, is_static_class=False, static_prefix=
        private string _Filler25 =",\"";
        
        
        
        
        // [DEBUG] Field: IHoldingFlag, is_external=, is_static_class=False, static_prefix=
        private string _IHoldingFlag ="";
        
        
        
        
        // [DEBUG] Field: Filler26, is_external=, is_static_class=False, static_prefix=
        private string _Filler26 =",";
        
        
        
        
        // [DEBUG] Field: ITransReference, is_external=, is_static_class=False, static_prefix=
        private string _ITransReference ="";
        
        
        
        
        // [DEBUG] Field: Filler27, is_external=, is_static_class=False, static_prefix=
        private string _Filler27 ="\",";
        
        
        
        
        // [DEBUG] Field: ITransBaseCost, is_external=, is_static_class=False, static_prefix=
        private decimal _ITransBaseCost =0;
        
        
        
        
        // [DEBUG] Field: Filler28, is_external=, is_static_class=False, static_prefix=
        private string _Filler28 =",";
        
        
        
        
        // [DEBUG] Field: ITransIndexation, is_external=, is_static_class=False, static_prefix=
        private decimal _ITransIndexation =0;
        
        
        
        
        // [DEBUG] Field: Filler29, is_external=, is_static_class=False, static_prefix=
        private string _Filler29 =",";
        
        
        
        
        // [DEBUG] Field: ITransProceeds, is_external=, is_static_class=False, static_prefix=
        private decimal _ITransProceeds =0;
        
        
        
        
    public WOldInitialisedRecord() {}
    
    public WOldInitialisedRecord(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller18(data.Substring(offset, 0).Trim());
        offset += 0;
        SetIFundCode(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller19(data.Substring(offset, 0).Trim());
        offset += 0;
        SetISedolCode(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller20(data.Substring(offset, 0).Trim());
        offset += 0;
        SetIHolding(PackedDecimalConverter.ToDecimal(data.Substring(offset, 14)));
        offset += 14;
        SetFiller21(data.Substring(offset, 0).Trim());
        offset += 0;
        SetIDisposalDate(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller22(data.Substring(offset, 0).Trim());
        offset += 0;
        SetITodaysDate(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller23(data.Substring(offset, 0).Trim());
        offset += 0;
        SetICapitalGain(PackedDecimalConverter.ToDecimal(data.Substring(offset, 14)));
        offset += 14;
        SetFiller24(data.Substring(offset, 0).Trim());
        offset += 0;
        SetICapitalLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 14)));
        offset += 14;
        SetFiller25(data.Substring(offset, 0).Trim());
        offset += 0;
        SetIHoldingFlag(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller26(data.Substring(offset, 0).Trim());
        offset += 0;
        SetITransReference(data.Substring(offset, 10).Trim());
        offset += 10;
        SetFiller27(data.Substring(offset, 0).Trim());
        offset += 0;
        SetITransBaseCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 14)));
        offset += 14;
        SetFiller28(data.Substring(offset, 0).Trim());
        offset += 0;
        SetITransIndexation(PackedDecimalConverter.ToDecimal(data.Substring(offset, 14)));
        offset += 14;
        SetFiller29(data.Substring(offset, 0).Trim());
        offset += 0;
        SetITransProceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 14)));
        offset += 14;
        
    }
    
    // Serialization methods
    public string GetWOldInitialisedRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler18.PadRight(0));
        result.Append(_IFundCode.PadRight(0));
        result.Append(_Filler19.PadRight(0));
        result.Append(_ISedolCode.PadRight(0));
        result.Append(_Filler20.PadRight(0));
        result.Append(_IHolding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler21.PadRight(0));
        result.Append(_IDisposalDate.PadRight(0));
        result.Append(_Filler22.PadRight(0));
        result.Append(_ITodaysDate.PadRight(0));
        result.Append(_Filler23.PadRight(0));
        result.Append(_ICapitalGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler24.PadRight(0));
        result.Append(_ICapitalLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler25.PadRight(0));
        result.Append(_IHoldingFlag.PadRight(0));
        result.Append(_Filler26.PadRight(0));
        result.Append(_ITransReference.PadRight(10));
        result.Append(_Filler27.PadRight(0));
        result.Append(_ITransBaseCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler28.PadRight(0));
        result.Append(_ITransIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler29.PadRight(0));
        result.Append(_ITransProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        
        return result.ToString();
    }
    
    public void SetWOldInitialisedRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller18(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetIFundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller19(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetISedolCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller20(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetIHolding(parsedDec);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller21(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetIDisposalDate(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller22(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetITodaysDate(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller23(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetICapitalGain(parsedDec);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller24(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetICapitalLoss(parsedDec);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller25(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetIHoldingFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller26(extracted);
        }
        offset += 0;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetITransReference(extracted);
        }
        offset += 10;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller27(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetITransBaseCost(parsedDec);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller28(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetITransIndexation(parsedDec);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller29(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetITransProceeds(parsedDec);
        }
        offset += 14;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller18()
    {
        return _Filler18;
    }
    
    // Standard Setter
    public void SetFiller18(string value)
    {
        _Filler18 = value;
    }
    
    // Get<>AsString()
    public string GetFiller18AsString()
    {
        return _Filler18.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller18AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler18 = value;
    }
    
    // Standard Getter
    public string GetIFundCode()
    {
        return _IFundCode;
    }
    
    // Standard Setter
    public void SetIFundCode(string value)
    {
        _IFundCode = value;
    }
    
    // Get<>AsString()
    public string GetIFundCodeAsString()
    {
        return _IFundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetIFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _IFundCode = value;
    }
    
    // Standard Getter
    public string GetFiller19()
    {
        return _Filler19;
    }
    
    // Standard Setter
    public void SetFiller19(string value)
    {
        _Filler19 = value;
    }
    
    // Get<>AsString()
    public string GetFiller19AsString()
    {
        return _Filler19.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller19AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler19 = value;
    }
    
    // Standard Getter
    public string GetISedolCode()
    {
        return _ISedolCode;
    }
    
    // Standard Setter
    public void SetISedolCode(string value)
    {
        _ISedolCode = value;
    }
    
    // Get<>AsString()
    public string GetISedolCodeAsString()
    {
        return _ISedolCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetISedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ISedolCode = value;
    }
    
    // Standard Getter
    public string GetFiller20()
    {
        return _Filler20;
    }
    
    // Standard Setter
    public void SetFiller20(string value)
    {
        _Filler20 = value;
    }
    
    // Get<>AsString()
    public string GetFiller20AsString()
    {
        return _Filler20.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller20AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler20 = value;
    }
    
    // Standard Getter
    public decimal GetIHolding()
    {
        return _IHolding;
    }
    
    // Standard Setter
    public void SetIHolding(decimal value)
    {
        _IHolding = value;
    }
    
    // Get<>AsString()
    public string GetIHoldingAsString()
    {
        return _IHolding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetIHoldingAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _IHolding = parsed;
    }
    
    // Standard Getter
    public string GetFiller21()
    {
        return _Filler21;
    }
    
    // Standard Setter
    public void SetFiller21(string value)
    {
        _Filler21 = value;
    }
    
    // Get<>AsString()
    public string GetFiller21AsString()
    {
        return _Filler21.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller21AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler21 = value;
    }
    
    // Standard Getter
    public string GetIDisposalDate()
    {
        return _IDisposalDate;
    }
    
    // Standard Setter
    public void SetIDisposalDate(string value)
    {
        _IDisposalDate = value;
    }
    
    // Get<>AsString()
    public string GetIDisposalDateAsString()
    {
        return _IDisposalDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetIDisposalDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _IDisposalDate = value;
    }
    
    // Standard Getter
    public string GetFiller22()
    {
        return _Filler22;
    }
    
    // Standard Setter
    public void SetFiller22(string value)
    {
        _Filler22 = value;
    }
    
    // Get<>AsString()
    public string GetFiller22AsString()
    {
        return _Filler22.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller22AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler22 = value;
    }
    
    // Standard Getter
    public string GetITodaysDate()
    {
        return _ITodaysDate;
    }
    
    // Standard Setter
    public void SetITodaysDate(string value)
    {
        _ITodaysDate = value;
    }
    
    // Get<>AsString()
    public string GetITodaysDateAsString()
    {
        return _ITodaysDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetITodaysDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ITodaysDate = value;
    }
    
    // Standard Getter
    public string GetFiller23()
    {
        return _Filler23;
    }
    
    // Standard Setter
    public void SetFiller23(string value)
    {
        _Filler23 = value;
    }
    
    // Get<>AsString()
    public string GetFiller23AsString()
    {
        return _Filler23.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller23AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler23 = value;
    }
    
    // Standard Getter
    public decimal GetICapitalGain()
    {
        return _ICapitalGain;
    }
    
    // Standard Setter
    public void SetICapitalGain(decimal value)
    {
        _ICapitalGain = value;
    }
    
    // Get<>AsString()
    public string GetICapitalGainAsString()
    {
        return _ICapitalGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetICapitalGainAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _ICapitalGain = parsed;
    }
    
    // Standard Getter
    public string GetFiller24()
    {
        return _Filler24;
    }
    
    // Standard Setter
    public void SetFiller24(string value)
    {
        _Filler24 = value;
    }
    
    // Get<>AsString()
    public string GetFiller24AsString()
    {
        return _Filler24.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller24AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler24 = value;
    }
    
    // Standard Getter
    public decimal GetICapitalLoss()
    {
        return _ICapitalLoss;
    }
    
    // Standard Setter
    public void SetICapitalLoss(decimal value)
    {
        _ICapitalLoss = value;
    }
    
    // Get<>AsString()
    public string GetICapitalLossAsString()
    {
        return _ICapitalLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetICapitalLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _ICapitalLoss = parsed;
    }
    
    // Standard Getter
    public string GetFiller25()
    {
        return _Filler25;
    }
    
    // Standard Setter
    public void SetFiller25(string value)
    {
        _Filler25 = value;
    }
    
    // Get<>AsString()
    public string GetFiller25AsString()
    {
        return _Filler25.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller25AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler25 = value;
    }
    
    // Standard Getter
    public string GetIHoldingFlag()
    {
        return _IHoldingFlag;
    }
    
    // Standard Setter
    public void SetIHoldingFlag(string value)
    {
        _IHoldingFlag = value;
    }
    
    // Get<>AsString()
    public string GetIHoldingFlagAsString()
    {
        return _IHoldingFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetIHoldingFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _IHoldingFlag = value;
    }
    
    // Standard Getter
    public string GetFiller26()
    {
        return _Filler26;
    }
    
    // Standard Setter
    public void SetFiller26(string value)
    {
        _Filler26 = value;
    }
    
    // Get<>AsString()
    public string GetFiller26AsString()
    {
        return _Filler26.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller26AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler26 = value;
    }
    
    // Standard Getter
    public string GetITransReference()
    {
        return _ITransReference;
    }
    
    // Standard Setter
    public void SetITransReference(string value)
    {
        _ITransReference = value;
    }
    
    // Get<>AsString()
    public string GetITransReferenceAsString()
    {
        return _ITransReference.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetITransReferenceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ITransReference = value;
    }
    
    // Standard Getter
    public string GetFiller27()
    {
        return _Filler27;
    }
    
    // Standard Setter
    public void SetFiller27(string value)
    {
        _Filler27 = value;
    }
    
    // Get<>AsString()
    public string GetFiller27AsString()
    {
        return _Filler27.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller27AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler27 = value;
    }
    
    // Standard Getter
    public decimal GetITransBaseCost()
    {
        return _ITransBaseCost;
    }
    
    // Standard Setter
    public void SetITransBaseCost(decimal value)
    {
        _ITransBaseCost = value;
    }
    
    // Get<>AsString()
    public string GetITransBaseCostAsString()
    {
        return _ITransBaseCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetITransBaseCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _ITransBaseCost = parsed;
    }
    
    // Standard Getter
    public string GetFiller28()
    {
        return _Filler28;
    }
    
    // Standard Setter
    public void SetFiller28(string value)
    {
        _Filler28 = value;
    }
    
    // Get<>AsString()
    public string GetFiller28AsString()
    {
        return _Filler28.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller28AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler28 = value;
    }
    
    // Standard Getter
    public decimal GetITransIndexation()
    {
        return _ITransIndexation;
    }
    
    // Standard Setter
    public void SetITransIndexation(decimal value)
    {
        _ITransIndexation = value;
    }
    
    // Get<>AsString()
    public string GetITransIndexationAsString()
    {
        return _ITransIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetITransIndexationAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _ITransIndexation = parsed;
    }
    
    // Standard Getter
    public string GetFiller29()
    {
        return _Filler29;
    }
    
    // Standard Setter
    public void SetFiller29(string value)
    {
        _Filler29 = value;
    }
    
    // Get<>AsString()
    public string GetFiller29AsString()
    {
        return _Filler29.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller29AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler29 = value;
    }
    
    // Standard Getter
    public decimal GetITransProceeds()
    {
        return _ITransProceeds;
    }
    
    // Standard Setter
    public void SetITransProceeds(decimal value)
    {
        _ITransProceeds = value;
    }
    
    // Get<>AsString()
    public string GetITransProceedsAsString()
    {
        return _ITransProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetITransProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _ITransProceeds = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: Filler30
public class Filler30
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler31, is_external=, is_static_class=False, static_prefix=
    private string _Filler31 =",\"";
    
    
    
    
    // [DEBUG] Field: IHoldingSign, is_external=, is_static_class=False, static_prefix=
    private string _IHoldingSign ="";
    
    
    
    
    // [DEBUG] Field: Filler32, is_external=, is_static_class=False, static_prefix=
    private string _Filler32 ="";
    
    
    
    
public Filler30() {}

public Filler30(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller31(data.Substring(offset, 0).Trim());
    offset += 0;
    SetIHoldingSign(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller32(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetFiller30AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler31.PadRight(0));
    result.Append(_IHoldingSign.PadRight(0));
    result.Append(_Filler32.PadRight(0));
    
    return result.ToString();
}

public void SetFiller30AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller31(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetIHoldingSign(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller32(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller31()
{
    return _Filler31;
}

// Standard Setter
public void SetFiller31(string value)
{
    _Filler31 = value;
}

// Get<>AsString()
public string GetFiller31AsString()
{
    return _Filler31.PadRight(0);
}

// Set<>AsString()
public void SetFiller31AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler31 = value;
}

// Standard Getter
public string GetIHoldingSign()
{
    return _IHoldingSign;
}

// Standard Setter
public void SetIHoldingSign(string value)
{
    _IHoldingSign = value;
}

// Get<>AsString()
public string GetIHoldingSignAsString()
{
    return _IHoldingSign.PadRight(0);
}

// Set<>AsString()
public void SetIHoldingSignAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _IHoldingSign = value;
}

// Standard Getter
public string GetFiller32()
{
    return _Filler32;
}

// Standard Setter
public void SetFiller32(string value)
{
    _Filler32 = value;
}

// Get<>AsString()
public string GetFiller32AsString()
{
    return _Filler32.PadRight(0);
}

// Set<>AsString()
public void SetFiller32AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler32 = value;
}



public static int GetSize()
{
    return _size;
}

}
}

}}