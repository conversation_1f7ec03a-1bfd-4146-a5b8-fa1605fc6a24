using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing ParmInfo Data Structure

public class ParmInfo
{
    private static int _size = 168;
    // [DEBUG] Class: ParmInfo, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: ParmLength, is_external=, is_static_class=False, static_prefix=
    private int _ParmLength =0;
    
    
    
    
    // [DEBUG] Field: ParmChars, is_external=, is_static_class=False, static_prefix=
    private ParmChars _ParmChars = new ParmChars();
    
    
    
    
    // [DEBUG] Field: ParmChR, is_external=, is_static_class=False, static_prefix=
    private ParmChR _ParmChR = new ParmChR();
    
    
    
    
    // [DEBUG] Field: ParmChFs, is_external=, is_static_class=False, static_prefix=
    private ParmChFs _ParmChFs = new ParmChFs();
    
    
    
    
    // [DEBUG] Field: ParmInfoX, is_external=, is_static_class=False, static_prefix=
    private string _ParmInfoX ="";
    
    
    
    
    
    // Serialization methods
    public string GetParmInfoAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_ParmLength.ToString().PadLeft(4, '0'));
        result.Append(_ParmChars.GetParmCharsAsString());
        result.Append(_ParmChR.GetParmChRAsString());
        result.Append(_ParmChFs.GetParmChFsAsString());
        result.Append(_ParmInfoX.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetParmInfoAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetParmLength(parsedInt);
        }
        offset += 4;
        if (offset + 139 <= data.Length)
        {
            _ParmChars.SetParmCharsAsString(data.Substring(offset, 139));
        }
        else
        {
            _ParmChars.SetParmCharsAsString(data.Substring(offset));
        }
        offset += 139;
        if (offset + 1 <= data.Length)
        {
            _ParmChR.SetParmChRAsString(data.Substring(offset, 1));
        }
        else
        {
            _ParmChR.SetParmChRAsString(data.Substring(offset));
        }
        offset += 1;
        if (offset + 24 <= data.Length)
        {
            _ParmChFs.SetParmChFsAsString(data.Substring(offset, 24));
        }
        else
        {
            _ParmChFs.SetParmChFsAsString(data.Substring(offset));
        }
        offset += 24;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetParmInfoX(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetParmInfoAsString();
    }
    // Set<>String Override function
    public void SetParmInfo(string value)
    {
        SetParmInfoAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetParmLength()
    {
        return _ParmLength;
    }
    
    // Standard Setter
    public void SetParmLength(int value)
    {
        _ParmLength = value;
    }
    
    // Get<>AsString()
    public string GetParmLengthAsString()
    {
        return _ParmLength.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetParmLengthAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _ParmLength = parsed;
    }
    
    // Standard Getter
    public ParmChars GetParmChars()
    {
        return _ParmChars;
    }
    
    // Standard Setter
    public void SetParmChars(ParmChars value)
    {
        _ParmChars = value;
    }
    
    // Get<>AsString()
    public string GetParmCharsAsString()
    {
        return _ParmChars != null ? _ParmChars.GetParmCharsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetParmCharsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_ParmChars == null)
        {
            _ParmChars = new ParmChars();
        }
        _ParmChars.SetParmCharsAsString(value);
    }
    
    // Standard Getter
    public ParmChR GetParmChR()
    {
        return _ParmChR;
    }
    
    // Standard Setter
    public void SetParmChR(ParmChR value)
    {
        _ParmChR = value;
    }
    
    // Get<>AsString()
    public string GetParmChRAsString()
    {
        return _ParmChR != null ? _ParmChR.GetParmChRAsString() : "";
    }
    
    // Set<>AsString()
    public void SetParmChRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_ParmChR == null)
        {
            _ParmChR = new ParmChR();
        }
        _ParmChR.SetParmChRAsString(value);
    }
    
    // Standard Getter
    public ParmChFs GetParmChFs()
    {
        return _ParmChFs;
    }
    
    // Standard Setter
    public void SetParmChFs(ParmChFs value)
    {
        _ParmChFs = value;
    }
    
    // Get<>AsString()
    public string GetParmChFsAsString()
    {
        return _ParmChFs != null ? _ParmChFs.GetParmChFsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetParmChFsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_ParmChFs == null)
        {
            _ParmChFs = new ParmChFs();
        }
        _ParmChFs.SetParmChFsAsString(value);
    }
    
    // Standard Getter
    public string GetParmInfoX()
    {
        return _ParmInfoX;
    }
    
    // Standard Setter
    public void SetParmInfoX(string value)
    {
        _ParmInfoX = value;
    }
    
    // Get<>AsString()
    public string GetParmInfoXAsString()
    {
        return _ParmInfoX.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetParmInfoXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmInfoX = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetParmChars(string value)
    {
        _ParmChars.SetParmCharsAsString(value);
    }
    // Nested Class: ParmChars
    public class ParmChars
    {
        private static int _size = 139;
        
        // Fields in the class
        
        
        // [DEBUG] Field: ParmChar1, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar1 ="";
        
        
        
        
        // [DEBUG] Field: ParmMsg, is_external=, is_static_class=False, static_prefix=
        private ParmChars.ParmMsg _ParmMsg = new ParmChars.ParmMsg();
        
        
        
        
    public ParmChars() {}
    
    public ParmChars(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetParmChar1(data.Substring(offset, 1).Trim());
        offset += 1;
        _ParmMsg.SetParmMsgAsString(data.Substring(offset, ParmMsg.GetSize()));
        offset += 138;
        
    }
    
    // Serialization methods
    public string GetParmCharsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_ParmChar1.PadRight(1));
        result.Append(_ParmMsg.GetParmMsgAsString());
        
        return result.ToString();
    }
    
    public void SetParmCharsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar1(extracted);
        }
        offset += 1;
        if (offset + 138 <= data.Length)
        {
            _ParmMsg.SetParmMsgAsString(data.Substring(offset, 138));
        }
        else
        {
            _ParmMsg.SetParmMsgAsString(data.Substring(offset));
        }
        offset += 138;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetParmChar1()
    {
        return _ParmChar1;
    }
    
    // Standard Setter
    public void SetParmChar1(string value)
    {
        _ParmChar1 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar1AsString()
    {
        return _ParmChar1.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar1 = value;
    }
    
    // Standard Getter
    public ParmMsg GetParmMsg()
    {
        return _ParmMsg;
    }
    
    // Standard Setter
    public void SetParmMsg(ParmMsg value)
    {
        _ParmMsg = value;
    }
    
    // Get<>AsString()
    public string GetParmMsgAsString()
    {
        return _ParmMsg != null ? _ParmMsg.GetParmMsgAsString() : "";
    }
    
    // Set<>AsString()
    public void SetParmMsgAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_ParmMsg == null)
        {
            _ParmMsg = new ParmMsg();
        }
        _ParmMsg.SetParmMsgAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: ParmMsg
    public class ParmMsg
    {
        private static int _size = 138;
        
        // Fields in the class
        
        
        // [DEBUG] Field: ParmChar2, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar2 ="";
        
        
        
        
        // [DEBUG] Field: ParmChar3, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar3 ="";
        
        
        
        
        // [DEBUG] Field: ParmChar4, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar4 ="";
        
        
        
        
        // [DEBUG] Field: ParmChar5, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar5 ="";
        
        
        
        
        // [DEBUG] Field: ParmChar6, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar6 ="";
        
        
        
        
        // [DEBUG] Field: ParmChar7, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar7 ="";
        
        
        
        
        // [DEBUG] Field: ParmChar8, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar8 ="";
        
        
        
        
        // [DEBUG] Field: ParmChar9, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar9 ="";
        
        
        
        
        // [DEBUG] Field: ParmChar10, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar10 ="";
        
        
        
        
        // [DEBUG] Field: ParmChar11, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar11 ="";
        
        
        
        
        // [DEBUG] Field: ParmChar12, is_external=, is_static_class=False, static_prefix=
        private string _ParmChar12 ="";
        
        
        
        
        // [DEBUG] Field: ParmCalSedol, is_external=, is_static_class=False, static_prefix=
        private ParmMsg.ParmCalSedol _ParmCalSedol = new ParmMsg.ParmCalSedol();
        
        
        
        
        // [DEBUG] Field: ParmDateTimeStamp, is_external=, is_static_class=False, static_prefix=
        private string _ParmDateTimeStamp ="";
        
        
        
        
        // [DEBUG] Field: ParmXFundCalcCall, is_external=, is_static_class=False, static_prefix=
        private ParmMsg.ParmXFundCalcCall _ParmXFundCalcCall = new ParmMsg.ParmXFundCalcCall();
        
        
        
        
        // [DEBUG] Field: ParmXFundRet, is_external=, is_static_class=False, static_prefix=
        private ParmMsg.ParmXFundRet _ParmXFundRet = new ParmMsg.ParmXFundRet();
        
        
        
        
    public ParmMsg() {}
    
    public ParmMsg(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetParmChar2(data.Substring(offset, 1).Trim());
        offset += 1;
        SetParmChar3(data.Substring(offset, 1).Trim());
        offset += 1;
        SetParmChar4(data.Substring(offset, 1).Trim());
        offset += 1;
        SetParmChar5(data.Substring(offset, 1).Trim());
        offset += 1;
        SetParmChar6(data.Substring(offset, 1).Trim());
        offset += 1;
        SetParmChar7(data.Substring(offset, 1).Trim());
        offset += 1;
        SetParmChar8(data.Substring(offset, 1).Trim());
        offset += 1;
        SetParmChar9(data.Substring(offset, 1).Trim());
        offset += 1;
        SetParmChar10(data.Substring(offset, 1).Trim());
        offset += 1;
        SetParmChar11(data.Substring(offset, 1).Trim());
        offset += 1;
        SetParmChar12(data.Substring(offset, 1).Trim());
        offset += 1;
        _ParmCalSedol.SetParmCalSedolAsString(data.Substring(offset, ParmCalSedol.GetSize()));
        offset += 11;
        SetParmDateTimeStamp(data.Substring(offset, 14).Trim());
        offset += 14;
        _ParmXFundCalcCall.SetParmXFundCalcCallAsString(data.Substring(offset, ParmXFundCalcCall.GetSize()));
        offset += 51;
        _ParmXFundRet.SetParmXFundRetAsString(data.Substring(offset, ParmXFundRet.GetSize()));
        offset += 51;
        
    }
    
    // Serialization methods
    public string GetParmMsgAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_ParmChar2.PadRight(1));
        result.Append(_ParmChar3.PadRight(1));
        result.Append(_ParmChar4.PadRight(1));
        result.Append(_ParmChar5.PadRight(1));
        result.Append(_ParmChar6.PadRight(1));
        result.Append(_ParmChar7.PadRight(1));
        result.Append(_ParmChar8.PadRight(1));
        result.Append(_ParmChar9.PadRight(1));
        result.Append(_ParmChar10.PadRight(1));
        result.Append(_ParmChar11.PadRight(1));
        result.Append(_ParmChar12.PadRight(1));
        result.Append(_ParmCalSedol.GetParmCalSedolAsString());
        result.Append(_ParmDateTimeStamp.PadRight(14));
        result.Append(_ParmXFundCalcCall.GetParmXFundCalcCallAsString());
        result.Append(_ParmXFundRet.GetParmXFundRetAsString());
        
        return result.ToString();
    }
    
    public void SetParmMsgAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar2(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar3(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar4(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar5(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar6(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar7(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar8(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar9(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar10(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar11(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetParmChar12(extracted);
        }
        offset += 1;
        if (offset + 11 <= data.Length)
        {
            _ParmCalSedol.SetParmCalSedolAsString(data.Substring(offset, 11));
        }
        else
        {
            _ParmCalSedol.SetParmCalSedolAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            SetParmDateTimeStamp(extracted);
        }
        offset += 14;
        if (offset + 51 <= data.Length)
        {
            _ParmXFundCalcCall.SetParmXFundCalcCallAsString(data.Substring(offset, 51));
        }
        else
        {
            _ParmXFundCalcCall.SetParmXFundCalcCallAsString(data.Substring(offset));
        }
        offset += 51;
        if (offset + 51 <= data.Length)
        {
            _ParmXFundRet.SetParmXFundRetAsString(data.Substring(offset, 51));
        }
        else
        {
            _ParmXFundRet.SetParmXFundRetAsString(data.Substring(offset));
        }
        offset += 51;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetParmChar2()
    {
        return _ParmChar2;
    }
    
    // Standard Setter
    public void SetParmChar2(string value)
    {
        _ParmChar2 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar2AsString()
    {
        return _ParmChar2.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar2 = value;
    }
    
    // Standard Getter
    public string GetParmChar3()
    {
        return _ParmChar3;
    }
    
    // Standard Setter
    public void SetParmChar3(string value)
    {
        _ParmChar3 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar3AsString()
    {
        return _ParmChar3.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar3 = value;
    }
    
    // Standard Getter
    public string GetParmChar4()
    {
        return _ParmChar4;
    }
    
    // Standard Setter
    public void SetParmChar4(string value)
    {
        _ParmChar4 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar4AsString()
    {
        return _ParmChar4.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar4 = value;
    }
    
    // Standard Getter
    public string GetParmChar5()
    {
        return _ParmChar5;
    }
    
    // Standard Setter
    public void SetParmChar5(string value)
    {
        _ParmChar5 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar5AsString()
    {
        return _ParmChar5.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar5 = value;
    }
    
    // Standard Getter
    public string GetParmChar6()
    {
        return _ParmChar6;
    }
    
    // Standard Setter
    public void SetParmChar6(string value)
    {
        _ParmChar6 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar6AsString()
    {
        return _ParmChar6.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar6 = value;
    }
    
    // Standard Getter
    public string GetParmChar7()
    {
        return _ParmChar7;
    }
    
    // Standard Setter
    public void SetParmChar7(string value)
    {
        _ParmChar7 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar7AsString()
    {
        return _ParmChar7.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar7 = value;
    }
    
    // Standard Getter
    public string GetParmChar8()
    {
        return _ParmChar8;
    }
    
    // Standard Setter
    public void SetParmChar8(string value)
    {
        _ParmChar8 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar8AsString()
    {
        return _ParmChar8.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar8 = value;
    }
    
    // Standard Getter
    public string GetParmChar9()
    {
        return _ParmChar9;
    }
    
    // Standard Setter
    public void SetParmChar9(string value)
    {
        _ParmChar9 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar9AsString()
    {
        return _ParmChar9.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar9 = value;
    }
    
    // Standard Getter
    public string GetParmChar10()
    {
        return _ParmChar10;
    }
    
    // Standard Setter
    public void SetParmChar10(string value)
    {
        _ParmChar10 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar10AsString()
    {
        return _ParmChar10.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar10 = value;
    }
    
    // Standard Getter
    public string GetParmChar11()
    {
        return _ParmChar11;
    }
    
    // Standard Setter
    public void SetParmChar11(string value)
    {
        _ParmChar11 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar11AsString()
    {
        return _ParmChar11.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar11AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar11 = value;
    }
    
    // Standard Getter
    public string GetParmChar12()
    {
        return _ParmChar12;
    }
    
    // Standard Setter
    public void SetParmChar12(string value)
    {
        _ParmChar12 = value;
    }
    
    // Get<>AsString()
    public string GetParmChar12AsString()
    {
        return _ParmChar12.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetParmChar12AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmChar12 = value;
    }
    
    // Standard Getter
    public ParmCalSedol GetParmCalSedol()
    {
        return _ParmCalSedol;
    }
    
    // Standard Setter
    public void SetParmCalSedol(ParmCalSedol value)
    {
        _ParmCalSedol = value;
    }
    
    // Get<>AsString()
    public string GetParmCalSedolAsString()
    {
        return _ParmCalSedol != null ? _ParmCalSedol.GetParmCalSedolAsString() : "";
    }
    
    // Set<>AsString()
    public void SetParmCalSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_ParmCalSedol == null)
        {
            _ParmCalSedol = new ParmCalSedol();
        }
        _ParmCalSedol.SetParmCalSedolAsString(value);
    }
    
    // Standard Getter
    public string GetParmDateTimeStamp()
    {
        return _ParmDateTimeStamp;
    }
    
    // Standard Setter
    public void SetParmDateTimeStamp(string value)
    {
        _ParmDateTimeStamp = value;
    }
    
    // Get<>AsString()
    public string GetParmDateTimeStampAsString()
    {
        return _ParmDateTimeStamp.PadRight(14);
    }
    
    // Set<>AsString()
    public void SetParmDateTimeStampAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmDateTimeStamp = value;
    }
    
    // Standard Getter
    public ParmXFundCalcCall GetParmXFundCalcCall()
    {
        return _ParmXFundCalcCall;
    }
    
    // Standard Setter
    public void SetParmXFundCalcCall(ParmXFundCalcCall value)
    {
        _ParmXFundCalcCall = value;
    }
    
    // Get<>AsString()
    public string GetParmXFundCalcCallAsString()
    {
        return _ParmXFundCalcCall != null ? _ParmXFundCalcCall.GetParmXFundCalcCallAsString() : "";
    }
    
    // Set<>AsString()
    public void SetParmXFundCalcCallAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_ParmXFundCalcCall == null)
        {
            _ParmXFundCalcCall = new ParmXFundCalcCall();
        }
        _ParmXFundCalcCall.SetParmXFundCalcCallAsString(value);
    }
    
    // Standard Getter
    public ParmXFundRet GetParmXFundRet()
    {
        return _ParmXFundRet;
    }
    
    // Standard Setter
    public void SetParmXFundRet(ParmXFundRet value)
    {
        _ParmXFundRet = value;
    }
    
    // Get<>AsString()
    public string GetParmXFundRetAsString()
    {
        return _ParmXFundRet != null ? _ParmXFundRet.GetParmXFundRetAsString() : "";
    }
    
    // Set<>AsString()
    public void SetParmXFundRetAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_ParmXFundRet == null)
        {
            _ParmXFundRet = new ParmXFundRet();
        }
        _ParmXFundRet.SetParmXFundRetAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: ParmCalSedol
    public class ParmCalSedol
    {
        private static int _size = 11;
        
        // Fields in the class
        
        
        // [DEBUG] Field: ParmCal, is_external=, is_static_class=False, static_prefix=
        private ParmCalSedol.ParmCal _ParmCal = new ParmCalSedol.ParmCal();
        
        
        
        
        // [DEBUG] Field: ParmSedol, is_external=, is_static_class=False, static_prefix=
        private string _ParmSedol ="";
        
        
        
        
    public ParmCalSedol() {}
    
    public ParmCalSedol(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _ParmCal.SetParmCalAsString(data.Substring(offset, ParmCal.GetSize()));
        offset += 4;
        SetParmSedol(data.Substring(offset, 7).Trim());
        offset += 7;
        
    }
    
    // Serialization methods
    public string GetParmCalSedolAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_ParmCal.GetParmCalAsString());
        result.Append(_ParmSedol.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetParmCalSedolAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            _ParmCal.SetParmCalAsString(data.Substring(offset, 4));
        }
        else
        {
            _ParmCal.SetParmCalAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetParmSedol(extracted);
        }
        offset += 7;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public ParmCal GetParmCal()
    {
        return _ParmCal;
    }
    
    // Standard Setter
    public void SetParmCal(ParmCal value)
    {
        _ParmCal = value;
    }
    
    // Get<>AsString()
    public string GetParmCalAsString()
    {
        return _ParmCal != null ? _ParmCal.GetParmCalAsString() : "";
    }
    
    // Set<>AsString()
    public void SetParmCalAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_ParmCal == null)
        {
            _ParmCal = new ParmCal();
        }
        _ParmCal.SetParmCalAsString(value);
    }
    
    // Standard Getter
    public string GetParmSedol()
    {
        return _ParmSedol;
    }
    
    // Standard Setter
    public void SetParmSedol(string value)
    {
        _ParmSedol = value;
    }
    
    // Get<>AsString()
    public string GetParmSedolAsString()
    {
        return _ParmSedol.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetParmSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParmSedol = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: ParmCal
    public class ParmCal
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler500, is_external=, is_static_class=False, static_prefix=
        private string _Filler500 ="";
        
        
        
        
        // [DEBUG] Field: Filler501, is_external=, is_static_class=False, static_prefix=
        private string _Filler501 ="";
        
        
        
        
    public ParmCal() {}
    
    public ParmCal(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller500(data.Substring(offset, 1).Trim());
        offset += 1;
        SetFiller501(data.Substring(offset, 3).Trim());
        offset += 3;
        
    }
    
    // Serialization methods
    public string GetParmCalAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler500.PadRight(1));
        result.Append(_Filler501.PadRight(3));
        
        return result.ToString();
    }
    
    public void SetParmCalAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller500(extracted);
        }
        offset += 1;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetFiller501(extracted);
        }
        offset += 3;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller500()
    {
        return _Filler500;
    }
    
    // Standard Setter
    public void SetFiller500(string value)
    {
        _Filler500 = value;
    }
    
    // Get<>AsString()
    public string GetFiller500AsString()
    {
        return _Filler500.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller500AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler500 = value;
    }
    
    // Standard Getter
    public string GetFiller501()
    {
        return _Filler501;
    }
    
    // Standard Setter
    public void SetFiller501(string value)
    {
        _Filler501 = value;
    }
    
    // Get<>AsString()
    public string GetFiller501AsString()
    {
        return _Filler501.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetFiller501AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler501 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
// Nested Class: ParmXFundCalcCall
public class ParmXFundCalcCall
{
    private static int _size = 51;
    
    // Fields in the class
    
    
    // [DEBUG] Field: ParmXFundDate, is_external=, is_static_class=False, static_prefix=
    private string _ParmXFundDate ="";
    
    
    
    
    // [DEBUG] Field: ParmXFundPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _ParmXFundPrice =0;
    
    
    
    
    // [DEBUG] Field: ParmXFundParPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _ParmXFundParPrice =0;
    
    
    
    
    // [DEBUG] Field: Filler502, is_external=, is_static_class=False, static_prefix=
    private string _Filler502 ="";
    
    
    
    
public ParmXFundCalcCall() {}

public ParmXFundCalcCall(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetParmXFundDate(data.Substring(offset, 6).Trim());
    offset += 6;
    SetParmXFundPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
    offset += 11;
    SetParmXFundParPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
    offset += 11;
    SetFiller502(data.Substring(offset, 23).Trim());
    offset += 23;
    
}

// Serialization methods
public string GetParmXFundCalcCallAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_ParmXFundDate.PadRight(6));
    result.Append(_ParmXFundPrice.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_ParmXFundParPrice.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler502.PadRight(23));
    
    return result.ToString();
}

public void SetParmXFundCalcCallAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetParmXFundDate(extracted);
    }
    offset += 6;
    if (offset + 11 <= data.Length)
    {
        string extracted = data.Substring(offset, 11).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetParmXFundPrice(parsedDec);
    }
    offset += 11;
    if (offset + 11 <= data.Length)
    {
        string extracted = data.Substring(offset, 11).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetParmXFundParPrice(parsedDec);
    }
    offset += 11;
    if (offset + 23 <= data.Length)
    {
        string extracted = data.Substring(offset, 23).Trim();
        SetFiller502(extracted);
    }
    offset += 23;
}

// Getter and Setter methods

// Standard Getter
public string GetParmXFundDate()
{
    return _ParmXFundDate;
}

// Standard Setter
public void SetParmXFundDate(string value)
{
    _ParmXFundDate = value;
}

// Get<>AsString()
public string GetParmXFundDateAsString()
{
    return _ParmXFundDate.PadRight(6);
}

// Set<>AsString()
public void SetParmXFundDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _ParmXFundDate = value;
}

// Standard Getter
public decimal GetParmXFundPrice()
{
    return _ParmXFundPrice;
}

// Standard Setter
public void SetParmXFundPrice(decimal value)
{
    _ParmXFundPrice = value;
}

// Get<>AsString()
public string GetParmXFundPriceAsString()
{
    return _ParmXFundPrice.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetParmXFundPriceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _ParmXFundPrice = parsed;
}

// Standard Getter
public decimal GetParmXFundParPrice()
{
    return _ParmXFundParPrice;
}

// Standard Setter
public void SetParmXFundParPrice(decimal value)
{
    _ParmXFundParPrice = value;
}

// Get<>AsString()
public string GetParmXFundParPriceAsString()
{
    return _ParmXFundParPrice.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetParmXFundParPriceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _ParmXFundParPrice = parsed;
}

// Standard Getter
public string GetFiller502()
{
    return _Filler502;
}

// Standard Setter
public void SetFiller502(string value)
{
    _Filler502 = value;
}

// Get<>AsString()
public string GetFiller502AsString()
{
    return _Filler502.PadRight(23);
}

// Set<>AsString()
public void SetFiller502AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler502 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: ParmXFundRet
public class ParmXFundRet
{
    private static int _size = 51;
    
    // Fields in the class
    
    
    // [DEBUG] Field: ParmXFundUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _ParmXFundUnits =0;
    
    
    
    
    // [DEBUG] Field: ParmXFundProfit, is_external=, is_static_class=False, static_prefix=
    private decimal _ParmXFundProfit =0;
    
    
    
    
    // [DEBUG] Field: ParmXFundGain, is_external=, is_static_class=False, static_prefix=
    private decimal _ParmXFundGain =0;
    
    
    
    
    // [DEBUG] Field: Filler503, is_external=, is_static_class=False, static_prefix=
    private string _Filler503 ="";
    
    
    
    
public ParmXFundRet() {}

public ParmXFundRet(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetParmXFundUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetParmXFundProfit(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetParmXFundGain(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetFiller503(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetParmXFundRetAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_ParmXFundUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_ParmXFundProfit.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_ParmXFundGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler503.PadRight(8));
    
    return result.ToString();
}

public void SetParmXFundRetAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetParmXFundUnits(parsedDec);
    }
    offset += 13;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetParmXFundProfit(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetParmXFundGain(parsedDec);
    }
    offset += 15;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller503(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public decimal GetParmXFundUnits()
{
    return _ParmXFundUnits;
}

// Standard Setter
public void SetParmXFundUnits(decimal value)
{
    _ParmXFundUnits = value;
}

// Get<>AsString()
public string GetParmXFundUnitsAsString()
{
    return _ParmXFundUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetParmXFundUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _ParmXFundUnits = parsed;
}

// Standard Getter
public decimal GetParmXFundProfit()
{
    return _ParmXFundProfit;
}

// Standard Setter
public void SetParmXFundProfit(decimal value)
{
    _ParmXFundProfit = value;
}

// Get<>AsString()
public string GetParmXFundProfitAsString()
{
    return _ParmXFundProfit.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetParmXFundProfitAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _ParmXFundProfit = parsed;
}

// Standard Getter
public decimal GetParmXFundGain()
{
    return _ParmXFundGain;
}

// Standard Setter
public void SetParmXFundGain(decimal value)
{
    _ParmXFundGain = value;
}

// Get<>AsString()
public string GetParmXFundGainAsString()
{
    return _ParmXFundGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetParmXFundGainAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _ParmXFundGain = parsed;
}

// Standard Getter
public string GetFiller503()
{
    return _Filler503;
}

// Standard Setter
public void SetFiller503(string value)
{
    _Filler503 = value;
}

// Get<>AsString()
public string GetFiller503AsString()
{
    return _Filler503.PadRight(8);
}

// Set<>AsString()
public void SetFiller503AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler503 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}
// Set<>String Override function (Nested)
public void SetParmChR(string value)
{
    _ParmChR.SetParmChRAsString(value);
}
// Nested Class: ParmChR
public class ParmChR
{
    private static int _size = 1;
    
    // Fields in the class
    
    
    // [DEBUG] Field: ParmCh, is_external=, is_static_class=False, static_prefix=
    private string[] _ParmCh = new string[88];
    
    
    
    
public ParmChR() {}

public ParmChR(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    for (int i = 0; i < 88; i++)
    {
        string value = data.Substring(offset, 1);
        _ParmCh[i] = value.Trim();
        offset += 1;
    }
    
}

// Serialization methods
public string GetParmChRAsString()
{
    StringBuilder result = new StringBuilder();
    
    for (int i = 0; i < 88; i++)
    {
        result.Append(_ParmCh[i].PadRight(1));
    }
    
    return result.ToString();
}

public void SetParmChRAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    for (int i = 0; i < 88; i++)
    {
        if (offset + 1 > data.Length) break;
        string val = data.Substring(offset, 1);
        
        _ParmCh[i] = val.Trim();
        offset += 1;
    }
}

// Getter and Setter methods

// Array Accessors for ParmCh
public string GetParmChAt(int index)
{
    return _ParmCh[index];
}

public void SetParmChAt(int index, string value)
{
    _ParmCh[index] = value;
}

public string GetParmChAsStringAt(int index)
{
    return _ParmCh[index].PadRight(1);
}

public void SetParmChAsStringAt(int index, string value)
{
    if (string.IsNullOrEmpty(value)) return;
    _ParmCh[index] = value;
}

// Flattened accessors (index 0)
public string GetParmCh()
{
    return _ParmCh != null && _ParmCh.Length > 0
    ? _ParmCh[0]
    : default(string);
}

public void SetParmCh(string value)
{
    if (_ParmCh == null || _ParmCh.Length == 0)
    _ParmCh = new string[1];
    _ParmCh[0] = value;
}

public string GetParmChAsString()
{
    return _ParmCh != null && _ParmCh.Length > 0
    ? _ParmCh[0].ToString()
    : string.Empty;
}

public void SetParmChAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    if (_ParmCh == null || _ParmCh.Length == 0)
    _ParmCh = new string[1];
    
    _ParmCh[0] = value;
}




public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetParmChFs(string value)
{
    _ParmChFs.SetParmChFsAsString(value);
}
// Nested Class: ParmChFs
public class ParmChFs
{
    private static int _size = 24;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler504, is_external=, is_static_class=False, static_prefix=
    private string _Filler504 ="";
    
    
    
    
    // [DEBUG] Field: LkFundSedol, is_external=, is_static_class=False, static_prefix=
    private ParmChFs.LkFundSedol _LkFundSedol = new ParmChFs.LkFundSedol();
    
    
    
    
    // [DEBUG] Field: Filler505, is_external=, is_static_class=False, static_prefix=
    private string _Filler505 ="";
    
    
    
    
public ParmChFs() {}

public ParmChFs(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller504(data.Substring(offset, 24).Trim());
    offset += 24;
    _LkFundSedol.SetLkFundSedolAsString(data.Substring(offset, LkFundSedol.GetSize()));
    offset += 0;
    SetFiller505(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetParmChFsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler504.PadRight(24));
    result.Append(_LkFundSedol.GetLkFundSedolAsString());
    result.Append(_Filler505.PadRight(0));
    
    return result.ToString();
}

public void SetParmChFsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 24 <= data.Length)
    {
        string extracted = data.Substring(offset, 24).Trim();
        SetFiller504(extracted);
    }
    offset += 24;
    if (offset + 0 <= data.Length)
    {
        _LkFundSedol.SetLkFundSedolAsString(data.Substring(offset, 0));
    }
    else
    {
        _LkFundSedol.SetLkFundSedolAsString(data.Substring(offset));
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller505(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller504()
{
    return _Filler504;
}

// Standard Setter
public void SetFiller504(string value)
{
    _Filler504 = value;
}

// Get<>AsString()
public string GetFiller504AsString()
{
    return _Filler504.PadRight(24);
}

// Set<>AsString()
public void SetFiller504AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler504 = value;
}

// Standard Getter
public LkFundSedol GetLkFundSedol()
{
    return _LkFundSedol;
}

// Standard Setter
public void SetLkFundSedol(LkFundSedol value)
{
    _LkFundSedol = value;
}

// Get<>AsString()
public string GetLkFundSedolAsString()
{
    return _LkFundSedol != null ? _LkFundSedol.GetLkFundSedolAsString() : "";
}

// Set<>AsString()
public void SetLkFundSedolAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_LkFundSedol == null)
    {
        _LkFundSedol = new LkFundSedol();
    }
    _LkFundSedol.SetLkFundSedolAsString(value);
}

// Standard Getter
public string GetFiller505()
{
    return _Filler505;
}

// Standard Setter
public void SetFiller505(string value)
{
    _Filler505 = value;
}

// Get<>AsString()
public string GetFiller505AsString()
{
    return _Filler505.PadRight(0);
}

// Set<>AsString()
public void SetFiller505AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler505 = value;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: LkFundSedol
public class LkFundSedol
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: LkFund, is_external=, is_static_class=False, static_prefix=
    private string _LkFund ="";
    
    
    
    
    // [DEBUG] Field: LkSedol, is_external=, is_static_class=False, static_prefix=
    private string _LkSedol ="";
    
    
    
    
public LkFundSedol() {}

public LkFundSedol(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetLkFund(data.Substring(offset, 0).Trim());
    offset += 0;
    SetLkSedol(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetLkFundSedolAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_LkFund.PadRight(0));
    result.Append(_LkSedol.PadRight(0));
    
    return result.ToString();
}

public void SetLkFundSedolAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetLkFund(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetLkSedol(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetLkFund()
{
    return _LkFund;
}

// Standard Setter
public void SetLkFund(string value)
{
    _LkFund = value;
}

// Get<>AsString()
public string GetLkFundAsString()
{
    return _LkFund.PadRight(0);
}

// Set<>AsString()
public void SetLkFundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LkFund = value;
}

// Standard Getter
public string GetLkSedol()
{
    return _LkSedol;
}

// Standard Setter
public void SetLkSedol(string value)
{
    _LkSedol = value;
}

// Get<>AsString()
public string GetLkSedolAsString()
{
    return _LkSedol.PadRight(0);
}

// Set<>AsString()
public void SetLkSedolAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LkSedol = value;
}



public static int GetSize()
{
    return _size;
}

}
}

}}