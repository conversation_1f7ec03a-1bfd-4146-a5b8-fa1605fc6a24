using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WOptionPremium Data Structure

public class WOptionPremium
{
    private static int _size = 9;
    // [DEBUG] Class: WOptionPremium, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WIndexOp, is_external=, is_static_class=False, static_prefix=
    private int _WIndexOp =1;
    
    
    
    
    // [DEBUG] Field: WIndexStartWp, is_external=, is_static_class=False, static_prefix=
    private int _WIndexStartWp =0;
    
    
    
    
    // [DEBUG] Field: WOptionPremiumFlag, is_external=, is_static_class=False, static_prefix=
    private string _WOptionPremiumFlag ="Y";
    
    
    // 88-level condition checks for WOptionPremiumFlag
    public bool IsWOptionPremiumY()
    {
        if (this._WOptionPremiumFlag == "'Y'") return true;
        return false;
    }
    public bool IsWOptionPremiumN()
    {
        if (this._WOptionPremiumFlag == "'N'") return true;
        return false;
    }
    
    
    
    // Serialization methods
    public string GetWOptionPremiumAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WIndexOp.ToString().PadLeft(4, '0'));
        result.Append(_WIndexStartWp.ToString().PadLeft(4, '0'));
        result.Append(_WOptionPremiumFlag.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWOptionPremiumAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWIndexOp(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWIndexStartWp(parsedInt);
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWOptionPremiumFlag(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWOptionPremiumAsString();
    }
    // Set<>String Override function
    public void SetWOptionPremium(string value)
    {
        SetWOptionPremiumAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWIndexOp()
    {
        return _WIndexOp;
    }
    
    // Standard Setter
    public void SetWIndexOp(int value)
    {
        _WIndexOp = value;
    }
    
    // Get<>AsString()
    public string GetWIndexOpAsString()
    {
        return _WIndexOp.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWIndexOpAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WIndexOp = parsed;
    }
    
    // Standard Getter
    public int GetWIndexStartWp()
    {
        return _WIndexStartWp;
    }
    
    // Standard Setter
    public void SetWIndexStartWp(int value)
    {
        _WIndexStartWp = value;
    }
    
    // Get<>AsString()
    public string GetWIndexStartWpAsString()
    {
        return _WIndexStartWp.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWIndexStartWpAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WIndexStartWp = parsed;
    }
    
    // Standard Getter
    public string GetWOptionPremiumFlag()
    {
        return _WOptionPremiumFlag;
    }
    
    // Standard Setter
    public void SetWOptionPremiumFlag(string value)
    {
        _WOptionPremiumFlag = value;
    }
    
    // Get<>AsString()
    public string GetWOptionPremiumFlagAsString()
    {
        return _WOptionPremiumFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWOptionPremiumFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WOptionPremiumFlag = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
