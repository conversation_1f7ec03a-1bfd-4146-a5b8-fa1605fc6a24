using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D68HeaderRecord Data Structure

public class D68HeaderRecord
{
    private static int _size = 53;
    // [DEBUG] Class: D68HeaderRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler77, is_external=, is_static_class=False, static_prefix=
    private string _Filler77 ="";
    
    
    // 88-level condition checks for Filler77
    public bool IsHeaderFound()
    {
        if (this._Filler77 == "'0000MICRO CGT FUND HEADER'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D68HeaderDate, is_external=, is_static_class=False, static_prefix=
    private string _D68HeaderDate ="";
    
    
    
    
    // [DEBUG] Field: Filler78, is_external=, is_static_class=False, static_prefix=
    private string _Filler78 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD68HeaderRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler77.PadRight(44));
        result.Append(_D68HeaderDate.PadRight(6));
        result.Append(_Filler78.PadRight(3));
        
        return result.ToString();
    }
    
    public void SetD68HeaderRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 44 <= data.Length)
        {
            string extracted = data.Substring(offset, 44).Trim();
            SetFiller77(extracted);
        }
        offset += 44;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetD68HeaderDate(extracted);
        }
        offset += 6;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetFiller78(extracted);
        }
        offset += 3;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD68HeaderRecordAsString();
    }
    // Set<>String Override function
    public void SetD68HeaderRecord(string value)
    {
        SetD68HeaderRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller77()
    {
        return _Filler77;
    }
    
    // Standard Setter
    public void SetFiller77(string value)
    {
        _Filler77 = value;
    }
    
    // Get<>AsString()
    public string GetFiller77AsString()
    {
        return _Filler77.PadRight(44);
    }
    
    // Set<>AsString()
    public void SetFiller77AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler77 = value;
    }
    
    // Standard Getter
    public string GetD68HeaderDate()
    {
        return _D68HeaderDate;
    }
    
    // Standard Setter
    public void SetD68HeaderDate(string value)
    {
        _D68HeaderDate = value;
    }
    
    // Get<>AsString()
    public string GetD68HeaderDateAsString()
    {
        return _D68HeaderDate.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetD68HeaderDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D68HeaderDate = value;
    }
    
    // Standard Getter
    public string GetFiller78()
    {
        return _Filler78;
    }
    
    // Standard Setter
    public void SetFiller78(string value)
    {
        _Filler78 = value;
    }
    
    // Get<>AsString()
    public string GetFiller78AsString()
    {
        return _Filler78.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetFiller78AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler78 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}