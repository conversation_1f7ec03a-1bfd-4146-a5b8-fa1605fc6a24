using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtabortDTO
{// <Section> Class for Ivar
public class Ivar
{
public Ivar() {}

// Fields in the class


// [DEBUG] Field: CommonLinkage, is_external=, is_static_class=False, static_prefix=
private CommonLinkage _CommonLinkage = new CommonLinkage();




// [DEBUG] Field: COUNTRY_FILE, is_external=, is_static_class=False, static_prefix=
public const string COUNTRY_FILE = "CGTCTRY ";




// [DEBUG] Field: GROUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string GROUP_FILE = "CGTGRP  ";




// [DEBUG] Field: STOCK_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_FILE = "CGTSTK  ";




// [DEBUG] Field: FUND_FILE, is_external=, is_static_class=False, static_prefix=
public const string FUND_FILE = "M-U-FUND";




// [DEBUG] Field: RPI_FILE, is_external=, is_static_class=False, static_prefix=
public const string RPI_FILE = "CGTRPI  ";




// [DEBUG] Field: PARAMETER_FILE, is_external=, is_static_class=False, static_prefix=
public const string PARAMETER_FILE = "M-PARAM ";




// [DEBUG] Field: USER_FILE, is_external=, is_static_class=False, static_prefix=
public const string USER_FILE = "M-USER  ";




// [DEBUG] Field: OUTPUT_LISTING, is_external=, is_static_class=False, static_prefix=
public const string OUTPUT_LISTING = "M-OUTLST";




// [DEBUG] Field: MASTER_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string MASTER_LOG_FILE = "M-LOG   ";




// [DEBUG] Field: REALISED_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string REALISED_DATA_FILE = "CGTDR   ";




// [DEBUG] Field: UNREALISED_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string UNREALISED_DATA_FILE = "CGTDU   ";




// [DEBUG] Field: NOTIONAL_SALE_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string NOTIONAL_SALE_DATA_FILE = "CGTDN   ";




// [DEBUG] Field: REALISED_TAX_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string REALISED_TAX_DATA_FILE = "CGTDT   ";




// [DEBUG] Field: UNREALISED_TAX_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string UNREALISED_TAX_DATA_FILE = "CGTDX   ";




// [DEBUG] Field: ERROR_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string ERROR_DATA_FILE = "CGTERR  ";




// [DEBUG] Field: PRINTER_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRINTER_FILE = "M-PRINT ";




// [DEBUG] Field: STOCK_TYPE_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_TYPE_FILE = "M-STOCK ";




// [DEBUG] Field: YE_REC2_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_REC2_DATA_FILE = "CGTYERR ";




// [DEBUG] Field: TRANSACTION_CODE_FILE, is_external=, is_static_class=False, static_prefix=
public const string TRANSACTION_CODE_FILE = "M-TRANS ";




// [DEBUG] Field: OUTPUT_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string OUTPUT_LOG_FILE = "M-OUTLOG";




// [DEBUG] Field: MESSAGE_FILE, is_external=, is_static_class=False, static_prefix=
public const string MESSAGE_FILE = "M-MESS  ";




// [DEBUG] Field: USER_FUND_FILE, is_external=, is_static_class=False, static_prefix=
public const string USER_FUND_FILE = "CGTFUNDX";




// [DEBUG] Field: HELP_TEXT_FILE, is_external=, is_static_class=False, static_prefix=
public const string HELP_TEXT_FILE = "M-HELP  ";




// [DEBUG] Field: DEFAULT_ACCESS_FILE, is_external=, is_static_class=False, static_prefix=
public const string DEFAULT_ACCESS_FILE = "M-DEF-AC";




// [DEBUG] Field: ACCESS_PROFILE_FILE, is_external=, is_static_class=False, static_prefix=
public const string ACCESS_PROFILE_FILE = "M-ACCESS";




// [DEBUG] Field: EXTEL_PRICES_FILE, is_external=, is_static_class=False, static_prefix=
public const string EXTEL_PRICES_FILE = "M-EPRICE";




// [DEBUG] Field: EXTEL_CURRENCY_FILE, is_external=, is_static_class=False, static_prefix=
public const string EXTEL_CURRENCY_FILE = "M-CURR  ";




// [DEBUG] Field: STOCK_PRICE_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_PRICE_FILE = "M-SPRICE";




// [DEBUG] Field: EXTEL_TRANSMISSION_FILE, is_external=, is_static_class=False, static_prefix=
public const string EXTEL_TRANSMISSION_FILE = "M-ETRANS";




// [DEBUG] Field: SEQ_BALANCE_FILE, is_external=, is_static_class=False, static_prefix=
public const string SEQ_BALANCE_FILE = "CGTMFO  ";




// [DEBUG] Field: TRANSACTION_FILE, is_external=, is_static_class=False, static_prefix=
public const string TRANSACTION_FILE = "CGTTRANS";




// [DEBUG] Field: BACKUP_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string BACKUP_LOG_FILE = "M-BK-LOG";




// [DEBUG] Field: BACKUP_DETAILS_FILE, is_external=, is_static_class=False, static_prefix=
public const string BACKUP_DETAILS_FILE = "M-BK-DET";




// [DEBUG] Field: STOCK_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_LOAD_DATA_FILE = "M-ST-DAT";




// [DEBUG] Field: FUNDS_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string FUNDS_LOAD_DATA_FILE = "M-FN-DAT";




// [DEBUG] Field: PRICE_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRICE_LOAD_DATA_FILE = "M-PR-DAT";




// [DEBUG] Field: BALANCE_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string BALANCE_LOAD_DATA_FILE = "M-BL-DAT";




// [DEBUG] Field: REPLACEMENT_ACQ_FILE, is_external=, is_static_class=False, static_prefix=
public const string REPLACEMENT_ACQ_FILE = "M-RP-ACQ";




// [DEBUG] Field: REPLACEMENT_DIS_FILE, is_external=, is_static_class=False, static_prefix=
public const string REPLACEMENT_DIS_FILE = "M-RP-DIS";




// [DEBUG] Field: REPORT_RUN_MSG_FILE, is_external=, is_static_class=False, static_prefix=
public const string REPORT_RUN_MSG_FILE = "CGTMSG  ";




// [DEBUG] Field: RCF_FILE, is_external=, is_static_class=False, static_prefix=
public const string RCF_FILE = "CGTRCF  ";




// [DEBUG] Field: RPI_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string RPI_LOAD_DATA_FILE = "M-RPIDAT";




// [DEBUG] Field: COUNTRY_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string COUNTRY_LOAD_DATA_FILE = "M-CY-DAT";




// [DEBUG] Field: GROUP_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string GROUP_LOAD_DATA_FILE = "M-GR-DAT";




// [DEBUG] Field: GAINLOSS_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string GAINLOSS_DATA_FILE = "CGTGLDAT";




// [DEBUG] Field: ACQUISITION_EXPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string ACQUISITION_EXPORT_FILE = "MAEXPORT";




// [DEBUG] Field: TAPER_RATE_FILE, is_external=, is_static_class=False, static_prefix=
public const string TAPER_RATE_FILE = "TAPERATE";




// [DEBUG] Field: ASSET_USAGE_CALENDAR_FILE, is_external=, is_static_class=False, static_prefix=
public const string ASSET_USAGE_CALENDAR_FILE = "ASSETUC ";




// [DEBUG] Field: PERIOD_END_CALENDAR_FILE, is_external=, is_static_class=False, static_prefix=
public const string PERIOD_END_CALENDAR_FILE = "PENDCAL ";




// [DEBUG] Field: PERIOD_END_CALENDAR_DATES_FILE, is_external=, is_static_class=False, static_prefix=
public const string PERIOD_END_CALENDAR_DATES_FILE = "PENDCALD";




// [DEBUG] Field: INTER_CONNECTED_FUNDS_FILE, is_external=, is_static_class=False, static_prefix=
public const string INTER_CONNECTED_FUNDS_FILE = "ICFUNDS ";




// [DEBUG] Field: PRICE_TYPES_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRICE_TYPES_FILE = "PRICETYP";




// [DEBUG] Field: PENDING_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string PENDING_LOG_FILE = "PENDLOG ";




// [DEBUG] Field: PENDING_ITEMS_FILE, is_external=, is_static_class=False, static_prefix=
public const string PENDING_ITEMS_FILE = "PENDITEM";




// [DEBUG] Field: GAINLOSS_DATA_FILE_LR, is_external=, is_static_class=False, static_prefix=
public const string GAINLOSS_DATA_FILE_LR = "CGTGLDLR";




// [DEBUG] Field: CG01_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CG01_REPORT_FILE = "M-CG01  ";




// [DEBUG] Field: CG02_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CG02_REPORT_FILE = "M-CG02  ";




// [DEBUG] Field: CG03_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CG03_REPORT_FILE = "M-CG03  ";




// [DEBUG] Field: ERROR_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string ERROR_REPORT_FILE = "M-ERR-RP";




// [DEBUG] Field: REALISED_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string REALISED_SCHEDULE_FILE = "CGTPR   ";




// [DEBUG] Field: UNREALISED_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string UNREALISED_SCHEDULE_FILE = "CGTPU   ";




// [DEBUG] Field: NOTIONAL_SALE_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string NOTIONAL_SALE_SCHEDULE_FILE = "CGTPN   ";




// [DEBUG] Field: REALISED_TAX_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string REALISED_TAX_SCHEDULE_FILE = "CGTPT   ";




// [DEBUG] Field: UNREALISED_TAX_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string UNREALISED_TAX_SCHEDULE_FILE = "CGTPX   ";




// [DEBUG] Field: OFFSHORE_INCOME_REPORT, is_external=, is_static_class=False, static_prefix=
public const string OFFSHORE_INCOME_REPORT = "M-OF-REP";




// [DEBUG] Field: YE_REC_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_REC_REPORT_FILE = "M-YE-REC";




// [DEBUG] Field: YE_DEL_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_DEL_REPORT_FILE = "M-YE-DEL";




// [DEBUG] Field: YE_CON_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_CON_REPORT_FILE = "M-YE-CON";




// [DEBUG] Field: YE_ERR_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_ERR_REPORT_FILE = "M-YE-ERR";




// [DEBUG] Field: YE_BAL_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_BAL_REPORT_FILE = "M-YE-BAL";




// [DEBUG] Field: FOREIGN_EXTEL_REPORT, is_external=, is_static_class=False, static_prefix=
public const string FOREIGN_EXTEL_REPORT = "M-EX-FOR";




// [DEBUG] Field: STERLING_EXTEL_REPORT, is_external=, is_static_class=False, static_prefix=
public const string STERLING_EXTEL_REPORT = "M-EX-STL";




// [DEBUG] Field: CGTR04_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CGTR04_REPORT_FILE = "M-CGTR04";




// [DEBUG] Field: CGTR05_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CGTR05_REPORT_FILE = "M-CGTR05";




// [DEBUG] Field: STOCK_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string STOCK_LOAD_REPORT = "M-ST-REP";




// [DEBUG] Field: FUNDS_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string FUNDS_LOAD_REPORT = "M-FN-REP";




// [DEBUG] Field: PRICE_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string PRICE_LOAD_REPORT = "M-PR-REP";




// [DEBUG] Field: SKAN1_REPORT, is_external=, is_static_class=False, static_prefix=
public const string SKAN1_REPORT = "M-SK1REP";




// [DEBUG] Field: SKAN2_REPORT, is_external=, is_static_class=False, static_prefix=
public const string SKAN2_REPORT = "M-SK2REP";




// [DEBUG] Field: NEW_REALISED_REPORT, is_external=, is_static_class=False, static_prefix=
public const string NEW_REALISED_REPORT = "M-NRGREP";




// [DEBUG] Field: NEW_UNREALISED_REPORT, is_external=, is_static_class=False, static_prefix=
public const string NEW_UNREALISED_REPORT = "M-NUGREP";




// [DEBUG] Field: MGM1_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string MGM1_REPORT_FILE = "M-MGMREP";




// [DEBUG] Field: CAPITAL_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CAPITAL_REPORT_FILE = "M-CAPREP";




// [DEBUG] Field: BALANCES_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string BALANCES_LOAD_REPORT = "M-BALREP";




// [DEBUG] Field: REPLACEMENT_RELIEF_REPORT, is_external=, is_static_class=False, static_prefix=
public const string REPLACEMENT_RELIEF_REPORT = "M-RP-REP";




// [DEBUG] Field: RPI_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string RPI_LOAD_REPORT = "M-RPIREP";




// [DEBUG] Field: COUNTRY_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string COUNTRY_LOAD_REPORT = "M-CY-REP";




// [DEBUG] Field: GROUP_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string GROUP_LOAD_REPORT = "M-GR-REP";




// [DEBUG] Field: GAINLOSS_REPORT, is_external=, is_static_class=False, static_prefix=
public const string GAINLOSS_REPORT = "CGTGLREP";




// [DEBUG] Field: LOST_INDEXATION_REPORT, is_external=, is_static_class=False, static_prefix=
public const string LOST_INDEXATION_REPORT = "M-ILOST ";




// [DEBUG] Field: LOSU_INDEXATION_REPORT, is_external=, is_static_class=False, static_prefix=
public const string LOSU_INDEXATION_REPORT = "M-ILOSU ";




// [DEBUG] Field: REAL_H_O_GAINS_REPORT, is_external=, is_static_class=False, static_prefix=
public const string REAL_H_O_GAINS_REPORT = "M-R-HO-G";




// [DEBUG] Field: UNREAL_H_O_GAINS_REPORT, is_external=, is_static_class=False, static_prefix=
public const string UNREAL_H_O_GAINS_REPORT = "M-U-HO-G";




// [DEBUG] Field: BATCH_RUN_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string BATCH_RUN_LOG_FILE = "B-RUNLOG";




// [DEBUG] Field: BATCH_QUIT_RUN_FILE, is_external=, is_static_class=False, static_prefix=
public const string BATCH_QUIT_RUN_FILE = "BQUITRUN";




// [DEBUG] Field: TRACE_FILE, is_external=, is_static_class=False, static_prefix=
public const string TRACE_FILE = "M-TRACE ";




// [DEBUG] Field: ERROR_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string ERROR_LOG_FILE = "M-ERRLOG";




// [DEBUG] Field: TAPER_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string TAPER_REPORT_FILE = "TAPERREP";




// [DEBUG] Field: TAPER_EXPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string TAPER_EXPORT_FILE = "TAPEREXP";




// [DEBUG] Field: ALLOWANCES_FROM_DB_FILE, is_external=, is_static_class=False, static_prefix=
public const string ALLOWANCES_FROM_DB_FILE = "ALLOWANC";




// [DEBUG] Field: LOSSES_FROM_DB_FILE, is_external=, is_static_class=False, static_prefix=
public const string LOSSES_FROM_DB_FILE = "LOSSFMDB";




// [DEBUG] Field: DISPOSALS_FROM_DB_FILE, is_external=, is_static_class=False, static_prefix=
public const string DISPOSALS_FROM_DB_FILE = "OTHERDIS";




// [DEBUG] Field: INTER_CONNECTED_FUNDS_EXPORT, is_external=, is_static_class=False, static_prefix=
public const string INTER_CONNECTED_FUNDS_EXPORT = "ICFUNDSE";




// [DEBUG] Field: INTER_CONNECTED_FUNDS_REPORT, is_external=, is_static_class=False, static_prefix=
public const string INTER_CONNECTED_FUNDS_REPORT = "ICFUNDSR";




// [DEBUG] Field: CGTR06_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CGTR06_REPORT_FILE = "M-CGTR06";




// [DEBUG] Field: DAILY_TRANSACTION_EXPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string DAILY_TRANSACTION_EXPORT_FILE = "DAILYTRN";




// [DEBUG] Field: COUNTRY_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string COUNTRY_BACKUP_FILE = "M-D01-BK";




// [DEBUG] Field: GROUP_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string GROUP_BACKUP_FILE = "M-D02-BK";




// [DEBUG] Field: STOCK_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_BACKUP_FILE = "M-D03-BK";




// [DEBUG] Field: FUND_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string FUND_BACKUP_FILE = "M-D04-BK";




// [DEBUG] Field: RPI_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string RPI_BACKUP_FILE = "M-D07-BK";




// [DEBUG] Field: PARAMETER_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string PARAMETER_BACKUP_FILE = "M-D08-BK";




// [DEBUG] Field: USER_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string USER_BACKUP_FILE = "M-D09-BK";




// [DEBUG] Field: MASTER_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string MASTER_BACKUP_FILE = "M-D13-BK";




// [DEBUG] Field: PRINTER_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRINTER_BACKUP_FILE = "M-D31-BK";




// [DEBUG] Field: OUT_LOG_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string OUT_LOG_BACKUP_FILE = "M-D35-BK";




// [DEBUG] Field: USER_FUNDS_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string USER_FUNDS_BACKUP_FILE = "M-D37-BK";




// [DEBUG] Field: ACCESS_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string ACCESS_BACKUP_FILE = "M-D40-BK";




// [DEBUG] Field: PRICE_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRICE_BACKUP_FILE = "M-D43-BK";




// [DEBUG] Field: BACKUP_LOG_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string BACKUP_LOG_BACKUP_FILE = "M-D47-BK";




// [DEBUG] Field: BACKUP_DETAILS_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string BACKUP_DETAILS_BACKUP_FILE = "M-D48-BK";




// [DEBUG] Field: REPORTS_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string REPORTS_BACKUP_FILE = "M-REP-BK";




// [DEBUG] Field: RCF_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string RCF_BACKUP_FILE = "M-D86-BK";




// [DEBUG] Field: OPEN_OP, is_external=, is_static_class=False, static_prefix=
public const string OPEN_OP = "OP ";




// [DEBUG] Field: OPEN_INPUT, is_external=, is_static_class=False, static_prefix=
public const string OPEN_INPUT = "OI ";




// [DEBUG] Field: OPEN_INPUT_REPORT, is_external=, is_static_class=False, static_prefix=
public const string OPEN_INPUT_REPORT = "OIR";




// [DEBUG] Field: OPEN_OUTPUT, is_external=, is_static_class=False, static_prefix=
public const string OPEN_OUTPUT = "OO ";




// [DEBUG] Field: OPEN_I_O, is_external=, is_static_class=False, static_prefix=
public const string OPEN_I_O = "IO ";




// [DEBUG] Field: OPEN_EXTEND, is_external=, is_static_class=False, static_prefix=
public const string OPEN_EXTEND = "OE ";




// [DEBUG] Field: OPEN_SINGLE_MASTER_FILE_INPUT, is_external=, is_static_class=False, static_prefix=
public const string OPEN_SINGLE_MASTER_FILE_INPUT = "OS ";




// [DEBUG] Field: CLOSE_SINGLE_MASTER_FILE_INPUT, is_external=, is_static_class=False, static_prefix=
public const string CLOSE_SINGLE_MASTER_FILE_INPUT = "CS ";




// [DEBUG] Field: SET_MASTER_FILE_NAMES, is_external=, is_static_class=False, static_prefix=
public const string SET_MASTER_FILE_NAMES = "FN ";




// [DEBUG] Field: START_EQUAL, is_external=, is_static_class=False, static_prefix=
public const string START_EQUAL = "SE ";




// [DEBUG] Field: START_NOT_LESS_THAN, is_external=, is_static_class=False, static_prefix=
public const string START_NOT_LESS_THAN = "SN ";




// [DEBUG] Field: START_NOT_LESS_THAN_KEY2, is_external=, is_static_class=False, static_prefix=
public const string START_NOT_LESS_THAN_KEY2 = "SN2";




// [DEBUG] Field: START_NOT_LESS_THAN_KEY3, is_external=, is_static_class=False, static_prefix=
public const string START_NOT_LESS_THAN_KEY3 = "SN3";




// [DEBUG] Field: START_GREATER_THAN, is_external=, is_static_class=False, static_prefix=
public const string START_GREATER_THAN = "SG ";




// [DEBUG] Field: START_GT_INVERSE_KEY, is_external=, is_static_class=False, static_prefix=
public const string START_GT_INVERSE_KEY = "SGI";




// [DEBUG] Field: START_LESS_THAN, is_external=, is_static_class=False, static_prefix=
public const string START_LESS_THAN = "SL ";




// [DEBUG] Field: START_NOT_GREATER_THAN, is_external=, is_static_class=False, static_prefix=
public const string START_NOT_GREATER_THAN = "SNG";




// [DEBUG] Field: READ_NEXT, is_external=, is_static_class=False, static_prefix=
public const string READ_NEXT = "RN ";




// [DEBUG] Field: READ_NEXT_WITH_LOCK, is_external=, is_static_class=False, static_prefix=
public const string READ_NEXT_WITH_LOCK = "RNL";




// [DEBUG] Field: READ_RECORD, is_external=, is_static_class=False, static_prefix=
public const string READ_RECORD = "RD ";




// [DEBUG] Field: READ_REPORT, is_external=, is_static_class=False, static_prefix=
public const string READ_REPORT = "RDR";




// [DEBUG] Field: READ_WITH_LOCK, is_external=, is_static_class=False, static_prefix=
public const string READ_WITH_LOCK = "RDL";




// [DEBUG] Field: READ_ON_KEY2, is_external=, is_static_class=False, static_prefix=
public const string READ_ON_KEY2 = "RD2";




// [DEBUG] Field: READ_ON_KEY3, is_external=, is_static_class=False, static_prefix=
public const string READ_ON_KEY3 = "RD3";




// [DEBUG] Field: READ_PREVIOUS_MASTER, is_external=, is_static_class=False, static_prefix=
public const string READ_PREVIOUS_MASTER = "RP ";




// [DEBUG] Field: WRITE_RECORD, is_external=, is_static_class=False, static_prefix=
public const string WRITE_RECORD = "WR ";




// [DEBUG] Field: REWRITE_RECORD, is_external=, is_static_class=False, static_prefix=
public const string REWRITE_RECORD = "RW ";




// [DEBUG] Field: DELETE_RECORD, is_external=, is_static_class=False, static_prefix=
public const string DELETE_RECORD = "DE ";




// [DEBUG] Field: CLOSE_FILE, is_external=, is_static_class=False, static_prefix=
public const string CLOSE_FILE = "CL ";




// [DEBUG] Field: CLOSE_REPORT, is_external=, is_static_class=False, static_prefix=
public const string CLOSE_REPORT = "CLR";




// [DEBUG] Field: CLOSE_ALL_FILES, is_external=, is_static_class=False, static_prefix=
public const string CLOSE_ALL_FILES = "CA ";




// [DEBUG] Field: UNLOCK_RECORD, is_external=, is_static_class=False, static_prefix=
public const string UNLOCK_RECORD = "UN ";




// [DEBUG] Field: BUILD_MASTER_FILE, is_external=, is_static_class=False, static_prefix=
public const string BUILD_MASTER_FILE = "BL ";




// [DEBUG] Field: GET_CALENDAR_DATES, is_external=, is_static_class=False, static_prefix=
public const string GET_CALENDAR_DATES = "GC ";




// [DEBUG] Field: SET_TRANS_EXPORTED_FLAGS, is_external=, is_static_class=False, static_prefix=
public const string SET_TRANS_EXPORTED_FLAGS = "TX ";




// [DEBUG] Field: READ_USER_FUND_RECORD, is_external=, is_static_class=False, static_prefix=
public const string READ_USER_FUND_RECORD = "RU ";




// [DEBUG] Field: READ_NEXT_USER_FUND_RECORD, is_external=, is_static_class=False, static_prefix=
public const string READ_NEXT_USER_FUND_RECORD = "NU ";




// [DEBUG] Field: GET_CONFIG_VALUE, is_external=, is_static_class=False, static_prefix=
public const string GET_CONFIG_VALUE = "CV ";




// [DEBUG] Field: GET_REQUEST_OPTIONS, is_external=, is_static_class=False, static_prefix=
public const string GET_REQUEST_OPTIONS = "RO ";




// [DEBUG] Field: REMAP_C_F_TRANSACTIONS, is_external=, is_static_class=False, static_prefix=
public const string REMAP_C_F_TRANSACTIONS = "RT ";




// [DEBUG] Field: RETURN_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] RETURN_KEY = new byte[] { 0x30, 0x30 };




// [DEBUG] Field: ESC_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] ESC_KEY = new byte[] { 0x31, 0x00 };




// [DEBUG] Field: F1_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F1_KEY = new byte[] { 0x31, 0x01 };




// [DEBUG] Field: F2_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F2_KEY = new byte[] { 0x31, 0x02 };




// [DEBUG] Field: F3_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F3_KEY = new byte[] { 0x31, 0x03 };




// [DEBUG] Field: F4_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F4_KEY = new byte[] { 0x31, 0x04 };




// [DEBUG] Field: F5_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F5_KEY = new byte[] { 0x31, 0x05 };




// [DEBUG] Field: F6_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F6_KEY = new byte[] { 0x31, 0x06 };




// [DEBUG] Field: F7_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F7_KEY = new byte[] { 0x31, 0x07 };




// [DEBUG] Field: F8_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F8_KEY = new byte[] { 0x31, 0x08 };




// [DEBUG] Field: F9_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F9_KEY = new byte[] { 0x31, 0x09 };




// [DEBUG] Field: F10_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F10_KEY = new byte[] { 0x31, 0x0A };




// [DEBUG] Field: CURSOR_UP, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CURSOR_UP = new byte[] { 0x31, 0x18 };




// [DEBUG] Field: CURSOR_DOWN, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CURSOR_DOWN = new byte[] { 0x31, 0x19 };




// [DEBUG] Field: CURSOR_LEFT, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CURSOR_LEFT = new byte[] { 0x31, 0x1B };




// [DEBUG] Field: CURSOR_RIGHT, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CURSOR_RIGHT = new byte[] { 0x31, 0x1A };




// [DEBUG] Field: PAGE_UP, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] PAGE_UP = new byte[] { 0x31, 0x1C };




// [DEBUG] Field: PAGE_DOWN, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] PAGE_DOWN = new byte[] { 0x31, 0x1D };




// [DEBUG] Field: ACCEPT_PAGE_UP, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] ACCEPT_PAGE_UP = new byte[] { 0x31, 0x35 };




// [DEBUG] Field: ACCEPT_PAGE_DOWN, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] ACCEPT_PAGE_DOWN = new byte[] { 0x31, 0x36 };




// [DEBUG] Field: INSERT_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] INSERT_KEY = new byte[] { 0x31, 0x1E };




// [DEBUG] Field: DELETE_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] DELETE_KEY = new byte[] { 0x31, 0x1F };




// [DEBUG] Field: F11_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F11_KEY = new byte[] { 0x31, 0x20 };




// [DEBUG] Field: F12_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F12_KEY = new byte[] { 0x31, 0x21 };




// [DEBUG] Field: HOME_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] HOME_KEY = new byte[] { 0x31, 0x22 };




// [DEBUG] Field: END_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] END_KEY = new byte[] { 0x31, 0x23 };




// [DEBUG] Field: CONTROL_HOME, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CONTROL_HOME = new byte[] { 0x31, 0x24 };




// [DEBUG] Field: CONTROL_END, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CONTROL_END = new byte[] { 0x31, 0x25 };




// [DEBUG] Field: CONTROL_PAGE_UP, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CONTROL_PAGE_UP = new byte[] { 0x31, 0x26 };




// [DEBUG] Field: CONTROL_PAGE_DOWN, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CONTROL_PAGE_DOWN = new byte[] { 0x31, 0x27 };




// [DEBUG] Field: FIRST_MASTER_YEAR, is_external=, is_static_class=False, static_prefix=
public const int FIRST_MASTER_YEAR = 1982;




// [DEBUG] Field: LAST_MASTER_YEAR, is_external=, is_static_class=False, static_prefix=
public const int LAST_MASTER_YEAR = 2044;




// [DEBUG] Field: FIRST_MASTER_YEAR_YY, is_external=, is_static_class=False, static_prefix=
public const int FIRST_MASTER_YEAR_YY = 82;




// [DEBUG] Field: LAST_MASTER_YEAR_YY, is_external=, is_static_class=False, static_prefix=
public const int LAST_MASTER_YEAR_YY = 44;




// [DEBUG] Field: FIRST_PERIOD_DATE, is_external=, is_static_class=False, static_prefix=
public const int FIRST_PERIOD_DATE = 19820101;




// [DEBUG] Field: LAST_PERIOD_DATE, is_external=, is_static_class=False, static_prefix=
public const int LAST_PERIOD_DATE = 20441231;




// [DEBUG] Field: FIRST_PERIOD_DATE_YYMMDD, is_external=, is_static_class=False, static_prefix=
public const int FIRST_PERIOD_DATE_YYMMDD = 820101;




// [DEBUG] Field: LAST_PERIOD_DATE_YYMMDD, is_external=, is_static_class=False, static_prefix=
public const int LAST_PERIOD_DATE_YYMMDD = 441231;




// [DEBUG] Field: CLIENT_PERIOD_END_DATE_97_98, is_external=, is_static_class=False, static_prefix=
public const int CLIENT_PERIOD_END_DATE_97_98 = 19980405;




// [DEBUG] Field: CLIENT_PERIOD_START_DATE_98_99, is_external=, is_static_class=False, static_prefix=
public const int CLIENT_PERIOD_START_DATE_98_99 = 19980406;




// [DEBUG] Field: CLIENT_PERIOD_START_DATE_00_01, is_external=, is_static_class=False, static_prefix=
public const int CLIENT_PERIOD_START_DATE_00_01 = 20000406;




// [DEBUG] Field: REIT_PROCESSING_START_DATE, is_external=, is_static_class=False, static_prefix=
public const int REIT_PROCESSING_START_DATE = 20070101;




// [DEBUG] Field: CREATE_2008_POOL_DATE, is_external=, is_static_class=False, static_prefix=
public const int CREATE_2008_POOL_DATE = 20080406;




// [DEBUG] Field: CREATE_2008_POOL_DATE_YYMMDD, is_external=, is_static_class=False, static_prefix=
public const int CREATE_2008_POOL_DATE_YYMMDD = 080406;




// [DEBUG] Field: FIRST_USABLE_DATE, is_external=, is_static_class=False, static_prefix=
public const int FIRST_USABLE_DATE = 450101;




// [DEBUG] Field: GROUP_NO_ACTION, is_external=, is_static_class=False, static_prefix=
public const int GROUP_NO_ACTION = 1;




// [DEBUG] Field: GROUP_PRO_RATA, is_external=, is_static_class=False, static_prefix=
public const int GROUP_PRO_RATA = 2;




// [DEBUG] Field: GROUP_NON_OLAB_FUND_DEFAULT, is_external=, is_static_class=False, static_prefix=
public const int GROUP_NON_OLAB_FUND_DEFAULT = 3;




// [DEBUG] Field: GROUP_OLAB_FUND_DEFAULT, is_external=, is_static_class=False, static_prefix=
public const int GROUP_OLAB_FUND_DEFAULT = 4;




// [DEBUG] Field: USE_EARLIER_PRICE_NONE, is_external=, is_static_class=False, static_prefix=
public const string USE_EARLIER_PRICE_NONE = " ";




// [DEBUG] Field: USE_EARLIER_PRICE_PREV_DAY, is_external=, is_static_class=False, static_prefix=
public const string USE_EARLIER_PRICE_PREV_DAY = "D";




// [DEBUG] Field: USE_EARLIER_PRICE_PREV_SESSION, is_external=, is_static_class=False, static_prefix=
public const string USE_EARLIER_PRICE_PREV_SESSION = "S";




// [DEBUG] Field: CgtabortLinkage, is_external=, is_static_class=False, static_prefix=
private CgtabortLinkage _CgtabortLinkage = new CgtabortLinkage();




// Getter and Setter methods

// Standard Getter
public CommonLinkage GetCommonLinkage()
{
    return _CommonLinkage;
}

// Standard Setter
public void SetCommonLinkage(CommonLinkage value)
{
    _CommonLinkage = value;
}

// Get<>AsString()
public string GetCommonLinkageAsString()
{
    return _CommonLinkage != null ? _CommonLinkage.GetCommonLinkageAsString() : "";
}

// Set<>AsString()
public void SetCommonLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CommonLinkage == null)
    {
        _CommonLinkage = new CommonLinkage();
    }
    _CommonLinkage.SetCommonLinkageAsString(value);
}

// Standard Getter
public CgtabortLinkage GetCgtabortLinkage()
{
    return _CgtabortLinkage;
}

// Standard Setter
public void SetCgtabortLinkage(CgtabortLinkage value)
{
    _CgtabortLinkage = value;
}

// Get<>AsString()
public string GetCgtabortLinkageAsString()
{
    return _CgtabortLinkage != null ? _CgtabortLinkage.GetCgtabortLinkageAsString() : "";
}

// Set<>AsString()
public void SetCgtabortLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtabortLinkage == null)
    {
        _CgtabortLinkage = new CgtabortLinkage();
    }
    _CgtabortLinkage.SetCgtabortLinkageAsString(value);
}



}}
