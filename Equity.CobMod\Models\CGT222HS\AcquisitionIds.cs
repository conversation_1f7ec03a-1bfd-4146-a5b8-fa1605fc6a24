using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// DTO class representing AcquisitionIds Data Structure

public class AcquisitionIds
{
    private static int _size = 81;
    // [DEBUG] Class: AcquisitionIds, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: MadaAcquisitionId, is_external=, is_static_class=False, static_prefix=
    private string _MadaAcquisitionId ="";
    
    
    
    
    // [DEBUG] Field: MadaHoldingId, is_external=, is_static_class=False, static_prefix=
    private string _MadaHoldingId ="";
    
    
    
    
    // [DEBUG] Field: MadaParentStockId, is_external=, is_static_class=False, static_prefix=
    private string _MadaParentStockId ="";
    
    
    
    
    // [DEBUG] Field: MadaTransactionCategoryId, is_external=, is_static_class=False, static_prefix=
    private string _MadaTransactionCategoryId ="";
    
    
    
    
    // [DEBUG] Field: MadaCtLinkFundId, is_external=, is_static_class=False, static_prefix=
    private string _MadaCtLinkFundId ="";
    
    
    
    
    // [DEBUG] Field: MadaPrevDispId, is_external=, is_static_class=False, static_prefix=
    private string _MadaPrevDispId ="";
    
    
    
    
    // [DEBUG] Field: MadaPrevAcqnId, is_external=, is_static_class=False, static_prefix=
    private string _MadaPrevAcqnId ="";
    
    
    
    
    // [DEBUG] Field: MadaDbTimestamp, is_external=, is_static_class=False, static_prefix=
    private string _MadaDbTimestamp ="";
    
    
    
    
    // [DEBUG] Field: Filler110, is_external=, is_static_class=False, static_prefix=
    private string _Filler110 ="";
    
    
    
    
    
    // Serialization methods
    public string GetAcquisitionIdsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_MadaAcquisitionId.PadRight(8));
        result.Append(_MadaHoldingId.PadRight(8));
        result.Append(_MadaParentStockId.PadRight(8));
        result.Append(_MadaTransactionCategoryId.PadRight(8));
        result.Append(_MadaCtLinkFundId.PadRight(8));
        result.Append(_MadaPrevDispId.PadRight(8));
        result.Append(_MadaPrevAcqnId.PadRight(8));
        result.Append(_MadaDbTimestamp.PadRight(24));
        result.Append(_Filler110.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetAcquisitionIdsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMadaAcquisitionId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMadaHoldingId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMadaParentStockId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMadaTransactionCategoryId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMadaCtLinkFundId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMadaPrevDispId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMadaPrevAcqnId(extracted);
        }
        offset += 8;
        if (offset + 24 <= data.Length)
        {
            string extracted = data.Substring(offset, 24).Trim();
            SetMadaDbTimestamp(extracted);
        }
        offset += 24;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller110(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetAcquisitionIdsAsString();
    }
    // Set<>String Override function
    public void SetAcquisitionIds(string value)
    {
        SetAcquisitionIdsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetMadaAcquisitionId()
    {
        return _MadaAcquisitionId;
    }
    
    // Standard Setter
    public void SetMadaAcquisitionId(string value)
    {
        _MadaAcquisitionId = value;
    }
    
    // Get<>AsString()
    public string GetMadaAcquisitionIdAsString()
    {
        return _MadaAcquisitionId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMadaAcquisitionIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MadaAcquisitionId = value;
    }
    
    // Standard Getter
    public string GetMadaHoldingId()
    {
        return _MadaHoldingId;
    }
    
    // Standard Setter
    public void SetMadaHoldingId(string value)
    {
        _MadaHoldingId = value;
    }
    
    // Get<>AsString()
    public string GetMadaHoldingIdAsString()
    {
        return _MadaHoldingId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMadaHoldingIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MadaHoldingId = value;
    }
    
    // Standard Getter
    public string GetMadaParentStockId()
    {
        return _MadaParentStockId;
    }
    
    // Standard Setter
    public void SetMadaParentStockId(string value)
    {
        _MadaParentStockId = value;
    }
    
    // Get<>AsString()
    public string GetMadaParentStockIdAsString()
    {
        return _MadaParentStockId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMadaParentStockIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MadaParentStockId = value;
    }
    
    // Standard Getter
    public string GetMadaTransactionCategoryId()
    {
        return _MadaTransactionCategoryId;
    }
    
    // Standard Setter
    public void SetMadaTransactionCategoryId(string value)
    {
        _MadaTransactionCategoryId = value;
    }
    
    // Get<>AsString()
    public string GetMadaTransactionCategoryIdAsString()
    {
        return _MadaTransactionCategoryId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMadaTransactionCategoryIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MadaTransactionCategoryId = value;
    }
    
    // Standard Getter
    public string GetMadaCtLinkFundId()
    {
        return _MadaCtLinkFundId;
    }
    
    // Standard Setter
    public void SetMadaCtLinkFundId(string value)
    {
        _MadaCtLinkFundId = value;
    }
    
    // Get<>AsString()
    public string GetMadaCtLinkFundIdAsString()
    {
        return _MadaCtLinkFundId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMadaCtLinkFundIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MadaCtLinkFundId = value;
    }
    
    // Standard Getter
    public string GetMadaPrevDispId()
    {
        return _MadaPrevDispId;
    }
    
    // Standard Setter
    public void SetMadaPrevDispId(string value)
    {
        _MadaPrevDispId = value;
    }
    
    // Get<>AsString()
    public string GetMadaPrevDispIdAsString()
    {
        return _MadaPrevDispId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMadaPrevDispIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MadaPrevDispId = value;
    }
    
    // Standard Getter
    public string GetMadaPrevAcqnId()
    {
        return _MadaPrevAcqnId;
    }
    
    // Standard Setter
    public void SetMadaPrevAcqnId(string value)
    {
        _MadaPrevAcqnId = value;
    }
    
    // Get<>AsString()
    public string GetMadaPrevAcqnIdAsString()
    {
        return _MadaPrevAcqnId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMadaPrevAcqnIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MadaPrevAcqnId = value;
    }
    
    // Standard Getter
    public string GetMadaDbTimestamp()
    {
        return _MadaDbTimestamp;
    }
    
    // Standard Setter
    public void SetMadaDbTimestamp(string value)
    {
        _MadaDbTimestamp = value;
    }
    
    // Get<>AsString()
    public string GetMadaDbTimestampAsString()
    {
        return _MadaDbTimestamp.PadRight(24);
    }
    
    // Set<>AsString()
    public void SetMadaDbTimestampAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MadaDbTimestamp = value;
    }
    
    // Standard Getter
    public string GetFiller110()
    {
        return _Filler110;
    }
    
    // Standard Setter
    public void SetFiller110(string value)
    {
        _Filler110 = value;
    }
    
    // Get<>AsString()
    public string GetFiller110AsString()
    {
        return _Filler110.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller110AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler110 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}