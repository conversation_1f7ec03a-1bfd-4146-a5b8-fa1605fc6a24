using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D69File Data Structure

public class D69File
{
    private static int _size = 12;
    // [DEBUG] Class: D69File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler221, is_external=, is_static_class=False, static_prefix=
    private string _Filler221 ="$";
    
    
    
    
    // [DEBUG] Field: D69UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D69UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler222, is_external=, is_static_class=False, static_prefix=
    private string _Filler222 ="FND.REP";
    
    
    
    
    
    // Serialization methods
    public string GetD69FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler221.PadRight(1));
        result.Append(_D69UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler222.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD69FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller221(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD69UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller222(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD69FileAsString();
    }
    // Set<>String Override function
    public void SetD69File(string value)
    {
        SetD69FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller221()
    {
        return _Filler221;
    }
    
    // Standard Setter
    public void SetFiller221(string value)
    {
        _Filler221 = value;
    }
    
    // Get<>AsString()
    public string GetFiller221AsString()
    {
        return _Filler221.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller221AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler221 = value;
    }
    
    // Standard Getter
    public int GetD69UserNo()
    {
        return _D69UserNo;
    }
    
    // Standard Setter
    public void SetD69UserNo(int value)
    {
        _D69UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD69UserNoAsString()
    {
        return _D69UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD69UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D69UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller222()
    {
        return _Filler222;
    }
    
    // Standard Setter
    public void SetFiller222(string value)
    {
        _Filler222 = value;
    }
    
    // Get<>AsString()
    public string GetFiller222AsString()
    {
        return _Filler222.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller222AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler222 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}