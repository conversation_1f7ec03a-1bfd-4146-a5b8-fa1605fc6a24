﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
//using Equity.CobMod.Models;
using WK.UK.CCH.Equity.CobolDataTransformation;
using Legacy4.Equity.CobMod.Models;
using Equity.CobMod.Models;
//using WKTEST.Model; // Assuming all provided model classes are in this namespace

namespace Equity.CobMod.FIleHandlers
{
    public class MasterComputationalDal
    {
        // --- State Variables (Equivalent to Working-Storage) ---
        private int _userNumber;
        private ComputationDA _masterComputationDA; // Instance of the C# DAO layer
        private UserInfoDA _userInfoDA;
        private UserFundsDA _userFundsDA;
        private bool _firstTimeReadUserFund = true; // Equivalent to bFirstTime for user funds
        private string _masterStoreFundCode = string.Empty; // To hold fund code across calls
        private int? _currentIlGCondition = null; // To hold ILG condition between reads

        // Placeholder for encoding - adjust if EBCDIC is needed
        private static readonly Encoding CobolEncoding = Encoding.ASCII;

        // --- Main Entry Point (Equivalent to PROCEDURE DIVISION USING) ---

        /// <summary>
        /// Performs file actions similar to the COBOL MasterComputationDAL program.
        /// </summary>
        /// <param name="fileAction">The action to perform (e.g., CobolConstants.OPEN_INPUT).</param>
        /// <param name="fileRecordArea">Input/Output buffer corresponding to L-FILE-RECORD-AREA. Size should be sufficient (e.g., CobolConstants.FILE_RECORD_AREA_SIZE).</param>
        /// <param name="userNumber">User number.</param>
        /// <param name="linkageIds">Input/Output structure for record IDs.</param>
        /// <param name="priceTypeStatus">Output price type status.</param>
        /// <param name="linkFundSedol">Input containing fund/sedol for specific modes.</param>
        /// <param name="year">Input year for specific modes.</param>
        /// <param name="returnCode">Output return code ('00' for success).</param>
        /// <param name="forceWrite">Input flag for forcing write operations.</param>
        public void PerformAction(
            FileAction fileAction,
            ref byte[] fileRecordArea, // Using byte[] to represent raw COBOL record area
            int userNumber,
            ref LinkageIDs linkageIds,
            ref string priceTypeStatus,
            string linkFundSedol, // Assuming format like "FUNDSEDOL" or similar if needed for OPEN
            int year,
            ref string returnCode,
            bool forceWrite)
        {
            _userNumber = userNumber; // Store user number for potential use in helpers
            returnCode = "00"; // Default success

            try
            {
                switch (fileAction.FileActionCode)
                {
                    case CobolConstants.OPEN_INPUT:
                    case CobolConstants.OPEN_I_O:
                        HandleOpen(fileAction, linkFundSedol, year, userNumber);
                        // Clear the record area buffer after open
                        InitializeByteArray(ref fileRecordArea, CobolConstants.FILE_RECORD_AREA_SIZE);
                        break;

                    case CobolConstants.START_NOT_LESS_THAN:
                        HandleStartNotLessThan(ref fileRecordArea);
                        break;

                    case CobolConstants.READ_NEXT:
                        HandleReadNext(ref fileRecordArea, ref linkageIds, ref priceTypeStatus);
                        break;

                    case CobolConstants.WRITE_RECORD:
                        HandleWriteRecord(fileRecordArea, ref linkageIds, ref returnCode, forceWrite);
                        break;

                    case CobolConstants.REWRITE_RECORD:
                        HandleRewriteRecord(fileRecordArea, ref linkageIds, ref returnCode);
                        break;

                    case CobolConstants.DELETE_RECORD:
                        HandleDeleteRecord(fileRecordArea, linkageIds, ref returnCode);
                        break;

                    case CobolConstants.CLOSE_FILE:
                        HandleClose();
                        break;

                    case CobolConstants.GET_CALENDAR_DATES:
                        HandleGetCalendarDates(ref fileRecordArea);
                        break;

                    case CobolConstants.READ_NEXT_USER_FUND_RECORD:
                        HandleReadNextUserFundRecord(ref fileRecordArea);
                        break;

                    case CobolConstants.READ_USER_FUND_RECORD:
                        HandleReadUserFundRecord(ref fileRecordArea);
                        break;

                    case CobolConstants.GET_CONFIG_VALUE:
                        HandleGetConfigValue(ref fileRecordArea);
                        break;

                    case CobolConstants.GET_REQUEST_OPTIONS:
                        HandleGetRequestOptions(ref fileRecordArea);
                        break;

                    case CobolConstants.REMAP_C_F_TRANSACTIONS:
                        HandleRemapCFTransactions(year);
                        break;

                    default:
                        returnCode = "99"; // Indicate unsupported action
                                           // Optionally log an error
                        break;
                }
            }
            catch (Exception ex)
            {
                // Basic error handling
                returnCode = "98"; // Indicate general error
                                   // Log the exception ex
                InitializeByteArray(ref fileRecordArea, CobolConstants.FILE_RECORD_AREA_SIZE); // Clear buffer on error
            }
        }

        // --- Helper Methods for Actions ---

        private void HandleOpen(FileAction fileAction, string linkFundSedol, int year, int userNumber)
        {
            // Determine the mode based on how the COBOL calls the constructor
            // This logic might need refinement based on how linkFundSedol/year indicate the mode.
            // Assuming constructor overloads or a mode parameter in the C# DAO.

            // Example: Inferring mode (adjust as needed)
            switch (fileAction.FileActionKey)
            {
                case FileAction.FULL_COMP:
                    _masterComputationDA = new ComputationDA(userNumber, true);
                    break;
                case FileAction.PARTIAL_COMP:
                    _masterComputationDA = new ComputationDA(userNumber, false);
                    break;
                //  case FileAction.SEDOL_WHATIF_COMP:
                case FileAction.SINGLE_SEDOL_COMP:
                    if (!string.IsNullOrEmpty(linkFundSedol) && linkFundSedol.Length >= 11) // Assuming FUND(4) + SEDOL(7)
                    {
                        // SEDOL-WHATIF-COMP or SINGLE-SEDOL-COMP
                        string fundCode = linkFundSedol.Substring(0, 4).Trim();
                        string sedolCode = linkFundSedol.Substring(4).Trim();
                        _masterComputationDA = new ComputationDA(userNumber, fundCode, sedolCode);
                    }
                    break;
                case FileAction.YEAR_END_COMP:
                    // YEAR-END-COMP
                    int fullYear = year;
                    if (year <= CobolConstants.LAST_MASTER_YEAR_YY) // Handle YY format
                    {
                        fullYear = (year >= (CobolConstants.FIRST_MASTER_YEAR_YY)) ? 1900 + year : 2000 + year;
                    }
                    _masterComputationDA = new ComputationDA(userNumber, fullYear);
                    break;
            }
            /*if (!string.IsNullOrEmpty(linkFundSedol) && linkFundSedol.Length >= 11) // Assuming FUND(4) + SEDOL(7)
            {
                // SEDOL-WHATIF-COMP or SINGLE-SEDOL-COMP
                string fundCode = linkFundSedol.Substring(0, 4).Trim();
                string sedolCode = linkFundSedol.Substring(4).Trim();
                _masterComputationDA = new ComputationDA(userNumber, fundCode, sedolCode);
            }*/
            /*else if (year > 0)
            {
                // YEAR-END-COMP
                int fullYear = year;
                if (year <= CobolConstants.LAST_MASTER_YEAR_YY) // Handle YY format
                {
                    fullYear = (year >= (CobolConstants.FIRST_MASTER_YEAR_YY)) ? 1900 + year : 2000 + year;
                }
                _masterComputationDA = new ComputationDA(userNumber, fullYear);
            }
            else
            {
                // FULL-COMP or PARTIAL-COMP (Distinguish based on a flag if needed)
                // Assuming FULL-COMP is default or indicated elsewhere
                bool isFullComputation = true; // Or determine this based on fileAction or another param
                _masterComputationDA = new ComputationDA(userNumber, isFullComputation);
            }*/

            // Instantiate other DAOs
           
            _userFundsDA = _masterComputationDA.UserFunds; // Assuming UserFunds is a property
            _userInfoDA = new UserInfoDA(); // Assuming separate instantiation
            _firstTimeReadUserFund = true;
            _masterStoreFundCode = string.Empty;
            _currentIlGCondition = null;

            // COBOL initializes DAO records to spaces - C# objects are initialized by constructors
        }
        
        private void HandleStartNotLessThan(ref byte[] fileRecordArea)
        {
            bool workPackageFound = _masterComputationDA.SetPointerToNextWorkPackage();
            string message = workPackageFound ? "next work package" : string.Empty;
            SetRecordAreaString(ref fileRecordArea, message);
        }

        private void HandleReadNext(ref byte[] fileRecordArea, ref LinkageIDs linkageIds, ref string priceTypeStatus)
        {
            string sRecordArea = _masterComputationDA.ReadNext(); // Get string representation from DAO

            if (string.IsNullOrEmpty(sRecordArea))
            {
                InitializeByteArray(ref fileRecordArea, CobolConstants.FILE_RECORD_AREA_SIZE); // End of data
               // returnCode = "10"; // Simulate EOF
                return;
            }

            char recordType = sRecordArea[0]; // First char indicates type (H, B, A, D)
            D13RecordHolder d13Holder = null;

            switch (recordType)
            {
                case 'H':
                    var holdingDA = MasterHoldingDARecord.MapFromString(sRecordArea);
                    d13Holder = SetupHeaderCobolRecord(holdingDA, ref linkageIds, ref priceTypeStatus);
                    _currentIlGCondition = holdingDA.MHDA_ILGCondition; // Store condition
                    break;
                case 'B':
                    var balanceDA = MasterBalanceDARecord.MapFromString(sRecordArea);
                    d13Holder = SetupBalanceCobolRecord(balanceDA, ref linkageIds);
                    // Restore condition from last header read
                    if (_currentIlGCondition.HasValue) linkageIds.MHDA_ILGCondition = _currentIlGCondition.Value;
                    break;
                case 'A':
                    var acquisitionDA = MasterAcquisitionDARecord.MapFromString(sRecordArea);
                    d13Holder = SetupAcquisitionCobolRecord(acquisitionDA, ref linkageIds);
                    // Restore condition
                    if (_currentIlGCondition.HasValue) linkageIds.MHDA_ILGCondition = _currentIlGCondition.Value;
                    break;
                case 'D':
                    var disposalDA = MasterDisposalDARecord.MapFromString(sRecordArea);
                    d13Holder = SetupDisposalCobolRecord(disposalDA, ref linkageIds);
                    // Restore condition
                    if (_currentIlGCondition.HasValue) linkageIds.MHDA_ILGCondition = _currentIlGCondition.Value;
                    break;
                default:
                    throw new InvalidOperationException($"Unknown record type '{recordType}' received from DAO.");
            }

            // Serialize the populated D13Holder back to the COBOL byte format
            SerializeD13HolderToCobolBytes(d13Holder, recordType.ToString(), ref fileRecordArea);
        }

        private void HandleWriteRecord(byte[] fileRecordArea, ref LinkageIDs linkageIds, ref string returnCode, bool forceWrite)
        {
            // Deserialize the input COBOL bytes into a D13RecordHolder
            D13RecordHolder d13Holder = DeserializeCobolBytesToD13Holder(fileRecordArea);
            string recordCode = d13Holder.D13_1_RECORD_CODE; // Assuming header code is always present or derivable

            string sRecordArea = null; // String representation for C# DAO
            string daoReturnCode = "00";

            switch (recordCode)
            {
                case CobolConstants.REC_CODE_HOLDING:
                    MasterHoldingDARecord holdingDA = SetupHeaderCsRecord(d13Holder, linkageIds);
                    sRecordArea = SerializeMasterRecordToString(holdingDA); // Needs implementation
                    int holdingId = _masterComputationDA.CreateNewHolding(sRecordArea);
                    // Update LinkageIDs with the new ID
                    linkageIds = LinkageIDs.Create(); // Clear existing IDs
                    linkageIds.MHDA_HoldingID = holdingId.ToString().PadLeft(8); // Assuming ID fits in 8 chars
                    break;

                case CobolConstants.REC_CODE_BALANCE_ACQUISITION:
                    // Need to distinguish Balance (02) from Acquisition (02) based on content/flags
                    // The COBOL checks L-FILE-BALANCE flag, which isn't directly available here.
                    // We might infer based on fields present in d13Holder or need another indicator.
                    // Assumption: Infer based on presence of Balance-specific fields like MBDA_totalUnitsPerTrancheBF
                    bool isBalance = d13Holder.D13_2_B_F_TRANCHE_TOTAL_UNITS.HasValue; // Example inference

                    if (isBalance)
                    {
                        MasterBalanceDARecord balanceDA = SetupBalanceCsRecord(d13Holder, linkageIds);
                        sRecordArea = SerializeMasterRecordToString(balanceDA); // Needs implementation
                        daoReturnCode = _masterComputationDA.CreateNewBalance(sRecordArea, forceWrite);
                    }
                    else // Is Acquisition
                    {
                        MasterAcquisitionDARecord acqDA = SetupAcquisitionCsRecord(d13Holder, linkageIds);
                        sRecordArea = SerializeMasterRecordToString(acqDA); // Needs implementation
                        daoReturnCode = _masterComputationDA.CreateNewAcquisition(sRecordArea);
                    }
                    returnCode = daoReturnCode;
                    break;

                case CobolConstants.REC_CODE_DISPOSAL:
                    MasterDisposalDARecord dispDA = SetupDisposalCsRecord(d13Holder, linkageIds);
                    sRecordArea = SerializeMasterRecordToString(dispDA); // Needs implementation
                    daoReturnCode = _masterComputationDA.CreateNewDisposal(sRecordArea);
                    returnCode = daoReturnCode;
                    break;

                default:
                    throw new InvalidOperationException($"Unknown record code '{recordCode}' for WRITE.");
            }
        }

        private void HandleRewriteRecord(byte[] fileRecordArea, ref LinkageIDs linkageIds, ref string returnCode)
        {
            // Deserialize the input COBOL bytes into a D13RecordHolder
            D13RecordHolder d13Holder = DeserializeCobolBytesToD13Holder(fileRecordArea);
            string recordCode = d13Holder.D13_1_RECORD_CODE; // Assuming header code is always present or derivable

            string sRecordArea = null; // String representation for C# DAO

            switch (recordCode)
            {
                case CobolConstants.REC_CODE_HOLDING:
                    MasterHoldingDARecord holdingDA = SetupHeaderCsRecord(d13Holder, linkageIds);
                    sRecordArea = SerializeMasterRecordToString(holdingDA); // Needs implementation
                    _masterComputationDA.UpdateHoldings(sRecordArea);
                    break;

                case CobolConstants.REC_CODE_BALANCE_ACQUISITION:
                    bool isBalance = d13Holder.D13_2_B_F_TRANCHE_TOTAL_UNITS.HasValue; // Example inference
                    if (isBalance)
                    {
                        MasterBalanceDARecord balanceDA = SetupBalanceCsRecord(d13Holder, linkageIds);
                        sRecordArea = SerializeMasterRecordToString(balanceDA); // Needs implementation
                        _masterComputationDA.UpdateBalance(sRecordArea);
                    }
                    else // Is Acquisition
                    {
                        MasterAcquisitionDARecord acqDA = SetupAcquisitionCsRecord(d13Holder, linkageIds);
                        sRecordArea = SerializeMasterRecordToString(acqDA); // Needs implementation
                        _masterComputationDA.UpdateAcquisitions(sRecordArea);
                    }
                    break;

                case CobolConstants.REC_CODE_DISPOSAL:
                    MasterDisposalDARecord dispDA = SetupDisposalCsRecord(d13Holder, linkageIds);
                    sRecordArea = SerializeMasterRecordToString(dispDA); // Needs implementation
                    _masterComputationDA.UpdateDisposals(sRecordArea);
                    break;

                default:
                    throw new InvalidOperationException($"Unknown record code '{recordCode}' for REWRITE.");
            }
        }

        private void HandleDeleteRecord(byte[] fileRecordArea, LinkageIDs linkageIds, ref string returnCode)
        {
            // We need the record code to know *which* ID to use from LinkageIDs.
            // Let's assume the first byte of fileRecordArea contains a hint or it's passed.
            // Or, deserialize just enough to get the code.
            // Simple approach: Try parsing the relevant ID if available.

            // Attempt to get record code from the buffer (assuming it's stored there)
            string recordCode = GetRecordCodeFromBuffer(fileRecordArea); // Placeholder function

            switch (recordCode)
            {
                case CobolConstants.REC_CODE_BALANCE_ACQUISITION:
                    // Assuming Balance deletion is intended here, as Acquisition delete isn't shown
                    if (!string.IsNullOrEmpty(linkageIds.MBDA_BalanceID))
                    {
                        if (int.TryParse(linkageIds.MBDA_BalanceID.Trim(), out int balanceId))
                        {
                            _masterComputationDA.DeleteBalance(balanceId);
                        }
                        else { returnCode = "91"; /* Invalid ID format */ }
                    }
                    else { returnCode = "92"; /* ID missing */ }
                    break;

                case CobolConstants.REC_CODE_DISPOSAL:
                    if (!string.IsNullOrEmpty(linkageIds.MDDA_DisposalID))
                    {
                        if (int.TryParse(linkageIds.MDDA_DisposalID.Trim(), out int disposalId))
                        {
                            _masterComputationDA.DeleteDisposal(disposalId);
                        }
                        else { returnCode = "91"; /* Invalid ID format */ }
                    }
                    else { returnCode = "92"; /* ID missing */ }
                    break;

                // Deleting Holding (01) or Acquisition (02) not shown in COBOL snippet for DELETE
                default:
                    returnCode = "90"; // Unsupported record type for delete
                    break;
            }
        }

        private void HandleClose()
        {
            _masterComputationDA?.Close(); // Assuming Close is static or instance method
            _masterComputationDA = null;
            _userInfoDA = null;
            _userFundsDA = null;
        }

        private void HandleGetCalendarDates(ref byte[] fileRecordArea)
        {
            string calendarCode = GetRecordAreaString(fileRecordArea).Trim();
            string result = _masterComputationDA.GetCalendarDates(calendarCode);
            SetRecordAreaString(ref fileRecordArea, result);
        }

        private void HandleReadNextUserFundRecord(ref byte[] fileRecordArea)
        {
            if (_firstTimeReadUserFund)
            {
                // Return header record derived from UserInfoDA
                GetUserInfoRecord(ref fileRecordArea);
                _firstTimeReadUserFund = false;
            }
            else
            {
                // Return next user fund record derived from UserFundsDA
                string sRecordArea = _userFundsDA.ReadNext();
                SetupUserFundCobolRecord(sRecordArea, ref fileRecordArea);
            }
        }

        private void HandleReadUserFundRecord(ref byte[] fileRecordArea)
        {
            string fundCode = GetRecordAreaString(fileRecordArea, 4).Trim(); // Get first 4 chars

            if (string.IsNullOrEmpty(fundCode)) // COBOL checks for SPACES
            {
                // Return header record derived from UserInfoDA
                GetUserInfoRecord(ref fileRecordArea);
            }
            // COBOL checks for LOW-VALUES/HIGH-VALUES - skip these checks unless necessary
            else
            {
                // Return specific user fund record
                string sRecordArea = _userFundsDA.ReadByFundCode(fundCode);
                SetupUserFundCobolRecord(sRecordArea, ref fileRecordArea);
            }
        }

        private void GetUserInfoRecord(ref byte[] fileRecordArea)
        {
            string sRecordArea = _userInfoDA.GetUserInfo(_userNumber);
            // Assuming sRecordArea contains the string representation of UserInfoRecord
            // We need to map this to D37HeaderRecord and serialize
            UserInfoRecord userInfo = ParseUserInfoString(sRecordArea); // Placeholder
            D37HeaderRecord d37Header = MapUserInfoToD37Header(userInfo); // Placeholder
            SerializeD37HeaderToCobolBytes(d37Header, ref fileRecordArea); // Placeholder
        }

        private void SetupUserFundCobolRecord(string sRecordArea, ref byte[] fileRecordArea)
        {
            if (string.IsNullOrEmpty(sRecordArea))
            {
                InitializeByteArray(ref fileRecordArea, CobolConstants.FILE_RECORD_AREA_SIZE);
                return;
            }
            // Assuming sRecordArea contains the string representation of UserFundRecord
            UserFundRecord userFund = ParseUserFundString(sRecordArea); // Placeholder
            D37DetailRecord d37Detail = MapUserFundToD37Detail(userFund); // Placeholder
            SerializeD37DetailToCobolBytes(d37Detail, ref fileRecordArea); // Placeholder
        }


        private void HandleGetConfigValue(ref byte[] fileRecordArea)
        {
            string configName = GetRecordAreaString(fileRecordArea).Trim();
            string result = _masterComputationDA.GetConfigValue(configName);
            SetRecordAreaString(ref fileRecordArea, result);
        }

        private void HandleGetRequestOptions(ref byte[] fileRecordArea)
        {
            string optionName = GetRecordAreaString(fileRecordArea).Trim();
            string result = _masterComputationDA.GetRequestOptions(optionName);
            SetRecordAreaString(ref fileRecordArea, result);
        }

        private void HandleRemapCFTransactions(int year)
        {
            int previousYear = year - 1; // Assuming year is the current processing year
            _masterComputationDA.LinkCarriedForwardTransactions(previousYear);
        }

        // --- Data Mapping Helpers (COBOL Sections A1-A8) ---

        // A1: Setup D13RecordHolder (COBOL format) from MasterHoldingDARecord (C# DAO)
        private D13RecordHolder SetupHeaderCobolRecord(MasterHoldingDARecord daRecord, ref LinkageIDs linkageIds, ref string priceTypeStatus)
        {
            var holder = InitializeHeaderHolder(); // Start with defaults

            // Map from daRecord to holder
            holder.D13_1_CO_AC_LK = daRecord.MHDA_FundCode;
            _masterStoreFundCode = daRecord.MHDA_FundCode; // Store for subsequent records
            holder.D13_1_SEDOL = daRecord.MHDA_SedolCode;
            // holder.D13_1_CONTRACT_NO = ???; // Not directly available in MasterHoldingDARecord
            holder.D13_1_RECORD_CODE = CobolConstants.REC_CODE_HOLDING;
            holder.D13_1_DATE_TIME_STAMP = daRecord.MHDA_DateTimeStamp;
            holder.D13_1_CALC_FLAG = daRecord.MHDA_HoldingStatusCode;
            holder.D13_1_R_CALC_PERIOD_END_DATE = daRecord.MHDA_RealisedCalculationDate;
            holder.D13_1_U_CALC_PERIOD_END_DATE = daRecord.MHDA_UnrealisedCalculationDate;
            holder.D13_1_SECURITY_SORT_CODE = daRecord.MHDA_SortCode;
            holder.D13_1_COUNTRY_CODE = daRecord.MHDA_CountryCode;
            holder.D13_1_GROUP_CODE = daRecord.MHDA_GroupCode;
            // holder.D13_1_INDUSTRIAL_CLASS = ???; // Not in MasterHoldingDARecord
            holder.D13_1_SECURITY_TYPE = daRecord.MHDA_SecurityType;
            holder.D13_1_CURRENT_MARKET_PRICE = daRecord.MHDA_MarketPrice;
            holder.D13_1_PRICE_PCT_INDICATOR = daRecord.MHDA_PricePercentIndicator;
            holder.D13_1_B_F_ACCOUNTING_VALUE = daRecord.MHDA_BookValueBF;
            holder.D13_1_ACCOUNTING_VALUE_YTD = daRecord.MHDA_BookValueYTD;
            holder.D13_1_PROFIT_LOSS = daRecord.MHDA_ProfitLoss;
            holder.D13_1_ISSUERS_NAME = daRecord.MHDA_IssuersName;
            holder.D13_1_STOCK_DESCRIPTION = daRecord.MHDA_StockDescription;
            holder.D13_1_ISSUED_CAPITAL = daRecord.MHDA_IssuedCapital;
            holder.D13_1_MOVEMENT_INDICATOR = ParseIntNullable(daRecord.MHDA_Sedolmovement);
            holder.D13_1_B_F_2PCT_HOLDING_DATE_R = daRecord.MHDA_DateOf2PercentHoldingBF;
            holder.D13_1_2PCT_HOLDING_DATE_YTD_R = daRecord.MHDA_DateOf2PercentHoldingYTD;
            holder.D13_1_SECURITY_INDICATOR = daRecord.MHDA_SecurityInd;
            holder.D13_1_TOTAL_UNITS_YTD = daRecord.MHDA_totalUnits;
            holder.D13_1_CAPITAL_GAIN_LOSS = daRecord.MHDA_CapitalGainLoss;
            holder.D13_1_UNREAL_GAIN_LOSS = daRecord.MHDA_UnrealisedGain;
            holder.D13_1_UNREAL_PROFIT_LOSS = daRecord.MHDA_UnrealisedProfit;
            holder.D13_1_OFFSHORE_CLASS = daRecord.MHDA_Offshore_Class;
            holder.D13_1_OVER_RIDE_FLAG = daRecord.MHDA_OverrideYearEnd;
            holder.D13_1_DATE_OF_ISSUE_R = daRecord.MHDA_DateOfIssue;
            holder.D13_1_INDEX_FROM_ISSUE = daRecord.MHDA_IndexFromIssue;
            holder.D13_1_DEEMED_GAIN_LOSS = daRecord.MHDA_DeemedGainLoss;
            holder.D13_1_DEEMED_PROFIT_LOSS = daRecord.MHDA_DeemedProfitLoss;
            holder.D13_1_HOLDING_FLAG = daRecord.MHDA_HoldingFlag;
            holder.D13_1_BOND_OVERRIDE = daRecord.MHDA_BondOverrideCode;
            holder.D13_1_LR_BASIS = daRecord.MHDA_LRBasis;
            holder.D13_1_BOND_MATURITY_DATE_R = daRecord.MHDA_BondMaturityDate;
            holder.D13_1_BOND_PAR_VALUE = daRecord.MHDA_BondParValue;
            holder.D13_1_BOND_GAIN_OVERRIDE = daRecord.MHDA_BondGainOverride;
            holder.D13_1_UNREAL_BOND_GAIN_LOSS = daRecord.MHDA_UnrealisedBondGainLoss;
            holder.D13_1_UNREAL_BOND_PROFIT_LOSS = daRecord.MHDA_UnrealisedBondProfitLoss;
            holder.D13_1_ASSET_USAGE = daRecord.MHDA_AssetUsageOverrideCode; // Mapping based on COBOL line 2058
            holder.D13_1_REAL_BOND_GAIN_LOSS = daRecord.MHDA_RealisedBondGainLoss;
            holder.D13_1_REAL_BOND_PROFIT_LOSS = daRecord.MHDA_RealisedBondProfitLoss;

            // Update output parameters
            priceTypeStatus = daRecord.MHDA_MarketPriceStatus;

            // Update LinkageIDs (Header specific part)
            linkageIds = LinkageIDs.Create(); // Clear previous
            linkageIds.MHDA_HoldingID = daRecord.MHDA_HoldingID;
            linkageIds.MHDA_FundID = daRecord.MHDA_FundID;
            linkageIds.MHDA_StockID = daRecord.MHDA_StockID;
            linkageIds.MHDA_BondOverrideID = daRecord.MHDA_BondOverrideID;
            linkageIds.MHDA_AssetUsageOverrideID = daRecord.MHDA_AssetUsageOverrideID;
            linkageIds.MHDA_DB_Timestamp = daRecord.MHDA_DB_Timestamp;
            if (daRecord.MHDA_ILGCondition.HasValue)
            {
                linkageIds.MHDA_ILGCondition = daRecord.MHDA_ILGCondition.Value;
            }

            return holder;
        }

        // A2: Setup D13RecordHolder (COBOL format) from MasterBalanceDARecord (C# DAO)
        private D13RecordHolder SetupBalanceCobolRecord(MasterBalanceDARecord daRecord, ref LinkageIDs linkageIds)
        {
            var holder = InitializeBalanceAcquisitionHolder(); // Start with defaults

            // Map common fields
            holder.D13_1_CO_AC_LK = _masterStoreFundCode; // Use stored fund code
            holder.D13_1_SEDOL = daRecord.MBDA_SedolCode;
            holder.D13_1_CONTRACT_NO = daRecord.MBDA_ContractNumber;
            holder.D13_1_RECORD_CODE = CobolConstants.REC_CODE_BALANCE_ACQUISITION;
            holder.D13_2_TRANSACTION_CATEGORY = daRecord.MBDA_BalanceTypeCode;
            holder.D13_2_DATE_TIME_STAMP = daRecord.MBDA_DateTimeStamp;
            if (!string.IsNullOrEmpty(daRecord.MBDA_ParentSedolCode))
            {
                holder.D13_2_PARENT_CAL = _masterStoreFundCode; // Assuming parent is in same fund
                holder.D13_2_PARENT_SEDOL_CODE = daRecord.MBDA_ParentSedolCode;
            }
            holder.D13_2_BARGAIN_DATE = daRecord.MBDA_BargainDate;
            holder.D13_2_NOTES_COMMENTS = daRecord.MBDA_NotesComments;
            holder.D13_2_STOCK_EXCH_INDICATOR = daRecord.MBDA_StockExchangeIndicator;
            holder.D13_2_PARENT_MARKET_PRICE = daRecord.MBDA_ParentPrice;
            holder.D13_2_LIABILITY_PER_SHARE = daRecord.MBDA_TotalLiabilityPrice;
            holder.D13_2_OUTSTANDING_LIABILITY = daRecord.MBDA_OutstandingLiabilityPrice;
            holder.D13_2_B_F_NI_PI_FLAG = daRecord.MBDA_NewIssuePrivFlagBF;
            holder.D13_2_NI_PI_FLAG_YTD = daRecord.MBDA_NewIssuePrivFlagYTD;
            // holder.D13_2_TRANSACTION_EXPORTED = ???; // Not in MasterBalanceDARecord
            // holder.D13_2_CT_LINK_FUND = ???;
            // holder.D13_2_SUB_FUND = ???;
            // holder.D13_2_TRANSACTION_OVERRIDE = ???;
            // holder.D13_2_TRANS_UNIT_PRICE = ???; // Not directly on balance
            // holder.D13_2_PRICE_PCT_INDICATOR = ???; // Usually 'S' for balance?

            // Map Balance specific fields
            holder.D13_2_B_F_TRANCHE_TOTAL_UNITS = daRecord.MBDA_totalUnitsPerTrancheBF;
            holder.D13_2_TRANCHE_TOTAL_UNITS_YTD = daRecord.MBDA_totalUnitsPerTrancheYTD;
            holder.D13_2_B_F_DISP_UNITS_REAC = daRecord.MBDA_DisposalUnitsReacquiredBF;
            holder.D13_2_DISP_UNITS_REAC_YTD = daRecord.MBDA_DisposalUnitsReacquiredYTD;
            holder.D13_2_BOOK_COST = daRecord.MBDA_BookCost;
            // holder.D13_2_ISSUE_SAME_CLASS = ???; // Default 'Y' in COBOL init
            holder.D13_2_B_F_DATE_PREV_OP_EVENT_R = daRecord.MBDA_PrevOpEventDateBF;
            holder.D13_2_DATE_PREV_OP_EVENT_YTD = daRecord.MBDA_PrevOpEventDateYTD;
            holder.D13_2_FIRST_DAY_DEALING_PRICE = daRecord.MBDA_FDDPrice;
            holder.D13_2_B_F_INDEXED_COST_BALANCE = daRecord.MBDA_IndexedCostBalanceBF;
            holder.D13_2_INDEXED_COST_BALANCE_YTD = daRecord.MBDA_IndexedCostBalanceYTD;
            holder.D13_2_UNINDEXABLE_FLAG = daRecord.MBDA_UnindexableHoldingsFlag;
            // holder.D13_2_GROUP_TRANSFER_FLAG = ???; // Default '0' in COBOL init
            holder.D13_2_B_F_LINK_RECORD_INDIC = ParseIntNullable(daRecord.MBDA_LinkedRecordIndicatorBF);
            holder.D13_2_LINK_RECORD_INDIC_YTD = ParseIntNullable(daRecord.MBDA_LinkedRecordIndicatorYTD);
            holder.D13_2_B_F_INDEX85_COST_BALANCE = daRecord.MBDA_IndexedPoolBF;
            holder.D13_2_INDEX85_COST_BALANCE_YTD = daRecord.MBDA_IndexedPoolYTD;
            holder.D13_2_TRANCHE_FLAG = daRecord.MBDA_TrancheFlag;
            holder.D13_2_RESET_TAPER_DATE = daRecord.MBDA_ResetTaperDateFlag;
            holder.D13_2_NO_OF_INITIAL_BOOK_COSTS = daRecord.MBDA_NumberofInitialBookCosts;
            holder.D13_2_B_F_NO_OF_COSTS_HELD = daRecord.MBDA_NumberofCostsHeldBF;
            holder.D13_2_NO_OF_COSTS_HELD_YTD = daRecord.MBDA_NumberofCostsHeldYTD;
            // holder.D13_2_CapitalGainLoss_AcqBal = ???; // Not on MasterBalanceDARecord
            // holder.D13_2_ProfitLoss_AcqBal = ???;
            // holder.D13_2_ProceedsOfDisposal_AcqBal = ???;

            // Map Cost Table
            holder.D13_2_COST_TABLE = new List<D13CostElement>();
            int costsHeldBF = daRecord.MBDA_NumberofCostsHeldBF ?? 0;
            int costsHeldYTD = daRecord.MBDA_NumberofCostsHeldYTD ?? 0;
            int maxCosts = Math.Max(costsHeldBF, costsHeldYTD);
            bool has82Value = (daRecord.MBDA_BudgetDayValue82BF ?? 0) > 0 || (daRecord.MBDA_BudgetDayValue82YTD ?? 0) > 0;

            // Add cost element 1 (from base fields)
            var cost1 = new D13CostElement
            {
                D13_2_B_F_BALANCE_UNITS = daRecord.MBDA_BaseUnitsBF,
                D13_2_BALANCE_UNITS_YTD = daRecord.MBDA_BaseUnitsYTD,
                D13_2_UNITS_PRESENT = "1", // Indicates presence
                D13_2_B_F_UNIND_COST_BALANCE = daRecord.MBDA_BaseCostBF,
                D13_2_UNIND_COST_BALANCE_YTD = daRecord.MBDA_BaseCostYTD,
                D13_2_B_F_BUDGET_DAY_VALUE = daRecord.MBDA_BudgetDayValue65BF,
                D13_2_BUDGET_DAY_VALUE_YTD = daRecord.MBDA_BudgetDayValue65YTD,
                D13_2_INDEX_DATE = daRecord.MBDA_BaseIndexDate,
                D13_2_COST_DATE = daRecord.MBDA_BargainDate, // Use bargain date as cost date for base
                D13_2_NI_NP_PI_COSTS = "?", // TODO: Determine this flag
                D13_2_TAPER_DATE = daRecord.MBDA_TaperDate,
                D13_2_B_F_TAPER_UNITS = daRecord.MBDA_TaperUnitsBF,
                D13_2_TAPER_UNITS_YTD = daRecord.MBDA_TaperUnitsYTD
            };
            holder.D13_2_COST_TABLE.Add(cost1);

            // Add subsequent cost elements from the nested list
            int daoCostIndex = 0;
            for (int i = 1; i < maxCosts; i++) // Start from 1 because base fields form the first element
            {
                if (has82Value && i == (maxCosts - 1)) continue; // Skip the slot for 82 value if present

                if (daoCostIndex < daRecord.Master_Balance_Cost_DA_Record.Count)
                {
                    var daoCost = daRecord.Master_Balance_Cost_DA_Record[daoCostIndex];
                    var costN = new D13CostElement
                    {
                        D13_2_B_F_BALANCE_UNITS = daoCost.MCDA_BalanceUnitsBF,
                        D13_2_BALANCE_UNITS_YTD = daoCost.MCDA_BalanceUnitsYTD,
                        D13_2_UNITS_PRESENT = daoCost.MCDA_CostTypeCode,
                        D13_2_B_F_UNIND_COST_BALANCE = daoCost.MCDA_UnindexedCostBalanceBF,
                        D13_2_UNIND_COST_BALANCE_YTD = daoCost.MCDA_UnindexedCostBalanceYTD,
                        D13_2_INDEX_DATE = daoCost.MCDA_IndexDate,
                        D13_2_COST_DATE = daoCost.MCDA_CostDate,
                        D13_2_NI_NP_PI_COSTS = "?", // TODO: Determine this flag
                                                    // 65 BDV and Taper only expected on first element in COBOL
                        D13_2_B_F_BUDGET_DAY_VALUE = 0,
                        D13_2_BUDGET_DAY_VALUE_YTD = 0,
                        D13_2_TAPER_DATE = string.Empty,
                        D13_2_B_F_TAPER_UNITS = 0,
                        D13_2_TAPER_UNITS_YTD = 0
                    };
                    holder.D13_2_COST_TABLE.Add(costN);
                    daoCostIndex++;
                }
                else
                {
                    // Add empty cost element if DAO list is shorter than expected count
                    holder.D13_2_COST_TABLE.Add(new D13CostElement());
                }
            }

            // Add 1982 value as the last cost element if present
            if (has82Value)
            {
                var cost82 = new D13CostElement
                {
                    D13_2_UNITS_PRESENT = "7",
                    D13_2_B_F_UNIND_COST_BALANCE = daRecord.MBDA_BudgetDayValue82BF,
                    D13_2_UNIND_COST_BALANCE_YTD = daRecord.MBDA_BudgetDayValue82YTD,
                    // Other fields are zero/space according to COBOL logic
                    D13_2_B_F_BALANCE_UNITS = 0,
                    D13_2_BALANCE_UNITS_YTD = 0,
                    D13_2_INDEX_DATE = "000000",
                    D13_2_COST_DATE = "000000",
                    D13_2_B_F_BUDGET_DAY_VALUE = 0,
                    D13_2_BUDGET_DAY_VALUE_YTD = 0,
                    D13_2_TAPER_DATE = string.Empty,
                    D13_2_B_F_TAPER_UNITS = 0,
                    D13_2_TAPER_UNITS_YTD = 0,
                    D13_2_NI_NP_PI_COSTS = "?" // TODO: Determine this flag
                };
                holder.D13_2_COST_TABLE.Add(cost82);
            }

            // Update LinkageIDs (Balance specific part)
            // linkageIds = LinkageIDs.Create(); // Don't clear, add to existing header info if needed
            linkageIds.MBDA_BalanceID = daRecord.MBDA_BalanceID;
            linkageIds.MBDA_HoldingID = daRecord.MBDA_HoldingID;
            linkageIds.MBDA_ParentStockID = daRecord.MBDA_ParentStockID;
            linkageIds.MBDA_DB_Timestamp = daRecord.MBDA_DB_Timestamp;
            // Other Balance-IDS fields if necessary

            return holder;
        }

        // A3: Setup D13RecordHolder (COBOL format) from MasterAcquisitionDARecord (C# DAO)
        private D13RecordHolder SetupAcquisitionCobolRecord(MasterAcquisitionDARecord daRecord, ref LinkageIDs linkageIds)
        {
            var holder = InitializeBalanceAcquisitionHolder(); // Start with defaults

            // Map common fields
            holder.D13_1_CO_AC_LK = _masterStoreFundCode;
            holder.D13_1_SEDOL = daRecord.MADA_SedolCode;
            holder.D13_1_CONTRACT_NO = daRecord.MADA_ContractNumber;
            holder.D13_1_RECORD_CODE = CobolConstants.REC_CODE_BALANCE_ACQUISITION;
            holder.D13_2_TRANSACTION_CATEGORY = daRecord.MADA_TransactionCategoryCode;
            holder.D13_2_DATE_TIME_STAMP = daRecord.MADA_DateTimeStamp;
            if (!string.IsNullOrEmpty(daRecord.MADA_ParentSedolCode))
            {
                holder.D13_2_PARENT_CAL = _masterStoreFundCode;
                holder.D13_2_PARENT_SEDOL_CODE = daRecord.MADA_ParentSedolCode;
            }
            // Map Previous CAL/SEDOL based on transaction type
            switch (daRecord.MADA_TransactionCategoryCode)
            {
                case CobolConstants.TRANS_EXERCISE_WRITTEN_PUT:
                    if (!string.IsNullOrEmpty(daRecord.MADA_PrevAcqnSedolCode))
                    {
                        holder.D13_2_PREVIOUS_CAL = daRecord.MADA_PrevAcqnFundCode;
                        holder.D13_2_PREVIOUS_SEDOL_CODE = daRecord.MADA_PrevAcqnSedolCode;
                    }
                    break;
                default:
                    if (!string.IsNullOrEmpty(daRecord.MADA_PrevDispSedolCode))
                    {
                        holder.D13_2_PREVIOUS_CAL = daRecord.MADA_PrevDispFundCode;
                        holder.D13_2_PREVIOUS_SEDOL_CODE = daRecord.MADA_PrevDispSedolCode;
                    }
                    break;
            }
            holder.D13_2_ORIGINAL_BARGAIN_NO = daRecord.MADA_BargainNumber;
            holder.D13_2_BARGAIN_DATE = daRecord.MADA_BargainDate;
            holder.D13_2_SETTLEMENT_DATE = daRecord.MADA_SettlementDate;
            holder.D13_2_NOTES_COMMENTS = daRecord.MADA_NotesComments;
            holder.D13_2_CT_LINK_FUND = daRecord.MADA_CTLinkFundCode;
            holder.D13_2_PARENT_MARKET_PRICE = daRecord.MADA_ParentPrice;
            holder.D13_2_TRANS_UNIT_PRICE = daRecord.MADA_TransactionPrice;
            holder.D13_2_LIABILITY_PER_SHARE = daRecord.MADA_LiabilityPerShare;
            holder.D13_2_OUTSTANDING_LIABILITY = daRecord.MADA_OSLiabilityPerShare;
            holder.D13_2_STOCK_EXCH_INDICATOR = daRecord.MADA_StockExchangeIndicator;
            holder.D13_2_TRANSACTION_EXPORTED = daRecord.MADA_TransactionExported;
            // holder.D13_2_B_F_NI_PI_FLAG = ???; // Not in MasterAcquisitionDARecord
            // holder.D13_2_NI_PI_FLAG_YTD = ???;
            // holder.D13_2_SUB_FUND = ???;
            // holder.D13_2_TRANSACTION_OVERRIDE = ???;
            // holder.D13_2_PRICE_PCT_INDICATOR = ???;

            // Map Acquisition specific fields (mapping to Balance/Acq fields in D13Holder)
            holder.D13_2_TRANCHE_FLAG = daRecord.MADA_TrancheFlag;
            holder.D13_2_RESET_TAPER_DATE = daRecord.MADA_ResetTaperDateFlag;
            holder.D13_2_CapitalGainLoss_AcqBal = daRecord.MADA_CapitalGainLoss;
            holder.D13_2_ProfitLoss_AcqBal = daRecord.MADA_ProfitLoss;
            holder.D13_2_ProceedsOfDisposal_AcqBal = daRecord.MADA_Proceeds;
            holder.D13_2_FIRST_DAY_DEALING_PRICE = daRecord.MADA_FDDPrice;

            // Map units/cost to the first cost element
            holder.D13_2_B_F_TRANCHE_TOTAL_UNITS = daRecord.MADA_NumberOfUnits_BF;
            holder.D13_2_TRANCHE_TOTAL_UNITS_YTD = daRecord.MADA_NumberOfUnits_YTD;

            var cost1 = new D13CostElement();
            if ((daRecord.MADA_NumberOfUnits_BF ?? 0) > 0 || (daRecord.MADA_NumberOfUnits_YTD ?? 0) > 0)
            {
                cost1.D13_2_UNITS_PRESENT = "1";
                cost1.D13_2_B_F_BALANCE_UNITS = daRecord.MADA_NumberOfUnits_BF;
                cost1.D13_2_BALANCE_UNITS_YTD = daRecord.MADA_NumberOfUnits_YTD;
                cost1.D13_2_B_F_UNIND_COST_BALANCE = daRecord.MADA_Cost_BF;
                cost1.D13_2_UNIND_COST_BALANCE_YTD = daRecord.MADA_Cost_YTD;
                cost1.D13_2_INDEX_DATE = daRecord.MADA_BargainDate; // Use bargain date
                cost1.D13_2_COST_DATE = daRecord.MADA_BargainDate;  // Use bargain date
                cost1.D13_2_NI_NP_PI_COSTS = "?"; // TODO: Determine flag
            }
            else
            {
                cost1.D13_2_UNITS_PRESENT = "0";
            }
            holder.D13_2_COST_TABLE = new List<D13CostElement> { cost1 };
            holder.D13_2_B_F_NO_OF_COSTS_HELD = 1; // Acquisition always has 1 effective cost entry
            holder.D13_2_NO_OF_COSTS_HELD_YTD = 1;

            // Update LinkageIDs (Acquisition specific part)
            linkageIds.MADA_AcquisitionID = daRecord.MADA_AcquisitionID;
            linkageIds.MADA_HoldingID = daRecord.MADA_HoldingID;
            linkageIds.MADA_ParentStockID = daRecord.MADA_ParentStockID;
            linkageIds.MADA_TransactionCategoryID = daRecord.MADA_TransactionCategoryID;
            linkageIds.MADA_CTLinkFundID = daRecord.MADA_CTLinkFundID;
            linkageIds.MADA_PrevDispID = daRecord.MADA_PrevDispID;
            linkageIds.MADA_PrevAcqnID = daRecord.MADA_PrevAcqnID;
            linkageIds.MADA_DB_Timestamp = daRecord.MADA_DB_Timestamp;

            return holder;
        }

        // A4: Setup D13RecordHolder (COBOL format) from MasterDisposalDARecord (C# DAO)
        private D13RecordHolder SetupDisposalCobolRecord(MasterDisposalDARecord daRecord, ref LinkageIDs linkageIds)
        {
            var holder = InitializeDisposalHolder(); // Start with defaults

            // Map common fields
            holder.D13_1_CO_AC_LK = _masterStoreFundCode;
            holder.D13_1_SEDOL = daRecord.MDDA_Sedol;
            holder.D13_1_CONTRACT_NO = daRecord.MDDA_ContractNo;
            holder.D13_1_RECORD_CODE = CobolConstants.REC_CODE_DISPOSAL; // Set disposal code
            holder.D13_2_TRANSACTION_CATEGORY = daRecord.MDDA_TransactionCategory;
            holder.D13_2_DATE_TIME_STAMP = daRecord.MDDA_DateTimeStamp;
            if (!string.IsNullOrEmpty(daRecord.MDDA_ParentSedol))
            {
                holder.D13_2_PARENT_CAL = _masterStoreFundCode;
                holder.D13_2_PARENT_SEDOL_CODE = daRecord.MDDA_ParentSedol;
            }
            // Map Previous CAL/SEDOL based on transaction type
            switch (daRecord.MDDA_TransactionCategory)
            {
                case CobolConstants.TRANS_EXERCISE_PURCHASED_PUT:
                    if (!string.IsNullOrEmpty(daRecord.MDDA_PrevDispSedolCode))
                    {
                        holder.D13_2_PREVIOUS_CAL = daRecord.MDDA_PrevDispFundCode;
                        holder.D13_2_PREVIOUS_SEDOL_CODE = daRecord.MDDA_PrevDispSedolCode;
                    }
                    break;
                case CobolConstants.TRANS_EXERCISE_WRITTEN_CALL:
                    if (!string.IsNullOrEmpty(daRecord.MDDA_PrevAcqnSedolCode))
                    {
                        holder.D13_2_PREVIOUS_CAL = daRecord.MDDA_PrevAcqnFundCode;
                        holder.D13_2_PREVIOUS_SEDOL_CODE = daRecord.MDDA_PrevAcqnSedolCode;
                    }
                    break;
            }
            holder.D13_2_ORIGINAL_BARGAIN_NO = daRecord.MDDA_BargainNumber;
            holder.D13_2_BARGAIN_DATE = daRecord.MDDA_BargainDate;
            holder.D13_2_SETTLEMENT_DATE = daRecord.MDDA_SettlementDate;
            holder.D13_2_NOTES_COMMENTS = daRecord.MDDA_NotesComments;
            holder.D13_2_CT_LINK_FUND = daRecord.MDDA_CTLinkFundCode;
            holder.D13_2_PARENT_MARKET_PRICE = daRecord.MDDA_ParentPrice;
            holder.D13_2_TRANS_UNIT_PRICE = daRecord.MDDA_TransactionPrice;
            holder.D13_2_LIABILITY_PER_SHARE = daRecord.MDDA_LiabilityPerShare;
            holder.D13_2_OUTSTANDING_LIABILITY = daRecord.MDDA_OSLiabilityPerShare;
            holder.D13_2_STOCK_EXCH_INDICATOR = daRecord.MDDA_StockExchangeIndicator;
            holder.D13_2_TRANSACTION_EXPORTED = daRecord.MDDA_TransactionExported;
            // holder.D13_2_B_F_NI_PI_FLAG = ???; // Not on Disposal
            // holder.D13_2_NI_PI_FLAG_YTD = ???;
            // holder.D13_2_SUB_FUND = ???;
            // holder.D13_2_TRANSACTION_OVERRIDE = ???;
            // holder.D13_2_PRICE_PCT_INDICATOR = ???;

            // Map Disposal specific fields
            holder.D13_2_NUMBER_OF_UNITS = daRecord.MDDA_NumberOfUnits;
            holder.D13_2_PROCEEDS = daRecord.MDDA_Proceeds;
            holder.D13_2_CAPITAL_GAIN_LOSS_Disp = daRecord.MDDA_CapitalGainLoss;
            holder.D13_2_FORCE_2PCT_MATCH_FLAG = daRecord.MDDA_Force2PercentMatching; // TODO: Check mapping logic if needed
            holder.D13_2_CGT_COST = daRecord.MDDA_CgtCost;
            holder.D13_2_PROFIT_LOSS_Disp = daRecord.MDDA_ProfitLoss;
            holder.D13_2_INDEXATION_USED = daRecord.MDDA_IndexationUsed;
            holder.D13_2_BOND_DISPOSAL = daRecord.MDDA_BondDisposal;
            // holder.D13_2_CONSOLIDATED_FLAG = ???; // Not in MasterDisposalDARecord
            // holder.D13_2_STORE_UNITS = ???;
            // holder.D13_2_STORE_PROCEEDS = ???;
            // holder.D13_2_CONSOLIDATE_KEY = ???;
            holder.D13_2_FA_2003_EXEMPTION = daRecord.MDDA_FA2003Exemption;
            holder.D13_2_GT_PRO_RATA_MATCH = daRecord.MDDA_GTMatchProRata;
            holder.D13_2_GT_USE_ORIGINAL_DATES = daRecord.MDDA_GTUseOriginalDates;
            holder.D13_2_NumberOfUnitsYTD_Disp = daRecord.MDDA_NumberOfUnitsYTD;
            holder.D13_2_ProceedsYTD_Disp = daRecord.MDDA_ProceedsYTD;

            // Update LinkageIDs (Disposal specific part)
            linkageIds.MDDA_DisposalID = daRecord.MDDA_DisposalID;
            linkageIds.MDDA_HoldingID = daRecord.MDDA_HoldingID;
            linkageIds.MDDA_ParentStockID = daRecord.MDDA_ParentStockID;
            linkageIds.MDDA_TransactionCategoryID = daRecord.MDDA_TransactionCategoryID;
            linkageIds.MDDA_CTLinkFundID = daRecord.MDDA_CTLinkFundID;
            linkageIds.MDDA_PrevDispID = daRecord.MDDA_PrevDispID;
            linkageIds.MDDA_PrevAcqnID = daRecord.MDDA_PrevAcqnID;
            linkageIds.MDDA_DB_Timestamp = daRecord.MDDA_DB_Timestamp;

            return holder;
        }

        // A5: Setup MasterHoldingDARecord (C# DAO) from D13RecordHolder (COBOL format)
        private MasterHoldingDARecord SetupHeaderCsRecord(D13RecordHolder holder, LinkageIDs linkageIds)
        {
            var da = new MasterHoldingDARecord();

            // Map from holder to da
            da.MHDA_RecordType = "H";
            da.MHDA_FundCode = holder.D13_1_CO_AC_LK;
            da.MHDA_SedolCode = holder.D13_1_SEDOL;
            da.MHDA_DateTimeStamp = holder.D13_1_DATE_TIME_STAMP;
            da.MHDA_HoldingStatusCode = holder.D13_1_CALC_FLAG;
            da.MHDA_RealisedCalculationDate = holder.D13_1_R_CALC_PERIOD_END_DATE;
            da.MHDA_UnrealisedCalculationDate = holder.D13_1_U_CALC_PERIOD_END_DATE;
            da.MHDA_SortCode = holder.D13_1_SECURITY_SORT_CODE;
            da.MHDA_CountryCode = holder.D13_1_COUNTRY_CODE;
            da.MHDA_GroupCode = holder.D13_1_GROUP_CODE;
            da.MHDA_SecurityType = holder.D13_1_SECURITY_TYPE;
            da.MHDA_MarketPrice = holder.D13_1_CURRENT_MARKET_PRICE;
            da.MHDA_PricePercentIndicator = holder.D13_1_PRICE_PCT_INDICATOR;
            da.MHDA_BookValueBF = holder.D13_1_B_F_ACCOUNTING_VALUE;
            da.MHDA_BookValueYTD = holder.D13_1_ACCOUNTING_VALUE_YTD;
            da.MHDA_ProfitLoss = holder.D13_1_PROFIT_LOSS;
            da.MHDA_IssuersName = holder.D13_1_ISSUERS_NAME;
            da.MHDA_StockDescription = holder.D13_1_STOCK_DESCRIPTION;
            da.MHDA_IssuedCapital = holder.D13_1_ISSUED_CAPITAL;
            da.MHDA_Sedolmovement = holder.D13_1_MOVEMENT_INDICATOR?.ToString();
            da.MHDA_DateOf2PercentHoldingBF = holder.D13_1_B_F_2PCT_HOLDING_DATE_R;
            da.MHDA_DateOf2PercentHoldingYTD = holder.D13_1_2PCT_HOLDING_DATE_YTD_R;
            da.MHDA_SecurityInd = holder.D13_1_SECURITY_INDICATOR;
            da.MHDA_totalUnits = holder.D13_1_TOTAL_UNITS_YTD;
            da.MHDA_CapitalGainLoss = holder.D13_1_CAPITAL_GAIN_LOSS;
            da.MHDA_UnrealisedGain = holder.D13_1_UNREAL_GAIN_LOSS;
            da.MHDA_UnrealisedProfit = holder.D13_1_UNREAL_PROFIT_LOSS;
            da.MHDA_Offshore_Class = holder.D13_1_OFFSHORE_CLASS;
            da.MHDA_OverrideYearEnd = holder.D13_1_OVER_RIDE_FLAG;
            da.MHDA_DateOfIssue = holder.D13_1_DATE_OF_ISSUE_R;
            da.MHDA_IndexFromIssue = holder.D13_1_INDEX_FROM_ISSUE;
            da.MHDA_DeemedGainLoss = holder.D13_1_DEEMED_GAIN_LOSS;
            da.MHDA_DeemedProfitLoss = holder.D13_1_DEEMED_PROFIT_LOSS;
            da.MHDA_HoldingFlag = holder.D13_1_HOLDING_FLAG;
            da.MHDA_BondOverrideCode = holder.D13_1_BOND_OVERRIDE;
            da.MHDA_LRBasis = holder.D13_1_LR_BASIS;
            da.MHDA_BondMaturityDate = holder.D13_1_BOND_MATURITY_DATE_R;
            da.MHDA_BondParValue = holder.D13_1_BOND_PAR_VALUE;
            da.MHDA_BondGainOverride = holder.D13_1_BOND_GAIN_OVERRIDE;
            da.MHDA_UnrealisedBondGainLoss = holder.D13_1_UNREAL_BOND_GAIN_LOSS;
            da.MHDA_UnrealisedBondProfitLoss = holder.D13_1_UNREAL_BOND_PROFIT_LOSS;
            da.MHDA_AssetUsageOverrideCode = holder.D13_1_ASSET_USAGE; // Mapping based on COBOL line 2404
            da.MHDA_RealisedBondGainLoss = holder.D13_1_REAL_BOND_GAIN_LOSS;
            da.MHDA_RealisedBondProfitLoss = holder.D13_1_REAL_BOND_PROFIT_LOSS;
            // da.MHDA_MarketPriceStatus = ???; // Needs L-PRICE-TYPE-STATUS input?
            // da.MHDA_AssetUsage = ???; // Not directly mapped back?

            // Apply IDs from LinkageIDs struct
            da.MHDA_HoldingID = linkageIds.MHDA_HoldingID;
            da.MHDA_FundID = linkageIds.MHDA_FundID;
            da.MHDA_StockID = linkageIds.MHDA_StockID;
            da.MHDA_BondOverrideID = linkageIds.MHDA_BondOverrideID;
            da.MHDA_AssetUsageOverrideID = linkageIds.MHDA_AssetUsageOverrideID;
            da.MHDA_DB_Timestamp = linkageIds.MHDA_DB_Timestamp;
            da.MHDA_ILGCondition = linkageIds.MHDA_ILGCondition;

            // Ensure timestamp is not spaces (COBOL line 2413)
            if (string.IsNullOrEmpty(da.MHDA_DB_Timestamp))
            {
                da.MHDA_DB_Timestamp = "000000000000000000000000"; // Zeroes or appropriate default
            }


            return da;
        }

        // A6: Setup MasterBalanceDARecord (C# DAO) from D13RecordHolder (COBOL format)
        private MasterBalanceDARecord SetupBalanceCsRecord(D13RecordHolder holder, LinkageIDs linkageIds)
        {
            var da = new MasterBalanceDARecord();

            // Map common fields
            da.MBDA_RecordType = "B";
            // da.MBDA_FundCode = holder.D13_1_CO_AC_LK; // Fund code not on MBDA
            da.MBDA_SedolCode = holder.D13_1_SEDOL;
            da.MBDA_ContractNumber = holder.D13_1_CONTRACT_NO;
            da.MBDA_BalanceTypeCode = holder.D13_2_TRANSACTION_CATEGORY;
            da.MBDA_DateTimeStamp = holder.D13_2_DATE_TIME_STAMP;
            da.MBDA_ParentSedolCode = holder.D13_2_PARENT_SEDOL_CODE;
            da.MBDA_BargainDate = holder.D13_2_BARGAIN_DATE;
            da.MBDA_NotesComments = holder.D13_2_NOTES_COMMENTS;
            da.MBDA_StockExchangeIndicator = holder.D13_2_STOCK_EXCH_INDICATOR;
            da.MBDA_ParentPrice = holder.D13_2_PARENT_MARKET_PRICE;
            da.MBDA_TotalLiabilityPrice = holder.D13_2_LIABILITY_PER_SHARE;
            da.MBDA_OutstandingLiabilityPrice = holder.D13_2_OUTSTANDING_LIABILITY;
            da.MBDA_NewIssuePrivFlagBF = holder.D13_2_B_F_NI_PI_FLAG;
            da.MBDA_NewIssuePrivFlagYTD = holder.D13_2_NI_PI_FLAG_YTD;

            // Map Balance specific fields
            da.MBDA_totalUnitsPerTrancheBF = holder.D13_2_B_F_TRANCHE_TOTAL_UNITS;
            da.MBDA_totalUnitsPerTrancheYTD = holder.D13_2_TRANCHE_TOTAL_UNITS_YTD;
            da.MBDA_DisposalUnitsReacquiredBF = holder.D13_2_B_F_DISP_UNITS_REAC;
            da.MBDA_DisposalUnitsReacquiredYTD = holder.D13_2_DISP_UNITS_REAC_YTD;
            da.MBDA_BookCost = holder.D13_2_BOOK_COST;
            da.MBDA_PrevOpEventDateBF = holder.D13_2_B_F_DATE_PREV_OP_EVENT_R;
            da.MBDA_PrevOpEventDateYTD = holder.D13_2_DATE_PREV_OP_EVENT_YTD;
            da.MBDA_FDDPrice = holder.D13_2_FIRST_DAY_DEALING_PRICE;
            da.MBDA_IndexedCostBalanceBF = holder.D13_2_B_F_INDEXED_COST_BALANCE;
            da.MBDA_IndexedCostBalanceYTD = holder.D13_2_INDEXED_COST_BALANCE_YTD;
            da.MBDA_UnindexableHoldingsFlag = holder.D13_2_UNINDEXABLE_FLAG;
            da.MBDA_LinkedRecordIndicatorBF = holder.D13_2_B_F_LINK_RECORD_INDIC?.ToString();
            da.MBDA_LinkedRecordIndicatorYTD = holder.D13_2_LINK_RECORD_INDIC_YTD?.ToString();
            da.MBDA_IndexedPoolBF = holder.D13_2_B_F_INDEX85_COST_BALANCE;
            da.MBDA_IndexedPoolYTD = holder.D13_2_INDEX85_COST_BALANCE_YTD;
            da.MBDA_TrancheFlag = holder.D13_2_TRANCHE_FLAG;
            da.MBDA_ResetTaperDateFlag = holder.D13_2_RESET_TAPER_DATE;
            da.MBDA_NumberofInitialBookCosts = holder.D13_2_NO_OF_INITIAL_BOOK_COSTS;
            da.MBDA_NumberofCostsHeldBF = holder.D13_2_B_F_NO_OF_COSTS_HELD;
            // MBDA_NumberofCostsHeldYTD is calculated below

            // Map Cost Table back to DAO structure
            da.Master_Balance_Cost_DA_Record = new List<MasterBalanceCostDARecord>();
            int costCountOut = 0;
            bool found82Value = false;
            decimal? budgetDay82BF = 0;
            decimal? budgetDay82YTD = 0;

            // Handle first cost element (Base/65BDV/Taper)
            if (holder.D13_2_COST_TABLE.Count > 0)
            {
                var cost1 = holder.D13_2_COST_TABLE[0];
                da.MBDA_BaseUnitsBF = cost1.D13_2_B_F_BALANCE_UNITS;
                da.MBDA_BaseUnitsYTD = cost1.D13_2_BALANCE_UNITS_YTD;
                da.MBDA_BaseCostBF = cost1.D13_2_B_F_UNIND_COST_BALANCE;
                da.MBDA_BaseCostYTD = cost1.D13_2_UNIND_COST_BALANCE_YTD;
                da.MBDA_BaseIndexDate = cost1.D13_2_INDEX_DATE;
                // da.MBDA_BargainDate = cost1.D13_2_COST_DATE; // Already set from common fields

                da.MBDA_BudgetDayValue65BF = cost1.D13_2_B_F_BUDGET_DAY_VALUE;
                da.MBDA_BudgetDayValue65YTD = cost1.D13_2_BUDGET_DAY_VALUE_YTD;
                da.MBDA_TaperDate = cost1.D13_2_TAPER_DATE;
                da.MBDA_TaperUnitsBF = cost1.D13_2_B_F_TAPER_UNITS;
                da.MBDA_TaperUnitsYTD = cost1.D13_2_TAPER_UNITS_YTD;
            }

            // Handle subsequent cost elements
            for (int i = 1; i < holder.D13_2_COST_TABLE.Count; i++)
            {
                var costN = holder.D13_2_COST_TABLE[i];

                // Skip blank cost types (COBOL line 2497)
                if (string.IsNullOrEmpty(costN.D13_2_UNITS_PRESENT)) continue;

                // Handle 82 value (COBOL line 2501)
                if (costN.D13_2_UNITS_PRESENT == "7")
                {
                    budgetDay82BF = costN.D13_2_B_F_UNIND_COST_BALANCE;
                    budgetDay82YTD = costN.D13_2_UNIND_COST_BALANCE_YTD;
                    found82Value = true;
                    continue; // Don't add to cost list
                }

                // Skip zero value costs (COBOL line 2508)
                if ((costN.D13_2_B_F_BALANCE_UNITS ?? 0) == 0 &&
                    (costN.D13_2_BALANCE_UNITS_YTD ?? 0) == 0 &&
                    (costN.D13_2_B_F_UNIND_COST_BALANCE ?? 0) == 0 &&
                    (costN.D13_2_UNIND_COST_BALANCE_YTD ?? 0) == 0)
                {
                    continue;
                }

                // Add valid cost element to DAO list
                var daoCost = new MasterBalanceCostDARecord
                {
                    MCDA_BalanceUnitsBF = costN.D13_2_B_F_BALANCE_UNITS,
                    MCDA_BalanceUnitsYTD = costN.D13_2_BALANCE_UNITS_YTD,
                    MCDA_CostTypeCode = costN.D13_2_UNITS_PRESENT,
                    MCDA_UnindexedCostBalanceBF = costN.D13_2_B_F_UNIND_COST_BALANCE,
                    MCDA_UnindexedCostBalanceYTD = costN.D13_2_UNIND_COST_BALANCE_YTD,
                    MCDA_IndexDate = costN.D13_2_INDEX_DATE,
                    MCDA_CostDate = costN.D13_2_COST_DATE
                    // TODO: Map NI_NP_PI_COSTS if needed
                };
                da.Master_Balance_Cost_DA_Record.Add(daoCost);
                costCountOut++;
            }

            da.MBDA_BudgetDayValue82BF = budgetDay82BF;
            da.MBDA_BudgetDayValue82YTD = budgetDay82YTD;

            // Calculate final cost count (COBOL line 2560)
            da.MBDA_NumberofCostsHeldYTD = costCountOut + 1; // +1 for the base cost element
            if (found82Value)
            {
                da.MBDA_NumberofCostsHeldYTD++;
            }


            // Apply IDs from LinkageIDs struct
            da.MBDA_BalanceID = linkageIds.MBDA_BalanceID;
            da.MBDA_HoldingID = linkageIds.MBDA_HoldingID;
            da.MBDA_ParentStockID = linkageIds.MBDA_ParentStockID;
            da.MBDA_DB_Timestamp = linkageIds.MBDA_DB_Timestamp;

            // Ensure timestamp is not spaces (COBOL line 2566)
            if (string.IsNullOrEmpty(da.MBDA_DB_Timestamp))
            {
                da.MBDA_DB_Timestamp = "000000000000000000000000"; // Zeroes or appropriate default
            }

            return da;
        }

        // A7: Setup MasterAcquisitionDARecord (C# DAO) from D13RecordHolder (COBOL format)
        private MasterAcquisitionDARecord SetupAcquisitionCsRecord(D13RecordHolder holder, LinkageIDs linkageIds)
        {
            var da = new MasterAcquisitionDARecord();

            // Map common fields
            da.MADA_RecordType = "A";
            // da.MADA_FundCode = holder.D13_1_CO_AC_LK; // Not on MADA
            da.MADA_SedolCode = holder.D13_1_SEDOL;
            da.MADA_ContractNumber = holder.D13_1_CONTRACT_NO;
            da.MADA_TransactionCategoryCode = holder.D13_2_TRANSACTION_CATEGORY;
            da.MADA_DateTimeStamp = holder.D13_2_DATE_TIME_STAMP;
            da.MADA_ParentSedolCode = holder.D13_2_PARENT_SEDOL_CODE;
            // Map Previous Fund/SEDOL based on type
            switch (holder.D13_2_TRANSACTION_CATEGORY) // Use holder's category
            {
                case CobolConstants.TRANS_EXERCISE_WRITTEN_PUT:
                    da.MADA_PrevAcqnFundCode = holder.D13_2_PREVIOUS_CAL;
                    da.MADA_PrevAcqnSedolCode = holder.D13_2_PREVIOUS_SEDOL_CODE;
                    break;
                default:
                    da.MADA_PrevDispFundCode = holder.D13_2_PREVIOUS_CAL;
                    da.MADA_PrevDispSedolCode = holder.D13_2_PREVIOUS_SEDOL_CODE;
                    break;
            }
            da.MADA_BargainNumber = holder.D13_2_ORIGINAL_BARGAIN_NO;
            da.MADA_BargainDate = holder.D13_2_BARGAIN_DATE;
            da.MADA_SettlementDate = holder.D13_2_SETTLEMENT_DATE;
            da.MADA_NotesComments = holder.D13_2_NOTES_COMMENTS;
            da.MADA_CTLinkFundCode = holder.D13_2_CT_LINK_FUND;
            da.MADA_ParentPrice = holder.D13_2_PARENT_MARKET_PRICE;
            da.MADA_TransactionPrice = holder.D13_2_TRANS_UNIT_PRICE;
            da.MADA_LiabilityPerShare = holder.D13_2_LIABILITY_PER_SHARE;
            da.MADA_OSLiabilityPerShare = holder.D13_2_OUTSTANDING_LIABILITY;
            da.MADA_StockExchangeIndicator = holder.D13_2_STOCK_EXCH_INDICATOR;
            da.MADA_TransactionExported = holder.D13_2_TRANSACTION_EXPORTED;

            // Map Acquisition specific fields
            da.MADA_TrancheFlag = holder.D13_2_TRANCHE_FLAG;
            da.MADA_ResetTaperDateFlag = holder.D13_2_RESET_TAPER_DATE;
            da.MADA_CapitalGainLoss = holder.D13_2_CapitalGainLoss_AcqBal;
            da.MADA_ProfitLoss = holder.D13_2_ProfitLoss_AcqBal;
            da.MADA_Proceeds = holder.D13_2_ProceedsOfDisposal_AcqBal;
            da.MADA_FDDPrice = holder.D13_2_FIRST_DAY_DEALING_PRICE;

            // Map units/cost from first cost element
            da.MADA_NumberOfUnits_BF = holder.D13_2_B_F_TRANCHE_TOTAL_UNITS;
            da.MADA_NumberOfUnits_YTD = holder.D13_2_TRANCHE_TOTAL_UNITS_YTD;
            if (holder.D13_2_COST_TABLE.Count > 0)
            {
                var cost1 = holder.D13_2_COST_TABLE[0];
                da.MADA_Cost_BF = cost1.D13_2_B_F_UNIND_COST_BALANCE;
                da.MADA_Cost_YTD = cost1.D13_2_UNIND_COST_BALANCE_YTD;
                // Bargain date already mapped
            }

            // Apply IDs from LinkageIDs struct
            da.MADA_AcquisitionID = linkageIds.MADA_AcquisitionID;
            da.MADA_HoldingID = linkageIds.MADA_HoldingID;
            da.MADA_ParentStockID = linkageIds.MADA_ParentStockID;
            da.MADA_TransactionCategoryID = linkageIds.MADA_TransactionCategoryID;
            da.MADA_CTLinkFundID = linkageIds.MADA_CTLinkFundID;
            da.MADA_PrevDispID = linkageIds.MADA_PrevDispID;
            da.MADA_PrevAcqnID = linkageIds.MADA_PrevAcqnID;
            da.MADA_DB_Timestamp = linkageIds.MADA_DB_Timestamp;

            // Ensure timestamp is not spaces (COBOL line 2640)
            if (string.IsNullOrEmpty(da.MADA_DB_Timestamp))
            {
                da.MADA_DB_Timestamp = "000000000000000000000000"; // Zeroes or appropriate default
            }

            return da;
        }

        // A8: Setup MasterDisposalDARecord (C# DAO) from D13RecordHolder (COBOL format)
        private MasterDisposalDARecord SetupDisposalCsRecord(D13RecordHolder holder, LinkageIDs linkageIds)
        {
            var da = new MasterDisposalDARecord();

            // Map common fields
            da.MDDA_RecordType = "D";
            // da.MDDA_FundCode = holder.D13_1_CO_AC_LK; // Not on MDDA
            da.MDDA_Sedol = holder.D13_1_SEDOL;
            da.MDDA_ContractNo = holder.D13_1_CONTRACT_NO;
            da.MDDA_TransactionCategory = holder.D13_2_TRANSACTION_CATEGORY;
            da.MDDA_DateTimeStamp = holder.D13_2_DATE_TIME_STAMP;
            da.MDDA_ParentSedol = holder.D13_2_PARENT_SEDOL_CODE;
            // Map Previous Fund/SEDOL based on type
            switch (holder.D13_2_TRANSACTION_CATEGORY) // Use holder's category
            {
                case CobolConstants.TRANS_EXERCISE_WRITTEN_CALL:
                    da.MDDA_PrevAcqnFundCode = holder.D13_2_PREVIOUS_CAL;
                    da.MDDA_PrevAcqnSedolCode = holder.D13_2_PREVIOUS_SEDOL_CODE;
                    break;
                case CobolConstants.TRANS_EXERCISE_PURCHASED_PUT:
                    da.MDDA_PrevDispFundCode = holder.D13_2_PREVIOUS_CAL; // Corrected target field
                    da.MDDA_PrevDispSedolCode = holder.D13_2_PREVIOUS_SEDOL_CODE; // Corrected target field
                    break;
            }
            da.MDDA_BargainNumber = holder.D13_2_ORIGINAL_BARGAIN_NO;
            da.MDDA_BargainDate = holder.D13_2_BARGAIN_DATE;
            da.MDDA_SettlementDate = holder.D13_2_SETTLEMENT_DATE;
            da.MDDA_NotesComments = holder.D13_2_NOTES_COMMENTS;
            da.MDDA_CTLinkFundCode = holder.D13_2_CT_LINK_FUND;
            da.MDDA_ParentPrice = holder.D13_2_PARENT_MARKET_PRICE;
            da.MDDA_TransactionPrice = holder.D13_2_TRANS_UNIT_PRICE;
            da.MDDA_LiabilityPerShare = holder.D13_2_LIABILITY_PER_SHARE;
            da.MDDA_OSLiabilityPerShare = holder.D13_2_OUTSTANDING_LIABILITY;
            da.MDDA_StockExchangeIndicator = holder.D13_2_STOCK_EXCH_INDICATOR;
            da.MDDA_TransactionExported = holder.D13_2_TRANSACTION_EXPORTED;

            // Map Disposal specific fields
            da.MDDA_NumberOfUnits = holder.D13_2_NUMBER_OF_UNITS;
            da.MDDA_Proceeds = holder.D13_2_PROCEEDS;
            da.MDDA_CapitalGainLoss = holder.D13_2_CAPITAL_GAIN_LOSS_Disp;
            da.MDDA_Force2PercentMatching = holder.D13_2_FORCE_2PCT_MATCH_FLAG;
            da.MDDA_CgtCost = holder.D13_2_CGT_COST;
            da.MDDA_ProfitLoss = holder.D13_2_PROFIT_LOSS_Disp;
            da.MDDA_IndexationUsed = holder.D13_2_INDEXATION_USED;
            da.MDDA_BondDisposal = holder.D13_2_BOND_DISPOSAL;
            da.MDDA_FA2003Exemption = holder.D13_2_FA_2003_EXEMPTION;
            da.MDDA_GTMatchProRata = holder.D13_2_GT_PRO_RATA_MATCH;
            da.MDDA_GTUseOriginalDates = holder.D13_2_GT_USE_ORIGINAL_DATES;
            da.MDDA_NumberOfUnitsYTD = holder.D13_2_NumberOfUnitsYTD_Disp;
            da.MDDA_ProceedsYTD = holder.D13_2_ProceedsYTD_Disp;

            // Apply IDs from LinkageIDs struct
            da.MDDA_DisposalID = linkageIds.MDDA_DisposalID;
            da.MDDA_HoldingID = linkageIds.MDDA_HoldingID;
            da.MDDA_ParentStockID = linkageIds.MDDA_ParentStockID;
            da.MDDA_TransactionCategoryID = linkageIds.MDDA_TransactionCategoryID;
            da.MDDA_CTLinkFundID = linkageIds.MDDA_CTLinkFundID;
            da.MDDA_PrevDispID = linkageIds.MDDA_PrevDispID;
            da.MDDA_PrevAcqnID = linkageIds.MDDA_PrevAcqnID;
            da.MDDA_DB_Timestamp = linkageIds.MDDA_DB_Timestamp;

            // Ensure timestamp is not spaces (COBOL line 2714)
            if (string.IsNullOrEmpty(da.MDDA_DB_Timestamp))
            {
                da.MDDA_DB_Timestamp = "000000000000000000000000"; // Zeroes or appropriate default
            }

            return da;
        }
        // --- Initialization Helpers (COBOL Sections B1, C1, D1, X) ---

        // B1: Initialize D13RecordHolder for Header
        private D13RecordHolder InitializeHeaderHolder()
        {
            var holder = new D13RecordHolder(); // Uses default initializers

            // Apply specific COBOL defaults not covered by C# defaults
            holder.D13_1_RECORD_CODE = CobolConstants.REC_CODE_HOLDING;
            holder.D13_1_CALC_FLAG = "NEW";
            holder.D13_1_B_F_2PCT_HOLDING_DATE_R = "999999";
            holder.D13_1_2PCT_HOLDING_DATE_YTD_R = "999999";
            holder.D13_1_SECURITY_INDICATOR = "0";
            holder.D13_1_MOVEMENT_INDICATOR = 0;
            // Zero values are default for nullable decimals/ints

            return holder;
        }

        // C1/X: Initialize D13RecordHolder for Balance/Acquisition
        private D13RecordHolder InitializeBalanceAcquisitionHolder()
        {
            var holder = new D13RecordHolder(); // Uses default initializers

            // Apply specific COBOL defaults (from X-INITIALISE-COMMON-FIELDS)
            holder.D13_2_PRICE_PCT_INDICATOR = "S";
            holder.D13_2_B_F_DATE_PREV_OP_EVENT_R = "000000"; // From D13-2-B-F-DATE-PREV-OP-EVENT
            holder.D13_2_DATE_PREV_OP_EVENT_YTD = "000000";
            holder.D13_2_ISSUE_SAME_CLASS = "Y";
            holder.D13_2_UNINDEXABLE_FLAG = "0";
            holder.D13_2_CURRENCY_CODE = "   ";

            // Apply specific COBOL defaults (from C1-INITIALISE-BAL-ACQUISITION)
            holder.D13_1_RECORD_CODE = CobolConstants.REC_CODE_BALANCE_ACQUISITION; // D13-2-RECORD-CODE-X
            holder.D13_2_GROUP_TRANSFER_FLAG = "0"; // Default from COBOL line 2783
            holder.D13_2_B_F_NO_OF_COSTS_HELD = 1; // Default from COBOL line 2796
            holder.D13_2_NO_OF_COSTS_HELD_YTD = 1; // Default from COBOL line 2797

            // Initialize first cost element default
            var cost1 = new D13CostElement { D13_2_UNITS_PRESENT = "1" }; // Default from COBOL line 2798
            holder.D13_2_COST_TABLE.Add(cost1);

            return holder;
        }

        // D1/X: Initialize D13RecordHolder for Disposal
        private D13RecordHolder InitializeDisposalHolder()
        {
            var holder = new D13RecordHolder(); // Uses default initializers

            // Apply specific COBOL defaults (from X-INITIALISE-COMMON-FIELDS)
            holder.D13_2_PRICE_PCT_INDICATOR = "S";
            holder.D13_2_B_F_DATE_PREV_OP_EVENT_R = "000000";
            holder.D13_2_DATE_PREV_OP_EVENT_YTD = "000000";
            holder.D13_2_ISSUE_SAME_CLASS = "Y";
            holder.D13_2_UNINDEXABLE_FLAG = "0";
            holder.D13_2_CURRENCY_CODE = "   ";

            // Apply specific COBOL defaults (from D1-INITIALISE-DISPOSAL)
            holder.D13_1_RECORD_CODE = CobolConstants.REC_CODE_DISPOSAL; // D13-2-RECORD-CODE-X
                                                                         // Zero values are default for nullable decimals

            return holder;
        }


        // --- Utility Helpers ---

        private int? ParseIntNullable(string value)
        {
            if (int.TryParse(value?.Trim(), out int result))
            {
                return result;
            }
            return null;
        }

        private void InitializeByteArray(ref byte[] buffer, int size, byte fillByte = (byte)' ')
        {
            if (buffer == null || buffer.Length != size)
            {
                buffer = new byte[size];
            }
            for (int i = 0; i < buffer.Length; i++)
            {
                buffer[i] = fillByte;
            }
        }

        private string GetRecordAreaString(byte[] buffer, int? length = null)
        {
            int count = length ?? buffer.Length;
            // Find the actual end of the string (first null or end of buffer)
            int actualLength = Array.IndexOf(buffer, (byte)0);
            if (actualLength == -1) actualLength = buffer.Length; // No null terminator
            count = Math.Min(count, actualLength);

            return CobolEncoding.GetString(buffer, 0, count);
        }

        private void SetRecordAreaString(ref byte[] buffer, string value)
        {
            InitializeByteArray(ref buffer, buffer.Length); // Clear buffer first
            byte[] stringBytes = CobolEncoding.GetBytes(value ?? string.Empty);
            int lengthToCopy = Math.Min(stringBytes.Length, buffer.Length);
            Array.Copy(stringBytes, 0, buffer, 0, lengthToCopy);
        }

        private string GetRecordCodeFromBuffer(byte[] buffer)
        {
            // Placeholder: Assumes record code is at a specific position
            // Needs actual layout info. Example: assume it's bytes 18-19 for non-header.
            if (buffer == null || buffer.Length < 20) return string.Empty; // Safety check
                                                                           // This position is a guess based on D13-1 layout for header vs D13-2 for others
                                                                           // Need the exact layout definition.
                                                                           // Let's try reading D13_1_RECORD_CODE position (offset 21, length 2)
            if (buffer.Length >= 23)
            {
                string code = CobolEncoding.GetString(buffer, 21, 2);
                if (code == CobolConstants.REC_CODE_HOLDING ||
                    code == CobolConstants.REC_CODE_BALANCE_ACQUISITION ||
                    code == CobolConstants.REC_CODE_DISPOSAL)
                {
                    return code;
                }
            }
            // If not header, try D13_2_RECORD_CODE position (offset 21, length 2)
            // The offset is the same in the provided D13RecordHolder structure,
            // but the actual COBOL layout might differ.
            // If the above didn't work, we can't reliably get it without the layout.
            return string.Empty; // Cannot determine
        }


        // --- PLACEHOLDER: Serialization / Deserialization ---
        // These methods require detailed implementation based on the exact COBOL record layout
        // including field lengths, offsets, PIC types (X, 9, S9), and COMP-3 handling.

        private void SerializeD13HolderToCobolBytes(D13RecordHolder holder, string recordType, ref byte[] buffer)
        {
            // TODO: Implement detailed serialization logic.
            // Example: Write holder.D13_1_CO_AC_LK (PIC X(4)) to buffer[0..3]
            // Example: Write holder.D13_1_SEDOL (PIC X(7)) to buffer[4..10]
            // Example: Convert holder.D13_1_CURRENT_MARKET_PRICE (decimal?) to COMP-3 PIC 9(9)V9(6)
            //          and write to the correct offset.
            // Use CobolEncoding.GetBytes for PIC X fields.
            // Use custom logic for PIC 9, S9, COMP-3.
            // Pad fields with spaces or leading zeros as required by PIC clauses.
            // Ensure the correct total length is written based on recordType (Header, Bal/Acq, Disp).

            // --- VERY Simplified Example (Illustrative ONLY) ---
            InitializeByteArray(ref buffer, CobolConstants.FILE_RECORD_AREA_SIZE);
            using (var stream = new MemoryStream(buffer))
            using (var writer = new BinaryWriter(stream)) // BinaryWriter helps, but COMP-3 needs custom code
            {
                WriteString(writer, holder.D13_1_CO_AC_LK, 4);
                WriteString(writer, holder.D13_1_SEDOL, 7);
                WriteString(writer, holder.D13_1_CONTRACT_NO, 10);
                WriteString(writer, holder.D13_1_RECORD_CODE, 2);
                // ... and so on for ALL fields in the correct order and format ...

                // Example for a COMP-3 field (Needs a real COMP-3 converter)
                // byte[] comp3Price = ConvertDecimalToComp3(holder.D13_1_CURRENT_MARKET_PRICE, 9, 6);
                // writer.Write(comp3Price);

                // ... continue for all fields based on recordType ...
                if (recordType == "B" || recordType == "A" || recordType == "D")
                {
                    // Write D13-2 common fields
                    // ...
                    if (recordType == "B" || recordType == "A")
                    {
                        // Write D13-2 Balance/Acq specific fields

                        // ...
                        // Write Cost Table
                        // ...
                    }
                    if (recordType == "D")
                    {
                        // Write D13-2 Disposal specific fields
                        // ...
                    }
                }
            }
            // --- End Simplified Example ---

            // For now, just put a marker
            // SetRecordAreaString(ref buffer, $"SERIALIZED_{recordType}_{holder.D13_1_SEDOL}");
            // throw new NotImplementedException("SerializeD13HolderToCobolBytes needs full implementation.");
        }

        private D13RecordHolder DeserializeCobolBytesToD13Holder(byte[] buffer)
        {
            // TODO: Implement detailed deserialization logic.
            // Read fields from 'buffer' based on exact offsets and lengths.
            // Use CobolEncoding.GetString for PIC X.
            // Use custom logic for PIC 9, S9 (handle sign), COMP-3 (convert to decimal).
            // Populate a new D13RecordHolder instance.
            // Determine which sections (Header, Bal/Acq Common, Bal/Acq Specific, Disp Specific, Cost Table)
            // are present based on the record code found in the buffer.

            // --- VERY Simplified Example (Illustrative ONLY) ---
            var holder = new D13RecordHolder();
            using (var stream = new System.IO.MemoryStream(buffer))
            using (var reader = new System.IO.BinaryReader(stream))
            {
                holder.D13_1_CO_AC_LK = ReadString(reader, 4);
                holder.D13_1_SEDOL = ReadString(reader, 7);
                holder.D13_1_CONTRACT_NO = ReadString(reader, 10);
                holder.D13_1_RECORD_CODE = ReadString(reader, 2);
                // ... and so on for ALL fields ...

                // Example for a COMP-3 field (Needs a real COMP-3 converter)
                // int comp3PriceLength = CalculateComp3Bytes(9, 6); // Function to get byte length
                // byte[] comp3PriceBytes = reader.ReadBytes(comp3PriceLength);
                // holder.D13_1_CURRENT_MARKET_PRICE = ConvertComp3ToDecimal(comp3PriceBytes, 9, 6);

                // ... conditionally read D13-2 fields based on holder.D13_1_RECORD_CODE ...
                if (holder.D13_1_RECORD_CODE == CobolConstants.REC_CODE_BALANCE_ACQUISITION ||
                    holder.D13_1_RECORD_CODE == CobolConstants.REC_CODE_DISPOSAL)
                {
                    // Read D13-2 common fields
                    // ...
                    if (holder.D13_1_RECORD_CODE == CobolConstants.REC_CODE_BALANCE_ACQUISITION)
                    {
                        // Read D13-2 Balance/Acq specific fields
                        // ...
                        // Read Cost Table based on D13_2_NO_OF_COSTS_HELD_YTD
                        // ...
                    }
                    else // Disposal
                    {
                        // Read D13-2 Disposal specific fields
                        // ...
                    }
                }
            }
            return holder;
            // --- End Simplified Example ---

            // For now, return empty
            // return new D13RecordHolder();
            // throw new NotImplementedException("DeserializeCobolBytesToD13Holder needs full implementation.");
        }

        // Placeholder: Serialize Master*DARecord to string format expected by C# DAO
        private string SerializeMasterRecordToString(object daRecord)
        {
            // TODO: Implement logic to convert MasterHoldingDARecord, MasterBalanceDARecord, etc.,
            // into the flat string format presumably used by the C# DAO layer methods
            // (CreateNewHolding, UpdateBalance, etc.). This might involve concatenating
            // fields with fixed padding or specific delimiters, matching how MapFromString works.

            // Example for MasterHoldingDARecord (needs padding/formatting)
            if (daRecord is MasterHoldingDARecord h)
            {
                return $"{h.MHDA_RecordType}{h.MHDA_HoldingID}{h.MHDA_FundID}{h.MHDA_FundCode}..."; // Add all fields formatted correctly
            }
            // Add similar blocks for other Master*DARecord types

            // throw new NotImplementedException($"SerializeMasterRecordToString needs implementation for {daRecord?.GetType().Name}");
            return daRecord?.ToString() ?? string.Empty; // Basic placeholder
        }

        // --- Placeholders for D37 Serialization/Deserialization ---

        private void SerializeD37HeaderToCobolBytes(D37HeaderRecord header, ref byte[] buffer)
        {
            // TODO: Implement serialization based on D37-RECORD layout (Lines 1586-1608)
            InitializeByteArray(ref buffer, CobolConstants.FILE_RECORD_AREA_SIZE); // Use appropriate size
                                                                                   // Example: Write D37_GLOBAL_COMP_NAME (X(50)), D37_MASTER_FILE_YEAR (XX), etc.
                                                                                   // SetRecordAreaString(ref buffer, $"SERIALIZED_D37H_{header.D37_GLOBAL_COMP_NAME}"); // Placeholder
            throw new NotImplementedException("SerializeD37HeaderToCobolBytes needs implementation.");
        }

        private void SerializeD37DetailToCobolBytes(D37DetailRecord detail, ref byte[] buffer)
        {
            // TODO: Implement serialization based on D37-RECORD-FORMAT-2 layout (Lines 1609-1645)
            InitializeByteArray(ref buffer, CobolConstants.FILE_RECORD_AREA_SIZE); // Use appropriate size
                                                                                   // Example: Write D37_FUND_CODE (X(4)), D37_NAME (X(50)), etc.
                                                                                   // SetRecordAreaString(ref buffer, $"SERIALIZED_D37D_{detail.D37_FUND_CODE}"); // Placeholder
            throw new NotImplementedException("SerializeD37DetailToCobolBytes needs implementation.");
        }

        // --- Placeholders for parsing DAO strings ---
        // These might be simple if the DAO layer returns objects directly,
        // or complex if it returns formatted strings.

        private UserInfoRecord ParseUserInfoString(string sRecordArea)
        {
            // TODO: Implement parsing if DAO returns a string
            // Example: Split string or use fixed positions
            // throw new NotImplementedException("ParseUserInfoString needs implementation.");
            return new UserInfoRecord { D37_GLOBAL_COMP_NAME = "Parsed User Info Name" }; // Placeholder
        }

        private UserFundRecord ParseUserFundString(string sRecordArea)
        {
            // TODO: Implement parsing if DAO returns a string
            // throw new NotImplementedException("ParseUserFundString needs implementation.");
            return new UserFundRecord { D37_FUND_CODE = "ParsedFund", D37_NAME = "Parsed Fund Name" }; // Placeholder
        }

        // --- Placeholders for mapping D37/User records ---

        private D37HeaderRecord MapUserInfoToD37Header(UserInfoRecord userInfo)
        {
            // TODO: Implement mapping
            return new D37HeaderRecord
            {
                D37_GLOBAL_COMP_NAME = userInfo.D37_GLOBAL_COMP_NAME,
                D37_MASTER_FILE_YEAR = userInfo.D37_MASTER_FILE_YEAR,
                D37_PRICING_DATE = userInfo.D37_PRICING_DATE
                // Map other fields if present
            };
        }

        private D37DetailRecord MapUserFundToD37Detail(UserFundRecord userFund)
        {
            // TODO: Implement mapping based on COBOL lines 2896-2907
            var detail = new D37DetailRecord
            {
                D37_FUND_CODE = userFund.D37_FUND_CODE,
                D37_NAME = userFund.D37_NAME, // Note: COBOL uses X(30) from DAO, D37 uses X(50)
                D37_FUND_TYPE = userFund.D37_FUND_TYPE,
                D37_PERIOD_START_DATE = userFund.D37_PERIOD_START_DATE,
                D37_PERIOD_END_DATE = userFund.D37_PERIOD_END_DATE,
                D37_ELECTION_1965_FI = userFund.D37_ELECTION_1965_FI,
                D37_ELECTION_1965_ORD = userFund.D37_ELECTION_1965_ORD,
                D37_ELECTION_1982 = userFund.D37_ELECTION_1982,
                // D37_CALC_REQUEST_FLAG handled below
                D37_INVERSE_FUND_CODE = string.Empty, // Not in UserFundRecord?
                D37_DEEMED_DISPOSAL_FLAG = userFund.D37_DEEMED_DISPOSAL_FLAG,
                D37_CALENDAR_CODE = userFund.D37_CALENDAR_CODE,
                D37_PRICE_TYPE_ID = userFund.D37_PRICE_TYPE_ID,
                D37_USE_EARLIER_PRICE = userFund.D37_USE_EARLIER_PRICE,
                D37_FUND_ID = userFund.D37_FUND_ID,
                D37_OLAB_FUND = userFund.D37_OLAB_FUND,
                D37_LIFE_SUMMARY_FUND = userFund.D37_LIFE_SUMMARY_FUND,
                D37_USE_LOSSES_FLAG = userFund.D37_USE_LOSSES_FLAG,
                D37_FA_2012_RE_INDEXATION = userFund.D37_FA_2012_RE_INDEXATION,
                D37_OLD_SUMMARY_FUND_CODE = userFund.D37_OLD_SUMMARY_FUND_CODE,
                D37_SUMMARY_FUND_CODE = userFund.D37_SUMMARY_FUND_CODE,
                D37_POST_2012_SUMMARY_FUND_CODE = userFund.D37_POST_2012_SUMMARY_FUND_CODE
            };

            // Map D37_CALC_REQUEST_FLAG based on D37_SMALL_DISTRIBUTION (COBOL lines 2901-2905)
            detail.D37_CALC_REQUEST_FLAG = (userFund.D37_SMALL_DISTRIBUTION == "0") ? "2" : "1";

            return detail;
        }


        // --- Helper for reading/writing fixed-length strings in byte arrays ---
        private void WriteString(BinaryWriter writer, string value, int length)
        {
            string paddedValue = (value ?? string.Empty).PadRight(length);
            byte[] bytes = CobolEncoding.GetBytes(paddedValue.Substring(0, length));
            writer.Write(bytes);
        }

        private string ReadString(BinaryReader reader, int length)
        {
            byte[] bytes = reader.ReadBytes(length);
            return CobolEncoding.GetString(bytes).TrimEnd();
        }

        // --- TODO: Add COMP-3 Conversion Helpers ---
        // byte[] ConvertDecimalToComp3(decimal? value, int integerDigits, int decimalDigits) { ... }
        // decimal? ConvertComp3ToDecimal(byte[] comp3Bytes, int integerDigits, int decimalDigits) { ... }
        // int CalculateComp3Bytes(int integerDigits, int decimalDigits) { ... }

    }
}
