using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// <Section> Class for Fvar
public class Fvar
{
public Fvar() {}

// Fields in the class


// [DEBUG] Field: OffshoreRecord, is_external=, is_static_class=False, static_prefix=
private OffshoreRecord _OffshoreRecord = new OffshoreRecord();




// [DEBUG] Field: OffshoreRecordDd, is_external=, is_static_class=False, static_prefix=
private OffshoreRecordDd _OffshoreRecordDd = new OffshoreRecordDd();




// [DEBUG] Field: IlostRecord, is_external=, is_static_class=False, static_prefix=
private IlostRecord _IlostRecord = new IlostRecord();




// [DEBUG] Field: IlosuRecord, is_external=, is_static_class=False, static_prefix=
private IlosuRecord _IlosuRecord = new IlosuRecord();




// Getter and Setter methods

// Standard Getter
public OffshoreRecord GetOffshoreRecord()
{
    return _OffshoreRecord;
}

// Standard Setter
public void SetOffshoreRecord(OffshoreRecord value)
{
    _OffshoreRecord = value;
}

// Get<>AsString()
public string GetOffshoreRecordAsString()
{
    return _OffshoreRecord != null ? _OffshoreRecord.GetOffshoreRecordAsString() : "";
}

// Set<>AsString()
public void SetOffshoreRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_OffshoreRecord == null)
    {
        _OffshoreRecord = new OffshoreRecord();
    }
    _OffshoreRecord.SetOffshoreRecordAsString(value);
}

// Standard Getter
public OffshoreRecordDd GetOffshoreRecordDd()
{
    return _OffshoreRecordDd;
}

// Standard Setter
public void SetOffshoreRecordDd(OffshoreRecordDd value)
{
    _OffshoreRecordDd = value;
}

// Get<>AsString()
public string GetOffshoreRecordDdAsString()
{
    return _OffshoreRecordDd != null ? _OffshoreRecordDd.GetOffshoreRecordDdAsString() : "";
}

// Set<>AsString()
public void SetOffshoreRecordDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_OffshoreRecordDd == null)
    {
        _OffshoreRecordDd = new OffshoreRecordDd();
    }
    _OffshoreRecordDd.SetOffshoreRecordDdAsString(value);
}

// Standard Getter
public IlostRecord GetIlostRecord()
{
    return _IlostRecord;
}

// Standard Setter
public void SetIlostRecord(IlostRecord value)
{
    _IlostRecord = value;
}

// Get<>AsString()
public string GetIlostRecordAsString()
{
    return _IlostRecord != null ? _IlostRecord.GetIlostRecordAsString() : "";
}

// Set<>AsString()
public void SetIlostRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_IlostRecord == null)
    {
        _IlostRecord = new IlostRecord();
    }
    _IlostRecord.SetIlostRecordAsString(value);
}

// Standard Getter
public IlosuRecord GetIlosuRecord()
{
    return _IlosuRecord;
}

// Standard Setter
public void SetIlosuRecord(IlosuRecord value)
{
    _IlosuRecord = value;
}

// Get<>AsString()
public string GetIlosuRecordAsString()
{
    return _IlosuRecord != null ? _IlosuRecord.GetIlosuRecordAsString() : "";
}

// Set<>AsString()
public void SetIlosuRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_IlosuRecord == null)
    {
        _IlosuRecord = new IlosuRecord();
    }
    _IlosuRecord.SetIlosuRecordAsString(value);
}


}}