using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D9Record Data Structure

public class D9Record
{
    private static int _size = 104;
    // [DEBUG] Class: D9Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D9Key, is_external=, is_static_class=False, static_prefix=
    private D9Key _D9Key = new D9Key();
    
    
    
    
    // [DEBUG] Field: D9Password, is_external=, is_static_class=False, static_prefix=
    private string _D9Password ="";
    
    
    
    
    // [DEBUG] Field: D9UserName, is_external=, is_static_class=False, static_prefix=
    private string _D9UserName ="";
    
    
    
    
    // [DEBUG] Field: D9LoggedOnFlag, is_external=, is_static_class=False, static_prefix=
    private string _D9LoggedOnFlag ="";
    
    
    
    
    // [DEBUG] Field: D9UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D9UserNo =0;
    
    
    
    
    // [DEBUG] Field: D9DefaultPrinter, is_external=, is_static_class=False, static_prefix=
    private string _D9DefaultPrinter ="";
    
    
    
    
    // [DEBUG] Field: D9DefaultFund, is_external=, is_static_class=False, static_prefix=
    private string _D9DefaultFund ="";
    
    
    
    
    // [DEBUG] Field: D9DefaultFundName, is_external=, is_static_class=False, static_prefix=
    private string _D9DefaultFundName ="";
    
    
    
    
    // [DEBUG] Field: D9InverseKey, is_external=, is_static_class=False, static_prefix=
    private D9InverseKey _D9InverseKey = new D9InverseKey();
    
    
    
    
    // [DEBUG] Field: D9UpdateCount, is_external=, is_static_class=False, static_prefix=
    private int _D9UpdateCount =0;
    
    
    
    
    
    // Serialization methods
    public string GetD9RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D9Key.GetD9KeyAsString());
        result.Append(_D9Password.PadRight(8));
        result.Append(_D9UserName.PadRight(30));
        result.Append(_D9LoggedOnFlag.PadRight(1));
        result.Append(_D9UserNo.ToString().PadLeft(4, '0'));
        result.Append(_D9DefaultPrinter.PadRight(7));
        result.Append(_D9DefaultFund.PadRight(4));
        result.Append(_D9DefaultFundName.PadRight(30));
        result.Append(_D9InverseKey.GetD9InverseKeyAsString());
        result.Append(_D9UpdateCount.ToString().PadLeft(4, '0'));
        
        return result.ToString();
    }
    
    public void SetD9RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            _D9Key.SetD9KeyAsString(data.Substring(offset, 8));
        }
        else
        {
            _D9Key.SetD9KeyAsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetD9Password(extracted);
        }
        offset += 8;
        if (offset + 30 <= data.Length)
        {
            string extracted = data.Substring(offset, 30).Trim();
            SetD9UserName(extracted);
        }
        offset += 30;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD9LoggedOnFlag(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD9UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetD9DefaultPrinter(extracted);
        }
        offset += 7;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD9DefaultFund(extracted);
        }
        offset += 4;
        if (offset + 30 <= data.Length)
        {
            string extracted = data.Substring(offset, 30).Trim();
            SetD9DefaultFundName(extracted);
        }
        offset += 30;
        if (offset + 8 <= data.Length)
        {
            _D9InverseKey.SetD9InverseKeyAsString(data.Substring(offset, 8));
        }
        else
        {
            _D9InverseKey.SetD9InverseKeyAsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD9UpdateCount(parsedInt);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD9RecordAsString();
    }
    // Set<>String Override function
    public void SetD9Record(string value)
    {
        SetD9RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D9Key GetD9Key()
    {
        return _D9Key;
    }
    
    // Standard Setter
    public void SetD9Key(D9Key value)
    {
        _D9Key = value;
    }
    
    // Get<>AsString()
    public string GetD9KeyAsString()
    {
        return _D9Key != null ? _D9Key.GetD9KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD9KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D9Key == null)
        {
            _D9Key = new D9Key();
        }
        _D9Key.SetD9KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD9Password()
    {
        return _D9Password;
    }
    
    // Standard Setter
    public void SetD9Password(string value)
    {
        _D9Password = value;
    }
    
    // Get<>AsString()
    public string GetD9PasswordAsString()
    {
        return _D9Password.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetD9PasswordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D9Password = value;
    }
    
    // Standard Getter
    public string GetD9UserName()
    {
        return _D9UserName;
    }
    
    // Standard Setter
    public void SetD9UserName(string value)
    {
        _D9UserName = value;
    }
    
    // Get<>AsString()
    public string GetD9UserNameAsString()
    {
        return _D9UserName.PadRight(30);
    }
    
    // Set<>AsString()
    public void SetD9UserNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D9UserName = value;
    }
    
    // Standard Getter
    public string GetD9LoggedOnFlag()
    {
        return _D9LoggedOnFlag;
    }
    
    // Standard Setter
    public void SetD9LoggedOnFlag(string value)
    {
        _D9LoggedOnFlag = value;
    }
    
    // Get<>AsString()
    public string GetD9LoggedOnFlagAsString()
    {
        return _D9LoggedOnFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD9LoggedOnFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D9LoggedOnFlag = value;
    }
    
    // Standard Getter
    public int GetD9UserNo()
    {
        return _D9UserNo;
    }
    
    // Standard Setter
    public void SetD9UserNo(int value)
    {
        _D9UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD9UserNoAsString()
    {
        return _D9UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD9UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D9UserNo = parsed;
    }
    
    // Standard Getter
    public string GetD9DefaultPrinter()
    {
        return _D9DefaultPrinter;
    }
    
    // Standard Setter
    public void SetD9DefaultPrinter(string value)
    {
        _D9DefaultPrinter = value;
    }
    
    // Get<>AsString()
    public string GetD9DefaultPrinterAsString()
    {
        return _D9DefaultPrinter.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetD9DefaultPrinterAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D9DefaultPrinter = value;
    }
    
    // Standard Getter
    public string GetD9DefaultFund()
    {
        return _D9DefaultFund;
    }
    
    // Standard Setter
    public void SetD9DefaultFund(string value)
    {
        _D9DefaultFund = value;
    }
    
    // Get<>AsString()
    public string GetD9DefaultFundAsString()
    {
        return _D9DefaultFund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD9DefaultFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D9DefaultFund = value;
    }
    
    // Standard Getter
    public string GetD9DefaultFundName()
    {
        return _D9DefaultFundName;
    }
    
    // Standard Setter
    public void SetD9DefaultFundName(string value)
    {
        _D9DefaultFundName = value;
    }
    
    // Get<>AsString()
    public string GetD9DefaultFundNameAsString()
    {
        return _D9DefaultFundName.PadRight(30);
    }
    
    // Set<>AsString()
    public void SetD9DefaultFundNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D9DefaultFundName = value;
    }
    
    // Standard Getter
    public D9InverseKey GetD9InverseKey()
    {
        return _D9InverseKey;
    }
    
    // Standard Setter
    public void SetD9InverseKey(D9InverseKey value)
    {
        _D9InverseKey = value;
    }
    
    // Get<>AsString()
    public string GetD9InverseKeyAsString()
    {
        return _D9InverseKey != null ? _D9InverseKey.GetD9InverseKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD9InverseKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D9InverseKey == null)
        {
            _D9InverseKey = new D9InverseKey();
        }
        _D9InverseKey.SetD9InverseKeyAsString(value);
    }
    
    // Standard Getter
    public int GetD9UpdateCount()
    {
        return _D9UpdateCount;
    }
    
    // Standard Setter
    public void SetD9UpdateCount(int value)
    {
        _D9UpdateCount = value;
    }
    
    // Get<>AsString()
    public string GetD9UpdateCountAsString()
    {
        return _D9UpdateCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD9UpdateCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D9UpdateCount = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD9Key(string value)
    {
        _D9Key.SetD9KeyAsString(value);
    }
    // Nested Class: D9Key
    public class D9Key
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D9Userid, is_external=, is_static_class=False, static_prefix=
        private string _D9Userid ="";
        
        
        
        
    public D9Key() {}
    
    public D9Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD9Userid(data.Substring(offset, 8).Trim());
        offset += 8;
        
    }
    
    // Serialization methods
    public string GetD9KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D9Userid.PadRight(8));
        
        return result.ToString();
    }
    
    public void SetD9KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetD9Userid(extracted);
        }
        offset += 8;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD9Userid()
    {
        return _D9Userid;
    }
    
    // Standard Setter
    public void SetD9Userid(string value)
    {
        _D9Userid = value;
    }
    
    // Get<>AsString()
    public string GetD9UseridAsString()
    {
        return _D9Userid.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetD9UseridAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D9Userid = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetD9InverseKey(string value)
{
    _D9InverseKey.SetD9InverseKeyAsString(value);
}
// Nested Class: D9InverseKey
public class D9InverseKey
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D9InverseUser, is_external=, is_static_class=False, static_prefix=
    private string _D9InverseUser ="";
    
    
    
    
public D9InverseKey() {}

public D9InverseKey(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD9InverseUser(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetD9InverseKeyAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D9InverseUser.PadRight(8));
    
    return result.ToString();
}

public void SetD9InverseKeyAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD9InverseUser(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetD9InverseUser()
{
    return _D9InverseUser;
}

// Standard Setter
public void SetD9InverseUser(string value)
{
    _D9InverseUser = value;
}

// Get<>AsString()
public string GetD9InverseUserAsString()
{
    return _D9InverseUser.PadRight(8);
}

// Set<>AsString()
public void SetD9InverseUserAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D9InverseUser = value;
}



public static int GetSize()
{
    return _size;
}

}

}}