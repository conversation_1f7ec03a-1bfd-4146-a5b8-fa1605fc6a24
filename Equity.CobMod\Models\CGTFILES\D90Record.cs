using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D90Record Data Structure

public class D90Record
{
    private static int _size = 151;
    // [DEBUG] Class: D90Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D90PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D90PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D90ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D90ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD90RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D90PrintControl.PadRight(1));
        result.Append(_D90ReportLine.PadRight(150));
        
        return result.ToString();
    }
    
    public void SetD90RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD90PrintControl(extracted);
        }
        offset += 1;
        if (offset + 150 <= data.Length)
        {
            string extracted = data.Substring(offset, 150).Trim();
            SetD90ReportLine(extracted);
        }
        offset += 150;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD90RecordAsString();
    }
    // Set<>String Override function
    public void SetD90Record(string value)
    {
        SetD90RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD90PrintControl()
    {
        return _D90PrintControl;
    }
    
    // Standard Setter
    public void SetD90PrintControl(string value)
    {
        _D90PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD90PrintControlAsString()
    {
        return _D90PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD90PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D90PrintControl = value;
    }
    
    // Standard Getter
    public string GetD90ReportLine()
    {
        return _D90ReportLine;
    }
    
    // Standard Setter
    public void SetD90ReportLine(string value)
    {
        _D90ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD90ReportLineAsString()
    {
        return _D90ReportLine.PadRight(150);
    }
    
    // Set<>AsString()
    public void SetD90ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D90ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}