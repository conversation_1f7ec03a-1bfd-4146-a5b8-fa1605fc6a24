using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WsLongDates Data Structure

public class WsLongDates
{
    private static int _size = 24;
    // [DEBUG] Class: WsLongDates, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsWtieBargainDateWiIe, is_external=, is_static_class=False, static_prefix=
    private string _WsWtieBargainDateWiIe ="";
    
    
    
    
    // [DEBUG] Field: WsWttBargainDateWiTd, is_external=, is_static_class=False, static_prefix=
    private string _WsWttBargainDateWiTd ="";
    
    
    
    
    // [DEBUG] Field: WsWttBargainDateWiTa, is_external=, is_static_class=False, static_prefix=
    private string _WsWttBargainDateWiTa ="";
    
    
    
    
    
    // Serialization methods
    public string GetWsLongDatesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsWtieBargainDateWiIe.PadRight(8));
        result.Append(_WsWttBargainDateWiTd.PadRight(8));
        result.Append(_WsWttBargainDateWiTa.PadRight(8));
        
        return result.ToString();
    }
    
    public void SetWsLongDatesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWsWtieBargainDateWiIe(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWsWttBargainDateWiTd(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWsWttBargainDateWiTa(extracted);
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsLongDatesAsString();
    }
    // Set<>String Override function
    public void SetWsLongDates(string value)
    {
        SetWsLongDatesAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsWtieBargainDateWiIe()
    {
        return _WsWtieBargainDateWiIe;
    }
    
    // Standard Setter
    public void SetWsWtieBargainDateWiIe(string value)
    {
        _WsWtieBargainDateWiIe = value;
    }
    
    // Get<>AsString()
    public string GetWsWtieBargainDateWiIeAsString()
    {
        return _WsWtieBargainDateWiIe.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWsWtieBargainDateWiIeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsWtieBargainDateWiIe = value;
    }
    
    // Standard Getter
    public string GetWsWttBargainDateWiTd()
    {
        return _WsWttBargainDateWiTd;
    }
    
    // Standard Setter
    public void SetWsWttBargainDateWiTd(string value)
    {
        _WsWttBargainDateWiTd = value;
    }
    
    // Get<>AsString()
    public string GetWsWttBargainDateWiTdAsString()
    {
        return _WsWttBargainDateWiTd.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWsWttBargainDateWiTdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsWttBargainDateWiTd = value;
    }
    
    // Standard Getter
    public string GetWsWttBargainDateWiTa()
    {
        return _WsWttBargainDateWiTa;
    }
    
    // Standard Setter
    public void SetWsWttBargainDateWiTa(string value)
    {
        _WsWttBargainDateWiTa = value;
    }
    
    // Get<>AsString()
    public string GetWsWttBargainDateWiTaAsString()
    {
        return _WsWttBargainDateWiTa.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWsWttBargainDateWiTaAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsWttBargainDateWiTa = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}