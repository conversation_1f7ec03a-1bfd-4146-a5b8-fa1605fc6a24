using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D85File Data Structure

public class D85File
{
    private static int _size = 12;
    // [DEBUG] Class: D85File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler260, is_external=, is_static_class=False, static_prefix=
    private string _Filler260 ="$";
    
    
    
    
    // [DEBUG] Field: D85UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D85UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler261, is_external=, is_static_class=False, static_prefix=
    private string _Filler261 ="RK";
    
    
    
    
    // [DEBUG] Field: D85ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D85ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler262, is_external=, is_static_class=False, static_prefix=
    private string _Filler262 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD85FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler260.PadRight(1));
        result.Append(_D85UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler261.PadRight(2));
        result.Append(_D85ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler262.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD85FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller260(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD85UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller261(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD85ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller262(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD85FileAsString();
    }
    // Set<>String Override function
    public void SetD85File(string value)
    {
        SetD85FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller260()
    {
        return _Filler260;
    }
    
    // Standard Setter
    public void SetFiller260(string value)
    {
        _Filler260 = value;
    }
    
    // Get<>AsString()
    public string GetFiller260AsString()
    {
        return _Filler260.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller260AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler260 = value;
    }
    
    // Standard Getter
    public int GetD85UserNo()
    {
        return _D85UserNo;
    }
    
    // Standard Setter
    public void SetD85UserNo(int value)
    {
        _D85UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD85UserNoAsString()
    {
        return _D85UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD85UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D85UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller261()
    {
        return _Filler261;
    }
    
    // Standard Setter
    public void SetFiller261(string value)
    {
        _Filler261 = value;
    }
    
    // Get<>AsString()
    public string GetFiller261AsString()
    {
        return _Filler261.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller261AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler261 = value;
    }
    
    // Standard Getter
    public int GetD85ReportNo()
    {
        return _D85ReportNo;
    }
    
    // Standard Setter
    public void SetD85ReportNo(int value)
    {
        _D85ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD85ReportNoAsString()
    {
        return _D85ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD85ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D85ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller262()
    {
        return _Filler262;
    }
    
    // Standard Setter
    public void SetFiller262(string value)
    {
        _Filler262 = value;
    }
    
    // Get<>AsString()
    public string GetFiller262AsString()
    {
        return _Filler262.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller262AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler262 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}