using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtIndexEligibleTranches Data Structure

public class WtIndexEligibleTranches
{
    private static int _size = 130;
    // [DEBUG] Class: WtIndexEligibleTranches, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler448, is_external=, is_static_class=False, static_prefix=
    private string _Filler448 ="INDEX-ELIGIBLE TRANCHES=====";
    
    
    
    
    // [DEBUG] Field: WtieMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtieMaxTableSize =2000;
    
    
    
    
    // [DEBUG] Field: WtieOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtieOccurs =2000;
    
    
    
    
    // [DEBUG] Field: WtieTable, is_external=, is_static_class=False, static_prefix=
    private WtieTable _WtieTable = new WtieTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtIndexEligibleTranchesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler448.PadRight(32));
        result.Append(_WtieMaxTableSize.ToString().PadLeft(4, '0'));
        result.Append(_WtieOccurs.ToString().PadLeft(4, '0'));
        result.Append(_WtieTable.GetWtieTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtIndexEligibleTranchesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 32 <= data.Length)
        {
            string extracted = data.Substring(offset, 32).Trim();
            SetFiller448(extracted);
        }
        offset += 32;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtieMaxTableSize(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtieOccurs(parsedInt);
        }
        offset += 4;
        if (offset + 90 <= data.Length)
        {
            _WtieTable.SetWtieTableAsString(data.Substring(offset, 90));
        }
        else
        {
            _WtieTable.SetWtieTableAsString(data.Substring(offset));
        }
        offset += 90;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtIndexEligibleTranchesAsString();
    }
    // Set<>String Override function
    public void SetWtIndexEligibleTranches(string value)
    {
        SetWtIndexEligibleTranchesAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller448()
    {
        return _Filler448;
    }
    
    // Standard Setter
    public void SetFiller448(string value)
    {
        _Filler448 = value;
    }
    
    // Get<>AsString()
    public string GetFiller448AsString()
    {
        return _Filler448.PadRight(32);
    }
    
    // Set<>AsString()
    public void SetFiller448AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler448 = value;
    }
    
    // Standard Getter
    public int GetWtieMaxTableSize()
    {
        return _WtieMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtieMaxTableSize(int value)
    {
        _WtieMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtieMaxTableSizeAsString()
    {
        return _WtieMaxTableSize.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtieMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtieMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtieOccurs()
    {
        return _WtieOccurs;
    }
    
    // Standard Setter
    public void SetWtieOccurs(int value)
    {
        _WtieOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtieOccursAsString()
    {
        return _WtieOccurs.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtieOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtieOccurs = parsed;
    }
    
    // Standard Getter
    public WtieTable GetWtieTable()
    {
        return _WtieTable;
    }
    
    // Standard Setter
    public void SetWtieTable(WtieTable value)
    {
        _WtieTable = value;
    }
    
    // Get<>AsString()
    public string GetWtieTableAsString()
    {
        return _WtieTable != null ? _WtieTable.GetWtieTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtieTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtieTable == null)
        {
            _WtieTable = new WtieTable();
        }
        _WtieTable.SetWtieTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtieTable(string value)
    {
        _WtieTable.SetWtieTableAsString(value);
    }
    // Nested Class: WtieTable
    public class WtieTable
    {
        private static int _size = 90;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtieElement, is_external=, is_static_class=False, static_prefix=
        private WtieTable.WtieElement[] _WtieElement = new WtieTable.WtieElement[2000];
        
        public void InitializeWtieElementArray()
        {
            for (int i = 0; i < 2000; i++)
            {
                _WtieElement[i] = new WtieTable.WtieElement();
            }
        }
        
        
        
    public WtieTable() {}
    
    public WtieTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWtieElementArray();
        for (int i = 0; i < 2000; i++)
        {
            _WtieElement[i].SetWtieElementAsString(data.Substring(offset, 90));
            offset += 90;
        }
        
    }
    
    // Serialization methods
    public string GetWtieTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 2000; i++)
        {
            result.Append(_WtieElement[i].GetWtieElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtieTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 2000; i++)
        {
            if (offset + 90 > data.Length) break;
            string val = data.Substring(offset, 90);
            
            _WtieElement[i].SetWtieElementAsString(val);
            offset += 90;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtieElement
    public WtieElement GetWtieElementAt(int index)
    {
        return _WtieElement[index];
    }
    
    public void SetWtieElementAt(int index, WtieElement value)
    {
        _WtieElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtieElement GetWtieElement()
    {
        return _WtieElement != null && _WtieElement.Length > 0
        ? _WtieElement[0]
        : new WtieElement();
    }
    
    public void SetWtieElement(WtieElement value)
    {
        if (_WtieElement == null || _WtieElement.Length == 0)
        _WtieElement = new WtieElement[1];
        _WtieElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtieElement
    public class WtieElement
    {
        private static int _size = 90;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtieIndex, is_external=, is_static_class=False, static_prefix=
        private string _WtieIndex ="";
        
        
        
        
        // [DEBUG] Field: WtieUnitsReacquired, is_external=, is_static_class=False, static_prefix=
        private decimal _WtieUnitsReacquired =0;
        
        
        
        
        // [DEBUG] Field: WtieBargainDate, is_external=, is_static_class=False, static_prefix=
        private string _WtieBargainDate ="";
        
        
        
        
        // [DEBUG] Field: WtieFlag, is_external=, is_static_class=False, static_prefix=
        private string _WtieFlag ="";
        
        
        // 88-level condition checks for WtieFlag
        public bool IsWtieMatched()
        {
            if (this._WtieFlag == "'M'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WtieTrancheFlag, is_external=, is_static_class=False, static_prefix=
        private string _WtieTrancheFlag ="";
        
        
        // 88-level condition checks for WtieTrancheFlag
        public bool IsWtieGroupTransfer()
        {
            if (this._WtieTrancheFlag == "'1'") return true;
            return false;
        }
        public bool IsWtiePp()
        {
            if (this._WtieTrancheFlag == "'P'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WtieDdUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WtieDdUnits =0;
        
        
        
        
        // [DEBUG] Field: WtieOriginalCost, is_external=, is_static_class=False, static_prefix=
        private decimal _WtieOriginalCost =0;
        
        
        
        
        // [DEBUG] Field: WtieDdValue, is_external=, is_static_class=False, static_prefix=
        private decimal _WtieDdValue =0;
        
        
        
        
        // [DEBUG] Field: WtieNdlUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WtieNdlUnits =0;
        
        
        
        
        // [DEBUG] Field: WtieNdlCost, is_external=, is_static_class=False, static_prefix=
        private decimal _WtieNdlCost =0;
        
        
        
        
    public WtieElement() {}
    
    public WtieElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtieIndex(data.Substring(offset, 4).Trim());
        offset += 4;
        SetWtieUnitsReacquired(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetWtieBargainDate(data.Substring(offset, 6).Trim());
        offset += 6;
        SetWtieFlag(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWtieTrancheFlag(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWtieDdUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetWtieOriginalCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetWtieDdValue(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetWtieNdlUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetWtieNdlCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        
    }
    
    // Serialization methods
    public string GetWtieElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtieIndex.PadRight(4));
        result.Append(_WtieUnitsReacquired.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtieBargainDate.PadRight(6));
        result.Append(_WtieFlag.PadRight(1));
        result.Append(_WtieTrancheFlag.PadRight(1));
        result.Append(_WtieDdUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtieOriginalCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtieDdValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtieNdlUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtieNdlCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        
        return result.ToString();
    }
    
    public void SetWtieElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtieIndex(extracted);
        }
        offset += 4;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtieUnitsReacquired(parsedDec);
        }
        offset += 13;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetWtieBargainDate(extracted);
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtieFlag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtieTrancheFlag(extracted);
        }
        offset += 1;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtieDdUnits(parsedDec);
        }
        offset += 13;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtieOriginalCost(parsedDec);
        }
        offset += 13;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtieDdValue(parsedDec);
        }
        offset += 13;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtieNdlUnits(parsedDec);
        }
        offset += 13;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtieNdlCost(parsedDec);
        }
        offset += 13;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtieIndex()
    {
        return _WtieIndex;
    }
    
    // Standard Setter
    public void SetWtieIndex(string value)
    {
        _WtieIndex = value;
    }
    
    // Get<>AsString()
    public string GetWtieIndexAsString()
    {
        return _WtieIndex.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtieIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtieIndex = value;
    }
    
    // Standard Getter
    public decimal GetWtieUnitsReacquired()
    {
        return _WtieUnitsReacquired;
    }
    
    // Standard Setter
    public void SetWtieUnitsReacquired(decimal value)
    {
        _WtieUnitsReacquired = value;
    }
    
    // Get<>AsString()
    public string GetWtieUnitsReacquiredAsString()
    {
        return _WtieUnitsReacquired.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtieUnitsReacquiredAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtieUnitsReacquired = parsed;
    }
    
    // Standard Getter
    public string GetWtieBargainDate()
    {
        return _WtieBargainDate;
    }
    
    // Standard Setter
    public void SetWtieBargainDate(string value)
    {
        _WtieBargainDate = value;
    }
    
    // Get<>AsString()
    public string GetWtieBargainDateAsString()
    {
        return _WtieBargainDate.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetWtieBargainDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtieBargainDate = value;
    }
    
    // Standard Getter
    public string GetWtieFlag()
    {
        return _WtieFlag;
    }
    
    // Standard Setter
    public void SetWtieFlag(string value)
    {
        _WtieFlag = value;
    }
    
    // Get<>AsString()
    public string GetWtieFlagAsString()
    {
        return _WtieFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtieFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtieFlag = value;
    }
    
    // Standard Getter
    public string GetWtieTrancheFlag()
    {
        return _WtieTrancheFlag;
    }
    
    // Standard Setter
    public void SetWtieTrancheFlag(string value)
    {
        _WtieTrancheFlag = value;
    }
    
    // Get<>AsString()
    public string GetWtieTrancheFlagAsString()
    {
        return _WtieTrancheFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtieTrancheFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtieTrancheFlag = value;
    }
    
    // Standard Getter
    public decimal GetWtieDdUnits()
    {
        return _WtieDdUnits;
    }
    
    // Standard Setter
    public void SetWtieDdUnits(decimal value)
    {
        _WtieDdUnits = value;
    }
    
    // Get<>AsString()
    public string GetWtieDdUnitsAsString()
    {
        return _WtieDdUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtieDdUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtieDdUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetWtieOriginalCost()
    {
        return _WtieOriginalCost;
    }
    
    // Standard Setter
    public void SetWtieOriginalCost(decimal value)
    {
        _WtieOriginalCost = value;
    }
    
    // Get<>AsString()
    public string GetWtieOriginalCostAsString()
    {
        return _WtieOriginalCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtieOriginalCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtieOriginalCost = parsed;
    }
    
    // Standard Getter
    public decimal GetWtieDdValue()
    {
        return _WtieDdValue;
    }
    
    // Standard Setter
    public void SetWtieDdValue(decimal value)
    {
        _WtieDdValue = value;
    }
    
    // Get<>AsString()
    public string GetWtieDdValueAsString()
    {
        return _WtieDdValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtieDdValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtieDdValue = parsed;
    }
    
    // Standard Getter
    public decimal GetWtieNdlUnits()
    {
        return _WtieNdlUnits;
    }
    
    // Standard Setter
    public void SetWtieNdlUnits(decimal value)
    {
        _WtieNdlUnits = value;
    }
    
    // Get<>AsString()
    public string GetWtieNdlUnitsAsString()
    {
        return _WtieNdlUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtieNdlUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtieNdlUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetWtieNdlCost()
    {
        return _WtieNdlCost;
    }
    
    // Standard Setter
    public void SetWtieNdlCost(decimal value)
    {
        _WtieNdlCost = value;
    }
    
    // Get<>AsString()
    public string GetWtieNdlCostAsString()
    {
        return _WtieNdlCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtieNdlCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtieNdlCost = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}

}}