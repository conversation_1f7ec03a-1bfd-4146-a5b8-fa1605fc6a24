using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtKeysUnprocessed Data Structure

public class WtKeysUnprocessed
{
    private static int _size = 51;
    // [DEBUG] Class: WtKeysUnprocessed, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler449, is_external=, is_static_class=False, static_prefix=
    private string _Filler449 ="KEYS UNPROCESSED===============";
    
    
    
    
    // [DEBUG] Field: WtkMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtkMaxTableSize =4000;
    
    
    
    
    // [DEBUG] Field: WtkOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtkOccurs =4000;
    
    
    
    
    // [DEBUG] Field: WtkKeysUnprocessedTable, is_external=, is_static_class=False, static_prefix=
    private WtkKeysUnprocessedTable _WtkKeysUnprocessedTable = new WtkKeysUnprocessedTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtKeysUnprocessedAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler449.PadRight(32));
        result.Append(_WtkMaxTableSize.ToString().PadLeft(4, '0'));
        result.Append(_WtkOccurs.ToString().PadLeft(4, '0'));
        result.Append(_WtkKeysUnprocessedTable.GetWtkKeysUnprocessedTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtKeysUnprocessedAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 32 <= data.Length)
        {
            string extracted = data.Substring(offset, 32).Trim();
            SetFiller449(extracted);
        }
        offset += 32;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtkMaxTableSize(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtkOccurs(parsedInt);
        }
        offset += 4;
        if (offset + 11 <= data.Length)
        {
            _WtkKeysUnprocessedTable.SetWtkKeysUnprocessedTableAsString(data.Substring(offset, 11));
        }
        else
        {
            _WtkKeysUnprocessedTable.SetWtkKeysUnprocessedTableAsString(data.Substring(offset));
        }
        offset += 11;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtKeysUnprocessedAsString();
    }
    // Set<>String Override function
    public void SetWtKeysUnprocessed(string value)
    {
        SetWtKeysUnprocessedAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller449()
    {
        return _Filler449;
    }
    
    // Standard Setter
    public void SetFiller449(string value)
    {
        _Filler449 = value;
    }
    
    // Get<>AsString()
    public string GetFiller449AsString()
    {
        return _Filler449.PadRight(32);
    }
    
    // Set<>AsString()
    public void SetFiller449AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler449 = value;
    }
    
    // Standard Getter
    public int GetWtkMaxTableSize()
    {
        return _WtkMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtkMaxTableSize(int value)
    {
        _WtkMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtkMaxTableSizeAsString()
    {
        return _WtkMaxTableSize.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtkMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtkMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtkOccurs()
    {
        return _WtkOccurs;
    }
    
    // Standard Setter
    public void SetWtkOccurs(int value)
    {
        _WtkOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtkOccursAsString()
    {
        return _WtkOccurs.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtkOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtkOccurs = parsed;
    }
    
    // Standard Getter
    public WtkKeysUnprocessedTable GetWtkKeysUnprocessedTable()
    {
        return _WtkKeysUnprocessedTable;
    }
    
    // Standard Setter
    public void SetWtkKeysUnprocessedTable(WtkKeysUnprocessedTable value)
    {
        _WtkKeysUnprocessedTable = value;
    }
    
    // Get<>AsString()
    public string GetWtkKeysUnprocessedTableAsString()
    {
        return _WtkKeysUnprocessedTable != null ? _WtkKeysUnprocessedTable.GetWtkKeysUnprocessedTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtkKeysUnprocessedTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtkKeysUnprocessedTable == null)
        {
            _WtkKeysUnprocessedTable = new WtkKeysUnprocessedTable();
        }
        _WtkKeysUnprocessedTable.SetWtkKeysUnprocessedTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtkKeysUnprocessedTable(string value)
    {
        _WtkKeysUnprocessedTable.SetWtkKeysUnprocessedTableAsString(value);
    }
    // Nested Class: WtkKeysUnprocessedTable
    public class WtkKeysUnprocessedTable
    {
        private static int _size = 11;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtkCalSedol, is_external=, is_static_class=False, static_prefix=
        private string[] _WtkCalSedol = new string[4000];
        
        
        
        
    public WtkKeysUnprocessedTable() {}
    
    public WtkKeysUnprocessedTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        for (int i = 0; i < 4000; i++)
        {
            string value = data.Substring(offset, 11);
            _WtkCalSedol[i] = value.Trim();
            offset += 11;
        }
        
    }
    
    // Serialization methods
    public string GetWtkKeysUnprocessedTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 4000; i++)
        {
            result.Append(_WtkCalSedol[i].PadRight(11));
        }
        
        return result.ToString();
    }
    
    public void SetWtkKeysUnprocessedTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 4000; i++)
        {
            if (offset + 11 > data.Length) break;
            string val = data.Substring(offset, 11);
            
            _WtkCalSedol[i] = val.Trim();
            offset += 11;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtkCalSedol
    public string GetWtkCalSedolAt(int index)
    {
        return _WtkCalSedol[index];
    }
    
    public void SetWtkCalSedolAt(int index, string value)
    {
        _WtkCalSedol[index] = value;
    }
    
    public string GetWtkCalSedolAsStringAt(int index)
    {
        return _WtkCalSedol[index].PadRight(11);
    }
    
    public void SetWtkCalSedolAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _WtkCalSedol[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetWtkCalSedol()
    {
        return _WtkCalSedol != null && _WtkCalSedol.Length > 0
        ? _WtkCalSedol[0]
        : default(string);
    }
    
    public void SetWtkCalSedol(string value)
    {
        if (_WtkCalSedol == null || _WtkCalSedol.Length == 0)
        _WtkCalSedol = new string[1];
        _WtkCalSedol[0] = value;
    }
    
    public string GetWtkCalSedolAsString()
    {
        return _WtkCalSedol != null && _WtkCalSedol.Length > 0
        ? _WtkCalSedol[0].ToString()
        : string.Empty;
    }
    
    public void SetWtkCalSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_WtkCalSedol == null || _WtkCalSedol.Length == 0)
        _WtkCalSedol = new string[1];
        
        _WtkCalSedol[0] = value;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}
