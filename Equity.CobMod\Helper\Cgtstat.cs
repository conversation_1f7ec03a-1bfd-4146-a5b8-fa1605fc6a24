using System;
using System.Text;
using EquityProject.CgtstatDTO;
namespace EquityProject.CgtstatPGM
{
    // Cgtstat Class Definition

    //Cgtstat Class Constructor
    public class Cgtstat
    {
        // Declare Cgtstat Class private variables
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();

        // Declare {program_name} Class getters setters
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        /// <summary>
        /// Helper method to call external subroutines
        /// </summary>
        /// <param name="ivar">The Ivar parameter to pass to the subroutine</param>
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
        }


        // Run() method
        public void Run(Gvar gvar, Ivar ivar)
        {
            // Call the main entry point
            // No mainline procedure found, add your entry point here
            Mainprocess(gvar, ivar);
        }

        // Methods representing paragraphs under procedure division
        /// <summary>
        /// Performs the main processing logic based on the COBOL paragraph: MAINPROCESS.
        /// </summary>
        /// <remarks>
        /// This method coordinates the initialization and message retrieval by invoking
        /// the corresponding methods that reflect the COBOL PERFORM statements:
        /// - A-INITIALISE
        /// - B-GET-MESSAGE
        /// 
        /// The method indicates the end of processing with the EXIT PROGRAM concept in COBOL,
        /// translating it implicitly as the conclusion of this C# method.
        /// </remarks>
        public void Mainprocess(Gvar gvar, Ivar ivar)
        {
            // PERFORM A-INITIALISE
            Ainitialise(gvar, ivar);

            // PERFORM B-GET-MESSAGE
            Bgetmessage(gvar, ivar);

            // Exit Program - implied as the end of the method
        }
        /// <summary>
        /// COBOL Paragraph: aInitialise
        /// </summary>
        /// <remarks>
        /// This method initializes various status fields by transferring values from linkage items or other fields.
        /// </remarks>
        public void Ainitialise(Gvar gvar, Ivar ivar)
        {
            // Move L-FILE-STATUS to FILE-STATUS
            gvar.SetFileStatusAsString(ivar.GetCgtstatLinkage().GetLFileStatus());

            // Move STATUS-1 to W-STATUS-1
            gvar.GetWStatus().SetWStatus1(gvar.GetFileStatus().GetStatus1());

            // If STATUS-1 equals "9"
            if (gvar.GetFileStatus().GetStatus1() == "9")
            {
                // Move STATUS-2 to W-BINARY-CHAR
                gvar.GetWBinaryField().SetWBinaryChar(gvar.GetFileStatus().GetStatus2());

                // Move W-BINARY-STATUS-2 to W-ST2-999
                gvar.GetWStatus().SetWSt2999(gvar.GetWBinaryStatus2());
            }
            else
            {
                // Otherwise, move STATUS-2 to W-STATUS-2
                gvar.GetWStatus().SetWStatus2(gvar.GetFileStatus().GetStatus2());
            }
        }
        /// <summary>
        /// COBOL paragraph: bGetMessage
        /// </summary>
        /// <remarks>
        /// This method searches through the MESSAGE-ENTRY and updates L-FILE-STATUS-TEXT 
        /// based on the status found. If the status matches predefined defaults, it sets 
        /// corresponding values; otherwise, it handles unknown statuses.
        /// </remarks>
        public void Bgetmessage(Gvar gvar, Ivar ivar)
        {

            // Construct the 5-char search key (W-STATUS is 5 chars: W-STATUS-1(1) + FILLER(1) + W-STATUS-2(3))
            string searchKey = _gvar.GetWStatus().GetWStatus1() + _gvar.GetWStatus().GetFiller1() + _gvar.GetWStatus().GetWStatus2();

            bool found = false;
            // SEARCH ALL MESSAGE-ENTRY (simulated with linear search)
            // Requires MessageTable.MessageEntry.MessageStatus to be size 5
            for (int i = 0; i < 120; i++)
            {
                // Assuming MessageEntry array is initialized and populated
                // GetMessageEntryAt(i).GetMessageStatus() should return a 5-char string
                if (_gvar.GetMessageTable().GetMessageEntryAt(i).GetMessageStatus() == searchKey)
                {
                    // WHEN MESSAGE-STATUS (MESSAGE-INDEX) = W-STATUS
                    // MOVE MESSAGE-ENTRY (MESSAGE-INDEX) TO L-FILE-STATUS-TEXT.
                    // Requires MessageEntry to be size 190 and LFileStatusText size 190
                    _ivar.GetCgtstatLinkage().SetLFileStatusText(_gvar.GetMessageTable().GetMessageEntryAt(i).GetMessageEntryAsString());
                    found = true;
                    break; // Found, exit search
                }
            }

            if (found)
            {
                // Handle the SEARCH AT END scenario
                string status1 = gvar.GetFileStatus().GetStatus1();
                switch (status1)
                {
                    case "0":
                        gvar.GetWDefaultMessages().SetWDefault0(gvar.GetWStatusAsString());
                        ivar.GetCgtstatLinkage().SetLFileStatusText(gvar.GetWDefaultMessages().GetWDefault0());
                        break;
                    case "1":
                        gvar.GetWDefaultMessages().SetWDefault1(gvar.GetWStatusAsString());
                        ivar.GetCgtstatLinkage().SetLFileStatusText(gvar.GetWDefaultMessages().GetWDefault1());
                        break;
                    case "2":
                        gvar.GetWDefaultMessages().SetWDefault2(gvar.GetWStatusAsString());
                        ivar.GetCgtstatLinkage().SetLFileStatusText(gvar.GetWDefaultMessages().GetWDefault2());
                        break;
                    case "3":
                        gvar.GetWDefaultMessages().SetWDefault3(gvar.GetWStatusAsString());
                        ivar.GetCgtstatLinkage().SetLFileStatusText(gvar.GetWDefaultMessages().GetWDefault3());
                        break;
                    case "4":
                        gvar.GetWDefaultMessages().SetWDefault4(gvar.GetWStatusAsString());
                        ivar.GetCgtstatLinkage().SetLFileStatusText(gvar.GetWDefaultMessages().GetWDefault4());
                        break;
                    default:
                        // Handle WHEN OTHER clause
                        gvar.GetWUnknown().SetWuStat(gvar.GetWStatusAsString());
                        ivar.GetCgtstatLinkage().SetLFileStatusText(gvar.GetWUnknownAsString());
                        break;
                }
            }
        }
    }
}
