using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtIndexIndexDates Data Structure

public class WtIndexIndexDates
{
    private static int _size = 28;
    // [DEBUG] Class: WtIndexIndexDates, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler444, is_external=, is_static_class=False, static_prefix=
    private string _Filler444 ="INDEX-INX-DATES=";
    
    
    
    
    // [DEBUG] Field: WtiiMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtiiMaxTableSize =4000;
    
    
    
    
    // [DEBUG] Field: WtiiOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtiiOccurs =4000;
    
    
    
    
    // [DEBUG] Field: WtiiTable, is_external=, is_static_class=False, static_prefix=
    private WtiiTable _WtiiTable = new WtiiTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtIndexIndexDatesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler444.PadRight(16));
        result.Append(_WtiiMaxTableSize.ToString().PadLeft(4, '0'));
        result.Append(_WtiiOccurs.ToString().PadLeft(4, '0'));
        result.Append(_WtiiTable.GetWtiiTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtIndexIndexDatesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller444(extracted);
        }
        offset += 16;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtiiMaxTableSize(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtiiOccurs(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            _WtiiTable.SetWtiiTableAsString(data.Substring(offset, 4));
        }
        else
        {
            _WtiiTable.SetWtiiTableAsString(data.Substring(offset));
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtIndexIndexDatesAsString();
    }
    // Set<>String Override function
    public void SetWtIndexIndexDates(string value)
    {
        SetWtIndexIndexDatesAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller444()
    {
        return _Filler444;
    }
    
    // Standard Setter
    public void SetFiller444(string value)
    {
        _Filler444 = value;
    }
    
    // Get<>AsString()
    public string GetFiller444AsString()
    {
        return _Filler444.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller444AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler444 = value;
    }
    
    // Standard Getter
    public int GetWtiiMaxTableSize()
    {
        return _WtiiMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtiiMaxTableSize(int value)
    {
        _WtiiMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtiiMaxTableSizeAsString()
    {
        return _WtiiMaxTableSize.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtiiMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtiiMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtiiOccurs()
    {
        return _WtiiOccurs;
    }
    
    // Standard Setter
    public void SetWtiiOccurs(int value)
    {
        _WtiiOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtiiOccursAsString()
    {
        return _WtiiOccurs.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtiiOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtiiOccurs = parsed;
    }
    
    // Standard Getter
    public WtiiTable GetWtiiTable()
    {
        return _WtiiTable;
    }
    
    // Standard Setter
    public void SetWtiiTable(WtiiTable value)
    {
        _WtiiTable = value;
    }
    
    // Get<>AsString()
    public string GetWtiiTableAsString()
    {
        return _WtiiTable != null ? _WtiiTable.GetWtiiTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtiiTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtiiTable == null)
        {
            _WtiiTable = new WtiiTable();
        }
        _WtiiTable.SetWtiiTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtiiTable(string value)
    {
        _WtiiTable.SetWtiiTableAsString(value);
    }
    // Nested Class: WtiiTable
    public class WtiiTable
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtiiIndex, is_external=, is_static_class=False, static_prefix=
        private string[] _WtiiIndex = new string[4000];
        
        
        
        
    public WtiiTable() {}
    
    public WtiiTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        for (int i = 0; i < 4000; i++)
        {
            string value = data.Substring(offset, 4);
            _WtiiIndex[i] = value.Trim();
            offset += 4;
        }
        
    }
    
    // Serialization methods
    public string GetWtiiTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 4000; i++)
        {
            result.Append(_WtiiIndex[i].PadRight(4));
        }
        
        return result.ToString();
    }
    
    public void SetWtiiTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 4000; i++)
        {
            if (offset + 4 > data.Length) break;
            string val = data.Substring(offset, 4);
            
            _WtiiIndex[i] = val.Trim();
            offset += 4;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtiiIndex
    public string GetWtiiIndexAt(int index)
    {
        return _WtiiIndex[index];
    }
    
    public void SetWtiiIndexAt(int index, string value)
    {
        _WtiiIndex[index] = value;
    }
    
    public string GetWtiiIndexAsStringAt(int index)
    {
        return _WtiiIndex[index].PadRight(4);
    }
    
    public void SetWtiiIndexAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _WtiiIndex[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetWtiiIndex()
    {
        return _WtiiIndex != null && _WtiiIndex.Length > 0
        ? _WtiiIndex[0]
        : default(string);
    }
    
    public void SetWtiiIndex(string value)
    {
        if (_WtiiIndex == null || _WtiiIndex.Length == 0)
        _WtiiIndex = new string[1];
        _WtiiIndex[0] = value;
    }
    
    public string GetWtiiIndexAsString()
    {
        return _WtiiIndex != null && _WtiiIndex.Length > 0
        ? _WtiiIndex[0].ToString()
        : string.Empty;
    }
    
    public void SetWtiiIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_WtiiIndex == null || _WtiiIndex.Length == 0)
        _WtiiIndex = new string[1];
        
        _WtiiIndex[0] = value;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}
