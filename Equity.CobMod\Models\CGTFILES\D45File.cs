using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D45File Data Structure

public class D45File
{
    private static int _size = 12;
    // [DEBUG] Class: D45File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler211, is_external=, is_static_class=False, static_prefix=
    private string _Filler211 ="$";
    
    
    
    
    // [DEBUG] Field: D45UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D45UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler212, is_external=, is_static_class=False, static_prefix=
    private string _Filler212 ="MYE.DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD45FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler211.PadRight(1));
        result.Append(_D45UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler212.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD45FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller211(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD45UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller212(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD45FileAsString();
    }
    // Set<>String Override function
    public void SetD45File(string value)
    {
        SetD45FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller211()
    {
        return _Filler211;
    }
    
    // Standard Setter
    public void SetFiller211(string value)
    {
        _Filler211 = value;
    }
    
    // Get<>AsString()
    public string GetFiller211AsString()
    {
        return _Filler211.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller211AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler211 = value;
    }
    
    // Standard Getter
    public int GetD45UserNo()
    {
        return _D45UserNo;
    }
    
    // Standard Setter
    public void SetD45UserNo(int value)
    {
        _D45UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD45UserNoAsString()
    {
        return _D45UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD45UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D45UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller212()
    {
        return _Filler212;
    }
    
    // Standard Setter
    public void SetFiller212(string value)
    {
        _Filler212 = value;
    }
    
    // Get<>AsString()
    public string GetFiller212AsString()
    {
        return _Filler212.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller212AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler212 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}