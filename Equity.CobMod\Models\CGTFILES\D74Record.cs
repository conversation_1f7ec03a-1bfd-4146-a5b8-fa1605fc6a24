using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D74Record Data Structure

public class D74Record
{
    private static int _size = 36;
    // [DEBUG] Class: D74Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler85, is_external=, is_static_class=False, static_prefix=
    private string _Filler85 ="";
    
    
    
    
    // [DEBUG] Field: D74FundCode, is_external=, is_static_class=False, static_prefix=
    private string _D74FundCode ="";
    
    
    
    
    // [DEBUG] Field: Filler86, is_external=, is_static_class=False, static_prefix=
    private string _Filler86 ="";
    
    
    
    
    // [DEBUG] Field: D74TodaysDate, is_external=, is_static_class=False, static_prefix=
    private string _D74TodaysDate ="";
    
    
    
    
    // [DEBUG] Field: Filler87, is_external=, is_static_class=False, static_prefix=
    private string _Filler87 ="";
    
    
    
    
    // [DEBUG] Field: D74CgtRealised, is_external=, is_static_class=False, static_prefix=
    private decimal _D74CgtRealised =0;
    
    
    
    
    // [DEBUG] Field: Filler88, is_external=, is_static_class=False, static_prefix=
    private string _Filler88 ="";
    
    
    
    
    // [DEBUG] Field: D74CgtLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D74CgtLoss =0;
    
    
    
    
    
    // Serialization methods
    public string GetD74RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler85.PadRight(0));
        result.Append(_D74FundCode.PadRight(0));
        result.Append(_Filler86.PadRight(0));
        result.Append(_D74TodaysDate.PadRight(10));
        result.Append(_Filler87.PadRight(0));
        result.Append(_D74CgtRealised.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler88.PadRight(0));
        result.Append(_D74CgtLoss.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        
        return result.ToString();
    }
    
    public void SetD74RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller85(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD74FundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller86(extracted);
        }
        offset += 0;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetD74TodaysDate(extracted);
        }
        offset += 10;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller87(extracted);
        }
        offset += 0;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD74CgtRealised(parsedDec);
        }
        offset += 13;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller88(extracted);
        }
        offset += 0;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD74CgtLoss(parsedDec);
        }
        offset += 13;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD74RecordAsString();
    }
    // Set<>String Override function
    public void SetD74Record(string value)
    {
        SetD74RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller85()
    {
        return _Filler85;
    }
    
    // Standard Setter
    public void SetFiller85(string value)
    {
        _Filler85 = value;
    }
    
    // Get<>AsString()
    public string GetFiller85AsString()
    {
        return _Filler85.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller85AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler85 = value;
    }
    
    // Standard Getter
    public string GetD74FundCode()
    {
        return _D74FundCode;
    }
    
    // Standard Setter
    public void SetD74FundCode(string value)
    {
        _D74FundCode = value;
    }
    
    // Get<>AsString()
    public string GetD74FundCodeAsString()
    {
        return _D74FundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD74FundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D74FundCode = value;
    }
    
    // Standard Getter
    public string GetFiller86()
    {
        return _Filler86;
    }
    
    // Standard Setter
    public void SetFiller86(string value)
    {
        _Filler86 = value;
    }
    
    // Get<>AsString()
    public string GetFiller86AsString()
    {
        return _Filler86.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller86AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler86 = value;
    }
    
    // Standard Getter
    public string GetD74TodaysDate()
    {
        return _D74TodaysDate;
    }
    
    // Standard Setter
    public void SetD74TodaysDate(string value)
    {
        _D74TodaysDate = value;
    }
    
    // Get<>AsString()
    public string GetD74TodaysDateAsString()
    {
        return _D74TodaysDate.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetD74TodaysDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D74TodaysDate = value;
    }
    
    // Standard Getter
    public string GetFiller87()
    {
        return _Filler87;
    }
    
    // Standard Setter
    public void SetFiller87(string value)
    {
        _Filler87 = value;
    }
    
    // Get<>AsString()
    public string GetFiller87AsString()
    {
        return _Filler87.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller87AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler87 = value;
    }
    
    // Standard Getter
    public decimal GetD74CgtRealised()
    {
        return _D74CgtRealised;
    }
    
    // Standard Setter
    public void SetD74CgtRealised(decimal value)
    {
        _D74CgtRealised = value;
    }
    
    // Get<>AsString()
    public string GetD74CgtRealisedAsString()
    {
        return _D74CgtRealised.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD74CgtRealisedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D74CgtRealised = parsed;
    }
    
    // Standard Getter
    public string GetFiller88()
    {
        return _Filler88;
    }
    
    // Standard Setter
    public void SetFiller88(string value)
    {
        _Filler88 = value;
    }
    
    // Get<>AsString()
    public string GetFiller88AsString()
    {
        return _Filler88.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller88AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler88 = value;
    }
    
    // Standard Getter
    public decimal GetD74CgtLoss()
    {
        return _D74CgtLoss;
    }
    
    // Standard Setter
    public void SetD74CgtLoss(decimal value)
    {
        _D74CgtLoss = value;
    }
    
    // Get<>AsString()
    public string GetD74CgtLossAsString()
    {
        return _D74CgtLoss.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD74CgtLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D74CgtLoss = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}