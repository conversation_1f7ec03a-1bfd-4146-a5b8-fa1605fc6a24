using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// <Section> Class for Gvar
public class Gvar
{
public Gvar() {}

// Fields in the class


// [DEBUG] Field: WNotNumeric, is_external=, is_static_class=False, static_prefix=
private string _WNotNumeric ="";


// 88-level condition checks for WNotNumeric
public bool IsWNotNumericY()
{
    if (this._WNotNumeric == "'Y'") return true;
    return false;
}
public bool IsWNotNumericN()
{
    if (this._WNotNumeric == "'N'") return true;
    return false;
}


// [DEBUG] Field: WWttcS, is_external=, is_static_class=False, static_prefix=
private int _WWttcS =0;




// [DEBUG] Field: WIlgComputationStartDate, is_external=, is_static_class=False, static_prefix=
private string _WIlgComputationStartDate ="";




// [DEBUG] Field: WTempFileDisk, is_external=, is_static_class=False, static_prefix=
private string _WTempFileDisk ="";


// 88-level condition checks for WTempFileDisk
public bool IsConfigTempFileDisk()
{
    if (this._WTempFileDisk == "True") return true;
    return false;
}


// [DEBUG] Field: WIlgPeriodEndRevaluations, is_external=, is_static_class=False, static_prefix=
private string _WIlgPeriodEndRevaluations ="";


// 88-level condition checks for WIlgPeriodEndRevaluations
public bool IsConfigIlgComputationPeriodEndRevaluations()
{
    if (this._WIlgPeriodEndRevaluations == "True") return true;
    return false;
}


// [DEBUG] Field: WReindexation, is_external=, is_static_class=False, static_prefix=
private string _WReindexation ="";


// 88-level condition checks for WReindexation
public bool IsWIsReindexation()
{
    if (this._WReindexation == "'Y'") return true;
    return false;
}
public bool IsWIsIndexation()
{
    if (this._WReindexation == "'N'") return true;
    return false;
}


// [DEBUG] Field: WF12BargainDateFa12, is_external=, is_static_class=False, static_prefix=
private int _WF12BargainDateFa12 =0;


// 88-level condition checks for WF12BargainDateFa12
public bool IsWF12Between20122013()
{
    if (this._WF12BargainDateFa12 >= 20121221 && this._WF12BargainDateFa12 <= 20121231) return true;
    return false;
}


// [DEBUG] Field: RetailPriceIndexRecord, is_external=, is_static_class=False, static_prefix=
private RetailPriceIndexRecord _RetailPriceIndexRecord = new RetailPriceIndexRecord();




// [DEBUG] Field: CompanyFundRecord, is_external=, is_static_class=False, static_prefix=
private CompanyFundRecord _CompanyFundRecord = new CompanyFundRecord();




// [DEBUG] Field: WsSaveCompanyFundRecord, is_external=, is_static_class=False, static_prefix=
private string _WsSaveCompanyFundRecord ="";




// [DEBUG] Field: D94Record, is_external=, is_static_class=False, static_prefix=
private D94Record _D94Record = new D94Record();




// [DEBUG] Field: D133Record, is_external=, is_static_class=False, static_prefix=
private D133Record _D133Record = new D133Record();




// [DEBUG] Field: D4Record, is_external=, is_static_class=False, static_prefix=
private D4Record _D4Record = new D4Record();




// [DEBUG] Field: D153Record, is_external=, is_static_class=False, static_prefix=
private D153Record _D153Record = new D153Record();




// [DEBUG] Field: Cgtdate2LinkageDate1, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate1 _Cgtdate2LinkageDate1 = new Cgtdate2LinkageDate1();




// [DEBUG] Field: Cgtdate2LinkageDate2, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate2 _Cgtdate2LinkageDate2 = new Cgtdate2LinkageDate2();




// [DEBUG] Field: Cgtdate2LinkageDate3, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate3 _Cgtdate2LinkageDate3 = new Cgtdate2LinkageDate3();




// [DEBUG] Field: Cgtdate2LinkageDate4, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate4 _Cgtdate2LinkageDate4 = new Cgtdate2LinkageDate4();




// [DEBUG] Field: Cgtdate2LinkageDate5, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate5 _Cgtdate2LinkageDate5 = new Cgtdate2LinkageDate5();




// [DEBUG] Field: Cgtdate2LinkageDate6, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate6 _Cgtdate2LinkageDate6 = new Cgtdate2LinkageDate6();




// [DEBUG] Field: Cgtdate2LinkageDate7, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate7 _Cgtdate2LinkageDate7 = new Cgtdate2LinkageDate7();




// [DEBUG] Field: Cgtdate2LinkageDate8, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate8 _Cgtdate2LinkageDate8 = new Cgtdate2LinkageDate8();




// [DEBUG] Field: Cgtdate2LinkageDate9, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate9 _Cgtdate2LinkageDate9 = new Cgtdate2LinkageDate9();




// [DEBUG] Field: Cgtdate2LinkageDate10, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate10 _Cgtdate2LinkageDate10 = new Cgtdate2LinkageDate10();




// [DEBUG] Field: CommonLinkage, is_external=, is_static_class=False, static_prefix=
private CommonLinkage _CommonLinkage = new CommonLinkage();




// [DEBUG] Field: COUNTRY_FILE, is_external=, is_static_class=False, static_prefix=
public const string COUNTRY_FILE = "CGTCTRY ";




// [DEBUG] Field: GROUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string GROUP_FILE = "CGTGRP  ";




// [DEBUG] Field: STOCK_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_FILE = "CGTSTK  ";




// [DEBUG] Field: FUND_FILE, is_external=, is_static_class=False, static_prefix=
public const string FUND_FILE = "M-U-FUND";




// [DEBUG] Field: RPI_FILE, is_external=, is_static_class=False, static_prefix=
public const string RPI_FILE = "CGTRPI  ";




// [DEBUG] Field: PARAMETER_FILE, is_external=, is_static_class=False, static_prefix=
public const string PARAMETER_FILE = "M-PARAM ";




// [DEBUG] Field: USER_FILE, is_external=, is_static_class=False, static_prefix=
public const string USER_FILE = "M-USER  ";




// [DEBUG] Field: OUTPUT_LISTING, is_external=, is_static_class=False, static_prefix=
public const string OUTPUT_LISTING = "M-OUTLST";




// [DEBUG] Field: MASTER_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string MASTER_LOG_FILE = "M-LOG   ";




// [DEBUG] Field: REALISED_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string REALISED_DATA_FILE = "CGTDR   ";




// [DEBUG] Field: UNREALISED_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string UNREALISED_DATA_FILE = "CGTDU   ";




// [DEBUG] Field: NOTIONAL_SALE_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string NOTIONAL_SALE_DATA_FILE = "CGTDN   ";




// [DEBUG] Field: REALISED_TAX_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string REALISED_TAX_DATA_FILE = "CGTDT   ";




// [DEBUG] Field: UNREALISED_TAX_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string UNREALISED_TAX_DATA_FILE = "CGTDX   ";




// [DEBUG] Field: ERROR_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string ERROR_DATA_FILE = "CGTERR  ";




// [DEBUG] Field: PRINTER_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRINTER_FILE = "M-PRINT ";




// [DEBUG] Field: STOCK_TYPE_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_TYPE_FILE = "M-STOCK ";




// [DEBUG] Field: YE_REC2_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_REC2_DATA_FILE = "CGTYERR ";




// [DEBUG] Field: TRANSACTION_CODE_FILE, is_external=, is_static_class=False, static_prefix=
public const string TRANSACTION_CODE_FILE = "M-TRANS ";




// [DEBUG] Field: OUTPUT_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string OUTPUT_LOG_FILE = "M-OUTLOG";




// [DEBUG] Field: MESSAGE_FILE, is_external=, is_static_class=False, static_prefix=
public const string MESSAGE_FILE = "M-MESS  ";




// [DEBUG] Field: USER_FUND_FILE, is_external=, is_static_class=False, static_prefix=
public const string USER_FUND_FILE = "CGTFUNDX";




// [DEBUG] Field: HELP_TEXT_FILE, is_external=, is_static_class=False, static_prefix=
public const string HELP_TEXT_FILE = "M-HELP  ";




// [DEBUG] Field: DEFAULT_ACCESS_FILE, is_external=, is_static_class=False, static_prefix=
public const string DEFAULT_ACCESS_FILE = "M-DEF-AC";




// [DEBUG] Field: ACCESS_PROFILE_FILE, is_external=, is_static_class=False, static_prefix=
public const string ACCESS_PROFILE_FILE = "M-ACCESS";




// [DEBUG] Field: EXTEL_PRICES_FILE, is_external=, is_static_class=False, static_prefix=
public const string EXTEL_PRICES_FILE = "M-EPRICE";




// [DEBUG] Field: EXTEL_CURRENCY_FILE, is_external=, is_static_class=False, static_prefix=
public const string EXTEL_CURRENCY_FILE = "M-CURR  ";




// [DEBUG] Field: STOCK_PRICE_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_PRICE_FILE = "M-SPRICE";




// [DEBUG] Field: EXTEL_TRANSMISSION_FILE, is_external=, is_static_class=False, static_prefix=
public const string EXTEL_TRANSMISSION_FILE = "M-ETRANS";




// [DEBUG] Field: SEQ_BALANCE_FILE, is_external=, is_static_class=False, static_prefix=
public const string SEQ_BALANCE_FILE = "CGTMFO  ";




// [DEBUG] Field: TRANSACTION_FILE, is_external=, is_static_class=False, static_prefix=
public const string TRANSACTION_FILE = "CGTTRANS";




// [DEBUG] Field: BACKUP_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string BACKUP_LOG_FILE = "M-BK-LOG";




// [DEBUG] Field: BACKUP_DETAILS_FILE, is_external=, is_static_class=False, static_prefix=
public const string BACKUP_DETAILS_FILE = "M-BK-DET";




// [DEBUG] Field: STOCK_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_LOAD_DATA_FILE = "M-ST-DAT";




// [DEBUG] Field: FUNDS_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string FUNDS_LOAD_DATA_FILE = "M-FN-DAT";




// [DEBUG] Field: PRICE_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRICE_LOAD_DATA_FILE = "M-PR-DAT";




// [DEBUG] Field: BALANCE_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string BALANCE_LOAD_DATA_FILE = "M-BL-DAT";




// [DEBUG] Field: REPLACEMENT_ACQ_FILE, is_external=, is_static_class=False, static_prefix=
public const string REPLACEMENT_ACQ_FILE = "M-RP-ACQ";




// [DEBUG] Field: REPLACEMENT_DIS_FILE, is_external=, is_static_class=False, static_prefix=
public const string REPLACEMENT_DIS_FILE = "M-RP-DIS";




// [DEBUG] Field: REPORT_RUN_MSG_FILE, is_external=, is_static_class=False, static_prefix=
public const string REPORT_RUN_MSG_FILE = "CGTMSG  ";




// [DEBUG] Field: RCF_FILE, is_external=, is_static_class=False, static_prefix=
public const string RCF_FILE = "CGTRCF  ";




// [DEBUG] Field: RPI_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string RPI_LOAD_DATA_FILE = "M-RPIDAT";




// [DEBUG] Field: COUNTRY_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string COUNTRY_LOAD_DATA_FILE = "M-CY-DAT";




// [DEBUG] Field: GROUP_LOAD_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string GROUP_LOAD_DATA_FILE = "M-GR-DAT";




// [DEBUG] Field: GAINLOSS_DATA_FILE, is_external=, is_static_class=False, static_prefix=
public const string GAINLOSS_DATA_FILE = "CGTGLDAT";




// [DEBUG] Field: ACQUISITION_EXPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string ACQUISITION_EXPORT_FILE = "MAEXPORT";




// [DEBUG] Field: TAPER_RATE_FILE, is_external=, is_static_class=False, static_prefix=
public const string TAPER_RATE_FILE = "TAPERATE";




// [DEBUG] Field: ASSET_USAGE_CALENDAR_FILE, is_external=, is_static_class=False, static_prefix=
public const string ASSET_USAGE_CALENDAR_FILE = "ASSETUC ";




// [DEBUG] Field: PERIOD_END_CALENDAR_FILE, is_external=, is_static_class=False, static_prefix=
public const string PERIOD_END_CALENDAR_FILE = "PENDCAL ";




// [DEBUG] Field: PERIOD_END_CALENDAR_DATES_FILE, is_external=, is_static_class=False, static_prefix=
public const string PERIOD_END_CALENDAR_DATES_FILE = "PENDCALD";




// [DEBUG] Field: INTER_CONNECTED_FUNDS_FILE, is_external=, is_static_class=False, static_prefix=
public const string INTER_CONNECTED_FUNDS_FILE = "ICFUNDS ";




// [DEBUG] Field: PRICE_TYPES_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRICE_TYPES_FILE = "PRICETYP";




// [DEBUG] Field: PENDING_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string PENDING_LOG_FILE = "PENDLOG ";




// [DEBUG] Field: PENDING_ITEMS_FILE, is_external=, is_static_class=False, static_prefix=
public const string PENDING_ITEMS_FILE = "PENDITEM";




// [DEBUG] Field: GAINLOSS_DATA_FILE_LR, is_external=, is_static_class=False, static_prefix=
public const string GAINLOSS_DATA_FILE_LR = "CGTGLDLR";




// [DEBUG] Field: CG01_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CG01_REPORT_FILE = "M-CG01  ";




// [DEBUG] Field: CG02_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CG02_REPORT_FILE = "M-CG02  ";




// [DEBUG] Field: CG03_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CG03_REPORT_FILE = "M-CG03  ";




// [DEBUG] Field: ERROR_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string ERROR_REPORT_FILE = "M-ERR-RP";




// [DEBUG] Field: REALISED_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string REALISED_SCHEDULE_FILE = "CGTPR   ";




// [DEBUG] Field: UNREALISED_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string UNREALISED_SCHEDULE_FILE = "CGTPU   ";




// [DEBUG] Field: NOTIONAL_SALE_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string NOTIONAL_SALE_SCHEDULE_FILE = "CGTPN   ";




// [DEBUG] Field: REALISED_TAX_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string REALISED_TAX_SCHEDULE_FILE = "CGTPT   ";




// [DEBUG] Field: UNREALISED_TAX_SCHEDULE_FILE, is_external=, is_static_class=False, static_prefix=
public const string UNREALISED_TAX_SCHEDULE_FILE = "CGTPX   ";




// [DEBUG] Field: OFFSHORE_INCOME_REPORT, is_external=, is_static_class=False, static_prefix=
public const string OFFSHORE_INCOME_REPORT = "M-OF-REP";




// [DEBUG] Field: YE_REC_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_REC_REPORT_FILE = "M-YE-REC";




// [DEBUG] Field: YE_DEL_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_DEL_REPORT_FILE = "M-YE-DEL";




// [DEBUG] Field: YE_CON_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_CON_REPORT_FILE = "M-YE-CON";




// [DEBUG] Field: YE_ERR_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_ERR_REPORT_FILE = "M-YE-ERR";




// [DEBUG] Field: YE_BAL_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string YE_BAL_REPORT_FILE = "M-YE-BAL";




// [DEBUG] Field: FOREIGN_EXTEL_REPORT, is_external=, is_static_class=False, static_prefix=
public const string FOREIGN_EXTEL_REPORT = "M-EX-FOR";




// [DEBUG] Field: STERLING_EXTEL_REPORT, is_external=, is_static_class=False, static_prefix=
public const string STERLING_EXTEL_REPORT = "M-EX-STL";




// [DEBUG] Field: CGTR04_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CGTR04_REPORT_FILE = "M-CGTR04";




// [DEBUG] Field: CGTR05_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CGTR05_REPORT_FILE = "M-CGTR05";




// [DEBUG] Field: STOCK_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string STOCK_LOAD_REPORT = "M-ST-REP";




// [DEBUG] Field: FUNDS_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string FUNDS_LOAD_REPORT = "M-FN-REP";




// [DEBUG] Field: PRICE_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string PRICE_LOAD_REPORT = "M-PR-REP";




// [DEBUG] Field: SKAN1_REPORT, is_external=, is_static_class=False, static_prefix=
public const string SKAN1_REPORT = "M-SK1REP";




// [DEBUG] Field: SKAN2_REPORT, is_external=, is_static_class=False, static_prefix=
public const string SKAN2_REPORT = "M-SK2REP";




// [DEBUG] Field: NEW_REALISED_REPORT, is_external=, is_static_class=False, static_prefix=
public const string NEW_REALISED_REPORT = "M-NRGREP";




// [DEBUG] Field: NEW_UNREALISED_REPORT, is_external=, is_static_class=False, static_prefix=
public const string NEW_UNREALISED_REPORT = "M-NUGREP";




// [DEBUG] Field: MGM1_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string MGM1_REPORT_FILE = "M-MGMREP";




// [DEBUG] Field: CAPITAL_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CAPITAL_REPORT_FILE = "M-CAPREP";




// [DEBUG] Field: BALANCES_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string BALANCES_LOAD_REPORT = "M-BALREP";




// [DEBUG] Field: REPLACEMENT_RELIEF_REPORT, is_external=, is_static_class=False, static_prefix=
public const string REPLACEMENT_RELIEF_REPORT = "M-RP-REP";




// [DEBUG] Field: RPI_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string RPI_LOAD_REPORT = "M-RPIREP";




// [DEBUG] Field: COUNTRY_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string COUNTRY_LOAD_REPORT = "M-CY-REP";




// [DEBUG] Field: GROUP_LOAD_REPORT, is_external=, is_static_class=False, static_prefix=
public const string GROUP_LOAD_REPORT = "M-GR-REP";




// [DEBUG] Field: GAINLOSS_REPORT, is_external=, is_static_class=False, static_prefix=
public const string GAINLOSS_REPORT = "CGTGLREP";




// [DEBUG] Field: LOST_INDEXATION_REPORT, is_external=, is_static_class=False, static_prefix=
public const string LOST_INDEXATION_REPORT = "M-ILOST ";




// [DEBUG] Field: LOSU_INDEXATION_REPORT, is_external=, is_static_class=False, static_prefix=
public const string LOSU_INDEXATION_REPORT = "M-ILOSU ";




// [DEBUG] Field: REAL_H_O_GAINS_REPORT, is_external=, is_static_class=False, static_prefix=
public const string REAL_H_O_GAINS_REPORT = "M-R-HO-G";




// [DEBUG] Field: UNREAL_H_O_GAINS_REPORT, is_external=, is_static_class=False, static_prefix=
public const string UNREAL_H_O_GAINS_REPORT = "M-U-HO-G";




// [DEBUG] Field: BATCH_RUN_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string BATCH_RUN_LOG_FILE = "B-RUNLOG";




// [DEBUG] Field: BATCH_QUIT_RUN_FILE, is_external=, is_static_class=False, static_prefix=
public const string BATCH_QUIT_RUN_FILE = "BQUITRUN";




// [DEBUG] Field: TRACE_FILE, is_external=, is_static_class=False, static_prefix=
public const string TRACE_FILE = "M-TRACE ";




// [DEBUG] Field: ERROR_LOG_FILE, is_external=, is_static_class=False, static_prefix=
public const string ERROR_LOG_FILE = "M-ERRLOG";




// [DEBUG] Field: TAPER_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string TAPER_REPORT_FILE = "TAPERREP";




// [DEBUG] Field: TAPER_EXPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string TAPER_EXPORT_FILE = "TAPEREXP";




// [DEBUG] Field: ALLOWANCES_FROM_DB_FILE, is_external=, is_static_class=False, static_prefix=
public const string ALLOWANCES_FROM_DB_FILE = "ALLOWANC";




// [DEBUG] Field: LOSSES_FROM_DB_FILE, is_external=, is_static_class=False, static_prefix=
public const string LOSSES_FROM_DB_FILE = "LOSSFMDB";




// [DEBUG] Field: DISPOSALS_FROM_DB_FILE, is_external=, is_static_class=False, static_prefix=
public const string DISPOSALS_FROM_DB_FILE = "OTHERDIS";




// [DEBUG] Field: INTER_CONNECTED_FUNDS_EXPORT, is_external=, is_static_class=False, static_prefix=
public const string INTER_CONNECTED_FUNDS_EXPORT = "ICFUNDSE";




// [DEBUG] Field: INTER_CONNECTED_FUNDS_REPORT, is_external=, is_static_class=False, static_prefix=
public const string INTER_CONNECTED_FUNDS_REPORT = "ICFUNDSR";




// [DEBUG] Field: CGTR06_REPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string CGTR06_REPORT_FILE = "M-CGTR06";




// [DEBUG] Field: DAILY_TRANSACTION_EXPORT_FILE, is_external=, is_static_class=False, static_prefix=
public const string DAILY_TRANSACTION_EXPORT_FILE = "DAILYTRN";




// [DEBUG] Field: COUNTRY_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string COUNTRY_BACKUP_FILE = "M-D01-BK";




// [DEBUG] Field: GROUP_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string GROUP_BACKUP_FILE = "M-D02-BK";




// [DEBUG] Field: STOCK_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string STOCK_BACKUP_FILE = "M-D03-BK";




// [DEBUG] Field: FUND_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string FUND_BACKUP_FILE = "M-D04-BK";




// [DEBUG] Field: RPI_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string RPI_BACKUP_FILE = "M-D07-BK";




// [DEBUG] Field: PARAMETER_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string PARAMETER_BACKUP_FILE = "M-D08-BK";




// [DEBUG] Field: USER_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string USER_BACKUP_FILE = "M-D09-BK";




// [DEBUG] Field: MASTER_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string MASTER_BACKUP_FILE = "M-D13-BK";




// [DEBUG] Field: PRINTER_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRINTER_BACKUP_FILE = "M-D31-BK";




// [DEBUG] Field: OUT_LOG_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string OUT_LOG_BACKUP_FILE = "M-D35-BK";




// [DEBUG] Field: USER_FUNDS_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string USER_FUNDS_BACKUP_FILE = "M-D37-BK";




// [DEBUG] Field: ACCESS_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string ACCESS_BACKUP_FILE = "M-D40-BK";




// [DEBUG] Field: PRICE_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string PRICE_BACKUP_FILE = "M-D43-BK";




// [DEBUG] Field: BACKUP_LOG_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string BACKUP_LOG_BACKUP_FILE = "M-D47-BK";




// [DEBUG] Field: BACKUP_DETAILS_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string BACKUP_DETAILS_BACKUP_FILE = "M-D48-BK";




// [DEBUG] Field: REPORTS_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string REPORTS_BACKUP_FILE = "M-REP-BK";




// [DEBUG] Field: RCF_BACKUP_FILE, is_external=, is_static_class=False, static_prefix=
public const string RCF_BACKUP_FILE = "M-D86-BK";




// [DEBUG] Field: OPEN_OP, is_external=, is_static_class=False, static_prefix=
public const string OPEN_OP = "OP ";




// [DEBUG] Field: OPEN_INPUT, is_external=, is_static_class=False, static_prefix=
public const string OPEN_INPUT = "OI ";




// [DEBUG] Field: OPEN_INPUT_REPORT, is_external=, is_static_class=False, static_prefix=
public const string OPEN_INPUT_REPORT = "OIR";




// [DEBUG] Field: OPEN_OUTPUT, is_external=, is_static_class=False, static_prefix=
public const string OPEN_OUTPUT = "OO ";




// [DEBUG] Field: OPEN_I_O, is_external=, is_static_class=False, static_prefix=
public const string OPEN_I_O = "IO ";




// [DEBUG] Field: OPEN_EXTEND, is_external=, is_static_class=False, static_prefix=
public const string OPEN_EXTEND = "OE ";




// [DEBUG] Field: OPEN_SINGLE_MASTER_FILE_INPUT, is_external=, is_static_class=False, static_prefix=
public const string OPEN_SINGLE_MASTER_FILE_INPUT = "OS ";




// [DEBUG] Field: CLOSE_SINGLE_MASTER_FILE_INPUT, is_external=, is_static_class=False, static_prefix=
public const string CLOSE_SINGLE_MASTER_FILE_INPUT = "CS ";




// [DEBUG] Field: SET_MASTER_FILE_NAMES, is_external=, is_static_class=False, static_prefix=
public const string SET_MASTER_FILE_NAMES = "FN ";




// [DEBUG] Field: START_EQUAL, is_external=, is_static_class=False, static_prefix=
public const string START_EQUAL = "SE ";




// [DEBUG] Field: START_NOT_LESS_THAN, is_external=, is_static_class=False, static_prefix=
public const string START_NOT_LESS_THAN = "SN ";




// [DEBUG] Field: START_NOT_LESS_THAN_KEY2, is_external=, is_static_class=False, static_prefix=
public const string START_NOT_LESS_THAN_KEY2 = "SN2";




// [DEBUG] Field: START_NOT_LESS_THAN_KEY3, is_external=, is_static_class=False, static_prefix=
public const string START_NOT_LESS_THAN_KEY3 = "SN3";




// [DEBUG] Field: START_GREATER_THAN, is_external=, is_static_class=False, static_prefix=
public const string START_GREATER_THAN = "SG ";




// [DEBUG] Field: START_GT_INVERSE_KEY, is_external=, is_static_class=False, static_prefix=
public const string START_GT_INVERSE_KEY = "SGI";




// [DEBUG] Field: START_LESS_THAN, is_external=, is_static_class=False, static_prefix=
public const string START_LESS_THAN = "SL ";




// [DEBUG] Field: START_NOT_GREATER_THAN, is_external=, is_static_class=False, static_prefix=
public const string START_NOT_GREATER_THAN = "SNG";




// [DEBUG] Field: READ_NEXT, is_external=, is_static_class=False, static_prefix=
public const string READ_NEXT = "RN ";




// [DEBUG] Field: READ_NEXT_WITH_LOCK, is_external=, is_static_class=False, static_prefix=
public const string READ_NEXT_WITH_LOCK = "RNL";




// [DEBUG] Field: READ_RECORD, is_external=, is_static_class=False, static_prefix=
public const string READ_RECORD = "RD ";




// [DEBUG] Field: READ_REPORT, is_external=, is_static_class=False, static_prefix=
public const string READ_REPORT = "RDR";




// [DEBUG] Field: READ_WITH_LOCK, is_external=, is_static_class=False, static_prefix=
public const string READ_WITH_LOCK = "RDL";




// [DEBUG] Field: READ_ON_KEY2, is_external=, is_static_class=False, static_prefix=
public const string READ_ON_KEY2 = "RD2";




// [DEBUG] Field: READ_ON_KEY3, is_external=, is_static_class=False, static_prefix=
public const string READ_ON_KEY3 = "RD3";




// [DEBUG] Field: READ_PREVIOUS_MASTER, is_external=, is_static_class=False, static_prefix=
public const string READ_PREVIOUS_MASTER = "RP ";




// [DEBUG] Field: WRITE_RECORD, is_external=, is_static_class=False, static_prefix=
public const string WRITE_RECORD = "WR ";




// [DEBUG] Field: REWRITE_RECORD, is_external=, is_static_class=False, static_prefix=
public const string REWRITE_RECORD = "RW ";




// [DEBUG] Field: DELETE_RECORD, is_external=, is_static_class=False, static_prefix=
public const string DELETE_RECORD = "DE ";




// [DEBUG] Field: CLOSE_FILE, is_external=, is_static_class=False, static_prefix=
public const string CLOSE_FILE = "CL ";




// [DEBUG] Field: CLOSE_REPORT, is_external=, is_static_class=False, static_prefix=
public const string CLOSE_REPORT = "CLR";




// [DEBUG] Field: CLOSE_ALL_FILES, is_external=, is_static_class=False, static_prefix=
public const string CLOSE_ALL_FILES = "CA ";




// [DEBUG] Field: UNLOCK_RECORD, is_external=, is_static_class=False, static_prefix=
public const string UNLOCK_RECORD = "UN ";




// [DEBUG] Field: BUILD_MASTER_FILE, is_external=, is_static_class=False, static_prefix=
public const string BUILD_MASTER_FILE = "BL ";




// [DEBUG] Field: GET_CALENDAR_DATES, is_external=, is_static_class=False, static_prefix=
public const string GET_CALENDAR_DATES = "GC ";




// [DEBUG] Field: SET_TRANS_EXPORTED_FLAGS, is_external=, is_static_class=False, static_prefix=
public const string SET_TRANS_EXPORTED_FLAGS = "TX ";




// [DEBUG] Field: READ_USER_FUND_RECORD, is_external=, is_static_class=False, static_prefix=
public const string READ_USER_FUND_RECORD = "RU ";




// [DEBUG] Field: READ_NEXT_USER_FUND_RECORD, is_external=, is_static_class=False, static_prefix=
public const string READ_NEXT_USER_FUND_RECORD = "NU ";




// [DEBUG] Field: GET_CONFIG_VALUE, is_external=, is_static_class=False, static_prefix=
public const string GET_CONFIG_VALUE = "CV ";




// [DEBUG] Field: GET_REQUEST_OPTIONS, is_external=, is_static_class=False, static_prefix=
public const string GET_REQUEST_OPTIONS = "RO ";




// [DEBUG] Field: REMAP_C_F_TRANSACTIONS, is_external=, is_static_class=False, static_prefix=
public const string REMAP_C_F_TRANSACTIONS = "RT ";




// [DEBUG] Field: RETURN_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] RETURN_KEY = new byte[] { 0x30, 0x30 };




// [DEBUG] Field: ESC_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] ESC_KEY = new byte[] { 0x31, 0x00 };




// [DEBUG] Field: F1_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F1_KEY = new byte[] { 0x31, 0x01 };




// [DEBUG] Field: F2_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F2_KEY = new byte[] { 0x31, 0x02 };




// [DEBUG] Field: F3_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F3_KEY = new byte[] { 0x31, 0x03 };




// [DEBUG] Field: F4_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F4_KEY = new byte[] { 0x31, 0x04 };




// [DEBUG] Field: F5_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F5_KEY = new byte[] { 0x31, 0x05 };




// [DEBUG] Field: F6_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F6_KEY = new byte[] { 0x31, 0x06 };




// [DEBUG] Field: F7_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F7_KEY = new byte[] { 0x31, 0x07 };




// [DEBUG] Field: F8_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F8_KEY = new byte[] { 0x31, 0x08 };




// [DEBUG] Field: F9_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F9_KEY = new byte[] { 0x31, 0x09 };




// [DEBUG] Field: F10_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F10_KEY = new byte[] { 0x31, 0x0A };




// [DEBUG] Field: CURSOR_UP, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CURSOR_UP = new byte[] { 0x31, 0x18 };




// [DEBUG] Field: CURSOR_DOWN, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CURSOR_DOWN = new byte[] { 0x31, 0x19 };




// [DEBUG] Field: CURSOR_LEFT, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CURSOR_LEFT = new byte[] { 0x31, 0x1B };




// [DEBUG] Field: CURSOR_RIGHT, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CURSOR_RIGHT = new byte[] { 0x31, 0x1A };




// [DEBUG] Field: PAGE_UP, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] PAGE_UP = new byte[] { 0x31, 0x1C };




// [DEBUG] Field: PAGE_DOWN, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] PAGE_DOWN = new byte[] { 0x31, 0x1D };




// [DEBUG] Field: ACCEPT_PAGE_UP, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] ACCEPT_PAGE_UP = new byte[] { 0x31, 0x35 };




// [DEBUG] Field: ACCEPT_PAGE_DOWN, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] ACCEPT_PAGE_DOWN = new byte[] { 0x31, 0x36 };




// [DEBUG] Field: INSERT_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] INSERT_KEY = new byte[] { 0x31, 0x1E };




// [DEBUG] Field: DELETE_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] DELETE_KEY = new byte[] { 0x31, 0x1F };




// [DEBUG] Field: F11_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F11_KEY = new byte[] { 0x31, 0x20 };




// [DEBUG] Field: F12_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] F12_KEY = new byte[] { 0x31, 0x21 };




// [DEBUG] Field: HOME_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] HOME_KEY = new byte[] { 0x31, 0x22 };




// [DEBUG] Field: END_KEY, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] END_KEY = new byte[] { 0x31, 0x23 };




// [DEBUG] Field: CONTROL_HOME, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CONTROL_HOME = new byte[] { 0x31, 0x24 };




// [DEBUG] Field: CONTROL_END, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CONTROL_END = new byte[] { 0x31, 0x25 };




// [DEBUG] Field: CONTROL_PAGE_UP, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CONTROL_PAGE_UP = new byte[] { 0x31, 0x26 };




// [DEBUG] Field: CONTROL_PAGE_DOWN, is_external=, is_static_class=False, static_prefix=
public static readonly byte[] CONTROL_PAGE_DOWN = new byte[] { 0x31, 0x27 };




// [DEBUG] Field: FIRST_MASTER_YEAR, is_external=, is_static_class=False, static_prefix=
public const int FIRST_MASTER_YEAR = 1982;




// [DEBUG] Field: LAST_MASTER_YEAR, is_external=, is_static_class=False, static_prefix=
public const int LAST_MASTER_YEAR = 2044;




// [DEBUG] Field: FIRST_MASTER_YEAR_YY, is_external=, is_static_class=False, static_prefix=
public const int FIRST_MASTER_YEAR_YY = 82;




// [DEBUG] Field: LAST_MASTER_YEAR_YY, is_external=, is_static_class=False, static_prefix=
public const int LAST_MASTER_YEAR_YY = 44;




// [DEBUG] Field: FIRST_PERIOD_DATE, is_external=, is_static_class=False, static_prefix=
public const int FIRST_PERIOD_DATE = 19820101;




// [DEBUG] Field: LAST_PERIOD_DATE, is_external=, is_static_class=False, static_prefix=
public const int LAST_PERIOD_DATE = 20441231;




// [DEBUG] Field: FIRST_PERIOD_DATE_YYMMDD, is_external=, is_static_class=False, static_prefix=
public const int FIRST_PERIOD_DATE_YYMMDD = 820101;




// [DEBUG] Field: LAST_PERIOD_DATE_YYMMDD, is_external=, is_static_class=False, static_prefix=
public const int LAST_PERIOD_DATE_YYMMDD = 441231;




// [DEBUG] Field: CLIENT_PERIOD_END_DATE_97_98, is_external=, is_static_class=False, static_prefix=
public const int CLIENT_PERIOD_END_DATE_97_98 = 19980405;




// [DEBUG] Field: CLIENT_PERIOD_START_DATE_98_99, is_external=, is_static_class=False, static_prefix=
public const int CLIENT_PERIOD_START_DATE_98_99 = 19980406;




// [DEBUG] Field: CLIENT_PERIOD_START_DATE_00_01, is_external=, is_static_class=False, static_prefix=
public const int CLIENT_PERIOD_START_DATE_00_01 = 20000406;




// [DEBUG] Field: REIT_PROCESSING_START_DATE, is_external=, is_static_class=False, static_prefix=
public const int REIT_PROCESSING_START_DATE = 20070101;




// [DEBUG] Field: CREATE_2008_POOL_DATE, is_external=, is_static_class=False, static_prefix=
public const int CREATE_2008_POOL_DATE = 20080406;




// [DEBUG] Field: CREATE_2008_POOL_DATE_YYMMDD, is_external=, is_static_class=False, static_prefix=
public const int CREATE_2008_POOL_DATE_YYMMDD = 080406;




// [DEBUG] Field: FIRST_USABLE_DATE, is_external=, is_static_class=False, static_prefix=
public const int FIRST_USABLE_DATE = 450101;




// [DEBUG] Field: GROUP_NO_ACTION, is_external=, is_static_class=False, static_prefix=
public const int GROUP_NO_ACTION = 1;




// [DEBUG] Field: GROUP_PRO_RATA, is_external=, is_static_class=False, static_prefix=
public const int GROUP_PRO_RATA = 2;




// [DEBUG] Field: GROUP_NON_OLAB_FUND_DEFAULT, is_external=, is_static_class=False, static_prefix=
public const int GROUP_NON_OLAB_FUND_DEFAULT = 3;




// [DEBUG] Field: GROUP_OLAB_FUND_DEFAULT, is_external=, is_static_class=False, static_prefix=
public const int GROUP_OLAB_FUND_DEFAULT = 4;




// [DEBUG] Field: USE_EARLIER_PRICE_NONE, is_external=, is_static_class=False, static_prefix=
public const string USE_EARLIER_PRICE_NONE = " ";




// [DEBUG] Field: USE_EARLIER_PRICE_PREV_DAY, is_external=, is_static_class=False, static_prefix=
public const string USE_EARLIER_PRICE_PREV_DAY = "D";




// [DEBUG] Field: USE_EARLIER_PRICE_PREV_SESSION, is_external=, is_static_class=False, static_prefix=
public const string USE_EARLIER_PRICE_PREV_SESSION = "S";




// [DEBUG] Field: Cgtdate3Linkage, is_external=, is_static_class=False, static_prefix=
private Cgtdate3Linkage _Cgtdate3Linkage = new Cgtdate3Linkage();




// [DEBUG] Field: EqtpathLinkage, is_external=, is_static_class=False, static_prefix=
private EqtpathLinkage _EqtpathLinkage = new EqtpathLinkage();




// [DEBUG] Field: ADMIN_DATA_PATH, is_external=, is_static_class=False, static_prefix=
public const string ADMIN_DATA_PATH = "EQADMIN";




// [DEBUG] Field: USER_DATA_PATH, is_external=, is_static_class=False, static_prefix=
public const string USER_DATA_PATH = "EQUSER";




// [DEBUG] Field: MASTER_DATA_PATH, is_external=, is_static_class=False, static_prefix=
public const string MASTER_DATA_PATH = "EQMASTER";




// [DEBUG] Field: CgttempLinkage, is_external=, is_static_class=False, static_prefix=
private CgttempLinkage _CgttempLinkage = new CgttempLinkage();




// [DEBUG] Field: CGTTEMP_OPEN_OUTPUT, is_external=, is_static_class=False, static_prefix=
public const string CGTTEMP_OPEN_OUTPUT = "OO";




// [DEBUG] Field: CGTTEMP_WRITE, is_external=, is_static_class=False, static_prefix=
public const string CGTTEMP_WRITE = "WR";




// [DEBUG] Field: CGTTEMP_CLOSE, is_external=, is_static_class=False, static_prefix=
public const string CGTTEMP_CLOSE = "CL";




// [DEBUG] Field: CGTTEMP_OPEN_IO, is_external=, is_static_class=False, static_prefix=
public const string CGTTEMP_OPEN_IO = "IO";




// [DEBUG] Field: CGTTEMP_READ, is_external=, is_static_class=False, static_prefix=
public const string CGTTEMP_READ = "RD";




// [DEBUG] Field: CGTTEMP_DELETE_FILE, is_external=, is_static_class=False, static_prefix=
public const string CGTTEMP_DELETE_FILE = "DE";




// [DEBUG] Field: CGTTEMP_REWRITE, is_external=, is_static_class=False, static_prefix=
public const string CGTTEMP_REWRITE = "RW";




// [DEBUG] Field: CGTTEMP_START_NOT_LESS_THAN, is_external=, is_static_class=False, static_prefix=
public const string CGTTEMP_START_NOT_LESS_THAN = "SN";




// [DEBUG] Field: CGTTEMP_READ_ON_KEY, is_external=, is_static_class=False, static_prefix=
public const string CGTTEMP_READ_ON_KEY = "RK";




// [DEBUG] Field: WIds, is_external=, is_static_class=False, static_prefix=
private WIds _WIds = new WIds();




// [DEBUG] Field: HeaderIds, is_external=, is_static_class=False, static_prefix=
private HeaderIds _HeaderIds = new HeaderIds();




// [DEBUG] Field: BalanceIds, is_external=, is_static_class=False, static_prefix=
private BalanceIds _BalanceIds = new BalanceIds();




// [DEBUG] Field: AcquisitionIds, is_external=, is_static_class=False, static_prefix=
private AcquisitionIds _AcquisitionIds = new AcquisitionIds();




// [DEBUG] Field: DisposalIds, is_external=, is_static_class=False, static_prefix=
private DisposalIds _DisposalIds = new DisposalIds();




// [DEBUG] Field: Filler92, is_external=, is_static_class=False, static_prefix=
private Filler92 _Filler92 = new Filler92();




// [DEBUG] Field: MAX_NO_OF_COSTS, is_external=, is_static_class=False, static_prefix=
public const int MAX_NO_OF_COSTS = 200;




// [DEBUG] Field: MAX_MASTER_RECORD_LEN, is_external=, is_static_class=False, static_prefix=
public const int MAX_MASTER_RECORD_LEN = 16270;




// [DEBUG] Field: CgttempKeyPrefix, is_external=, is_static_class=False, static_prefix=
private string _CgttempKeyPrefix ="";




// [DEBUG] Field: HEADER_KEY_PREFIX, is_external=, is_static_class=False, static_prefix=
public const string HEADER_KEY_PREFIX = "HEADER ";




// [DEBUG] Field: TRANSACTION_KEY_PREFIX, is_external=, is_static_class=False, static_prefix=
public const string TRANSACTION_KEY_PREFIX = "TRANS  ";




// [DEBUG] Field: COST_KEY_PREFIX, is_external=, is_static_class=False, static_prefix=
public const string COST_KEY_PREFIX = "COST ";




// [DEBUG] Field: LMessage, is_external=, is_static_class=False, static_prefix=
private string _LMessage ="";




// [DEBUG] Field: LCaller, is_external=, is_static_class=False, static_prefix=
private string _LCaller ="";




// [DEBUG] Field: WsLongDates, is_external=, is_static_class=False, static_prefix=
private WsLongDates _WsLongDates = new WsLongDates();




// [DEBUG] Field: OffshoreRpt, is_external=, is_static_class=False, static_prefix=
private string _OffshoreRpt ="";




// [DEBUG] Field: OffshoreRptDd, is_external=, is_static_class=False, static_prefix=
private string _OffshoreRptDd ="";




// [DEBUG] Field: IlostReport, is_external=, is_static_class=False, static_prefix=
private string _IlostReport ="";




// [DEBUG] Field: IlosuReport, is_external=, is_static_class=False, static_prefix=
private string _IlosuReport ="";




// [DEBUG] Field: OffshoreRptFile, is_external=, is_static_class=False, static_prefix=
private OffshoreRptFile _OffshoreRptFile = new OffshoreRptFile();




// [DEBUG] Field: OffshoreRptFileDd, is_external=, is_static_class=False, static_prefix=
private OffshoreRptFileDd _OffshoreRptFileDd = new OffshoreRptFileDd();




// [DEBUG] Field: IlostReportFile, is_external=, is_static_class=False, static_prefix=
private IlostReportFile _IlostReportFile = new IlostReportFile();




// [DEBUG] Field: IlosuReportFile, is_external=, is_static_class=False, static_prefix=
private IlosuReportFile _IlosuReportFile = new IlosuReportFile();




// [DEBUG] Field: PoolRptRecord, is_external=, is_static_class=False, static_prefix=
private string _PoolRptRecord ="";




// [DEBUG] Field: ErrRec, is_external=, is_static_class=False, static_prefix=
private ErrRec _ErrRec = new ErrRec();




// [DEBUG] Field: WWarningFields, is_external=, is_static_class=False, static_prefix=
private WWarningFields _WWarningFields = new WWarningFields();




// [DEBUG] Field: ElcgmioLinkage1, is_external=, is_static_class=False, static_prefix=
private ElcgmioLinkage1 _ElcgmioLinkage1 = new ElcgmioLinkage1();




// [DEBUG] Field: FULL_COMP, is_external=, is_static_class=False, static_prefix=
public const int FULL_COMP = 6;




// [DEBUG] Field: PARTIAL_COMP, is_external=, is_static_class=False, static_prefix=
public const int PARTIAL_COMP = 9;




// [DEBUG] Field: SEDOL_WHATIF_COMP, is_external=, is_static_class=False, static_prefix=
public const int SEDOL_WHATIF_COMP = 7;




// [DEBUG] Field: SINGLE_SEDOL_COMP, is_external=, is_static_class=False, static_prefix=
public const int SINGLE_SEDOL_COMP = 7;




// [DEBUG] Field: YEAR_END_COMP, is_external=, is_static_class=False, static_prefix=
public const int YEAR_END_COMP = 8;




// [DEBUG] Field: ElcgmioLinkage2, is_external=, is_static_class=False, static_prefix=
private ElcgmioLinkage2 _ElcgmioLinkage2 = new ElcgmioLinkage2();




// [DEBUG] Field: CgtpriceLinkage, is_external=, is_static_class=False, static_prefix=
private CgtpriceLinkage _CgtpriceLinkage = new CgtpriceLinkage();




// [DEBUG] Field: WsIoPgm, is_external=, is_static_class=False, static_prefix=
private string _WsIoPgm ="ELCGTX24";




// [DEBUG] Field: WsCioLink, is_external=, is_static_class=False, static_prefix=
private WsCioLink _WsCioLink = new WsCioLink();




// [DEBUG] Field: WsFileFlags, is_external=, is_static_class=False, static_prefix=
private WsFileFlags _WsFileFlags = new WsFileFlags();




// [DEBUG] Field: ScheduleRecord, is_external=, is_static_class=False, static_prefix=
private ScheduleRecord _ScheduleRecord = new ScheduleRecord();




// [DEBUG] Field: CgtMasterIds, is_external=, is_static_class=False, static_prefix=
private string _CgtMasterIds ="";




// [DEBUG] Field: CgtMasterRecord, is_external=, is_static_class=False, static_prefix=
private CgtMasterRecord _CgtMasterRecord = new CgtMasterRecord();




// [DEBUG] Field: CgtMasterRecord01, is_external=, is_static_class=False, static_prefix=
private CgtMasterRecord01 _CgtMasterRecord01 = new CgtMasterRecord01();




// [DEBUG] Field: CgtMasterRecord02, is_external=, is_static_class=False, static_prefix=
private CgtMasterRecord02 _CgtMasterRecord02 = new CgtMasterRecord02();




// [DEBUG] Field: CgtMasterRecord03, is_external=, is_static_class=False, static_prefix=
private CgtMasterRecord03 _CgtMasterRecord03 = new CgtMasterRecord03();




// [DEBUG] Field: ParRecord, is_external=, is_static_class=False, static_prefix=
private ParRecord _ParRecord = new ParRecord();




// [DEBUG] Field: D13Record, is_external=, is_static_class=False, static_prefix=
private string _D13Record ="";




// [DEBUG] Field: D13SedolHeaderRecord, is_external=, is_static_class=False, static_prefix=
private D13SedolHeaderRecord _D13SedolHeaderRecord = new D13SedolHeaderRecord();




// [DEBUG] Field: D13BalAcqDispRecord, is_external=, is_static_class=False, static_prefix=
private D13BalAcqDispRecord _D13BalAcqDispRecord = new D13BalAcqDispRecord();




// [DEBUG] Field: WsRptItems, is_external=, is_static_class=False, static_prefix=
private WsRptItems _WsRptItems = new WsRptItems();




// [DEBUG] Field: WsIlostReportItems, is_external=, is_static_class=False, static_prefix=
private WsIlostReportItems _WsIlostReportItems = new WsIlostReportItems();




// [DEBUG] Field: WsIlosuReportItems, is_external=, is_static_class=False, static_prefix=
private WsIlosuReportItems _WsIlosuReportItems = new WsIlosuReportItems();




// [DEBUG] Field: WSpecials, is_external=, is_static_class=False, static_prefix=
private WSpecials _WSpecials = new WSpecials();




// [DEBUG] Field: WDdText, is_external=, is_static_class=False, static_prefix=
private string _WDdText ="";




// [DEBUG] Field: D43Record, is_external=, is_static_class=False, static_prefix=
private D43Record _D43Record = new D43Record();




// [DEBUG] Field: CgtfilesLinkage, is_external=, is_static_class=False, static_prefix=
private CgtfilesLinkage _CgtfilesLinkage = new CgtfilesLinkage();




// [DEBUG] Field: LFileRecordArea, is_external=, is_static_class=False, static_prefix=
private LFileRecordArea _LFileRecordArea = new LFileRecordArea();




// [DEBUG] Field: LValidDate, is_external=, is_static_class=False, static_prefix=
private LValidDate _LValidDate = new LValidDate();




// [DEBUG] Field: WsSaveCgt, is_external=, is_static_class=False, static_prefix=
private WsSaveCgt _WsSaveCgt = new WsSaveCgt();




// [DEBUG] Field: WsSaveIds, is_external=, is_static_class=False, static_prefix=
private string _WsSaveIds ="";




// [DEBUG] Field: WsSaveCgt2, is_external=, is_static_class=False, static_prefix=
private WsSaveCgt2 _WsSaveCgt2 = new WsSaveCgt2();




// [DEBUG] Field: WcConstants, is_external=, is_static_class=False, static_prefix=
private WcConstants _WcConstants = new WcConstants();




// [DEBUG] Field: WeErrorMessages, is_external=, is_static_class=False, static_prefix=
private WeErrorMessages _WeErrorMessages = new WeErrorMessages();




// [DEBUG] Field: WerRecordErrors, is_external=, is_static_class=False, static_prefix=
private WerRecordErrors _WerRecordErrors = new WerRecordErrors();




// [DEBUG] Field: WErrorOccurs, is_external=, is_static_class=False, static_prefix=
private int _WErrorOccurs =58;




// [DEBUG] Field: WiInterface, is_external=, is_static_class=False, static_prefix=
private WiInterface _WiInterface = new WiInterface();




// [DEBUG] Field: WlLiterals, is_external=, is_static_class=False, static_prefix=
private WlLiterals _WlLiterals = new WlLiterals();




// [DEBUG] Field: WsStorage, is_external=, is_static_class=False, static_prefix=
private WsStorage _WsStorage = new WsStorage();




// [DEBUG] Field: HOLDING_LEVEL_NOT_SET, is_external=, is_static_class=False, static_prefix=
public const int HOLDING_LEVEL_NOT_SET = -999;




// [DEBUG] Field: DEFERRED_LOSS_DD_LINE_NO, is_external=, is_static_class=False, static_prefix=
public const int DEFERRED_LOSS_DD_LINE_NO = 90000;




// [DEBUG] Field: DISPOSAL_DD_LINE_NO, is_external=, is_static_class=False, static_prefix=
public const int DISPOSAL_DD_LINE_NO = 95000;




// [DEBUG] Field: WtTables, is_external=, is_static_class=False, static_prefix=
private WtTables _WtTables = new WtTables();




// [DEBUG] Field: WsWiNormGtTranches, is_external=, is_static_class=False, static_prefix=
private WsWiNormGtTranches _WsWiNormGtTranches = new WsWiNormGtTranches();




// [DEBUG] Field: WtCompany, is_external=, is_static_class=False, static_prefix=
private WtCompany _WtCompany = new WtCompany();




// [DEBUG] Field: EqgetdtsLinkage, is_external=, is_static_class=False, static_prefix=
private EqgetdtsLinkage _EqgetdtsLinkage = new EqgetdtsLinkage();




// [DEBUG] Field: WtDescriptionsA, is_external=, is_static_class=False, static_prefix=
private WtDescriptionsA _WtDescriptionsA = new WtDescriptionsA();




// [DEBUG] Field: WtDescriptionsD, is_external=, is_static_class=False, static_prefix=
private WtDescriptionsD _WtDescriptionsD = new WtDescriptionsD();




// [DEBUG] Field: WtIndexTranches, is_external=, is_static_class=False, static_prefix=
private WtIndexTranches _WtIndexTranches = new WtIndexTranches();




// [DEBUG] Field: WtIndexCost, is_external=, is_static_class=False, static_prefix=
private WtIndexCost _WtIndexCost = new WtIndexCost();




// [DEBUG] Field: WtIndexIndexDates, is_external=, is_static_class=False, static_prefix=
private WtIndexIndexDates _WtIndexIndexDates = new WtIndexIndexDates();




// [DEBUG] Field: WtGtTranches, is_external=, is_static_class=False, static_prefix=
private WtGtTranches _WtGtTranches = new WtGtTranches();




// [DEBUG] Field: WtGtMatchedTranches, is_external=, is_static_class=False, static_prefix=
private WtGtMatchedTranches _WtGtMatchedTranches = new WtGtMatchedTranches();




// [DEBUG] Field: WtGtMatchedBargainDates, is_external=, is_static_class=False, static_prefix=
private WtGtMatchedBargainDates _WtGtMatchedBargainDates = new WtGtMatchedBargainDates();




// [DEBUG] Field: WtIndexEligibleTranches, is_external=, is_static_class=False, static_prefix=
private WtIndexEligibleTranches _WtIndexEligibleTranches = new WtIndexEligibleTranches();




// [DEBUG] Field: WtKeysUnprocessed, is_external=, is_static_class=False, static_prefix=
private WtKeysUnprocessed _WtKeysUnprocessed = new WtKeysUnprocessed();




// [DEBUG] Field: WtPartProcessedKeys, is_external=, is_static_class=False, static_prefix=
private WtPartProcessedKeys _WtPartProcessedKeys = new WtPartProcessedKeys();




// [DEBUG] Field: WtRetailPriceIndexes, is_external=, is_static_class=False, static_prefix=
private WtRetailPriceIndexes _WtRetailPriceIndexes = new WtRetailPriceIndexes();




// [DEBUG] Field: WtSedolHeaders, is_external=, is_static_class=False, static_prefix=
private WtSedolHeaders _WtSedolHeaders = new WtSedolHeaders();




// [DEBUG] Field: BUSINESS_ASSET, is_external=, is_static_class=False, static_prefix=
public const string BUSINESS_ASSET = "B";




// [DEBUG] Field: NON_BUSINESS_ASSET, is_external=, is_static_class=False, static_prefix=
public const string NON_BUSINESS_ASSET = "N";




// [DEBUG] Field: MIXED_USE_ASSET, is_external=, is_static_class=False, static_prefix=
public const string MIXED_USE_ASSET = "M";




// [DEBUG] Field: NO_ASSET_USAGE_OVERRIDE, is_external=, is_static_class=False, static_prefix=
public const string NO_ASSET_USAGE_OVERRIDE = " ";




// [DEBUG] Field: WtTransactions, is_external=, is_static_class=False, static_prefix=
private WtTransactions _WtTransactions = new WtTransactions();




// [DEBUG] Field: SORT_DISP_START_VALUE, is_external=, is_static_class=False, static_prefix=
public const int SORT_DISP_START_VALUE = 400;




// [DEBUG] Field: SAME_DAY_BA_SPACE_FACTOR, is_external=, is_static_class=False, static_prefix=
public const int SAME_DAY_BA_SPACE_FACTOR = 1;




// [DEBUG] Field: SAME_DAY_DISP_SPACE_FACTOR, is_external=, is_static_class=False, static_prefix=
public const int SAME_DAY_DISP_SPACE_FACTOR = 3;




// [DEBUG] Field: SAME_DAY_ACQN_SPACE_FACTOR, is_external=, is_static_class=False, static_prefix=
public const int SAME_DAY_ACQN_SPACE_FACTOR = 6;




// [DEBUG] Field: WttcCosts, is_external=, is_static_class=False, static_prefix=
private WttcCosts _WttcCosts = new WttcCosts();




// [DEBUG] Field: WtUnits, is_external=, is_static_class=False, static_prefix=
private WtUnits _WtUnits = new WtUnits();




// [DEBUG] Field: WtAssetUsageCalendar, is_external=, is_static_class=False, static_prefix=
private WtAssetUsageCalendar _WtAssetUsageCalendar = new WtAssetUsageCalendar();




// [DEBUG] Field: WCalendarWorkFields, is_external=, is_static_class=False, static_prefix=
private WCalendarWorkFields _WCalendarWorkFields = new WCalendarWorkFields();




// [DEBUG] Field: WtFundRecords, is_external=, is_static_class=False, static_prefix=
private WtFundRecords _WtFundRecords = new WtFundRecords();




// [DEBUG] Field: WtRestructureDates, is_external=, is_static_class=False, static_prefix=
private WtRestructureDates _WtRestructureDates = new WtRestructureDates();




// [DEBUG] Field: WtRestructureHoldings, is_external=, is_static_class=False, static_prefix=
private WtRestructureHoldings _WtRestructureHoldings = new WtRestructureHoldings();




// [DEBUG] Field: NewFields, is_external=, is_static_class=False, static_prefix=
private NewFields _NewFields = new NewFields();




// [DEBUG] Field: WsDispEyMsg, is_external=, is_static_class=False, static_prefix=
private WsDispEyMsg _WsDispEyMsg = new WsDispEyMsg();




// [DEBUG] Field: WsRunControl, is_external=, is_static_class=False, static_prefix=
private WsRunControl _WsRunControl = new WsRunControl();




// [DEBUG] Field: LForceWrite, is_external=, is_static_class=False, static_prefix=
private string _LForceWrite ="";


// 88-level condition checks for LForceWrite
public bool IsForceWrite()
{
    if (this._LForceWrite == "'Y'") return true;
    return false;
}
public bool IsDontForceWrite()
{
    if (this._LForceWrite == "'N'") return true;
    return false;
}


// [DEBUG] Field: WtProcessedFundRecords, is_external=, is_static_class=False, static_prefix=
private WtProcessedFundRecords _WtProcessedFundRecords = new WtProcessedFundRecords();




// [DEBUG] Field: WsIrishCgtFields, is_external=, is_static_class=False, static_prefix=
private WsIrishCgtFields _WsIrishCgtFields = new WsIrishCgtFields();




// [DEBUG] Field: WtNdlPurchasesTable, is_external=, is_static_class=False, static_prefix=
private WtNdlPurchasesTable _WtNdlPurchasesTable = new WtNdlPurchasesTable();




// [DEBUG] Field: WtNdlSalesTable, is_external=, is_static_class=False, static_prefix=
private WtNdlSalesTable _WtNdlSalesTable = new WtNdlSalesTable();




// [DEBUG] Field: WtRpiMessages, is_external=, is_static_class=False, static_prefix=
private WtRpiMessages _WtRpiMessages = new WtRpiMessages();




// [DEBUG] Field: WFundProcessed, is_external=, is_static_class=False, static_prefix=
private string _WFundProcessed ="";


// 88-level condition checks for WFundProcessed
public bool IsFundIsProcessed()
{
    if (this._WFundProcessed == "'Y'") return true;
    return false;
}
public bool IsFundNotProcessed()
{
    if (this._WFundProcessed == "'N'") return true;
    return false;
}


// [DEBUG] Field: WSearchFundCode, is_external=, is_static_class=False, static_prefix=
private string _WSearchFundCode ="";




// [DEBUG] Field: WTransCatDerivatives, is_external=, is_static_class=False, static_prefix=
private string _WTransCatDerivatives ="";


// 88-level condition checks for WTransCatDerivatives
public bool IsWDerivatives()
{
    if (this._WTransCatDerivatives == "'WP'") return true;
    return false;
}


// [DEBUG] Field: TempDataLinkage, is_external=, is_static_class=False, static_prefix=
private TempDataLinkage _TempDataLinkage = new TempDataLinkage();




// [DEBUG] Field: TimingLinkage, is_external=, is_static_class=False, static_prefix=
private TimingLinkage _TimingLinkage = new TimingLinkage();




// [DEBUG] Field: NEW_DERIVATIVE_EXPORT_FORMAT, is_external=, is_static_class=False, static_prefix=
public const string NEW_DERIVATIVE_EXPORT_FORMAT = "NewDerivativeExportFormat";




// [DEBUG] Field: TEMP_FILE_DISK, is_external=, is_static_class=False, static_prefix=
public const string TEMP_FILE_DISK = "TempFileToDisk";




// [DEBUG] Field: ILG_COMPUTATION_START_DATE, is_external=, is_static_class=False, static_prefix=
public const string ILG_COMPUTATION_START_DATE = "IndexLinkedGiltsComputationStartDate";




// [DEBUG] Field: ILG_COMPUTATION_PERIOD_END_REVALUATIONS, is_external=, is_static_class=False, static_prefix=
public const string ILG_COMPUTATION_PERIOD_END_REVALUATIONS = "IndexLinkedGiltsComputationPeriodEndRevaluations";




// [DEBUG] Field: ASSET_TOTALS_REALISED_SCHEDULE, is_external=, is_static_class=False, static_prefix=
public const string ASSET_TOTALS_REALISED_SCHEDULE = "AssetTotalsOnRealisedSchedule";




// [DEBUG] Field: ASSET_TOTALS_REALISED_BOND_SCHEDULE, is_external=, is_static_class=False, static_prefix=
public const string ASSET_TOTALS_REALISED_BOND_SCHEDULE = "AssetTotalsOnRealisedBondSchedule";




// [DEBUG] Field: WConfigItem, is_external=, is_static_class=False, static_prefix=
private string _WConfigItem ="";




// [DEBUG] Field: CheckCatLinkage, is_external=, is_static_class=False, static_prefix=
private CheckCatLinkage _CheckCatLinkage = new CheckCatLinkage();




// [DEBUG] Field: WDisplayCategory, is_external=, is_static_class=False, static_prefix=
private string _WDisplayCategory ="";




// [DEBUG] Field: WsSaveScheduleRa, is_external=, is_static_class=False, static_prefix=
private WsSaveScheduleRa _WsSaveScheduleRa = new WsSaveScheduleRa();




// [DEBUG] Field: WDerivativeFields, is_external=, is_static_class=False, static_prefix=
private WDerivativeFields _WDerivativeFields = new WDerivativeFields();




// [DEBUG] Field: WTotalToMaturityDate, is_external=, is_static_class=False, static_prefix=
private decimal _WTotalToMaturityDate =0;




// [DEBUG] Field: WOptionPremium, is_external=, is_static_class=False, static_prefix=
private WOptionPremium _WOptionPremium = new WOptionPremium();




// Getter and Setter methods

// Standard Getter
public string GetWNotNumeric()
{
    return _WNotNumeric;
}

// Standard Setter
public void SetWNotNumeric(string value)
{
    _WNotNumeric = value;
}

// Get<>AsString()
public string GetWNotNumericAsString()
{
    return _WNotNumeric.PadRight(0);
}

// Set<>AsString()
public void SetWNotNumericAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WNotNumeric = value;
}

// Standard Getter
public int GetWWttcS()
{
    return _WWttcS;
}

// Standard Setter
public void SetWWttcS(int value)
{
    _WWttcS = value;
}

// Get<>AsString()
public string GetWWttcSAsString()
{
    return _WWttcS.ToString().PadLeft(5, '0');
}

// Set<>AsString()
public void SetWWttcSAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WWttcS = parsed;
}

// Standard Getter
public string GetWIlgComputationStartDate()
{
    return _WIlgComputationStartDate;
}

// Standard Setter
public void SetWIlgComputationStartDate(string value)
{
    _WIlgComputationStartDate = value;
}

// Get<>AsString()
public string GetWIlgComputationStartDateAsString()
{
    return _WIlgComputationStartDate.PadRight(0);
}

// Set<>AsString()
public void SetWIlgComputationStartDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WIlgComputationStartDate = value;
}

// Standard Getter
public string GetWTempFileDisk()
{
    return _WTempFileDisk;
}

// Standard Setter
public void SetWTempFileDisk(string value)
{
    _WTempFileDisk = value;
}

// Get<>AsString()
public string GetWTempFileDiskAsString()
{
    return _WTempFileDisk.PadRight(5);
}

// Set<>AsString()
public void SetWTempFileDiskAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WTempFileDisk = value;
}

// Standard Getter
public string GetWIlgPeriodEndRevaluations()
{
    return _WIlgPeriodEndRevaluations;
}

// Standard Setter
public void SetWIlgPeriodEndRevaluations(string value)
{
    _WIlgPeriodEndRevaluations = value;
}

// Get<>AsString()
public string GetWIlgPeriodEndRevaluationsAsString()
{
    return _WIlgPeriodEndRevaluations.PadRight(5);
}

// Set<>AsString()
public void SetWIlgPeriodEndRevaluationsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WIlgPeriodEndRevaluations = value;
}

// Standard Getter
public string GetWReindexation()
{
    return _WReindexation;
}

// Standard Setter
public void SetWReindexation(string value)
{
    _WReindexation = value;
}

// Get<>AsString()
public string GetWReindexationAsString()
{
    return _WReindexation.PadRight(0);
}

// Set<>AsString()
public void SetWReindexationAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WReindexation = value;
}

// Standard Getter
public int GetWf12BargainDateFa12()
{
    return _WF12BargainDateFa12;
}

// Standard Setter
public void SetWf12BargainDateFa12(int value)
{
    _WF12BargainDateFa12 = value;
}

// Get<>AsString()
public string GetWf12BargainDateFa12AsString()
{
    return _WF12BargainDateFa12.ToString().PadLeft(8, '0');
}

// Set<>AsString()
public void SetWf12BargainDateFa12AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WF12BargainDateFa12 = parsed;
}

// Standard Getter
public RetailPriceIndexRecord GetRetailPriceIndexRecord()
{
    return _RetailPriceIndexRecord;
}

// Standard Setter
public void SetRetailPriceIndexRecord(RetailPriceIndexRecord value)
{
    _RetailPriceIndexRecord = value;
}

// Get<>AsString()
public string GetRetailPriceIndexRecordAsString()
{
    return _RetailPriceIndexRecord != null ? _RetailPriceIndexRecord.GetRetailPriceIndexRecordAsString() : "";
}

// Set<>AsString()
public void SetRetailPriceIndexRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_RetailPriceIndexRecord == null)
    {
        _RetailPriceIndexRecord = new RetailPriceIndexRecord();
    }
    _RetailPriceIndexRecord.SetRetailPriceIndexRecordAsString(value);
}

// Standard Getter
public CompanyFundRecord GetCompanyFundRecord()
{
    return _CompanyFundRecord;
}

// Standard Setter
public void SetCompanyFundRecord(CompanyFundRecord value)
{
    _CompanyFundRecord = value;
}

// Get<>AsString()
public string GetCompanyFundRecordAsString()
{
    return _CompanyFundRecord != null ? _CompanyFundRecord.GetCompanyFundRecordAsString() : "";
}

// Set<>AsString()
public void SetCompanyFundRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CompanyFundRecord == null)
    {
        _CompanyFundRecord = new CompanyFundRecord();
    }
    _CompanyFundRecord.SetCompanyFundRecordAsString(value);
}

// Standard Getter
public string GetWsSaveCompanyFundRecord()
{
    return _WsSaveCompanyFundRecord;
}

// Standard Setter
public void SetWsSaveCompanyFundRecord(string value)
{
    _WsSaveCompanyFundRecord = value;
}

// Get<>AsString()
public string GetWsSaveCompanyFundRecordAsString()
{
    return _WsSaveCompanyFundRecord.PadRight(500);
}

// Set<>AsString()
public void SetWsSaveCompanyFundRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsSaveCompanyFundRecord = value;
}

// Standard Getter
public D94Record GetD94Record()
{
    return _D94Record;
}

// Standard Setter
public void SetD94Record(D94Record value)
{
    _D94Record = value;
}

// Get<>AsString()
public string GetD94RecordAsString()
{
    return _D94Record != null ? _D94Record.GetD94RecordAsString() : "";
}

// Set<>AsString()
public void SetD94RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D94Record == null)
    {
        _D94Record = new D94Record();
    }
    _D94Record.SetD94RecordAsString(value);
}

// Standard Getter
public D133Record GetD133Record()
{
    return _D133Record;
}

// Standard Setter
public void SetD133Record(D133Record value)
{
    _D133Record = value;
}

// Get<>AsString()
public string GetD133RecordAsString()
{
    return _D133Record != null ? _D133Record.GetD133RecordAsString() : "";
}

// Set<>AsString()
public void SetD133RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D133Record == null)
    {
        _D133Record = new D133Record();
    }
    _D133Record.SetD133RecordAsString(value);
}

// Standard Getter
public D4Record GetD4Record()
{
    return _D4Record;
}

// Standard Setter
public void SetD4Record(D4Record value)
{
    _D4Record = value;
}

// Get<>AsString()
public string GetD4RecordAsString()
{
    return _D4Record != null ? _D4Record.GetD4RecordAsString() : "";
}

// Set<>AsString()
public void SetD4RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D4Record == null)
    {
        _D4Record = new D4Record();
    }
    _D4Record.SetD4RecordAsString(value);
}

// Standard Getter
public D153Record GetD153Record()
{
    return _D153Record;
}

// Standard Setter
public void SetD153Record(D153Record value)
{
    _D153Record = value;
}

// Get<>AsString()
public string GetD153RecordAsString()
{
    return _D153Record != null ? _D153Record.GetD153RecordAsString() : "";
}

// Set<>AsString()
public void SetD153RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D153Record == null)
    {
        _D153Record = new D153Record();
    }
    _D153Record.SetD153RecordAsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate1 GetCgtdate2LinkageDate1()
{
    return _Cgtdate2LinkageDate1;
}

// Standard Setter
public void SetCgtdate2LinkageDate1(Cgtdate2LinkageDate1 value)
{
    _Cgtdate2LinkageDate1 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate1AsString()
{
    return _Cgtdate2LinkageDate1 != null ? _Cgtdate2LinkageDate1.GetCgtdate2LinkageDate1AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate1 == null)
    {
        _Cgtdate2LinkageDate1 = new Cgtdate2LinkageDate1();
    }
    _Cgtdate2LinkageDate1.SetCgtdate2LinkageDate1AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate2 GetCgtdate2LinkageDate2()
{
    return _Cgtdate2LinkageDate2;
}

// Standard Setter
public void SetCgtdate2LinkageDate2(Cgtdate2LinkageDate2 value)
{
    _Cgtdate2LinkageDate2 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate2AsString()
{
    return _Cgtdate2LinkageDate2 != null ? _Cgtdate2LinkageDate2.GetCgtdate2LinkageDate2AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate2 == null)
    {
        _Cgtdate2LinkageDate2 = new Cgtdate2LinkageDate2();
    }
    _Cgtdate2LinkageDate2.SetCgtdate2LinkageDate2AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate3 GetCgtdate2LinkageDate3()
{
    return _Cgtdate2LinkageDate3;
}

// Standard Setter
public void SetCgtdate2LinkageDate3(Cgtdate2LinkageDate3 value)
{
    _Cgtdate2LinkageDate3 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate3AsString()
{
    return _Cgtdate2LinkageDate3 != null ? _Cgtdate2LinkageDate3.GetCgtdate2LinkageDate3AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate3 == null)
    {
        _Cgtdate2LinkageDate3 = new Cgtdate2LinkageDate3();
    }
    _Cgtdate2LinkageDate3.SetCgtdate2LinkageDate3AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate4 GetCgtdate2LinkageDate4()
{
    return _Cgtdate2LinkageDate4;
}

// Standard Setter
public void SetCgtdate2LinkageDate4(Cgtdate2LinkageDate4 value)
{
    _Cgtdate2LinkageDate4 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate4AsString()
{
    return _Cgtdate2LinkageDate4 != null ? _Cgtdate2LinkageDate4.GetCgtdate2LinkageDate4AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate4 == null)
    {
        _Cgtdate2LinkageDate4 = new Cgtdate2LinkageDate4();
    }
    _Cgtdate2LinkageDate4.SetCgtdate2LinkageDate4AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate5 GetCgtdate2LinkageDate5()
{
    return _Cgtdate2LinkageDate5;
}

// Standard Setter
public void SetCgtdate2LinkageDate5(Cgtdate2LinkageDate5 value)
{
    _Cgtdate2LinkageDate5 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate5AsString()
{
    return _Cgtdate2LinkageDate5 != null ? _Cgtdate2LinkageDate5.GetCgtdate2LinkageDate5AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate5AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate5 == null)
    {
        _Cgtdate2LinkageDate5 = new Cgtdate2LinkageDate5();
    }
    _Cgtdate2LinkageDate5.SetCgtdate2LinkageDate5AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate6 GetCgtdate2LinkageDate6()
{
    return _Cgtdate2LinkageDate6;
}

// Standard Setter
public void SetCgtdate2LinkageDate6(Cgtdate2LinkageDate6 value)
{
    _Cgtdate2LinkageDate6 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate6AsString()
{
    return _Cgtdate2LinkageDate6 != null ? _Cgtdate2LinkageDate6.GetCgtdate2LinkageDate6AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate6 == null)
    {
        _Cgtdate2LinkageDate6 = new Cgtdate2LinkageDate6();
    }
    _Cgtdate2LinkageDate6.SetCgtdate2LinkageDate6AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate7 GetCgtdate2LinkageDate7()
{
    return _Cgtdate2LinkageDate7;
}

// Standard Setter
public void SetCgtdate2LinkageDate7(Cgtdate2LinkageDate7 value)
{
    _Cgtdate2LinkageDate7 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate7AsString()
{
    return _Cgtdate2LinkageDate7 != null ? _Cgtdate2LinkageDate7.GetCgtdate2LinkageDate7AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate7 == null)
    {
        _Cgtdate2LinkageDate7 = new Cgtdate2LinkageDate7();
    }
    _Cgtdate2LinkageDate7.SetCgtdate2LinkageDate7AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate8 GetCgtdate2LinkageDate8()
{
    return _Cgtdate2LinkageDate8;
}

// Standard Setter
public void SetCgtdate2LinkageDate8(Cgtdate2LinkageDate8 value)
{
    _Cgtdate2LinkageDate8 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate8AsString()
{
    return _Cgtdate2LinkageDate8 != null ? _Cgtdate2LinkageDate8.GetCgtdate2LinkageDate8AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate8 == null)
    {
        _Cgtdate2LinkageDate8 = new Cgtdate2LinkageDate8();
    }
    _Cgtdate2LinkageDate8.SetCgtdate2LinkageDate8AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate9 GetCgtdate2LinkageDate9()
{
    return _Cgtdate2LinkageDate9;
}

// Standard Setter
public void SetCgtdate2LinkageDate9(Cgtdate2LinkageDate9 value)
{
    _Cgtdate2LinkageDate9 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate9AsString()
{
    return _Cgtdate2LinkageDate9 != null ? _Cgtdate2LinkageDate9.GetCgtdate2LinkageDate9AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate9 == null)
    {
        _Cgtdate2LinkageDate9 = new Cgtdate2LinkageDate9();
    }
    _Cgtdate2LinkageDate9.SetCgtdate2LinkageDate9AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate10 GetCgtdate2LinkageDate10()
{
    return _Cgtdate2LinkageDate10;
}

// Standard Setter
public void SetCgtdate2LinkageDate10(Cgtdate2LinkageDate10 value)
{
    _Cgtdate2LinkageDate10 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate10AsString()
{
    return _Cgtdate2LinkageDate10 != null ? _Cgtdate2LinkageDate10.GetCgtdate2LinkageDate10AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate10AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate10 == null)
    {
        _Cgtdate2LinkageDate10 = new Cgtdate2LinkageDate10();
    }
    _Cgtdate2LinkageDate10.SetCgtdate2LinkageDate10AsString(value);
}

// Standard Getter
public CommonLinkage GetCommonLinkage()
{
    return _CommonLinkage;
}

// Standard Setter
public void SetCommonLinkage(CommonLinkage value)
{
    _CommonLinkage = value;
}

// Get<>AsString()
public string GetCommonLinkageAsString()
{
    return _CommonLinkage != null ? _CommonLinkage.GetCommonLinkageAsString() : "";
}

// Set<>AsString()
public void SetCommonLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CommonLinkage == null)
    {
        _CommonLinkage = new CommonLinkage();
    }
    _CommonLinkage.SetCommonLinkageAsString(value);
}

// Standard Getter
public Cgtdate3Linkage GetCgtdate3Linkage()
{
    return _Cgtdate3Linkage;
}

// Standard Setter
public void SetCgtdate3Linkage(Cgtdate3Linkage value)
{
    _Cgtdate3Linkage = value;
}

// Get<>AsString()
public string GetCgtdate3LinkageAsString()
{
    return _Cgtdate3Linkage != null ? _Cgtdate3Linkage.GetCgtdate3LinkageAsString() : "";
}

// Set<>AsString()
public void SetCgtdate3LinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate3Linkage == null)
    {
        _Cgtdate3Linkage = new Cgtdate3Linkage();
    }
    _Cgtdate3Linkage.SetCgtdate3LinkageAsString(value);
}

// Standard Getter
public EqtpathLinkage GetEqtpathLinkage()
{
    return _EqtpathLinkage;
}

// Standard Setter
public void SetEqtpathLinkage(EqtpathLinkage value)
{
    _EqtpathLinkage = value;
}

// Get<>AsString()
public string GetEqtpathLinkageAsString()
{
    return _EqtpathLinkage != null ? _EqtpathLinkage.GetEqtpathLinkageAsString() : "";
}

// Set<>AsString()
public void SetEqtpathLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_EqtpathLinkage == null)
    {
        _EqtpathLinkage = new EqtpathLinkage();
    }
    _EqtpathLinkage.SetEqtpathLinkageAsString(value);
}

// Standard Getter
public CgttempLinkage GetCgttempLinkage()
{
    return _CgttempLinkage;
}

// Standard Setter
public void SetCgttempLinkage(CgttempLinkage value)
{
    _CgttempLinkage = value;
}

// Get<>AsString()
public string GetCgttempLinkageAsString()
{
    return _CgttempLinkage != null ? _CgttempLinkage.GetCgttempLinkageAsString() : "";
}

// Set<>AsString()
public void SetCgttempLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgttempLinkage == null)
    {
        _CgttempLinkage = new CgttempLinkage();
    }
    _CgttempLinkage.SetCgttempLinkageAsString(value);
}

// Standard Getter
public WIds GetWIds()
{
    return _WIds;
}

// Standard Setter
public void SetWIds(WIds value)
{
    _WIds = value;
}

// Get<>AsString()
public string GetWIdsAsString()
{
    return _WIds != null ? _WIds.GetWIdsAsString() : "";
}

// Set<>AsString()
public void SetWIdsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WIds == null)
    {
        _WIds = new WIds();
    }
    _WIds.SetWIdsAsString(value);
}

// Standard Getter
public HeaderIds GetHeaderIds()
{
    return _HeaderIds;
}

// Standard Setter
public void SetHeaderIds(HeaderIds value)
{
    _HeaderIds = value;
}

// Get<>AsString()
public string GetHeaderIdsAsString()
{
    return _HeaderIds != null ? _HeaderIds.GetHeaderIdsAsString() : "";
}

// Set<>AsString()
public void SetHeaderIdsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_HeaderIds == null)
    {
        _HeaderIds = new HeaderIds();
    }
    _HeaderIds.SetHeaderIdsAsString(value);
}

// Standard Getter
public BalanceIds GetBalanceIds()
{
    return _BalanceIds;
}

// Standard Setter
public void SetBalanceIds(BalanceIds value)
{
    _BalanceIds = value;
}

// Get<>AsString()
public string GetBalanceIdsAsString()
{
    return _BalanceIds != null ? _BalanceIds.GetBalanceIdsAsString() : "";
}

// Set<>AsString()
public void SetBalanceIdsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_BalanceIds == null)
    {
        _BalanceIds = new BalanceIds();
    }
    _BalanceIds.SetBalanceIdsAsString(value);
}

// Standard Getter
public AcquisitionIds GetAcquisitionIds()
{
    return _AcquisitionIds;
}

// Standard Setter
public void SetAcquisitionIds(AcquisitionIds value)
{
    _AcquisitionIds = value;
}

// Get<>AsString()
public string GetAcquisitionIdsAsString()
{
    return _AcquisitionIds != null ? _AcquisitionIds.GetAcquisitionIdsAsString() : "";
}

// Set<>AsString()
public void SetAcquisitionIdsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_AcquisitionIds == null)
    {
        _AcquisitionIds = new AcquisitionIds();
    }
    _AcquisitionIds.SetAcquisitionIdsAsString(value);
}

// Standard Getter
public DisposalIds GetDisposalIds()
{
    return _DisposalIds;
}

// Standard Setter
public void SetDisposalIds(DisposalIds value)
{
    _DisposalIds = value;
}

// Get<>AsString()
public string GetDisposalIdsAsString()
{
    return _DisposalIds != null ? _DisposalIds.GetDisposalIdsAsString() : "";
}

// Set<>AsString()
public void SetDisposalIdsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_DisposalIds == null)
    {
        _DisposalIds = new DisposalIds();
    }
    _DisposalIds.SetDisposalIdsAsString(value);
}

// Standard Getter
public Filler92 GetFiller92()
{
    return _Filler92;
}

// Standard Setter
public void SetFiller92(Filler92 value)
{
    _Filler92 = value;
}

// Get<>AsString()
public string GetFiller92AsString()
{
    return _Filler92 != null ? _Filler92.GetFiller92AsString() : "";
}

// Set<>AsString()
public void SetFiller92AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler92 == null)
    {
        _Filler92 = new Filler92();
    }
    _Filler92.SetFiller92AsString(value);
}

// Standard Getter
public string GetCgttempKeyPrefix()
{
    return _CgttempKeyPrefix;
}

// Standard Setter
public void SetCgttempKeyPrefix(string value)
{
    _CgttempKeyPrefix = value;
}

// Get<>AsString()
public string GetCgttempKeyPrefixAsString()
{
    return _CgttempKeyPrefix.PadRight(0);
}

// Set<>AsString()
public void SetCgttempKeyPrefixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgttempKeyPrefix = value;
}

// Standard Getter
public string GetLMessage()
{
    return _LMessage;
}

// Standard Setter
public void SetLMessage(string value)
{
    _LMessage = value;
}

// Get<>AsString()
public string GetLMessageAsString()
{
    return _LMessage.PadRight(100);
}

// Set<>AsString()
public void SetLMessageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LMessage = value;
}

// Standard Getter
public string GetLCaller()
{
    return _LCaller;
}

// Standard Setter
public void SetLCaller(string value)
{
    _LCaller = value;
}

// Get<>AsString()
public string GetLCallerAsString()
{
    return _LCaller.PadRight(100);
}

// Set<>AsString()
public void SetLCallerAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LCaller = value;
}

// Standard Getter
public WsLongDates GetWsLongDates()
{
    return _WsLongDates;
}

// Standard Setter
public void SetWsLongDates(WsLongDates value)
{
    _WsLongDates = value;
}

// Get<>AsString()
public string GetWsLongDatesAsString()
{
    return _WsLongDates != null ? _WsLongDates.GetWsLongDatesAsString() : "";
}

// Set<>AsString()
public void SetWsLongDatesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsLongDates == null)
    {
        _WsLongDates = new WsLongDates();
    }
    _WsLongDates.SetWsLongDatesAsString(value);
}

// Standard Getter
public string GetOffshoreRpt()
{
    return _OffshoreRpt;
}

// Standard Setter
public void SetOffshoreRpt(string value)
{
    _OffshoreRpt = value;
}

// Get<>AsString()
public string GetOffshoreRptAsString()
{
    return _OffshoreRpt.PadRight(256);
}

// Set<>AsString()
public void SetOffshoreRptAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _OffshoreRpt = value;
}

// Standard Getter
public string GetOffshoreRptDd()
{
    return _OffshoreRptDd;
}

// Standard Setter
public void SetOffshoreRptDd(string value)
{
    _OffshoreRptDd = value;
}

// Get<>AsString()
public string GetOffshoreRptDdAsString()
{
    return _OffshoreRptDd.PadRight(256);
}

// Set<>AsString()
public void SetOffshoreRptDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _OffshoreRptDd = value;
}

// Standard Getter
public string GetIlostReport()
{
    return _IlostReport;
}

// Standard Setter
public void SetIlostReport(string value)
{
    _IlostReport = value;
}

// Get<>AsString()
public string GetIlostReportAsString()
{
    return _IlostReport.PadRight(256);
}

// Set<>AsString()
public void SetIlostReportAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _IlostReport = value;
}

// Standard Getter
public string GetIlosuReport()
{
    return _IlosuReport;
}

// Standard Setter
public void SetIlosuReport(string value)
{
    _IlosuReport = value;
}

// Get<>AsString()
public string GetIlosuReportAsString()
{
    return _IlosuReport.PadRight(256);
}

// Set<>AsString()
public void SetIlosuReportAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _IlosuReport = value;
}

// Standard Getter
public OffshoreRptFile GetOffshoreRptFile()
{
    return _OffshoreRptFile;
}

// Standard Setter
public void SetOffshoreRptFile(OffshoreRptFile value)
{
    _OffshoreRptFile = value;
}

// Get<>AsString()
public string GetOffshoreRptFileAsString()
{
    return _OffshoreRptFile != null ? _OffshoreRptFile.GetOffshoreRptFileAsString() : "";
}

// Set<>AsString()
public void SetOffshoreRptFileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_OffshoreRptFile == null)
    {
        _OffshoreRptFile = new OffshoreRptFile();
    }
    _OffshoreRptFile.SetOffshoreRptFileAsString(value);
}

// Standard Getter
public OffshoreRptFileDd GetOffshoreRptFileDd()
{
    return _OffshoreRptFileDd;
}

// Standard Setter
public void SetOffshoreRptFileDd(OffshoreRptFileDd value)
{
    _OffshoreRptFileDd = value;
}

// Get<>AsString()
public string GetOffshoreRptFileDdAsString()
{
    return _OffshoreRptFileDd != null ? _OffshoreRptFileDd.GetOffshoreRptFileDdAsString() : "";
}

// Set<>AsString()
public void SetOffshoreRptFileDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_OffshoreRptFileDd == null)
    {
        _OffshoreRptFileDd = new OffshoreRptFileDd();
    }
    _OffshoreRptFileDd.SetOffshoreRptFileDdAsString(value);
}

// Standard Getter
public IlostReportFile GetIlostReportFile()
{
    return _IlostReportFile;
}

// Standard Setter
public void SetIlostReportFile(IlostReportFile value)
{
    _IlostReportFile = value;
}

// Get<>AsString()
public string GetIlostReportFileAsString()
{
    return _IlostReportFile != null ? _IlostReportFile.GetIlostReportFileAsString() : "";
}

// Set<>AsString()
public void SetIlostReportFileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_IlostReportFile == null)
    {
        _IlostReportFile = new IlostReportFile();
    }
    _IlostReportFile.SetIlostReportFileAsString(value);
}

// Standard Getter
public IlosuReportFile GetIlosuReportFile()
{
    return _IlosuReportFile;
}

// Standard Setter
public void SetIlosuReportFile(IlosuReportFile value)
{
    _IlosuReportFile = value;
}

// Get<>AsString()
public string GetIlosuReportFileAsString()
{
    return _IlosuReportFile != null ? _IlosuReportFile.GetIlosuReportFileAsString() : "";
}

// Set<>AsString()
public void SetIlosuReportFileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_IlosuReportFile == null)
    {
        _IlosuReportFile = new IlosuReportFile();
    }
    _IlosuReportFile.SetIlosuReportFileAsString(value);
}

// Standard Getter
public string GetPoolRptRecord()
{
    return _PoolRptRecord;
}

// Standard Setter
public void SetPoolRptRecord(string value)
{
    _PoolRptRecord = value;
}

// Get<>AsString()
public string GetPoolRptRecordAsString()
{
    return _PoolRptRecord.PadRight(180);
}

// Set<>AsString()
public void SetPoolRptRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _PoolRptRecord = value;
}

// Standard Getter
public ErrRec GetErrRec()
{
    return _ErrRec;
}

// Standard Setter
public void SetErrRec(ErrRec value)
{
    _ErrRec = value;
}

// Get<>AsString()
public string GetErrRecAsString()
{
    return _ErrRec != null ? _ErrRec.GetErrRecAsString() : "";
}

// Set<>AsString()
public void SetErrRecAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ErrRec == null)
    {
        _ErrRec = new ErrRec();
    }
    _ErrRec.SetErrRecAsString(value);
}

// Standard Getter
public WWarningFields GetWWarningFields()
{
    return _WWarningFields;
}

// Standard Setter
public void SetWWarningFields(WWarningFields value)
{
    _WWarningFields = value;
}

// Get<>AsString()
public string GetWWarningFieldsAsString()
{
    return _WWarningFields != null ? _WWarningFields.GetWWarningFieldsAsString() : "";
}

// Set<>AsString()
public void SetWWarningFieldsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WWarningFields == null)
    {
        _WWarningFields = new WWarningFields();
    }
    _WWarningFields.SetWWarningFieldsAsString(value);
}

// Standard Getter
public ElcgmioLinkage1 GetElcgmioLinkage1()
{
    return _ElcgmioLinkage1;
}

// Standard Setter
public void SetElcgmioLinkage1(ElcgmioLinkage1 value)
{
    _ElcgmioLinkage1 = value;
}

// Get<>AsString()
public string GetElcgmioLinkage1AsString()
{
    return _ElcgmioLinkage1 != null ? _ElcgmioLinkage1.GetElcgmioLinkage1AsString() : "";
}

// Set<>AsString()
public void SetElcgmioLinkage1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ElcgmioLinkage1 == null)
    {
        _ElcgmioLinkage1 = new ElcgmioLinkage1();
    }
    _ElcgmioLinkage1.SetElcgmioLinkage1AsString(value);
}

// Standard Getter
public ElcgmioLinkage2 GetElcgmioLinkage2()
{
    return _ElcgmioLinkage2;
}

// Standard Setter
public void SetElcgmioLinkage2(ElcgmioLinkage2 value)
{
    _ElcgmioLinkage2 = value;
}

// Get<>AsString()
public string GetElcgmioLinkage2AsString()
{
    return _ElcgmioLinkage2 != null ? _ElcgmioLinkage2.GetElcgmioLinkage2AsString() : "";
}

// Set<>AsString()
public void SetElcgmioLinkage2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ElcgmioLinkage2 == null)
    {
        _ElcgmioLinkage2 = new ElcgmioLinkage2();
    }
    _ElcgmioLinkage2.SetElcgmioLinkage2AsString(value);
}

// Standard Getter
public CgtpriceLinkage GetCgtpriceLinkage()
{
    return _CgtpriceLinkage;
}

// Standard Setter
public void SetCgtpriceLinkage(CgtpriceLinkage value)
{
    _CgtpriceLinkage = value;
}

// Get<>AsString()
public string GetCgtpriceLinkageAsString()
{
    return _CgtpriceLinkage != null ? _CgtpriceLinkage.GetCgtpriceLinkageAsString() : "";
}

// Set<>AsString()
public void SetCgtpriceLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtpriceLinkage == null)
    {
        _CgtpriceLinkage = new CgtpriceLinkage();
    }
    _CgtpriceLinkage.SetCgtpriceLinkageAsString(value);
}

// Standard Getter
public string GetWsIoPgm()
{
    return _WsIoPgm;
}

// Standard Setter
public void SetWsIoPgm(string value)
{
    _WsIoPgm = value;
}

// Get<>AsString()
public string GetWsIoPgmAsString()
{
    return _WsIoPgm.PadRight(8);
}

// Set<>AsString()
public void SetWsIoPgmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIoPgm = value;
}

// Standard Getter
public WsCioLink GetWsCioLink()
{
    return _WsCioLink;
}

// Standard Setter
public void SetWsCioLink(WsCioLink value)
{
    _WsCioLink = value;
}

// Get<>AsString()
public string GetWsCioLinkAsString()
{
    return _WsCioLink != null ? _WsCioLink.GetWsCioLinkAsString() : "";
}

// Set<>AsString()
public void SetWsCioLinkAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsCioLink == null)
    {
        _WsCioLink = new WsCioLink();
    }
    _WsCioLink.SetWsCioLinkAsString(value);
}

// Standard Getter
public WsFileFlags GetWsFileFlags()
{
    return _WsFileFlags;
}

// Standard Setter
public void SetWsFileFlags(WsFileFlags value)
{
    _WsFileFlags = value;
}

// Get<>AsString()
public string GetWsFileFlagsAsString()
{
    return _WsFileFlags != null ? _WsFileFlags.GetWsFileFlagsAsString() : "";
}

// Set<>AsString()
public void SetWsFileFlagsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsFileFlags == null)
    {
        _WsFileFlags = new WsFileFlags();
    }
    _WsFileFlags.SetWsFileFlagsAsString(value);
}

// Standard Getter
public ScheduleRecord GetScheduleRecord()
{
    return _ScheduleRecord;
}

// Standard Setter
public void SetScheduleRecord(ScheduleRecord value)
{
    _ScheduleRecord = value;
}

// Get<>AsString()
public string GetScheduleRecordAsString()
{
    return _ScheduleRecord != null ? _ScheduleRecord.GetScheduleRecordAsString() : "";
}

// Set<>AsString()
public void SetScheduleRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ScheduleRecord == null)
    {
        _ScheduleRecord = new ScheduleRecord();
    }
    _ScheduleRecord.SetScheduleRecordAsString(value);
}

// Standard Getter
public string GetCgtMasterIds()
{
    return _CgtMasterIds;
}

// Standard Setter
public void SetCgtMasterIds(string value)
{
    _CgtMasterIds = value;
}

// Get<>AsString()
public string GetCgtMasterIdsAsString()
{
    return _CgtMasterIds.PadRight(0);
}

// Set<>AsString()
public void SetCgtMasterIdsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtMasterIds = value;
}

// Standard Getter
public CgtMasterRecord GetCgtMasterRecord()
{
    return _CgtMasterRecord;
}

// Standard Setter
public void SetCgtMasterRecord(CgtMasterRecord value)
{
    _CgtMasterRecord = value;
}

// Get<>AsString()
public string GetCgtMasterRecordAsString()
{
    return _CgtMasterRecord != null ? _CgtMasterRecord.GetCgtMasterRecordAsString() : "";
}

// Set<>AsString()
public void SetCgtMasterRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtMasterRecord == null)
    {
        _CgtMasterRecord = new CgtMasterRecord();
    }
    _CgtMasterRecord.SetCgtMasterRecordAsString(value);
}

// Standard Getter
public CgtMasterRecord01 GetCgtMasterRecord01()
{
    return _CgtMasterRecord01;
}

// Standard Setter
public void SetCgtMasterRecord01(CgtMasterRecord01 value)
{
    _CgtMasterRecord01 = value;
}

// Get<>AsString()
public string GetCgtMasterRecord01AsString()
{
    return _CgtMasterRecord01 != null ? _CgtMasterRecord01.GetCgtMasterRecord01AsString() : "";
}

// Set<>AsString()
public void SetCgtMasterRecord01AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtMasterRecord01 == null)
    {
        _CgtMasterRecord01 = new CgtMasterRecord01();
    }
    _CgtMasterRecord01.SetCgtMasterRecord01AsString(value);
}

// Standard Getter
public CgtMasterRecord02 GetCgtMasterRecord02()
{
    return _CgtMasterRecord02;
}

// Standard Setter
public void SetCgtMasterRecord02(CgtMasterRecord02 value)
{
    _CgtMasterRecord02 = value;
}

// Get<>AsString()
public string GetCgtMasterRecord02AsString()
{
    return _CgtMasterRecord02 != null ? _CgtMasterRecord02.GetCgtMasterRecord02AsString() : "";
}

// Set<>AsString()
public void SetCgtMasterRecord02AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtMasterRecord02 == null)
    {
        _CgtMasterRecord02 = new CgtMasterRecord02();
    }
    _CgtMasterRecord02.SetCgtMasterRecord02AsString(value);
}

// Standard Getter
public CgtMasterRecord03 GetCgtMasterRecord03()
{
    return _CgtMasterRecord03;
}

// Standard Setter
public void SetCgtMasterRecord03(CgtMasterRecord03 value)
{
    _CgtMasterRecord03 = value;
}

// Get<>AsString()
public string GetCgtMasterRecord03AsString()
{
    return _CgtMasterRecord03 != null ? _CgtMasterRecord03.GetCgtMasterRecord03AsString() : "";
}

// Set<>AsString()
public void SetCgtMasterRecord03AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtMasterRecord03 == null)
    {
        _CgtMasterRecord03 = new CgtMasterRecord03();
    }
    _CgtMasterRecord03.SetCgtMasterRecord03AsString(value);
}

// Standard Getter
public ParRecord GetParRecord()
{
    return _ParRecord;
}

// Standard Setter
public void SetParRecord(ParRecord value)
{
    _ParRecord = value;
}

// Get<>AsString()
public string GetParRecordAsString()
{
    return _ParRecord != null ? _ParRecord.GetParRecordAsString() : "";
}

// Set<>AsString()
public void SetParRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ParRecord == null)
    {
        _ParRecord = new ParRecord();
    }
    _ParRecord.SetParRecordAsString(value);
}

// Standard Getter
public string GetD13Record()
{
    return _D13Record;
}

// Standard Setter
public void SetD13Record(string value)
{
    _D13Record = value;
}

// Get<>AsString()
public string GetD13RecordAsString()
{
    return _D13Record.PadRight(1870);
}

// Set<>AsString()
public void SetD13RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D13Record = value;
}

// Standard Getter
public D13SedolHeaderRecord GetD13SedolHeaderRecord()
{
    return _D13SedolHeaderRecord;
}

// Standard Setter
public void SetD13SedolHeaderRecord(D13SedolHeaderRecord value)
{
    _D13SedolHeaderRecord = value;
}

// Get<>AsString()
public string GetD13SedolHeaderRecordAsString()
{
    return _D13SedolHeaderRecord != null ? _D13SedolHeaderRecord.GetD13SedolHeaderRecordAsString() : "";
}

// Set<>AsString()
public void SetD13SedolHeaderRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D13SedolHeaderRecord == null)
    {
        _D13SedolHeaderRecord = new D13SedolHeaderRecord();
    }
    _D13SedolHeaderRecord.SetD13SedolHeaderRecordAsString(value);
}

// Standard Getter
public D13BalAcqDispRecord GetD13BalAcqDispRecord()
{
    return _D13BalAcqDispRecord;
}

// Standard Setter
public void SetD13BalAcqDispRecord(D13BalAcqDispRecord value)
{
    _D13BalAcqDispRecord = value;
}

// Get<>AsString()
public string GetD13BalAcqDispRecordAsString()
{
    return _D13BalAcqDispRecord != null ? _D13BalAcqDispRecord.GetD13BalAcqDispRecordAsString() : "";
}

// Set<>AsString()
public void SetD13BalAcqDispRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D13BalAcqDispRecord == null)
    {
        _D13BalAcqDispRecord = new D13BalAcqDispRecord();
    }
    _D13BalAcqDispRecord.SetD13BalAcqDispRecordAsString(value);
}

// Standard Getter
public WsRptItems GetWsRptItems()
{
    return _WsRptItems;
}

// Standard Setter
public void SetWsRptItems(WsRptItems value)
{
    _WsRptItems = value;
}

// Get<>AsString()
public string GetWsRptItemsAsString()
{
    return _WsRptItems != null ? _WsRptItems.GetWsRptItemsAsString() : "";
}

// Set<>AsString()
public void SetWsRptItemsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsRptItems == null)
    {
        _WsRptItems = new WsRptItems();
    }
    _WsRptItems.SetWsRptItemsAsString(value);
}

// Standard Getter
public WsIlostReportItems GetWsIlostReportItems()
{
    return _WsIlostReportItems;
}

// Standard Setter
public void SetWsIlostReportItems(WsIlostReportItems value)
{
    _WsIlostReportItems = value;
}

// Get<>AsString()
public string GetWsIlostReportItemsAsString()
{
    return _WsIlostReportItems != null ? _WsIlostReportItems.GetWsIlostReportItemsAsString() : "";
}

// Set<>AsString()
public void SetWsIlostReportItemsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsIlostReportItems == null)
    {
        _WsIlostReportItems = new WsIlostReportItems();
    }
    _WsIlostReportItems.SetWsIlostReportItemsAsString(value);
}

// Standard Getter
public WsIlosuReportItems GetWsIlosuReportItems()
{
    return _WsIlosuReportItems;
}

// Standard Setter
public void SetWsIlosuReportItems(WsIlosuReportItems value)
{
    _WsIlosuReportItems = value;
}

// Get<>AsString()
public string GetWsIlosuReportItemsAsString()
{
    return _WsIlosuReportItems != null ? _WsIlosuReportItems.GetWsIlosuReportItemsAsString() : "";
}

// Set<>AsString()
public void SetWsIlosuReportItemsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsIlosuReportItems == null)
    {
        _WsIlosuReportItems = new WsIlosuReportItems();
    }
    _WsIlosuReportItems.SetWsIlosuReportItemsAsString(value);
}

// Standard Getter
public WSpecials GetWSpecials()
{
    return _WSpecials;
}

// Standard Setter
public void SetWSpecials(WSpecials value)
{
    _WSpecials = value;
}

// Get<>AsString()
public string GetWSpecialsAsString()
{
    return _WSpecials != null ? _WSpecials.GetWSpecialsAsString() : "";
}

// Set<>AsString()
public void SetWSpecialsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WSpecials == null)
    {
        _WSpecials = new WSpecials();
    }
    _WSpecials.SetWSpecialsAsString(value);
}

// Standard Getter
public string GetWDdText()
{
    return _WDdText;
}

// Standard Setter
public void SetWDdText(string value)
{
    _WDdText = value;
}

// Get<>AsString()
public string GetWDdTextAsString()
{
    return _WDdText.PadRight(0);
}

// Set<>AsString()
public void SetWDdTextAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WDdText = value;
}

// Standard Getter
public D43Record GetD43Record()
{
    return _D43Record;
}

// Standard Setter
public void SetD43Record(D43Record value)
{
    _D43Record = value;
}

// Get<>AsString()
public string GetD43RecordAsString()
{
    return _D43Record != null ? _D43Record.GetD43RecordAsString() : "";
}

// Set<>AsString()
public void SetD43RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D43Record == null)
    {
        _D43Record = new D43Record();
    }
    _D43Record.SetD43RecordAsString(value);
}

// Standard Getter
public CgtfilesLinkage GetCgtfilesLinkage()
{
    return _CgtfilesLinkage;
}

// Standard Setter
public void SetCgtfilesLinkage(CgtfilesLinkage value)
{
    _CgtfilesLinkage = value;
}

// Get<>AsString()
public string GetCgtfilesLinkageAsString()
{
    return _CgtfilesLinkage != null ? _CgtfilesLinkage.GetCgtfilesLinkageAsString() : "";
}

// Set<>AsString()
public void SetCgtfilesLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtfilesLinkage == null)
    {
        _CgtfilesLinkage = new CgtfilesLinkage();
    }
    _CgtfilesLinkage.SetCgtfilesLinkageAsString(value);
}

// Standard Getter
public LFileRecordArea GetLFileRecordArea()
{
    return _LFileRecordArea;
}

// Standard Setter
public void SetLFileRecordArea(LFileRecordArea value)
{
    _LFileRecordArea = value;
}

// Get<>AsString()
public string GetLFileRecordAreaAsString()
{
    return _LFileRecordArea != null ? _LFileRecordArea.GetLFileRecordAreaAsString() : "";
}

// Set<>AsString()
public void SetLFileRecordAreaAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_LFileRecordArea == null)
    {
        _LFileRecordArea = new LFileRecordArea();
    }
    _LFileRecordArea.SetLFileRecordAreaAsString(value);
}

// Standard Getter
public LValidDate GetLValidDate()
{
    return _LValidDate;
}

// Standard Setter
public void SetLValidDate(LValidDate value)
{
    _LValidDate = value;
}

// Get<>AsString()
public string GetLValidDateAsString()
{
    return _LValidDate != null ? _LValidDate.GetLValidDateAsString() : "";
}

// Set<>AsString()
public void SetLValidDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_LValidDate == null)
    {
        _LValidDate = new LValidDate();
    }
    _LValidDate.SetLValidDateAsString(value);
}

// Standard Getter
public WsSaveCgt GetWsSaveCgt()
{
    return _WsSaveCgt;
}

// Standard Setter
public void SetWsSaveCgt(WsSaveCgt value)
{
    _WsSaveCgt = value;
}

// Get<>AsString()
public string GetWsSaveCgtAsString()
{
    return _WsSaveCgt != null ? _WsSaveCgt.GetWsSaveCgtAsString() : "";
}

// Set<>AsString()
public void SetWsSaveCgtAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsSaveCgt == null)
    {
        _WsSaveCgt = new WsSaveCgt();
    }
    _WsSaveCgt.SetWsSaveCgtAsString(value);
}

// Standard Getter
public string GetWsSaveIds()
{
    return _WsSaveIds;
}

// Standard Setter
public void SetWsSaveIds(string value)
{
    _WsSaveIds = value;
}

// Get<>AsString()
public string GetWsSaveIdsAsString()
{
    return _WsSaveIds.PadRight(0);
}

// Set<>AsString()
public void SetWsSaveIdsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsSaveIds = value;
}

// Standard Getter
public WsSaveCgt2 GetWsSaveCgt2()
{
    return _WsSaveCgt2;
}

// Standard Setter
public void SetWsSaveCgt2(WsSaveCgt2 value)
{
    _WsSaveCgt2 = value;
}

// Get<>AsString()
public string GetWsSaveCgt2AsString()
{
    return _WsSaveCgt2 != null ? _WsSaveCgt2.GetWsSaveCgt2AsString() : "";
}

// Set<>AsString()
public void SetWsSaveCgt2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsSaveCgt2 == null)
    {
        _WsSaveCgt2 = new WsSaveCgt2();
    }
    _WsSaveCgt2.SetWsSaveCgt2AsString(value);
}

// Standard Getter
public WcConstants GetWcConstants()
{
    return _WcConstants;
}

// Standard Setter
public void SetWcConstants(WcConstants value)
{
    _WcConstants = value;
}

// Get<>AsString()
public string GetWcConstantsAsString()
{
    return _WcConstants != null ? _WcConstants.GetWcConstantsAsString() : "";
}

// Set<>AsString()
public void SetWcConstantsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WcConstants == null)
    {
        _WcConstants = new WcConstants();
    }
    _WcConstants.SetWcConstantsAsString(value);
}

// Standard Getter
public WeErrorMessages GetWeErrorMessages()
{
    return _WeErrorMessages;
}

// Standard Setter
public void SetWeErrorMessages(WeErrorMessages value)
{
    _WeErrorMessages = value;
}

// Get<>AsString()
public string GetWeErrorMessagesAsString()
{
    return _WeErrorMessages != null ? _WeErrorMessages.GetWeErrorMessagesAsString() : "";
}

// Set<>AsString()
public void SetWeErrorMessagesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WeErrorMessages == null)
    {
        _WeErrorMessages = new WeErrorMessages();
    }
    _WeErrorMessages.SetWeErrorMessagesAsString(value);
}

// Standard Getter
public WerRecordErrors GetWerRecordErrors()
{
    return _WerRecordErrors;
}

// Standard Setter
public void SetWerRecordErrors(WerRecordErrors value)
{
    _WerRecordErrors = value;
}

// Get<>AsString()
public string GetWerRecordErrorsAsString()
{
    return _WerRecordErrors != null ? _WerRecordErrors.GetWerRecordErrorsAsString() : "";
}

// Set<>AsString()
public void SetWerRecordErrorsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WerRecordErrors == null)
    {
        _WerRecordErrors = new WerRecordErrors();
    }
    _WerRecordErrors.SetWerRecordErrorsAsString(value);
}

// Standard Getter
public int GetWErrorOccurs()
{
    return _WErrorOccurs;
}

// Standard Setter
public void SetWErrorOccurs(int value)
{
    _WErrorOccurs = value;
}

// Get<>AsString()
public string GetWErrorOccursAsString()
{
    return _WErrorOccurs.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWErrorOccursAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WErrorOccurs = parsed;
}

// Standard Getter
public WiInterface GetWiInterface()
{
    return _WiInterface;
}

// Standard Setter
public void SetWiInterface(WiInterface value)
{
    _WiInterface = value;
}

// Get<>AsString()
public string GetWiInterfaceAsString()
{
    return _WiInterface != null ? _WiInterface.GetWiInterfaceAsString() : "";
}

// Set<>AsString()
public void SetWiInterfaceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WiInterface == null)
    {
        _WiInterface = new WiInterface();
    }
    _WiInterface.SetWiInterfaceAsString(value);
}

// Standard Getter
public WlLiterals GetWlLiterals()
{
    return _WlLiterals;
}

// Standard Setter
public void SetWlLiterals(WlLiterals value)
{
    _WlLiterals = value;
}

// Get<>AsString()
public string GetWlLiteralsAsString()
{
    return _WlLiterals != null ? _WlLiterals.GetWlLiteralsAsString() : "";
}

// Set<>AsString()
public void SetWlLiteralsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WlLiterals == null)
    {
        _WlLiterals = new WlLiterals();
    }
    _WlLiterals.SetWlLiteralsAsString(value);
}

// Standard Getter
public WsStorage GetWsStorage()
{
    return _WsStorage;
}

// Standard Setter
public void SetWsStorage(WsStorage value)
{
    _WsStorage = value;
}

// Get<>AsString()
public string GetWsStorageAsString()
{
    return _WsStorage != null ? _WsStorage.GetWsStorageAsString() : "";
}

// Set<>AsString()
public void SetWsStorageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsStorage == null)
    {
        _WsStorage = new WsStorage();
    }
    _WsStorage.SetWsStorageAsString(value);
}

// Standard Getter
public WtTables GetWtTables()
{
    return _WtTables;
}

// Standard Setter
public void SetWtTables(WtTables value)
{
    _WtTables = value;
}

// Get<>AsString()
public string GetWtTablesAsString()
{
    return _WtTables != null ? _WtTables.GetWtTablesAsString() : "";
}

// Set<>AsString()
public void SetWtTablesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtTables == null)
    {
        _WtTables = new WtTables();
    }
    _WtTables.SetWtTablesAsString(value);
}

// Standard Getter
public WsWiNormGtTranches GetWsWiNormGtTranches()
{
    return _WsWiNormGtTranches;
}

// Standard Setter
public void SetWsWiNormGtTranches(WsWiNormGtTranches value)
{
    _WsWiNormGtTranches = value;
}

// Get<>AsString()
public string GetWsWiNormGtTranchesAsString()
{
    return _WsWiNormGtTranches != null ? _WsWiNormGtTranches.GetWsWiNormGtTranchesAsString() : "";
}

// Set<>AsString()
public void SetWsWiNormGtTranchesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsWiNormGtTranches == null)
    {
        _WsWiNormGtTranches = new WsWiNormGtTranches();
    }
    _WsWiNormGtTranches.SetWsWiNormGtTranchesAsString(value);
}

// Standard Getter
public WtCompany GetWtCompany()
{
    return _WtCompany;
}

// Standard Setter
public void SetWtCompany(WtCompany value)
{
    _WtCompany = value;
}

// Get<>AsString()
public string GetWtCompanyAsString()
{
    return _WtCompany != null ? _WtCompany.GetWtCompanyAsString() : "";
}

// Set<>AsString()
public void SetWtCompanyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtCompany == null)
    {
        _WtCompany = new WtCompany();
    }
    _WtCompany.SetWtCompanyAsString(value);
}

// Standard Getter
public EqgetdtsLinkage GetEqgetdtsLinkage()
{
    return _EqgetdtsLinkage;
}

// Standard Setter
public void SetEqgetdtsLinkage(EqgetdtsLinkage value)
{
    _EqgetdtsLinkage = value;
}

// Get<>AsString()
public string GetEqgetdtsLinkageAsString()
{
    return _EqgetdtsLinkage != null ? _EqgetdtsLinkage.GetEqgetdtsLinkageAsString() : "";
}

// Set<>AsString()
public void SetEqgetdtsLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_EqgetdtsLinkage == null)
    {
        _EqgetdtsLinkage = new EqgetdtsLinkage();
    }
    _EqgetdtsLinkage.SetEqgetdtsLinkageAsString(value);
}

// Standard Getter
public WtDescriptionsA GetWtDescriptionsA()
{
    return _WtDescriptionsA;
}

// Standard Setter
public void SetWtDescriptionsA(WtDescriptionsA value)
{
    _WtDescriptionsA = value;
}

// Get<>AsString()
public string GetWtDescriptionsAAsString()
{
    return _WtDescriptionsA != null ? _WtDescriptionsA.GetWtDescriptionsAAsString() : "";
}

// Set<>AsString()
public void SetWtDescriptionsAAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtDescriptionsA == null)
    {
        _WtDescriptionsA = new WtDescriptionsA();
    }
    _WtDescriptionsA.SetWtDescriptionsAAsString(value);
}

// Standard Getter
public WtDescriptionsD GetWtDescriptionsD()
{
    return _WtDescriptionsD;
}

// Standard Setter
public void SetWtDescriptionsD(WtDescriptionsD value)
{
    _WtDescriptionsD = value;
}

// Get<>AsString()
public string GetWtDescriptionsDAsString()
{
    return _WtDescriptionsD != null ? _WtDescriptionsD.GetWtDescriptionsDAsString() : "";
}

// Set<>AsString()
public void SetWtDescriptionsDAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtDescriptionsD == null)
    {
        _WtDescriptionsD = new WtDescriptionsD();
    }
    _WtDescriptionsD.SetWtDescriptionsDAsString(value);
}

// Standard Getter
public WtIndexTranches GetWtIndexTranches()
{
    return _WtIndexTranches;
}

// Standard Setter
public void SetWtIndexTranches(WtIndexTranches value)
{
    _WtIndexTranches = value;
}

// Get<>AsString()
public string GetWtIndexTranchesAsString()
{
    return _WtIndexTranches != null ? _WtIndexTranches.GetWtIndexTranchesAsString() : "";
}

// Set<>AsString()
public void SetWtIndexTranchesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtIndexTranches == null)
    {
        _WtIndexTranches = new WtIndexTranches();
    }
    _WtIndexTranches.SetWtIndexTranchesAsString(value);
}

// Standard Getter
public WtIndexCost GetWtIndexCost()
{
    return _WtIndexCost;
}

// Standard Setter
public void SetWtIndexCost(WtIndexCost value)
{
    _WtIndexCost = value;
}

// Get<>AsString()
public string GetWtIndexCostAsString()
{
    return _WtIndexCost != null ? _WtIndexCost.GetWtIndexCostAsString() : "";
}

// Set<>AsString()
public void SetWtIndexCostAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtIndexCost == null)
    {
        _WtIndexCost = new WtIndexCost();
    }
    _WtIndexCost.SetWtIndexCostAsString(value);
}

// Standard Getter
public WtIndexIndexDates GetWtIndexIndexDates()
{
    return _WtIndexIndexDates;
}

// Standard Setter
public void SetWtIndexIndexDates(WtIndexIndexDates value)
{
    _WtIndexIndexDates = value;
}

// Get<>AsString()
public string GetWtIndexIndexDatesAsString()
{
    return _WtIndexIndexDates != null ? _WtIndexIndexDates.GetWtIndexIndexDatesAsString() : "";
}

// Set<>AsString()
public void SetWtIndexIndexDatesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtIndexIndexDates == null)
    {
        _WtIndexIndexDates = new WtIndexIndexDates();
    }
    _WtIndexIndexDates.SetWtIndexIndexDatesAsString(value);
}

// Standard Getter
public WtGtTranches GetWtGtTranches()
{
    return _WtGtTranches;
}

// Standard Setter
public void SetWtGtTranches(WtGtTranches value)
{
    _WtGtTranches = value;
}

// Get<>AsString()
public string GetWtGtTranchesAsString()
{
    return _WtGtTranches != null ? _WtGtTranches.GetWtGtTranchesAsString() : "";
}

// Set<>AsString()
public void SetWtGtTranchesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtGtTranches == null)
    {
        _WtGtTranches = new WtGtTranches();
    }
    _WtGtTranches.SetWtGtTranchesAsString(value);
}

// Standard Getter
public WtGtMatchedTranches GetWtGtMatchedTranches()
{
    return _WtGtMatchedTranches;
}

// Standard Setter
public void SetWtGtMatchedTranches(WtGtMatchedTranches value)
{
    _WtGtMatchedTranches = value;
}

// Get<>AsString()
public string GetWtGtMatchedTranchesAsString()
{
    return _WtGtMatchedTranches != null ? _WtGtMatchedTranches.GetWtGtMatchedTranchesAsString() : "";
}

// Set<>AsString()
public void SetWtGtMatchedTranchesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtGtMatchedTranches == null)
    {
        _WtGtMatchedTranches = new WtGtMatchedTranches();
    }
    _WtGtMatchedTranches.SetWtGtMatchedTranchesAsString(value);
}

// Standard Getter
public WtGtMatchedBargainDates GetWtGtMatchedBargainDates()
{
    return _WtGtMatchedBargainDates;
}

// Standard Setter
public void SetWtGtMatchedBargainDates(WtGtMatchedBargainDates value)
{
    _WtGtMatchedBargainDates = value;
}

// Get<>AsString()
public string GetWtGtMatchedBargainDatesAsString()
{
    return _WtGtMatchedBargainDates != null ? _WtGtMatchedBargainDates.GetWtGtMatchedBargainDatesAsString() : "";
}

// Set<>AsString()
public void SetWtGtMatchedBargainDatesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtGtMatchedBargainDates == null)
    {
        _WtGtMatchedBargainDates = new WtGtMatchedBargainDates();
    }
    _WtGtMatchedBargainDates.SetWtGtMatchedBargainDatesAsString(value);
}

// Standard Getter
public WtIndexEligibleTranches GetWtIndexEligibleTranches()
{
    return _WtIndexEligibleTranches;
}

// Standard Setter
public void SetWtIndexEligibleTranches(WtIndexEligibleTranches value)
{
    _WtIndexEligibleTranches = value;
}

// Get<>AsString()
public string GetWtIndexEligibleTranchesAsString()
{
    return _WtIndexEligibleTranches != null ? _WtIndexEligibleTranches.GetWtIndexEligibleTranchesAsString() : "";
}

// Set<>AsString()
public void SetWtIndexEligibleTranchesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtIndexEligibleTranches == null)
    {
        _WtIndexEligibleTranches = new WtIndexEligibleTranches();
    }
    _WtIndexEligibleTranches.SetWtIndexEligibleTranchesAsString(value);
}

// Standard Getter
public WtKeysUnprocessed GetWtKeysUnprocessed()
{
    return _WtKeysUnprocessed;
}

// Standard Setter
public void SetWtKeysUnprocessed(WtKeysUnprocessed value)
{
    _WtKeysUnprocessed = value;
}

// Get<>AsString()
public string GetWtKeysUnprocessedAsString()
{
    return _WtKeysUnprocessed != null ? _WtKeysUnprocessed.GetWtKeysUnprocessedAsString() : "";
}

// Set<>AsString()
public void SetWtKeysUnprocessedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtKeysUnprocessed == null)
    {
        _WtKeysUnprocessed = new WtKeysUnprocessed();
    }
    _WtKeysUnprocessed.SetWtKeysUnprocessedAsString(value);
}

// Standard Getter
public WtPartProcessedKeys GetWtPartProcessedKeys()
{
    return _WtPartProcessedKeys;
}

// Standard Setter
public void SetWtPartProcessedKeys(WtPartProcessedKeys value)
{
    _WtPartProcessedKeys = value;
}

// Get<>AsString()
public string GetWtPartProcessedKeysAsString()
{
    return _WtPartProcessedKeys != null ? _WtPartProcessedKeys.GetWtPartProcessedKeysAsString() : "";
}

// Set<>AsString()
public void SetWtPartProcessedKeysAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtPartProcessedKeys == null)
    {
        _WtPartProcessedKeys = new WtPartProcessedKeys();
    }
    _WtPartProcessedKeys.SetWtPartProcessedKeysAsString(value);
}

// Standard Getter
public WtRetailPriceIndexes GetWtRetailPriceIndexes()
{
    return _WtRetailPriceIndexes;
}

// Standard Setter
public void SetWtRetailPriceIndexes(WtRetailPriceIndexes value)
{
    _WtRetailPriceIndexes = value;
}

// Get<>AsString()
public string GetWtRetailPriceIndexesAsString()
{
    return _WtRetailPriceIndexes != null ? _WtRetailPriceIndexes.GetWtRetailPriceIndexesAsString() : "";
}

// Set<>AsString()
public void SetWtRetailPriceIndexesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtRetailPriceIndexes == null)
    {
        _WtRetailPriceIndexes = new WtRetailPriceIndexes();
    }
    _WtRetailPriceIndexes.SetWtRetailPriceIndexesAsString(value);
}

// Standard Getter
public WtSedolHeaders GetWtSedolHeaders()
{
    return _WtSedolHeaders;
}

// Standard Setter
public void SetWtSedolHeaders(WtSedolHeaders value)
{
    _WtSedolHeaders = value;
}

// Get<>AsString()
public string GetWtSedolHeadersAsString()
{
    return _WtSedolHeaders != null ? _WtSedolHeaders.GetWtSedolHeadersAsString() : "";
}

// Set<>AsString()
public void SetWtSedolHeadersAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtSedolHeaders == null)
    {
        _WtSedolHeaders = new WtSedolHeaders();
    }
    _WtSedolHeaders.SetWtSedolHeadersAsString(value);
}

// Standard Getter
public WtTransactions GetWtTransactions()
{
    return _WtTransactions;
}

// Standard Setter
public void SetWtTransactions(WtTransactions value)
{
    _WtTransactions = value;
}

// Get<>AsString()
public string GetWtTransactionsAsString()
{
    return _WtTransactions != null ? _WtTransactions.GetWtTransactionsAsString() : "";
}

// Set<>AsString()
public void SetWtTransactionsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtTransactions == null)
    {
        _WtTransactions = new WtTransactions();
    }
    _WtTransactions.SetWtTransactionsAsString(value);
}

// Standard Getter
public WttcCosts GetWttcCosts()
{
    return _WttcCosts;
}

// Standard Setter
public void SetWttcCosts(WttcCosts value)
{
    _WttcCosts = value;
}

// Get<>AsString()
public string GetWttcCostsAsString()
{
    return _WttcCosts != null ? _WttcCosts.GetWttcCostsAsString() : "";
}

// Set<>AsString()
public void SetWttcCostsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttcCosts == null)
    {
        _WttcCosts = new WttcCosts();
    }
    _WttcCosts.SetWttcCostsAsString(value);
}

// Standard Getter
public WtUnits GetWtUnits()
{
    return _WtUnits;
}

// Standard Setter
public void SetWtUnits(WtUnits value)
{
    _WtUnits = value;
}

// Get<>AsString()
public string GetWtUnitsAsString()
{
    return _WtUnits != null ? _WtUnits.GetWtUnitsAsString() : "";
}

// Set<>AsString()
public void SetWtUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtUnits == null)
    {
        _WtUnits = new WtUnits();
    }
    _WtUnits.SetWtUnitsAsString(value);
}

// Standard Getter
public WtAssetUsageCalendar GetWtAssetUsageCalendar()
{
    return _WtAssetUsageCalendar;
}

// Standard Setter
public void SetWtAssetUsageCalendar(WtAssetUsageCalendar value)
{
    _WtAssetUsageCalendar = value;
}

// Get<>AsString()
public string GetWtAssetUsageCalendarAsString()
{
    return _WtAssetUsageCalendar != null ? _WtAssetUsageCalendar.GetWtAssetUsageCalendarAsString() : "";
}

// Set<>AsString()
public void SetWtAssetUsageCalendarAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtAssetUsageCalendar == null)
    {
        _WtAssetUsageCalendar = new WtAssetUsageCalendar();
    }
    _WtAssetUsageCalendar.SetWtAssetUsageCalendarAsString(value);
}

// Standard Getter
public WCalendarWorkFields GetWCalendarWorkFields()
{
    return _WCalendarWorkFields;
}

// Standard Setter
public void SetWCalendarWorkFields(WCalendarWorkFields value)
{
    _WCalendarWorkFields = value;
}

// Get<>AsString()
public string GetWCalendarWorkFieldsAsString()
{
    return _WCalendarWorkFields != null ? _WCalendarWorkFields.GetWCalendarWorkFieldsAsString() : "";
}

// Set<>AsString()
public void SetWCalendarWorkFieldsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WCalendarWorkFields == null)
    {
        _WCalendarWorkFields = new WCalendarWorkFields();
    }
    _WCalendarWorkFields.SetWCalendarWorkFieldsAsString(value);
}

// Standard Getter
public WtFundRecords GetWtFundRecords()
{
    return _WtFundRecords;
}

// Standard Setter
public void SetWtFundRecords(WtFundRecords value)
{
    _WtFundRecords = value;
}

// Get<>AsString()
public string GetWtFundRecordsAsString()
{
    return _WtFundRecords != null ? _WtFundRecords.GetWtFundRecordsAsString() : "";
}

// Set<>AsString()
public void SetWtFundRecordsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtFundRecords == null)
    {
        _WtFundRecords = new WtFundRecords();
    }
    _WtFundRecords.SetWtFundRecordsAsString(value);
}

// Standard Getter
public WtRestructureDates GetWtRestructureDates()
{
    return _WtRestructureDates;
}

// Standard Setter
public void SetWtRestructureDates(WtRestructureDates value)
{
    _WtRestructureDates = value;
}

// Get<>AsString()
public string GetWtRestructureDatesAsString()
{
    return _WtRestructureDates != null ? _WtRestructureDates.GetWtRestructureDatesAsString() : "";
}

// Set<>AsString()
public void SetWtRestructureDatesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtRestructureDates == null)
    {
        _WtRestructureDates = new WtRestructureDates();
    }
    _WtRestructureDates.SetWtRestructureDatesAsString(value);
}

// Standard Getter
public WtRestructureHoldings GetWtRestructureHoldings()
{
    return _WtRestructureHoldings;
}

// Standard Setter
public void SetWtRestructureHoldings(WtRestructureHoldings value)
{
    _WtRestructureHoldings = value;
}

// Get<>AsString()
public string GetWtRestructureHoldingsAsString()
{
    return _WtRestructureHoldings != null ? _WtRestructureHoldings.GetWtRestructureHoldingsAsString() : "";
}

// Set<>AsString()
public void SetWtRestructureHoldingsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtRestructureHoldings == null)
    {
        _WtRestructureHoldings = new WtRestructureHoldings();
    }
    _WtRestructureHoldings.SetWtRestructureHoldingsAsString(value);
}

// Standard Getter
public NewFields GetNewFields()
{
    return _NewFields;
}

// Standard Setter
public void SetNewFields(NewFields value)
{
    _NewFields = value;
}

// Get<>AsString()
public string GetNewFieldsAsString()
{
    return _NewFields != null ? _NewFields.GetNewFieldsAsString() : "";
}

// Set<>AsString()
public void SetNewFieldsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_NewFields == null)
    {
        _NewFields = new NewFields();
    }
    _NewFields.SetNewFieldsAsString(value);
}

// Standard Getter
public WsDispEyMsg GetWsDispEyMsg()
{
    return _WsDispEyMsg;
}

// Standard Setter
public void SetWsDispEyMsg(WsDispEyMsg value)
{
    _WsDispEyMsg = value;
}

// Get<>AsString()
public string GetWsDispEyMsgAsString()
{
    return _WsDispEyMsg != null ? _WsDispEyMsg.GetWsDispEyMsgAsString() : "";
}

// Set<>AsString()
public void SetWsDispEyMsgAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsDispEyMsg == null)
    {
        _WsDispEyMsg = new WsDispEyMsg();
    }
    _WsDispEyMsg.SetWsDispEyMsgAsString(value);
}

// Standard Getter
public WsRunControl GetWsRunControl()
{
    return _WsRunControl;
}

// Standard Setter
public void SetWsRunControl(WsRunControl value)
{
    _WsRunControl = value;
}

// Get<>AsString()
public string GetWsRunControlAsString()
{
    return _WsRunControl != null ? _WsRunControl.GetWsRunControlAsString() : "";
}

// Set<>AsString()
public void SetWsRunControlAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsRunControl == null)
    {
        _WsRunControl = new WsRunControl();
    }
    _WsRunControl.SetWsRunControlAsString(value);
}

// Standard Getter
public string GetLForceWrite()
{
    return _LForceWrite;
}

// Standard Setter
public void SetLForceWrite(string value)
{
    _LForceWrite = value;
}

// Get<>AsString()
public string GetLForceWriteAsString()
{
    return _LForceWrite.PadRight(0);
}

// Set<>AsString()
public void SetLForceWriteAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LForceWrite = value;
}

// Standard Getter
public WtProcessedFundRecords GetWtProcessedFundRecords()
{
    return _WtProcessedFundRecords;
}

// Standard Setter
public void SetWtProcessedFundRecords(WtProcessedFundRecords value)
{
    _WtProcessedFundRecords = value;
}

// Get<>AsString()
public string GetWtProcessedFundRecordsAsString()
{
    return _WtProcessedFundRecords != null ? _WtProcessedFundRecords.GetWtProcessedFundRecordsAsString() : "";
}

// Set<>AsString()
public void SetWtProcessedFundRecordsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtProcessedFundRecords == null)
    {
        _WtProcessedFundRecords = new WtProcessedFundRecords();
    }
    _WtProcessedFundRecords.SetWtProcessedFundRecordsAsString(value);
}

// Standard Getter
public WsIrishCgtFields GetWsIrishCgtFields()
{
    return _WsIrishCgtFields;
}

// Standard Setter
public void SetWsIrishCgtFields(WsIrishCgtFields value)
{
    _WsIrishCgtFields = value;
}

// Get<>AsString()
public string GetWsIrishCgtFieldsAsString()
{
    return _WsIrishCgtFields != null ? _WsIrishCgtFields.GetWsIrishCgtFieldsAsString() : "";
}

// Set<>AsString()
public void SetWsIrishCgtFieldsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsIrishCgtFields == null)
    {
        _WsIrishCgtFields = new WsIrishCgtFields();
    }
    _WsIrishCgtFields.SetWsIrishCgtFieldsAsString(value);
}

// Standard Getter
public WtNdlPurchasesTable GetWtNdlPurchasesTable()
{
    return _WtNdlPurchasesTable;
}

// Standard Setter
public void SetWtNdlPurchasesTable(WtNdlPurchasesTable value)
{
    _WtNdlPurchasesTable = value;
}

// Get<>AsString()
public string GetWtNdlPurchasesTableAsString()
{
    return _WtNdlPurchasesTable != null ? _WtNdlPurchasesTable.GetWtNdlPurchasesTableAsString() : "";
}

// Set<>AsString()
public void SetWtNdlPurchasesTableAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtNdlPurchasesTable == null)
    {
        _WtNdlPurchasesTable = new WtNdlPurchasesTable();
    }
    _WtNdlPurchasesTable.SetWtNdlPurchasesTableAsString(value);
}

// Standard Getter
public WtNdlSalesTable GetWtNdlSalesTable()
{
    return _WtNdlSalesTable;
}

// Standard Setter
public void SetWtNdlSalesTable(WtNdlSalesTable value)
{
    _WtNdlSalesTable = value;
}

// Get<>AsString()
public string GetWtNdlSalesTableAsString()
{
    return _WtNdlSalesTable != null ? _WtNdlSalesTable.GetWtNdlSalesTableAsString() : "";
}

// Set<>AsString()
public void SetWtNdlSalesTableAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtNdlSalesTable == null)
    {
        _WtNdlSalesTable = new WtNdlSalesTable();
    }
    _WtNdlSalesTable.SetWtNdlSalesTableAsString(value);
}

// Standard Getter
public WtRpiMessages GetWtRpiMessages()
{
    return _WtRpiMessages;
}

// Standard Setter
public void SetWtRpiMessages(WtRpiMessages value)
{
    _WtRpiMessages = value;
}

// Get<>AsString()
public string GetWtRpiMessagesAsString()
{
    return _WtRpiMessages != null ? _WtRpiMessages.GetWtRpiMessagesAsString() : "";
}

// Set<>AsString()
public void SetWtRpiMessagesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtRpiMessages == null)
    {
        _WtRpiMessages = new WtRpiMessages();
    }
    _WtRpiMessages.SetWtRpiMessagesAsString(value);
}

// Standard Getter
public string GetWFundProcessed()
{
    return _WFundProcessed;
}

// Standard Setter
public void SetWFundProcessed(string value)
{
    _WFundProcessed = value;
}

// Get<>AsString()
public string GetWFundProcessedAsString()
{
    return _WFundProcessed.PadRight(0);
}

// Set<>AsString()
public void SetWFundProcessedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WFundProcessed = value;
}

// Standard Getter
public string GetWSearchFundCode()
{
    return _WSearchFundCode;
}

// Standard Setter
public void SetWSearchFundCode(string value)
{
    _WSearchFundCode = value;
}

// Get<>AsString()
public string GetWSearchFundCodeAsString()
{
    return _WSearchFundCode.PadRight(4);
}

// Set<>AsString()
public void SetWSearchFundCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WSearchFundCode = value;
}

// Standard Getter
public string GetWTransCatDerivatives()
{
    return _WTransCatDerivatives;
}

// Standard Setter
public void SetWTransCatDerivatives(string value)
{
    _WTransCatDerivatives = value;
}

// Get<>AsString()
public string GetWTransCatDerivativesAsString()
{
    return _WTransCatDerivatives.PadRight(0);
}

// Set<>AsString()
public void SetWTransCatDerivativesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WTransCatDerivatives = value;
}

// Standard Getter
public TempDataLinkage GetTempDataLinkage()
{
    return _TempDataLinkage;
}

// Standard Setter
public void SetTempDataLinkage(TempDataLinkage value)
{
    _TempDataLinkage = value;
}

// Get<>AsString()
public string GetTempDataLinkageAsString()
{
    return _TempDataLinkage != null ? _TempDataLinkage.GetTempDataLinkageAsString() : "";
}

// Set<>AsString()
public void SetTempDataLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_TempDataLinkage == null)
    {
        _TempDataLinkage = new TempDataLinkage();
    }
    _TempDataLinkage.SetTempDataLinkageAsString(value);
}

// Standard Getter
public TimingLinkage GetTimingLinkage()
{
    return _TimingLinkage;
}

// Standard Setter
public void SetTimingLinkage(TimingLinkage value)
{
    _TimingLinkage = value;
}

// Get<>AsString()
public string GetTimingLinkageAsString()
{
    return _TimingLinkage != null ? _TimingLinkage.GetTimingLinkageAsString() : "";
}

// Set<>AsString()
public void SetTimingLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_TimingLinkage == null)
    {
        _TimingLinkage = new TimingLinkage();
    }
    _TimingLinkage.SetTimingLinkageAsString(value);
}

// Standard Getter
public string GetWConfigItem()
{
    return _WConfigItem;
}

// Standard Setter
public void SetWConfigItem(string value)
{
    _WConfigItem = value;
}

// Get<>AsString()
public string GetWConfigItemAsString()
{
    return _WConfigItem.PadRight(10);
}

// Set<>AsString()
public void SetWConfigItemAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WConfigItem = value;
}

// Standard Getter
public CheckCatLinkage GetCheckCatLinkage()
{
    return _CheckCatLinkage;
}

// Standard Setter
public void SetCheckCatLinkage(CheckCatLinkage value)
{
    _CheckCatLinkage = value;
}

// Get<>AsString()
public string GetCheckCatLinkageAsString()
{
    return _CheckCatLinkage != null ? _CheckCatLinkage.GetCheckCatLinkageAsString() : "";
}

// Set<>AsString()
public void SetCheckCatLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CheckCatLinkage == null)
    {
        _CheckCatLinkage = new CheckCatLinkage();
    }
    _CheckCatLinkage.SetCheckCatLinkageAsString(value);
}

// Standard Getter
public string GetWDisplayCategory()
{
    return _WDisplayCategory;
}

// Standard Setter
public void SetWDisplayCategory(string value)
{
    _WDisplayCategory = value;
}

// Get<>AsString()
public string GetWDisplayCategoryAsString()
{
    return _WDisplayCategory.PadRight(0);
}

// Set<>AsString()
public void SetWDisplayCategoryAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WDisplayCategory = value;
}

// Standard Getter
public WsSaveScheduleRa GetWsSaveScheduleRa()
{
    return _WsSaveScheduleRa;
}

// Standard Setter
public void SetWsSaveScheduleRa(WsSaveScheduleRa value)
{
    _WsSaveScheduleRa = value;
}

// Get<>AsString()
public string GetWsSaveScheduleRaAsString()
{
    return _WsSaveScheduleRa != null ? _WsSaveScheduleRa.GetWsSaveScheduleRaAsString() : "";
}

// Set<>AsString()
public void SetWsSaveScheduleRaAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsSaveScheduleRa == null)
    {
        _WsSaveScheduleRa = new WsSaveScheduleRa();
    }
    _WsSaveScheduleRa.SetWsSaveScheduleRaAsString(value);
}

// Standard Getter
public WDerivativeFields GetWDerivativeFields()
{
    return _WDerivativeFields;
}

// Standard Setter
public void SetWDerivativeFields(WDerivativeFields value)
{
    _WDerivativeFields = value;
}

// Get<>AsString()
public string GetWDerivativeFieldsAsString()
{
    return _WDerivativeFields != null ? _WDerivativeFields.GetWDerivativeFieldsAsString() : "";
}

// Set<>AsString()
public void SetWDerivativeFieldsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WDerivativeFields == null)
    {
        _WDerivativeFields = new WDerivativeFields();
    }
    _WDerivativeFields.SetWDerivativeFieldsAsString(value);
}

// Standard Getter
public decimal GetWTotalToMaturityDate()
{
    return _WTotalToMaturityDate;
}

// Standard Setter
public void SetWTotalToMaturityDate(decimal value)
{
    _WTotalToMaturityDate = value;
}

// Get<>AsString()
public string GetWTotalToMaturityDateAsString()
{
    return _WTotalToMaturityDate.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWTotalToMaturityDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WTotalToMaturityDate = parsed;
}

// Standard Getter
public WOptionPremium GetWOptionPremium()
{
    return _WOptionPremium;
}

// Standard Setter
public void SetWOptionPremium(WOptionPremium value)
{
    _WOptionPremium = value;
}

// Get<>AsString()
public string GetWOptionPremiumAsString()
{
    return _WOptionPremium != null ? _WOptionPremium.GetWOptionPremiumAsString() : "";
}

// Set<>AsString()
public void SetWOptionPremiumAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WOptionPremium == null)
    {
        _WOptionPremium = new WOptionPremium();
    }
    _WOptionPremium.SetWOptionPremiumAsString(value);
}


public void SyncWIdsFromHeaderIds()
{
    this.SetWIdsAsString(this.GetHeaderIdsAsString());
}

public void SyncHeaderIdsFromWIds()
{
    this.SetHeaderIdsAsString(this.GetWIdsAsString());
}
public void SyncWIdsFromBalanceIds()
{
    this.SetWIdsAsString(this.GetBalanceIdsAsString());
}

public void SyncBalanceIdsFromWIds()
{
    this.SetBalanceIdsAsString(this.GetWIdsAsString());
}
public void SyncWIdsFromAcquisitionIds()
{
    this.SetWIdsAsString(this.GetAcquisitionIdsAsString());
}

public void SyncAcquisitionIdsFromWIds()
{
    this.SetAcquisitionIdsAsString(this.GetWIdsAsString());
}
public void SyncWIdsFromDisposalIds()
{
    this.SetWIdsAsString(this.GetDisposalIdsAsString());
}

public void SyncDisposalIdsFromWIds()
{
    this.SetDisposalIdsAsString(this.GetWIdsAsString());
}
public void SyncWIdsFromFiller92()
{
    this.SetWIdsAsString(this.GetFiller92AsString());
}

public void SyncFiller92FromWIds()
{
    this.SetFiller92AsString(this.GetWIdsAsString());
}
public void SyncCgtMasterRecordFromCgtMasterRecord01()
{
    this.SetCgtMasterRecordAsString(this.GetCgtMasterRecord01AsString());
}

public void SyncCgtMasterRecord01FromCgtMasterRecord()
{
    this.SetCgtMasterRecord01AsString(this.GetCgtMasterRecordAsString());
}
public void SyncCgtMasterRecordFromCgtMasterRecord02()
{
    this.SetCgtMasterRecordAsString(this.GetCgtMasterRecord02AsString());
}

public void SyncCgtMasterRecord02FromCgtMasterRecord()
{
    this.SetCgtMasterRecord02AsString(this.GetCgtMasterRecordAsString());
}
public void SyncCgtMasterRecordFromCgtMasterRecord03()
{
    this.SetCgtMasterRecordAsString(this.GetCgtMasterRecord03AsString());
}

public void SyncCgtMasterRecord03FromCgtMasterRecord()
{
    this.SetCgtMasterRecord03AsString(this.GetCgtMasterRecordAsString());
}
}}