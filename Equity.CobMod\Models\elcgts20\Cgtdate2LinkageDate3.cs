using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing Cgtdate2LinkageDate3 Data Structure

public class Cgtdate2LinkageDate3
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate3, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd3, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd3 =0;
    
    
    
    
    // [DEBUG] Field: Filler20, is_external=, is_static_class=False, static_prefix=
    private Filler20 _Filler20 = new Filler20();
    
    
    
    
    // [DEBUG] Field: Filler21, is_external=, is_static_class=False, static_prefix=
    private Filler21 _Filler21 = new Filler21();
    
    
    
    
    // [DEBUG] Field: Filler22, is_external=, is_static_class=False, static_prefix=
    private Filler22 _Filler22 = new Filler22();
    
    
    
    
    // [DEBUG] Field: Filler23, is_external=, is_static_class=False, static_prefix=
    private Filler23 _Filler23 = new Filler23();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate3AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd3.ToString().PadLeft(8, '0'));
        result.Append(_Filler20.GetFiller20AsString());
        result.Append(_Filler21.GetFiller21AsString());
        result.Append(_Filler22.GetFiller22AsString());
        result.Append(_Filler23.GetFiller23AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate3AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd3(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler20.SetFiller20AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler20.SetFiller20AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler21.SetFiller21AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler21.SetFiller21AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler22.SetFiller22AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler22.SetFiller22AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler23.SetFiller23AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler23.SetFiller23AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate3AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate3(string value)
    {
        SetCgtdate2LinkageDate3AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd3()
    {
        return _Cgtdate2Ccyymmdd3;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd3(int value)
    {
        _Cgtdate2Ccyymmdd3 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd3AsString()
    {
        return _Cgtdate2Ccyymmdd3.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd3 = parsed;
    }
    
    // Standard Getter
    public Filler20 GetFiller20()
    {
        return _Filler20;
    }
    
    // Standard Setter
    public void SetFiller20(Filler20 value)
    {
        _Filler20 = value;
    }
    
    // Get<>AsString()
    public string GetFiller20AsString()
    {
        return _Filler20 != null ? _Filler20.GetFiller20AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller20AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler20 == null)
        {
            _Filler20 = new Filler20();
        }
        _Filler20.SetFiller20AsString(value);
    }
    
    // Standard Getter
    public Filler21 GetFiller21()
    {
        return _Filler21;
    }
    
    // Standard Setter
    public void SetFiller21(Filler21 value)
    {
        _Filler21 = value;
    }
    
    // Get<>AsString()
    public string GetFiller21AsString()
    {
        return _Filler21 != null ? _Filler21.GetFiller21AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller21AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler21 == null)
        {
            _Filler21 = new Filler21();
        }
        _Filler21.SetFiller21AsString(value);
    }
    
    // Standard Getter
    public Filler22 GetFiller22()
    {
        return _Filler22;
    }
    
    // Standard Setter
    public void SetFiller22(Filler22 value)
    {
        _Filler22 = value;
    }
    
    // Get<>AsString()
    public string GetFiller22AsString()
    {
        return _Filler22 != null ? _Filler22.GetFiller22AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller22AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler22 == null)
        {
            _Filler22 = new Filler22();
        }
        _Filler22.SetFiller22AsString(value);
    }
    
    // Standard Getter
    public Filler23 GetFiller23()
    {
        return _Filler23;
    }
    
    // Standard Setter
    public void SetFiller23(Filler23 value)
    {
        _Filler23 = value;
    }
    
    // Get<>AsString()
    public string GetFiller23AsString()
    {
        return _Filler23 != null ? _Filler23.GetFiller23AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller23AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler23 == null)
        {
            _Filler23 = new Filler23();
        }
        _Filler23.SetFiller23AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller20(string value)
    {
        _Filler20.SetFiller20AsString(value);
    }
    // Nested Class: Filler20
    public class Filler20
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc3, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc3 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd3, is_external=, is_static_class=False, static_prefix=
        private Filler20.Cgtdate2Yymmdd3 _Cgtdate2Yymmdd3 = new Filler20.Cgtdate2Yymmdd3();
        
        
        
        
    public Filler20() {}
    
    public Filler20(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc3(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd3.SetCgtdate2Yymmdd3AsString(data.Substring(offset, Cgtdate2Yymmdd3.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller20AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc3.PadRight(2));
        result.Append(_Cgtdate2Yymmdd3.GetCgtdate2Yymmdd3AsString());
        
        return result.ToString();
    }
    
    public void SetFiller20AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc3(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd3.SetCgtdate2Yymmdd3AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd3.SetCgtdate2Yymmdd3AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc3()
    {
        return _Cgtdate2Cc3;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc3(string value)
    {
        _Cgtdate2Cc3 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc3AsString()
    {
        return _Cgtdate2Cc3.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc3 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd3 GetCgtdate2Yymmdd3()
    {
        return _Cgtdate2Yymmdd3;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd3(Cgtdate2Yymmdd3 value)
    {
        _Cgtdate2Yymmdd3 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd3AsString()
    {
        return _Cgtdate2Yymmdd3 != null ? _Cgtdate2Yymmdd3.GetCgtdate2Yymmdd3AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd3 == null)
        {
            _Cgtdate2Yymmdd3 = new Cgtdate2Yymmdd3();
        }
        _Cgtdate2Yymmdd3.SetCgtdate2Yymmdd3AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd3
    public class Cgtdate2Yymmdd3
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy3, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy3 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd3, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd3.Cgtdate2Mmdd3 _Cgtdate2Mmdd3 = new Cgtdate2Yymmdd3.Cgtdate2Mmdd3();
        
        
        
        
    public Cgtdate2Yymmdd3() {}
    
    public Cgtdate2Yymmdd3(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy3(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd3.SetCgtdate2Mmdd3AsString(data.Substring(offset, Cgtdate2Mmdd3.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd3AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy3.PadRight(2));
        result.Append(_Cgtdate2Mmdd3.GetCgtdate2Mmdd3AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd3AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy3(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd3.SetCgtdate2Mmdd3AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd3.SetCgtdate2Mmdd3AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy3()
    {
        return _Cgtdate2Yy3;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy3(string value)
    {
        _Cgtdate2Yy3 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy3AsString()
    {
        return _Cgtdate2Yy3.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy3 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd3 GetCgtdate2Mmdd3()
    {
        return _Cgtdate2Mmdd3;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd3(Cgtdate2Mmdd3 value)
    {
        _Cgtdate2Mmdd3 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd3AsString()
    {
        return _Cgtdate2Mmdd3 != null ? _Cgtdate2Mmdd3.GetCgtdate2Mmdd3AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd3 == null)
        {
            _Cgtdate2Mmdd3 = new Cgtdate2Mmdd3();
        }
        _Cgtdate2Mmdd3.SetCgtdate2Mmdd3AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd3
    public class Cgtdate2Mmdd3
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm3, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm3 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd3, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd3 ="";
        
        
        
        
    public Cgtdate2Mmdd3() {}
    
    public Cgtdate2Mmdd3(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm3(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd3(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd3AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm3.PadRight(2));
        result.Append(_Cgtdate2Dd3.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd3AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm3(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd3(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm3()
    {
        return _Cgtdate2Mm3;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm3(string value)
    {
        _Cgtdate2Mm3 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm3AsString()
    {
        return _Cgtdate2Mm3.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm3 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd3()
    {
        return _Cgtdate2Dd3;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd3(string value)
    {
        _Cgtdate2Dd3 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd3AsString()
    {
        return _Cgtdate2Dd3.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd3 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller21(string value)
{
    _Filler21.SetFiller21AsString(value);
}
// Nested Class: Filler21
public class Filler21
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy3, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy3 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd3, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd3 =0;
    
    
    
    
public Filler21() {}

public Filler21(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy3(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd3(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller21AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy3.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd3.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller21AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy3(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd3(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy3()
{
    return _Cgtdate2CCcyy3;
}

// Standard Setter
public void SetCgtdate2CCcyy3(int value)
{
    _Cgtdate2CCcyy3 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy3AsString()
{
    return _Cgtdate2CCcyy3.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy3 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd3()
{
    return _Cgtdate2CMmdd3;
}

// Standard Setter
public void SetCgtdate2CMmdd3(int value)
{
    _Cgtdate2CMmdd3 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd3AsString()
{
    return _Cgtdate2CMmdd3.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd3 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller22(string value)
{
    _Filler22.SetFiller22AsString(value);
}
// Nested Class: Filler22
public class Filler22
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc3, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc3 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy3, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy3 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm3, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm3 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd3, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd3 =0;
    
    
    
    
public Filler22() {}

public Filler22(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc3(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy3(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm3(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd3(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller22AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc3.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy3.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm3.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd3.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller22AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc3(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy3(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm3(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd3(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc3()
{
    return _Cgtdate2CCc3;
}

// Standard Setter
public void SetCgtdate2CCc3(int value)
{
    _Cgtdate2CCc3 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc3AsString()
{
    return _Cgtdate2CCc3.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc3 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy3()
{
    return _Cgtdate2CYy3;
}

// Standard Setter
public void SetCgtdate2CYy3(int value)
{
    _Cgtdate2CYy3 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy3AsString()
{
    return _Cgtdate2CYy3.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy3 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm3()
{
    return _Cgtdate2CMm3;
}

// Standard Setter
public void SetCgtdate2CMm3(int value)
{
    _Cgtdate2CMm3 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm3AsString()
{
    return _Cgtdate2CMm3.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm3 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd3()
{
    return _Cgtdate2CDd3;
}

// Standard Setter
public void SetCgtdate2CDd3(int value)
{
    _Cgtdate2CDd3 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd3AsString()
{
    return _Cgtdate2CDd3.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd3 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller23(string value)
{
    _Filler23.SetFiller23AsString(value);
}
// Nested Class: Filler23
public class Filler23
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm3, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm3 =0;
    
    
    
    
    // [DEBUG] Field: Filler24, is_external=, is_static_class=False, static_prefix=
    private string _Filler24 ="";
    
    
    
    
public Filler23() {}

public Filler23(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm3(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller24(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller23AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm3.ToString().PadLeft(6, '0'));
    result.Append(_Filler24.PadRight(2));
    
    return result.ToString();
}

public void SetFiller23AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm3(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller24(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm3()
{
    return _Cgtdate2CCcyymm3;
}

// Standard Setter
public void SetCgtdate2CCcyymm3(int value)
{
    _Cgtdate2CCcyymm3 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm3AsString()
{
    return _Cgtdate2CCcyymm3.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm3 = parsed;
}

// Standard Getter
public string GetFiller24()
{
    return _Filler24;
}

// Standard Setter
public void SetFiller24(string value)
{
    _Filler24 = value;
}

// Get<>AsString()
public string GetFiller24AsString()
{
    return _Filler24.PadRight(2);
}

// Set<>AsString()
public void SetFiller24AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler24 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}