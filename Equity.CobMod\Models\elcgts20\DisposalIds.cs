using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing DisposalIds Data Structure

public class DisposalIds
{
    private static int _size = 81;
    // [DEBUG] Class: DisposalIds, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: MddaDisposalId, is_external=, is_static_class=False, static_prefix=
    private string _MddaDisposalId ="";
    
    
    
    
    // [DEBUG] Field: MddaHoldingId, is_external=, is_static_class=False, static_prefix=
    private string _MddaHoldingId ="";
    
    
    
    
    // [DEBUG] Field: MddaParentStockId, is_external=, is_static_class=False, static_prefix=
    private string _MddaParentStockId ="";
    
    
    
    
    // [DEBUG] Field: MddaTransactionCategoryId, is_external=, is_static_class=False, static_prefix=
    private string _MddaTransactionCategoryId ="";
    
    
    
    
    // [DEBUG] Field: MddaCtLinkFundId, is_external=, is_static_class=False, static_prefix=
    private string _MddaCtLinkFundId ="";
    
    
    
    
    // [DEBUG] Field: MddaPrevDispId, is_external=, is_static_class=False, static_prefix=
    private string _MddaPrevDispId ="";
    
    
    
    
    // [DEBUG] Field: MddaPrevAcqnId, is_external=, is_static_class=False, static_prefix=
    private string _MddaPrevAcqnId ="";
    
    
    
    
    // [DEBUG] Field: MddaDbTimestamp, is_external=, is_static_class=False, static_prefix=
    private string _MddaDbTimestamp ="";
    
    
    
    
    // [DEBUG] Field: Filler91, is_external=, is_static_class=False, static_prefix=
    private string _Filler91 ="";
    
    
    
    
    
    // Serialization methods
    public string GetDisposalIdsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_MddaDisposalId.PadRight(8));
        result.Append(_MddaHoldingId.PadRight(8));
        result.Append(_MddaParentStockId.PadRight(8));
        result.Append(_MddaTransactionCategoryId.PadRight(8));
        result.Append(_MddaCtLinkFundId.PadRight(8));
        result.Append(_MddaPrevDispId.PadRight(8));
        result.Append(_MddaPrevAcqnId.PadRight(8));
        result.Append(_MddaDbTimestamp.PadRight(24));
        result.Append(_Filler91.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetDisposalIdsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMddaDisposalId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMddaHoldingId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMddaParentStockId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMddaTransactionCategoryId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMddaCtLinkFundId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMddaPrevDispId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMddaPrevAcqnId(extracted);
        }
        offset += 8;
        if (offset + 24 <= data.Length)
        {
            string extracted = data.Substring(offset, 24).Trim();
            SetMddaDbTimestamp(extracted);
        }
        offset += 24;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller91(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetDisposalIdsAsString();
    }
    // Set<>String Override function
    public void SetDisposalIds(string value)
    {
        SetDisposalIdsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetMddaDisposalId()
    {
        return _MddaDisposalId;
    }
    
    // Standard Setter
    public void SetMddaDisposalId(string value)
    {
        _MddaDisposalId = value;
    }
    
    // Get<>AsString()
    public string GetMddaDisposalIdAsString()
    {
        return _MddaDisposalId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMddaDisposalIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MddaDisposalId = value;
    }
    
    // Standard Getter
    public string GetMddaHoldingId()
    {
        return _MddaHoldingId;
    }
    
    // Standard Setter
    public void SetMddaHoldingId(string value)
    {
        _MddaHoldingId = value;
    }
    
    // Get<>AsString()
    public string GetMddaHoldingIdAsString()
    {
        return _MddaHoldingId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMddaHoldingIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MddaHoldingId = value;
    }
    
    // Standard Getter
    public string GetMddaParentStockId()
    {
        return _MddaParentStockId;
    }
    
    // Standard Setter
    public void SetMddaParentStockId(string value)
    {
        _MddaParentStockId = value;
    }
    
    // Get<>AsString()
    public string GetMddaParentStockIdAsString()
    {
        return _MddaParentStockId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMddaParentStockIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MddaParentStockId = value;
    }
    
    // Standard Getter
    public string GetMddaTransactionCategoryId()
    {
        return _MddaTransactionCategoryId;
    }
    
    // Standard Setter
    public void SetMddaTransactionCategoryId(string value)
    {
        _MddaTransactionCategoryId = value;
    }
    
    // Get<>AsString()
    public string GetMddaTransactionCategoryIdAsString()
    {
        return _MddaTransactionCategoryId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMddaTransactionCategoryIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MddaTransactionCategoryId = value;
    }
    
    // Standard Getter
    public string GetMddaCtLinkFundId()
    {
        return _MddaCtLinkFundId;
    }
    
    // Standard Setter
    public void SetMddaCtLinkFundId(string value)
    {
        _MddaCtLinkFundId = value;
    }
    
    // Get<>AsString()
    public string GetMddaCtLinkFundIdAsString()
    {
        return _MddaCtLinkFundId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMddaCtLinkFundIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MddaCtLinkFundId = value;
    }
    
    // Standard Getter
    public string GetMddaPrevDispId()
    {
        return _MddaPrevDispId;
    }
    
    // Standard Setter
    public void SetMddaPrevDispId(string value)
    {
        _MddaPrevDispId = value;
    }
    
    // Get<>AsString()
    public string GetMddaPrevDispIdAsString()
    {
        return _MddaPrevDispId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMddaPrevDispIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MddaPrevDispId = value;
    }
    
    // Standard Getter
    public string GetMddaPrevAcqnId()
    {
        return _MddaPrevAcqnId;
    }
    
    // Standard Setter
    public void SetMddaPrevAcqnId(string value)
    {
        _MddaPrevAcqnId = value;
    }
    
    // Get<>AsString()
    public string GetMddaPrevAcqnIdAsString()
    {
        return _MddaPrevAcqnId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMddaPrevAcqnIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MddaPrevAcqnId = value;
    }
    
    // Standard Getter
    public string GetMddaDbTimestamp()
    {
        return _MddaDbTimestamp;
    }
    
    // Standard Setter
    public void SetMddaDbTimestamp(string value)
    {
        _MddaDbTimestamp = value;
    }
    
    // Get<>AsString()
    public string GetMddaDbTimestampAsString()
    {
        return _MddaDbTimestamp.PadRight(24);
    }
    
    // Set<>AsString()
    public void SetMddaDbTimestampAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MddaDbTimestamp = value;
    }
    
    // Standard Getter
    public string GetFiller91()
    {
        return _Filler91;
    }
    
    // Standard Setter
    public void SetFiller91(string value)
    {
        _Filler91 = value;
    }
    
    // Get<>AsString()
    public string GetFiller91AsString()
    {
        return _Filler91.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller91AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler91 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}