using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D81Record Data Structure

public class D81Record
{
    private static int _size = 120;
    // [DEBUG] Class: D81Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D81KeyRecord, is_external=, is_static_class=False, static_prefix=
    private D81KeyRecord _D81KeyRecord = new D81KeyRecord();
    
    
    
    
    
    // Serialization methods
    public string GetD81RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D81KeyRecord.GetD81KeyRecordAsString());
        
        return result.ToString();
    }
    
    public void SetD81RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 120 <= data.Length)
        {
            _D81KeyRecord.SetD81KeyRecordAsString(data.Substring(offset, 120));
        }
        else
        {
            _D81KeyRecord.SetD81KeyRecordAsString(data.Substring(offset));
        }
        offset += 120;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD81RecordAsString();
    }
    // Set<>String Override function
    public void SetD81Record(string value)
    {
        SetD81RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D81KeyRecord GetD81KeyRecord()
    {
        return _D81KeyRecord;
    }
    
    // Standard Setter
    public void SetD81KeyRecord(D81KeyRecord value)
    {
        _D81KeyRecord = value;
    }
    
    // Get<>AsString()
    public string GetD81KeyRecordAsString()
    {
        return _D81KeyRecord != null ? _D81KeyRecord.GetD81KeyRecordAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD81KeyRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D81KeyRecord == null)
        {
            _D81KeyRecord = new D81KeyRecord();
        }
        _D81KeyRecord.SetD81KeyRecordAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD81KeyRecord(string value)
    {
        _D81KeyRecord.SetD81KeyRecordAsString(value);
    }
    // Nested Class: D81KeyRecord
    public class D81KeyRecord
    {
        private static int _size = 120;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D81SplitKey01, is_external=, is_static_class=False, static_prefix=
        private D81KeyRecord.D81SplitKey01 _D81SplitKey01 = new D81KeyRecord.D81SplitKey01();
        
        
        
        
        // [DEBUG] Field: Filler109, is_external=, is_static_class=False, static_prefix=
        private string _Filler109 ="";
        
        
        
        
        // [DEBUG] Field: D81SplitKey02, is_external=, is_static_class=False, static_prefix=
        private D81KeyRecord.D81SplitKey02 _D81SplitKey02 = new D81KeyRecord.D81SplitKey02();
        
        
        
        
        // [DEBUG] Field: D81SplitKey04, is_external=, is_static_class=False, static_prefix=
        private D81KeyRecord.D81SplitKey04 _D81SplitKey04 = new D81KeyRecord.D81SplitKey04();
        
        
        
        
        // [DEBUG] Field: D81SplitKey06, is_external=, is_static_class=False, static_prefix=
        private D81KeyRecord.D81SplitKey06 _D81SplitKey06 = new D81KeyRecord.D81SplitKey06();
        
        
        
        
        // [DEBUG] Field: Filler110, is_external=, is_static_class=False, static_prefix=
        private string _Filler110 ="";
        
        
        
        
        // [DEBUG] Field: D81SplitKey07, is_external=, is_static_class=False, static_prefix=
        private D81KeyRecord.D81SplitKey07 _D81SplitKey07 = new D81KeyRecord.D81SplitKey07();
        
        
        
        
        // [DEBUG] Field: D81SplitKey03, is_external=, is_static_class=False, static_prefix=
        private D81KeyRecord.D81SplitKey03 _D81SplitKey03 = new D81KeyRecord.D81SplitKey03();
        
        
        
        
        // [DEBUG] Field: Filler111, is_external=, is_static_class=False, static_prefix=
        private string _Filler111 ="";
        
        
        
        
        // [DEBUG] Field: D81TrancheFlag, is_external=, is_static_class=False, static_prefix=
        private string _D81TrancheFlag ="";
        
        
        
        
        // [DEBUG] Field: Filler112, is_external=, is_static_class=False, static_prefix=
        private string _Filler112 ="";
        
        
        
        
        // [DEBUG] Field: Filler113, is_external=, is_static_class=False, static_prefix=
        private string _Filler113 ="";
        
        
        
        
        // [DEBUG] Field: Filler114, is_external=, is_static_class=False, static_prefix=
        private string _Filler114 ="";
        
        
        
        
        // [DEBUG] Field: D81SplitKey05, is_external=, is_static_class=False, static_prefix=
        private D81KeyRecord.D81SplitKey05 _D81SplitKey05 = new D81KeyRecord.D81SplitKey05();
        
        
        
        
        // [DEBUG] Field: Filler115, is_external=, is_static_class=False, static_prefix=
        private string _Filler115 ="";
        
        
        
        
        // [DEBUG] Field: Filler116, is_external=, is_static_class=False, static_prefix=
        private string _Filler116 ="";
        
        
        
        
        // [DEBUG] Field: Filler117, is_external=, is_static_class=False, static_prefix=
        private string _Filler117 ="";
        
        
        
        
        // [DEBUG] Field: Filler118, is_external=, is_static_class=False, static_prefix=
        private string _Filler118 ="";
        
        
        
        
    public D81KeyRecord() {}
    
    public D81KeyRecord(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _D81SplitKey01.SetD81SplitKey01AsString(data.Substring(offset, D81SplitKey01.GetSize()));
        offset += 1;
        SetFiller109(data.Substring(offset, 0).Trim());
        offset += 0;
        _D81SplitKey02.SetD81SplitKey02AsString(data.Substring(offset, D81SplitKey02.GetSize()));
        offset += 0;
        _D81SplitKey04.SetD81SplitKey04AsString(data.Substring(offset, D81SplitKey04.GetSize()));
        offset += 0;
        _D81SplitKey06.SetD81SplitKey06AsString(data.Substring(offset, D81SplitKey06.GetSize()));
        offset += 0;
        SetFiller110(data.Substring(offset, 110).Trim());
        offset += 110;
        _D81SplitKey07.SetD81SplitKey07AsString(data.Substring(offset, D81SplitKey07.GetSize()));
        offset += 9;
        _D81SplitKey03.SetD81SplitKey03AsString(data.Substring(offset, D81SplitKey03.GetSize()));
        offset += 0;
        SetFiller111(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD81TrancheFlag(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller112(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller113(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller114(data.Substring(offset, 0).Trim());
        offset += 0;
        _D81SplitKey05.SetD81SplitKey05AsString(data.Substring(offset, D81SplitKey05.GetSize()));
        offset += 0;
        SetFiller115(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller116(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller117(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller118(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD81KeyRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D81SplitKey01.GetD81SplitKey01AsString());
        result.Append(_Filler109.PadRight(0));
        result.Append(_D81SplitKey02.GetD81SplitKey02AsString());
        result.Append(_D81SplitKey04.GetD81SplitKey04AsString());
        result.Append(_D81SplitKey06.GetD81SplitKey06AsString());
        result.Append(_Filler110.PadRight(110));
        result.Append(_D81SplitKey07.GetD81SplitKey07AsString());
        result.Append(_D81SplitKey03.GetD81SplitKey03AsString());
        result.Append(_Filler111.PadRight(0));
        result.Append(_D81TrancheFlag.PadRight(0));
        result.Append(_Filler112.PadRight(0));
        result.Append(_Filler113.PadRight(0));
        result.Append(_Filler114.PadRight(0));
        result.Append(_D81SplitKey05.GetD81SplitKey05AsString());
        result.Append(_Filler115.PadRight(0));
        result.Append(_Filler116.PadRight(0));
        result.Append(_Filler117.PadRight(0));
        result.Append(_Filler118.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD81KeyRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            _D81SplitKey01.SetD81SplitKey01AsString(data.Substring(offset, 1));
        }
        else
        {
            _D81SplitKey01.SetD81SplitKey01AsString(data.Substring(offset));
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller109(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D81SplitKey02.SetD81SplitKey02AsString(data.Substring(offset, 0));
        }
        else
        {
            _D81SplitKey02.SetD81SplitKey02AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D81SplitKey04.SetD81SplitKey04AsString(data.Substring(offset, 0));
        }
        else
        {
            _D81SplitKey04.SetD81SplitKey04AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D81SplitKey06.SetD81SplitKey06AsString(data.Substring(offset, 0));
        }
        else
        {
            _D81SplitKey06.SetD81SplitKey06AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 110 <= data.Length)
        {
            string extracted = data.Substring(offset, 110).Trim();
            SetFiller110(extracted);
        }
        offset += 110;
        if (offset + 9 <= data.Length)
        {
            _D81SplitKey07.SetD81SplitKey07AsString(data.Substring(offset, 9));
        }
        else
        {
            _D81SplitKey07.SetD81SplitKey07AsString(data.Substring(offset));
        }
        offset += 9;
        if (offset + 0 <= data.Length)
        {
            _D81SplitKey03.SetD81SplitKey03AsString(data.Substring(offset, 0));
        }
        else
        {
            _D81SplitKey03.SetD81SplitKey03AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller111(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD81TrancheFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller112(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller113(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller114(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D81SplitKey05.SetD81SplitKey05AsString(data.Substring(offset, 0));
        }
        else
        {
            _D81SplitKey05.SetD81SplitKey05AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller115(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller116(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller117(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller118(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public D81SplitKey01 GetD81SplitKey01()
    {
        return _D81SplitKey01;
    }
    
    // Standard Setter
    public void SetD81SplitKey01(D81SplitKey01 value)
    {
        _D81SplitKey01 = value;
    }
    
    // Get<>AsString()
    public string GetD81SplitKey01AsString()
    {
        return _D81SplitKey01 != null ? _D81SplitKey01.GetD81SplitKey01AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD81SplitKey01AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D81SplitKey01 == null)
        {
            _D81SplitKey01 = new D81SplitKey01();
        }
        _D81SplitKey01.SetD81SplitKey01AsString(value);
    }
    
    // Standard Getter
    public string GetFiller109()
    {
        return _Filler109;
    }
    
    // Standard Setter
    public void SetFiller109(string value)
    {
        _Filler109 = value;
    }
    
    // Get<>AsString()
    public string GetFiller109AsString()
    {
        return _Filler109.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller109AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler109 = value;
    }
    
    // Standard Getter
    public D81SplitKey02 GetD81SplitKey02()
    {
        return _D81SplitKey02;
    }
    
    // Standard Setter
    public void SetD81SplitKey02(D81SplitKey02 value)
    {
        _D81SplitKey02 = value;
    }
    
    // Get<>AsString()
    public string GetD81SplitKey02AsString()
    {
        return _D81SplitKey02 != null ? _D81SplitKey02.GetD81SplitKey02AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD81SplitKey02AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D81SplitKey02 == null)
        {
            _D81SplitKey02 = new D81SplitKey02();
        }
        _D81SplitKey02.SetD81SplitKey02AsString(value);
    }
    
    // Standard Getter
    public D81SplitKey04 GetD81SplitKey04()
    {
        return _D81SplitKey04;
    }
    
    // Standard Setter
    public void SetD81SplitKey04(D81SplitKey04 value)
    {
        _D81SplitKey04 = value;
    }
    
    // Get<>AsString()
    public string GetD81SplitKey04AsString()
    {
        return _D81SplitKey04 != null ? _D81SplitKey04.GetD81SplitKey04AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD81SplitKey04AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D81SplitKey04 == null)
        {
            _D81SplitKey04 = new D81SplitKey04();
        }
        _D81SplitKey04.SetD81SplitKey04AsString(value);
    }
    
    // Standard Getter
    public D81SplitKey06 GetD81SplitKey06()
    {
        return _D81SplitKey06;
    }
    
    // Standard Setter
    public void SetD81SplitKey06(D81SplitKey06 value)
    {
        _D81SplitKey06 = value;
    }
    
    // Get<>AsString()
    public string GetD81SplitKey06AsString()
    {
        return _D81SplitKey06 != null ? _D81SplitKey06.GetD81SplitKey06AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD81SplitKey06AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D81SplitKey06 == null)
        {
            _D81SplitKey06 = new D81SplitKey06();
        }
        _D81SplitKey06.SetD81SplitKey06AsString(value);
    }
    
    // Standard Getter
    public string GetFiller110()
    {
        return _Filler110;
    }
    
    // Standard Setter
    public void SetFiller110(string value)
    {
        _Filler110 = value;
    }
    
    // Get<>AsString()
    public string GetFiller110AsString()
    {
        return _Filler110.PadRight(110);
    }
    
    // Set<>AsString()
    public void SetFiller110AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler110 = value;
    }
    
    // Standard Getter
    public D81SplitKey07 GetD81SplitKey07()
    {
        return _D81SplitKey07;
    }
    
    // Standard Setter
    public void SetD81SplitKey07(D81SplitKey07 value)
    {
        _D81SplitKey07 = value;
    }
    
    // Get<>AsString()
    public string GetD81SplitKey07AsString()
    {
        return _D81SplitKey07 != null ? _D81SplitKey07.GetD81SplitKey07AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD81SplitKey07AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D81SplitKey07 == null)
        {
            _D81SplitKey07 = new D81SplitKey07();
        }
        _D81SplitKey07.SetD81SplitKey07AsString(value);
    }
    
    // Standard Getter
    public D81SplitKey03 GetD81SplitKey03()
    {
        return _D81SplitKey03;
    }
    
    // Standard Setter
    public void SetD81SplitKey03(D81SplitKey03 value)
    {
        _D81SplitKey03 = value;
    }
    
    // Get<>AsString()
    public string GetD81SplitKey03AsString()
    {
        return _D81SplitKey03 != null ? _D81SplitKey03.GetD81SplitKey03AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD81SplitKey03AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D81SplitKey03 == null)
        {
            _D81SplitKey03 = new D81SplitKey03();
        }
        _D81SplitKey03.SetD81SplitKey03AsString(value);
    }
    
    // Standard Getter
    public string GetFiller111()
    {
        return _Filler111;
    }
    
    // Standard Setter
    public void SetFiller111(string value)
    {
        _Filler111 = value;
    }
    
    // Get<>AsString()
    public string GetFiller111AsString()
    {
        return _Filler111.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller111AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler111 = value;
    }
    
    // Standard Getter
    public string GetD81TrancheFlag()
    {
        return _D81TrancheFlag;
    }
    
    // Standard Setter
    public void SetD81TrancheFlag(string value)
    {
        _D81TrancheFlag = value;
    }
    
    // Get<>AsString()
    public string GetD81TrancheFlagAsString()
    {
        return _D81TrancheFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD81TrancheFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D81TrancheFlag = value;
    }
    
    // Standard Getter
    public string GetFiller112()
    {
        return _Filler112;
    }
    
    // Standard Setter
    public void SetFiller112(string value)
    {
        _Filler112 = value;
    }
    
    // Get<>AsString()
    public string GetFiller112AsString()
    {
        return _Filler112.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller112AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler112 = value;
    }
    
    // Standard Getter
    public string GetFiller113()
    {
        return _Filler113;
    }
    
    // Standard Setter
    public void SetFiller113(string value)
    {
        _Filler113 = value;
    }
    
    // Get<>AsString()
    public string GetFiller113AsString()
    {
        return _Filler113.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller113AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler113 = value;
    }
    
    // Standard Getter
    public string GetFiller114()
    {
        return _Filler114;
    }
    
    // Standard Setter
    public void SetFiller114(string value)
    {
        _Filler114 = value;
    }
    
    // Get<>AsString()
    public string GetFiller114AsString()
    {
        return _Filler114.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller114AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler114 = value;
    }
    
    // Standard Getter
    public D81SplitKey05 GetD81SplitKey05()
    {
        return _D81SplitKey05;
    }
    
    // Standard Setter
    public void SetD81SplitKey05(D81SplitKey05 value)
    {
        _D81SplitKey05 = value;
    }
    
    // Get<>AsString()
    public string GetD81SplitKey05AsString()
    {
        return _D81SplitKey05 != null ? _D81SplitKey05.GetD81SplitKey05AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD81SplitKey05AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D81SplitKey05 == null)
        {
            _D81SplitKey05 = new D81SplitKey05();
        }
        _D81SplitKey05.SetD81SplitKey05AsString(value);
    }
    
    // Standard Getter
    public string GetFiller115()
    {
        return _Filler115;
    }
    
    // Standard Setter
    public void SetFiller115(string value)
    {
        _Filler115 = value;
    }
    
    // Get<>AsString()
    public string GetFiller115AsString()
    {
        return _Filler115.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller115AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler115 = value;
    }
    
    // Standard Getter
    public string GetFiller116()
    {
        return _Filler116;
    }
    
    // Standard Setter
    public void SetFiller116(string value)
    {
        _Filler116 = value;
    }
    
    // Get<>AsString()
    public string GetFiller116AsString()
    {
        return _Filler116.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller116AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler116 = value;
    }
    
    // Standard Getter
    public string GetFiller117()
    {
        return _Filler117;
    }
    
    // Standard Setter
    public void SetFiller117(string value)
    {
        _Filler117 = value;
    }
    
    // Get<>AsString()
    public string GetFiller117AsString()
    {
        return _Filler117.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller117AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler117 = value;
    }
    
    // Standard Getter
    public string GetFiller118()
    {
        return _Filler118;
    }
    
    // Standard Setter
    public void SetFiller118(string value)
    {
        _Filler118 = value;
    }
    
    // Get<>AsString()
    public string GetFiller118AsString()
    {
        return _Filler118.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller118AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler118 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D81SplitKey01
    public class D81SplitKey01
    {
        private static int _size = 1;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D81CurrencySort, is_external=, is_static_class=False, static_prefix=
        private int _D81CurrencySort =0;
        
        
        // 88-level condition checks for D81CurrencySort
        public bool IsD81CurrencySterling()
        {
            if (this._D81CurrencySort == 0) return true;
            return false;
        }
        public bool IsD81CurrencyEuro()
        {
            if (this._D81CurrencySort == 1) return true;
            return false;
        }
        
        
        // [DEBUG] Field: D81CoAcLk, is_external=, is_static_class=False, static_prefix=
        private string _D81CoAcLk ="";
        
        
        
        
        // [DEBUG] Field: D81CountryCode, is_external=, is_static_class=False, static_prefix=
        private string _D81CountryCode ="";
        
        
        
        
    public D81SplitKey01() {}
    
    public D81SplitKey01(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD81CurrencySort(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetD81CoAcLk(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD81CountryCode(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD81SplitKey01AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D81CurrencySort.ToString().PadLeft(1, '0'));
        result.Append(_D81CoAcLk.PadRight(0));
        result.Append(_D81CountryCode.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD81SplitKey01AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD81CurrencySort(parsedInt);
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD81CoAcLk(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD81CountryCode(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD81CurrencySort()
    {
        return _D81CurrencySort;
    }
    
    // Standard Setter
    public void SetD81CurrencySort(int value)
    {
        _D81CurrencySort = value;
    }
    
    // Get<>AsString()
    public string GetD81CurrencySortAsString()
    {
        return _D81CurrencySort.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD81CurrencySortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D81CurrencySort = parsed;
    }
    
    // Standard Getter
    public string GetD81CoAcLk()
    {
        return _D81CoAcLk;
    }
    
    // Standard Setter
    public void SetD81CoAcLk(string value)
    {
        _D81CoAcLk = value;
    }
    
    // Get<>AsString()
    public string GetD81CoAcLkAsString()
    {
        return _D81CoAcLk.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD81CoAcLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D81CoAcLk = value;
    }
    
    // Standard Getter
    public string GetD81CountryCode()
    {
        return _D81CountryCode;
    }
    
    // Standard Setter
    public void SetD81CountryCode(string value)
    {
        _D81CountryCode = value;
    }
    
    // Get<>AsString()
    public string GetD81CountryCodeAsString()
    {
        return _D81CountryCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD81CountryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D81CountryCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: D81SplitKey02
public class D81SplitKey02
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D81MainGroup23, is_external=, is_static_class=False, static_prefix=
    private string _D81MainGroup23 ="";
    
    
    
    
    // [DEBUG] Field: D81SecurityType, is_external=, is_static_class=False, static_prefix=
    private string _D81SecurityType ="";
    
    
    
    
    // [DEBUG] Field: D81SecuritySortCode, is_external=, is_static_class=False, static_prefix=
    private string _D81SecuritySortCode ="";
    
    
    
    
    // [DEBUG] Field: D81SedolSort, is_external=, is_static_class=False, static_prefix=
    private string _D81SedolSort ="";
    
    
    
    
    // [DEBUG] Field: D81RecordType, is_external=, is_static_class=False, static_prefix=
    private string _D81RecordType ="";
    
    
    
    
public D81SplitKey02() {}

public D81SplitKey02(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD81MainGroup23(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD81SecurityType(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD81SecuritySortCode(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD81SedolSort(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD81RecordType(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD81SplitKey02AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D81MainGroup23.PadRight(0));
    result.Append(_D81SecurityType.PadRight(0));
    result.Append(_D81SecuritySortCode.PadRight(0));
    result.Append(_D81SedolSort.PadRight(0));
    result.Append(_D81RecordType.PadRight(0));
    
    return result.ToString();
}

public void SetD81SplitKey02AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD81MainGroup23(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD81SecurityType(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD81SecuritySortCode(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD81SedolSort(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD81RecordType(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD81MainGroup23()
{
    return _D81MainGroup23;
}

// Standard Setter
public void SetD81MainGroup23(string value)
{
    _D81MainGroup23 = value;
}

// Get<>AsString()
public string GetD81MainGroup23AsString()
{
    return _D81MainGroup23.PadRight(0);
}

// Set<>AsString()
public void SetD81MainGroup23AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81MainGroup23 = value;
}

// Standard Getter
public string GetD81SecurityType()
{
    return _D81SecurityType;
}

// Standard Setter
public void SetD81SecurityType(string value)
{
    _D81SecurityType = value;
}

// Get<>AsString()
public string GetD81SecurityTypeAsString()
{
    return _D81SecurityType.PadRight(0);
}

// Set<>AsString()
public void SetD81SecurityTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81SecurityType = value;
}

// Standard Getter
public string GetD81SecuritySortCode()
{
    return _D81SecuritySortCode;
}

// Standard Setter
public void SetD81SecuritySortCode(string value)
{
    _D81SecuritySortCode = value;
}

// Get<>AsString()
public string GetD81SecuritySortCodeAsString()
{
    return _D81SecuritySortCode.PadRight(0);
}

// Set<>AsString()
public void SetD81SecuritySortCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81SecuritySortCode = value;
}

// Standard Getter
public string GetD81SedolSort()
{
    return _D81SedolSort;
}

// Standard Setter
public void SetD81SedolSort(string value)
{
    _D81SedolSort = value;
}

// Get<>AsString()
public string GetD81SedolSortAsString()
{
    return _D81SedolSort.PadRight(0);
}

// Set<>AsString()
public void SetD81SedolSortAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81SedolSort = value;
}

// Standard Getter
public string GetD81RecordType()
{
    return _D81RecordType;
}

// Standard Setter
public void SetD81RecordType(string value)
{
    _D81RecordType = value;
}

// Get<>AsString()
public string GetD81RecordTypeAsString()
{
    return _D81RecordType.PadRight(0);
}

// Set<>AsString()
public void SetD81RecordTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81RecordType = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D81SplitKey04
public class D81SplitKey04
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D81AcquisitionDate, is_external=, is_static_class=False, static_prefix=
    private D81SplitKey04.D81AcquisitionDate _D81AcquisitionDate = new D81SplitKey04.D81AcquisitionDate();
    
    
    
    
public D81SplitKey04() {}

public D81SplitKey04(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D81AcquisitionDate.SetD81AcquisitionDateAsString(data.Substring(offset, D81AcquisitionDate.GetSize()));
    offset += 0;
    
}

// Serialization methods
public string GetD81SplitKey04AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D81AcquisitionDate.GetD81AcquisitionDateAsString());
    
    return result.ToString();
}

public void SetD81SplitKey04AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        _D81AcquisitionDate.SetD81AcquisitionDateAsString(data.Substring(offset, 0));
    }
    else
    {
        _D81AcquisitionDate.SetD81AcquisitionDateAsString(data.Substring(offset));
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public D81AcquisitionDate GetD81AcquisitionDate()
{
    return _D81AcquisitionDate;
}

// Standard Setter
public void SetD81AcquisitionDate(D81AcquisitionDate value)
{
    _D81AcquisitionDate = value;
}

// Get<>AsString()
public string GetD81AcquisitionDateAsString()
{
    return _D81AcquisitionDate != null ? _D81AcquisitionDate.GetD81AcquisitionDateAsString() : "";
}

// Set<>AsString()
public void SetD81AcquisitionDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D81AcquisitionDate == null)
    {
        _D81AcquisitionDate = new D81AcquisitionDate();
    }
    _D81AcquisitionDate.SetD81AcquisitionDateAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D81AcquisitionDate
public class D81AcquisitionDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D81AcquisitionDateYy, is_external=, is_static_class=False, static_prefix=
    private string _D81AcquisitionDateYy ="";
    
    
    
    
    // [DEBUG] Field: D81AcquisitionDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D81AcquisitionDateMm ="";
    
    
    
    
    // [DEBUG] Field: D81AcquisitionDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D81AcquisitionDateDd ="";
    
    
    
    
public D81AcquisitionDate() {}

public D81AcquisitionDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD81AcquisitionDateYy(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD81AcquisitionDateMm(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD81AcquisitionDateDd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD81AcquisitionDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D81AcquisitionDateYy.PadRight(0));
    result.Append(_D81AcquisitionDateMm.PadRight(0));
    result.Append(_D81AcquisitionDateDd.PadRight(0));
    
    return result.ToString();
}

public void SetD81AcquisitionDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD81AcquisitionDateYy(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD81AcquisitionDateMm(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD81AcquisitionDateDd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD81AcquisitionDateYy()
{
    return _D81AcquisitionDateYy;
}

// Standard Setter
public void SetD81AcquisitionDateYy(string value)
{
    _D81AcquisitionDateYy = value;
}

// Get<>AsString()
public string GetD81AcquisitionDateYyAsString()
{
    return _D81AcquisitionDateYy.PadRight(0);
}

// Set<>AsString()
public void SetD81AcquisitionDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81AcquisitionDateYy = value;
}

// Standard Getter
public string GetD81AcquisitionDateMm()
{
    return _D81AcquisitionDateMm;
}

// Standard Setter
public void SetD81AcquisitionDateMm(string value)
{
    _D81AcquisitionDateMm = value;
}

// Get<>AsString()
public string GetD81AcquisitionDateMmAsString()
{
    return _D81AcquisitionDateMm.PadRight(0);
}

// Set<>AsString()
public void SetD81AcquisitionDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81AcquisitionDateMm = value;
}

// Standard Getter
public string GetD81AcquisitionDateDd()
{
    return _D81AcquisitionDateDd;
}

// Standard Setter
public void SetD81AcquisitionDateDd(string value)
{
    _D81AcquisitionDateDd = value;
}

// Get<>AsString()
public string GetD81AcquisitionDateDdAsString()
{
    return _D81AcquisitionDateDd.PadRight(0);
}

// Set<>AsString()
public void SetD81AcquisitionDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81AcquisitionDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: D81SplitKey06
public class D81SplitKey06
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D81TrancheContractNumber, is_external=, is_static_class=False, static_prefix=
    private string _D81TrancheContractNumber ="";
    
    
    
    
    // [DEBUG] Field: D81LineNumber, is_external=, is_static_class=False, static_prefix=
    private string _D81LineNumber ="";
    
    
    
    
    // [DEBUG] Field: D81BdvIndicator, is_external=, is_static_class=False, static_prefix=
    private string _D81BdvIndicator ="";
    
    
    
    
public D81SplitKey06() {}

public D81SplitKey06(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD81TrancheContractNumber(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD81LineNumber(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD81BdvIndicator(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD81SplitKey06AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D81TrancheContractNumber.PadRight(0));
    result.Append(_D81LineNumber.PadRight(0));
    result.Append(_D81BdvIndicator.PadRight(0));
    
    return result.ToString();
}

public void SetD81SplitKey06AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD81TrancheContractNumber(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD81LineNumber(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD81BdvIndicator(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD81TrancheContractNumber()
{
    return _D81TrancheContractNumber;
}

// Standard Setter
public void SetD81TrancheContractNumber(string value)
{
    _D81TrancheContractNumber = value;
}

// Get<>AsString()
public string GetD81TrancheContractNumberAsString()
{
    return _D81TrancheContractNumber.PadRight(0);
}

// Set<>AsString()
public void SetD81TrancheContractNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81TrancheContractNumber = value;
}

// Standard Getter
public string GetD81LineNumber()
{
    return _D81LineNumber;
}

// Standard Setter
public void SetD81LineNumber(string value)
{
    _D81LineNumber = value;
}

// Get<>AsString()
public string GetD81LineNumberAsString()
{
    return _D81LineNumber.PadRight(0);
}

// Set<>AsString()
public void SetD81LineNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81LineNumber = value;
}

// Standard Getter
public string GetD81BdvIndicator()
{
    return _D81BdvIndicator;
}

// Standard Setter
public void SetD81BdvIndicator(string value)
{
    _D81BdvIndicator = value;
}

// Get<>AsString()
public string GetD81BdvIndicatorAsString()
{
    return _D81BdvIndicator.PadRight(0);
}

// Set<>AsString()
public void SetD81BdvIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81BdvIndicator = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D81SplitKey07
public class D81SplitKey07
{
    private static int _size = 9;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D81SequenceNumber, is_external=, is_static_class=False, static_prefix=
    private int _D81SequenceNumber =0;
    
    
    
    
public D81SplitKey07() {}

public D81SplitKey07(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD81SequenceNumber(int.Parse(data.Substring(offset, 9).Trim()));
    offset += 9;
    
}

// Serialization methods
public string GetD81SplitKey07AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D81SequenceNumber.ToString().PadLeft(9, '0'));
    
    return result.ToString();
}

public void SetD81SplitKey07AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD81SequenceNumber(parsedInt);
    }
    offset += 9;
}

// Getter and Setter methods

// Standard Getter
public int GetD81SequenceNumber()
{
    return _D81SequenceNumber;
}

// Standard Setter
public void SetD81SequenceNumber(int value)
{
    _D81SequenceNumber = value;
}

// Get<>AsString()
public string GetD81SequenceNumberAsString()
{
    return _D81SequenceNumber.ToString().PadLeft(9, '0');
}

// Set<>AsString()
public void SetD81SequenceNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D81SequenceNumber = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D81SplitKey03
public class D81SplitKey03
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D81AcquisitionDateCc, is_external=, is_static_class=False, static_prefix=
    private string _D81AcquisitionDateCc ="";
    
    
    
    
public D81SplitKey03() {}

public D81SplitKey03(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD81AcquisitionDateCc(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD81SplitKey03AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D81AcquisitionDateCc.PadRight(0));
    
    return result.ToString();
}

public void SetD81SplitKey03AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD81AcquisitionDateCc(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD81AcquisitionDateCc()
{
    return _D81AcquisitionDateCc;
}

// Standard Setter
public void SetD81AcquisitionDateCc(string value)
{
    _D81AcquisitionDateCc = value;
}

// Get<>AsString()
public string GetD81AcquisitionDateCcAsString()
{
    return _D81AcquisitionDateCc.PadRight(0);
}

// Set<>AsString()
public void SetD81AcquisitionDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81AcquisitionDateCc = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D81SplitKey05
public class D81SplitKey05
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D81TaperDateCc, is_external=, is_static_class=False, static_prefix=
    private string _D81TaperDateCc ="";
    
    
    
    
    // [DEBUG] Field: D81TaperDate, is_external=, is_static_class=False, static_prefix=
    private D81SplitKey05.D81TaperDate _D81TaperDate = new D81SplitKey05.D81TaperDate();
    
    
    
    
public D81SplitKey05() {}

public D81SplitKey05(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD81TaperDateCc(data.Substring(offset, 0).Trim());
    offset += 0;
    _D81TaperDate.SetD81TaperDateAsString(data.Substring(offset, D81TaperDate.GetSize()));
    offset += 0;
    
}

// Serialization methods
public string GetD81SplitKey05AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D81TaperDateCc.PadRight(0));
    result.Append(_D81TaperDate.GetD81TaperDateAsString());
    
    return result.ToString();
}

public void SetD81SplitKey05AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD81TaperDateCc(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        _D81TaperDate.SetD81TaperDateAsString(data.Substring(offset, 0));
    }
    else
    {
        _D81TaperDate.SetD81TaperDateAsString(data.Substring(offset));
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD81TaperDateCc()
{
    return _D81TaperDateCc;
}

// Standard Setter
public void SetD81TaperDateCc(string value)
{
    _D81TaperDateCc = value;
}

// Get<>AsString()
public string GetD81TaperDateCcAsString()
{
    return _D81TaperDateCc.PadRight(0);
}

// Set<>AsString()
public void SetD81TaperDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81TaperDateCc = value;
}

// Standard Getter
public D81TaperDate GetD81TaperDate()
{
    return _D81TaperDate;
}

// Standard Setter
public void SetD81TaperDate(D81TaperDate value)
{
    _D81TaperDate = value;
}

// Get<>AsString()
public string GetD81TaperDateAsString()
{
    return _D81TaperDate != null ? _D81TaperDate.GetD81TaperDateAsString() : "";
}

// Set<>AsString()
public void SetD81TaperDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D81TaperDate == null)
    {
        _D81TaperDate = new D81TaperDate();
    }
    _D81TaperDate.SetD81TaperDateAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D81TaperDate
public class D81TaperDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D81TaperDateYy, is_external=, is_static_class=False, static_prefix=
    private string _D81TaperDateYy ="";
    
    
    
    
    // [DEBUG] Field: D81TaperDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D81TaperDateMm ="";
    
    
    
    
    // [DEBUG] Field: D81TaperDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D81TaperDateDd ="";
    
    
    
    
public D81TaperDate() {}

public D81TaperDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD81TaperDateYy(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD81TaperDateMm(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD81TaperDateDd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD81TaperDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D81TaperDateYy.PadRight(0));
    result.Append(_D81TaperDateMm.PadRight(0));
    result.Append(_D81TaperDateDd.PadRight(0));
    
    return result.ToString();
}

public void SetD81TaperDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD81TaperDateYy(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD81TaperDateMm(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD81TaperDateDd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD81TaperDateYy()
{
    return _D81TaperDateYy;
}

// Standard Setter
public void SetD81TaperDateYy(string value)
{
    _D81TaperDateYy = value;
}

// Get<>AsString()
public string GetD81TaperDateYyAsString()
{
    return _D81TaperDateYy.PadRight(0);
}

// Set<>AsString()
public void SetD81TaperDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81TaperDateYy = value;
}

// Standard Getter
public string GetD81TaperDateMm()
{
    return _D81TaperDateMm;
}

// Standard Setter
public void SetD81TaperDateMm(string value)
{
    _D81TaperDateMm = value;
}

// Get<>AsString()
public string GetD81TaperDateMmAsString()
{
    return _D81TaperDateMm.PadRight(0);
}

// Set<>AsString()
public void SetD81TaperDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81TaperDateMm = value;
}

// Standard Getter
public string GetD81TaperDateDd()
{
    return _D81TaperDateDd;
}

// Standard Setter
public void SetD81TaperDateDd(string value)
{
    _D81TaperDateDd = value;
}

// Get<>AsString()
public string GetD81TaperDateDdAsString()
{
    return _D81TaperDateDd.PadRight(0);
}

// Set<>AsString()
public void SetD81TaperDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D81TaperDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}

}}