using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt179HsDTO
{// <Section> Class for Fvar
public class Fvar
{
public Fvar() {}

// Fields in the class


// [DEBUG] Field: D77Record, is_external=, is_static_class=False, static_prefix=
private D77Record _D77Record = new D77Record();




// Getter and Setter methods

// Standard Getter
public D77Record GetD77Record()
{
    return _D77Record;
}

// Standard Setter
public void SetD77Record(D77Record value)
{
    _D77Record = value;
}

// Get<>AsString()
public string GetD77RecordAsString()
{
    return _D77Record != null ? _D77Record.GetD77RecordAsString() : "";
}

// Set<>AsString()
public void SetD77RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D77Record == null)
    {
        _D77Record = new D77Record();
    }
    _D77Record.SetD77RecordAsString(value);
}


}}