using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D75File Data Structure

public class D75File
{
    private static int _size = 12;
    // [DEBUG] Class: D75File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler235, is_external=, is_static_class=False, static_prefix=
    private string _Filler235 ="$";
    
    
    
    
    // [DEBUG] Field: D75UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D75UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler236, is_external=, is_static_class=False, static_prefix=
    private string _Filler236 ="RF";
    
    
    
    
    // [DEBUG] Field: D75ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D75ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler237, is_external=, is_static_class=False, static_prefix=
    private string _Filler237 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD75FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler235.PadRight(1));
        result.Append(_D75UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler236.PadRight(2));
        result.Append(_D75ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler237.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD75FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller235(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD75UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller236(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD75ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller237(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD75FileAsString();
    }
    // Set<>String Override function
    public void SetD75File(string value)
    {
        SetD75FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller235()
    {
        return _Filler235;
    }
    
    // Standard Setter
    public void SetFiller235(string value)
    {
        _Filler235 = value;
    }
    
    // Get<>AsString()
    public string GetFiller235AsString()
    {
        return _Filler235.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller235AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler235 = value;
    }
    
    // Standard Getter
    public int GetD75UserNo()
    {
        return _D75UserNo;
    }
    
    // Standard Setter
    public void SetD75UserNo(int value)
    {
        _D75UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD75UserNoAsString()
    {
        return _D75UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD75UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D75UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller236()
    {
        return _Filler236;
    }
    
    // Standard Setter
    public void SetFiller236(string value)
    {
        _Filler236 = value;
    }
    
    // Get<>AsString()
    public string GetFiller236AsString()
    {
        return _Filler236.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller236AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler236 = value;
    }
    
    // Standard Getter
    public int GetD75ReportNo()
    {
        return _D75ReportNo;
    }
    
    // Standard Setter
    public void SetD75ReportNo(int value)
    {
        _D75ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD75ReportNoAsString()
    {
        return _D75ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD75ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D75ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller237()
    {
        return _Filler237;
    }
    
    // Standard Setter
    public void SetFiller237(string value)
    {
        _Filler237 = value;
    }
    
    // Get<>AsString()
    public string GetFiller237AsString()
    {
        return _Filler237.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller237AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler237 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}