using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing OffshoreRecord Data Structure

public class OffshoreRecord
{
    private static int _size = 165;
    // [DEBUG] Class: OffshoreRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler1, is_external=, is_static_class=False, static_prefix=
    private string _Filler1 ="";
    
    
    
    
    // [DEBUG] Field: OffshRec, is_external=, is_static_class=False, static_prefix=
    private string _OffshRec ="";
    
    
    
    
    
    // Serialization methods
    public string GetOffshoreRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler1.PadRight(1));
        result.Append(_OffshRec.PadRight(164));
        
        return result.ToString();
    }
    
    public void SetOffshoreRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller1(extracted);
        }
        offset += 1;
        if (offset + 164 <= data.Length)
        {
            string extracted = data.Substring(offset, 164).Trim();
            SetOffshRec(extracted);
        }
        offset += 164;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetOffshoreRecordAsString();
    }
    // Set<>String Override function
    public void SetOffshoreRecord(string value)
    {
        SetOffshoreRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller1()
    {
        return _Filler1;
    }
    
    // Standard Setter
    public void SetFiller1(string value)
    {
        _Filler1 = value;
    }
    
    // Get<>AsString()
    public string GetFiller1AsString()
    {
        return _Filler1.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler1 = value;
    }
    
    // Standard Getter
    public string GetOffshRec()
    {
        return _OffshRec;
    }
    
    // Standard Setter
    public void SetOffshRec(string value)
    {
        _OffshRec = value;
    }
    
    // Get<>AsString()
    public string GetOffshRecAsString()
    {
        return _OffshRec.PadRight(164);
    }
    
    // Set<>AsString()
    public void SetOffshRecAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _OffshRec = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}