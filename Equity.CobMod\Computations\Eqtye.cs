using System;
using System.Text;
using EquityProject.CgtabortPGM;
using EquityProject.EqtlogPGM;
using EquityProject.EqtpathPGM;
using EquityProject.EqtyeDTO;
using EquityProject.EquityGlobalParmsDTO;
using Legacy4.Equity.CobMod.FIleHandlers;

namespace EquityProject.EqtyePGM
{
    // Eqtye Class Definition

    //Eqtye Class Constructor
    public class Eqtye
    {
        // Declare Eqtye Class private variables
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();
        private EquityGlobalParms equityGlobalParms;

        // Declare {program_name} Class getters setters
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        /// <summary>
        /// Helper method to call external subroutines
        /// </summary>
        /// <param name="ivar">The Ivar parameter to pass to the subroutine</param>
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
        }


        // Run() method
        public void Run(Gvar gvar, Ivar ivar)
        {
            // Call the main entry point
            // No mainline procedure found, add your entry point here
        }

        // Methods representing paragraphs under procedure division
        /// <summary>
        /// COBOL Paragraph Name: AMAIN
        /// </summary>
        /// <remarks>
        /// This method executes the A-START and C-PROCESS paragraphs sequentially.
        /// The original COBOL paragraph starts the tasks.
        /// Following the execution there is an exit statement to terminate the program.
        /// This paragraph initiates the COBOL program and processes the necessary data.
        /// </remarks>
        /// <param name="gvar">Gvar class used to access Gvar variables</param>
        /// <param name="ivar">Ivar class used to access Ivar variables</param>
        public void AMain(Gvar gvar, Ivar ivar)
        {
            // Call the BStart method to perform the equivalent logic of the B-START paragraph
            BStart(gvar, ivar);

            // Call the CProcess method to perform the equivalent logic of the C-PROCESS paragraph
            CProcess(gvar, ivar);

            // In COBOL: EXIT PROGRAM, STOP RUN - These are handled by the environment or calling logic in C#
        }
        /// <summary>
        /// COBOL paragraph: bStart
        /// Moves program name, log action value and empty spaces tag to the log action field respectively and calls the CGTLOG program.
        /// </summary>
        /// <param name="gvar">Global variable class containing all global variables</param>
        /// <param name="ivar">Instance variable class containing all local variables and parameters</param>
        /// <remarks>
        /// The original COBOL code performs the following operations:
        /// - Moves W-PROGRAM-NAME to L-LOG-PROGRAM
        /// - Moves CLEAR-RUN-LOG to L-LOG-ACTION
        /// - Moves SPACES to L-LOG-FILE-NAME
        /// - EOPerforms paragraph X-CALL-CGTLOG
        /// This C# method replicates the same logic using C# conventions and best practices.
        /// </remarks>
        public void BStart(Gvar gvar, Ivar ivar)
        {
            // Move W-PROGRAM-NAME to L-LOG-PROGRAM
            // Get the current CgtlogLinkageArea1 object or create a new one if it doesn't exist
            var currentCgtlogLinkageArea1 = gvar.GetCgtlogLinkageArea1();

            // Create a new CgtlogLinkageArea1 object with the updated L-LOG-PROGRAM value
            var updatedCgtlogLinkageArea1 = new CgtlogLinkageArea1();
            updatedCgtlogLinkageArea1.SetLLogProgram(gvar.GetWProgramName());

            // Set the updated L-LOG-ACTION value (this should be the original action, keep unchanged)
            updatedCgtlogLinkageArea1.SetLLogAction(currentCgtlogLinkageArea1.GetLLogAction());

            // Set the updated L-LOG-FILE-NAME value (this should be the original file name, keep unchanged)
            updatedCgtlogLinkageArea1.SetLLogFileName(currentCgtlogLinkageArea1.GetLLogFileName());

            // Update the gvar with the new CgtlogLinkageArea1 object
            gvar.SetCgtlogLinkageArea1(updatedCgtlogLinkageArea1);

            // Move CLEAR-RUN-LOG to L-LOG-ACTION
            gvar.SetCgtlogLinkageArea1(updatedCgtlogLinkageArea1);

            // Move SPACES to L-LOG-FILE-NAME
            gvar.SetCgtlogLinkageArea1(updatedCgtlogLinkageArea1);

            // Perform X-CALL-CGTLOG
            XCallCgtlog(gvar, ivar);
        }
        /// <summary>
        /// COBOL paragraph name: cProcess
        /// </summary>
        /// <param name="gvar">Collection of global variables.</param>
        /// <param name="ivar">Collection of input variables.</param>
        /// <remarks>
        /// This method translates the specified COBOL paragraph to C#.
        /// </remarks>
        public void CProcess(Gvar gvar, Ivar ivar)
        {            
            C1SetupParms(gvar, ivar);
            XDeleteOldCalcFiles(gvar, ivar);
            // Create a new instance of the external program
            //must uncomment
            //ELCGTK40 elcgtk40 = new ELCGTK40();
            /* Call the Run method with the parameters specified in the USING clause
            elcgtk40.Run(gvar.GetParmInfo(), gvar.GetLkCgtMasterRecord());
            gvar.SetCancel(gvar.GetElcgtk40());
            gvar.SetCancel(gvar.GetElcgtkct());
            gvar.SetCancel(gvar.GetCgtlogLinkageArea1().GetCgtlog());*/

            // Translate EVALUATE statement to C# using if-else
            // Note: This makes a lot of assumptions about the RETURN-CODE value which will need to be adjusted.
            if (gvar.GetReturnCode() >= 9) //RETURN-CODE > 8
            {
                // Set RETURN-CODE to SEE-RUN-LOG
                gvar.SetReturnCode(Ivar.SEE_RUN_LOG);
                //gvar.SetCExit(Ivar.SEE_RUN_LOG);
            }
            else if (gvar.GetReturnCode() != 0) //RETURN-CODE <> 0
            {
                // Set RETURN-CODE to SEE-OUTPUT-REPORTS
                gvar.SetReturnCode(Ivar.SEE_OUTPUT_REPORTS);
                //gvar.SetCExit(Ivar.SEE_OUTPUT_REPORTS);
            }
            C2CallSegregation89(gvar, ivar);
            // if RETURN-CODE is greater than 0, set RETURN-CODE to SEE-RUN-LOG and go to C-EXIT
            if (gvar.GetReturnCode() > 0)
            {
                gvar.SetReturnCode(Ivar.SEE_RUN_LOG);
                //gvar.SetCExit(Ivar.SEE_RUN_LOG);
            }

            C3BalanceFor2013(gvar, ivar);
            if (gvar.GetReturnCode() > 0)
            {
                // Set RETURN-CODE to SEE-RUN-LOG
                gvar.SetReturnCode(Ivar.SEE_RUN_LOG);
                //gvar.SetCExit(Ivar.SEE_RUN_LOG);
            }

            // Create a new instance of the external program
            //must uncomment
            /*CGTBALUP cgtbalup = new CGTBALUP();
            // Call the Run method with the parameters specified in the USING clause
            cgtbalup.Run(gvar.GetCommonLinkage(), gvar.GetWProgramName(), gvar.GetLUpdateFlag(), ivar.GetEquityParameters().GetEquityGenerationNo());

            gvar.SetCancel(gvar.GetCgtbalup());
            gvar.SetCancel(gvar.GetCgtlogLinkageArea1().GetCgtlog(), 1); //NOT 1 - LK-CGT-LOGFLE-RECORD*/
        }
        /// <summary>
        /// COBOL paragraph: C1SetupParms
        /// This method sets up various parameters and performs necessary operations
        /// to read and process a file. It handles file actions such as open, read, and close,
        /// and evaluates specific conditions to set parameters accordingly.
        /// </summary>
        /// <param name="gvar">Global variables accessor</param>
        /// <param name="ivar">Input variables accessor</param>
        /// <remarks>
        /// This method performs the following steps:
        /// 1. Sets the file action to open input and the file name to the parameter file.
        /// 2. Calls the XCallCgtfiles method to perform the file action.
        /// 3. If the operation is not successful, calls the XCallCgtabort method.
        /// 4. Sets the file action to read next and calls the XCallCgtfiles method.
        /// 5. If the operation is not successful, calls the XCallCgtabort method.
        /// 6. If successful, moves the file record area to the D8-RECORD.
        /// 7. Sets the file action to close file and calls the XCallCgtfiles method.
        /// 8. Sets various parameters based on the conditions evaluated.
        /// </remarks>
        public void C1SetupParms(Gvar gvar, Ivar ivar)
        {
            // Set the file action to open input
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.OPEN_INPUT);

            // Set the file name to the parameter file
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.PARAMETER_FILE);

            // Perform the file action
            XCallCgtfiles(gvar, ivar);

            // Check if the operation was successful
            if (!gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                // If not successful, call the abort method
                XCallCgtabort(gvar, ivar);
            }

            // Set the file action to read next
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_NEXT);

            // Perform the file action
            XCallCgtfiles(gvar, ivar);

            // Check if the operation was successful
            if (!gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                // If not successful, call the abort method
                XCallCgtabort(gvar, ivar);
            }
            else
            {
                // If successful, move the file record area to the D8-RECORD
                gvar.SetD8RecordAsString(gvar.GetLFileRecordAreaAsString());
            }

            // Set the file action to close file
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.CLOSE_FILE);

            // Perform the file action
            XCallCgtfiles(gvar, ivar);

            // Set various parameters to spaces
            gvar.GetParmInfo().SetParmCharsAsString(" ");

            // Evaluate the D8-USE-BV-FOR-PROFIT condition
            if (gvar.GetD8Record().GetD8UseBvForProfit() == "N")
            {
                gvar.GetParmInfo().GetParmChars().SetParmChar1("N");
            }
            else
            {
                gvar.GetParmInfo().GetParmChars().GetParmMsg().SetParmChar2(" ");
            }

            // Evaluate the D8-YE-DELETIONS condition
            if (gvar.GetD8Record().GetD8YeDeletions() == "N")
            {
                gvar.GetParmInfo().GetParmChars().GetParmMsg().SetParmChar3("N");
            }
            else
            {
                gvar.GetParmInfo().GetParmChars().GetParmMsg().SetParmChar3(" ");
            }

            // Set other parameters
            gvar.GetParmInfo().GetParmChars().SetParmChar1("N");
            gvar.GetParmInfo().GetParmChars().GetParmMsg().SetParmChar5("R");
            gvar.GetParmInfo().GetParmChars().GetParmMsg().SetParmIoMethod(Gvar.MICRO_IO);
            gvar.GetParmInfo().GetParmChars().GetParmMsg().SetParmUserNoPrefix("$");
            gvar.GetParmInfo().SetParmLength(17);
            gvar.GetParmInfo().GetParmChars().GetParmMsg().SetParmUserNo(ivar.GetEquityParameters().GetEquityGeneralParms().GetEquityUserNo());
            gvar.GetParmInfo().GetParmChars().GetParmMsg().SetParmGenerationNo(ivar.GetEquityParameters().GetEquityBatchParameters().GetEquityGenerationNo());
            gvar.GetParmInfo().GetParmChars().GetParmMsg().SetParmYear(ivar.GetEquityParameters().GetEquityGeneralParms().GetEquityYear());

            // Set CGTSPLIT parameters
            gvar.GetCgtsplitLinkage().GetCgtsplitChars().SetCgtsplitIoMethod(Gvar.MICRO_IO);
            gvar.GetCgtsplitLinkage().SetCgtsplitLength(1);
            gvar.GetCgtsplitLinkage().SetCgtsplitUserNo(ivar.GetEquityParameters().GetEquityGeneralParms().GetEquityUserNo());

            // Set the master file year
            gvar.GetCommonLinkage().SetLMasterFileYear(int.Parse(ivar.GetEquityParameters().GetEquityGeneralParms().GetEquityYear()));

            // Check if all funds are selected
            if (ivar.GetEquityParameters().GetEquityBatchParameters().IsAllFunds())
            {
                gvar.SetLUpdateFlag("Y");
            }
            else
            {
                gvar.SetLUpdateFlag("N");
            }
        }
        /// <summary>
        /// Implements the logic from the COBOL paragraph c2CallSegregation89.
        /// </summary>
        /// <param name="gvar">Global variables container.</param>
        /// <param name="ivar">Input variables container.</param>
        /// <remarks>
        /// This method checks if SEGREGATE-ASSETS is true and EQUITY-YEAR is '89'.
        /// If both conditions are true, it logs specific messages using RunLogDAL
        /// and sets the RETURN-CODE to 12.
        /// </remarks>
        public void C2CallSegregation89(Gvar gvar, Ivar ivar)
        {
            // Check if SEGREGATE-ASSETS is true and EQUITY-YEAR is '89'
            if (ivar.GetEquityParameters().GetEquityBatchParameters().IsSegregateAssets()
                && ivar.GetEquityParameters().GetEquityGeneralParms().GetEquityYear() == "89")
            {
                // Move WRITE-RECORD to W-LOG-ACTION
                gvar.GetWLog().SetWLogAction(Gvar.WRITE_RECORD);
                // Move '----  Atention  ----' to W-LOG-STRING               
                gvar.GetWLog().SetWLogString("----  Atention  ----");

                // Create a new instance of RunLogDAL and call its Run method
                RunLogDAL runLogDal = new RunLogDAL();
                LogMessage logMessage = new LogMessage(gvar.GetWLogAsString());

                runLogDal.ProcessLogRequest(logMessage);

                // String concatenation for the log message
                var separator1 = "----  Atention  ----";
                var segregatedscreen = "SEGREGATION OF ASSETS PROGRAM FOR YEAR 19";
                var year = ivar.GetEquityParameters().GetEquityGeneralParms().GetEquityYear();
                var notRunning = "IS NOT RUNNING";
                string combinedString = $"{segregatedscreen} {year} {notRunning}";

                // Move the combined string to W-LOG-STRING
                gvar.GetWLog().SetWLogString(combinedString);
                logMessage = new LogMessage(gvar.GetWLogAsString());

                // Call RunLogDAL again with the updated W-LOG-STRING
                runLogDal.ProcessLogRequest(logMessage);

                // Move '---- PLEASE CONTACT C.C.H ---- ' to W-LOG-STRING
                var pleaseContact = $"PLEASE CONTACT C.C.H {Gvar.WRITE_RECORD}";
                gvar.GetWLog().SetWLogString(pleaseContact);
                logMessage = new LogMessage(gvar.GetWLogAsString());

                // Call RunLogDAL with the new W-LOG-STRING
                runLogDal.ProcessLogRequest(logMessage);

                // Set RETURN-CODE to 12
                gvar.SetReturnCode(12);
            }
        }
        /// <summary>
        /// COBOL Paragraph: C3BalanceFor2012
        /// </summary>
        /// <param name="gvar">Global variables structure</param>
        /// <param name="ivar">Input variables structure</param>
        /// <remarks>
        ///    This method checks if the equity year is '12'
        ///      If true, then it calls the external program "CgtSplit2022" using
        ///     the dataset CgtSplitLinkage. Finally, it cancels both "CgtSplit2022"
        ///    and "CgtLog" programs.
        ///
        /// </remarks>
        public void C3BalanceFor2013(Gvar gvar, Ivar ivar)
        {
            // Get the equity year from the input variables structure
            string equityYear = ivar.GetEquityParameters().GetEquityGeneralParms().GetEquityYear();

            // Check if the equity year equals '12'
            if (equityYear == "12")
            {
                //must uncomment
                /*
                // Create an instance of the external program "CgtSplit2022"
                CgtSplit2022 cgtSplit2022 = new CgtSplit2022();

                // Get the CgtSplitLinkage dataset from the global variables structure
                CgtSplitLinkage cgtSplitLinkage = gvar.GetCgtsplitLinkage();

                // Call the Run method of the external program with the CgtSplitLinkage dataset
                cgtSplit2022.Run(cgtSplitLinkage);

                // Set the CgtSplit2022 variable in the global variables structure to be canceled
                gvar.SetCgtsplit2012("");

                // Set the CgtLog variable in the global variables structure to be canceled
                gvar.SetCgtlogLinkageArea1AsString(" ");*/
            }
        }
        /// <summary>
        /// This method performs the COBOL paragraph X-DELETE-OLD-CALC-FILES.
        /// This segment handles file name constructions and deletion operations
        /// using specified suffixes and prefixes corresponding to different functionalities.
        ///
        /// </summary>
        /// <param name="gvar">A parameter of type Gvar that contains global variables.</param>
        /// <param name="ivar">A parameter of type Ivar that contains input variables.</param>
        /// <remarks>
        /// This method equivalents the logic and flow of the COBOL paragraph as follows:'
        /// - Move USER-DATA-PATH to EQTPATH-PATH-ENV-VARIABLE
        /// - Construct and concatenate file names using equity-user-no, equity-generation-no, and fixed suffixes (R7, R8, etc.)
        /// - Construct and concatenate file names for MYE.DAT and MYE.IDX
        /// - Perform the deletion of the constructed files.
        ///
        /// For the STRING and STRING ... DELIMITED BY SIZE statements, equivalent C# concatenation
        /// function String.Concat is used to mimic the effect.
        ///
        /// The PERFORM statements are mapped directly to method calls to simulate the COBOL functionality.
        /// </remarks>
        public void XDeleteOldCalcFiles(Gvar gvar, Ivar ivar)
        {
            // Move USER-DATA-PATH to EQTPATH-PATH-ENV-VARIABLE
            gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);

            // Process file name construction and deletion for different file types
            ConstructAndDeleteFile(gvar, ivar, "R7", ".REP");
            ConstructAndDeleteFile(gvar, ivar, "R8", ".REP");
            ConstructAndDeleteFile(gvar, ivar, "R9", ".REP");
            ConstructAndDeleteFile(gvar, ivar, "RA", ".REP");
            ConstructAndDeleteFile(gvar, ivar, "DB", ".DAT");
            ConstructAndDeleteFile(gvar, ivar, "BU", ".REP");
            ConstructAndDeleteFile(gvar, ivar, string.Empty, "MYE.DAT");
            ConstructAndDeleteFile(gvar, ivar, string.Empty, "MYE.IDX");
        }

        ///This helper method is necessary to process the repetitive logic of string construction and file deletion.
        ///It is called multiple times with different parameters to simulate the COBOL PERFORM logic.


        ///The XSetNameAndDeleteFile method is called after constructing the file name.
        ///Since no implementation is provided, it is expected to be defined elsewhere.
        /// <summary>
        /// Represents the xSetNameAndDeleteFile paragraph from COBOL.
        /// This method performs the following steps:
        /// 1. Moves the value from L-DEL-FILE to EQTPATH-FILE-NAME.
        /// 2. Calls the XCallEqtpath method.
        /// 3. Moves the value from EQTPATH-PATH-FILE-NAME to L-DEL-FILE.
        /// </summary>
        /// <param name="gvar">Instance of Gvar containing the program's global variables.</param>
        /// <param name="ivar">Instance of Ivar containing the program's working storage variables.</param>
        /// <remarks>
        /// This method is converted from the COBOL `xSetNameAndDeleteFile` paragraph.
        /// The COBOL MOVE statements are translated to setter/getter method calls in C#.
        /// The COBOL PERFORM statement is translated into a method call to XCallEqtpath.
        /// COBOL: MOVE L-DEL-FILE TO EQTPATH-FILE-NAME
        /// COBOL: PERFORM X-CALL-EQTPATH
        /// COBOL: MOVE EQTPATH-PATH-FILE-NAME TO L-DEL-FILE
        /// </remarks>
        public void XSetNameAndDeleteFile(Gvar gvar, Ivar ivar)
        {
            // Move the value from L-DEL-FILE to EQTPATH-FILE-NAME
            var lDelFileValue = gvar.GetCgtdelLinkage().GetLDelFile();
            gvar.GetEqtpathLinkage().SetEqtpathFileName(lDelFileValue);
            gvar.SetEqtpathLinkage(gvar.GetEqtpathLinkage());

            // Call the XCallEqtpath method
            XCallEqtpath(gvar, ivar);

            // Move the value from EQTPATH-PATH-FILE-NAME to L-DEL-FILE
            var eqtpathPathFileNameValue = gvar.GetEqtpathLinkage().GetEqtpathPathFileName();
            gvar.GetCgtdelLinkage().SetLDelFile(eqtpathPathFileNameValue);
            gvar.SetCgtdelLinkage(gvar.GetCgtdelLinkage());
        }

        public void XCallCgtfiles(Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the external program CGTFILES
            //must uncomment
            /*Cgtfiles cgtfiles = new Cgtfiles();
            // Call the Run method with the parameters specified in the USING clause
            // Getthe parameters
            CgtfilesLinkage cgtfilesLinkage = gvar.GetCgtfilesLinkage();
            CommonLinkage commonLinkage = gvar.GetCommonLinkage();
            LFileRecordArea lFileRecordArea = gvar.GetLFileRecordArea();
            // Call the CGTFILES program
            cgtfiles.Run(cgtfilesLinkage, lFileRecordArea, commonLinkage);*/
        }
        /// <summary>
        /// Calls an external program to process payment source details.
        /// </summary>
        /// <param name="gvar">Global variables</param>
        /// <param name="ivar">Input variables</param>
        /// <remarks>
        /// <para>COBOL Paragraph: xCallEqtpath</para>
        /// <para>
        ///     Implementation of the COBOL CALL "EQTPATH" USING EQTPATH-LINKAGE.
        ///     Converts the COBOL call statement to a C# method that invokes the external program with the necessary parameters.
        /// </para>
        /// </remarks>
        public void XCallEqtpath(Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the EQTPATH external program
            Eqtpath eqtpath = new Eqtpath();
            eqtpath.GetIvar().SetEqtpathLinkageAsString(gvar.GetEqtpathLinkageAsString());
            // Call the Run method of EQTPATH with the EQTPATH-LINKAGE parameter
            eqtpath.Run(eqtpath.GetGvar(), eqtpath.GetIvar(), equityGlobalParms);
        }
        /// <summary>
        /// Conversion of COBOL paragraph xCallCgtlog which handles calling the external program.
        /// </summary>
        /// <param name="gvar">Global variables</param>
        /// <param name="ivar">Input variables</param>
        /// <remarks>
        ///  This method is a conversion of COBOL X-callCgtlog paragraph.
        ///  It handles the dynamic conversion of external program.
        ///  The USING clause indicates the parameters passed to the external program.
        /// </remarks>
        public void XCallCgtlog(Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the CGTLOG program
            Eqtlog cgtLog = new Eqtlog();
            cgtLog.GetIvar().SetLinkageArea1AsString(gvar.GetCgtlogLinkageArea1AsString());
            cgtLog.GetIvar().SetLinkageArea2AsString(gvar.GetCgtlogLinkageArea2AsString());
            // Call the Run method of the CGTLOG program with the input parameter.
            cgtLog.Run(cgtLog.GetGvar(), cgtLog.GetIvar());
        }
        /// <summary>
        /// COBOL paragraph name: xCallCgtabort
        /// </summary>
        /// <param name="gvar">
        /// global variables accessible within this method
        /// </param>
        /// <param name="ivar">
        /// local variables accessible within this method
        /// </param>
        /// <remarks>
        /// <para>Method equivalent to COBOL paragraph "xCallCgtabort".</para>
        /// <para>Implements the COBOL CALL statement with appropriate logic.</para>
        /// </remarks>
        public void XCallCgtabort(Gvar gvar, Ivar ivar)
        {
            // Equivalent to: CALL "CGTABORT" USING COMMON-LINKAGE

            // Create a new instance of the CGTabort class
            Cgtabort cgtabort = new Cgtabort();
            cgtabort.GetIvar().SetCgtabortLinkageAsString(gvar.GetCgtabortLinkageAsString());
            cgtabort.GetIvar().SetCommonLinkageAsString(gvar.GetCommonLinkageAsString());
            // Call the Run method with the appropriate parameter
            cgtabort.Run(cgtabort.GetGvar(), cgtabort.GetIvar());

        }

        // Helper methods

        private void ConstructAndDeleteFile(Gvar gvar, Ivar ivar, string suffix, string extension)
        {
            // Construct the file name using string concatenation
            var fileName = string.Concat('$', ivar.GetEquityParameters().GetEquityGeneralParms().GetEquityUserNo(),
                                          suffix, ivar.GetEquityParameters().GetEquityBatchParameters().GetEquityGenerationNo(), extension);
            gvar.GetCgtdelLinkage().SetLDelFile(fileName);

            // Perform the deletion of the file
            XSetNameAndDeleteFile(gvar, ivar);
        }

    }
}
