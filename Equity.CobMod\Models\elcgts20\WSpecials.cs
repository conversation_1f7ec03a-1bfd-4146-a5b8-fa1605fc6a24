using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WSpecials Data Structure

public class WSpecials
{
    private static int _size = 873;
    // [DEBUG] Class: WSpecials, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler337, is_external=, is_static_class=False, static_prefix=
    private string _Filler337 ="WORKING-STORAGE*";
    
    
    
    
    // [DEBUG] Field: WProgname, is_external=, is_static_class=False, static_prefix=
    private string _WProgname ="ELCGTS20";
    
    
    
    
    // [DEBUG] Field: WOslTotal, is_external=, is_static_class=False, static_prefix=
    private decimal _WOslTotal =0;
    
    
    
    
    // [DEBUG] Field: WAppCall, is_external=, is_static_class=False, static_prefix=
    private decimal _WAppCall =0;
    
    
    
    
    // [DEBUG] Field: WPrevAppCalls, is_external=, is_static_class=False, static_prefix=
    private decimal _WPrevAppCalls =0;
    
    
    
    
    // [DEBUG] Field: WOslOccCount, is_external=, is_static_class=False, static_prefix=
    private int _WOslOccCount =0;
    
    
    
    
    // [DEBUG] Field: WLoopCount, is_external=, is_static_class=False, static_prefix=
    private int _WLoopCount =0;
    
    
    
    
    // [DEBUG] Field: WsStoreDsProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _WsStoreDsProceeds =0;
    
    
    
    
    // [DEBUG] Field: WsStoreDpCost, is_external=, is_static_class=False, static_prefix=
    private decimal _WsStoreDpCost =0;
    
    
    
    
    // [DEBUG] Field: WsStoreWiTdIndex, is_external=, is_static_class=False, static_prefix=
    private string _WsStoreWiTdIndex ="";
    
    
    
    
    // [DEBUG] Field: WsStoreWiS, is_external=, is_static_class=False, static_prefix=
    private string _WsStoreWiS ="";
    
    
    
    
    // [DEBUG] Field: WsAccumulationValue, is_external=, is_static_class=False, static_prefix=
    private decimal _WsAccumulationValue =0;
    
    
    
    
    // [DEBUG] Field: WsRaBaSearchCategory, is_external=, is_static_class=False, static_prefix=
    private string _WsRaBaSearchCategory ="";
    
    
    
    
    // [DEBUG] Field: WsRaBaTransactionValue, is_external=, is_static_class=False, static_prefix=
    private decimal _WsRaBaTransactionValue =0;
    
    
    
    
    // [DEBUG] Field: WsRaBaSameDayValue, is_external=, is_static_class=False, static_prefix=
    private decimal _WsRaBaSameDayValue =0;
    
    
    
    
    // [DEBUG] Field: WsLastRaBaCalSedol, is_external=, is_static_class=False, static_prefix=
    private string _WsLastRaBaCalSedol ="";
    
    
    
    
    // [DEBUG] Field: WsProcessingNs, is_external=, is_static_class=False, static_prefix=
    private string _WsProcessingNs ="";
    
    
    // 88-level condition checks for WsProcessingNs
    public bool IsProcessingNs()
    {
        if (this._WsProcessingNs == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsSelectivePerform, is_external=, is_static_class=False, static_prefix=
    private string _WsSelectivePerform ="";
    
    
    
    
    // [DEBUG] Field: WsProcessingDisposal, is_external=, is_static_class=False, static_prefix=
    private string _WsProcessingDisposal ="";
    
    
    // 88-level condition checks for WsProcessingDisposal
    public bool IsProcessingDisposal()
    {
        if (this._WsProcessingDisposal == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsDeemedDisposalFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsDeemedDisposalFlag ="";
    
    
    // 88-level condition checks for WsDeemedDisposalFlag
    public bool IsProcessingDeemedDisposal()
    {
        if (this._WsDeemedDisposalFlag == "'Y'") return true;
        return false;
    }
    public bool IsNotProcessingDeemedDisposal()
    {
        if (this._WsDeemedDisposalFlag == "'N'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsStoredGsDate, is_external=, is_static_class=False, static_prefix=
    private string _WsStoredGsDate ="";
    
    
    
    
    // [DEBUG] Field: WsNumField, is_external=, is_static_class=False, static_prefix=
    private int _WsNumField =0;
    
    
    
    
    // [DEBUG] Field: WsCostDisplay, is_external=, is_static_class=False, static_prefix=
    private string _WsCostDisplay ="";
    
    
    
    
    // [DEBUG] Field: WsGtSub, is_external=, is_static_class=False, static_prefix=
    private int _WsGtSub =0;
    
    
    
    
    // [DEBUG] Field: WsDisplayNumber, is_external=, is_static_class=False, static_prefix=
    private decimal _WsDisplayNumber =0;
    
    
    
    
    // [DEBUG] Field: WsDisp1, is_external=, is_static_class=False, static_prefix=
    private decimal _WsDisp1 =0;
    
    
    
    
    // [DEBUG] Field: WsDisp2, is_external=, is_static_class=False, static_prefix=
    private decimal _WsDisp2 =0;
    
    
    
    
    // [DEBUG] Field: WsZzzDisplayCount, is_external=, is_static_class=False, static_prefix=
    private int _WsZzzDisplayCount =0;
    
    
    
    
    // [DEBUG] Field: WsZzzCostCount, is_external=, is_static_class=False, static_prefix=
    private int _WsZzzCostCount =0;
    
    
    
    
    // [DEBUG] Field: WWhenCompiled, is_external=, is_static_class=False, static_prefix=
    private WWhenCompiled _WWhenCompiled = new WWhenCompiled();
    
    
    
    
    // [DEBUG] Field: WReportName, is_external=, is_static_class=False, static_prefix=
    private string _WReportName ="       ";
    
    
    
    
    // [DEBUG] Field: WParKeyAccessible, is_external=, is_static_class=False, static_prefix=
    private string _WParKeyAccessible ="N";
    
    
    
    
    // [DEBUG] Field: StatusKey, is_external=, is_static_class=False, static_prefix=
    private StatusKey _StatusKey = new StatusKey();
    
    
    
    
    // [DEBUG] Field: WsLongDate, is_external=, is_static_class=False, static_prefix=
    private WsLongDate _WsLongDate = new WsLongDate();
    
    
    
    
    // [DEBUG] Field: WsResult, is_external=, is_static_class=False, static_prefix=
    private int _WsResult =0;
    
    
    
    
    // [DEBUG] Field: WsRemainder, is_external=, is_static_class=False, static_prefix=
    private int _WsRemainder =0;
    
    
    
    
    // [DEBUG] Field: WsBondDeemedDisposal, is_external=, is_static_class=False, static_prefix=
    private string _WsBondDeemedDisposal ="";
    
    
    
    
    // [DEBUG] Field: WMarketPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _WMarketPrice =0;
    
    
    
    
    // [DEBUG] Field: WsBondGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WsBondGainLoss =0;
    
    
    
    
    // [DEBUG] Field: WsBondProcessFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsBondProcessFlag ="B";
    
    
    
    
    // [DEBUG] Field: WsBondCost, is_external=, is_static_class=False, static_prefix=
    private decimal _WsBondCost =0;
    
    
    
    
    // [DEBUG] Field: WsMv, is_external=, is_static_class=False, static_prefix=
    private decimal _WsMv =0;
    
    
    
    
    // [DEBUG] Field: WsRvIndexedCost, is_external=, is_static_class=False, static_prefix=
    private decimal _WsRvIndexedCost =0;
    
    
    
    
    // [DEBUG] Field: WsMonthsToRedemption, is_external=, is_static_class=False, static_prefix=
    private decimal _WsMonthsToRedemption =0;
    
    
    
    
    // [DEBUG] Field: WsYearsToRedemption, is_external=, is_static_class=False, static_prefix=
    private int _WsYearsToRedemption =0;
    
    
    
    
    // [DEBUG] Field: WsSub, is_external=, is_static_class=False, static_prefix=
    private int _WsSub =0;
    
    
    
    
    // [DEBUG] Field: WsWttCatSub, is_external=, is_static_class=False, static_prefix=
    private int _WsWttCatSub =0;
    
    
    
    
    // [DEBUG] Field: WsAccrualProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _WsAccrualProceeds =0;
    
    
    
    
    // [DEBUG] Field: WsTrancheMonths, is_external=, is_static_class=False, static_prefix=
    private decimal _WsTrancheMonths =0;
    
    
    
    
    // [DEBUG] Field: WsAccrualDdProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _WsAccrualDdProceeds =0;
    
    
    
    
    // [DEBUG] Field: WsTotalAccrualDdProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _WsTotalAccrualDdProceeds =0;
    
    
    
    
    // [DEBUG] Field: WsEquityToBondFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsEquityToBondFlag ="";
    
    
    // 88-level condition checks for WsEquityToBondFlag
    public bool IsEquityToBondReorg()
    {
        if (this._WsEquityToBondFlag == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsNonLrToLrBondFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsNonLrToLrBondFlag ="";
    
    
    // 88-level condition checks for WsNonLrToLrBondFlag
    public bool IsNonLrToLrBondReorg()
    {
        if (this._WsNonLrToLrBondFlag == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsCliPendDate9798, is_external=, is_static_class=False, static_prefix=
    private WsCliPendDate9798 _WsCliPendDate9798 = new WsCliPendDate9798();
    
    
    
    
    // [DEBUG] Field: WsCliPstartDate9899, is_external=, is_static_class=False, static_prefix=
    private string _WsCliPstartDate9899 ="980406";
    
    
    
    
    // [DEBUG] Field: WsCli16Mar98Date, is_external=, is_static_class=False, static_prefix=
    private string _WsCli16Mar98Date ="980316";
    
    
    
    
    // [DEBUG] Field: WsBondToBondFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsBondToBondFlag ="";
    
    
    // 88-level condition checks for WsBondToBondFlag
    public bool IsBondToBondReorg()
    {
        if (this._WsBondToBondFlag == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsAcquisitionBondFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsAcquisitionBondFlag ="";
    
    
    // 88-level condition checks for WsAcquisitionBondFlag
    public bool IsAcquisitionBond()
    {
        if (this._WsAcquisitionBondFlag == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsTempWtsIndex, is_external=, is_static_class=False, static_prefix=
    private string _WsTempWtsIndex ="";
    
    
    
    
    // [DEBUG] Field: WsTempWtieIndex, is_external=, is_static_class=False, static_prefix=
    private string _WsTempWtieIndex ="";
    
    
    
    
    // [DEBUG] Field: WsWiGt, is_external=, is_static_class=False, static_prefix=
    private string _WsWiGt ="";
    
    
    
    
    // [DEBUG] Field: WsWiTt, is_external=, is_static_class=False, static_prefix=
    private string _WsWiTt ="";
    
    
    
    
    // [DEBUG] Field: WsWttc2, is_external=, is_static_class=False, static_prefix=
    private string _WsWttc2 ="";
    
    
    
    
    // [DEBUG] Field: WsTaperMatchUnitsLeft, is_external=, is_static_class=False, static_prefix=
    private decimal _WsTaperMatchUnitsLeft =0;
    
    
    
    
    // [DEBUG] Field: WsTotalTaperMatchUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _WsTotalTaperMatchUnits =0;
    
    
    
    
    // [DEBUG] Field: WsMiniTaperMatchUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _WsMiniTaperMatchUnits =0;
    
    
    
    
    // [DEBUG] Field: WsTaperMatchGainLeft, is_external=, is_static_class=False, static_prefix=
    private decimal _WsTaperMatchGainLeft =0;
    
    
    
    
    // [DEBUG] Field: WsTotalTaperMatchGain, is_external=, is_static_class=False, static_prefix=
    private decimal _WsTotalTaperMatchGain =0;
    
    
    
    
    // [DEBUG] Field: WsMiniTaperMatchGain, is_external=, is_static_class=False, static_prefix=
    private decimal _WsMiniTaperMatchGain =0;
    
    
    
    
    // [DEBUG] Field: WsTaperMatchProcLeft, is_external=, is_static_class=False, static_prefix=
    private decimal _WsTaperMatchProcLeft =0;
    
    
    
    
    // [DEBUG] Field: WsTotalTaperMatchProc, is_external=, is_static_class=False, static_prefix=
    private decimal _WsTotalTaperMatchProc =0;
    
    
    
    
    // [DEBUG] Field: WsMiniTaperMatchProc, is_external=, is_static_class=False, static_prefix=
    private decimal _WsMiniTaperMatchProc =0;
    
    
    
    
    // [DEBUG] Field: WsTaperUnitsUsed, is_external=, is_static_class=False, static_prefix=
    private decimal _WsTaperUnitsUsed =0;
    
    
    
    
    // [DEBUG] Field: WsTaperGainUsed, is_external=, is_static_class=False, static_prefix=
    private decimal _WsTaperGainUsed =0;
    
    
    
    
    // [DEBUG] Field: WsTaperProcUsed, is_external=, is_static_class=False, static_prefix=
    private decimal _WsTaperProcUsed =0;
    
    
    
    
    // [DEBUG] Field: WsTotalTaperUnitsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WsTotalTaperUnitsYtd =0;
    
    
    
    
    // [DEBUG] Field: WsMiniTaperUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _WsMiniTaperUnits =0;
    
    
    
    
    // [DEBUG] Field: WsMiniTaperNewUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _WsMiniTaperNewUnits =0;
    
    
    
    
    // [DEBUG] Field: WsSameDayTaperTranches, is_external=, is_static_class=False, static_prefix=
    private int _WsSameDayTaperTranches =0;
    
    
    
    
    // [DEBUG] Field: WsTaperTrancheSub, is_external=, is_static_class=False, static_prefix=
    private int _WsTaperTrancheSub =0;
    
    
    
    
    // [DEBUG] Field: WsIndexationLimitFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsIndexationLimitFlag ="";
    
    
    // 88-level condition checks for WsIndexationLimitFlag
    public bool IsIndexationLimitWritten()
    {
        if (this._WsIndexationLimitFlag == "'Y'") return true;
        return false;
    }
    public bool IsIndexationLimitNotWritten()
    {
        if (this._WsIndexationLimitFlag == "'N'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsReorgDisposalValue, is_external=, is_static_class=False, static_prefix=
    private decimal _WsReorgDisposalValue =0;
    
    
    
    
    // [DEBUG] Field: WSFund, is_external=, is_static_class=False, static_prefix=
    private string _WSFund ="";
    
    
    
    
    // [DEBUG] Field: WSSedol, is_external=, is_static_class=False, static_prefix=
    private string _WSSedol ="";
    
    
    
    
    // [DEBUG] Field: WsContract, is_external=, is_static_class=False, static_prefix=
    private WsContract _WsContract = new WsContract();
    
    
    
    
    // [DEBUG] Field: WsDsContractRef, is_external=, is_static_class=False, static_prefix=
    private WsDsContractRef _WsDsContractRef = new WsDsContractRef();
    
    
    
    
    // [DEBUG] Field: WsRvContractRef, is_external=, is_static_class=False, static_prefix=
    private WsRvContractRef _WsRvContractRef = new WsRvContractRef();
    
    
    
    
    // [DEBUG] Field: WSearchCalSedol, is_external=, is_static_class=False, static_prefix=
    private string _WSearchCalSedol ="";
    
    
    
    
    // [DEBUG] Field: WExerciseProcessingFlag, is_external=, is_static_class=False, static_prefix=
    private string _WExerciseProcessingFlag ="";
    
    
    // 88-level condition checks for WExerciseProcessingFlag
    public bool IsProcessingOption()
    {
        if (this._WExerciseProcessingFlag == "'O'") return true;
        return false;
    }
    public bool IsProcessingStock()
    {
        if (this._WExerciseProcessingFlag == "'S'") return true;
        return false;
    }
    
    
    
    // Serialization methods
    public string GetWSpecialsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler337.PadRight(16));
        result.Append(_WProgname.PadRight(8));
        result.Append(_WOslTotal.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WAppCall.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WPrevAppCalls.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WOslOccCount.ToString().PadLeft(5, '0'));
        result.Append(_WLoopCount.ToString().PadLeft(5, '0'));
        result.Append(_WsStoreDsProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsStoreDpCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsStoreWiTdIndex.PadRight(4));
        result.Append(_WsStoreWiS.PadRight(4));
        result.Append(_WsAccumulationValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsRaBaSearchCategory.PadRight(2));
        result.Append(_WsRaBaTransactionValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsRaBaSameDayValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsLastRaBaCalSedol.PadRight(11));
        result.Append(_WsProcessingNs.PadRight(1));
        result.Append(_WsSelectivePerform.PadRight(1));
        result.Append(_WsProcessingDisposal.PadRight(1));
        result.Append(_WsDeemedDisposalFlag.PadRight(1));
        result.Append(_WsStoredGsDate.PadRight(6));
        result.Append(_WsNumField.ToString().PadLeft(4, '0'));
        result.Append(_WsCostDisplay.PadRight(3));
        result.Append(_WsGtSub.ToString().PadLeft(5, '0'));
        result.Append(_WsDisplayNumber.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsDisp1.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsDisp2.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsZzzDisplayCount.ToString().PadLeft(18, '0'));
        result.Append(_WsZzzCostCount.ToString().PadLeft(18, '0'));
        result.Append(_WWhenCompiled.GetWWhenCompiledAsString());
        result.Append(_WReportName.PadRight(7));
        result.Append(_WParKeyAccessible.PadRight(1));
        result.Append(_StatusKey.GetStatusKeyAsString());
        result.Append(_WsLongDate.GetWsLongDateAsString());
        result.Append(_WsResult.ToString().PadLeft(4, '0'));
        result.Append(_WsRemainder.ToString().PadLeft(4, '0'));
        result.Append(_WsBondDeemedDisposal.PadRight(1));
        result.Append(_WMarketPrice.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsBondGainLoss.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsBondProcessFlag.PadRight(1));
        result.Append(_WsBondCost.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsMv.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsRvIndexedCost.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsMonthsToRedemption.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsYearsToRedemption.ToString().PadLeft(4, '0'));
        result.Append(_WsSub.ToString().PadLeft(4, '0'));
        result.Append(_WsWttCatSub.ToString().PadLeft(4, '0'));
        result.Append(_WsAccrualProceeds.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsTrancheMonths.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsAccrualDdProceeds.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsTotalAccrualDdProceeds.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsEquityToBondFlag.PadRight(1));
        result.Append(_WsNonLrToLrBondFlag.PadRight(1));
        result.Append(_WsCliPendDate9798.GetWsCliPendDate9798AsString());
        result.Append(_WsCliPstartDate9899.PadRight(6));
        result.Append(_WsCli16Mar98Date.PadRight(6));
        result.Append(_WsBondToBondFlag.PadRight(1));
        result.Append(_WsAcquisitionBondFlag.PadRight(1));
        result.Append(_WsTempWtsIndex.PadRight(4));
        result.Append(_WsTempWtieIndex.PadRight(4));
        result.Append(_WsWiGt.PadRight(4));
        result.Append(_WsWiTt.PadRight(4));
        result.Append(_WsWttc2.PadRight(4));
        result.Append(_WsTaperMatchUnitsLeft.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsTotalTaperMatchUnits.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsMiniTaperMatchUnits.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsTaperMatchGainLeft.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsTotalTaperMatchGain.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsMiniTaperMatchGain.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsTaperMatchProcLeft.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsTotalTaperMatchProc.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsMiniTaperMatchProc.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsTaperUnitsUsed.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsTaperGainUsed.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsTaperProcUsed.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsTotalTaperUnitsYtd.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsMiniTaperUnits.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsMiniTaperNewUnits.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsSameDayTaperTranches.ToString().PadLeft(4, '0'));
        result.Append(_WsTaperTrancheSub.ToString().PadLeft(4, '0'));
        result.Append(_WsIndexationLimitFlag.PadRight(0));
        result.Append(_WsReorgDisposalValue.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WSFund.PadRight(0));
        result.Append(_WSSedol.PadRight(0));
        result.Append(_WsContract.GetWsContractAsString());
        result.Append(_WsDsContractRef.GetWsDsContractRefAsString());
        result.Append(_WsRvContractRef.GetWsRvContractRefAsString());
        result.Append(_WSearchCalSedol.PadRight(11));
        result.Append(_WExerciseProcessingFlag.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetWSpecialsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller337(extracted);
        }
        offset += 16;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWProgname(extracted);
        }
        offset += 8;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWOslTotal(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWAppCall(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWPrevAppCalls(parsedDec);
        }
        offset += 17;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWOslOccCount(parsedInt);
        }
        offset += 5;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWLoopCount(parsedInt);
        }
        offset += 5;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsStoreDsProceeds(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsStoreDpCost(parsedDec);
        }
        offset += 15;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWsStoreWiTdIndex(extracted);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWsStoreWiS(extracted);
        }
        offset += 4;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsAccumulationValue(parsedDec);
        }
        offset += 15;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetWsRaBaSearchCategory(extracted);
        }
        offset += 2;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsRaBaTransactionValue(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsRaBaSameDayValue(parsedDec);
        }
        offset += 15;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetWsLastRaBaCalSedol(extracted);
        }
        offset += 11;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsProcessingNs(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsSelectivePerform(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsProcessingDisposal(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsDeemedDisposalFlag(extracted);
        }
        offset += 1;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetWsStoredGsDate(extracted);
        }
        offset += 6;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsNumField(parsedInt);
        }
        offset += 4;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetWsCostDisplay(extracted);
        }
        offset += 3;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsGtSub(parsedInt);
        }
        offset += 5;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsDisplayNumber(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsDisp1(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsDisp2(parsedDec);
        }
        offset += 15;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsZzzDisplayCount(parsedInt);
        }
        offset += 18;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsZzzCostCount(parsedInt);
        }
        offset += 18;
        if (offset + 34 <= data.Length)
        {
            _WWhenCompiled.SetWWhenCompiledAsString(data.Substring(offset, 34));
        }
        else
        {
            _WWhenCompiled.SetWWhenCompiledAsString(data.Substring(offset));
        }
        offset += 34;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetWReportName(extracted);
        }
        offset += 7;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWParKeyAccessible(extracted);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            _StatusKey.SetStatusKeyAsString(data.Substring(offset, 2));
        }
        else
        {
            _StatusKey.SetStatusKeyAsString(data.Substring(offset));
        }
        offset += 2;
        if (offset + 16 <= data.Length)
        {
            _WsLongDate.SetWsLongDateAsString(data.Substring(offset, 16));
        }
        else
        {
            _WsLongDate.SetWsLongDateAsString(data.Substring(offset));
        }
        offset += 16;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsResult(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsRemainder(parsedInt);
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsBondDeemedDisposal(extracted);
        }
        offset += 1;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWMarketPrice(parsedDec);
        }
        offset += 11;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsBondGainLoss(parsedDec);
        }
        offset += 17;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsBondProcessFlag(extracted);
        }
        offset += 1;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsBondCost(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsMv(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsRvIndexedCost(parsedDec);
        }
        offset += 17;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsMonthsToRedemption(parsedDec);
        }
        offset += 12;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsYearsToRedemption(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsSub(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsWttCatSub(parsedInt);
        }
        offset += 4;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsAccrualProceeds(parsedDec);
        }
        offset += 17;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsTrancheMonths(parsedDec);
        }
        offset += 12;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsAccrualDdProceeds(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsTotalAccrualDdProceeds(parsedDec);
        }
        offset += 17;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsEquityToBondFlag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsNonLrToLrBondFlag(extracted);
        }
        offset += 1;
        if (offset + 6 <= data.Length)
        {
            _WsCliPendDate9798.SetWsCliPendDate9798AsString(data.Substring(offset, 6));
        }
        else
        {
            _WsCliPendDate9798.SetWsCliPendDate9798AsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetWsCliPstartDate9899(extracted);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetWsCli16Mar98Date(extracted);
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsBondToBondFlag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsAcquisitionBondFlag(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWsTempWtsIndex(extracted);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWsTempWtieIndex(extracted);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWsWiGt(extracted);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWsWiTt(extracted);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWsWttc2(extracted);
        }
        offset += 4;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsTaperMatchUnitsLeft(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsTotalTaperMatchUnits(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsMiniTaperMatchUnits(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsTaperMatchGainLeft(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsTotalTaperMatchGain(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsMiniTaperMatchGain(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsTaperMatchProcLeft(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsTotalTaperMatchProc(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsMiniTaperMatchProc(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsTaperUnitsUsed(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsTaperGainUsed(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsTaperProcUsed(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsTotalTaperUnitsYtd(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsMiniTaperUnits(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsMiniTaperNewUnits(parsedDec);
        }
        offset += 17;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsSameDayTaperTranches(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsTaperTrancheSub(parsedInt);
        }
        offset += 4;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWsIndexationLimitFlag(extracted);
        }
        offset += 0;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsReorgDisposalValue(parsedDec);
        }
        offset += 17;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWsFund(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWsSedol(extracted);
        }
        offset += 0;
        if (offset + 4 <= data.Length)
        {
            _WsContract.SetWsContractAsString(data.Substring(offset, 4));
        }
        else
        {
            _WsContract.SetWsContractAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 8 <= data.Length)
        {
            _WsDsContractRef.SetWsDsContractRefAsString(data.Substring(offset, 8));
        }
        else
        {
            _WsDsContractRef.SetWsDsContractRefAsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _WsRvContractRef.SetWsRvContractRefAsString(data.Substring(offset, 8));
        }
        else
        {
            _WsRvContractRef.SetWsRvContractRefAsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetWSearchCalSedol(extracted);
        }
        offset += 11;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWExerciseProcessingFlag(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWSpecialsAsString();
    }
    // Set<>String Override function
    public void SetWSpecials(string value)
    {
        SetWSpecialsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller337()
    {
        return _Filler337;
    }
    
    // Standard Setter
    public void SetFiller337(string value)
    {
        _Filler337 = value;
    }
    
    // Get<>AsString()
    public string GetFiller337AsString()
    {
        return _Filler337.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller337AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler337 = value;
    }
    
    // Standard Getter
    public string GetWProgname()
    {
        return _WProgname;
    }
    
    // Standard Setter
    public void SetWProgname(string value)
    {
        _WProgname = value;
    }
    
    // Get<>AsString()
    public string GetWPrognameAsString()
    {
        return _WProgname.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWPrognameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WProgname = value;
    }
    
    // Standard Getter
    public decimal GetWOslTotal()
    {
        return _WOslTotal;
    }
    
    // Standard Setter
    public void SetWOslTotal(decimal value)
    {
        _WOslTotal = value;
    }
    
    // Get<>AsString()
    public string GetWOslTotalAsString()
    {
        return _WOslTotal.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWOslTotalAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WOslTotal = parsed;
    }
    
    // Standard Getter
    public decimal GetWAppCall()
    {
        return _WAppCall;
    }
    
    // Standard Setter
    public void SetWAppCall(decimal value)
    {
        _WAppCall = value;
    }
    
    // Get<>AsString()
    public string GetWAppCallAsString()
    {
        return _WAppCall.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWAppCallAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WAppCall = parsed;
    }
    
    // Standard Getter
    public decimal GetWPrevAppCalls()
    {
        return _WPrevAppCalls;
    }
    
    // Standard Setter
    public void SetWPrevAppCalls(decimal value)
    {
        _WPrevAppCalls = value;
    }
    
    // Get<>AsString()
    public string GetWPrevAppCallsAsString()
    {
        return _WPrevAppCalls.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWPrevAppCallsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WPrevAppCalls = parsed;
    }
    
    // Standard Getter
    public int GetWOslOccCount()
    {
        return _WOslOccCount;
    }
    
    // Standard Setter
    public void SetWOslOccCount(int value)
    {
        _WOslOccCount = value;
    }
    
    // Get<>AsString()
    public string GetWOslOccCountAsString()
    {
        return _WOslOccCount.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWOslOccCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WOslOccCount = parsed;
    }
    
    // Standard Getter
    public int GetWLoopCount()
    {
        return _WLoopCount;
    }
    
    // Standard Setter
    public void SetWLoopCount(int value)
    {
        _WLoopCount = value;
    }
    
    // Get<>AsString()
    public string GetWLoopCountAsString()
    {
        return _WLoopCount.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWLoopCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WLoopCount = parsed;
    }
    
    // Standard Getter
    public decimal GetWsStoreDsProceeds()
    {
        return _WsStoreDsProceeds;
    }
    
    // Standard Setter
    public void SetWsStoreDsProceeds(decimal value)
    {
        _WsStoreDsProceeds = value;
    }
    
    // Get<>AsString()
    public string GetWsStoreDsProceedsAsString()
    {
        return _WsStoreDsProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsStoreDsProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsStoreDsProceeds = parsed;
    }
    
    // Standard Getter
    public decimal GetWsStoreDpCost()
    {
        return _WsStoreDpCost;
    }
    
    // Standard Setter
    public void SetWsStoreDpCost(decimal value)
    {
        _WsStoreDpCost = value;
    }
    
    // Get<>AsString()
    public string GetWsStoreDpCostAsString()
    {
        return _WsStoreDpCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsStoreDpCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsStoreDpCost = parsed;
    }
    
    // Standard Getter
    public string GetWsStoreWiTdIndex()
    {
        return _WsStoreWiTdIndex;
    }
    
    // Standard Setter
    public void SetWsStoreWiTdIndex(string value)
    {
        _WsStoreWiTdIndex = value;
    }
    
    // Get<>AsString()
    public string GetWsStoreWiTdIndexAsString()
    {
        return _WsStoreWiTdIndex.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWsStoreWiTdIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsStoreWiTdIndex = value;
    }
    
    // Standard Getter
    public string GetWsStoreWiS()
    {
        return _WsStoreWiS;
    }
    
    // Standard Setter
    public void SetWsStoreWiS(string value)
    {
        _WsStoreWiS = value;
    }
    
    // Get<>AsString()
    public string GetWsStoreWiSAsString()
    {
        return _WsStoreWiS.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWsStoreWiSAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsStoreWiS = value;
    }
    
    // Standard Getter
    public decimal GetWsAccumulationValue()
    {
        return _WsAccumulationValue;
    }
    
    // Standard Setter
    public void SetWsAccumulationValue(decimal value)
    {
        _WsAccumulationValue = value;
    }
    
    // Get<>AsString()
    public string GetWsAccumulationValueAsString()
    {
        return _WsAccumulationValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsAccumulationValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsAccumulationValue = parsed;
    }
    
    // Standard Getter
    public string GetWsRaBaSearchCategory()
    {
        return _WsRaBaSearchCategory;
    }
    
    // Standard Setter
    public void SetWsRaBaSearchCategory(string value)
    {
        _WsRaBaSearchCategory = value;
    }
    
    // Get<>AsString()
    public string GetWsRaBaSearchCategoryAsString()
    {
        return _WsRaBaSearchCategory.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetWsRaBaSearchCategoryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsRaBaSearchCategory = value;
    }
    
    // Standard Getter
    public decimal GetWsRaBaTransactionValue()
    {
        return _WsRaBaTransactionValue;
    }
    
    // Standard Setter
    public void SetWsRaBaTransactionValue(decimal value)
    {
        _WsRaBaTransactionValue = value;
    }
    
    // Get<>AsString()
    public string GetWsRaBaTransactionValueAsString()
    {
        return _WsRaBaTransactionValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsRaBaTransactionValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsRaBaTransactionValue = parsed;
    }
    
    // Standard Getter
    public decimal GetWsRaBaSameDayValue()
    {
        return _WsRaBaSameDayValue;
    }
    
    // Standard Setter
    public void SetWsRaBaSameDayValue(decimal value)
    {
        _WsRaBaSameDayValue = value;
    }
    
    // Get<>AsString()
    public string GetWsRaBaSameDayValueAsString()
    {
        return _WsRaBaSameDayValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsRaBaSameDayValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsRaBaSameDayValue = parsed;
    }
    
    // Standard Getter
    public string GetWsLastRaBaCalSedol()
    {
        return _WsLastRaBaCalSedol;
    }
    
    // Standard Setter
    public void SetWsLastRaBaCalSedol(string value)
    {
        _WsLastRaBaCalSedol = value;
    }
    
    // Get<>AsString()
    public string GetWsLastRaBaCalSedolAsString()
    {
        return _WsLastRaBaCalSedol.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetWsLastRaBaCalSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsLastRaBaCalSedol = value;
    }
    
    // Standard Getter
    public string GetWsProcessingNs()
    {
        return _WsProcessingNs;
    }
    
    // Standard Setter
    public void SetWsProcessingNs(string value)
    {
        _WsProcessingNs = value;
    }
    
    // Get<>AsString()
    public string GetWsProcessingNsAsString()
    {
        return _WsProcessingNs.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsProcessingNsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsProcessingNs = value;
    }
    
    // Standard Getter
    public string GetWsSelectivePerform()
    {
        return _WsSelectivePerform;
    }
    
    // Standard Setter
    public void SetWsSelectivePerform(string value)
    {
        _WsSelectivePerform = value;
    }
    
    // Get<>AsString()
    public string GetWsSelectivePerformAsString()
    {
        return _WsSelectivePerform.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsSelectivePerformAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsSelectivePerform = value;
    }
    
    // Standard Getter
    public string GetWsProcessingDisposal()
    {
        return _WsProcessingDisposal;
    }
    
    // Standard Setter
    public void SetWsProcessingDisposal(string value)
    {
        _WsProcessingDisposal = value;
    }
    
    // Get<>AsString()
    public string GetWsProcessingDisposalAsString()
    {
        return _WsProcessingDisposal.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsProcessingDisposalAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsProcessingDisposal = value;
    }
    
    // Standard Getter
    public string GetWsDeemedDisposalFlag()
    {
        return _WsDeemedDisposalFlag;
    }
    
    // Standard Setter
    public void SetWsDeemedDisposalFlag(string value)
    {
        _WsDeemedDisposalFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsDeemedDisposalFlagAsString()
    {
        return _WsDeemedDisposalFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsDeemedDisposalFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsDeemedDisposalFlag = value;
    }
    
    // Standard Getter
    public string GetWsStoredGsDate()
    {
        return _WsStoredGsDate;
    }
    
    // Standard Setter
    public void SetWsStoredGsDate(string value)
    {
        _WsStoredGsDate = value;
    }
    
    // Get<>AsString()
    public string GetWsStoredGsDateAsString()
    {
        return _WsStoredGsDate.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetWsStoredGsDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsStoredGsDate = value;
    }
    
    // Standard Getter
    public int GetWsNumField()
    {
        return _WsNumField;
    }
    
    // Standard Setter
    public void SetWsNumField(int value)
    {
        _WsNumField = value;
    }
    
    // Get<>AsString()
    public string GetWsNumFieldAsString()
    {
        return _WsNumField.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsNumFieldAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsNumField = parsed;
    }
    
    // Standard Getter
    public string GetWsCostDisplay()
    {
        return _WsCostDisplay;
    }
    
    // Standard Setter
    public void SetWsCostDisplay(string value)
    {
        _WsCostDisplay = value;
    }
    
    // Get<>AsString()
    public string GetWsCostDisplayAsString()
    {
        return _WsCostDisplay.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetWsCostDisplayAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsCostDisplay = value;
    }
    
    // Standard Getter
    public int GetWsGtSub()
    {
        return _WsGtSub;
    }
    
    // Standard Setter
    public void SetWsGtSub(int value)
    {
        _WsGtSub = value;
    }
    
    // Get<>AsString()
    public string GetWsGtSubAsString()
    {
        return _WsGtSub.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWsGtSubAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsGtSub = parsed;
    }
    
    // Standard Getter
    public decimal GetWsDisplayNumber()
    {
        return _WsDisplayNumber;
    }
    
    // Standard Setter
    public void SetWsDisplayNumber(decimal value)
    {
        _WsDisplayNumber = value;
    }
    
    // Get<>AsString()
    public string GetWsDisplayNumberAsString()
    {
        return _WsDisplayNumber.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsDisplayNumberAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsDisplayNumber = parsed;
    }
    
    // Standard Getter
    public decimal GetWsDisp1()
    {
        return _WsDisp1;
    }
    
    // Standard Setter
    public void SetWsDisp1(decimal value)
    {
        _WsDisp1 = value;
    }
    
    // Get<>AsString()
    public string GetWsDisp1AsString()
    {
        return _WsDisp1.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsDisp1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsDisp1 = parsed;
    }
    
    // Standard Getter
    public decimal GetWsDisp2()
    {
        return _WsDisp2;
    }
    
    // Standard Setter
    public void SetWsDisp2(decimal value)
    {
        _WsDisp2 = value;
    }
    
    // Get<>AsString()
    public string GetWsDisp2AsString()
    {
        return _WsDisp2.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsDisp2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsDisp2 = parsed;
    }
    
    // Standard Getter
    public int GetWsZzzDisplayCount()
    {
        return _WsZzzDisplayCount;
    }
    
    // Standard Setter
    public void SetWsZzzDisplayCount(int value)
    {
        _WsZzzDisplayCount = value;
    }
    
    // Get<>AsString()
    public string GetWsZzzDisplayCountAsString()
    {
        return _WsZzzDisplayCount.ToString().PadLeft(18, '0');
    }
    
    // Set<>AsString()
    public void SetWsZzzDisplayCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsZzzDisplayCount = parsed;
    }
    
    // Standard Getter
    public int GetWsZzzCostCount()
    {
        return _WsZzzCostCount;
    }
    
    // Standard Setter
    public void SetWsZzzCostCount(int value)
    {
        _WsZzzCostCount = value;
    }
    
    // Get<>AsString()
    public string GetWsZzzCostCountAsString()
    {
        return _WsZzzCostCount.ToString().PadLeft(18, '0');
    }
    
    // Set<>AsString()
    public void SetWsZzzCostCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsZzzCostCount = parsed;
    }
    
    // Standard Getter
    public WWhenCompiled GetWWhenCompiled()
    {
        return _WWhenCompiled;
    }
    
    // Standard Setter
    public void SetWWhenCompiled(WWhenCompiled value)
    {
        _WWhenCompiled = value;
    }
    
    // Get<>AsString()
    public string GetWWhenCompiledAsString()
    {
        return _WWhenCompiled != null ? _WWhenCompiled.GetWWhenCompiledAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWWhenCompiledAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WWhenCompiled == null)
        {
            _WWhenCompiled = new WWhenCompiled();
        }
        _WWhenCompiled.SetWWhenCompiledAsString(value);
    }
    
    // Standard Getter
    public string GetWReportName()
    {
        return _WReportName;
    }
    
    // Standard Setter
    public void SetWReportName(string value)
    {
        _WReportName = value;
    }
    
    // Get<>AsString()
    public string GetWReportNameAsString()
    {
        return _WReportName.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetWReportNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WReportName = value;
    }
    
    // Standard Getter
    public string GetWParKeyAccessible()
    {
        return _WParKeyAccessible;
    }
    
    // Standard Setter
    public void SetWParKeyAccessible(string value)
    {
        _WParKeyAccessible = value;
    }
    
    // Get<>AsString()
    public string GetWParKeyAccessibleAsString()
    {
        return _WParKeyAccessible.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWParKeyAccessibleAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WParKeyAccessible = value;
    }
    
    // Standard Getter
    public StatusKey GetStatusKey()
    {
        return _StatusKey;
    }
    
    // Standard Setter
    public void SetStatusKey(StatusKey value)
    {
        _StatusKey = value;
    }
    
    // Get<>AsString()
    public string GetStatusKeyAsString()
    {
        return _StatusKey != null ? _StatusKey.GetStatusKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetStatusKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_StatusKey == null)
        {
            _StatusKey = new StatusKey();
        }
        _StatusKey.SetStatusKeyAsString(value);
    }
    
    // Standard Getter
    public WsLongDate GetWsLongDate()
    {
        return _WsLongDate;
    }
    
    // Standard Setter
    public void SetWsLongDate(WsLongDate value)
    {
        _WsLongDate = value;
    }
    
    // Get<>AsString()
    public string GetWsLongDateAsString()
    {
        return _WsLongDate != null ? _WsLongDate.GetWsLongDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsLongDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsLongDate == null)
        {
            _WsLongDate = new WsLongDate();
        }
        _WsLongDate.SetWsLongDateAsString(value);
    }
    
    // Standard Getter
    public int GetWsResult()
    {
        return _WsResult;
    }
    
    // Standard Setter
    public void SetWsResult(int value)
    {
        _WsResult = value;
    }
    
    // Get<>AsString()
    public string GetWsResultAsString()
    {
        return _WsResult.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsResultAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsResult = parsed;
    }
    
    // Standard Getter
    public int GetWsRemainder()
    {
        return _WsRemainder;
    }
    
    // Standard Setter
    public void SetWsRemainder(int value)
    {
        _WsRemainder = value;
    }
    
    // Get<>AsString()
    public string GetWsRemainderAsString()
    {
        return _WsRemainder.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsRemainderAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsRemainder = parsed;
    }
    
    // Standard Getter
    public string GetWsBondDeemedDisposal()
    {
        return _WsBondDeemedDisposal;
    }
    
    // Standard Setter
    public void SetWsBondDeemedDisposal(string value)
    {
        _WsBondDeemedDisposal = value;
    }
    
    // Get<>AsString()
    public string GetWsBondDeemedDisposalAsString()
    {
        return _WsBondDeemedDisposal.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsBondDeemedDisposalAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsBondDeemedDisposal = value;
    }
    
    // Standard Getter
    public decimal GetWMarketPrice()
    {
        return _WMarketPrice;
    }
    
    // Standard Setter
    public void SetWMarketPrice(decimal value)
    {
        _WMarketPrice = value;
    }
    
    // Get<>AsString()
    public string GetWMarketPriceAsString()
    {
        return _WMarketPrice.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWMarketPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WMarketPrice = parsed;
    }
    
    // Standard Getter
    public decimal GetWsBondGainLoss()
    {
        return _WsBondGainLoss;
    }
    
    // Standard Setter
    public void SetWsBondGainLoss(decimal value)
    {
        _WsBondGainLoss = value;
    }
    
    // Get<>AsString()
    public string GetWsBondGainLossAsString()
    {
        return _WsBondGainLoss.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsBondGainLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsBondGainLoss = parsed;
    }
    
    // Standard Getter
    public string GetWsBondProcessFlag()
    {
        return _WsBondProcessFlag;
    }
    
    // Standard Setter
    public void SetWsBondProcessFlag(string value)
    {
        _WsBondProcessFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsBondProcessFlagAsString()
    {
        return _WsBondProcessFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsBondProcessFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsBondProcessFlag = value;
    }
    
    // Standard Getter
    public decimal GetWsBondCost()
    {
        return _WsBondCost;
    }
    
    // Standard Setter
    public void SetWsBondCost(decimal value)
    {
        _WsBondCost = value;
    }
    
    // Get<>AsString()
    public string GetWsBondCostAsString()
    {
        return _WsBondCost.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsBondCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsBondCost = parsed;
    }
    
    // Standard Getter
    public decimal GetWsMv()
    {
        return _WsMv;
    }
    
    // Standard Setter
    public void SetWsMv(decimal value)
    {
        _WsMv = value;
    }
    
    // Get<>AsString()
    public string GetWsMvAsString()
    {
        return _WsMv.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsMvAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsMv = parsed;
    }
    
    // Standard Getter
    public decimal GetWsRvIndexedCost()
    {
        return _WsRvIndexedCost;
    }
    
    // Standard Setter
    public void SetWsRvIndexedCost(decimal value)
    {
        _WsRvIndexedCost = value;
    }
    
    // Get<>AsString()
    public string GetWsRvIndexedCostAsString()
    {
        return _WsRvIndexedCost.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsRvIndexedCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsRvIndexedCost = parsed;
    }
    
    // Standard Getter
    public decimal GetWsMonthsToRedemption()
    {
        return _WsMonthsToRedemption;
    }
    
    // Standard Setter
    public void SetWsMonthsToRedemption(decimal value)
    {
        _WsMonthsToRedemption = value;
    }
    
    // Get<>AsString()
    public string GetWsMonthsToRedemptionAsString()
    {
        return _WsMonthsToRedemption.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsMonthsToRedemptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsMonthsToRedemption = parsed;
    }
    
    // Standard Getter
    public int GetWsYearsToRedemption()
    {
        return _WsYearsToRedemption;
    }
    
    // Standard Setter
    public void SetWsYearsToRedemption(int value)
    {
        _WsYearsToRedemption = value;
    }
    
    // Get<>AsString()
    public string GetWsYearsToRedemptionAsString()
    {
        return _WsYearsToRedemption.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsYearsToRedemptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsYearsToRedemption = parsed;
    }
    
    // Standard Getter
    public int GetWsSub()
    {
        return _WsSub;
    }
    
    // Standard Setter
    public void SetWsSub(int value)
    {
        _WsSub = value;
    }
    
    // Get<>AsString()
    public string GetWsSubAsString()
    {
        return _WsSub.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsSubAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsSub = parsed;
    }
    
    // Standard Getter
    public int GetWsWttCatSub()
    {
        return _WsWttCatSub;
    }
    
    // Standard Setter
    public void SetWsWttCatSub(int value)
    {
        _WsWttCatSub = value;
    }
    
    // Get<>AsString()
    public string GetWsWttCatSubAsString()
    {
        return _WsWttCatSub.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsWttCatSubAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsWttCatSub = parsed;
    }
    
    // Standard Getter
    public decimal GetWsAccrualProceeds()
    {
        return _WsAccrualProceeds;
    }
    
    // Standard Setter
    public void SetWsAccrualProceeds(decimal value)
    {
        _WsAccrualProceeds = value;
    }
    
    // Get<>AsString()
    public string GetWsAccrualProceedsAsString()
    {
        return _WsAccrualProceeds.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsAccrualProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsAccrualProceeds = parsed;
    }
    
    // Standard Getter
    public decimal GetWsTrancheMonths()
    {
        return _WsTrancheMonths;
    }
    
    // Standard Setter
    public void SetWsTrancheMonths(decimal value)
    {
        _WsTrancheMonths = value;
    }
    
    // Get<>AsString()
    public string GetWsTrancheMonthsAsString()
    {
        return _WsTrancheMonths.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsTrancheMonthsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsTrancheMonths = parsed;
    }
    
    // Standard Getter
    public decimal GetWsAccrualDdProceeds()
    {
        return _WsAccrualDdProceeds;
    }
    
    // Standard Setter
    public void SetWsAccrualDdProceeds(decimal value)
    {
        _WsAccrualDdProceeds = value;
    }
    
    // Get<>AsString()
    public string GetWsAccrualDdProceedsAsString()
    {
        return _WsAccrualDdProceeds.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsAccrualDdProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsAccrualDdProceeds = parsed;
    }
    
    // Standard Getter
    public decimal GetWsTotalAccrualDdProceeds()
    {
        return _WsTotalAccrualDdProceeds;
    }
    
    // Standard Setter
    public void SetWsTotalAccrualDdProceeds(decimal value)
    {
        _WsTotalAccrualDdProceeds = value;
    }
    
    // Get<>AsString()
    public string GetWsTotalAccrualDdProceedsAsString()
    {
        return _WsTotalAccrualDdProceeds.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsTotalAccrualDdProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsTotalAccrualDdProceeds = parsed;
    }
    
    // Standard Getter
    public string GetWsEquityToBondFlag()
    {
        return _WsEquityToBondFlag;
    }
    
    // Standard Setter
    public void SetWsEquityToBondFlag(string value)
    {
        _WsEquityToBondFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsEquityToBondFlagAsString()
    {
        return _WsEquityToBondFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsEquityToBondFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsEquityToBondFlag = value;
    }
    
    // Standard Getter
    public string GetWsNonLrToLrBondFlag()
    {
        return _WsNonLrToLrBondFlag;
    }
    
    // Standard Setter
    public void SetWsNonLrToLrBondFlag(string value)
    {
        _WsNonLrToLrBondFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsNonLrToLrBondFlagAsString()
    {
        return _WsNonLrToLrBondFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsNonLrToLrBondFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsNonLrToLrBondFlag = value;
    }
    
    // Standard Getter
    public WsCliPendDate9798 GetWsCliPendDate9798()
    {
        return _WsCliPendDate9798;
    }
    
    // Standard Setter
    public void SetWsCliPendDate9798(WsCliPendDate9798 value)
    {
        _WsCliPendDate9798 = value;
    }
    
    // Get<>AsString()
    public string GetWsCliPendDate9798AsString()
    {
        return _WsCliPendDate9798 != null ? _WsCliPendDate9798.GetWsCliPendDate9798AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsCliPendDate9798AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsCliPendDate9798 == null)
        {
            _WsCliPendDate9798 = new WsCliPendDate9798();
        }
        _WsCliPendDate9798.SetWsCliPendDate9798AsString(value);
    }
    
    // Standard Getter
    public string GetWsCliPstartDate9899()
    {
        return _WsCliPstartDate9899;
    }
    
    // Standard Setter
    public void SetWsCliPstartDate9899(string value)
    {
        _WsCliPstartDate9899 = value;
    }
    
    // Get<>AsString()
    public string GetWsCliPstartDate9899AsString()
    {
        return _WsCliPstartDate9899.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetWsCliPstartDate9899AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsCliPstartDate9899 = value;
    }
    
    // Standard Getter
    public string GetWsCli16Mar98Date()
    {
        return _WsCli16Mar98Date;
    }
    
    // Standard Setter
    public void SetWsCli16Mar98Date(string value)
    {
        _WsCli16Mar98Date = value;
    }
    
    // Get<>AsString()
    public string GetWsCli16Mar98DateAsString()
    {
        return _WsCli16Mar98Date.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetWsCli16Mar98DateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsCli16Mar98Date = value;
    }
    
    // Standard Getter
    public string GetWsBondToBondFlag()
    {
        return _WsBondToBondFlag;
    }
    
    // Standard Setter
    public void SetWsBondToBondFlag(string value)
    {
        _WsBondToBondFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsBondToBondFlagAsString()
    {
        return _WsBondToBondFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsBondToBondFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsBondToBondFlag = value;
    }
    
    // Standard Getter
    public string GetWsAcquisitionBondFlag()
    {
        return _WsAcquisitionBondFlag;
    }
    
    // Standard Setter
    public void SetWsAcquisitionBondFlag(string value)
    {
        _WsAcquisitionBondFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsAcquisitionBondFlagAsString()
    {
        return _WsAcquisitionBondFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsAcquisitionBondFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsAcquisitionBondFlag = value;
    }
    
    // Standard Getter
    public string GetWsTempWtsIndex()
    {
        return _WsTempWtsIndex;
    }
    
    // Standard Setter
    public void SetWsTempWtsIndex(string value)
    {
        _WsTempWtsIndex = value;
    }
    
    // Get<>AsString()
    public string GetWsTempWtsIndexAsString()
    {
        return _WsTempWtsIndex.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWsTempWtsIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsTempWtsIndex = value;
    }
    
    // Standard Getter
    public string GetWsTempWtieIndex()
    {
        return _WsTempWtieIndex;
    }
    
    // Standard Setter
    public void SetWsTempWtieIndex(string value)
    {
        _WsTempWtieIndex = value;
    }
    
    // Get<>AsString()
    public string GetWsTempWtieIndexAsString()
    {
        return _WsTempWtieIndex.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWsTempWtieIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsTempWtieIndex = value;
    }
    
    // Standard Getter
    public string GetWsWiGt()
    {
        return _WsWiGt;
    }
    
    // Standard Setter
    public void SetWsWiGt(string value)
    {
        _WsWiGt = value;
    }
    
    // Get<>AsString()
    public string GetWsWiGtAsString()
    {
        return _WsWiGt.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWsWiGtAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsWiGt = value;
    }
    
    // Standard Getter
    public string GetWsWiTt()
    {
        return _WsWiTt;
    }
    
    // Standard Setter
    public void SetWsWiTt(string value)
    {
        _WsWiTt = value;
    }
    
    // Get<>AsString()
    public string GetWsWiTtAsString()
    {
        return _WsWiTt.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWsWiTtAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsWiTt = value;
    }
    
    // Standard Getter
    public string GetWsWttc2()
    {
        return _WsWttc2;
    }
    
    // Standard Setter
    public void SetWsWttc2(string value)
    {
        _WsWttc2 = value;
    }
    
    // Get<>AsString()
    public string GetWsWttc2AsString()
    {
        return _WsWttc2.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWsWttc2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsWttc2 = value;
    }
    
    // Standard Getter
    public decimal GetWsTaperMatchUnitsLeft()
    {
        return _WsTaperMatchUnitsLeft;
    }
    
    // Standard Setter
    public void SetWsTaperMatchUnitsLeft(decimal value)
    {
        _WsTaperMatchUnitsLeft = value;
    }
    
    // Get<>AsString()
    public string GetWsTaperMatchUnitsLeftAsString()
    {
        return _WsTaperMatchUnitsLeft.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsTaperMatchUnitsLeftAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsTaperMatchUnitsLeft = parsed;
    }
    
    // Standard Getter
    public decimal GetWsTotalTaperMatchUnits()
    {
        return _WsTotalTaperMatchUnits;
    }
    
    // Standard Setter
    public void SetWsTotalTaperMatchUnits(decimal value)
    {
        _WsTotalTaperMatchUnits = value;
    }
    
    // Get<>AsString()
    public string GetWsTotalTaperMatchUnitsAsString()
    {
        return _WsTotalTaperMatchUnits.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsTotalTaperMatchUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsTotalTaperMatchUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetWsMiniTaperMatchUnits()
    {
        return _WsMiniTaperMatchUnits;
    }
    
    // Standard Setter
    public void SetWsMiniTaperMatchUnits(decimal value)
    {
        _WsMiniTaperMatchUnits = value;
    }
    
    // Get<>AsString()
    public string GetWsMiniTaperMatchUnitsAsString()
    {
        return _WsMiniTaperMatchUnits.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsMiniTaperMatchUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsMiniTaperMatchUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetWsTaperMatchGainLeft()
    {
        return _WsTaperMatchGainLeft;
    }
    
    // Standard Setter
    public void SetWsTaperMatchGainLeft(decimal value)
    {
        _WsTaperMatchGainLeft = value;
    }
    
    // Get<>AsString()
    public string GetWsTaperMatchGainLeftAsString()
    {
        return _WsTaperMatchGainLeft.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsTaperMatchGainLeftAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsTaperMatchGainLeft = parsed;
    }
    
    // Standard Getter
    public decimal GetWsTotalTaperMatchGain()
    {
        return _WsTotalTaperMatchGain;
    }
    
    // Standard Setter
    public void SetWsTotalTaperMatchGain(decimal value)
    {
        _WsTotalTaperMatchGain = value;
    }
    
    // Get<>AsString()
    public string GetWsTotalTaperMatchGainAsString()
    {
        return _WsTotalTaperMatchGain.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsTotalTaperMatchGainAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsTotalTaperMatchGain = parsed;
    }
    
    // Standard Getter
    public decimal GetWsMiniTaperMatchGain()
    {
        return _WsMiniTaperMatchGain;
    }
    
    // Standard Setter
    public void SetWsMiniTaperMatchGain(decimal value)
    {
        _WsMiniTaperMatchGain = value;
    }
    
    // Get<>AsString()
    public string GetWsMiniTaperMatchGainAsString()
    {
        return _WsMiniTaperMatchGain.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsMiniTaperMatchGainAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsMiniTaperMatchGain = parsed;
    }
    
    // Standard Getter
    public decimal GetWsTaperMatchProcLeft()
    {
        return _WsTaperMatchProcLeft;
    }
    
    // Standard Setter
    public void SetWsTaperMatchProcLeft(decimal value)
    {
        _WsTaperMatchProcLeft = value;
    }
    
    // Get<>AsString()
    public string GetWsTaperMatchProcLeftAsString()
    {
        return _WsTaperMatchProcLeft.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsTaperMatchProcLeftAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsTaperMatchProcLeft = parsed;
    }
    
    // Standard Getter
    public decimal GetWsTotalTaperMatchProc()
    {
        return _WsTotalTaperMatchProc;
    }
    
    // Standard Setter
    public void SetWsTotalTaperMatchProc(decimal value)
    {
        _WsTotalTaperMatchProc = value;
    }
    
    // Get<>AsString()
    public string GetWsTotalTaperMatchProcAsString()
    {
        return _WsTotalTaperMatchProc.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsTotalTaperMatchProcAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsTotalTaperMatchProc = parsed;
    }
    
    // Standard Getter
    public decimal GetWsMiniTaperMatchProc()
    {
        return _WsMiniTaperMatchProc;
    }
    
    // Standard Setter
    public void SetWsMiniTaperMatchProc(decimal value)
    {
        _WsMiniTaperMatchProc = value;
    }
    
    // Get<>AsString()
    public string GetWsMiniTaperMatchProcAsString()
    {
        return _WsMiniTaperMatchProc.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsMiniTaperMatchProcAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsMiniTaperMatchProc = parsed;
    }
    
    // Standard Getter
    public decimal GetWsTaperUnitsUsed()
    {
        return _WsTaperUnitsUsed;
    }
    
    // Standard Setter
    public void SetWsTaperUnitsUsed(decimal value)
    {
        _WsTaperUnitsUsed = value;
    }
    
    // Get<>AsString()
    public string GetWsTaperUnitsUsedAsString()
    {
        return _WsTaperUnitsUsed.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsTaperUnitsUsedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsTaperUnitsUsed = parsed;
    }
    
    // Standard Getter
    public decimal GetWsTaperGainUsed()
    {
        return _WsTaperGainUsed;
    }
    
    // Standard Setter
    public void SetWsTaperGainUsed(decimal value)
    {
        _WsTaperGainUsed = value;
    }
    
    // Get<>AsString()
    public string GetWsTaperGainUsedAsString()
    {
        return _WsTaperGainUsed.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsTaperGainUsedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsTaperGainUsed = parsed;
    }
    
    // Standard Getter
    public decimal GetWsTaperProcUsed()
    {
        return _WsTaperProcUsed;
    }
    
    // Standard Setter
    public void SetWsTaperProcUsed(decimal value)
    {
        _WsTaperProcUsed = value;
    }
    
    // Get<>AsString()
    public string GetWsTaperProcUsedAsString()
    {
        return _WsTaperProcUsed.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsTaperProcUsedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsTaperProcUsed = parsed;
    }
    
    // Standard Getter
    public decimal GetWsTotalTaperUnitsYtd()
    {
        return _WsTotalTaperUnitsYtd;
    }
    
    // Standard Setter
    public void SetWsTotalTaperUnitsYtd(decimal value)
    {
        _WsTotalTaperUnitsYtd = value;
    }
    
    // Get<>AsString()
    public string GetWsTotalTaperUnitsYtdAsString()
    {
        return _WsTotalTaperUnitsYtd.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsTotalTaperUnitsYtdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsTotalTaperUnitsYtd = parsed;
    }
    
    // Standard Getter
    public decimal GetWsMiniTaperUnits()
    {
        return _WsMiniTaperUnits;
    }
    
    // Standard Setter
    public void SetWsMiniTaperUnits(decimal value)
    {
        _WsMiniTaperUnits = value;
    }
    
    // Get<>AsString()
    public string GetWsMiniTaperUnitsAsString()
    {
        return _WsMiniTaperUnits.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsMiniTaperUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsMiniTaperUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetWsMiniTaperNewUnits()
    {
        return _WsMiniTaperNewUnits;
    }
    
    // Standard Setter
    public void SetWsMiniTaperNewUnits(decimal value)
    {
        _WsMiniTaperNewUnits = value;
    }
    
    // Get<>AsString()
    public string GetWsMiniTaperNewUnitsAsString()
    {
        return _WsMiniTaperNewUnits.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsMiniTaperNewUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsMiniTaperNewUnits = parsed;
    }
    
    // Standard Getter
    public int GetWsSameDayTaperTranches()
    {
        return _WsSameDayTaperTranches;
    }
    
    // Standard Setter
    public void SetWsSameDayTaperTranches(int value)
    {
        _WsSameDayTaperTranches = value;
    }
    
    // Get<>AsString()
    public string GetWsSameDayTaperTranchesAsString()
    {
        return _WsSameDayTaperTranches.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsSameDayTaperTranchesAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsSameDayTaperTranches = parsed;
    }
    
    // Standard Getter
    public int GetWsTaperTrancheSub()
    {
        return _WsTaperTrancheSub;
    }
    
    // Standard Setter
    public void SetWsTaperTrancheSub(int value)
    {
        _WsTaperTrancheSub = value;
    }
    
    // Get<>AsString()
    public string GetWsTaperTrancheSubAsString()
    {
        return _WsTaperTrancheSub.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsTaperTrancheSubAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsTaperTrancheSub = parsed;
    }
    
    // Standard Getter
    public string GetWsIndexationLimitFlag()
    {
        return _WsIndexationLimitFlag;
    }
    
    // Standard Setter
    public void SetWsIndexationLimitFlag(string value)
    {
        _WsIndexationLimitFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsIndexationLimitFlagAsString()
    {
        return _WsIndexationLimitFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWsIndexationLimitFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsIndexationLimitFlag = value;
    }
    
    // Standard Getter
    public decimal GetWsReorgDisposalValue()
    {
        return _WsReorgDisposalValue;
    }
    
    // Standard Setter
    public void SetWsReorgDisposalValue(decimal value)
    {
        _WsReorgDisposalValue = value;
    }
    
    // Get<>AsString()
    public string GetWsReorgDisposalValueAsString()
    {
        return _WsReorgDisposalValue.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsReorgDisposalValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsReorgDisposalValue = parsed;
    }
    
    // Standard Getter
    public string GetWsFund()
    {
        return _WSFund;
    }
    
    // Standard Setter
    public void SetWsFund(string value)
    {
        _WSFund = value;
    }
    
    // Get<>AsString()
    public string GetWsFundAsString()
    {
        return _WSFund.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWsFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WSFund = value;
    }
    
    // Standard Getter
    public string GetWsSedol()
    {
        return _WSSedol;
    }
    
    // Standard Setter
    public void SetWsSedol(string value)
    {
        _WSSedol = value;
    }
    
    // Get<>AsString()
    public string GetWsSedolAsString()
    {
        return _WSSedol.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWsSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WSSedol = value;
    }
    
    // Standard Getter
    public WsContract GetWsContract()
    {
        return _WsContract;
    }
    
    // Standard Setter
    public void SetWsContract(WsContract value)
    {
        _WsContract = value;
    }
    
    // Get<>AsString()
    public string GetWsContractAsString()
    {
        return _WsContract != null ? _WsContract.GetWsContractAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsContractAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsContract == null)
        {
            _WsContract = new WsContract();
        }
        _WsContract.SetWsContractAsString(value);
    }
    
    // Standard Getter
    public WsDsContractRef GetWsDsContractRef()
    {
        return _WsDsContractRef;
    }
    
    // Standard Setter
    public void SetWsDsContractRef(WsDsContractRef value)
    {
        _WsDsContractRef = value;
    }
    
    // Get<>AsString()
    public string GetWsDsContractRefAsString()
    {
        return _WsDsContractRef != null ? _WsDsContractRef.GetWsDsContractRefAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsDsContractRefAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsDsContractRef == null)
        {
            _WsDsContractRef = new WsDsContractRef();
        }
        _WsDsContractRef.SetWsDsContractRefAsString(value);
    }
    
    // Standard Getter
    public WsRvContractRef GetWsRvContractRef()
    {
        return _WsRvContractRef;
    }
    
    // Standard Setter
    public void SetWsRvContractRef(WsRvContractRef value)
    {
        _WsRvContractRef = value;
    }
    
    // Get<>AsString()
    public string GetWsRvContractRefAsString()
    {
        return _WsRvContractRef != null ? _WsRvContractRef.GetWsRvContractRefAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsRvContractRefAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsRvContractRef == null)
        {
            _WsRvContractRef = new WsRvContractRef();
        }
        _WsRvContractRef.SetWsRvContractRefAsString(value);
    }
    
    // Standard Getter
    public string GetWSearchCalSedol()
    {
        return _WSearchCalSedol;
    }
    
    // Standard Setter
    public void SetWSearchCalSedol(string value)
    {
        _WSearchCalSedol = value;
    }
    
    // Get<>AsString()
    public string GetWSearchCalSedolAsString()
    {
        return _WSearchCalSedol.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetWSearchCalSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WSearchCalSedol = value;
    }
    
    // Standard Getter
    public string GetWExerciseProcessingFlag()
    {
        return _WExerciseProcessingFlag;
    }
    
    // Standard Setter
    public void SetWExerciseProcessingFlag(string value)
    {
        _WExerciseProcessingFlag = value;
    }
    
    // Get<>AsString()
    public string GetWExerciseProcessingFlagAsString()
    {
        return _WExerciseProcessingFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWExerciseProcessingFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WExerciseProcessingFlag = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWWhenCompiled(string value)
    {
        _WWhenCompiled.SetWWhenCompiledAsString(value);
    }
    // Nested Class: WWhenCompiled
    public class WWhenCompiled
    {
        private static int _size = 34;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WCompileTime, is_external=, is_static_class=False, static_prefix=
        private string _WCompileTime ="";
        
        
        
        
        // [DEBUG] Field: WCompileDate, is_external=, is_static_class=False, static_prefix=
        private string _WCompileDate ="";
        
        
        
        
        // [DEBUG] Field: Filler338, is_external=, is_static_class=False, static_prefix=
        private string _Filler338 ="";
        
        
        
        
    public WWhenCompiled() {}
    
    public WWhenCompiled(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWCompileTime(data.Substring(offset, 8).Trim());
        offset += 8;
        SetWCompileDate(data.Substring(offset, 12).Trim());
        offset += 12;
        SetFiller338(data.Substring(offset, 14).Trim());
        offset += 14;
        
    }
    
    // Serialization methods
    public string GetWWhenCompiledAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WCompileTime.PadRight(8));
        result.Append(_WCompileDate.PadRight(12));
        result.Append(_Filler338.PadRight(14));
        
        return result.ToString();
    }
    
    public void SetWWhenCompiledAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWCompileTime(extracted);
        }
        offset += 8;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            SetWCompileDate(extracted);
        }
        offset += 12;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            SetFiller338(extracted);
        }
        offset += 14;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWCompileTime()
    {
        return _WCompileTime;
    }
    
    // Standard Setter
    public void SetWCompileTime(string value)
    {
        _WCompileTime = value;
    }
    
    // Get<>AsString()
    public string GetWCompileTimeAsString()
    {
        return _WCompileTime.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWCompileTimeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WCompileTime = value;
    }
    
    // Standard Getter
    public string GetWCompileDate()
    {
        return _WCompileDate;
    }
    
    // Standard Setter
    public void SetWCompileDate(string value)
    {
        _WCompileDate = value;
    }
    
    // Get<>AsString()
    public string GetWCompileDateAsString()
    {
        return _WCompileDate.PadRight(12);
    }
    
    // Set<>AsString()
    public void SetWCompileDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WCompileDate = value;
    }
    
    // Standard Getter
    public string GetFiller338()
    {
        return _Filler338;
    }
    
    // Standard Setter
    public void SetFiller338(string value)
    {
        _Filler338 = value;
    }
    
    // Get<>AsString()
    public string GetFiller338AsString()
    {
        return _Filler338.PadRight(14);
    }
    
    // Set<>AsString()
    public void SetFiller338AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler338 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetStatusKey(string value)
{
    _StatusKey.SetStatusKeyAsString(value);
}
// Nested Class: StatusKey
public class StatusKey
{
    private static int _size = 2;
    
    // Fields in the class
    
    
    // [DEBUG] Field: StatusKey1, is_external=, is_static_class=False, static_prefix=
    private string _StatusKey1 ="";
    
    
    
    
    // [DEBUG] Field: StatusKey2, is_external=, is_static_class=False, static_prefix=
    private string _StatusKey2 ="";
    
    
    
    
public StatusKey() {}

public StatusKey(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetStatusKey1(data.Substring(offset, 1).Trim());
    offset += 1;
    SetStatusKey2(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetStatusKeyAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_StatusKey1.PadRight(1));
    result.Append(_StatusKey2.PadRight(1));
    
    return result.ToString();
}

public void SetStatusKeyAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetStatusKey1(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetStatusKey2(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetStatusKey1()
{
    return _StatusKey1;
}

// Standard Setter
public void SetStatusKey1(string value)
{
    _StatusKey1 = value;
}

// Get<>AsString()
public string GetStatusKey1AsString()
{
    return _StatusKey1.PadRight(1);
}

// Set<>AsString()
public void SetStatusKey1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _StatusKey1 = value;
}

// Standard Getter
public string GetStatusKey2()
{
    return _StatusKey2;
}

// Standard Setter
public void SetStatusKey2(string value)
{
    _StatusKey2 = value;
}

// Get<>AsString()
public string GetStatusKey2AsString()
{
    return _StatusKey2.PadRight(1);
}

// Set<>AsString()
public void SetStatusKey2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _StatusKey2 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsLongDate(string value)
{
    _WsLongDate.SetWsLongDateAsString(value);
}
// Nested Class: WsLongDate
public class WsLongDate
{
    private static int _size = 16;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsLongDateCcyymmdd, is_external=, is_static_class=False, static_prefix=
    private WsLongDate.WsLongDateCcyymmdd _WsLongDateCcyymmdd = new WsLongDate.WsLongDateCcyymmdd();
    
    
    
    
    // [DEBUG] Field: Filler339, is_external=, is_static_class=False, static_prefix=
    private WsLongDate.Filler339 _Filler339 = new WsLongDate.Filler339();
    
    
    
    
public WsLongDate() {}

public WsLongDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WsLongDateCcyymmdd.SetWsLongDateCcyymmddAsString(data.Substring(offset, WsLongDateCcyymmdd.GetSize()));
    offset += 8;
    _Filler339.SetFiller339AsString(data.Substring(offset, Filler339.GetSize()));
    offset += 8;
    
}

// Serialization methods
public string GetWsLongDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsLongDateCcyymmdd.GetWsLongDateCcyymmddAsString());
    result.Append(_Filler339.GetFiller339AsString());
    
    return result.ToString();
}

public void SetWsLongDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 8 <= data.Length)
    {
        _WsLongDateCcyymmdd.SetWsLongDateCcyymmddAsString(data.Substring(offset, 8));
    }
    else
    {
        _WsLongDateCcyymmdd.SetWsLongDateCcyymmddAsString(data.Substring(offset));
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        _Filler339.SetFiller339AsString(data.Substring(offset, 8));
    }
    else
    {
        _Filler339.SetFiller339AsString(data.Substring(offset));
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public WsLongDateCcyymmdd GetWsLongDateCcyymmdd()
{
    return _WsLongDateCcyymmdd;
}

// Standard Setter
public void SetWsLongDateCcyymmdd(WsLongDateCcyymmdd value)
{
    _WsLongDateCcyymmdd = value;
}

// Get<>AsString()
public string GetWsLongDateCcyymmddAsString()
{
    return _WsLongDateCcyymmdd != null ? _WsLongDateCcyymmdd.GetWsLongDateCcyymmddAsString() : "";
}

// Set<>AsString()
public void SetWsLongDateCcyymmddAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsLongDateCcyymmdd == null)
    {
        _WsLongDateCcyymmdd = new WsLongDateCcyymmdd();
    }
    _WsLongDateCcyymmdd.SetWsLongDateCcyymmddAsString(value);
}

// Standard Getter
public Filler339 GetFiller339()
{
    return _Filler339;
}

// Standard Setter
public void SetFiller339(Filler339 value)
{
    _Filler339 = value;
}

// Get<>AsString()
public string GetFiller339AsString()
{
    return _Filler339 != null ? _Filler339.GetFiller339AsString() : "";
}

// Set<>AsString()
public void SetFiller339AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler339 == null)
    {
        _Filler339 = new Filler339();
    }
    _Filler339.SetFiller339AsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WsLongDateCcyymmdd
public class WsLongDateCcyymmdd
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsLongDateCc, is_external=, is_static_class=False, static_prefix=
    private int _WsLongDateCc =0;
    
    
    
    
    // [DEBUG] Field: WsLongDateYymmdd, is_external=, is_static_class=False, static_prefix=
    private WsLongDateCcyymmdd.WsLongDateYymmdd _WsLongDateYymmdd = new WsLongDateCcyymmdd.WsLongDateYymmdd();
    
    
    
    
public WsLongDateCcyymmdd() {}

public WsLongDateCcyymmdd(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsLongDateCc(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    _WsLongDateYymmdd.SetWsLongDateYymmddAsString(data.Substring(offset, WsLongDateYymmdd.GetSize()));
    offset += 6;
    
}

// Serialization methods
public string GetWsLongDateCcyymmddAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsLongDateCc.ToString().PadLeft(2, '0'));
    result.Append(_WsLongDateYymmdd.GetWsLongDateYymmddAsString());
    
    return result.ToString();
}

public void SetWsLongDateCcyymmddAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWsLongDateCc(parsedInt);
    }
    offset += 2;
    if (offset + 6 <= data.Length)
    {
        _WsLongDateYymmdd.SetWsLongDateYymmddAsString(data.Substring(offset, 6));
    }
    else
    {
        _WsLongDateYymmdd.SetWsLongDateYymmddAsString(data.Substring(offset));
    }
    offset += 6;
}

// Getter and Setter methods

// Standard Getter
public int GetWsLongDateCc()
{
    return _WsLongDateCc;
}

// Standard Setter
public void SetWsLongDateCc(int value)
{
    _WsLongDateCc = value;
}

// Get<>AsString()
public string GetWsLongDateCcAsString()
{
    return _WsLongDateCc.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWsLongDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WsLongDateCc = parsed;
}

// Standard Getter
public WsLongDateYymmdd GetWsLongDateYymmdd()
{
    return _WsLongDateYymmdd;
}

// Standard Setter
public void SetWsLongDateYymmdd(WsLongDateYymmdd value)
{
    _WsLongDateYymmdd = value;
}

// Get<>AsString()
public string GetWsLongDateYymmddAsString()
{
    return _WsLongDateYymmdd != null ? _WsLongDateYymmdd.GetWsLongDateYymmddAsString() : "";
}

// Set<>AsString()
public void SetWsLongDateYymmddAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsLongDateYymmdd == null)
    {
        _WsLongDateYymmdd = new WsLongDateYymmdd();
    }
    _WsLongDateYymmdd.SetWsLongDateYymmddAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WsLongDateYymmdd
public class WsLongDateYymmdd
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsLongDateYy, is_external=, is_static_class=False, static_prefix=
    private int _WsLongDateYy =0;
    
    
    
    
    // [DEBUG] Field: WsLongDateMm, is_external=, is_static_class=False, static_prefix=
    private int _WsLongDateMm =0;
    
    
    
    
    // [DEBUG] Field: WsLongDateDd, is_external=, is_static_class=False, static_prefix=
    private int _WsLongDateDd =0;
    
    
    
    
public WsLongDateYymmdd() {}

public WsLongDateYymmdd(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsLongDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWsLongDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWsLongDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWsLongDateYymmddAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsLongDateYy.ToString().PadLeft(2, '0'));
    result.Append(_WsLongDateMm.ToString().PadLeft(2, '0'));
    result.Append(_WsLongDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWsLongDateYymmddAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWsLongDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWsLongDateMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWsLongDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWsLongDateYy()
{
    return _WsLongDateYy;
}

// Standard Setter
public void SetWsLongDateYy(int value)
{
    _WsLongDateYy = value;
}

// Get<>AsString()
public string GetWsLongDateYyAsString()
{
    return _WsLongDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWsLongDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WsLongDateYy = parsed;
}

// Standard Getter
public int GetWsLongDateMm()
{
    return _WsLongDateMm;
}

// Standard Setter
public void SetWsLongDateMm(int value)
{
    _WsLongDateMm = value;
}

// Get<>AsString()
public string GetWsLongDateMmAsString()
{
    return _WsLongDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWsLongDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WsLongDateMm = parsed;
}

// Standard Getter
public int GetWsLongDateDd()
{
    return _WsLongDateDd;
}

// Standard Setter
public void SetWsLongDateDd(int value)
{
    _WsLongDateDd = value;
}

// Get<>AsString()
public string GetWsLongDateDdAsString()
{
    return _WsLongDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWsLongDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WsLongDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: Filler339
public class Filler339
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsLongDateCcyy, is_external=, is_static_class=False, static_prefix=
    private int _WsLongDateCcyy =0;
    
    
    
    
    // [DEBUG] Field: Filler340, is_external=, is_static_class=False, static_prefix=
    private int _Filler340 =0;
    
    
    
    
public Filler339() {}

public Filler339(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsLongDateCcyy(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetFiller340(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller339AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsLongDateCcyy.ToString().PadLeft(4, '0'));
    result.Append(_Filler340.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller339AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWsLongDateCcyy(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetFiller340(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetWsLongDateCcyy()
{
    return _WsLongDateCcyy;
}

// Standard Setter
public void SetWsLongDateCcyy(int value)
{
    _WsLongDateCcyy = value;
}

// Get<>AsString()
public string GetWsLongDateCcyyAsString()
{
    return _WsLongDateCcyy.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetWsLongDateCcyyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WsLongDateCcyy = parsed;
}

// Standard Getter
public int GetFiller340()
{
    return _Filler340;
}

// Standard Setter
public void SetFiller340(int value)
{
    _Filler340 = value;
}

// Get<>AsString()
public string GetFiller340AsString()
{
    return _Filler340.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetFiller340AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Filler340 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetWsCliPendDate9798(string value)
{
    _WsCliPendDate9798.SetWsCliPendDate9798AsString(value);
}
// Nested Class: WsCliPendDate9798
public class WsCliPendDate9798
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsCliPendDate9798Yymm, is_external=, is_static_class=False, static_prefix=
    private string _WsCliPendDate9798Yymm ="9804";
    
    
    
    
    // [DEBUG] Field: WsCliPendDate9798Dd, is_external=, is_static_class=False, static_prefix=
    private string _WsCliPendDate9798Dd ="05";
    
    
    
    
public WsCliPendDate9798() {}

public WsCliPendDate9798(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsCliPendDate9798Yymm(data.Substring(offset, 4).Trim());
    offset += 4;
    SetWsCliPendDate9798Dd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetWsCliPendDate9798AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsCliPendDate9798Yymm.PadRight(4));
    result.Append(_WsCliPendDate9798Dd.PadRight(2));
    
    return result.ToString();
}

public void SetWsCliPendDate9798AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWsCliPendDate9798Yymm(extracted);
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWsCliPendDate9798Dd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetWsCliPendDate9798Yymm()
{
    return _WsCliPendDate9798Yymm;
}

// Standard Setter
public void SetWsCliPendDate9798Yymm(string value)
{
    _WsCliPendDate9798Yymm = value;
}

// Get<>AsString()
public string GetWsCliPendDate9798YymmAsString()
{
    return _WsCliPendDate9798Yymm.PadRight(4);
}

// Set<>AsString()
public void SetWsCliPendDate9798YymmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsCliPendDate9798Yymm = value;
}

// Standard Getter
public string GetWsCliPendDate9798Dd()
{
    return _WsCliPendDate9798Dd;
}

// Standard Setter
public void SetWsCliPendDate9798Dd(string value)
{
    _WsCliPendDate9798Dd = value;
}

// Get<>AsString()
public string GetWsCliPendDate9798DdAsString()
{
    return _WsCliPendDate9798Dd.PadRight(2);
}

// Set<>AsString()
public void SetWsCliPendDate9798DdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsCliPendDate9798Dd = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsContract(string value)
{
    _WsContract.SetWsContractAsString(value);
}
// Nested Class: WsContract
public class WsContract
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler341, is_external=, is_static_class=False, static_prefix=
    private string _Filler341 ="";
    
    
    
    
    // [DEBUG] Field: WSLastContract, is_external=, is_static_class=False, static_prefix=
    private string _WSLastContract ="";
    
    
    
    
    // [DEBUG] Field: WSLastContractB, is_external=, is_static_class=False, static_prefix=
    private int _WSLastContractB =0;
    
    
    
    
public WsContract() {}

public WsContract(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller341(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWsLastContract(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWsLastContractB(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetWsContractAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler341.PadRight(0));
    result.Append(_WSLastContract.PadRight(0));
    result.Append(_WSLastContractB.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetWsContractAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller341(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWsLastContract(extracted);
    }
    offset += 0;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWsLastContractB(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller341()
{
    return _Filler341;
}

// Standard Setter
public void SetFiller341(string value)
{
    _Filler341 = value;
}

// Get<>AsString()
public string GetFiller341AsString()
{
    return _Filler341.PadRight(0);
}

// Set<>AsString()
public void SetFiller341AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler341 = value;
}

// Standard Getter
public string GetWsLastContract()
{
    return _WSLastContract;
}

// Standard Setter
public void SetWsLastContract(string value)
{
    _WSLastContract = value;
}

// Get<>AsString()
public string GetWsLastContractAsString()
{
    return _WSLastContract.PadRight(0);
}

// Set<>AsString()
public void SetWsLastContractAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WSLastContract = value;
}

// Standard Getter
public int GetWsLastContractB()
{
    return _WSLastContractB;
}

// Standard Setter
public void SetWsLastContractB(int value)
{
    _WSLastContractB = value;
}

// Get<>AsString()
public string GetWsLastContractBAsString()
{
    return _WSLastContractB.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetWsLastContractBAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WSLastContractB = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsDsContractRef(string value)
{
    _WsDsContractRef.SetWsDsContractRefAsString(value);
}
// Nested Class: WsDsContractRef
public class WsDsContractRef
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler342, is_external=, is_static_class=False, static_prefix=
    private string _Filler342 ="DS";
    
    
    
    
    // [DEBUG] Field: WsDsContractNum, is_external=, is_static_class=False, static_prefix=
    private int _WsDsContractNum =0;
    
    
    
    
public WsDsContractRef() {}

public WsDsContractRef(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller342(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWsDsContractNum(int.Parse(data.Substring(offset, 8).Trim()));
    offset += 8;
    
}

// Serialization methods
public string GetWsDsContractRefAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler342.PadRight(0));
    result.Append(_WsDsContractNum.ToString().PadLeft(8, '0'));
    
    return result.ToString();
}

public void SetWsDsContractRefAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller342(extracted);
    }
    offset += 0;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWsDsContractNum(parsedInt);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller342()
{
    return _Filler342;
}

// Standard Setter
public void SetFiller342(string value)
{
    _Filler342 = value;
}

// Get<>AsString()
public string GetFiller342AsString()
{
    return _Filler342.PadRight(0);
}

// Set<>AsString()
public void SetFiller342AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler342 = value;
}

// Standard Getter
public int GetWsDsContractNum()
{
    return _WsDsContractNum;
}

// Standard Setter
public void SetWsDsContractNum(int value)
{
    _WsDsContractNum = value;
}

// Get<>AsString()
public string GetWsDsContractNumAsString()
{
    return _WsDsContractNum.ToString().PadLeft(8, '0');
}

// Set<>AsString()
public void SetWsDsContractNumAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WsDsContractNum = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsRvContractRef(string value)
{
    _WsRvContractRef.SetWsRvContractRefAsString(value);
}
// Nested Class: WsRvContractRef
public class WsRvContractRef
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler343, is_external=, is_static_class=False, static_prefix=
    private string _Filler343 ="RV";
    
    
    
    
    // [DEBUG] Field: WsRvContractNum, is_external=, is_static_class=False, static_prefix=
    private int _WsRvContractNum =0;
    
    
    
    
public WsRvContractRef() {}

public WsRvContractRef(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller343(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWsRvContractNum(int.Parse(data.Substring(offset, 8).Trim()));
    offset += 8;
    
}

// Serialization methods
public string GetWsRvContractRefAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler343.PadRight(0));
    result.Append(_WsRvContractNum.ToString().PadLeft(8, '0'));
    
    return result.ToString();
}

public void SetWsRvContractRefAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller343(extracted);
    }
    offset += 0;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWsRvContractNum(parsedInt);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller343()
{
    return _Filler343;
}

// Standard Setter
public void SetFiller343(string value)
{
    _Filler343 = value;
}

// Get<>AsString()
public string GetFiller343AsString()
{
    return _Filler343.PadRight(0);
}

// Set<>AsString()
public void SetFiller343AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler343 = value;
}

// Standard Getter
public int GetWsRvContractNum()
{
    return _WsRvContractNum;
}

// Standard Setter
public void SetWsRvContractNum(int value)
{
    _WsRvContractNum = value;
}

// Get<>AsString()
public string GetWsRvContractNumAsString()
{
    return _WsRvContractNum.ToString().PadLeft(8, '0');
}

// Set<>AsString()
public void SetWsRvContractNumAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WsRvContractNum = parsed;
}



public static int GetSize()
{
    return _size;
}

}

}}