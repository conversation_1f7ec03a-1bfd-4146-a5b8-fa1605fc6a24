﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Legacy4.Equity.CobMod.Models;

namespace Legacy4.Equity.CobMod.Facade
{
    /// <summary>
    /// C# implementation of EquityFacadeClass from COBOL
    /// </summary>
    public class EquityFacadeClass
    {
        /// <summary>
        /// Implementation of ExecEquityFacadeClass method
        /// </summary>
        public void ExecEquityFacadeClass(
            ref string EquityAction,
            ref string EquityReportAction,
            ref string EquityUserID,
            ref string EquityPassword,
            ref string EquityUserDataPath,
            ref string EquityUserNo,
            ref string EquityYear,
            ref string EquityReturnCode,
            ref string EquityGenerationNo,
            ref string EquityLogSwitch,
            ref string EquityScheduleType,
            ref string EquityAnalysisSchedule,
            ref string EquityRealisedVersion,
            ref string EquitySedolNumbers,
            ref string EquityScanxFormat,
            ref string EquityCalcFundCode,
            ref string EquityCalcSedolCode,
            ref string EquityCalcDisposalDate,
            ref string EquityCalcMarketPrice,
            ref string EquityCalcParentPrice,
            ref string EquityCalcUnitsSold,
            ref string EquityCalcProfit,
            ref string EquityCalcGain,
            ref string EquitySegregateAssets,
            ref string EquityCGTSCOT1Format,
            ref string EquityUnrealisedGains,
            ref string EquitySedolNumbersLR,
            ref bool EquityDerivatives,
            ref bool EquityPLOnDistributions,
            ref bool EquityFullPricingExport,
            ref bool EquityIrishCGT,
            ref bool EquityIrishCGTProRata)
        {
            // Create an instance of EquityRouterLinkage
            EquityRouterLinkage equityRouterLinkage = new EquityRouterLinkage();

            // Populate the linkage structure
            equityRouterLinkage.Action = EquityAction;
            equityRouterLinkage.ReportAction = EquityReportAction;
            equityRouterLinkage.UserID = EquityUserID;
            equityRouterLinkage.Password = EquityPassword;
            equityRouterLinkage.UserDataPath = EquityUserDataPath;
            equityRouterLinkage.UserNo = EquityUserNo;
            equityRouterLinkage.Year = EquityYear;
            equityRouterLinkage.ReturnCode = EquityReturnCode;
            equityRouterLinkage.GenerationNo = EquityGenerationNo;
            equityRouterLinkage.LogSwitch = EquityLogSwitch;
            equityRouterLinkage.ScheduleType = EquityScheduleType;
            equityRouterLinkage.AnalysisSchedule = EquityAnalysisSchedule;
            equityRouterLinkage.RealisedVersion = EquityRealisedVersion;
            equityRouterLinkage.SedolNumbers = EquitySedolNumbers;
            equityRouterLinkage.ScanxFormat = EquityScanxFormat;
            equityRouterLinkage.CalcFundCode = EquityCalcFundCode;
            equityRouterLinkage.CalcSedolCode = EquityCalcSedolCode;
            equityRouterLinkage.CalcDisposalDate = EquityCalcDisposalDate;
            equityRouterLinkage.CalcMarketPrice = EquityCalcMarketPrice;
            equityRouterLinkage.CalcParentPrice = EquityCalcParentPrice;
            equityRouterLinkage.CalcUnitsSold = EquityCalcUnitsSold;
            equityRouterLinkage.CalcProfit = EquityCalcProfit;
            equityRouterLinkage.CalcGain = EquityCalcGain;
            equityRouterLinkage.SegregateAssets = EquitySegregateAssets;
            equityRouterLinkage.CGTSCOT1Format = EquityCGTSCOT1Format;
            equityRouterLinkage.UnrealisedGains = EquityUnrealisedGains;
            equityRouterLinkage.SedolNumbersLR = EquitySedolNumbersLR;

            // Handle boolean values
            equityRouterLinkage.Derivatives = EquityDerivatives ? "Y" : "N";
            equityRouterLinkage.PLDistributions = EquityPLOnDistributions ? "Y" : "N";
            equityRouterLinkage.FullPricingExport = EquityFullPricingExport ? "Y" : "N";
            equityRouterLinkage.IrishCGT = EquityIrishCGT ? "Y" : "N";
            equityRouterLinkage.IrishCGTProRata = EquityIrishCGTProRata ? "Y" : "N";

            // Create RunUnit
            EquityRouter runUnit = new EquityRouter();
            try
            {
                // Call COBOL program
                runUnit.Execute(equityRouterLinkage);
            }
            catch (Exception ex)
            {
                throw ex;
            }

            // Update ref parameters
            EquityAction = equityRouterLinkage.Action;
            EquityReportAction = equityRouterLinkage.ReportAction;
            EquityUserID = equityRouterLinkage.UserID;
            EquityPassword = equityRouterLinkage.Password;
            EquityUserDataPath = equityRouterLinkage.UserDataPath;
            EquityUserNo = equityRouterLinkage.UserNo;
            EquityYear = equityRouterLinkage.Year;
            EquityReturnCode = Environment.GetEnvironmentVariable("RETURN-CODE") ?? "000000";
            EquityGenerationNo = equityRouterLinkage.GenerationNo;
            EquityLogSwitch = equityRouterLinkage.LogSwitch;
            EquityScheduleType = equityRouterLinkage.ScheduleType;
            EquityAnalysisSchedule = equityRouterLinkage.AnalysisSchedule;
            EquityRealisedVersion = equityRouterLinkage.RealisedVersion;
            EquitySedolNumbers = equityRouterLinkage.SedolNumbers;
            EquityScanxFormat = equityRouterLinkage.ScanxFormat;
            EquityCalcFundCode = equityRouterLinkage.CalcFundCode;
            EquityCalcSedolCode = equityRouterLinkage.CalcSedolCode;
            EquityCalcDisposalDate = equityRouterLinkage.CalcDisposalDate;
            EquityCalcMarketPrice = equityRouterLinkage.CalcMarketPrice;
            EquityCalcParentPrice = equityRouterLinkage.CalcParentPrice;
            EquityCalcUnitsSold = equityRouterLinkage.CalcUnitsSold;
            EquityCalcProfit = equityRouterLinkage.CalcProfit;
            EquityCalcGain = equityRouterLinkage.CalcGain;
            EquitySegregateAssets = equityRouterLinkage.SegregateAssets;
            EquityCGTSCOT1Format = equityRouterLinkage.CGTSCOT1Format;
            EquityUnrealisedGains = equityRouterLinkage.UnrealisedGains;
            EquitySedolNumbersLR = equityRouterLinkage.SedolNumbersLR;
        }
    }
}
