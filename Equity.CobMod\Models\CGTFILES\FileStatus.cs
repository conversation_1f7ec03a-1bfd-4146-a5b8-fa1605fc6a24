using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing FileStatus Data Structure

public class FileStatus
{
    private static int _size = 2;
    // [DEBUG] Class: FileStatus, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Status1, is_external=, is_static_class=False, static_prefix=
    private string _Status1 ="";
    
    
    
    
    // [DEBUG] Field: Status2, is_external=, is_static_class=False, static_prefix=
    private string _Status2 ="";
    
    
    
    
    
    // Serialization methods
    public string GetFileStatusAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Status1.PadRight(1));
        result.Append(_Status2.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetFileStatusAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetStatus1(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetStatus2(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetFileStatusAsString();
    }
    // Set<>String Override function
    public void SetFileStatus(string value)
    {
        SetFileStatusAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetStatus1()
    {
        return _Status1;
    }
    
    // Standard Setter
    public void SetStatus1(string value)
    {
        _Status1 = value;
    }
    
    // Get<>AsString()
    public string GetStatus1AsString()
    {
        return _Status1.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetStatus1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Status1 = value;
    }
    
    // Standard Getter
    public string GetStatus2()
    {
        return _Status2;
    }
    
    // Standard Setter
    public void SetStatus2(string value)
    {
        _Status2 = value;
    }
    
    // Get<>AsString()
    public string GetStatus2AsString()
    {
        return _Status2.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetStatus2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Status2 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}