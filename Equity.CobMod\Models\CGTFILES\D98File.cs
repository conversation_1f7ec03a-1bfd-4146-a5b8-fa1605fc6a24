using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D98File Data Structure

public class D98File
{
    private static int _size = 12;
    // [DEBUG] Class: D98File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler285, is_external=, is_static_class=False, static_prefix=
    private string _Filler285 ="$";
    
    
    
    
    // [DEBUG] Field: D98UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D98UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler286, is_external=, is_static_class=False, static_prefix=
    private string _Filler286 ="DP";
    
    
    
    
    // [DEBUG] Field: D98ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D98ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler287, is_external=, is_static_class=False, static_prefix=
    private string _Filler287 =".DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD98FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler285.PadRight(1));
        result.Append(_D98UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler286.PadRight(2));
        result.Append(_D98ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler287.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD98FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller285(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD98UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller286(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD98ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller287(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD98FileAsString();
    }
    // Set<>String Override function
    public void SetD98File(string value)
    {
        SetD98FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller285()
    {
        return _Filler285;
    }
    
    // Standard Setter
    public void SetFiller285(string value)
    {
        _Filler285 = value;
    }
    
    // Get<>AsString()
    public string GetFiller285AsString()
    {
        return _Filler285.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller285AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler285 = value;
    }
    
    // Standard Getter
    public int GetD98UserNo()
    {
        return _D98UserNo;
    }
    
    // Standard Setter
    public void SetD98UserNo(int value)
    {
        _D98UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD98UserNoAsString()
    {
        return _D98UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD98UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D98UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller286()
    {
        return _Filler286;
    }
    
    // Standard Setter
    public void SetFiller286(string value)
    {
        _Filler286 = value;
    }
    
    // Get<>AsString()
    public string GetFiller286AsString()
    {
        return _Filler286.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller286AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler286 = value;
    }
    
    // Standard Getter
    public int GetD98ReportNo()
    {
        return _D98ReportNo;
    }
    
    // Standard Setter
    public void SetD98ReportNo(int value)
    {
        _D98ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD98ReportNoAsString()
    {
        return _D98ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD98ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D98ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller287()
    {
        return _Filler287;
    }
    
    // Standard Setter
    public void SetFiller287(string value)
    {
        _Filler287 = value;
    }
    
    // Get<>AsString()
    public string GetFiller287AsString()
    {
        return _Filler287.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller287AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler287 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}