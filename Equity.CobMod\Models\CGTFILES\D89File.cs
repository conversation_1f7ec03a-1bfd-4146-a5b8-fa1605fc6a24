using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D89File Data Structure

public class D89File
{
    private static int _size = 128;
    // [DEBUG] Class: D89File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler266, is_external=, is_static_class=False, static_prefix=
    private string _Filler266 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD89FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler266.PadRight(128));
        
        return result.ToString();
    }
    
    public void SetD89FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 128 <= data.Length)
        {
            string extracted = data.Substring(offset, 128).Trim();
            SetFiller266(extracted);
        }
        offset += 128;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD89FileAsString();
    }
    // Set<>String Override function
    public void SetD89File(string value)
    {
        SetD89FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller266()
    {
        return _Filler266;
    }
    
    // Standard Setter
    public void SetFiller266(string value)
    {
        _Filler266 = value;
    }
    
    // Get<>AsString()
    public string GetFiller266AsString()
    {
        return _Filler266.PadRight(128);
    }
    
    // Set<>AsString()
    public void SetFiller266AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler266 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}