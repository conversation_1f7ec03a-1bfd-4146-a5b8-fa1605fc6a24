using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing Cgtdate2LinkageDate10 Data Structure

public class Cgtdate2LinkageDate10
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate10, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd10, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd10 =0;
    
    
    
    
    // [DEBUG] Field: Filler55, is_external=, is_static_class=False, static_prefix=
    private Filler55 _Filler55 = new Filler55();
    
    
    
    
    // [DEBUG] Field: Filler56, is_external=, is_static_class=False, static_prefix=
    private Filler56 _Filler56 = new Filler56();
    
    
    
    
    // [DEBUG] Field: Filler57, is_external=, is_static_class=False, static_prefix=
    private Filler57 _Filler57 = new Filler57();
    
    
    
    
    // [DEBUG] Field: Filler58, is_external=, is_static_class=False, static_prefix=
    private Filler58 _Filler58 = new Filler58();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate10AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd10.ToString().PadLeft(8, '0'));
        result.Append(_Filler55.GetFiller55AsString());
        result.Append(_Filler56.GetFiller56AsString());
        result.Append(_Filler57.GetFiller57AsString());
        result.Append(_Filler58.GetFiller58AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate10AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd10(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler55.SetFiller55AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler55.SetFiller55AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler56.SetFiller56AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler56.SetFiller56AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler57.SetFiller57AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler57.SetFiller57AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler58.SetFiller58AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler58.SetFiller58AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate10AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate10(string value)
    {
        SetCgtdate2LinkageDate10AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd10()
    {
        return _Cgtdate2Ccyymmdd10;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd10(int value)
    {
        _Cgtdate2Ccyymmdd10 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd10AsString()
    {
        return _Cgtdate2Ccyymmdd10.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd10 = parsed;
    }
    
    // Standard Getter
    public Filler55 GetFiller55()
    {
        return _Filler55;
    }
    
    // Standard Setter
    public void SetFiller55(Filler55 value)
    {
        _Filler55 = value;
    }
    
    // Get<>AsString()
    public string GetFiller55AsString()
    {
        return _Filler55 != null ? _Filler55.GetFiller55AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller55AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler55 == null)
        {
            _Filler55 = new Filler55();
        }
        _Filler55.SetFiller55AsString(value);
    }
    
    // Standard Getter
    public Filler56 GetFiller56()
    {
        return _Filler56;
    }
    
    // Standard Setter
    public void SetFiller56(Filler56 value)
    {
        _Filler56 = value;
    }
    
    // Get<>AsString()
    public string GetFiller56AsString()
    {
        return _Filler56 != null ? _Filler56.GetFiller56AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller56AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler56 == null)
        {
            _Filler56 = new Filler56();
        }
        _Filler56.SetFiller56AsString(value);
    }
    
    // Standard Getter
    public Filler57 GetFiller57()
    {
        return _Filler57;
    }
    
    // Standard Setter
    public void SetFiller57(Filler57 value)
    {
        _Filler57 = value;
    }
    
    // Get<>AsString()
    public string GetFiller57AsString()
    {
        return _Filler57 != null ? _Filler57.GetFiller57AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller57AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler57 == null)
        {
            _Filler57 = new Filler57();
        }
        _Filler57.SetFiller57AsString(value);
    }
    
    // Standard Getter
    public Filler58 GetFiller58()
    {
        return _Filler58;
    }
    
    // Standard Setter
    public void SetFiller58(Filler58 value)
    {
        _Filler58 = value;
    }
    
    // Get<>AsString()
    public string GetFiller58AsString()
    {
        return _Filler58 != null ? _Filler58.GetFiller58AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller58AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler58 == null)
        {
            _Filler58 = new Filler58();
        }
        _Filler58.SetFiller58AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller55(string value)
    {
        _Filler55.SetFiller55AsString(value);
    }
    // Nested Class: Filler55
    public class Filler55
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc10, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc10 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd10, is_external=, is_static_class=False, static_prefix=
        private Filler55.Cgtdate2Yymmdd10 _Cgtdate2Yymmdd10 = new Filler55.Cgtdate2Yymmdd10();
        
        
        
        
    public Filler55() {}
    
    public Filler55(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc10(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd10.SetCgtdate2Yymmdd10AsString(data.Substring(offset, Cgtdate2Yymmdd10.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller55AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc10.PadRight(2));
        result.Append(_Cgtdate2Yymmdd10.GetCgtdate2Yymmdd10AsString());
        
        return result.ToString();
    }
    
    public void SetFiller55AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc10(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd10.SetCgtdate2Yymmdd10AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd10.SetCgtdate2Yymmdd10AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc10()
    {
        return _Cgtdate2Cc10;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc10(string value)
    {
        _Cgtdate2Cc10 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc10AsString()
    {
        return _Cgtdate2Cc10.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc10 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd10 GetCgtdate2Yymmdd10()
    {
        return _Cgtdate2Yymmdd10;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd10(Cgtdate2Yymmdd10 value)
    {
        _Cgtdate2Yymmdd10 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd10AsString()
    {
        return _Cgtdate2Yymmdd10 != null ? _Cgtdate2Yymmdd10.GetCgtdate2Yymmdd10AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd10 == null)
        {
            _Cgtdate2Yymmdd10 = new Cgtdate2Yymmdd10();
        }
        _Cgtdate2Yymmdd10.SetCgtdate2Yymmdd10AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd10
    public class Cgtdate2Yymmdd10
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy10, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy10 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd10, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd10.Cgtdate2Mmdd10 _Cgtdate2Mmdd10 = new Cgtdate2Yymmdd10.Cgtdate2Mmdd10();
        
        
        
        
    public Cgtdate2Yymmdd10() {}
    
    public Cgtdate2Yymmdd10(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy10(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd10.SetCgtdate2Mmdd10AsString(data.Substring(offset, Cgtdate2Mmdd10.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd10AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy10.PadRight(2));
        result.Append(_Cgtdate2Mmdd10.GetCgtdate2Mmdd10AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd10AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy10(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd10.SetCgtdate2Mmdd10AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd10.SetCgtdate2Mmdd10AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy10()
    {
        return _Cgtdate2Yy10;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy10(string value)
    {
        _Cgtdate2Yy10 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy10AsString()
    {
        return _Cgtdate2Yy10.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy10 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd10 GetCgtdate2Mmdd10()
    {
        return _Cgtdate2Mmdd10;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd10(Cgtdate2Mmdd10 value)
    {
        _Cgtdate2Mmdd10 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd10AsString()
    {
        return _Cgtdate2Mmdd10 != null ? _Cgtdate2Mmdd10.GetCgtdate2Mmdd10AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd10 == null)
        {
            _Cgtdate2Mmdd10 = new Cgtdate2Mmdd10();
        }
        _Cgtdate2Mmdd10.SetCgtdate2Mmdd10AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd10
    public class Cgtdate2Mmdd10
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm10, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm10 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd10, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd10 ="";
        
        
        
        
    public Cgtdate2Mmdd10() {}
    
    public Cgtdate2Mmdd10(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm10(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd10(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd10AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm10.PadRight(2));
        result.Append(_Cgtdate2Dd10.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd10AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm10(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd10(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm10()
    {
        return _Cgtdate2Mm10;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm10(string value)
    {
        _Cgtdate2Mm10 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm10AsString()
    {
        return _Cgtdate2Mm10.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm10 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd10()
    {
        return _Cgtdate2Dd10;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd10(string value)
    {
        _Cgtdate2Dd10 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd10AsString()
    {
        return _Cgtdate2Dd10.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd10 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller56(string value)
{
    _Filler56.SetFiller56AsString(value);
}
// Nested Class: Filler56
public class Filler56
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy10, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy10 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd10, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd10 =0;
    
    
    
    
public Filler56() {}

public Filler56(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy10(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd10(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller56AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy10.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd10.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller56AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy10(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd10(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy10()
{
    return _Cgtdate2CCcyy10;
}

// Standard Setter
public void SetCgtdate2CCcyy10(int value)
{
    _Cgtdate2CCcyy10 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy10AsString()
{
    return _Cgtdate2CCcyy10.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy10AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy10 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd10()
{
    return _Cgtdate2CMmdd10;
}

// Standard Setter
public void SetCgtdate2CMmdd10(int value)
{
    _Cgtdate2CMmdd10 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd10AsString()
{
    return _Cgtdate2CMmdd10.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd10AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd10 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller57(string value)
{
    _Filler57.SetFiller57AsString(value);
}
// Nested Class: Filler57
public class Filler57
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc10, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc10 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy10, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy10 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm10, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm10 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd10, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd10 =0;
    
    
    
    
public Filler57() {}

public Filler57(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc10(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy10(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm10(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd10(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller57AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc10.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy10.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm10.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd10.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller57AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc10(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy10(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm10(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd10(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc10()
{
    return _Cgtdate2CCc10;
}

// Standard Setter
public void SetCgtdate2CCc10(int value)
{
    _Cgtdate2CCc10 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc10AsString()
{
    return _Cgtdate2CCc10.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc10AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc10 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy10()
{
    return _Cgtdate2CYy10;
}

// Standard Setter
public void SetCgtdate2CYy10(int value)
{
    _Cgtdate2CYy10 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy10AsString()
{
    return _Cgtdate2CYy10.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy10AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy10 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm10()
{
    return _Cgtdate2CMm10;
}

// Standard Setter
public void SetCgtdate2CMm10(int value)
{
    _Cgtdate2CMm10 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm10AsString()
{
    return _Cgtdate2CMm10.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm10AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm10 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd10()
{
    return _Cgtdate2CDd10;
}

// Standard Setter
public void SetCgtdate2CDd10(int value)
{
    _Cgtdate2CDd10 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd10AsString()
{
    return _Cgtdate2CDd10.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd10AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd10 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller58(string value)
{
    _Filler58.SetFiller58AsString(value);
}
// Nested Class: Filler58
public class Filler58
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm10, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm10 =0;
    
    
    
    
    // [DEBUG] Field: Filler59, is_external=, is_static_class=False, static_prefix=
    private string _Filler59 ="";
    
    
    
    
public Filler58() {}

public Filler58(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm10(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller59(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller58AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm10.ToString().PadLeft(6, '0'));
    result.Append(_Filler59.PadRight(2));
    
    return result.ToString();
}

public void SetFiller58AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm10(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller59(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm10()
{
    return _Cgtdate2CCcyymm10;
}

// Standard Setter
public void SetCgtdate2CCcyymm10(int value)
{
    _Cgtdate2CCcyymm10 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm10AsString()
{
    return _Cgtdate2CCcyymm10.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm10AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm10 = parsed;
}

// Standard Getter
public string GetFiller59()
{
    return _Filler59;
}

// Standard Setter
public void SetFiller59(string value)
{
    _Filler59 = value;
}

// Get<>AsString()
public string GetFiller59AsString()
{
    return _Filler59.PadRight(2);
}

// Set<>AsString()
public void SetFiller59AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler59 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}