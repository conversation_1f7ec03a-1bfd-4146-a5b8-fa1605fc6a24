using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing WPrintRecord Data Structure

public class WPrintRecord
{
    private static int _size = 218;
    // [DEBUG] Class: WPrintRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WPrintChar, is_external=, is_static_class=False, static_prefix=
    private string[] _WPrintChar = new string[218];
    
    
    
    
    
    // Serialization methods
    public string GetWPrintRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 218; i++)
        {
            result.Append(_WPrintChar[i].PadRight(1));
        }
        
        return result.ToString();
    }
    
    public void SetWPrintRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 218; i++)
        {
            if (offset + 1 > data.Length) break;
            string val = data.Substring(offset, 1);
            
            _WPrintChar[i] = val.Trim();
            offset += 1;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWPrintRecordAsString();
    }
    // Set<>String Override function
    public void SetWPrintRecord(string value)
    {
        SetWPrintRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Array Accessors for WPrintChar
    public string GetWPrintCharAt(int index)
    {
        return _WPrintChar[index];
    }
    
    public void SetWPrintCharAt(int index, string value)
    {
        _WPrintChar[index] = value;
    }
    
    public string GetWPrintCharAsStringAt(int index)
    {
        return _WPrintChar[index].PadRight(1);
    }
    
    public void SetWPrintCharAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _WPrintChar[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetWPrintChar()
    {
        return _WPrintChar != null && _WPrintChar.Length > 0
        ? _WPrintChar[0]
        : default(string);
    }
    
    public void SetWPrintChar(string value)
    {
        if (_WPrintChar == null || _WPrintChar.Length == 0)
        _WPrintChar = new string[1];
        _WPrintChar[0] = value;
    }
    
    public string GetWPrintCharAsString()
    {
        return _WPrintChar != null && _WPrintChar.Length > 0
        ? _WPrintChar[0].ToString()
        : string.Empty;
    }
    
    public void SetWPrintCharAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_WPrintChar == null || _WPrintChar.Length == 0)
        _WPrintChar = new string[1];
        
        _WPrintChar[0] = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}