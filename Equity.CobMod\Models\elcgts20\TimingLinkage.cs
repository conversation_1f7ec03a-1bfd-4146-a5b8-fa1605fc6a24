using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing TimingLinkage Data Structure

public class TimingLinkage
{
    private static int _size = 380;
    // [DEBUG] Class: TimingLinkage, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: TimingAction, is_external=, is_static_class=False, static_prefix=
    private int _TimingAction =0;
    
    
    // 88-level condition checks for TimingAction
    public bool IsTimingBeginSection()
    {
        if (this._TimingAction == 1) return true;
        return false;
    }
    public bool IsTimingEndSection()
    {
        if (this._TimingAction == 2) return true;
        return false;
    }
    
    
    // [DEBUG] Field: TimingSection, is_external=, is_static_class=False, static_prefix=
    private int _TimingSection =0;
    
    
    // 88-level condition checks for TimingSection
    public bool IsTimingSectionJobRunner()
    {
        if (this._TimingSection == 1) return true;
        return false;
    }
    public bool IsTimingSectionCobolRouter()
    {
        if (this._TimingSection == 2) return true;
        return false;
    }
    public bool IsTimingSectionCobolComp()
    {
        if (this._TimingSection == 3) return true;
        return false;
    }
    public bool IsTimingSectionCobolScheduleIo()
    {
        if (this._TimingSection == 4) return true;
        return false;
    }
    public bool IsTimingSectionCobolErrorDataIo()
    {
        if (this._TimingSection == 5) return true;
        return false;
    }
    public bool IsTimingSectionCobolLogIo()
    {
        if (this._TimingSection == 6) return true;
        return false;
    }
    public bool IsTimingSectionDataTrans()
    {
        if (this._TimingSection == 7) return true;
        return false;
    }
    public bool IsTimingSectionCobolStrings()
    {
        if (this._TimingSection == 8) return true;
        return false;
    }
    public bool IsTimingSectionCobolDiskTempIo()
    {
        if (this._TimingSection == 9) return true;
        return false;
    }
    public bool IsTimingSectionCsharpTempIo()
    {
        if (this._TimingSection == 10) return true;
        return false;
    }
    public bool IsTimingSectionCobolMemoryTempIo()
    {
        if (this._TimingSection == 11) return true;
        return false;
    }
    public bool IsTimingSectionCobolOtherIo()
    {
        if (this._TimingSection == 12) return true;
        return false;
    }
    
    
    // [DEBUG] Field: TimingNoOfSections, is_external=, is_static_class=False, static_prefix=
    private int _TimingNoOfSections =12;
    
    
    
    
    // [DEBUG] Field: TimingDescriptions, is_external=, is_static_class=False, static_prefix=
    private TimingDescriptions _TimingDescriptions = new TimingDescriptions();
    
    
    
    
    // [DEBUG] Field: Filler499, is_external=, is_static_class=False, static_prefix=
    private Filler499 _Filler499 = new Filler499();
    
    
    
    
    // [DEBUG] Field: TimingMessageSuffix, is_external=, is_static_class=False, static_prefix=
    private string _TimingMessageSuffix ="";
    
    
    
    
    
    // Serialization methods
    public string GetTimingLinkageAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_TimingAction.ToString().PadLeft(1, '0'));
        result.Append(_TimingSection.ToString().PadLeft(2, '0'));
        result.Append(_TimingNoOfSections.ToString().PadLeft(2, '0'));
        result.Append(_TimingDescriptions.GetTimingDescriptionsAsString());
        result.Append(_Filler499.GetFiller499AsString());
        result.Append(_TimingMessageSuffix.PadRight(50));
        
        return result.ToString();
    }
    
    public void SetTimingLinkageAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetTimingAction(parsedInt);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetTimingSection(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetTimingNoOfSections(parsedInt);
        }
        offset += 2;
        if (offset + 300 <= data.Length)
        {
            _TimingDescriptions.SetTimingDescriptionsAsString(data.Substring(offset, 300));
        }
        else
        {
            _TimingDescriptions.SetTimingDescriptionsAsString(data.Substring(offset));
        }
        offset += 300;
        if (offset + 25 <= data.Length)
        {
            _Filler499.SetFiller499AsString(data.Substring(offset, 25));
        }
        else
        {
            _Filler499.SetFiller499AsString(data.Substring(offset));
        }
        offset += 25;
        if (offset + 50 <= data.Length)
        {
            string extracted = data.Substring(offset, 50).Trim();
            SetTimingMessageSuffix(extracted);
        }
        offset += 50;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetTimingLinkageAsString();
    }
    // Set<>String Override function
    public void SetTimingLinkage(string value)
    {
        SetTimingLinkageAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetTimingAction()
    {
        return _TimingAction;
    }
    
    // Standard Setter
    public void SetTimingAction(int value)
    {
        _TimingAction = value;
    }
    
    // Get<>AsString()
    public string GetTimingActionAsString()
    {
        return _TimingAction.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetTimingActionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _TimingAction = parsed;
    }
    
    // Standard Getter
    public int GetTimingSection()
    {
        return _TimingSection;
    }
    
    // Standard Setter
    public void SetTimingSection(int value)
    {
        _TimingSection = value;
    }
    
    // Get<>AsString()
    public string GetTimingSectionAsString()
    {
        return _TimingSection.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetTimingSectionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _TimingSection = parsed;
    }
    
    // Standard Getter
    public int GetTimingNoOfSections()
    {
        return _TimingNoOfSections;
    }
    
    // Standard Setter
    public void SetTimingNoOfSections(int value)
    {
        _TimingNoOfSections = value;
    }
    
    // Get<>AsString()
    public string GetTimingNoOfSectionsAsString()
    {
        return _TimingNoOfSections.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetTimingNoOfSectionsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _TimingNoOfSections = parsed;
    }
    
    // Standard Getter
    public TimingDescriptions GetTimingDescriptions()
    {
        return _TimingDescriptions;
    }
    
    // Standard Setter
    public void SetTimingDescriptions(TimingDescriptions value)
    {
        _TimingDescriptions = value;
    }
    
    // Get<>AsString()
    public string GetTimingDescriptionsAsString()
    {
        return _TimingDescriptions != null ? _TimingDescriptions.GetTimingDescriptionsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetTimingDescriptionsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_TimingDescriptions == null)
        {
            _TimingDescriptions = new TimingDescriptions();
        }
        _TimingDescriptions.SetTimingDescriptionsAsString(value);
    }
    
    // Standard Getter
    public Filler499 GetFiller499()
    {
        return _Filler499;
    }
    
    // Standard Setter
    public void SetFiller499(Filler499 value)
    {
        _Filler499 = value;
    }
    
    // Get<>AsString()
    public string GetFiller499AsString()
    {
        return _Filler499 != null ? _Filler499.GetFiller499AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller499AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler499 == null)
        {
            _Filler499 = new Filler499();
        }
        _Filler499.SetFiller499AsString(value);
    }
    
    // Standard Getter
    public string GetTimingMessageSuffix()
    {
        return _TimingMessageSuffix;
    }
    
    // Standard Setter
    public void SetTimingMessageSuffix(string value)
    {
        _TimingMessageSuffix = value;
    }
    
    // Get<>AsString()
    public string GetTimingMessageSuffixAsString()
    {
        return _TimingMessageSuffix.PadRight(50);
    }
    
    // Set<>AsString()
    public void SetTimingMessageSuffixAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _TimingMessageSuffix = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetTimingDescriptions(string value)
    {
        _TimingDescriptions.SetTimingDescriptionsAsString(value);
    }
    // Nested Class: TimingDescriptions
    public class TimingDescriptions
    {
        private static int _size = 300;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler487, is_external=, is_static_class=False, static_prefix=
        private string _Filler487 ="Job Runner";
        
        
        
        
        // [DEBUG] Field: Filler488, is_external=, is_static_class=False, static_prefix=
        private string _Filler488 ="Cobol Router";
        
        
        
        
        // [DEBUG] Field: Filler489, is_external=, is_static_class=False, static_prefix=
        private string _Filler489 ="Cobol Comp";
        
        
        
        
        // [DEBUG] Field: Filler490, is_external=, is_static_class=False, static_prefix=
        private string _Filler490 ="Cobol Schedule IO";
        
        
        
        
        // [DEBUG] Field: Filler491, is_external=, is_static_class=False, static_prefix=
        private string _Filler491 ="Cobol Error IO";
        
        
        
        
        // [DEBUG] Field: Filler492, is_external=, is_static_class=False, static_prefix=
        private string _Filler492 ="Cobol Log IO";
        
        
        
        
        // [DEBUG] Field: Filler493, is_external=, is_static_class=False, static_prefix=
        private string _Filler493 ="Data Transformation";
        
        
        
        
        // [DEBUG] Field: Filler494, is_external=, is_static_class=False, static_prefix=
        private string _Filler494 ="Cobol String Handling";
        
        
        
        
        // [DEBUG] Field: Filler495, is_external=, is_static_class=False, static_prefix=
        private string _Filler495 ="Cobol TempFile Disk IO";
        
        
        
        
        // [DEBUG] Field: Filler496, is_external=, is_static_class=False, static_prefix=
        private string _Filler496 ="C# TempFile Handling";
        
        
        
        
        // [DEBUG] Field: Filler497, is_external=, is_static_class=False, static_prefix=
        private string _Filler497 ="Cobol TempFile Handling";
        
        
        
        
        // [DEBUG] Field: Filler498, is_external=, is_static_class=False, static_prefix=
        private string _Filler498 ="Cobol Other IO";
        
        
        
        
    public TimingDescriptions() {}
    
    public TimingDescriptions(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller487(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller488(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller489(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller490(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller491(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller492(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller493(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller494(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller495(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller496(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller497(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller498(data.Substring(offset, 25).Trim());
        offset += 25;
        
    }
    
    // Serialization methods
    public string GetTimingDescriptionsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler487.PadRight(25));
        result.Append(_Filler488.PadRight(25));
        result.Append(_Filler489.PadRight(25));
        result.Append(_Filler490.PadRight(25));
        result.Append(_Filler491.PadRight(25));
        result.Append(_Filler492.PadRight(25));
        result.Append(_Filler493.PadRight(25));
        result.Append(_Filler494.PadRight(25));
        result.Append(_Filler495.PadRight(25));
        result.Append(_Filler496.PadRight(25));
        result.Append(_Filler497.PadRight(25));
        result.Append(_Filler498.PadRight(25));
        
        return result.ToString();
    }
    
    public void SetTimingDescriptionsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller487(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller488(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller489(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller490(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller491(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller492(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller493(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller494(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller495(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller496(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller497(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller498(extracted);
        }
        offset += 25;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller487()
    {
        return _Filler487;
    }
    
    // Standard Setter
    public void SetFiller487(string value)
    {
        _Filler487 = value;
    }
    
    // Get<>AsString()
    public string GetFiller487AsString()
    {
        return _Filler487.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller487AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler487 = value;
    }
    
    // Standard Getter
    public string GetFiller488()
    {
        return _Filler488;
    }
    
    // Standard Setter
    public void SetFiller488(string value)
    {
        _Filler488 = value;
    }
    
    // Get<>AsString()
    public string GetFiller488AsString()
    {
        return _Filler488.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller488AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler488 = value;
    }
    
    // Standard Getter
    public string GetFiller489()
    {
        return _Filler489;
    }
    
    // Standard Setter
    public void SetFiller489(string value)
    {
        _Filler489 = value;
    }
    
    // Get<>AsString()
    public string GetFiller489AsString()
    {
        return _Filler489.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller489AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler489 = value;
    }
    
    // Standard Getter
    public string GetFiller490()
    {
        return _Filler490;
    }
    
    // Standard Setter
    public void SetFiller490(string value)
    {
        _Filler490 = value;
    }
    
    // Get<>AsString()
    public string GetFiller490AsString()
    {
        return _Filler490.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller490AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler490 = value;
    }
    
    // Standard Getter
    public string GetFiller491()
    {
        return _Filler491;
    }
    
    // Standard Setter
    public void SetFiller491(string value)
    {
        _Filler491 = value;
    }
    
    // Get<>AsString()
    public string GetFiller491AsString()
    {
        return _Filler491.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller491AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler491 = value;
    }
    
    // Standard Getter
    public string GetFiller492()
    {
        return _Filler492;
    }
    
    // Standard Setter
    public void SetFiller492(string value)
    {
        _Filler492 = value;
    }
    
    // Get<>AsString()
    public string GetFiller492AsString()
    {
        return _Filler492.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller492AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler492 = value;
    }
    
    // Standard Getter
    public string GetFiller493()
    {
        return _Filler493;
    }
    
    // Standard Setter
    public void SetFiller493(string value)
    {
        _Filler493 = value;
    }
    
    // Get<>AsString()
    public string GetFiller493AsString()
    {
        return _Filler493.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller493AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler493 = value;
    }
    
    // Standard Getter
    public string GetFiller494()
    {
        return _Filler494;
    }
    
    // Standard Setter
    public void SetFiller494(string value)
    {
        _Filler494 = value;
    }
    
    // Get<>AsString()
    public string GetFiller494AsString()
    {
        return _Filler494.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller494AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler494 = value;
    }
    
    // Standard Getter
    public string GetFiller495()
    {
        return _Filler495;
    }
    
    // Standard Setter
    public void SetFiller495(string value)
    {
        _Filler495 = value;
    }
    
    // Get<>AsString()
    public string GetFiller495AsString()
    {
        return _Filler495.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller495AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler495 = value;
    }
    
    // Standard Getter
    public string GetFiller496()
    {
        return _Filler496;
    }
    
    // Standard Setter
    public void SetFiller496(string value)
    {
        _Filler496 = value;
    }
    
    // Get<>AsString()
    public string GetFiller496AsString()
    {
        return _Filler496.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller496AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler496 = value;
    }
    
    // Standard Getter
    public string GetFiller497()
    {
        return _Filler497;
    }
    
    // Standard Setter
    public void SetFiller497(string value)
    {
        _Filler497 = value;
    }
    
    // Get<>AsString()
    public string GetFiller497AsString()
    {
        return _Filler497.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller497AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler497 = value;
    }
    
    // Standard Getter
    public string GetFiller498()
    {
        return _Filler498;
    }
    
    // Standard Setter
    public void SetFiller498(string value)
    {
        _Filler498 = value;
    }
    
    // Get<>AsString()
    public string GetFiller498AsString()
    {
        return _Filler498.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller498AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler498 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetFiller499(string value)
{
    _Filler499.SetFiller499AsString(value);
}
// Nested Class: Filler499
public class Filler499
{
    private static int _size = 25;
    
    // Fields in the class
    
    
    // [DEBUG] Field: TimingElement, is_external=, is_static_class=False, static_prefix=
    private string[] _TimingElement = new string[12];
    
    
    
    
public Filler499() {}

public Filler499(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    for (int i = 0; i < 12; i++)
    {
        string value = data.Substring(offset, 25);
        _TimingElement[i] = value.Trim();
        offset += 25;
    }
    
}

// Serialization methods
public string GetFiller499AsString()
{
    StringBuilder result = new StringBuilder();
    
    for (int i = 0; i < 12; i++)
    {
        result.Append(_TimingElement[i].PadRight(25));
    }
    
    return result.ToString();
}

public void SetFiller499AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    for (int i = 0; i < 12; i++)
    {
        if (offset + 25 > data.Length) break;
        string val = data.Substring(offset, 25);
        
        _TimingElement[i] = val.Trim();
        offset += 25;
    }
}

// Getter and Setter methods

// Array Accessors for TimingElement
public string GetTimingElementAt(int index)
{
    return _TimingElement[index];
}

public void SetTimingElementAt(int index, string value)
{
    _TimingElement[index] = value;
}

public string GetTimingElementAsStringAt(int index)
{
    return _TimingElement[index].PadRight(25);
}

public void SetTimingElementAsStringAt(int index, string value)
{
    if (string.IsNullOrEmpty(value)) return;
    _TimingElement[index] = value;
}

// Flattened accessors (index 0)
public string GetTimingElement()
{
    return _TimingElement != null && _TimingElement.Length > 0
    ? _TimingElement[0]
    : default(string);
}

public void SetTimingElement(string value)
{
    if (_TimingElement == null || _TimingElement.Length == 0)
    _TimingElement = new string[1];
    _TimingElement[0] = value;
}

public string GetTimingElementAsString()
{
    return _TimingElement != null && _TimingElement.Length > 0
    ? _TimingElement[0].ToString()
    : string.Empty;
}

public void SetTimingElementAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    if (_TimingElement == null || _TimingElement.Length == 0)
    _TimingElement = new string[1];
    
    _TimingElement[0] = value;
}




public static int GetSize()
{
    return _size;
}

}

}}
