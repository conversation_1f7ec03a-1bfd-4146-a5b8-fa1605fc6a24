using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt179HsDTO
{// DTO class representing Cgtdate2LinkageDate2 Data Structure

public class Cgtdate2LinkageDate2
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate2, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd2 =0;
    
    
    
    
    // [DEBUG] Field: Filler44, is_external=, is_static_class=False, static_prefix=
    private Filler44 _Filler44 = new Filler44();
    
    
    
    
    // [DEBUG] Field: Filler45, is_external=, is_static_class=False, static_prefix=
    private Filler45 _Filler45 = new Filler45();
    
    
    
    
    // [DEBUG] Field: Filler46, is_external=, is_static_class=False, static_prefix=
    private Filler46 _Filler46 = new Filler46();
    
    
    
    
    // [DEBUG] Field: Filler47, is_external=, is_static_class=False, static_prefix=
    private Filler47 _Filler47 = new Filler47();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd2.ToString().PadLeft(8, '0'));
        result.Append(_Filler44.GetFiller44AsString());
        result.Append(_Filler45.GetFiller45AsString());
        result.Append(_Filler46.GetFiller46AsString());
        result.Append(_Filler47.GetFiller47AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd2(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler44.SetFiller44AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler44.SetFiller44AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler45.SetFiller45AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler45.SetFiller45AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler46.SetFiller46AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler46.SetFiller46AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler47.SetFiller47AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler47.SetFiller47AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate2AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate2(string value)
    {
        SetCgtdate2LinkageDate2AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd2()
    {
        return _Cgtdate2Ccyymmdd2;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd2(int value)
    {
        _Cgtdate2Ccyymmdd2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd2AsString()
    {
        return _Cgtdate2Ccyymmdd2.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd2 = parsed;
    }
    
    // Standard Getter
    public Filler44 GetFiller44()
    {
        return _Filler44;
    }
    
    // Standard Setter
    public void SetFiller44(Filler44 value)
    {
        _Filler44 = value;
    }
    
    // Get<>AsString()
    public string GetFiller44AsString()
    {
        return _Filler44 != null ? _Filler44.GetFiller44AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller44AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler44 == null)
        {
            _Filler44 = new Filler44();
        }
        _Filler44.SetFiller44AsString(value);
    }
    
    // Standard Getter
    public Filler45 GetFiller45()
    {
        return _Filler45;
    }
    
    // Standard Setter
    public void SetFiller45(Filler45 value)
    {
        _Filler45 = value;
    }
    
    // Get<>AsString()
    public string GetFiller45AsString()
    {
        return _Filler45 != null ? _Filler45.GetFiller45AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller45AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler45 == null)
        {
            _Filler45 = new Filler45();
        }
        _Filler45.SetFiller45AsString(value);
    }
    
    // Standard Getter
    public Filler46 GetFiller46()
    {
        return _Filler46;
    }
    
    // Standard Setter
    public void SetFiller46(Filler46 value)
    {
        _Filler46 = value;
    }
    
    // Get<>AsString()
    public string GetFiller46AsString()
    {
        return _Filler46 != null ? _Filler46.GetFiller46AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller46AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler46 == null)
        {
            _Filler46 = new Filler46();
        }
        _Filler46.SetFiller46AsString(value);
    }
    
    // Standard Getter
    public Filler47 GetFiller47()
    {
        return _Filler47;
    }
    
    // Standard Setter
    public void SetFiller47(Filler47 value)
    {
        _Filler47 = value;
    }
    
    // Get<>AsString()
    public string GetFiller47AsString()
    {
        return _Filler47 != null ? _Filler47.GetFiller47AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller47AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler47 == null)
        {
            _Filler47 = new Filler47();
        }
        _Filler47.SetFiller47AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller44(string value)
    {
        _Filler44.SetFiller44AsString(value);
    }
    // Nested Class: Filler44
    public class Filler44
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc2, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc2 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd2, is_external=, is_static_class=False, static_prefix=
        private Filler44.Cgtdate2Yymmdd2 _Cgtdate2Yymmdd2 = new Filler44.Cgtdate2Yymmdd2();
        
        
        
        
    public Filler44() {}
    
    public Filler44(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc2(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd2.SetCgtdate2Yymmdd2AsString(data.Substring(offset, Cgtdate2Yymmdd2.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller44AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc2.PadRight(2));
        result.Append(_Cgtdate2Yymmdd2.GetCgtdate2Yymmdd2AsString());
        
        return result.ToString();
    }
    
    public void SetFiller44AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc2(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd2.SetCgtdate2Yymmdd2AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd2.SetCgtdate2Yymmdd2AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc2()
    {
        return _Cgtdate2Cc2;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc2(string value)
    {
        _Cgtdate2Cc2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc2AsString()
    {
        return _Cgtdate2Cc2.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc2 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd2 GetCgtdate2Yymmdd2()
    {
        return _Cgtdate2Yymmdd2;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd2(Cgtdate2Yymmdd2 value)
    {
        _Cgtdate2Yymmdd2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd2AsString()
    {
        return _Cgtdate2Yymmdd2 != null ? _Cgtdate2Yymmdd2.GetCgtdate2Yymmdd2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd2 == null)
        {
            _Cgtdate2Yymmdd2 = new Cgtdate2Yymmdd2();
        }
        _Cgtdate2Yymmdd2.SetCgtdate2Yymmdd2AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd2
    public class Cgtdate2Yymmdd2
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy2, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy2 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd2, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd2.Cgtdate2Mmdd2 _Cgtdate2Mmdd2 = new Cgtdate2Yymmdd2.Cgtdate2Mmdd2();
        
        
        
        
    public Cgtdate2Yymmdd2() {}
    
    public Cgtdate2Yymmdd2(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy2(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd2.SetCgtdate2Mmdd2AsString(data.Substring(offset, Cgtdate2Mmdd2.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy2.PadRight(2));
        result.Append(_Cgtdate2Mmdd2.GetCgtdate2Mmdd2AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy2(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd2.SetCgtdate2Mmdd2AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd2.SetCgtdate2Mmdd2AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy2()
    {
        return _Cgtdate2Yy2;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy2(string value)
    {
        _Cgtdate2Yy2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy2AsString()
    {
        return _Cgtdate2Yy2.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy2 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd2 GetCgtdate2Mmdd2()
    {
        return _Cgtdate2Mmdd2;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd2(Cgtdate2Mmdd2 value)
    {
        _Cgtdate2Mmdd2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd2AsString()
    {
        return _Cgtdate2Mmdd2 != null ? _Cgtdate2Mmdd2.GetCgtdate2Mmdd2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd2 == null)
        {
            _Cgtdate2Mmdd2 = new Cgtdate2Mmdd2();
        }
        _Cgtdate2Mmdd2.SetCgtdate2Mmdd2AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd2
    public class Cgtdate2Mmdd2
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm2, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm2 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd2, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd2 ="";
        
        
        
        
    public Cgtdate2Mmdd2() {}
    
    public Cgtdate2Mmdd2(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm2(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd2(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm2.PadRight(2));
        result.Append(_Cgtdate2Dd2.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm2(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd2(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm2()
    {
        return _Cgtdate2Mm2;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm2(string value)
    {
        _Cgtdate2Mm2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm2AsString()
    {
        return _Cgtdate2Mm2.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm2 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd2()
    {
        return _Cgtdate2Dd2;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd2(string value)
    {
        _Cgtdate2Dd2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd2AsString()
    {
        return _Cgtdate2Dd2.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd2 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller45(string value)
{
    _Filler45.SetFiller45AsString(value);
}
// Nested Class: Filler45
public class Filler45
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy2 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd2 =0;
    
    
    
    
public Filler45() {}

public Filler45(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy2(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd2(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller45AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy2.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd2.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller45AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy2(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd2(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy2()
{
    return _Cgtdate2CCcyy2;
}

// Standard Setter
public void SetCgtdate2CCcyy2(int value)
{
    _Cgtdate2CCcyy2 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy2AsString()
{
    return _Cgtdate2CCcyy2.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy2 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd2()
{
    return _Cgtdate2CMmdd2;
}

// Standard Setter
public void SetCgtdate2CMmdd2(int value)
{
    _Cgtdate2CMmdd2 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd2AsString()
{
    return _Cgtdate2CMmdd2.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd2 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller46(string value)
{
    _Filler46.SetFiller46AsString(value);
}
// Nested Class: Filler46
public class Filler46
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc2 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy2 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm2 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd2 =0;
    
    
    
    
public Filler46() {}

public Filler46(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc2(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy2(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm2(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd2(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller46AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc2.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy2.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm2.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd2.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller46AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc2(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy2(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm2(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd2(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc2()
{
    return _Cgtdate2CCc2;
}

// Standard Setter
public void SetCgtdate2CCc2(int value)
{
    _Cgtdate2CCc2 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc2AsString()
{
    return _Cgtdate2CCc2.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc2 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy2()
{
    return _Cgtdate2CYy2;
}

// Standard Setter
public void SetCgtdate2CYy2(int value)
{
    _Cgtdate2CYy2 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy2AsString()
{
    return _Cgtdate2CYy2.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy2 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm2()
{
    return _Cgtdate2CMm2;
}

// Standard Setter
public void SetCgtdate2CMm2(int value)
{
    _Cgtdate2CMm2 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm2AsString()
{
    return _Cgtdate2CMm2.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm2 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd2()
{
    return _Cgtdate2CDd2;
}

// Standard Setter
public void SetCgtdate2CDd2(int value)
{
    _Cgtdate2CDd2 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd2AsString()
{
    return _Cgtdate2CDd2.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd2 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller47(string value)
{
    _Filler47.SetFiller47AsString(value);
}
// Nested Class: Filler47
public class Filler47
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm2 =0;
    
    
    
    
    // [DEBUG] Field: Filler48, is_external=, is_static_class=False, static_prefix=
    private string _Filler48 ="";
    
    
    
    
public Filler47() {}

public Filler47(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm2(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller48(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller47AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm2.ToString().PadLeft(6, '0'));
    result.Append(_Filler48.PadRight(2));
    
    return result.ToString();
}

public void SetFiller47AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm2(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller48(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm2()
{
    return _Cgtdate2CCcyymm2;
}

// Standard Setter
public void SetCgtdate2CCcyymm2(int value)
{
    _Cgtdate2CCcyymm2 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm2AsString()
{
    return _Cgtdate2CCcyymm2.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm2 = parsed;
}

// Standard Getter
public string GetFiller48()
{
    return _Filler48;
}

// Standard Setter
public void SetFiller48(string value)
{
    _Filler48 = value;
}

// Get<>AsString()
public string GetFiller48AsString()
{
    return _Filler48.PadRight(2);
}

// Set<>AsString()
public void SetFiller48AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler48 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}