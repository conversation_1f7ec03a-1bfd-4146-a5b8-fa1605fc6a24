﻿using System;
using System.Collections.Generic;
using System.Text;
using Legacy4.Equity.CobMod.Models;
using WK.UK.CCH.Equity.CobolDataTransformation;

namespace Legacy4.Equity.CobMod.Helper
{
    public class CheckCat
    {
        // Declare object reference for TransactionCategoryDA
        private TransactionCategoryDA oTransactionCategoryDA;

        // Fields for handling condition names (88 Values in COBOL)
        private bool FirstTime { get; set; } = true;
        private bool NotFirstTime { get; set; } = false;


        public void Execute(CheckCatLinkage checkCatLinkage)
        {
            // If it's the first time, initialize the TransactionCategoryDA object
            if (FirstTime)
            {
                oTransactionCategoryDA = new TransactionCategoryDA();  // Assuming "New" instantiates the object
                NotFirstTime = true;
                FirstTime = false;
            }

            // Logic for checking the status and invoking methods
            if (checkCatLinkage.CheckCatString.CheckCatStatus == CheckCatStatusEnum.ToGetDscr)
            {
                // Example: "GetParentDescriptionByTransactionCode" is invoked when status is ToGetDscr
                checkCatLinkage.CheckCatString.CheckCatCategoryDesc = oTransactionCategoryDA.GetParentDescriptionByTransactionCode(checkCatLinkage.CheckCatString.CheckCatCategoryCode);
            }
            else
            {
                // Example: "GetRowByID" is invoked when status is not ToGetDscr
                checkCatLinkage.CheckCatString.CheckCatCategoryCode = oTransactionCategoryDA.GetRowByID(checkCatLinkage.CheckCatTransactionID);
            }
        }
    }
}