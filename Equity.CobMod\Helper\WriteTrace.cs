﻿using System;
using System.Diagnostics;
using EquityProject.WriteTraceDTO; // For Debug.WriteLine and Debug.Close

/// <summary>
/// Provides tracing functionality, mimicking the COBOL WriteTrace program.
/// This class maintains internal state for trace activation.
/// </summary>
namespace EquityProject.WritetracePGM { 
    public class WriteTrace
    {
        // Corresponds to COBOL's w-trace-status (pic x value 'n').
        // This is internal state that persists across calls to *this instance* of the service.
        private bool _isTraceOn;

        // Corresponds to COBOL's oMessageString Object Reference StringClass.
        // In C#, we just use string directly.

        // Corresponds to COBOL's W-message pic x(220).
        // We'll build this string dynamically.

        // Corresponds to COBOL's w-date-time and WS-TIME-IN-HUNDREDTHS.
        // We'll use DateTime.Now and TimeSpan for this.

        /// <summary>
        /// Initializes a new instance of the <see cref="WriteTrace"/> class.
        /// Sets the initial trace status to off.
        /// </summary>
        public WriteTrace()
        {
            // COBOL: w-trace-status pic x value 'n'.
            _isTraceOn = false;
        }

        /// <summary>
        /// Processes a trace message or action, mimicking the PROCEDURE DIVISION of the COBOL WriteTrace program.
        /// </summary>
        /// <param name="parameters">
        /// An instance of <see cref="TraceMessageParameters"/> containing the trace message or action.
        /// </param>
        public void Execute(TraceMessageParameters parameters)
        {
            // COBOL: evaluate true
            switch (parameters.Action)
            {
                // COBOL: when start-trace
                case TraceAction.StartTrace:
                    // COBOL: invoke DebugClass "WriteLine" using oMessageString (before setting it)
                    // This first WriteLine in COBOL might be writing whatever was in oMessageString previously.
                    // Given the context, it's likely a blank or previous message. We'll omit it for clarity
                    // unless there's a specific requirement for it.
                    // If it's meant to write the *input* l-message before the "StartTrace," string:
                    // Debug.WriteLine(parameters.RawMessage);

                    // COBOL: set oMessageString to "StartTrace,"
                    // COBOL: invoke DebugClass "WriteLine" using oMessageString
                    Debug.WriteLine("StartTrace,");

                    // COBOL: set trace-on to true
                    _isTraceOn = true;
                    break;

                // COBOL: when end-trace
                case TraceAction.EndTrace:
                    // COBOL: set oMessageString to "EndTrace,"
                    // COBOL: invoke DebugClass "WriteLine" using oMessageString
                    Debug.WriteLine("EndTrace,");

                    // COBOL: invoke DebugClass "Close"
                    // In C#, Debug.Close() flushes the listeners. It's not always necessary
                    // as listeners often flush on their own or on application exit.
                    // However, to mimic COBOL, we include it.
                    Debug.Close();

                    // COBOL: set trace-off to true
                    _isTraceOn = false;
                    break;

                // COBOL: when other
                case TraceAction.Message:
                case TraceAction.Unknown: // Treat unknown actions as regular messages if trace is on
                    // COBOL: if trace-on
                    if (_isTraceOn)
                    {
                        // COBOL: move function current-date to w-date-time
                        // COBOL: compute WS-TIME-IN-HUNDREDTHS = ...
                        // In C#, we use DateTime.Now and TimeSpan.TotalMilliseconds or similar.
                        DateTime now = DateTime.Now;
                        // COBOL's WS-TIME-IN-HUNDREDTHS is total hundredths of seconds since midnight.
                        long totalHundredths = (long)now.TimeOfDay.TotalMilliseconds / 10;

                        // COBOL: STRING WS-TIME-IN-HUNDREDTHS ',' l-message DELIMITED BY SIZE INTO W-message
                        // COBOL: SET oMessageString TO W-message
                        // COBOL: invoke DebugClass "WriteLine" using oMessageString
                        string formattedMessage = $"{totalHundredths},{parameters.MessageContent}";

                        // COBOL's W-message is PIC X(220). We should ensure the output doesn't exceed this.
                        // If the combined string is longer than 220, truncate it.
                        if (formattedMessage.Length > 220)
                        {
                            formattedMessage = formattedMessage.Substring(0, 220);
                        }

                        Debug.WriteLine(formattedMessage);
                    }
                    break;
            }

            // COBOL: goback. (Method simply returns)
        }
    }
}