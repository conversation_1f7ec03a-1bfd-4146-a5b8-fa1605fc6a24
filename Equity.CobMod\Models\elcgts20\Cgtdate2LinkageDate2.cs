using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing Cgtdate2LinkageDate2 Data Structure

public class Cgtdate2LinkageDate2
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate2, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd2 =0;
    
    
    
    
    // [DEBUG] Field: Filler15, is_external=, is_static_class=False, static_prefix=
    private Filler15 _Filler15 = new Filler15();
    
    
    
    
    // [DEBUG] Field: Filler16, is_external=, is_static_class=False, static_prefix=
    private Filler16 _Filler16 = new Filler16();
    
    
    
    
    // [DEBUG] Field: Filler17, is_external=, is_static_class=False, static_prefix=
    private Filler17 _Filler17 = new Filler17();
    
    
    
    
    // [DEBUG] Field: Filler18, is_external=, is_static_class=False, static_prefix=
    private Filler18 _Filler18 = new Filler18();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd2.ToString().PadLeft(8, '0'));
        result.Append(_Filler15.GetFiller15AsString());
        result.Append(_Filler16.GetFiller16AsString());
        result.Append(_Filler17.GetFiller17AsString());
        result.Append(_Filler18.GetFiller18AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd2(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler15.SetFiller15AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler15.SetFiller15AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler16.SetFiller16AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler16.SetFiller16AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler17.SetFiller17AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler17.SetFiller17AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler18.SetFiller18AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler18.SetFiller18AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate2AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate2(string value)
    {
        SetCgtdate2LinkageDate2AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd2()
    {
        return _Cgtdate2Ccyymmdd2;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd2(int value)
    {
        _Cgtdate2Ccyymmdd2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd2AsString()
    {
        return _Cgtdate2Ccyymmdd2.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd2 = parsed;
    }
    
    // Standard Getter
    public Filler15 GetFiller15()
    {
        return _Filler15;
    }
    
    // Standard Setter
    public void SetFiller15(Filler15 value)
    {
        _Filler15 = value;
    }
    
    // Get<>AsString()
    public string GetFiller15AsString()
    {
        return _Filler15 != null ? _Filler15.GetFiller15AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller15AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler15 == null)
        {
            _Filler15 = new Filler15();
        }
        _Filler15.SetFiller15AsString(value);
    }
    
    // Standard Getter
    public Filler16 GetFiller16()
    {
        return _Filler16;
    }
    
    // Standard Setter
    public void SetFiller16(Filler16 value)
    {
        _Filler16 = value;
    }
    
    // Get<>AsString()
    public string GetFiller16AsString()
    {
        return _Filler16 != null ? _Filler16.GetFiller16AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller16AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler16 == null)
        {
            _Filler16 = new Filler16();
        }
        _Filler16.SetFiller16AsString(value);
    }
    
    // Standard Getter
    public Filler17 GetFiller17()
    {
        return _Filler17;
    }
    
    // Standard Setter
    public void SetFiller17(Filler17 value)
    {
        _Filler17 = value;
    }
    
    // Get<>AsString()
    public string GetFiller17AsString()
    {
        return _Filler17 != null ? _Filler17.GetFiller17AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller17AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler17 == null)
        {
            _Filler17 = new Filler17();
        }
        _Filler17.SetFiller17AsString(value);
    }
    
    // Standard Getter
    public Filler18 GetFiller18()
    {
        return _Filler18;
    }
    
    // Standard Setter
    public void SetFiller18(Filler18 value)
    {
        _Filler18 = value;
    }
    
    // Get<>AsString()
    public string GetFiller18AsString()
    {
        return _Filler18 != null ? _Filler18.GetFiller18AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller18AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler18 == null)
        {
            _Filler18 = new Filler18();
        }
        _Filler18.SetFiller18AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller15(string value)
    {
        _Filler15.SetFiller15AsString(value);
    }
    // Nested Class: Filler15
    public class Filler15
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc2, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc2 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd2, is_external=, is_static_class=False, static_prefix=
        private Filler15.Cgtdate2Yymmdd2 _Cgtdate2Yymmdd2 = new Filler15.Cgtdate2Yymmdd2();
        
        
        
        
    public Filler15() {}
    
    public Filler15(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc2(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd2.SetCgtdate2Yymmdd2AsString(data.Substring(offset, Cgtdate2Yymmdd2.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller15AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc2.PadRight(2));
        result.Append(_Cgtdate2Yymmdd2.GetCgtdate2Yymmdd2AsString());
        
        return result.ToString();
    }
    
    public void SetFiller15AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc2(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd2.SetCgtdate2Yymmdd2AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd2.SetCgtdate2Yymmdd2AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc2()
    {
        return _Cgtdate2Cc2;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc2(string value)
    {
        _Cgtdate2Cc2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc2AsString()
    {
        return _Cgtdate2Cc2.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc2 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd2 GetCgtdate2Yymmdd2()
    {
        return _Cgtdate2Yymmdd2;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd2(Cgtdate2Yymmdd2 value)
    {
        _Cgtdate2Yymmdd2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd2AsString()
    {
        return _Cgtdate2Yymmdd2 != null ? _Cgtdate2Yymmdd2.GetCgtdate2Yymmdd2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd2 == null)
        {
            _Cgtdate2Yymmdd2 = new Cgtdate2Yymmdd2();
        }
        _Cgtdate2Yymmdd2.SetCgtdate2Yymmdd2AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd2
    public class Cgtdate2Yymmdd2
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy2, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy2 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd2, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd2.Cgtdate2Mmdd2 _Cgtdate2Mmdd2 = new Cgtdate2Yymmdd2.Cgtdate2Mmdd2();
        
        
        
        
    public Cgtdate2Yymmdd2() {}
    
    public Cgtdate2Yymmdd2(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy2(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd2.SetCgtdate2Mmdd2AsString(data.Substring(offset, Cgtdate2Mmdd2.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy2.PadRight(2));
        result.Append(_Cgtdate2Mmdd2.GetCgtdate2Mmdd2AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy2(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd2.SetCgtdate2Mmdd2AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd2.SetCgtdate2Mmdd2AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy2()
    {
        return _Cgtdate2Yy2;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy2(string value)
    {
        _Cgtdate2Yy2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy2AsString()
    {
        return _Cgtdate2Yy2.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy2 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd2 GetCgtdate2Mmdd2()
    {
        return _Cgtdate2Mmdd2;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd2(Cgtdate2Mmdd2 value)
    {
        _Cgtdate2Mmdd2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd2AsString()
    {
        return _Cgtdate2Mmdd2 != null ? _Cgtdate2Mmdd2.GetCgtdate2Mmdd2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd2 == null)
        {
            _Cgtdate2Mmdd2 = new Cgtdate2Mmdd2();
        }
        _Cgtdate2Mmdd2.SetCgtdate2Mmdd2AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd2
    public class Cgtdate2Mmdd2
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm2, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm2 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd2, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd2 ="";
        
        
        
        
    public Cgtdate2Mmdd2() {}
    
    public Cgtdate2Mmdd2(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm2(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd2(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm2.PadRight(2));
        result.Append(_Cgtdate2Dd2.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm2(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd2(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm2()
    {
        return _Cgtdate2Mm2;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm2(string value)
    {
        _Cgtdate2Mm2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm2AsString()
    {
        return _Cgtdate2Mm2.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm2 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd2()
    {
        return _Cgtdate2Dd2;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd2(string value)
    {
        _Cgtdate2Dd2 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd2AsString()
    {
        return _Cgtdate2Dd2.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd2 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller16(string value)
{
    _Filler16.SetFiller16AsString(value);
}
// Nested Class: Filler16
public class Filler16
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy2 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd2 =0;
    
    
    
    
public Filler16() {}

public Filler16(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy2(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd2(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller16AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy2.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd2.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller16AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy2(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd2(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy2()
{
    return _Cgtdate2CCcyy2;
}

// Standard Setter
public void SetCgtdate2CCcyy2(int value)
{
    _Cgtdate2CCcyy2 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy2AsString()
{
    return _Cgtdate2CCcyy2.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy2 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd2()
{
    return _Cgtdate2CMmdd2;
}

// Standard Setter
public void SetCgtdate2CMmdd2(int value)
{
    _Cgtdate2CMmdd2 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd2AsString()
{
    return _Cgtdate2CMmdd2.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd2 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller17(string value)
{
    _Filler17.SetFiller17AsString(value);
}
// Nested Class: Filler17
public class Filler17
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc2 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy2 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm2 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd2 =0;
    
    
    
    
public Filler17() {}

public Filler17(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc2(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy2(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm2(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd2(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller17AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc2.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy2.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm2.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd2.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller17AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc2(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy2(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm2(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd2(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc2()
{
    return _Cgtdate2CCc2;
}

// Standard Setter
public void SetCgtdate2CCc2(int value)
{
    _Cgtdate2CCc2 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc2AsString()
{
    return _Cgtdate2CCc2.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc2 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy2()
{
    return _Cgtdate2CYy2;
}

// Standard Setter
public void SetCgtdate2CYy2(int value)
{
    _Cgtdate2CYy2 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy2AsString()
{
    return _Cgtdate2CYy2.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy2 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm2()
{
    return _Cgtdate2CMm2;
}

// Standard Setter
public void SetCgtdate2CMm2(int value)
{
    _Cgtdate2CMm2 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm2AsString()
{
    return _Cgtdate2CMm2.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm2 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd2()
{
    return _Cgtdate2CDd2;
}

// Standard Setter
public void SetCgtdate2CDd2(int value)
{
    _Cgtdate2CDd2 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd2AsString()
{
    return _Cgtdate2CDd2.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd2 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller18(string value)
{
    _Filler18.SetFiller18AsString(value);
}
// Nested Class: Filler18
public class Filler18
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm2, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm2 =0;
    
    
    
    
    // [DEBUG] Field: Filler19, is_external=, is_static_class=False, static_prefix=
    private string _Filler19 ="";
    
    
    
    
public Filler18() {}

public Filler18(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm2(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller19(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller18AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm2.ToString().PadLeft(6, '0'));
    result.Append(_Filler19.PadRight(2));
    
    return result.ToString();
}

public void SetFiller18AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm2(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller19(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm2()
{
    return _Cgtdate2CCcyymm2;
}

// Standard Setter
public void SetCgtdate2CCcyymm2(int value)
{
    _Cgtdate2CCcyymm2 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm2AsString()
{
    return _Cgtdate2CCcyymm2.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm2 = parsed;
}

// Standard Getter
public string GetFiller19()
{
    return _Filler19;
}

// Standard Setter
public void SetFiller19(string value)
{
    _Filler19 = value;
}

// Get<>AsString()
public string GetFiller19AsString()
{
    return _Filler19.PadRight(2);
}

// Set<>AsString()
public void SetFiller19AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler19 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}
