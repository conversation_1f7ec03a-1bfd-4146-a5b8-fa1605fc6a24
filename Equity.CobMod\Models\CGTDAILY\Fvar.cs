using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdailyDTO
{// <Section> Class for Fvar
public class Fvar
{
public Fvar() {}

// Fields in the class


// [DEBUG] Field: D171Record, is_external=, is_static_class=False, static_prefix=
private D171Record _D171Record = new D171Record();




// Getter and Setter methods

// Standard Getter
public D171Record GetD171Record()
{
    return _D171Record;
}

// Standard Setter
public void SetD171Record(D171Record value)
{
    _D171Record = value;
}

// Get<>AsString()
public string GetD171RecordAsString()
{
    return _D171Record != null ? _D171Record.GetD171RecordAsString() : "";
}

// Set<>AsString()
public void SetD171RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D171Record == null)
    {
        _D171Record = new D171Record();
    }
    _D171Record.SetD171RecordAsString(value);
}


}}