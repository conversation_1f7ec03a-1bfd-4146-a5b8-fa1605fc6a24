using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt179HsDTO
{// DTO class representing BalanceIds Data Structure

public class BalanceIds
{
    private static int _size = 81;
    // [DEBUG] Class: BalanceIds, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: MbdaBalanceId, is_external=, is_static_class=False, static_prefix=
    private string _MbdaBalanceId ="";
    
    
    
    
    // [DEBUG] Field: MbdaHoldingId, is_external=, is_static_class=False, static_prefix=
    private string _MbdaHoldingId ="";
    
    
    
    
    // [DEBUG] Field: MbdaParentStockId, is_external=, is_static_class=False, static_prefix=
    private string _MbdaParentStockId ="";
    
    
    
    
    // [DEBUG] Field: Filler105, is_external=, is_static_class=False, static_prefix=
    private string _Filler105 ="";
    
    
    
    
    // [DEBUG] Field: Filler106, is_external=, is_static_class=False, static_prefix=
    private string _Filler106 ="";
    
    
    
    
    // [DEBUG] Field: Filler107, is_external=, is_static_class=False, static_prefix=
    private string _Filler107 ="";
    
    
    
    
    // [DEBUG] Field: Filler108, is_external=, is_static_class=False, static_prefix=
    private string _Filler108 ="";
    
    
    
    
    // [DEBUG] Field: MbdaDbTimestamp, is_external=, is_static_class=False, static_prefix=
    private string _MbdaDbTimestamp ="";
    
    
    
    
    // [DEBUG] Field: Filler109, is_external=, is_static_class=False, static_prefix=
    private string _Filler109 ="";
    
    
    
    
    
    // Serialization methods
    public string GetBalanceIdsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_MbdaBalanceId.PadRight(8));
        result.Append(_MbdaHoldingId.PadRight(8));
        result.Append(_MbdaParentStockId.PadRight(8));
        result.Append(_Filler105.PadRight(8));
        result.Append(_Filler106.PadRight(8));
        result.Append(_Filler107.PadRight(8));
        result.Append(_Filler108.PadRight(8));
        result.Append(_MbdaDbTimestamp.PadRight(24));
        result.Append(_Filler109.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetBalanceIdsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMbdaBalanceId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMbdaHoldingId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMbdaParentStockId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller105(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller106(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller107(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller108(extracted);
        }
        offset += 8;
        if (offset + 24 <= data.Length)
        {
            string extracted = data.Substring(offset, 24).Trim();
            SetMbdaDbTimestamp(extracted);
        }
        offset += 24;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller109(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetBalanceIdsAsString();
    }
    // Set<>String Override function
    public void SetBalanceIds(string value)
    {
        SetBalanceIdsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetMbdaBalanceId()
    {
        return _MbdaBalanceId;
    }
    
    // Standard Setter
    public void SetMbdaBalanceId(string value)
    {
        _MbdaBalanceId = value;
    }
    
    // Get<>AsString()
    public string GetMbdaBalanceIdAsString()
    {
        return _MbdaBalanceId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMbdaBalanceIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MbdaBalanceId = value;
    }
    
    // Standard Getter
    public string GetMbdaHoldingId()
    {
        return _MbdaHoldingId;
    }
    
    // Standard Setter
    public void SetMbdaHoldingId(string value)
    {
        _MbdaHoldingId = value;
    }
    
    // Get<>AsString()
    public string GetMbdaHoldingIdAsString()
    {
        return _MbdaHoldingId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMbdaHoldingIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MbdaHoldingId = value;
    }
    
    // Standard Getter
    public string GetMbdaParentStockId()
    {
        return _MbdaParentStockId;
    }
    
    // Standard Setter
    public void SetMbdaParentStockId(string value)
    {
        _MbdaParentStockId = value;
    }
    
    // Get<>AsString()
    public string GetMbdaParentStockIdAsString()
    {
        return _MbdaParentStockId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMbdaParentStockIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MbdaParentStockId = value;
    }
    
    // Standard Getter
    public string GetFiller105()
    {
        return _Filler105;
    }
    
    // Standard Setter
    public void SetFiller105(string value)
    {
        _Filler105 = value;
    }
    
    // Get<>AsString()
    public string GetFiller105AsString()
    {
        return _Filler105.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller105AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler105 = value;
    }
    
    // Standard Getter
    public string GetFiller106()
    {
        return _Filler106;
    }
    
    // Standard Setter
    public void SetFiller106(string value)
    {
        _Filler106 = value;
    }
    
    // Get<>AsString()
    public string GetFiller106AsString()
    {
        return _Filler106.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller106AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler106 = value;
    }
    
    // Standard Getter
    public string GetFiller107()
    {
        return _Filler107;
    }
    
    // Standard Setter
    public void SetFiller107(string value)
    {
        _Filler107 = value;
    }
    
    // Get<>AsString()
    public string GetFiller107AsString()
    {
        return _Filler107.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller107AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler107 = value;
    }
    
    // Standard Getter
    public string GetFiller108()
    {
        return _Filler108;
    }
    
    // Standard Setter
    public void SetFiller108(string value)
    {
        _Filler108 = value;
    }
    
    // Get<>AsString()
    public string GetFiller108AsString()
    {
        return _Filler108.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller108AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler108 = value;
    }
    
    // Standard Getter
    public string GetMbdaDbTimestamp()
    {
        return _MbdaDbTimestamp;
    }
    
    // Standard Setter
    public void SetMbdaDbTimestamp(string value)
    {
        _MbdaDbTimestamp = value;
    }
    
    // Get<>AsString()
    public string GetMbdaDbTimestampAsString()
    {
        return _MbdaDbTimestamp.PadRight(24);
    }
    
    // Set<>AsString()
    public void SetMbdaDbTimestampAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MbdaDbTimestamp = value;
    }
    
    // Standard Getter
    public string GetFiller109()
    {
        return _Filler109;
    }
    
    // Standard Setter
    public void SetFiller109(string value)
    {
        _Filler109 = value;
    }
    
    // Get<>AsString()
    public string GetFiller109AsString()
    {
        return _Filler109.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller109AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler109 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}