using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing D43Record Data Structure

public class D43Record
{
    private static int _size = 32;
    // [DEBUG] Class: D43Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D43Key, is_external=, is_static_class=False, static_prefix=
    private D43Key _D43Key = new D43Key();
    
    
    
    
    // [DEBUG] Field: D43MarketPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _D43MarketPrice =0;
    
    
    
    
    // [DEBUG] Field: D43Currency, is_external=, is_static_class=False, static_prefix=
    private string _D43Currency ="";
    
    
    
    
    
    // Serialization methods
    public string GetD43RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D43Key.GetD43KeyAsString());
        result.Append(_D43MarketPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D43Currency.PadRight(3));
        
        return result.ToString();
    }
    
    public void SetD43RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 23 <= data.Length)
        {
            _D43Key.SetD43KeyAsString(data.Substring(offset, 23));
        }
        else
        {
            _D43Key.SetD43KeyAsString(data.Substring(offset));
        }
        offset += 23;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD43MarketPrice(parsedDec);
        }
        offset += 6;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD43Currency(extracted);
        }
        offset += 3;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD43RecordAsString();
    }
    // Set<>String Override function
    public void SetD43Record(string value)
    {
        SetD43RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D43Key GetD43Key()
    {
        return _D43Key;
    }
    
    // Standard Setter
    public void SetD43Key(D43Key value)
    {
        _D43Key = value;
    }
    
    // Get<>AsString()
    public string GetD43KeyAsString()
    {
        return _D43Key != null ? _D43Key.GetD43KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD43KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D43Key == null)
        {
            _D43Key = new D43Key();
        }
        _D43Key.SetD43KeyAsString(value);
    }
    
    // Standard Getter
    public decimal GetD43MarketPrice()
    {
        return _D43MarketPrice;
    }
    
    // Standard Setter
    public void SetD43MarketPrice(decimal value)
    {
        _D43MarketPrice = value;
    }
    
    // Get<>AsString()
    public string GetD43MarketPriceAsString()
    {
        return _D43MarketPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD43MarketPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D43MarketPrice = parsed;
    }
    
    // Standard Getter
    public string GetD43Currency()
    {
        return _D43Currency;
    }
    
    // Standard Setter
    public void SetD43Currency(string value)
    {
        _D43Currency = value;
    }
    
    // Get<>AsString()
    public string GetD43CurrencyAsString()
    {
        return _D43Currency.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD43CurrencyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D43Currency = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD43Key(string value)
    {
        _D43Key.SetD43KeyAsString(value);
    }
    // Nested Class: D43Key
    public class D43Key
    {
        private static int _size = 23;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D43SedolCode, is_external=, is_static_class=False, static_prefix=
        private string _D43SedolCode ="";
        
        
        
        
        // [DEBUG] Field: D3PriceDateR, is_external=, is_static_class=False, static_prefix=
        private D43Key.D3PriceDateR _D3PriceDateR = new D43Key.D3PriceDateR();
        
        
        
        
        // [DEBUG] Field: D43PriceDate, is_external=, is_static_class=False, static_prefix=
        private int _D43PriceDate =0;
        
        
        
        
        // [DEBUG] Field: D43SequenceCode, is_external=, is_static_class=False, static_prefix=
        private int _D43SequenceCode =0;
        
        
        
        
        // [DEBUG] Field: D43ExtelPrice, is_external=, is_static_class=False, static_prefix=
        private string _D43ExtelPrice ="";
        
        
        
        
    public D43Key() {}
    
    public D43Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD43SedolCode(data.Substring(offset, 7).Trim());
        offset += 7;
        _D3PriceDateR.SetD3PriceDateRAsString(data.Substring(offset, D3PriceDateR.GetSize()));
        offset += 6;
        SetD43PriceDate(int.Parse(data.Substring(offset, 6).Trim()));
        offset += 6;
        SetD43SequenceCode(int.Parse(data.Substring(offset, 3).Trim()));
        offset += 3;
        SetD43ExtelPrice(data.Substring(offset, 1).Trim());
        offset += 1;
        
    }
    
    // Serialization methods
    public string GetD43KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D43SedolCode.PadRight(7));
        result.Append(_D3PriceDateR.GetD3PriceDateRAsString());
        result.Append(_D43PriceDate.ToString().PadLeft(6, '0'));
        result.Append(_D43SequenceCode.ToString().PadLeft(3, '0'));
        result.Append(_D43ExtelPrice.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetD43KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetD43SedolCode(extracted);
        }
        offset += 7;
        if (offset + 6 <= data.Length)
        {
            _D3PriceDateR.SetD3PriceDateRAsString(data.Substring(offset, 6));
        }
        else
        {
            _D3PriceDateR.SetD3PriceDateRAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD43PriceDate(parsedInt);
        }
        offset += 6;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD43SequenceCode(parsedInt);
        }
        offset += 3;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD43ExtelPrice(extracted);
        }
        offset += 1;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD43SedolCode()
    {
        return _D43SedolCode;
    }
    
    // Standard Setter
    public void SetD43SedolCode(string value)
    {
        _D43SedolCode = value;
    }
    
    // Get<>AsString()
    public string GetD43SedolCodeAsString()
    {
        return _D43SedolCode.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetD43SedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D43SedolCode = value;
    }
    
    // Standard Getter
    public D3PriceDateR GetD3PriceDateR()
    {
        return _D3PriceDateR;
    }
    
    // Standard Setter
    public void SetD3PriceDateR(D3PriceDateR value)
    {
        _D3PriceDateR = value;
    }
    
    // Get<>AsString()
    public string GetD3PriceDateRAsString()
    {
        return _D3PriceDateR != null ? _D3PriceDateR.GetD3PriceDateRAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD3PriceDateRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D3PriceDateR == null)
        {
            _D3PriceDateR = new D3PriceDateR();
        }
        _D3PriceDateR.SetD3PriceDateRAsString(value);
    }
    
    // Standard Getter
    public int GetD43PriceDate()
    {
        return _D43PriceDate;
    }
    
    // Standard Setter
    public void SetD43PriceDate(int value)
    {
        _D43PriceDate = value;
    }
    
    // Get<>AsString()
    public string GetD43PriceDateAsString()
    {
        return _D43PriceDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD43PriceDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D43PriceDate = parsed;
    }
    
    // Standard Getter
    public int GetD43SequenceCode()
    {
        return _D43SequenceCode;
    }
    
    // Standard Setter
    public void SetD43SequenceCode(int value)
    {
        _D43SequenceCode = value;
    }
    
    // Get<>AsString()
    public string GetD43SequenceCodeAsString()
    {
        return _D43SequenceCode.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetD43SequenceCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D43SequenceCode = parsed;
    }
    
    // Standard Getter
    public string GetD43ExtelPrice()
    {
        return _D43ExtelPrice;
    }
    
    // Standard Setter
    public void SetD43ExtelPrice(string value)
    {
        _D43ExtelPrice = value;
    }
    
    // Get<>AsString()
    public string GetD43ExtelPriceAsString()
    {
        return _D43ExtelPrice.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD43ExtelPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D43ExtelPrice = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D3PriceDateR
    public class D3PriceDateR
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D3PriceDateYy, is_external=, is_static_class=False, static_prefix=
        private int _D3PriceDateYy =0;
        
        
        
        
        // [DEBUG] Field: D3PriceDateMm, is_external=, is_static_class=False, static_prefix=
        private int _D3PriceDateMm =0;
        
        
        
        
        // [DEBUG] Field: D3PriceDateDd, is_external=, is_static_class=False, static_prefix=
        private int _D3PriceDateDd =0;
        
        
        
        
    public D3PriceDateR() {}
    
    public D3PriceDateR(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD3PriceDateYy(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetD3PriceDateMm(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetD3PriceDateDd(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetD3PriceDateRAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D3PriceDateYy.ToString().PadLeft(2, '0'));
        result.Append(_D3PriceDateMm.ToString().PadLeft(2, '0'));
        result.Append(_D3PriceDateDd.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetD3PriceDateRAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD3PriceDateYy(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD3PriceDateMm(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD3PriceDateDd(parsedInt);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD3PriceDateYy()
    {
        return _D3PriceDateYy;
    }
    
    // Standard Setter
    public void SetD3PriceDateYy(int value)
    {
        _D3PriceDateYy = value;
    }
    
    // Get<>AsString()
    public string GetD3PriceDateYyAsString()
    {
        return _D3PriceDateYy.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD3PriceDateYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D3PriceDateYy = parsed;
    }
    
    // Standard Getter
    public int GetD3PriceDateMm()
    {
        return _D3PriceDateMm;
    }
    
    // Standard Setter
    public void SetD3PriceDateMm(int value)
    {
        _D3PriceDateMm = value;
    }
    
    // Get<>AsString()
    public string GetD3PriceDateMmAsString()
    {
        return _D3PriceDateMm.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD3PriceDateMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D3PriceDateMm = parsed;
    }
    
    // Standard Getter
    public int GetD3PriceDateDd()
    {
        return _D3PriceDateDd;
    }
    
    // Standard Setter
    public void SetD3PriceDateDd(int value)
    {
        _D3PriceDateDd = value;
    }
    
    // Get<>AsString()
    public string GetD3PriceDateDdAsString()
    {
        return _D3PriceDateDd.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD3PriceDateDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D3PriceDateDd = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}

}}
