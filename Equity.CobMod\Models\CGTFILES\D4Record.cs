using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D4Record Data Structure

public class D4Record
{
    private static int _size = 109;
    // [DEBUG] Class: D4Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D4Key, is_external=, is_static_class=False, static_prefix=
    private D4Key _D4Key = new D4Key();
    
    
    
    
    // [DEBUG] Field: D4FundName, is_external=, is_static_class=False, static_prefix=
    private string _D4FundName ="";
    
    
    
    
    // [DEBUG] Field: D4Key2, is_external=, is_static_class=False, static_prefix=
    private D4Key2 _D4Key2 = new D4Key2();
    
    
    
    
    // [DEBUG] Field: D4FundType, is_external=, is_static_class=False, static_prefix=
    private string _D4FundType ="";
    
    
    
    
    // [DEBUG] Field: D4Election1965Ord, is_external=, is_static_class=False, static_prefix=
    private string _D4Election1965Ord ="";
    
    
    
    
    // [DEBUG] Field: D4Election1965Fixed, is_external=, is_static_class=False, static_prefix=
    private string _D4Election1965Fixed ="";
    
    
    
    
    // [DEBUG] Field: D4Election1982, is_external=, is_static_class=False, static_prefix=
    private string _D4Election1982 ="";
    
    
    
    
    // [DEBUG] Field: D4PeriodStartDate, is_external=, is_static_class=False, static_prefix=
    private D4PeriodStartDate _D4PeriodStartDate = new D4PeriodStartDate();
    
    
    
    
    // [DEBUG] Field: D4PeriodEndDate, is_external=, is_static_class=False, static_prefix=
    private D4PeriodEndDate _D4PeriodEndDate = new D4PeriodEndDate();
    
    
    
    
    // [DEBUG] Field: D4UpdateCount, is_external=, is_static_class=False, static_prefix=
    private int _D4UpdateCount =0;
    
    
    
    
    // [DEBUG] Field: D4InverseKey, is_external=, is_static_class=False, static_prefix=
    private D4InverseKey _D4InverseKey = new D4InverseKey();
    
    
    
    
    // [DEBUG] Field: D4Key3, is_external=, is_static_class=False, static_prefix=
    private D4Key3 _D4Key3 = new D4Key3();
    
    
    
    
    // [DEBUG] Field: D4OlabFund, is_external=, is_static_class=False, static_prefix=
    private string _D4OlabFund ="";
    
    
    
    
    // [DEBUG] Field: D4LifeSummaryFund, is_external=, is_static_class=False, static_prefix=
    private string _D4LifeSummaryFund ="";
    
    
    
    
    // [DEBUG] Field: D4CalendarId, is_external=, is_static_class=False, static_prefix=
    private string _D4CalendarId ="";
    
    
    
    
    // [DEBUG] Field: D4UseBfLosses, is_external=, is_static_class=False, static_prefix=
    private string _D4UseBfLosses ="";
    
    
    
    
    // [DEBUG] Field: D4PriceTypeCode, is_external=, is_static_class=False, static_prefix=
    private string _D4PriceTypeCode ="";
    
    
    
    
    // [DEBUG] Field: D4UseEarlierPrice, is_external=, is_static_class=False, static_prefix=
    private string _D4UseEarlierPrice ="";
    
    
    
    
    // [DEBUG] Field: D4PercentageAllowance, is_external=, is_static_class=False, static_prefix=
    private string _D4PercentageAllowance ="";
    
    
    
    
    // [DEBUG] Field: D4PercentageAllowance9, is_external=, is_static_class=False, static_prefix=
    private decimal _D4PercentageAllowance9 =0;
    
    
    
    
    // [DEBUG] Field: D4Filler, is_external=, is_static_class=False, static_prefix=
    private string _D4Filler ="";
    
    
    
    
    
    // Serialization methods
    public string GetD4RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D4Key.GetD4KeyAsString());
        result.Append(_D4FundName.PadRight(30));
        result.Append(_D4Key2.GetD4Key2AsString());
        result.Append(_D4FundType.PadRight(1));
        result.Append(_D4Election1965Ord.PadRight(1));
        result.Append(_D4Election1965Fixed.PadRight(1));
        result.Append(_D4Election1982.PadRight(1));
        result.Append(_D4PeriodStartDate.GetD4PeriodStartDateAsString());
        result.Append(_D4PeriodEndDate.GetD4PeriodEndDateAsString());
        result.Append(_D4UpdateCount.ToString().PadLeft(4, '0'));
        result.Append(_D4InverseKey.GetD4InverseKeyAsString());
        result.Append(_D4Key3.GetD4Key3AsString());
        result.Append(_D4OlabFund.PadRight(1));
        result.Append(_D4LifeSummaryFund.PadRight(1));
        result.Append(_D4CalendarId.PadRight(4));
        result.Append(_D4UseBfLosses.PadRight(1));
        result.Append(_D4PriceTypeCode.PadRight(0));
        result.Append(_D4UseEarlierPrice.PadRight(0));
        result.Append(_D4PercentageAllowance.PadRight(0));
        result.Append(_D4PercentageAllowance9.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D4Filler.PadRight(36));
        
        return result.ToString();
    }
    
    public void SetD4RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            _D4Key.SetD4KeyAsString(data.Substring(offset, 4));
        }
        else
        {
            _D4Key.SetD4KeyAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 30 <= data.Length)
        {
            string extracted = data.Substring(offset, 30).Trim();
            SetD4FundName(extracted);
        }
        offset += 30;
        if (offset + 4 <= data.Length)
        {
            _D4Key2.SetD4Key2AsString(data.Substring(offset, 4));
        }
        else
        {
            _D4Key2.SetD4Key2AsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD4FundType(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD4Election1965Ord(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD4Election1965Fixed(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD4Election1982(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            _D4PeriodStartDate.SetD4PeriodStartDateAsString(data.Substring(offset, 4));
        }
        else
        {
            _D4PeriodStartDate.SetD4PeriodStartDateAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            _D4PeriodEndDate.SetD4PeriodEndDateAsString(data.Substring(offset, 4));
        }
        else
        {
            _D4PeriodEndDate.SetD4PeriodEndDateAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD4UpdateCount(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            _D4InverseKey.SetD4InverseKeyAsString(data.Substring(offset, 4));
        }
        else
        {
            _D4InverseKey.SetD4InverseKeyAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            _D4Key3.SetD4Key3AsString(data.Substring(offset, 4));
        }
        else
        {
            _D4Key3.SetD4Key3AsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD4OlabFund(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD4LifeSummaryFund(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD4CalendarId(extracted);
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD4UseBfLosses(extracted);
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD4PriceTypeCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD4UseEarlierPrice(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD4PercentageAllowance(extracted);
        }
        offset += 0;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD4PercentageAllowance9(parsedDec);
        }
        offset += 4;
        if (offset + 36 <= data.Length)
        {
            string extracted = data.Substring(offset, 36).Trim();
            SetD4Filler(extracted);
        }
        offset += 36;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD4RecordAsString();
    }
    // Set<>String Override function
    public void SetD4Record(string value)
    {
        SetD4RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D4Key GetD4Key()
    {
        return _D4Key;
    }
    
    // Standard Setter
    public void SetD4Key(D4Key value)
    {
        _D4Key = value;
    }
    
    // Get<>AsString()
    public string GetD4KeyAsString()
    {
        return _D4Key != null ? _D4Key.GetD4KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD4KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D4Key == null)
        {
            _D4Key = new D4Key();
        }
        _D4Key.SetD4KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD4FundName()
    {
        return _D4FundName;
    }
    
    // Standard Setter
    public void SetD4FundName(string value)
    {
        _D4FundName = value;
    }
    
    // Get<>AsString()
    public string GetD4FundNameAsString()
    {
        return _D4FundName.PadRight(30);
    }
    
    // Set<>AsString()
    public void SetD4FundNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D4FundName = value;
    }
    
    // Standard Getter
    public D4Key2 GetD4Key2()
    {
        return _D4Key2;
    }
    
    // Standard Setter
    public void SetD4Key2(D4Key2 value)
    {
        _D4Key2 = value;
    }
    
    // Get<>AsString()
    public string GetD4Key2AsString()
    {
        return _D4Key2 != null ? _D4Key2.GetD4Key2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD4Key2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D4Key2 == null)
        {
            _D4Key2 = new D4Key2();
        }
        _D4Key2.SetD4Key2AsString(value);
    }
    
    // Standard Getter
    public string GetD4FundType()
    {
        return _D4FundType;
    }
    
    // Standard Setter
    public void SetD4FundType(string value)
    {
        _D4FundType = value;
    }
    
    // Get<>AsString()
    public string GetD4FundTypeAsString()
    {
        return _D4FundType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD4FundTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D4FundType = value;
    }
    
    // Standard Getter
    public string GetD4Election1965Ord()
    {
        return _D4Election1965Ord;
    }
    
    // Standard Setter
    public void SetD4Election1965Ord(string value)
    {
        _D4Election1965Ord = value;
    }
    
    // Get<>AsString()
    public string GetD4Election1965OrdAsString()
    {
        return _D4Election1965Ord.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD4Election1965OrdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D4Election1965Ord = value;
    }
    
    // Standard Getter
    public string GetD4Election1965Fixed()
    {
        return _D4Election1965Fixed;
    }
    
    // Standard Setter
    public void SetD4Election1965Fixed(string value)
    {
        _D4Election1965Fixed = value;
    }
    
    // Get<>AsString()
    public string GetD4Election1965FixedAsString()
    {
        return _D4Election1965Fixed.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD4Election1965FixedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D4Election1965Fixed = value;
    }
    
    // Standard Getter
    public string GetD4Election1982()
    {
        return _D4Election1982;
    }
    
    // Standard Setter
    public void SetD4Election1982(string value)
    {
        _D4Election1982 = value;
    }
    
    // Get<>AsString()
    public string GetD4Election1982AsString()
    {
        return _D4Election1982.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD4Election1982AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D4Election1982 = value;
    }
    
    // Standard Getter
    public D4PeriodStartDate GetD4PeriodStartDate()
    {
        return _D4PeriodStartDate;
    }
    
    // Standard Setter
    public void SetD4PeriodStartDate(D4PeriodStartDate value)
    {
        _D4PeriodStartDate = value;
    }
    
    // Get<>AsString()
    public string GetD4PeriodStartDateAsString()
    {
        return _D4PeriodStartDate != null ? _D4PeriodStartDate.GetD4PeriodStartDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD4PeriodStartDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D4PeriodStartDate == null)
        {
            _D4PeriodStartDate = new D4PeriodStartDate();
        }
        _D4PeriodStartDate.SetD4PeriodStartDateAsString(value);
    }
    
    // Standard Getter
    public D4PeriodEndDate GetD4PeriodEndDate()
    {
        return _D4PeriodEndDate;
    }
    
    // Standard Setter
    public void SetD4PeriodEndDate(D4PeriodEndDate value)
    {
        _D4PeriodEndDate = value;
    }
    
    // Get<>AsString()
    public string GetD4PeriodEndDateAsString()
    {
        return _D4PeriodEndDate != null ? _D4PeriodEndDate.GetD4PeriodEndDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD4PeriodEndDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D4PeriodEndDate == null)
        {
            _D4PeriodEndDate = new D4PeriodEndDate();
        }
        _D4PeriodEndDate.SetD4PeriodEndDateAsString(value);
    }
    
    // Standard Getter
    public int GetD4UpdateCount()
    {
        return _D4UpdateCount;
    }
    
    // Standard Setter
    public void SetD4UpdateCount(int value)
    {
        _D4UpdateCount = value;
    }
    
    // Get<>AsString()
    public string GetD4UpdateCountAsString()
    {
        return _D4UpdateCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD4UpdateCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D4UpdateCount = parsed;
    }
    
    // Standard Getter
    public D4InverseKey GetD4InverseKey()
    {
        return _D4InverseKey;
    }
    
    // Standard Setter
    public void SetD4InverseKey(D4InverseKey value)
    {
        _D4InverseKey = value;
    }
    
    // Get<>AsString()
    public string GetD4InverseKeyAsString()
    {
        return _D4InverseKey != null ? _D4InverseKey.GetD4InverseKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD4InverseKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D4InverseKey == null)
        {
            _D4InverseKey = new D4InverseKey();
        }
        _D4InverseKey.SetD4InverseKeyAsString(value);
    }
    
    // Standard Getter
    public D4Key3 GetD4Key3()
    {
        return _D4Key3;
    }
    
    // Standard Setter
    public void SetD4Key3(D4Key3 value)
    {
        _D4Key3 = value;
    }
    
    // Get<>AsString()
    public string GetD4Key3AsString()
    {
        return _D4Key3 != null ? _D4Key3.GetD4Key3AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD4Key3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D4Key3 == null)
        {
            _D4Key3 = new D4Key3();
        }
        _D4Key3.SetD4Key3AsString(value);
    }
    
    // Standard Getter
    public string GetD4OlabFund()
    {
        return _D4OlabFund;
    }
    
    // Standard Setter
    public void SetD4OlabFund(string value)
    {
        _D4OlabFund = value;
    }
    
    // Get<>AsString()
    public string GetD4OlabFundAsString()
    {
        return _D4OlabFund.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD4OlabFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D4OlabFund = value;
    }
    
    // Standard Getter
    public string GetD4LifeSummaryFund()
    {
        return _D4LifeSummaryFund;
    }
    
    // Standard Setter
    public void SetD4LifeSummaryFund(string value)
    {
        _D4LifeSummaryFund = value;
    }
    
    // Get<>AsString()
    public string GetD4LifeSummaryFundAsString()
    {
        return _D4LifeSummaryFund.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD4LifeSummaryFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D4LifeSummaryFund = value;
    }
    
    // Standard Getter
    public string GetD4CalendarId()
    {
        return _D4CalendarId;
    }
    
    // Standard Setter
    public void SetD4CalendarId(string value)
    {
        _D4CalendarId = value;
    }
    
    // Get<>AsString()
    public string GetD4CalendarIdAsString()
    {
        return _D4CalendarId.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD4CalendarIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D4CalendarId = value;
    }
    
    // Standard Getter
    public string GetD4UseBfLosses()
    {
        return _D4UseBfLosses;
    }
    
    // Standard Setter
    public void SetD4UseBfLosses(string value)
    {
        _D4UseBfLosses = value;
    }
    
    // Get<>AsString()
    public string GetD4UseBfLossesAsString()
    {
        return _D4UseBfLosses.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD4UseBfLossesAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D4UseBfLosses = value;
    }
    
    // Standard Getter
    public string GetD4PriceTypeCode()
    {
        return _D4PriceTypeCode;
    }
    
    // Standard Setter
    public void SetD4PriceTypeCode(string value)
    {
        _D4PriceTypeCode = value;
    }
    
    // Get<>AsString()
    public string GetD4PriceTypeCodeAsString()
    {
        return _D4PriceTypeCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD4PriceTypeCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D4PriceTypeCode = value;
    }
    
    // Standard Getter
    public string GetD4UseEarlierPrice()
    {
        return _D4UseEarlierPrice;
    }
    
    // Standard Setter
    public void SetD4UseEarlierPrice(string value)
    {
        _D4UseEarlierPrice = value;
    }
    
    // Get<>AsString()
    public string GetD4UseEarlierPriceAsString()
    {
        return _D4UseEarlierPrice.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD4UseEarlierPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D4UseEarlierPrice = value;
    }
    
    // Standard Getter
    public string GetD4PercentageAllowance()
    {
        return _D4PercentageAllowance;
    }
    
    // Standard Setter
    public void SetD4PercentageAllowance(string value)
    {
        _D4PercentageAllowance = value;
    }
    
    // Get<>AsString()
    public string GetD4PercentageAllowanceAsString()
    {
        return _D4PercentageAllowance.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD4PercentageAllowanceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D4PercentageAllowance = value;
    }
    
    // Standard Getter
    public decimal GetD4PercentageAllowance9()
    {
        return _D4PercentageAllowance9;
    }
    
    // Standard Setter
    public void SetD4PercentageAllowance9(decimal value)
    {
        _D4PercentageAllowance9 = value;
    }
    
    // Get<>AsString()
    public string GetD4PercentageAllowance9AsString()
    {
        return _D4PercentageAllowance9.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD4PercentageAllowance9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D4PercentageAllowance9 = parsed;
    }
    
    // Standard Getter
    public string GetD4Filler()
    {
        return _D4Filler;
    }
    
    // Standard Setter
    public void SetD4Filler(string value)
    {
        _D4Filler = value;
    }
    
    // Get<>AsString()
    public string GetD4FillerAsString()
    {
        return _D4Filler.PadRight(36);
    }
    
    // Set<>AsString()
    public void SetD4FillerAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D4Filler = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD4Key(string value)
    {
        _D4Key.SetD4KeyAsString(value);
    }
    // Nested Class: D4Key
    public class D4Key
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D4FundCode, is_external=, is_static_class=False, static_prefix=
        private string _D4FundCode ="";
        
        
        
        
    public D4Key() {}
    
    public D4Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD4FundCode(data.Substring(offset, 4).Trim());
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetD4KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D4FundCode.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD4KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD4FundCode(extracted);
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD4FundCode()
    {
        return _D4FundCode;
    }
    
    // Standard Setter
    public void SetD4FundCode(string value)
    {
        _D4FundCode = value;
    }
    
    // Get<>AsString()
    public string GetD4FundCodeAsString()
    {
        return _D4FundCode.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD4FundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D4FundCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetD4Key2(string value)
{
    _D4Key2.SetD4Key2AsString(value);
}
// Nested Class: D4Key2
public class D4Key2
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D4SummaryFund, is_external=, is_static_class=False, static_prefix=
    private string _D4SummaryFund ="";
    
    
    
    
public D4Key2() {}

public D4Key2(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD4SummaryFund(data.Substring(offset, 4).Trim());
    offset += 4;
    
}

// Serialization methods
public string GetD4Key2AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D4SummaryFund.PadRight(4));
    
    return result.ToString();
}

public void SetD4Key2AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetD4SummaryFund(extracted);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public string GetD4SummaryFund()
{
    return _D4SummaryFund;
}

// Standard Setter
public void SetD4SummaryFund(string value)
{
    _D4SummaryFund = value;
}

// Get<>AsString()
public string GetD4SummaryFundAsString()
{
    return _D4SummaryFund.PadRight(4);
}

// Set<>AsString()
public void SetD4SummaryFundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D4SummaryFund = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD4PeriodStartDate(string value)
{
    _D4PeriodStartDate.SetD4PeriodStartDateAsString(value);
}
// Nested Class: D4PeriodStartDate
public class D4PeriodStartDate
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D4PeriodStartDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D4PeriodStartDateMm ="";
    
    
    
    
    // [DEBUG] Field: D4PeriodStartDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D4PeriodStartDateDd ="";
    
    
    
    
public D4PeriodStartDate() {}

public D4PeriodStartDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD4PeriodStartDateMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD4PeriodStartDateDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD4PeriodStartDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D4PeriodStartDateMm.PadRight(2));
    result.Append(_D4PeriodStartDateDd.PadRight(2));
    
    return result.ToString();
}

public void SetD4PeriodStartDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD4PeriodStartDateMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD4PeriodStartDateDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD4PeriodStartDateMm()
{
    return _D4PeriodStartDateMm;
}

// Standard Setter
public void SetD4PeriodStartDateMm(string value)
{
    _D4PeriodStartDateMm = value;
}

// Get<>AsString()
public string GetD4PeriodStartDateMmAsString()
{
    return _D4PeriodStartDateMm.PadRight(2);
}

// Set<>AsString()
public void SetD4PeriodStartDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D4PeriodStartDateMm = value;
}

// Standard Getter
public string GetD4PeriodStartDateDd()
{
    return _D4PeriodStartDateDd;
}

// Standard Setter
public void SetD4PeriodStartDateDd(string value)
{
    _D4PeriodStartDateDd = value;
}

// Get<>AsString()
public string GetD4PeriodStartDateDdAsString()
{
    return _D4PeriodStartDateDd.PadRight(2);
}

// Set<>AsString()
public void SetD4PeriodStartDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D4PeriodStartDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD4PeriodEndDate(string value)
{
    _D4PeriodEndDate.SetD4PeriodEndDateAsString(value);
}
// Nested Class: D4PeriodEndDate
public class D4PeriodEndDate
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D4PeriodEndDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D4PeriodEndDateMm ="";
    
    
    
    
    // [DEBUG] Field: D4PeriodEndDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D4PeriodEndDateDd ="";
    
    
    
    
public D4PeriodEndDate() {}

public D4PeriodEndDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD4PeriodEndDateMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD4PeriodEndDateDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD4PeriodEndDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D4PeriodEndDateMm.PadRight(2));
    result.Append(_D4PeriodEndDateDd.PadRight(2));
    
    return result.ToString();
}

public void SetD4PeriodEndDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD4PeriodEndDateMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD4PeriodEndDateDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD4PeriodEndDateMm()
{
    return _D4PeriodEndDateMm;
}

// Standard Setter
public void SetD4PeriodEndDateMm(string value)
{
    _D4PeriodEndDateMm = value;
}

// Get<>AsString()
public string GetD4PeriodEndDateMmAsString()
{
    return _D4PeriodEndDateMm.PadRight(2);
}

// Set<>AsString()
public void SetD4PeriodEndDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D4PeriodEndDateMm = value;
}

// Standard Getter
public string GetD4PeriodEndDateDd()
{
    return _D4PeriodEndDateDd;
}

// Standard Setter
public void SetD4PeriodEndDateDd(string value)
{
    _D4PeriodEndDateDd = value;
}

// Get<>AsString()
public string GetD4PeriodEndDateDdAsString()
{
    return _D4PeriodEndDateDd.PadRight(2);
}

// Set<>AsString()
public void SetD4PeriodEndDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D4PeriodEndDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD4InverseKey(string value)
{
    _D4InverseKey.SetD4InverseKeyAsString(value);
}
// Nested Class: D4InverseKey
public class D4InverseKey
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D4InverseFundCode, is_external=, is_static_class=False, static_prefix=
    private string _D4InverseFundCode ="";
    
    
    
    
public D4InverseKey() {}

public D4InverseKey(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD4InverseFundCode(data.Substring(offset, 4).Trim());
    offset += 4;
    
}

// Serialization methods
public string GetD4InverseKeyAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D4InverseFundCode.PadRight(4));
    
    return result.ToString();
}

public void SetD4InverseKeyAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetD4InverseFundCode(extracted);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public string GetD4InverseFundCode()
{
    return _D4InverseFundCode;
}

// Standard Setter
public void SetD4InverseFundCode(string value)
{
    _D4InverseFundCode = value;
}

// Get<>AsString()
public string GetD4InverseFundCodeAsString()
{
    return _D4InverseFundCode.PadRight(4);
}

// Set<>AsString()
public void SetD4InverseFundCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D4InverseFundCode = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD4Key3(string value)
{
    _D4Key3.SetD4Key3AsString(value);
}
// Nested Class: D4Key3
public class D4Key3
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D4NewSummaryFund, is_external=, is_static_class=False, static_prefix=
    private string _D4NewSummaryFund ="";
    
    
    
    
public D4Key3() {}

public D4Key3(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD4NewSummaryFund(data.Substring(offset, 4).Trim());
    offset += 4;
    
}

// Serialization methods
public string GetD4Key3AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D4NewSummaryFund.PadRight(4));
    
    return result.ToString();
}

public void SetD4Key3AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetD4NewSummaryFund(extracted);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public string GetD4NewSummaryFund()
{
    return _D4NewSummaryFund;
}

// Standard Setter
public void SetD4NewSummaryFund(string value)
{
    _D4NewSummaryFund = value;
}

// Get<>AsString()
public string GetD4NewSummaryFundAsString()
{
    return _D4NewSummaryFund.PadRight(4);
}

// Set<>AsString()
public void SetD4NewSummaryFundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D4NewSummaryFund = value;
}



public static int GetSize()
{
    return _size;
}

}

}}