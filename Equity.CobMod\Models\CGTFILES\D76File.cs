using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D76File Data Structure

public class D76File
{
    private static int _size = 12;
    // [DEBUG] Class: D76File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler238, is_external=, is_static_class=False, static_prefix=
    private string _Filler238 ="$";
    
    
    
    
    // [DEBUG] Field: D76UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D76UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler239, is_external=, is_static_class=False, static_prefix=
    private string _Filler239 ="RG";
    
    
    
    
    // [DEBUG] Field: D76ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D76ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler240, is_external=, is_static_class=False, static_prefix=
    private string _Filler240 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD76FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler238.PadRight(1));
        result.Append(_D76UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler239.PadRight(2));
        result.Append(_D76ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler240.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD76FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller238(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD76UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller239(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD76ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller240(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD76FileAsString();
    }
    // Set<>String Override function
    public void SetD76File(string value)
    {
        SetD76FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller238()
    {
        return _Filler238;
    }
    
    // Standard Setter
    public void SetFiller238(string value)
    {
        _Filler238 = value;
    }
    
    // Get<>AsString()
    public string GetFiller238AsString()
    {
        return _Filler238.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller238AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler238 = value;
    }
    
    // Standard Getter
    public int GetD76UserNo()
    {
        return _D76UserNo;
    }
    
    // Standard Setter
    public void SetD76UserNo(int value)
    {
        _D76UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD76UserNoAsString()
    {
        return _D76UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD76UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D76UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller239()
    {
        return _Filler239;
    }
    
    // Standard Setter
    public void SetFiller239(string value)
    {
        _Filler239 = value;
    }
    
    // Get<>AsString()
    public string GetFiller239AsString()
    {
        return _Filler239.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller239AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler239 = value;
    }
    
    // Standard Getter
    public int GetD76ReportNo()
    {
        return _D76ReportNo;
    }
    
    // Standard Setter
    public void SetD76ReportNo(int value)
    {
        _D76ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD76ReportNoAsString()
    {
        return _D76ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD76ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D76ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller240()
    {
        return _Filler240;
    }
    
    // Standard Setter
    public void SetFiller240(string value)
    {
        _Filler240 = value;
    }
    
    // Get<>AsString()
    public string GetFiller240AsString()
    {
        return _Filler240.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller240AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler240 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}