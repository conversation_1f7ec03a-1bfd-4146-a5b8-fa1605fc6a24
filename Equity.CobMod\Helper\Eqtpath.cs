﻿using System;
using System.Text;
using System.IO; // Required for Path.Combine
using EquityProject.CommonDTO; // Assuming EquityGlobalParms might be defined here if not in EqtpathDTO
using EquityProject.EqtpathDTO;
using EquityProject.EquityGlobalParmsDTO;
using EquityProject.EqtdebugPGM;

namespace EquityProject.EqtpathPGM
{
    // Eqtpath Class Definition
    public class Eqtpath
    {
        // Declare Eqtpath Class private variables
        // These hold the state specific to this program instance
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();
        // Assume EquityGlobalParms is needed and passed in or globally accessible
        private EquityGlobalParms _equityGlobalParms = new EquityGlobalParms();
        
        private Eqtdebug eqtdebug = new Eqtdebug();

        // Declare Eqtpath Class getters setters
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }
        public EquityGlobalParms GetEquityGlobalParms() { return _equityGlobalParms; }
        public void SetEquityGlobalParms(EquityGlobalParms value) { _equityGlobalParms = value; }

        // Constructor
        public Eqtpath()
        {
            // Initialize Gvar/Ivar if necessary, though they are often populated externally
        }

        // Run() method - Main entry point corresponding to PROCEDURE DIVISION
        // Updated signature to accept EquityGlobalParms explicitly
        public void Run(Gvar gvar, Ivar ivar, EquityGlobalParms equityGlobalParms)
        {
            // Set internal state from parameters
            this._gvar = gvar;
            this._ivar = ivar;
            this._equityGlobalParms = equityGlobalParms;

            eqtdebug.GetIvar().SetEqtdebugLinkageAsString(ivar.GetEqtpathLinkageAsString());
            eqtdebug.CallSub(eqtdebug.GetIvar());
            // Execute the main logic block
            AMain(gvar, ivar, equityGlobalParms);

            // EXIT PROGRAM is implicit upon returning from Run
        }

        // CallSub() method (from template, not directly used by COBOL logic shown)
        public void CallSub(Ivar ivar)
        {

            // Implement subroutine call logic here if needed
        }

        // Methods representing sections

        // A-MAIN SECTION.
        public void AMain(Gvar gvar, Ivar ivar, EquityGlobalParms equityGlobalParms)
        {
            EqtpathLinkage eqtpathLinkage = ivar.GetEqtpathLinkage(); // Convenience reference
            EquityGlobalParms.GPaths globalPaths = equityGlobalParms.GetGPaths(); // Convenience reference

            // MOVE SPACES TO EQTPATH-PATH-FILE-NAME
            eqtpathLinkage.SetEqtpathPathFileName(" ");
            // Initialize return code (although COBOL doesn't explicitly, good practice)
            eqtpathLinkage.SetEqtpathReturnCode(0);

            // EVALUATE EQTPATH-PATH-ENV-VARIABLE
            // Use Trim() to handle potential trailing spaces from COBOL PIC X fields
            string pathEnvVariable = eqtpathLinkage.GetEqtpathPathEnvVariable()?.Trim() ?? "";

            switch (pathEnvVariable)
            {
                // WHEN ADMIN-DATA-PATH
                case Ivar.ADMIN_DATA_PATH: // Use constant from Ivar.cs
                    // MOVE G-ADMIN-DATA-PATH TO W-PATH-NAME
                    gvar.SetWPathName(globalPaths.GetGAdminDataPath());
                    // PERFORM B-JOIN-PATH
                    BJoinPath(gvar, ivar, equityGlobalParms);
                    break;

                // WHEN USER-DATA-PATH
                case Ivar.USER_DATA_PATH: // Use constant from Ivar.cs
                    // MOVE G-USER-DATA-PATH TO W-PATH-NAME
                    gvar.SetWPathName(globalPaths.GetGUserDataPath());
                    // PERFORM B-JOIN-PATH
                    BJoinPath(gvar, ivar, equityGlobalParms);
                    break;

                // WHEN MASTER-DATA-PATH
                case Ivar.MASTER_DATA_PATH: // Use constant from Ivar.cs
                    // MOVE G-MASTER-DATA-PATH TO W-PATH-NAME
                    gvar.SetWPathName(globalPaths.GetGMasterDataPath());
                    // PERFORM B-JOIN-PATH
                    BJoinPath(gvar, ivar, equityGlobalParms);
                    break;

                // WHEN OTHER
                default:
                    // MOVE 9 TO EQTPATH-RETURN-CODE
                    eqtpathLinkage.SetEqtpathReturnCode(9);
                    // Optionally log this error
                    PrepareDebugMessage($"EQTPATH Error: Unknown Path Env Var '{pathEnvVariable}'");
                    XCallEqtdebug(gvar, ivar, equityGlobalParms);
                    break;
            }
            // End EVALUATE

            // EXIT PROGRAM is handled by returning from Run()

            // A-EXIT. EXIT. (No code here)
            AExit(gvar, ivar);
        }

        // Methods representing paras under sections
        public void AExit(Gvar gvar, Ivar ivar)
        {
            // EXIT. (No logic in COBOL example)
        }

        // B-JOIN-PATH SECTION.
        public void BJoinPath(Gvar gvar, Ivar ivar, EquityGlobalParms equityGlobalParms)
        {
            EqtpathLinkage eqtpathLinkage = ivar.GetEqtpathLinkage(); // Convenience

            // The COBOL code prepares parameters extensively for 'CBL_JOIN_FILENAME'.
            // In C#, the standard and more robust way to combine paths is Path.Combine.
            // We will use Path.Combine and ignore the setup for SPLIT-PARAMS and buffers
            // (SplitBuffer, JoinBuffer, PathBuffer, BasenameBuffer, ExtensionBuffer)
            // as they are not needed for the C# equivalent function.

            string basePath = gvar.GetWPathName()?.TrimEnd() ?? ""; // Get the path determined in AMain, trim trailing space
            string fileName = eqtpathLinkage.GetEqtpathFileName()?.Trim() ?? ""; // Trim spaces from filename

            // Basic validation
            if (string.IsNullOrEmpty(basePath))
            {
                PrepareDebugMessage($"EQTPATH Error: Base path is empty for Env Var '{eqtpathLinkage.GetEqtpathPathEnvVariable()}'");
                XCallEqtdebug(gvar, ivar, equityGlobalParms);
                eqtpathLinkage.SetEqtpathPathFileName(fileName); // Return just the filename if no path
                eqtpathLinkage.SetEqtpathReturnCode(1); // Indicate potential issue: no path found
                BExit(gvar, ivar);
                return;
            }

            if (string.IsNullOrEmpty(fileName))
            {
                PrepareDebugMessage($"EQTPATH Error: File name is empty for Env Var '{eqtpathLinkage.GetEqtpathPathEnvVariable()}'");
                XCallEqtdebug(gvar, ivar, equityGlobalParms);
                eqtpathLinkage.SetEqtpathPathFileName(basePath); // Return just the path if no filename
                eqtpathLinkage.SetEqtpathReturnCode(2); // Indicate potential issue: no filename provided
                BExit(gvar, ivar);
                return;
            }

            try
            {
                // CALL 'CBL_JOIN_FILENAME' ... equivalent using System.IO.Path.Combine
                string combinedPath = Path.Combine(basePath, fileName);

                // MOVE JOIN-BUFFER TO EQTPATH-PATH-FILE-NAME
                // Ensure the result fits within the allocated DTO size (256)
                if (combinedPath.Length > 256)
                {
                    // Handle error: Resulting path is too long
                    PrepareDebugMessage($"EQTPATH Warning: Joined path exceeded 256 chars, truncated.",
                                        $"  Original File: {fileName}",
                                        $"  Truncated Path: {combinedPath.Substring(0, 256)}");
                    XCallEqtdebug(gvar, ivar, equityGlobalParms);
                    combinedPath = combinedPath.Substring(0, 256); // Truncate
                    eqtpathLinkage.SetEqtpathReturnCode(3); // Indicate warning/error: path too long
                }
                else
                {
                    // MOVE 0 TO EQTPATH-RETURN-CODE (Set success code only if length is okay)
                    eqtpathLinkage.SetEqtpathReturnCode(0);
                }
                eqtpathLinkage.SetEqtpathPathFileName(combinedPath);

            }
            catch (ArgumentException argEx)
            {
                // Handle potential errors from Path.Combine (e.g., invalid characters in filename)
                PrepareDebugMessage($"EQTPATH Error: Invalid characters in path components.",
                                    $"  Base: '{basePath}', File: '{fileName}'",
                                    $"  Exception: {argEx.Message}");
                XCallEqtdebug(gvar, ivar, equityGlobalParms);
                eqtpathLinkage.SetEqtpathPathFileName(""); // Clear output path on error
                eqtpathLinkage.SetEqtpathReturnCode(4); // Indicate error during join
            }
            catch (Exception ex) // Catch other potential errors
            {
                PrepareDebugMessage($"EQTPATH Error: Unexpected error joining path.",
                                    $"  Base: '{basePath}', File: '{fileName}'",
                                    $"  Exception: {ex.Message}");
                XCallEqtdebug(gvar, ivar, equityGlobalParms);
                eqtpathLinkage.SetEqtpathPathFileName(""); // Clear output path on error
                eqtpathLinkage.SetEqtpathReturnCode(5); // Indicate generic error during join
            }

            // B-EXIT. EXIT.
            BExit(gvar, ivar);
        }

        // Methods representing paras under sections
        public void BExit(Gvar gvar, Ivar ivar)
        {
            // EXIT. (No logic in COBOL example)
        }

        // Method to encapsulate calling EQTDEBUG (from COPY EQTDEBUG.CPY)
        // This assumes EQTDEBUG is available and follows the pattern established previously.
        public void XCallEqtdebug(Gvar gvar, Ivar ivar, EquityGlobalParms equityGlobalParms)
        {
            // Check if logging is enabled in global params (example check)
            if (equityGlobalParms?.GetGGeneralParms()?.IsOffMode() ?? true) // Default to off if params are null
            {
                Console.WriteLine("(Debug logging is off)"); // Optional console message if not logging
                                                             // Clear the message buffer anyway
                PrepareDebugMessage(); // Call with defaults to clear
                XExit(gvar, ivar);
                return;
            }

            try
            {
                // EQTDEBUG uses its own Linkage/Ivar structure passed TO it.
                // We use the EqtdebugLinkage area within *this* program's Gvar
                // to pass the message TO EQTDEBUG.
                Eqtdebug debugProgram = eqtdebug;

                // EQTDEBUG's Run method expects its own Gvar, Ivar, and EquityGlobalParms.
                // - We pass *this* program's Gvar because EQTDEBUG might need common GVAR data
                //   (like CommonLinkage) and it contains the message we prepared.
                // - We create a *new* Ivar specific to EQTDEBUG and populate its linkage
                //   from the debug area in *this* program's Gvar.
                // - We pass the common EquityGlobalParms.
                // IMPORTANT: Get the debug linkage *from this program's GVAR* where we prepared it
              //  eqtdebugIvar.SetEqtdebugLinkage(gvar.GetEqtdebugLinkage());

                // Call the debug program
                // Assuming Eqtdebug's Run method signature is Run(Gvar, Ivar, EquityGlobalParms)
                debugProgram.Run(eqtdebug.GetGvar(), eqtdebug.GetIvar(), _equityGlobalParms);

                // Clear the debug message area in this program's GVAR *after* sending
                PrepareDebugMessage(); // Call with defaults to clear
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Failed to call EQTDEBUG from EQTPATH: {ex.Message}");
                // Avoid recursive error logging if EQTDEBUG itself fails
                // Clear the message buffer to prevent repeated attempts on subsequent errors
                PrepareDebugMessage();
            }

            XExit(gvar, ivar); // Call exit paragraph
        }

        // Helper to prepare the debug message in GVAR's debug area
        private void PrepareDebugMessage(string line1 = "", string line2 = "", string line3 = "")
        {
            // Use the Gvar instance variable _gvar
            _gvar.GetEqtdebugLinkage().SetEqtdebugTextLine1(line1.Length > 80 ? line1.Substring(0, 80) : line1);
            _gvar.GetEqtdebugLinkage().SetEqtdebugTextLine2(line2.Length > 80 ? line2.Substring(0, 80) : line2);
            _gvar.GetEqtdebugLinkage().SetEqtdebugTextLine3(line3.Length > 80 ? line3.Substring(0, 80) : line3);
        }


        // Methods representing paras under sections
        public void XExit(Gvar gvar, Ivar ivar)
        {
            // EXIT. (No logic in COBOL example)
        }


        // Main() method of the Eqtpath Class - Example entry point for testing
        public static void Main(string[] args)
        {
            Eqtpath program = new Eqtpath();
            // Create specific Gvar/Ivar for Eqtpath
            Gvar eqtpathGvar = new Gvar();
            Ivar eqtpathIvar = new Ivar();
            // Create Global Params - needed by Eqtpath and potentially by Eqtdebug
            EquityGlobalParms equityGlobalParms = new EquityGlobalParms();

            // --- Setup Global Paths ---
            equityGlobalParms.GetGPaths().SetGAdminDataPath(@"C:\EquityApp\AdminData\"); // Example with trailing slash
            equityGlobalParms.GetGPaths().SetGUserDataPath(@"/usr/local/equity/user"); // Example Unix path
            equityGlobalParms.GetGPaths().SetGMasterDataPath(@"\\SERVER\Share\Master"); // Example UNC path

            // --- Setup Debug Logging Mode ---
            // 'P'=Pause, 'D'=Scroll, 'L'=Log(via TRACEPGM), 'O'=Off
            equityGlobalParms.GetGGeneralParms().SetGLogSwitch("P");
            equityGlobalParms.GetGGeneralParms().SetGUserNo("0001"); // Needed if LogSwitch='L'

            // --- Test Case 1: Admin Path ---
            Console.WriteLine("--- Test Case 1: Admin Path ---");
            eqtpathIvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Ivar.ADMIN_DATA_PATH);
            eqtpathIvar.GetEqtpathLinkage().SetEqtpathFileName("parameters.cfg");
            program.Run(eqtpathGvar, eqtpathIvar, equityGlobalParms);
            Console.WriteLine($"  Input Env Var : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathPathEnvVariable()}");
            Console.WriteLine($"  Input Filename: {eqtpathIvar.GetEqtpathLinkage().GetEqtpathFileName()}");
            Console.WriteLine($"  Output Path   : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathPathFileName()}");
            Console.WriteLine($"  Return Code   : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathReturnCode()}"); // Expected: 0
            Console.WriteLine("--------------------------------");

            // --- Test Case 2: User Path ---
            Console.WriteLine("--- Test Case 2: User Path ---");
            eqtpathIvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Ivar.USER_DATA_PATH);
            eqtpathIvar.GetEqtpathLinkage().SetEqtpathFileName("   user_report.txt   "); // With spaces
            program.Run(eqtpathGvar, eqtpathIvar, equityGlobalParms);
            Console.WriteLine($"  Input Env Var : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathPathEnvVariable()}");
            Console.WriteLine($"  Input Filename: {eqtpathIvar.GetEqtpathLinkage().GetEqtpathFileName()}");
            Console.WriteLine($"  Output Path   : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathPathFileName()}"); // Expect spaces trimmed from filename part
            Console.WriteLine($"  Return Code   : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathReturnCode()}"); // Expected: 0
            Console.WriteLine("--------------------------------");

            // --- Test Case 3: Master Path ---
            Console.WriteLine("--- Test Case 3: Master Path ---");
            eqtpathIvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Ivar.MASTER_DATA_PATH);
            eqtpathIvar.GetEqtpathLinkage().SetEqtpathFileName("STOCK.DAT");
            program.Run(eqtpathGvar, eqtpathIvar, equityGlobalParms);
            Console.WriteLine($"  Input Env Var : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathPathEnvVariable()}");
            Console.WriteLine($"  Input Filename: {eqtpathIvar.GetEqtpathLinkage().GetEqtpathFileName()}");
            Console.WriteLine($"  Output Path   : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathPathFileName()}");
            Console.WriteLine($"  Return Code   : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathReturnCode()}"); // Expected: 0
            Console.WriteLine("--------------------------------");


            // --- Test Case 4: Invalid Env Var ---
            Console.WriteLine("--- Test Case 4: Invalid Env Var ---");
            eqtpathIvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable("INVALID");
            eqtpathIvar.GetEqtpathLinkage().SetEqtpathFileName("somefile.txt");
            program.Run(eqtpathGvar, eqtpathIvar, equityGlobalParms);
            Console.WriteLine($"  Input Env Var : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathPathEnvVariable()}");
            Console.WriteLine($"  Input Filename: {eqtpathIvar.GetEqtpathLinkage().GetEqtpathFileName()}");
            Console.WriteLine($"  Output Path   : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathPathFileName()}"); // Expected: ""
            Console.WriteLine($"  Return Code   : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathReturnCode()}"); // Expected: 9
            Console.WriteLine("--------------------------------");

            // --- Test Case 5: Empty Filename ---
            Console.WriteLine("--- Test Case 5: Empty Filename ---");
            eqtpathIvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Ivar.ADMIN_DATA_PATH);
            eqtpathIvar.GetEqtpathLinkage().SetEqtpathFileName(""); // Empty filename
            program.Run(eqtpathGvar, eqtpathIvar, equityGlobalParms);
            Console.WriteLine($"  Input Env Var : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathPathEnvVariable()}");
            Console.WriteLine($"  Input Filename: {eqtpathIvar.GetEqtpathLinkage().GetEqtpathFileName()}");
            Console.WriteLine($"  Output Path   : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathPathFileName()}"); // Expected: Base path only
            Console.WriteLine($"  Return Code   : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathReturnCode()}"); // Expected: 2
            Console.WriteLine("--------------------------------");

            // --- Test Case 6: Path too long ---
            Console.WriteLine("--- Test Case 6: Path too long ---");
            string longFilename = new string('A', 250) + ".txt"; // Creates a filename that will likely exceed 256 when combined
            equityGlobalParms.GetGPaths().SetGAdminDataPath(@"C:\VeryLongDirectoryName\AnotherLongName\EvenLongerOne");
            eqtpathIvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Ivar.ADMIN_DATA_PATH);
            eqtpathIvar.GetEqtpathLinkage().SetEqtpathFileName(longFilename);
            program.Run(eqtpathGvar, eqtpathIvar, equityGlobalParms);
            Console.WriteLine($"  Input Env Var : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathPathEnvVariable()}");
            Console.WriteLine($"  Input Filename: {eqtpathIvar.GetEqtpathLinkage().GetEqtpathFileName().Substring(0, 20)}..."); // Show start of long name
            Console.WriteLine($"  Output Path   : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathPathFileName()}"); // Should be truncated
            Console.WriteLine($"  Output Len    : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathPathFileName().Length}"); // Should be 256
            Console.WriteLine($"  Return Code   : {eqtpathIvar.GetEqtpathLinkage().GetEqtpathReturnCode()}"); // Expected: 3
            Console.WriteLine("--------------------------------");
        }
    }
}