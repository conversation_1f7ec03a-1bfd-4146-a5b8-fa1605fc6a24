using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D93HeaderRecord Data Structure

public class D93HeaderRecord
{
    private static int _size = 22;
    // [DEBUG] Class: D93HeaderRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler132, is_external=, is_static_class=False, static_prefix=
    private string _Filler132 ="";
    
    
    // 88-level condition checks for Filler132
    public bool IsD93HeaderFound()
    {
        if (this._Filler132 == "'0000EQUITY RPI HEADER '") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D93ExtractDate, is_external=, is_static_class=False, static_prefix=
    private D93ExtractDate _D93ExtractDate = new D93ExtractDate();
    
    
    
    
    
    // Serialization methods
    public string GetD93HeaderRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler132.PadRight(22));
        result.Append(_D93ExtractDate.GetD93ExtractDateAsString());
        
        return result.ToString();
    }
    
    public void SetD93HeaderRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 22 <= data.Length)
        {
            string extracted = data.Substring(offset, 22).Trim();
            SetFiller132(extracted);
        }
        offset += 22;
        if (offset + 0 <= data.Length)
        {
            _D93ExtractDate.SetD93ExtractDateAsString(data.Substring(offset, 0));
        }
        else
        {
            _D93ExtractDate.SetD93ExtractDateAsString(data.Substring(offset));
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD93HeaderRecordAsString();
    }
    // Set<>String Override function
    public void SetD93HeaderRecord(string value)
    {
        SetD93HeaderRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller132()
    {
        return _Filler132;
    }
    
    // Standard Setter
    public void SetFiller132(string value)
    {
        _Filler132 = value;
    }
    
    // Get<>AsString()
    public string GetFiller132AsString()
    {
        return _Filler132.PadRight(22);
    }
    
    // Set<>AsString()
    public void SetFiller132AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler132 = value;
    }
    
    // Standard Getter
    public D93ExtractDate GetD93ExtractDate()
    {
        return _D93ExtractDate;
    }
    
    // Standard Setter
    public void SetD93ExtractDate(D93ExtractDate value)
    {
        _D93ExtractDate = value;
    }
    
    // Get<>AsString()
    public string GetD93ExtractDateAsString()
    {
        return _D93ExtractDate != null ? _D93ExtractDate.GetD93ExtractDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD93ExtractDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D93ExtractDate == null)
        {
            _D93ExtractDate = new D93ExtractDate();
        }
        _D93ExtractDate.SetD93ExtractDateAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD93ExtractDate(string value)
    {
        _D93ExtractDate.SetD93ExtractDateAsString(value);
    }
    // Nested Class: D93ExtractDate
    public class D93ExtractDate
    {
        private static int _size = 0;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D93ExtractDateYy, is_external=, is_static_class=False, static_prefix=
        private string _D93ExtractDateYy ="";
        
        
        
        
        // [DEBUG] Field: D93ExtractDateMm, is_external=, is_static_class=False, static_prefix=
        private string _D93ExtractDateMm ="";
        
        
        
        
        // [DEBUG] Field: D93ExtractDateDd, is_external=, is_static_class=False, static_prefix=
        private string _D93ExtractDateDd ="";
        
        
        
        
    public D93ExtractDate() {}
    
    public D93ExtractDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD93ExtractDateYy(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD93ExtractDateMm(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD93ExtractDateDd(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD93ExtractDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D93ExtractDateYy.PadRight(0));
        result.Append(_D93ExtractDateMm.PadRight(0));
        result.Append(_D93ExtractDateDd.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD93ExtractDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD93ExtractDateYy(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD93ExtractDateMm(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD93ExtractDateDd(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD93ExtractDateYy()
    {
        return _D93ExtractDateYy;
    }
    
    // Standard Setter
    public void SetD93ExtractDateYy(string value)
    {
        _D93ExtractDateYy = value;
    }
    
    // Get<>AsString()
    public string GetD93ExtractDateYyAsString()
    {
        return _D93ExtractDateYy.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD93ExtractDateYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D93ExtractDateYy = value;
    }
    
    // Standard Getter
    public string GetD93ExtractDateMm()
    {
        return _D93ExtractDateMm;
    }
    
    // Standard Setter
    public void SetD93ExtractDateMm(string value)
    {
        _D93ExtractDateMm = value;
    }
    
    // Get<>AsString()
    public string GetD93ExtractDateMmAsString()
    {
        return _D93ExtractDateMm.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD93ExtractDateMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D93ExtractDateMm = value;
    }
    
    // Standard Getter
    public string GetD93ExtractDateDd()
    {
        return _D93ExtractDateDd;
    }
    
    // Standard Setter
    public void SetD93ExtractDateDd(string value)
    {
        _D93ExtractDateDd = value;
    }
    
    // Get<>AsString()
    public string GetD93ExtractDateDdAsString()
    {
        return _D93ExtractDateDd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD93ExtractDateDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D93ExtractDateDd = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}