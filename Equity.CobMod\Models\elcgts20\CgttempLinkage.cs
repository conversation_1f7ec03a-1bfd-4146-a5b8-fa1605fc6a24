using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing CgttempLinkage Data Structure

public class CgttempLinkage
{
    private static int _size = 2105;
    // [DEBUG] Class: CgttempLinkage, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: CgttempAction, is_external=, is_static_class=False, static_prefix=
    private string _CgttempAction ="";
    
    
    
    
    // [DEBUG] Field: CgttempStatus, is_external=, is_static_class=False, static_prefix=
    private string _CgttempStatus ="";
    
    
    
    
    // [DEBUG] Field: CgttempRecord, is_external=, is_static_class=False, static_prefix=
    private CgttempRecord _CgttempRecord = new CgttempRecord();
    
    
    
    
    // [DEBUG] Field: CgttempUserNo, is_external=, is_static_class=False, static_prefix=
    private int _CgttempUserNo =0;
    
    
    
    
    // [DEBUG] Field: CgttempReportNumber, is_external=, is_static_class=False, static_prefix=
    private int _CgttempReportNumber =0;
    
    
    
    
    
    // Serialization methods
    public string GetCgttempLinkageAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_CgttempAction.PadRight(0));
        result.Append(_CgttempStatus.PadRight(0));
        result.Append(_CgttempRecord.GetCgttempRecordAsString());
        result.Append(_CgttempUserNo.ToString().PadLeft(4, '0'));
        result.Append(_CgttempReportNumber.ToString().PadLeft(1, '0'));
        
        return result.ToString();
    }
    
    public void SetCgttempLinkageAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCgttempAction(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCgttempStatus(extracted);
        }
        offset += 0;
        if (offset + 2100 <= data.Length)
        {
            _CgttempRecord.SetCgttempRecordAsString(data.Substring(offset, 2100));
        }
        else
        {
            _CgttempRecord.SetCgttempRecordAsString(data.Substring(offset));
        }
        offset += 2100;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgttempUserNo(parsedInt);
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgttempReportNumber(parsedInt);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgttempLinkageAsString();
    }
    // Set<>String Override function
    public void SetCgttempLinkage(string value)
    {
        SetCgttempLinkageAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgttempAction()
    {
        return _CgttempAction;
    }
    
    // Standard Setter
    public void SetCgttempAction(string value)
    {
        _CgttempAction = value;
    }
    
    // Get<>AsString()
    public string GetCgttempActionAsString()
    {
        return _CgttempAction.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCgttempActionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgttempAction = value;
    }
    
    // Standard Getter
    public string GetCgttempStatus()
    {
        return _CgttempStatus;
    }
    
    // Standard Setter
    public void SetCgttempStatus(string value)
    {
        _CgttempStatus = value;
    }
    
    // Get<>AsString()
    public string GetCgttempStatusAsString()
    {
        return _CgttempStatus.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCgttempStatusAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgttempStatus = value;
    }
    
    // Standard Getter
    public CgttempRecord GetCgttempRecord()
    {
        return _CgttempRecord;
    }
    
    // Standard Setter
    public void SetCgttempRecord(CgttempRecord value)
    {
        _CgttempRecord = value;
    }
    
    // Get<>AsString()
    public string GetCgttempRecordAsString()
    {
        return _CgttempRecord != null ? _CgttempRecord.GetCgttempRecordAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgttempRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CgttempRecord == null)
        {
            _CgttempRecord = new CgttempRecord();
        }
        _CgttempRecord.SetCgttempRecordAsString(value);
    }
    
    // Standard Getter
    public int GetCgttempUserNo()
    {
        return _CgttempUserNo;
    }
    
    // Standard Setter
    public void SetCgttempUserNo(int value)
    {
        _CgttempUserNo = value;
    }
    
    // Get<>AsString()
    public string GetCgttempUserNoAsString()
    {
        return _CgttempUserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetCgttempUserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _CgttempUserNo = parsed;
    }
    
    // Standard Getter
    public int GetCgttempReportNumber()
    {
        return _CgttempReportNumber;
    }
    
    // Standard Setter
    public void SetCgttempReportNumber(int value)
    {
        _CgttempReportNumber = value;
    }
    
    // Get<>AsString()
    public string GetCgttempReportNumberAsString()
    {
        return _CgttempReportNumber.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetCgttempReportNumberAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _CgttempReportNumber = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetCgttempRecord(string value)
    {
        _CgttempRecord.SetCgttempRecordAsString(value);
    }
    // Nested Class: CgttempRecord
    public class CgttempRecord
    {
        private static int _size = 2100;
        
        // Fields in the class
        
        
        // [DEBUG] Field: CgttempKey, is_external=, is_static_class=False, static_prefix=
        private string _CgttempKey ="";
        
        
        
        
        // [DEBUG] Field: CgttempDetails, is_external=, is_static_class=False, static_prefix=
        private string _CgttempDetails ="";
        
        
        
        
    public CgttempRecord() {}
    
    public CgttempRecord(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgttempKey(data.Substring(offset, 100).Trim());
        offset += 100;
        SetCgttempDetails(data.Substring(offset, 2000).Trim());
        offset += 2000;
        
    }
    
    // Serialization methods
    public string GetCgttempRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_CgttempKey.PadRight(100));
        result.Append(_CgttempDetails.PadRight(2000));
        
        return result.ToString();
    }
    
    public void SetCgttempRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 100 <= data.Length)
        {
            string extracted = data.Substring(offset, 100).Trim();
            SetCgttempKey(extracted);
        }
        offset += 100;
        if (offset + 2000 <= data.Length)
        {
            string extracted = data.Substring(offset, 2000).Trim();
            SetCgttempDetails(extracted);
        }
        offset += 2000;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgttempKey()
    {
        return _CgttempKey;
    }
    
    // Standard Setter
    public void SetCgttempKey(string value)
    {
        _CgttempKey = value;
    }
    
    // Get<>AsString()
    public string GetCgttempKeyAsString()
    {
        return _CgttempKey.PadRight(100);
    }
    
    // Set<>AsString()
    public void SetCgttempKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgttempKey = value;
    }
    
    // Standard Getter
    public string GetCgttempDetails()
    {
        return _CgttempDetails;
    }
    
    // Standard Setter
    public void SetCgttempDetails(string value)
    {
        _CgttempDetails = value;
    }
    
    // Get<>AsString()
    public string GetCgttempDetailsAsString()
    {
        return _CgttempDetails.PadRight(2000);
    }
    
    // Set<>AsString()
    public void SetCgttempDetailsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgttempDetails = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}
