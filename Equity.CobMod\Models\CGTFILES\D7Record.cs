using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D7Record Data Structure

public class D7Record
{
    private static int _size = 22;
    // [DEBUG] Class: D7Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D7Key, is_external=, is_static_class=False, static_prefix=
    private D7Key _D7Key = new D7Key();
    
    
    
    
    // [DEBUG] Field: D7Value, is_external=, is_static_class=False, static_prefix=
    private decimal _D7Value =0;
    
    
    
    
    // [DEBUG] Field: D7RpiEst, is_external=, is_static_class=False, static_prefix=
    private string _D7RpiEst ="";
    
    
    
    
    // [DEBUG] Field: D7DateAmended, is_external=, is_static_class=False, static_prefix=
    private D7DateAmended _D7DateAmended = new D7DateAmended();
    
    
    
    
    // [DEBUG] Field: D7UpdateCount, is_external=, is_static_class=False, static_prefix=
    private int _D7UpdateCount =0;
    
    
    
    
    
    // Serialization methods
    public string GetD7RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D7Key.GetD7KeyAsString());
        result.Append(_D7Value.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D7RpiEst.PadRight(1));
        result.Append(_D7DateAmended.GetD7DateAmendedAsString());
        result.Append(_D7UpdateCount.ToString().PadLeft(4, '0'));
        
        return result.ToString();
    }
    
    public void SetD7RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 6 <= data.Length)
        {
            _D7Key.SetD7KeyAsString(data.Substring(offset, 6));
        }
        else
        {
            _D7Key.SetD7KeyAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD7Value(parsedDec);
        }
        offset += 5;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD7RpiEst(extracted);
        }
        offset += 1;
        if (offset + 6 <= data.Length)
        {
            _D7DateAmended.SetD7DateAmendedAsString(data.Substring(offset, 6));
        }
        else
        {
            _D7DateAmended.SetD7DateAmendedAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD7UpdateCount(parsedInt);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD7RecordAsString();
    }
    // Set<>String Override function
    public void SetD7Record(string value)
    {
        SetD7RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D7Key GetD7Key()
    {
        return _D7Key;
    }
    
    // Standard Setter
    public void SetD7Key(D7Key value)
    {
        _D7Key = value;
    }
    
    // Get<>AsString()
    public string GetD7KeyAsString()
    {
        return _D7Key != null ? _D7Key.GetD7KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD7KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D7Key == null)
        {
            _D7Key = new D7Key();
        }
        _D7Key.SetD7KeyAsString(value);
    }
    
    // Standard Getter
    public decimal GetD7Value()
    {
        return _D7Value;
    }
    
    // Standard Setter
    public void SetD7Value(decimal value)
    {
        _D7Value = value;
    }
    
    // Get<>AsString()
    public string GetD7ValueAsString()
    {
        return _D7Value.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD7ValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D7Value = parsed;
    }
    
    // Standard Getter
    public string GetD7RpiEst()
    {
        return _D7RpiEst;
    }
    
    // Standard Setter
    public void SetD7RpiEst(string value)
    {
        _D7RpiEst = value;
    }
    
    // Get<>AsString()
    public string GetD7RpiEstAsString()
    {
        return _D7RpiEst.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD7RpiEstAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D7RpiEst = value;
    }
    
    // Standard Getter
    public D7DateAmended GetD7DateAmended()
    {
        return _D7DateAmended;
    }
    
    // Standard Setter
    public void SetD7DateAmended(D7DateAmended value)
    {
        _D7DateAmended = value;
    }
    
    // Get<>AsString()
    public string GetD7DateAmendedAsString()
    {
        return _D7DateAmended != null ? _D7DateAmended.GetD7DateAmendedAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD7DateAmendedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D7DateAmended == null)
        {
            _D7DateAmended = new D7DateAmended();
        }
        _D7DateAmended.SetD7DateAmendedAsString(value);
    }
    
    // Standard Getter
    public int GetD7UpdateCount()
    {
        return _D7UpdateCount;
    }
    
    // Standard Setter
    public void SetD7UpdateCount(int value)
    {
        _D7UpdateCount = value;
    }
    
    // Get<>AsString()
    public string GetD7UpdateCountAsString()
    {
        return _D7UpdateCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD7UpdateCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D7UpdateCount = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD7Key(string value)
    {
        _D7Key.SetD7KeyAsString(value);
    }
    // Nested Class: D7Key
    public class D7Key
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D7RpiDate, is_external=, is_static_class=False, static_prefix=
        private D7Key.D7RpiDate _D7RpiDate = new D7Key.D7RpiDate();
        
        
        
        
    public D7Key() {}
    
    public D7Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _D7RpiDate.SetD7RpiDateAsString(data.Substring(offset, D7RpiDate.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetD7KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D7RpiDate.GetD7RpiDateAsString());
        
        return result.ToString();
    }
    
    public void SetD7KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 6 <= data.Length)
        {
            _D7RpiDate.SetD7RpiDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _D7RpiDate.SetD7RpiDateAsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public D7RpiDate GetD7RpiDate()
    {
        return _D7RpiDate;
    }
    
    // Standard Setter
    public void SetD7RpiDate(D7RpiDate value)
    {
        _D7RpiDate = value;
    }
    
    // Get<>AsString()
    public string GetD7RpiDateAsString()
    {
        return _D7RpiDate != null ? _D7RpiDate.GetD7RpiDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD7RpiDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D7RpiDate == null)
        {
            _D7RpiDate = new D7RpiDate();
        }
        _D7RpiDate.SetD7RpiDateAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D7RpiDate
    public class D7RpiDate
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D7RpiDateCc, is_external=, is_static_class=False, static_prefix=
        private int _D7RpiDateCc =0;
        
        
        
        
        // [DEBUG] Field: D7RpiDateYy, is_external=, is_static_class=False, static_prefix=
        private int _D7RpiDateYy =0;
        
        
        
        
        // [DEBUG] Field: D7RpiDateMm, is_external=, is_static_class=False, static_prefix=
        private int _D7RpiDateMm =0;
        
        
        
        
    public D7RpiDate() {}
    
    public D7RpiDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD7RpiDateCc(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetD7RpiDateYy(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetD7RpiDateMm(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetD7RpiDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D7RpiDateCc.ToString().PadLeft(2, '0'));
        result.Append(_D7RpiDateYy.ToString().PadLeft(2, '0'));
        result.Append(_D7RpiDateMm.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetD7RpiDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD7RpiDateCc(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD7RpiDateYy(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD7RpiDateMm(parsedInt);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD7RpiDateCc()
    {
        return _D7RpiDateCc;
    }
    
    // Standard Setter
    public void SetD7RpiDateCc(int value)
    {
        _D7RpiDateCc = value;
    }
    
    // Get<>AsString()
    public string GetD7RpiDateCcAsString()
    {
        return _D7RpiDateCc.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD7RpiDateCcAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D7RpiDateCc = parsed;
    }
    
    // Standard Getter
    public int GetD7RpiDateYy()
    {
        return _D7RpiDateYy;
    }
    
    // Standard Setter
    public void SetD7RpiDateYy(int value)
    {
        _D7RpiDateYy = value;
    }
    
    // Get<>AsString()
    public string GetD7RpiDateYyAsString()
    {
        return _D7RpiDateYy.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD7RpiDateYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D7RpiDateYy = parsed;
    }
    
    // Standard Getter
    public int GetD7RpiDateMm()
    {
        return _D7RpiDateMm;
    }
    
    // Standard Setter
    public void SetD7RpiDateMm(int value)
    {
        _D7RpiDateMm = value;
    }
    
    // Get<>AsString()
    public string GetD7RpiDateMmAsString()
    {
        return _D7RpiDateMm.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD7RpiDateMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D7RpiDateMm = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
// Set<>String Override function (Nested)
public void SetD7DateAmended(string value)
{
    _D7DateAmended.SetD7DateAmendedAsString(value);
}
// Nested Class: D7DateAmended
public class D7DateAmended
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D7DateAmendedYy, is_external=, is_static_class=False, static_prefix=
    private int _D7DateAmendedYy =0;
    
    
    
    
    // [DEBUG] Field: D7DateAmendedMm, is_external=, is_static_class=False, static_prefix=
    private int _D7DateAmendedMm =0;
    
    
    
    
    // [DEBUG] Field: D7DateAmendedDd, is_external=, is_static_class=False, static_prefix=
    private int _D7DateAmendedDd =0;
    
    
    
    
public D7DateAmended() {}

public D7DateAmended(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD7DateAmendedYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD7DateAmendedMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD7DateAmendedDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD7DateAmendedAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D7DateAmendedYy.ToString().PadLeft(2, '0'));
    result.Append(_D7DateAmendedMm.ToString().PadLeft(2, '0'));
    result.Append(_D7DateAmendedDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD7DateAmendedAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD7DateAmendedYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD7DateAmendedMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD7DateAmendedDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetD7DateAmendedYy()
{
    return _D7DateAmendedYy;
}

// Standard Setter
public void SetD7DateAmendedYy(int value)
{
    _D7DateAmendedYy = value;
}

// Get<>AsString()
public string GetD7DateAmendedYyAsString()
{
    return _D7DateAmendedYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD7DateAmendedYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D7DateAmendedYy = parsed;
}

// Standard Getter
public int GetD7DateAmendedMm()
{
    return _D7DateAmendedMm;
}

// Standard Setter
public void SetD7DateAmendedMm(int value)
{
    _D7DateAmendedMm = value;
}

// Get<>AsString()
public string GetD7DateAmendedMmAsString()
{
    return _D7DateAmendedMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD7DateAmendedMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D7DateAmendedMm = parsed;
}

// Standard Getter
public int GetD7DateAmendedDd()
{
    return _D7DateAmendedDd;
}

// Standard Setter
public void SetD7DateAmendedDd(int value)
{
    _D7DateAmendedDd = value;
}

// Get<>AsString()
public string GetD7DateAmendedDdAsString()
{
    return _D7DateAmendedDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD7DateAmendedDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D7DateAmendedDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}

}}