using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D69Record Data Structure

public class D69Record
{
    private static int _size = 181;
    // [DEBUG] Class: D69Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D69PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D69PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D69ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D69ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD69RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D69PrintControl.PadRight(1));
        result.Append(_D69ReportLine.PadRight(180));
        
        return result.ToString();
    }
    
    public void SetD69RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD69PrintControl(extracted);
        }
        offset += 1;
        if (offset + 180 <= data.Length)
        {
            string extracted = data.Substring(offset, 180).Trim();
            SetD69ReportLine(extracted);
        }
        offset += 180;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD69RecordAsString();
    }
    // Set<>String Override function
    public void SetD69Record(string value)
    {
        SetD69RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD69PrintControl()
    {
        return _D69PrintControl;
    }
    
    // Standard Setter
    public void SetD69PrintControl(string value)
    {
        _D69PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD69PrintControlAsString()
    {
        return _D69PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD69PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D69PrintControl = value;
    }
    
    // Standard Getter
    public string GetD69ReportLine()
    {
        return _D69ReportLine;
    }
    
    // Standard Setter
    public void SetD69ReportLine(string value)
    {
        _D69ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD69ReportLineAsString()
    {
        return _D69ReportLine.PadRight(180);
    }
    
    // Set<>AsString()
    public void SetD69ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D69ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}