using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt179HsDTO
{// DTO class representing Cgtdate2LinkageDate6 Data Structure

public class Cgtdate2LinkageDate6
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate6, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd6 =0;
    
    
    
    
    // [DEBUG] Field: Filler64, is_external=, is_static_class=False, static_prefix=
    private Filler64 _Filler64 = new Filler64();
    
    
    
    
    // [DEBUG] Field: Filler65, is_external=, is_static_class=False, static_prefix=
    private Filler65 _Filler65 = new Filler65();
    
    
    
    
    // [DEBUG] Field: Filler66, is_external=, is_static_class=False, static_prefix=
    private Filler66 _Filler66 = new Filler66();
    
    
    
    
    // [DEBUG] Field: Filler67, is_external=, is_static_class=False, static_prefix=
    private Filler67 _Filler67 = new Filler67();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate6AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd6.ToString().PadLeft(8, '0'));
        result.Append(_Filler64.GetFiller64AsString());
        result.Append(_Filler65.GetFiller65AsString());
        result.Append(_Filler66.GetFiller66AsString());
        result.Append(_Filler67.GetFiller67AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate6AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd6(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler64.SetFiller64AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler64.SetFiller64AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler65.SetFiller65AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler65.SetFiller65AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler66.SetFiller66AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler66.SetFiller66AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler67.SetFiller67AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler67.SetFiller67AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate6AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate6(string value)
    {
        SetCgtdate2LinkageDate6AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd6()
    {
        return _Cgtdate2Ccyymmdd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd6(int value)
    {
        _Cgtdate2Ccyymmdd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd6AsString()
    {
        return _Cgtdate2Ccyymmdd6.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd6 = parsed;
    }
    
    // Standard Getter
    public Filler64 GetFiller64()
    {
        return _Filler64;
    }
    
    // Standard Setter
    public void SetFiller64(Filler64 value)
    {
        _Filler64 = value;
    }
    
    // Get<>AsString()
    public string GetFiller64AsString()
    {
        return _Filler64 != null ? _Filler64.GetFiller64AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller64AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler64 == null)
        {
            _Filler64 = new Filler64();
        }
        _Filler64.SetFiller64AsString(value);
    }
    
    // Standard Getter
    public Filler65 GetFiller65()
    {
        return _Filler65;
    }
    
    // Standard Setter
    public void SetFiller65(Filler65 value)
    {
        _Filler65 = value;
    }
    
    // Get<>AsString()
    public string GetFiller65AsString()
    {
        return _Filler65 != null ? _Filler65.GetFiller65AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller65AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler65 == null)
        {
            _Filler65 = new Filler65();
        }
        _Filler65.SetFiller65AsString(value);
    }
    
    // Standard Getter
    public Filler66 GetFiller66()
    {
        return _Filler66;
    }
    
    // Standard Setter
    public void SetFiller66(Filler66 value)
    {
        _Filler66 = value;
    }
    
    // Get<>AsString()
    public string GetFiller66AsString()
    {
        return _Filler66 != null ? _Filler66.GetFiller66AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller66AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler66 == null)
        {
            _Filler66 = new Filler66();
        }
        _Filler66.SetFiller66AsString(value);
    }
    
    // Standard Getter
    public Filler67 GetFiller67()
    {
        return _Filler67;
    }
    
    // Standard Setter
    public void SetFiller67(Filler67 value)
    {
        _Filler67 = value;
    }
    
    // Get<>AsString()
    public string GetFiller67AsString()
    {
        return _Filler67 != null ? _Filler67.GetFiller67AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller67AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler67 == null)
        {
            _Filler67 = new Filler67();
        }
        _Filler67.SetFiller67AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller64(string value)
    {
        _Filler64.SetFiller64AsString(value);
    }
    // Nested Class: Filler64
    public class Filler64
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc6 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd6, is_external=, is_static_class=False, static_prefix=
        private Filler64.Cgtdate2Yymmdd6 _Cgtdate2Yymmdd6 = new Filler64.Cgtdate2Yymmdd6();
        
        
        
        
    public Filler64() {}
    
    public Filler64(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc6(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(data.Substring(offset, Cgtdate2Yymmdd6.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller64AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc6.PadRight(2));
        result.Append(_Cgtdate2Yymmdd6.GetCgtdate2Yymmdd6AsString());
        
        return result.ToString();
    }
    
    public void SetFiller64AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc6(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc6()
    {
        return _Cgtdate2Cc6;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc6(string value)
    {
        _Cgtdate2Cc6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc6AsString()
    {
        return _Cgtdate2Cc6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc6 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd6 GetCgtdate2Yymmdd6()
    {
        return _Cgtdate2Yymmdd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd6(Cgtdate2Yymmdd6 value)
    {
        _Cgtdate2Yymmdd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd6AsString()
    {
        return _Cgtdate2Yymmdd6 != null ? _Cgtdate2Yymmdd6.GetCgtdate2Yymmdd6AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd6 == null)
        {
            _Cgtdate2Yymmdd6 = new Cgtdate2Yymmdd6();
        }
        _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd6
    public class Cgtdate2Yymmdd6
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy6 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd6, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd6.Cgtdate2Mmdd6 _Cgtdate2Mmdd6 = new Cgtdate2Yymmdd6.Cgtdate2Mmdd6();
        
        
        
        
    public Cgtdate2Yymmdd6() {}
    
    public Cgtdate2Yymmdd6(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy6(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(data.Substring(offset, Cgtdate2Mmdd6.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd6AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy6.PadRight(2));
        result.Append(_Cgtdate2Mmdd6.GetCgtdate2Mmdd6AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd6AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy6(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy6()
    {
        return _Cgtdate2Yy6;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy6(string value)
    {
        _Cgtdate2Yy6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy6AsString()
    {
        return _Cgtdate2Yy6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy6 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd6 GetCgtdate2Mmdd6()
    {
        return _Cgtdate2Mmdd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd6(Cgtdate2Mmdd6 value)
    {
        _Cgtdate2Mmdd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd6AsString()
    {
        return _Cgtdate2Mmdd6 != null ? _Cgtdate2Mmdd6.GetCgtdate2Mmdd6AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd6 == null)
        {
            _Cgtdate2Mmdd6 = new Cgtdate2Mmdd6();
        }
        _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd6
    public class Cgtdate2Mmdd6
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm6 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd6 ="";
        
        
        
        
    public Cgtdate2Mmdd6() {}
    
    public Cgtdate2Mmdd6(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm6(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd6(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd6AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm6.PadRight(2));
        result.Append(_Cgtdate2Dd6.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd6AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm6(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd6(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm6()
    {
        return _Cgtdate2Mm6;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm6(string value)
    {
        _Cgtdate2Mm6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm6AsString()
    {
        return _Cgtdate2Mm6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm6 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd6()
    {
        return _Cgtdate2Dd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd6(string value)
    {
        _Cgtdate2Dd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd6AsString()
    {
        return _Cgtdate2Dd6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd6 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller65(string value)
{
    _Filler65.SetFiller65AsString(value);
}
// Nested Class: Filler65
public class Filler65
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd6 =0;
    
    
    
    
public Filler65() {}

public Filler65(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy6(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd6(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller65AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy6.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd6.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller65AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy6(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd6(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy6()
{
    return _Cgtdate2CCcyy6;
}

// Standard Setter
public void SetCgtdate2CCcyy6(int value)
{
    _Cgtdate2CCcyy6 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy6AsString()
{
    return _Cgtdate2CCcyy6.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy6 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd6()
{
    return _Cgtdate2CMmdd6;
}

// Standard Setter
public void SetCgtdate2CMmdd6(int value)
{
    _Cgtdate2CMmdd6 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd6AsString()
{
    return _Cgtdate2CMmdd6.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd6 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller66(string value)
{
    _Filler66.SetFiller66AsString(value);
}
// Nested Class: Filler66
public class Filler66
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd6 =0;
    
    
    
    
public Filler66() {}

public Filler66(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller66AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc6.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy6.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm6.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd6.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller66AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc6(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy6(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm6(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd6(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc6()
{
    return _Cgtdate2CCc6;
}

// Standard Setter
public void SetCgtdate2CCc6(int value)
{
    _Cgtdate2CCc6 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc6AsString()
{
    return _Cgtdate2CCc6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc6 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy6()
{
    return _Cgtdate2CYy6;
}

// Standard Setter
public void SetCgtdate2CYy6(int value)
{
    _Cgtdate2CYy6 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy6AsString()
{
    return _Cgtdate2CYy6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy6 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm6()
{
    return _Cgtdate2CMm6;
}

// Standard Setter
public void SetCgtdate2CMm6(int value)
{
    _Cgtdate2CMm6 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm6AsString()
{
    return _Cgtdate2CMm6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm6 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd6()
{
    return _Cgtdate2CDd6;
}

// Standard Setter
public void SetCgtdate2CDd6(int value)
{
    _Cgtdate2CDd6 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd6AsString()
{
    return _Cgtdate2CDd6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd6 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller67(string value)
{
    _Filler67.SetFiller67AsString(value);
}
// Nested Class: Filler67
public class Filler67
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm6 =0;
    
    
    
    
    // [DEBUG] Field: Filler68, is_external=, is_static_class=False, static_prefix=
    private string _Filler68 ="";
    
    
    
    
public Filler67() {}

public Filler67(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm6(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller68(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller67AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm6.ToString().PadLeft(6, '0'));
    result.Append(_Filler68.PadRight(2));
    
    return result.ToString();
}

public void SetFiller67AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm6(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller68(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm6()
{
    return _Cgtdate2CCcyymm6;
}

// Standard Setter
public void SetCgtdate2CCcyymm6(int value)
{
    _Cgtdate2CCcyymm6 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm6AsString()
{
    return _Cgtdate2CCcyymm6.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm6 = parsed;
}

// Standard Getter
public string GetFiller68()
{
    return _Filler68;
}

// Standard Setter
public void SetFiller68(string value)
{
    _Filler68 = value;
}

// Get<>AsString()
public string GetFiller68AsString()
{
    return _Filler68.PadRight(2);
}

// Set<>AsString()
public void SetFiller68AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler68 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}