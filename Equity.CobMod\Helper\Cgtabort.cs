using System;
using System.Text;
using EquityProject.CgtabortDTO;
using EquityProject.EqtdebugPGM;
namespace EquityProject.CgtabortPGM
{
    // Cgtabort Class Definition

    //Cgtabort Class Constructor
    public class Cgtabort
    {
        // Declare Cgtabort Class private variables
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();

        // Declare {program_name} Class getters setters
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        /// <summary>
        /// Helper method to call external subroutines
        /// </summary>
        /// <param name="ivar">The Ivar parameter to pass to the subroutine</param>
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
        }


        // Run() method
        public void Run(Gvar gvar, Ivar ivar)
        {
            // Call the main entry point
            // No mainline procedure found, add your entry point here
            Xlogerror(gvar, ivar);
        }

        // Methods representing paragraphs under procedure division
        /// <summary>
        /// xLogError
        /// </summary>
        /// <remarks>
        /// COBOL paragraph xLogError converted to C# method Xlogerror
        /// </remarks>
        public void Xlogerror(Gvar gvar, Ivar ivar)
        {
            // MOVE ERROR-LOG-FILE TO L-FILE-NAME
            gvar.GetCgtfilesLinkage().SetLFileName(Ivar.ERROR_LOG_FILE);

            // MOVE OPEN-EXTEND TO L-FILE-ACTION
            gvar.GetCgtfilesLinkage().SetLFileAction(Ivar.OPEN_EXTEND);

            // PERFORM X-CALL-CGTFILES
            Xcallcgtfiles(gvar, ivar);

            // PERFORM X-CALL-CGTABORT-ERROR
            Xlogcgtaborterror(gvar, ivar);

            // MOVE EQUITY-RETURN-CODE TO EQUITY-STATUS
            gvar.GetEquityStatusRecord().SetEquityStatus(gvar.GetEquityParameters().GetEquityGeneralParms().GetEquityReturnCode());

            // MOVE 'E' TO EQUITY-ERROR-WARNING-FLAG
            gvar.GetEquityStatusRecord().SetEquityErrorWarningFlag("E");

            // MOVE W-ERROR-MESSAGE2 TO EQUITY-MESSAGE
            gvar.GetEquityStatusRecord().SetEquityMessage(gvar.GetWErrorMessage2());

            // MOVE WRITE-RECORD TO L-FILE-ACTION
            gvar.GetCgtfilesLinkage().SetLFileAction(Ivar.WRITE_RECORD);

            // MOVE EQUITY-STATUS-RECORD TO L-FILE-RECORD-AREA
            gvar.SetLFileRecordAreaAsString(gvar.GetEquityStatusRecordAsString());

            // PERFORM X-CALL-CGTFILES
            Xcallcgtfiles(gvar, ivar);

            // PERFORM X-CALL-CGTABORT-ERROR
            Xlogcgtaborterror(gvar,ivar);

            // MOVE CLOSE-FILE TO L-FILE-ACTION
            gvar.GetCgtfilesLinkage().SetLFileAction(Ivar.CLOSE_FILE);

            // PERFORM X-CALL-CGTFILES
            Xcallcgtfiles(gvar,ivar);

            // PERFORM X-CALL-CGTABORT-ERROR
            Xcallcgtaborterror(gvar,ivar);
        }
        /// <summary>
        /// xCallCgtabortError
        /// </summary>
        /// <remarks>
        /// COBOL paragraph xCallCgtabortError converted to C# method Xcallcgtaborterror
        /// </remarks>
        public void Xcallcgtaborterror(Gvar gvar, Ivar ivar)
        {
            // IF SUCCESSFUL OR ( L-FILE-ACTION = OPEN-EXTEND AND OPEN-OK )
            if (gvar.GetCgtfilesLinkage().IsSuccessful() || (gvar.GetCgtfilesLinkage().GetLFileAction().Equals(Ivar.OPEN_EXTEND) && gvar.GetCgtfilesLinkage().IsOpenOk()))
            {
                // IF L-FILE-ACTION NOT = OPEN-EXTEND
                if (!gvar.GetCgtfilesLinkage().GetLFileAction().Equals(Ivar.OPEN_EXTEND))
                {
                    // PERFORM X-LOG-CGTABORT-ERROR
                    Xlogcgtaborterror(gvar, ivar);  // Simulating the PERFORM statement as there's no direct equivalent
                }
            }
        }
        /// <summary>
        /// xLogCgtabortError
        /// </summary>
        /// <remarks>
        /// COBOL paragraph xLogCgtabortError converted to C# method Xlogcgtaborterror
        /// </remarks>
        public void Xlogcgtaborterror(Gvar gvar, Ivar ivar)
        {
            // STRING   '*** Error EQTABORT cannot log error, status '
            string eqtdebugText = "*** Error EQTABORT cannot log error, status ";

            // L-FILE-RETURN-CODE
            eqtdebugText += gvar.GetCgtfilesLinkage().GetLFileReturnCode();

            // ' action '
            eqtdebugText += " action ";

            // L-FILE-ACTION
            eqtdebugText += gvar.GetCgtfilesLinkage().GetLFileAction();

            // ' message:'
            eqtdebugText += " message:";

            // DELIMITED   BY   SIZE
            // INTO   EQTDEBUG-TEXT
            gvar.GetEqtdebugLinkage().SetEqtdebugText(eqtdebugText);

            // PERFORM   X-CALL-EQTDEBUG.
            Xcalleqtdebug(gvar, ivar);

            // MOVE   W-ERROR-MESSAGE2   TO   EQTDEBUG-TEXT
            gvar.GetEqtdebugLinkage().SetEqtdebugText(gvar.GetWErrorMessage2());

            // PERFORM   X-CALL-EQTDEBUG.
            Xcalleqtdebug(gvar, ivar);

            // MOVE   '***'   TO   EQTDEBUG-TEXT
            gvar.GetEqtdebugLinkage().SetEqtdebugText("***");

            // PERFORM   X-CALL-EQTDEBUG.
            Xcalleqtdebug(gvar,ivar);
        }
        /// <summary>
        /// xCallEqtdebug
        /// </summary>
        /// <remarks>
        /// This method calls the EQTDEBUG program using the EQTDEBUG-LINKAGE parameter.
        /// </remarks>
        public void Xcalleqtdebug(Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the EQTDEBUG program
            Eqtdebug eqtdebug = new Eqtdebug();
            eqtdebug.GetIvar().SetEqtdebugLinkageAsString(gvar.GetEqtdebugLinkageAsString());
            // Call the Run method with the EQTDEBUG-LINKAGE parameter
            eqtdebug.Run(eqtdebug.GetGvar(),eqtdebug.GetIvar(),new EquityGlobalParmsDTO.EquityGlobalParms());
        }
      
        /// <summary>
        /// xCallCgtfiles
        /// </summary>
        /// <remarks>
        /// COBOL paragraph xCallCgtfiles converted to C# method Xcallcgtfiles
        /// </remarks>
        public void Xcallcgtfiles(Gvar gvar, Ivar ivar)
        {
            // CALL   'CGTFILES'   USING   CGTFILES-LINKAGE
            //                         L-FILE-RECORD-AREA
            //                         COMMON-LINKAGE.
       /*     Cgtfiles cgtfiles = new Cgtfiles();
            cgtfiles.GetIvar().SetCgtfilesLinkageAsString(gvar.GetCgtfilesLinkageAsString());
            cgtfiles.GetIvar().SetLFileRecordAreaAsString(gvar.GetLFileRecordAreaAsString());
            cgtfiles.GetIvar().SetCommonLinkageAsString(ivar.GetCommonLinkageAsString());
           
            cgtfiles.Run(cgtfiles.GetFvar(),cgtfiles.GetGvar(), cgtfiles.GetIvar());
      */  }

    }
}
