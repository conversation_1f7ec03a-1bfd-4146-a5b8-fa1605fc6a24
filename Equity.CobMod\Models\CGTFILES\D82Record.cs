using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D82Record Data Structure

public class D82Record
{
    private static int _size = 158;
    // [DEBUG] Class: D82Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D82PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D82PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D82ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D82ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD82RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D82PrintControl.PadRight(1));
        result.Append(_D82ReportLine.PadRight(157));
        
        return result.ToString();
    }
    
    public void SetD82RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD82PrintControl(extracted);
        }
        offset += 1;
        if (offset + 157 <= data.Length)
        {
            string extracted = data.Substring(offset, 157).Trim();
            SetD82ReportLine(extracted);
        }
        offset += 157;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD82RecordAsString();
    }
    // Set<>String Override function
    public void SetD82Record(string value)
    {
        SetD82RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD82PrintControl()
    {
        return _D82PrintControl;
    }
    
    // Standard Setter
    public void SetD82PrintControl(string value)
    {
        _D82PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD82PrintControlAsString()
    {
        return _D82PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD82PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D82PrintControl = value;
    }
    
    // Standard Getter
    public string GetD82ReportLine()
    {
        return _D82ReportLine;
    }
    
    // Standard Setter
    public void SetD82ReportLine(string value)
    {
        _D82ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD82ReportLineAsString()
    {
        return _D82ReportLine.PadRight(157);
    }
    
    // Set<>AsString()
    public void SetD82ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D82ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}