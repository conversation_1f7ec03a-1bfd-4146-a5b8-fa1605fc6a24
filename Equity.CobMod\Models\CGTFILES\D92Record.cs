using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D92Record Data Structure

public class D92Record
{
    private static int _size = 151;
    // [DEBUG] Class: D92Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D92PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D92PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D92ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D92ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD92RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D92PrintControl.PadRight(1));
        result.Append(_D92ReportLine.PadRight(150));
        
        return result.ToString();
    }
    
    public void SetD92RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD92PrintControl(extracted);
        }
        offset += 1;
        if (offset + 150 <= data.Length)
        {
            string extracted = data.Substring(offset, 150).Trim();
            SetD92ReportLine(extracted);
        }
        offset += 150;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD92RecordAsString();
    }
    // Set<>String Override function
    public void SetD92Record(string value)
    {
        SetD92RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD92PrintControl()
    {
        return _D92PrintControl;
    }
    
    // Standard Setter
    public void SetD92PrintControl(string value)
    {
        _D92PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD92PrintControlAsString()
    {
        return _D92PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD92PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D92PrintControl = value;
    }
    
    // Standard Getter
    public string GetD92ReportLine()
    {
        return _D92ReportLine;
    }
    
    // Standard Setter
    public void SetD92ReportLine(string value)
    {
        _D92ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD92ReportLineAsString()
    {
        return _D92ReportLine.PadRight(150);
    }
    
    // Set<>AsString()
    public void SetD92ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D92ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}