using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing OffshoreRecordDd Data Structure

public class OffshoreRecordDd
{
    private static int _size = 165;
    // [DEBUG] Class: OffshoreRecordDd, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler2, is_external=, is_static_class=False, static_prefix=
    private string _Filler2 ="";
    
    
    
    
    // [DEBUG] Field: OffshRecDd, is_external=, is_static_class=False, static_prefix=
    private string _OffshRecDd ="";
    
    
    
    
    
    // Serialization methods
    public string GetOffshoreRecordDdAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler2.PadRight(1));
        result.Append(_OffshRecDd.PadRight(164));
        
        return result.ToString();
    }
    
    public void SetOffshoreRecordDdAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller2(extracted);
        }
        offset += 1;
        if (offset + 164 <= data.Length)
        {
            string extracted = data.Substring(offset, 164).Trim();
            SetOffshRecDd(extracted);
        }
        offset += 164;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetOffshoreRecordDdAsString();
    }
    // Set<>String Override function
    public void SetOffshoreRecordDd(string value)
    {
        SetOffshoreRecordDdAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller2()
    {
        return _Filler2;
    }
    
    // Standard Setter
    public void SetFiller2(string value)
    {
        _Filler2 = value;
    }
    
    // Get<>AsString()
    public string GetFiller2AsString()
    {
        return _Filler2.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler2 = value;
    }
    
    // Standard Getter
    public string GetOffshRecDd()
    {
        return _OffshRecDd;
    }
    
    // Standard Setter
    public void SetOffshRecDd(string value)
    {
        _OffshRecDd = value;
    }
    
    // Get<>AsString()
    public string GetOffshRecDdAsString()
    {
        return _OffshRecDd.PadRight(164);
    }
    
    // Set<>AsString()
    public void SetOffshRecDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _OffshRecDd = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}