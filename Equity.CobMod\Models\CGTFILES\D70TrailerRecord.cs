using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D70TrailerRecord Data Structure

public class D70TrailerRecord
{
    private static int _size = 63;
    // [DEBUG] Class: D70TrailerRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler83, is_external=, is_static_class=False, static_prefix=
    private string _Filler83 ="";
    
    
    // 88-level condition checks for Filler83
    public bool IsD70TrailerFound()
    {
        if (this._Filler83 == "'9999999MICRO CGT PRICE TRL'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D70TrailerCount, is_external=, is_static_class=False, static_prefix=
    private int _D70TrailerCount =0;
    
    
    
    
    // [DEBUG] Field: Filler84, is_external=, is_static_class=False, static_prefix=
    private string _Filler84 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD70TrailerRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler83.PadRight(27));
        result.Append(_D70TrailerCount.ToString().PadLeft(6, '0'));
        result.Append(_Filler84.PadRight(30));
        
        return result.ToString();
    }
    
    public void SetD70TrailerRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 27 <= data.Length)
        {
            string extracted = data.Substring(offset, 27).Trim();
            SetFiller83(extracted);
        }
        offset += 27;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD70TrailerCount(parsedInt);
        }
        offset += 6;
        if (offset + 30 <= data.Length)
        {
            string extracted = data.Substring(offset, 30).Trim();
            SetFiller84(extracted);
        }
        offset += 30;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD70TrailerRecordAsString();
    }
    // Set<>String Override function
    public void SetD70TrailerRecord(string value)
    {
        SetD70TrailerRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller83()
    {
        return _Filler83;
    }
    
    // Standard Setter
    public void SetFiller83(string value)
    {
        _Filler83 = value;
    }
    
    // Get<>AsString()
    public string GetFiller83AsString()
    {
        return _Filler83.PadRight(27);
    }
    
    // Set<>AsString()
    public void SetFiller83AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler83 = value;
    }
    
    // Standard Getter
    public int GetD70TrailerCount()
    {
        return _D70TrailerCount;
    }
    
    // Standard Setter
    public void SetD70TrailerCount(int value)
    {
        _D70TrailerCount = value;
    }
    
    // Get<>AsString()
    public string GetD70TrailerCountAsString()
    {
        return _D70TrailerCount.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD70TrailerCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D70TrailerCount = parsed;
    }
    
    // Standard Getter
    public string GetFiller84()
    {
        return _Filler84;
    }
    
    // Standard Setter
    public void SetFiller84(string value)
    {
        _Filler84 = value;
    }
    
    // Get<>AsString()
    public string GetFiller84AsString()
    {
        return _Filler84.PadRight(30);
    }
    
    // Set<>AsString()
    public void SetFiller84AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler84 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}