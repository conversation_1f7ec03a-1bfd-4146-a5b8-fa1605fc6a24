using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WsWiNormGtTranches Data Structure

public class WsWiNormGtTranches
{
    private static int _size = 45;
    // [DEBUG] Class: WsWiNormGtTranches, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler374, is_external=, is_static_class=False, static_prefix=
    private string _Filler374 ="NORMAL-GT-TRNCH=";
    
    
    
    
    // [DEBUG] Field: WsWiNormMaxtablesize, is_external=, is_static_class=False, static_prefix=
    private int _WsWiNormMaxtablesize =4000;
    
    
    
    
    // [DEBUG] Field: WsWiNormOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WsWiNormOccurs =4000;
    
    
    
    
    // [DEBUG] Field: WsWiNormTable, is_external=, is_static_class=False, static_prefix=
    private WsWiNormTable _WsWiNormTable = new WsWiNormTable();
    
    
    
    
    
    // Serialization methods
    public string GetWsWiNormGtTranchesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler374.PadRight(16));
        result.Append(_WsWiNormMaxtablesize.ToString().PadLeft(4, '0'));
        result.Append(_WsWiNormOccurs.ToString().PadLeft(4, '0'));
        result.Append(_WsWiNormTable.GetWsWiNormTableAsString());
        
        return result.ToString();
    }
    
    public void SetWsWiNormGtTranchesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller374(extracted);
        }
        offset += 16;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsWiNormMaxtablesize(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsWiNormOccurs(parsedInt);
        }
        offset += 4;
        if (offset + 21 <= data.Length)
        {
            _WsWiNormTable.SetWsWiNormTableAsString(data.Substring(offset, 21));
        }
        else
        {
            _WsWiNormTable.SetWsWiNormTableAsString(data.Substring(offset));
        }
        offset += 21;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsWiNormGtTranchesAsString();
    }
    // Set<>String Override function
    public void SetWsWiNormGtTranches(string value)
    {
        SetWsWiNormGtTranchesAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller374()
    {
        return _Filler374;
    }
    
    // Standard Setter
    public void SetFiller374(string value)
    {
        _Filler374 = value;
    }
    
    // Get<>AsString()
    public string GetFiller374AsString()
    {
        return _Filler374.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller374AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler374 = value;
    }
    
    // Standard Getter
    public int GetWsWiNormMaxtablesize()
    {
        return _WsWiNormMaxtablesize;
    }
    
    // Standard Setter
    public void SetWsWiNormMaxtablesize(int value)
    {
        _WsWiNormMaxtablesize = value;
    }
    
    // Get<>AsString()
    public string GetWsWiNormMaxtablesizeAsString()
    {
        return _WsWiNormMaxtablesize.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsWiNormMaxtablesizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsWiNormMaxtablesize = parsed;
    }
    
    // Standard Getter
    public int GetWsWiNormOccurs()
    {
        return _WsWiNormOccurs;
    }
    
    // Standard Setter
    public void SetWsWiNormOccurs(int value)
    {
        _WsWiNormOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWsWiNormOccursAsString()
    {
        return _WsWiNormOccurs.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsWiNormOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsWiNormOccurs = parsed;
    }
    
    // Standard Getter
    public WsWiNormTable GetWsWiNormTable()
    {
        return _WsWiNormTable;
    }
    
    // Standard Setter
    public void SetWsWiNormTable(WsWiNormTable value)
    {
        _WsWiNormTable = value;
    }
    
    // Get<>AsString()
    public string GetWsWiNormTableAsString()
    {
        return _WsWiNormTable != null ? _WsWiNormTable.GetWsWiNormTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsWiNormTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsWiNormTable == null)
        {
            _WsWiNormTable = new WsWiNormTable();
        }
        _WsWiNormTable.SetWsWiNormTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWsWiNormTable(string value)
    {
        _WsWiNormTable.SetWsWiNormTableAsString(value);
    }
    // Nested Class: WsWiNormTable
    public class WsWiNormTable
    {
        private static int _size = 21;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WsWiNormElement, is_external=, is_static_class=False, static_prefix=
        private WsWiNormTable.WsWiNormElement[] _WsWiNormElement = new WsWiNormTable.WsWiNormElement[4000];
        
        public void InitializeWsWiNormElementArray()
        {
            for (int i = 0; i < 4000; i++)
            {
                _WsWiNormElement[i] = new WsWiNormTable.WsWiNormElement();
            }
        }
        
        
        
    public WsWiNormTable() {}
    
    public WsWiNormTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWsWiNormElementArray();
        for (int i = 0; i < 4000; i++)
        {
            _WsWiNormElement[i].SetWsWiNormElementAsString(data.Substring(offset, 21));
            offset += 21;
        }
        
    }
    
    // Serialization methods
    public string GetWsWiNormTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 4000; i++)
        {
            result.Append(_WsWiNormElement[i].GetWsWiNormElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWsWiNormTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 4000; i++)
        {
            if (offset + 21 > data.Length) break;
            string val = data.Substring(offset, 21);
            
            _WsWiNormElement[i].SetWsWiNormElementAsString(val);
            offset += 21;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WsWiNormElement
    public WsWiNormElement GetWsWiNormElementAt(int index)
    {
        return _WsWiNormElement[index];
    }
    
    public void SetWsWiNormElementAt(int index, WsWiNormElement value)
    {
        _WsWiNormElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WsWiNormElement GetWsWiNormElement()
    {
        return _WsWiNormElement != null && _WsWiNormElement.Length > 0
        ? _WsWiNormElement[0]
        : new WsWiNormElement();
    }
    
    public void SetWsWiNormElement(WsWiNormElement value)
    {
        if (_WsWiNormElement == null || _WsWiNormElement.Length == 0)
        _WsWiNormElement = new WsWiNormElement[1];
        _WsWiNormElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WsWiNormElement
    public class WsWiNormElement
    {
        private static int _size = 21;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WsWiNormal, is_external=, is_static_class=False, static_prefix=
        private string _WsWiNormal ="";
        
        
        
        
        // [DEBUG] Field: WsWiNormalIndxn, is_external=, is_static_class=False, static_prefix=
        private decimal _WsWiNormalIndxn =0;
        
        
        
        
        // [DEBUG] Field: WsWiIndxnFlag, is_external=, is_static_class=False, static_prefix=
        private string _WsWiIndxnFlag ="";
        
        
        // 88-level condition checks for WsWiIndxnFlag
        public bool IsWsWiIndxnNotSet()
        {
            if (this._WsWiIndxnFlag == "'N'") return true;
            return false;
        }
        public bool IsWsWiIndxnSet()
        {
            if (this._WsWiIndxnFlag == "'S'") return true;
            return false;
        }
        
        
    public WsWiNormElement() {}
    
    public WsWiNormElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWsWiNormal(data.Substring(offset, 4).Trim());
        offset += 4;
        SetWsWiNormalIndxn(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
        offset += 17;
        SetWsWiIndxnFlag(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetWsWiNormElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsWiNormal.PadRight(4));
        result.Append(_WsWiNormalIndxn.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsWiIndxnFlag.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetWsWiNormElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWsWiNormal(extracted);
        }
        offset += 4;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsWiNormalIndxn(parsedDec);
        }
        offset += 17;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWsWiIndxnFlag(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsWiNormal()
    {
        return _WsWiNormal;
    }
    
    // Standard Setter
    public void SetWsWiNormal(string value)
    {
        _WsWiNormal = value;
    }
    
    // Get<>AsString()
    public string GetWsWiNormalAsString()
    {
        return _WsWiNormal.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWsWiNormalAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsWiNormal = value;
    }
    
    // Standard Getter
    public decimal GetWsWiNormalIndxn()
    {
        return _WsWiNormalIndxn;
    }
    
    // Standard Setter
    public void SetWsWiNormalIndxn(decimal value)
    {
        _WsWiNormalIndxn = value;
    }
    
    // Get<>AsString()
    public string GetWsWiNormalIndxnAsString()
    {
        return _WsWiNormalIndxn.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsWiNormalIndxnAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsWiNormalIndxn = parsed;
    }
    
    // Standard Getter
    public string GetWsWiIndxnFlag()
    {
        return _WsWiIndxnFlag;
    }
    
    // Standard Setter
    public void SetWsWiIndxnFlag(string value)
    {
        _WsWiIndxnFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsWiIndxnFlagAsString()
    {
        return _WsWiIndxnFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWsWiIndxnFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsWiIndxnFlag = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}

}}
