using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// DTO class representing Filler112 Data Structure

public class Filler112
{
    private static int _size = 81;
    // [DEBUG] Class: Filler112, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: YearEndIds, is_external=, is_static_class=False, static_prefix=
    private YearEndIds _YearEndIds = new YearEndIds();
    
    
    
    
    // [DEBUG] Field: Filler120, is_external=, is_static_class=False, static_prefix=
    private string _Filler120 ="";
    
    
    
    
    // [DEBUG] Field: Filler121, is_external=, is_static_class=False, static_prefix=
    private string _Filler121 ="";
    
    
    
    
    
    // Serialization methods
    public string GetFiller112AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_YearEndIds.GetYearEndIdsAsString());
        result.Append(_Filler120.PadRight(24));
        result.Append(_Filler121.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetFiller112AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 56 <= data.Length)
        {
            _YearEndIds.SetYearEndIdsAsString(data.Substring(offset, 56));
        }
        else
        {
            _YearEndIds.SetYearEndIdsAsString(data.Substring(offset));
        }
        offset += 56;
        if (offset + 24 <= data.Length)
        {
            string extracted = data.Substring(offset, 24).Trim();
            SetFiller120(extracted);
        }
        offset += 24;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller121(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetFiller112AsString();
    }
    // Set<>String Override function
    public void SetFiller112(string value)
    {
        SetFiller112AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public YearEndIds GetYearEndIds()
    {
        return _YearEndIds;
    }
    
    // Standard Setter
    public void SetYearEndIds(YearEndIds value)
    {
        _YearEndIds = value;
    }
    
    // Get<>AsString()
    public string GetYearEndIdsAsString()
    {
        return _YearEndIds != null ? _YearEndIds.GetYearEndIdsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetYearEndIdsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_YearEndIds == null)
        {
            _YearEndIds = new YearEndIds();
        }
        _YearEndIds.SetYearEndIdsAsString(value);
    }
    
    // Standard Getter
    public string GetFiller120()
    {
        return _Filler120;
    }
    
    // Standard Setter
    public void SetFiller120(string value)
    {
        _Filler120 = value;
    }
    
    // Get<>AsString()
    public string GetFiller120AsString()
    {
        return _Filler120.PadRight(24);
    }
    
    // Set<>AsString()
    public void SetFiller120AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler120 = value;
    }
    
    // Standard Getter
    public string GetFiller121()
    {
        return _Filler121;
    }
    
    // Standard Setter
    public void SetFiller121(string value)
    {
        _Filler121 = value;
    }
    
    // Get<>AsString()
    public string GetFiller121AsString()
    {
        return _Filler121.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller121AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler121 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetYearEndIds(string value)
    {
        _YearEndIds.SetYearEndIdsAsString(value);
    }
    // Nested Class: YearEndIds
    public class YearEndIds
    {
        private static int _size = 56;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler113, is_external=, is_static_class=False, static_prefix=
        private string _Filler113 ="";
        
        
        
        
        // [DEBUG] Field: Filler114, is_external=, is_static_class=False, static_prefix=
        private string _Filler114 ="";
        
        
        
        
        // [DEBUG] Field: Filler115, is_external=, is_static_class=False, static_prefix=
        private string _Filler115 ="";
        
        
        
        
        // [DEBUG] Field: Filler116, is_external=, is_static_class=False, static_prefix=
        private string _Filler116 ="";
        
        
        
        
        // [DEBUG] Field: Filler117, is_external=, is_static_class=False, static_prefix=
        private string _Filler117 ="";
        
        
        
        
        // [DEBUG] Field: Filler118, is_external=, is_static_class=False, static_prefix=
        private string _Filler118 ="";
        
        
        
        
        // [DEBUG] Field: Filler119, is_external=, is_static_class=False, static_prefix=
        private string _Filler119 ="";
        
        
        
        
    public YearEndIds() {}
    
    public YearEndIds(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller113(data.Substring(offset, 8).Trim());
        offset += 8;
        SetFiller114(data.Substring(offset, 8).Trim());
        offset += 8;
        SetFiller115(data.Substring(offset, 8).Trim());
        offset += 8;
        SetFiller116(data.Substring(offset, 8).Trim());
        offset += 8;
        SetFiller117(data.Substring(offset, 8).Trim());
        offset += 8;
        SetFiller118(data.Substring(offset, 8).Trim());
        offset += 8;
        SetFiller119(data.Substring(offset, 8).Trim());
        offset += 8;
        
    }
    
    // Serialization methods
    public string GetYearEndIdsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler113.PadRight(8));
        result.Append(_Filler114.PadRight(8));
        result.Append(_Filler115.PadRight(8));
        result.Append(_Filler116.PadRight(8));
        result.Append(_Filler117.PadRight(8));
        result.Append(_Filler118.PadRight(8));
        result.Append(_Filler119.PadRight(8));
        
        return result.ToString();
    }
    
    public void SetYearEndIdsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller113(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller114(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller115(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller116(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller117(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller118(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller119(extracted);
        }
        offset += 8;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller113()
    {
        return _Filler113;
    }
    
    // Standard Setter
    public void SetFiller113(string value)
    {
        _Filler113 = value;
    }
    
    // Get<>AsString()
    public string GetFiller113AsString()
    {
        return _Filler113.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller113AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler113 = value;
    }
    
    // Standard Getter
    public string GetFiller114()
    {
        return _Filler114;
    }
    
    // Standard Setter
    public void SetFiller114(string value)
    {
        _Filler114 = value;
    }
    
    // Get<>AsString()
    public string GetFiller114AsString()
    {
        return _Filler114.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller114AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler114 = value;
    }
    
    // Standard Getter
    public string GetFiller115()
    {
        return _Filler115;
    }
    
    // Standard Setter
    public void SetFiller115(string value)
    {
        _Filler115 = value;
    }
    
    // Get<>AsString()
    public string GetFiller115AsString()
    {
        return _Filler115.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller115AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler115 = value;
    }
    
    // Standard Getter
    public string GetFiller116()
    {
        return _Filler116;
    }
    
    // Standard Setter
    public void SetFiller116(string value)
    {
        _Filler116 = value;
    }
    
    // Get<>AsString()
    public string GetFiller116AsString()
    {
        return _Filler116.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller116AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler116 = value;
    }
    
    // Standard Getter
    public string GetFiller117()
    {
        return _Filler117;
    }
    
    // Standard Setter
    public void SetFiller117(string value)
    {
        _Filler117 = value;
    }
    
    // Get<>AsString()
    public string GetFiller117AsString()
    {
        return _Filler117.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller117AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler117 = value;
    }
    
    // Standard Getter
    public string GetFiller118()
    {
        return _Filler118;
    }
    
    // Standard Setter
    public void SetFiller118(string value)
    {
        _Filler118 = value;
    }
    
    // Get<>AsString()
    public string GetFiller118AsString()
    {
        return _Filler118.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller118AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler118 = value;
    }
    
    // Standard Getter
    public string GetFiller119()
    {
        return _Filler119;
    }
    
    // Standard Setter
    public void SetFiller119(string value)
    {
        _Filler119 = value;
    }
    
    // Get<>AsString()
    public string GetFiller119AsString()
    {
        return _Filler119.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller119AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler119 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}