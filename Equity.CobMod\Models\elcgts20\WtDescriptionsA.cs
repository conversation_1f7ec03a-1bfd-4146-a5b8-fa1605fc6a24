using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtDescriptionsA Data Structure

public class WtDescriptionsA
{
    private static int _size = 570;
    // [DEBUG] Class: WtDescriptionsA, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler378, is_external=, is_static_class=False, static_prefix=
    private string _Filler378 ="DECRIPTIONS-A===";
    
    
    
    
    // [DEBUG] Field: WtdaOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtdaOccurs =28;
    
    
    
    
    // [DEBUG] Field: WtdaDescriptionsTable, is_external=, is_static_class=False, static_prefix=
    private WtdaDescriptionsTable _WtdaDescriptionsTable = new WtdaDescriptionsTable();
    
    
    
    
    // [DEBUG] Field: WtdaDescR, is_external=, is_static_class=False, static_prefix=
    private WtdaDescR _WtdaDescR = new WtdaDescR();
    
    
    
    
    
    // Serialization methods
    public string GetWtDescriptionsAAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler378.PadRight(16));
        result.Append(_WtdaOccurs.ToString().PadLeft(3, '0'));
        result.Append(_WtdaDescriptionsTable.GetWtdaDescriptionsTableAsString());
        result.Append(_WtdaDescR.GetWtdaDescRAsString());
        
        return result.ToString();
    }
    
    public void SetWtDescriptionsAAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller378(extracted);
        }
        offset += 16;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtdaOccurs(parsedInt);
        }
        offset += 3;
        if (offset + 532 <= data.Length)
        {
            _WtdaDescriptionsTable.SetWtdaDescriptionsTableAsString(data.Substring(offset, 532));
        }
        else
        {
            _WtdaDescriptionsTable.SetWtdaDescriptionsTableAsString(data.Substring(offset));
        }
        offset += 532;
        if (offset + 19 <= data.Length)
        {
            _WtdaDescR.SetWtdaDescRAsString(data.Substring(offset, 19));
        }
        else
        {
            _WtdaDescR.SetWtdaDescRAsString(data.Substring(offset));
        }
        offset += 19;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtDescriptionsAAsString();
    }
    // Set<>String Override function
    public void SetWtDescriptionsA(string value)
    {
        SetWtDescriptionsAAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller378()
    {
        return _Filler378;
    }
    
    // Standard Setter
    public void SetFiller378(string value)
    {
        _Filler378 = value;
    }
    
    // Get<>AsString()
    public string GetFiller378AsString()
    {
        return _Filler378.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller378AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler378 = value;
    }
    
    // Standard Getter
    public int GetWtdaOccurs()
    {
        return _WtdaOccurs;
    }
    
    // Standard Setter
    public void SetWtdaOccurs(int value)
    {
        _WtdaOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtdaOccursAsString()
    {
        return _WtdaOccurs.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetWtdaOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtdaOccurs = parsed;
    }
    
    // Standard Getter
    public WtdaDescriptionsTable GetWtdaDescriptionsTable()
    {
        return _WtdaDescriptionsTable;
    }
    
    // Standard Setter
    public void SetWtdaDescriptionsTable(WtdaDescriptionsTable value)
    {
        _WtdaDescriptionsTable = value;
    }
    
    // Get<>AsString()
    public string GetWtdaDescriptionsTableAsString()
    {
        return _WtdaDescriptionsTable != null ? _WtdaDescriptionsTable.GetWtdaDescriptionsTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtdaDescriptionsTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtdaDescriptionsTable == null)
        {
            _WtdaDescriptionsTable = new WtdaDescriptionsTable();
        }
        _WtdaDescriptionsTable.SetWtdaDescriptionsTableAsString(value);
    }
    
    // Standard Getter
    public WtdaDescR GetWtdaDescR()
    {
        return _WtdaDescR;
    }
    
    // Standard Setter
    public void SetWtdaDescR(WtdaDescR value)
    {
        _WtdaDescR = value;
    }
    
    // Get<>AsString()
    public string GetWtdaDescRAsString()
    {
        return _WtdaDescR != null ? _WtdaDescR.GetWtdaDescRAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtdaDescRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtdaDescR == null)
        {
            _WtdaDescR = new WtdaDescR();
        }
        _WtdaDescR.SetWtdaDescRAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtdaDescriptionsTable(string value)
    {
        _WtdaDescriptionsTable.SetWtdaDescriptionsTableAsString(value);
    }
    // Nested Class: WtdaDescriptionsTable
    public class WtdaDescriptionsTable
    {
        private static int _size = 532;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler379, is_external=, is_static_class=False, static_prefix=
        private string _Filler379 ="00 ACQUISITION";
        
        
        
        
        // [DEBUG] Field: Filler380, is_external=, is_static_class=False, static_prefix=
        private string _Filler380 ="SP STOCK PURCHASE";
        
        
        
        
        // [DEBUG] Field: Filler381, is_external=, is_static_class=False, static_prefix=
        private string _Filler381 ="BAPBONUS";
        
        
        
        
        // [DEBUG] Field: Filler382, is_external=, is_static_class=False, static_prefix=
        private string _Filler382 ="NI NEW ISSUE";
        
        
        
        
        // [DEBUG] Field: Filler383, is_external=, is_static_class=False, static_prefix=
        private string _Filler383 ="RAPRIGHTS";
        
        
        
        
        // [DEBUG] Field: Filler384, is_external=, is_static_class=False, static_prefix=
        private string _Filler384 ="RP RIGHTS PURCHASE";
        
        
        
        
        // [DEBUG] Field: Filler385, is_external=, is_static_class=False, static_prefix=
        private string _Filler385 ="UL U/W PURCHASE";
        
        
        
        
        // [DEBUG] Field: Filler386, is_external=, is_static_class=False, static_prefix=
        private string _Filler386 ="CLFCL FM";
        
        
        
        
        // [DEBUG] Field: Filler387, is_external=, is_static_class=False, static_prefix=
        private string _Filler387 ="RCFRC FM";
        
        
        
        
        // [DEBUG] Field: Filler388, is_external=, is_static_class=False, static_prefix=
        private string _Filler388 ="RRFRR FM";
        
        
        
        
        // [DEBUG] Field: Filler389, is_external=, is_static_class=False, static_prefix=
        private string _Filler389 ="TRFTR FM";
        
        
        
        
        // [DEBUG] Field: Filler390, is_external=, is_static_class=False, static_prefix=
        private string _Filler390 ="RGFRG FM";
        
        
        
        
        // [DEBUG] Field: Filler391, is_external=, is_static_class=False, static_prefix=
        private string _Filler391 ="CNFCN FM";
        
        
        
        
        // [DEBUG] Field: Filler392, is_external=, is_static_class=False, static_prefix=
        private string _Filler392 ="CPFCP FM";
        
        
        
        
        // [DEBUG] Field: Filler393, is_external=, is_static_class=False, static_prefix=
        private string _Filler393 ="TPFTP FM";
        
        
        
        
        // [DEBUG] Field: Filler394, is_external=, is_static_class=False, static_prefix=
        private string _Filler394 ="GPCGP FM";
        
        
        
        
        // [DEBUG] Field: Filler395, is_external=, is_static_class=False, static_prefix=
        private string _Filler395 ="SX SHARE EXCHANGE";
        
        
        
        
        // [DEBUG] Field: Filler396, is_external=, is_static_class=False, static_prefix=
        private string _Filler396 ="ROFRO FM";
        
        
        
        
        // [DEBUG] Field: Filler397, is_external=, is_static_class=False, static_prefix=
        private string _Filler397 ="CTXCT FM";
        
        
        
        
        // [DEBUG] Field: Filler398, is_external=, is_static_class=False, static_prefix=
        private string _Filler398 ="AC ACCUMULATION";
        
        
        
        
        // [DEBUG] Field: Filler399, is_external=, is_static_class=False, static_prefix=
        private string _Filler399 ="PI PRIVATISATION";
        
        
        
        
        // [DEBUG] Field: Filler400, is_external=, is_static_class=False, static_prefix=
        private string _Filler400 ="NS NEW ISSUE SALE";
        
        
        
        
        // [DEBUG] Field: Filler401, is_external=, is_static_class=False, static_prefix=
        private string _Filler401 ="NP NEW ISSUE PURCH";
        
        
        
        
        // [DEBUG] Field: Filler402, is_external=, is_static_class=False, static_prefix=
        private string _Filler402 ="TDFTD FM";
        
        
        
        
        // [DEBUG] Field: Filler403, is_external=, is_static_class=False, static_prefix=
        private string _Filler403 ="OL OPTION LAPSE";
        
        
        
        
        // [DEBUG] Field: Filler404, is_external=, is_static_class=False, static_prefix=
        private string _Filler404 ="ECDEX xx";
        
        
        
        
        // [DEBUG] Field: Filler405, is_external=, is_static_class=False, static_prefix=
        private string _Filler405 ="WCDEX xx";
        
        
        
        
        // [DEBUG] Field: Filler406, is_external=, is_static_class=False, static_prefix=
        private string _Filler406 ="WPDEX xx";
        
        
        
        
    public WtdaDescriptionsTable() {}
    
    public WtdaDescriptionsTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller379(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller380(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller381(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller382(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller383(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller384(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller385(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller386(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller387(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller388(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller389(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller390(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller391(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller392(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller393(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller394(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller395(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller396(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller397(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller398(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller399(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller400(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller401(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller402(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller403(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller404(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller405(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller406(data.Substring(offset, 19).Trim());
        offset += 19;
        
    }
    
    // Serialization methods
    public string GetWtdaDescriptionsTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler379.PadRight(19));
        result.Append(_Filler380.PadRight(19));
        result.Append(_Filler381.PadRight(19));
        result.Append(_Filler382.PadRight(19));
        result.Append(_Filler383.PadRight(19));
        result.Append(_Filler384.PadRight(19));
        result.Append(_Filler385.PadRight(19));
        result.Append(_Filler386.PadRight(19));
        result.Append(_Filler387.PadRight(19));
        result.Append(_Filler388.PadRight(19));
        result.Append(_Filler389.PadRight(19));
        result.Append(_Filler390.PadRight(19));
        result.Append(_Filler391.PadRight(19));
        result.Append(_Filler392.PadRight(19));
        result.Append(_Filler393.PadRight(19));
        result.Append(_Filler394.PadRight(19));
        result.Append(_Filler395.PadRight(19));
        result.Append(_Filler396.PadRight(19));
        result.Append(_Filler397.PadRight(19));
        result.Append(_Filler398.PadRight(19));
        result.Append(_Filler399.PadRight(19));
        result.Append(_Filler400.PadRight(19));
        result.Append(_Filler401.PadRight(19));
        result.Append(_Filler402.PadRight(19));
        result.Append(_Filler403.PadRight(19));
        result.Append(_Filler404.PadRight(19));
        result.Append(_Filler405.PadRight(19));
        result.Append(_Filler406.PadRight(19));
        
        return result.ToString();
    }
    
    public void SetWtdaDescriptionsTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller379(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller380(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller381(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller382(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller383(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller384(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller385(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller386(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller387(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller388(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller389(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller390(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller391(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller392(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller393(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller394(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller395(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller396(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller397(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller398(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller399(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller400(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller401(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller402(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller403(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller404(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller405(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller406(extracted);
        }
        offset += 19;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller379()
    {
        return _Filler379;
    }
    
    // Standard Setter
    public void SetFiller379(string value)
    {
        _Filler379 = value;
    }
    
    // Get<>AsString()
    public string GetFiller379AsString()
    {
        return _Filler379.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller379AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler379 = value;
    }
    
    // Standard Getter
    public string GetFiller380()
    {
        return _Filler380;
    }
    
    // Standard Setter
    public void SetFiller380(string value)
    {
        _Filler380 = value;
    }
    
    // Get<>AsString()
    public string GetFiller380AsString()
    {
        return _Filler380.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller380AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler380 = value;
    }
    
    // Standard Getter
    public string GetFiller381()
    {
        return _Filler381;
    }
    
    // Standard Setter
    public void SetFiller381(string value)
    {
        _Filler381 = value;
    }
    
    // Get<>AsString()
    public string GetFiller381AsString()
    {
        return _Filler381.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller381AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler381 = value;
    }
    
    // Standard Getter
    public string GetFiller382()
    {
        return _Filler382;
    }
    
    // Standard Setter
    public void SetFiller382(string value)
    {
        _Filler382 = value;
    }
    
    // Get<>AsString()
    public string GetFiller382AsString()
    {
        return _Filler382.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller382AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler382 = value;
    }
    
    // Standard Getter
    public string GetFiller383()
    {
        return _Filler383;
    }
    
    // Standard Setter
    public void SetFiller383(string value)
    {
        _Filler383 = value;
    }
    
    // Get<>AsString()
    public string GetFiller383AsString()
    {
        return _Filler383.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller383AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler383 = value;
    }
    
    // Standard Getter
    public string GetFiller384()
    {
        return _Filler384;
    }
    
    // Standard Setter
    public void SetFiller384(string value)
    {
        _Filler384 = value;
    }
    
    // Get<>AsString()
    public string GetFiller384AsString()
    {
        return _Filler384.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller384AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler384 = value;
    }
    
    // Standard Getter
    public string GetFiller385()
    {
        return _Filler385;
    }
    
    // Standard Setter
    public void SetFiller385(string value)
    {
        _Filler385 = value;
    }
    
    // Get<>AsString()
    public string GetFiller385AsString()
    {
        return _Filler385.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller385AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler385 = value;
    }
    
    // Standard Getter
    public string GetFiller386()
    {
        return _Filler386;
    }
    
    // Standard Setter
    public void SetFiller386(string value)
    {
        _Filler386 = value;
    }
    
    // Get<>AsString()
    public string GetFiller386AsString()
    {
        return _Filler386.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller386AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler386 = value;
    }
    
    // Standard Getter
    public string GetFiller387()
    {
        return _Filler387;
    }
    
    // Standard Setter
    public void SetFiller387(string value)
    {
        _Filler387 = value;
    }
    
    // Get<>AsString()
    public string GetFiller387AsString()
    {
        return _Filler387.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller387AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler387 = value;
    }
    
    // Standard Getter
    public string GetFiller388()
    {
        return _Filler388;
    }
    
    // Standard Setter
    public void SetFiller388(string value)
    {
        _Filler388 = value;
    }
    
    // Get<>AsString()
    public string GetFiller388AsString()
    {
        return _Filler388.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller388AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler388 = value;
    }
    
    // Standard Getter
    public string GetFiller389()
    {
        return _Filler389;
    }
    
    // Standard Setter
    public void SetFiller389(string value)
    {
        _Filler389 = value;
    }
    
    // Get<>AsString()
    public string GetFiller389AsString()
    {
        return _Filler389.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller389AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler389 = value;
    }
    
    // Standard Getter
    public string GetFiller390()
    {
        return _Filler390;
    }
    
    // Standard Setter
    public void SetFiller390(string value)
    {
        _Filler390 = value;
    }
    
    // Get<>AsString()
    public string GetFiller390AsString()
    {
        return _Filler390.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller390AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler390 = value;
    }
    
    // Standard Getter
    public string GetFiller391()
    {
        return _Filler391;
    }
    
    // Standard Setter
    public void SetFiller391(string value)
    {
        _Filler391 = value;
    }
    
    // Get<>AsString()
    public string GetFiller391AsString()
    {
        return _Filler391.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller391AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler391 = value;
    }
    
    // Standard Getter
    public string GetFiller392()
    {
        return _Filler392;
    }
    
    // Standard Setter
    public void SetFiller392(string value)
    {
        _Filler392 = value;
    }
    
    // Get<>AsString()
    public string GetFiller392AsString()
    {
        return _Filler392.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller392AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler392 = value;
    }
    
    // Standard Getter
    public string GetFiller393()
    {
        return _Filler393;
    }
    
    // Standard Setter
    public void SetFiller393(string value)
    {
        _Filler393 = value;
    }
    
    // Get<>AsString()
    public string GetFiller393AsString()
    {
        return _Filler393.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller393AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler393 = value;
    }
    
    // Standard Getter
    public string GetFiller394()
    {
        return _Filler394;
    }
    
    // Standard Setter
    public void SetFiller394(string value)
    {
        _Filler394 = value;
    }
    
    // Get<>AsString()
    public string GetFiller394AsString()
    {
        return _Filler394.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller394AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler394 = value;
    }
    
    // Standard Getter
    public string GetFiller395()
    {
        return _Filler395;
    }
    
    // Standard Setter
    public void SetFiller395(string value)
    {
        _Filler395 = value;
    }
    
    // Get<>AsString()
    public string GetFiller395AsString()
    {
        return _Filler395.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller395AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler395 = value;
    }
    
    // Standard Getter
    public string GetFiller396()
    {
        return _Filler396;
    }
    
    // Standard Setter
    public void SetFiller396(string value)
    {
        _Filler396 = value;
    }
    
    // Get<>AsString()
    public string GetFiller396AsString()
    {
        return _Filler396.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller396AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler396 = value;
    }
    
    // Standard Getter
    public string GetFiller397()
    {
        return _Filler397;
    }
    
    // Standard Setter
    public void SetFiller397(string value)
    {
        _Filler397 = value;
    }
    
    // Get<>AsString()
    public string GetFiller397AsString()
    {
        return _Filler397.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller397AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler397 = value;
    }
    
    // Standard Getter
    public string GetFiller398()
    {
        return _Filler398;
    }
    
    // Standard Setter
    public void SetFiller398(string value)
    {
        _Filler398 = value;
    }
    
    // Get<>AsString()
    public string GetFiller398AsString()
    {
        return _Filler398.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller398AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler398 = value;
    }
    
    // Standard Getter
    public string GetFiller399()
    {
        return _Filler399;
    }
    
    // Standard Setter
    public void SetFiller399(string value)
    {
        _Filler399 = value;
    }
    
    // Get<>AsString()
    public string GetFiller399AsString()
    {
        return _Filler399.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller399AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler399 = value;
    }
    
    // Standard Getter
    public string GetFiller400()
    {
        return _Filler400;
    }
    
    // Standard Setter
    public void SetFiller400(string value)
    {
        _Filler400 = value;
    }
    
    // Get<>AsString()
    public string GetFiller400AsString()
    {
        return _Filler400.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller400AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler400 = value;
    }
    
    // Standard Getter
    public string GetFiller401()
    {
        return _Filler401;
    }
    
    // Standard Setter
    public void SetFiller401(string value)
    {
        _Filler401 = value;
    }
    
    // Get<>AsString()
    public string GetFiller401AsString()
    {
        return _Filler401.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller401AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler401 = value;
    }
    
    // Standard Getter
    public string GetFiller402()
    {
        return _Filler402;
    }
    
    // Standard Setter
    public void SetFiller402(string value)
    {
        _Filler402 = value;
    }
    
    // Get<>AsString()
    public string GetFiller402AsString()
    {
        return _Filler402.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller402AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler402 = value;
    }
    
    // Standard Getter
    public string GetFiller403()
    {
        return _Filler403;
    }
    
    // Standard Setter
    public void SetFiller403(string value)
    {
        _Filler403 = value;
    }
    
    // Get<>AsString()
    public string GetFiller403AsString()
    {
        return _Filler403.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller403AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler403 = value;
    }
    
    // Standard Getter
    public string GetFiller404()
    {
        return _Filler404;
    }
    
    // Standard Setter
    public void SetFiller404(string value)
    {
        _Filler404 = value;
    }
    
    // Get<>AsString()
    public string GetFiller404AsString()
    {
        return _Filler404.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller404AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler404 = value;
    }
    
    // Standard Getter
    public string GetFiller405()
    {
        return _Filler405;
    }
    
    // Standard Setter
    public void SetFiller405(string value)
    {
        _Filler405 = value;
    }
    
    // Get<>AsString()
    public string GetFiller405AsString()
    {
        return _Filler405.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller405AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler405 = value;
    }
    
    // Standard Getter
    public string GetFiller406()
    {
        return _Filler406;
    }
    
    // Standard Setter
    public void SetFiller406(string value)
    {
        _Filler406 = value;
    }
    
    // Get<>AsString()
    public string GetFiller406AsString()
    {
        return _Filler406.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller406AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler406 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetWtdaDescR(string value)
{
    _WtdaDescR.SetWtdaDescRAsString(value);
}
// Nested Class: WtdaDescR
public class WtdaDescR
{
    private static int _size = 19;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtdaElement, is_external=, is_static_class=False, static_prefix=
    private WtdaDescR.WtdaElement[] _WtdaElement = new WtdaDescR.WtdaElement[28];
    
    public void InitializeWtdaElementArray()
    {
        for (int i = 0; i < 28; i++)
        {
            _WtdaElement[i] = new WtdaDescR.WtdaElement();
        }
    }
    
    
    
public WtdaDescR() {}

public WtdaDescR(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    InitializeWtdaElementArray();
    for (int i = 0; i < 28; i++)
    {
        _WtdaElement[i].SetWtdaElementAsString(data.Substring(offset, 19));
        offset += 19;
    }
    
}

// Serialization methods
public string GetWtdaDescRAsString()
{
    StringBuilder result = new StringBuilder();
    
    for (int i = 0; i < 28; i++)
    {
        result.Append(_WtdaElement[i].GetWtdaElementAsString());
    }
    
    return result.ToString();
}

public void SetWtdaDescRAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    for (int i = 0; i < 28; i++)
    {
        if (offset + 19 > data.Length) break;
        string val = data.Substring(offset, 19);
        
        _WtdaElement[i].SetWtdaElementAsString(val);
        offset += 19;
    }
}

// Getter and Setter methods

// Array Accessors for WtdaElement
public WtdaElement GetWtdaElementAt(int index)
{
    return _WtdaElement[index];
}

public void SetWtdaElementAt(int index, WtdaElement value)
{
    _WtdaElement[index] = value;
}

// Flattened accessors (index 0)
public WtdaElement GetWtdaElement()
{
    return _WtdaElement != null && _WtdaElement.Length > 0
    ? _WtdaElement[0]
    : new WtdaElement();
}

public void SetWtdaElement(WtdaElement value)
{
    if (_WtdaElement == null || _WtdaElement.Length == 0)
    _WtdaElement = new WtdaElement[1];
    _WtdaElement[0] = value;
}





public static int GetSize()
{
    return _size;
}

// Nested Class: WtdaElement
public class WtdaElement
{
    private static int _size = 19;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtdaTransCat, is_external=, is_static_class=False, static_prefix=
    private string _WtdaTransCat ="";
    
    
    
    
    // [DEBUG] Field: WtdaSedolType, is_external=, is_static_class=False, static_prefix=
    private string _WtdaSedolType ="";
    
    
    
    
    // [DEBUG] Field: WtdaDescription, is_external=, is_static_class=False, static_prefix=
    private WtdaElement.WtdaDescription _WtdaDescription = new WtdaElement.WtdaDescription();
    
    
    
    
public WtdaElement() {}

public WtdaElement(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtdaTransCat(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWtdaSedolType(data.Substring(offset, 1).Trim());
    offset += 1;
    _WtdaDescription.SetWtdaDescriptionAsString(data.Substring(offset, WtdaDescription.GetSize()));
    offset += 16;
    
}

// Serialization methods
public string GetWtdaElementAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtdaTransCat.PadRight(2));
    result.Append(_WtdaSedolType.PadRight(1));
    result.Append(_WtdaDescription.GetWtdaDescriptionAsString());
    
    return result.ToString();
}

public void SetWtdaElementAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWtdaTransCat(extracted);
    }
    offset += 2;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtdaSedolType(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        _WtdaDescription.SetWtdaDescriptionAsString(data.Substring(offset, 16));
    }
    else
    {
        _WtdaDescription.SetWtdaDescriptionAsString(data.Substring(offset));
    }
    offset += 16;
}

// Getter and Setter methods

// Standard Getter
public string GetWtdaTransCat()
{
    return _WtdaTransCat;
}

// Standard Setter
public void SetWtdaTransCat(string value)
{
    _WtdaTransCat = value;
}

// Get<>AsString()
public string GetWtdaTransCatAsString()
{
    return _WtdaTransCat.PadRight(2);
}

// Set<>AsString()
public void SetWtdaTransCatAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtdaTransCat = value;
}

// Standard Getter
public string GetWtdaSedolType()
{
    return _WtdaSedolType;
}

// Standard Setter
public void SetWtdaSedolType(string value)
{
    _WtdaSedolType = value;
}

// Get<>AsString()
public string GetWtdaSedolTypeAsString()
{
    return _WtdaSedolType.PadRight(1);
}

// Set<>AsString()
public void SetWtdaSedolTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtdaSedolType = value;
}

// Standard Getter
public WtdaDescription GetWtdaDescription()
{
    return _WtdaDescription;
}

// Standard Setter
public void SetWtdaDescription(WtdaDescription value)
{
    _WtdaDescription = value;
}

// Get<>AsString()
public string GetWtdaDescriptionAsString()
{
    return _WtdaDescription != null ? _WtdaDescription.GetWtdaDescriptionAsString() : "";
}

// Set<>AsString()
public void SetWtdaDescriptionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtdaDescription == null)
    {
        _WtdaDescription = new WtdaDescription();
    }
    _WtdaDescription.SetWtdaDescriptionAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WtdaDescription
public class WtdaDescription
{
    private static int _size = 16;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler407, is_external=, is_static_class=False, static_prefix=
    private string _Filler407 ="";
    
    
    
    
    // [DEBUG] Field: WtdaToFrom, is_external=, is_static_class=False, static_prefix=
    private string _WtdaToFrom ="";
    
    
    
    
    // [DEBUG] Field: Filler408, is_external=, is_static_class=False, static_prefix=
    private string _Filler408 ="";
    
    
    
    
    // [DEBUG] Field: WtdaSedol, is_external=, is_static_class=False, static_prefix=
    private string _WtdaSedol ="";
    
    
    
    
public WtdaDescription() {}

public WtdaDescription(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller407(data.Substring(offset, 3).Trim());
    offset += 3;
    SetWtdaToFrom(data.Substring(offset, 2).Trim());
    offset += 2;
    SetFiller408(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWtdaSedol(data.Substring(offset, 9).Trim());
    offset += 9;
    
}

// Serialization methods
public string GetWtdaDescriptionAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler407.PadRight(3));
    result.Append(_WtdaToFrom.PadRight(2));
    result.Append(_Filler408.PadRight(2));
    result.Append(_WtdaSedol.PadRight(9));
    
    return result.ToString();
}

public void SetWtdaDescriptionAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller407(extracted);
    }
    offset += 3;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWtdaToFrom(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller408(extracted);
    }
    offset += 2;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        SetWtdaSedol(extracted);
    }
    offset += 9;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller407()
{
    return _Filler407;
}

// Standard Setter
public void SetFiller407(string value)
{
    _Filler407 = value;
}

// Get<>AsString()
public string GetFiller407AsString()
{
    return _Filler407.PadRight(3);
}

// Set<>AsString()
public void SetFiller407AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler407 = value;
}

// Standard Getter
public string GetWtdaToFrom()
{
    return _WtdaToFrom;
}

// Standard Setter
public void SetWtdaToFrom(string value)
{
    _WtdaToFrom = value;
}

// Get<>AsString()
public string GetWtdaToFromAsString()
{
    return _WtdaToFrom.PadRight(2);
}

// Set<>AsString()
public void SetWtdaToFromAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtdaToFrom = value;
}

// Standard Getter
public string GetFiller408()
{
    return _Filler408;
}

// Standard Setter
public void SetFiller408(string value)
{
    _Filler408 = value;
}

// Get<>AsString()
public string GetFiller408AsString()
{
    return _Filler408.PadRight(2);
}

// Set<>AsString()
public void SetFiller408AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler408 = value;
}

// Standard Getter
public string GetWtdaSedol()
{
    return _WtdaSedol;
}

// Standard Setter
public void SetWtdaSedol(string value)
{
    _WtdaSedol = value;
}

// Get<>AsString()
public string GetWtdaSedolAsString()
{
    return _WtdaSedol.PadRight(9);
}

// Set<>AsString()
public void SetWtdaSedolAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtdaSedol = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}

}}