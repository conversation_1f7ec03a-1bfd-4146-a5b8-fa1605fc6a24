using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtRpiMessages Data Structure

public class WtRpiMessages
{
    private static int _size = 57;
    // [DEBUG] Class: WtRpiMessages, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler486, is_external=, is_static_class=False, static_prefix=
    private string _Filler486 ="RPI MESSAGE TABLE===============";
    
    
    
    
    // [DEBUG] Field: WtrpiMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtrpiMaxTableSize =1000;
    
    
    
    
    // [DEBUG] Field: WtrpiOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtrpiOccurs =0;
    
    
    
    
    // [DEBUG] Field: WtrpiMessageTable, is_external=, is_static_class=False, static_prefix=
    private WtrpiMessageTable _WtrpiMessageTable = new WtrpiMessageTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtRpiMessagesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler486.PadRight(32));
        result.Append(_WtrpiMaxTableSize.ToString().PadLeft(5, '0'));
        result.Append(_WtrpiOccurs.ToString().PadLeft(5, '0'));
        result.Append(_WtrpiMessageTable.GetWtrpiMessageTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtRpiMessagesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 32 <= data.Length)
        {
            string extracted = data.Substring(offset, 32).Trim();
            SetFiller486(extracted);
        }
        offset += 32;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtrpiMaxTableSize(parsedInt);
        }
        offset += 5;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtrpiOccurs(parsedInt);
        }
        offset += 5;
        if (offset + 15 <= data.Length)
        {
            _WtrpiMessageTable.SetWtrpiMessageTableAsString(data.Substring(offset, 15));
        }
        else
        {
            _WtrpiMessageTable.SetWtrpiMessageTableAsString(data.Substring(offset));
        }
        offset += 15;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtRpiMessagesAsString();
    }
    // Set<>String Override function
    public void SetWtRpiMessages(string value)
    {
        SetWtRpiMessagesAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller486()
    {
        return _Filler486;
    }
    
    // Standard Setter
    public void SetFiller486(string value)
    {
        _Filler486 = value;
    }
    
    // Get<>AsString()
    public string GetFiller486AsString()
    {
        return _Filler486.PadRight(32);
    }
    
    // Set<>AsString()
    public void SetFiller486AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler486 = value;
    }
    
    // Standard Getter
    public int GetWtrpiMaxTableSize()
    {
        return _WtrpiMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtrpiMaxTableSize(int value)
    {
        _WtrpiMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtrpiMaxTableSizeAsString()
    {
        return _WtrpiMaxTableSize.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWtrpiMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtrpiMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtrpiOccurs()
    {
        return _WtrpiOccurs;
    }
    
    // Standard Setter
    public void SetWtrpiOccurs(int value)
    {
        _WtrpiOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtrpiOccursAsString()
    {
        return _WtrpiOccurs.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWtrpiOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtrpiOccurs = parsed;
    }
    
    // Standard Getter
    public WtrpiMessageTable GetWtrpiMessageTable()
    {
        return _WtrpiMessageTable;
    }
    
    // Standard Setter
    public void SetWtrpiMessageTable(WtrpiMessageTable value)
    {
        _WtrpiMessageTable = value;
    }
    
    // Get<>AsString()
    public string GetWtrpiMessageTableAsString()
    {
        return _WtrpiMessageTable != null ? _WtrpiMessageTable.GetWtrpiMessageTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtrpiMessageTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtrpiMessageTable == null)
        {
            _WtrpiMessageTable = new WtrpiMessageTable();
        }
        _WtrpiMessageTable.SetWtrpiMessageTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtrpiMessageTable(string value)
    {
        _WtrpiMessageTable.SetWtrpiMessageTableAsString(value);
    }
    // Nested Class: WtrpiMessageTable
    public class WtrpiMessageTable
    {
        private static int _size = 15;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtpfElement, is_external=, is_static_class=False, static_prefix=
        private WtrpiMessageTable.WtpfElement[] _WtpfElement = new WtrpiMessageTable.WtpfElement[1000];
        
        public void InitializeWtpfElementArray()
        {
            for (int i = 0; i < 1000; i++)
            {
                _WtpfElement[i] = new WtrpiMessageTable.WtpfElement();
            }
        }
        
        
        
    public WtrpiMessageTable() {}
    
    public WtrpiMessageTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWtpfElementArray();
        for (int i = 0; i < 1000; i++)
        {
            _WtpfElement[i].SetWtpfElementAsString(data.Substring(offset, 15));
            offset += 15;
        }
        
    }
    
    // Serialization methods
    public string GetWtrpiMessageTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 1000; i++)
        {
            result.Append(_WtpfElement[i].GetWtpfElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtrpiMessageTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 1000; i++)
        {
            if (offset + 15 > data.Length) break;
            string val = data.Substring(offset, 15);
            
            _WtpfElement[i].SetWtpfElementAsString(val);
            offset += 15;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtpfElement
    public WtpfElement GetWtpfElementAt(int index)
    {
        return _WtpfElement[index];
    }
    
    public void SetWtpfElementAt(int index, WtpfElement value)
    {
        _WtpfElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtpfElement GetWtpfElement()
    {
        return _WtpfElement != null && _WtpfElement.Length > 0
        ? _WtpfElement[0]
        : new WtpfElement();
    }
    
    public void SetWtpfElement(WtpfElement value)
    {
        if (_WtpfElement == null || _WtpfElement.Length == 0)
        _WtpfElement = new WtpfElement[1];
        _WtpfElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtpfElement
    public class WtpfElement
    {
        private static int _size = 15;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtrpiMsgYymm, is_external=, is_static_class=False, static_prefix=
        private string _WtrpiMsgYymm ="";
        
        
        
        
        // [DEBUG] Field: WtrpiMsgFund, is_external=, is_static_class=False, static_prefix=
        private string _WtrpiMsgFund ="";
        
        
        
        
        // [DEBUG] Field: WtrpiMsgSedol, is_external=, is_static_class=False, static_prefix=
        private string _WtrpiMsgSedol ="";
        
        
        
        
    public WtpfElement() {}
    
    public WtpfElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtrpiMsgYymm(data.Substring(offset, 4).Trim());
        offset += 4;
        SetWtrpiMsgFund(data.Substring(offset, 4).Trim());
        offset += 4;
        SetWtrpiMsgSedol(data.Substring(offset, 7).Trim());
        offset += 7;
        
    }
    
    // Serialization methods
    public string GetWtpfElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtrpiMsgYymm.PadRight(4));
        result.Append(_WtrpiMsgFund.PadRight(4));
        result.Append(_WtrpiMsgSedol.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetWtpfElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtrpiMsgYymm(extracted);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtrpiMsgFund(extracted);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetWtrpiMsgSedol(extracted);
        }
        offset += 7;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtrpiMsgYymm()
    {
        return _WtrpiMsgYymm;
    }
    
    // Standard Setter
    public void SetWtrpiMsgYymm(string value)
    {
        _WtrpiMsgYymm = value;
    }
    
    // Get<>AsString()
    public string GetWtrpiMsgYymmAsString()
    {
        return _WtrpiMsgYymm.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtrpiMsgYymmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtrpiMsgYymm = value;
    }
    
    // Standard Getter
    public string GetWtrpiMsgFund()
    {
        return _WtrpiMsgFund;
    }
    
    // Standard Setter
    public void SetWtrpiMsgFund(string value)
    {
        _WtrpiMsgFund = value;
    }
    
    // Get<>AsString()
    public string GetWtrpiMsgFundAsString()
    {
        return _WtrpiMsgFund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtrpiMsgFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtrpiMsgFund = value;
    }
    
    // Standard Getter
    public string GetWtrpiMsgSedol()
    {
        return _WtrpiMsgSedol;
    }
    
    // Standard Setter
    public void SetWtrpiMsgSedol(string value)
    {
        _WtrpiMsgSedol = value;
    }
    
    // Get<>AsString()
    public string GetWtrpiMsgSedolAsString()
    {
        return _WtrpiMsgSedol.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetWtrpiMsgSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtrpiMsgSedol = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}

}}