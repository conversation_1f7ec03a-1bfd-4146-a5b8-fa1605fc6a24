using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D82File Data Structure

public class D82File
{
    private static int _size = 12;
    // [DEBUG] Class: D82File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler254, is_external=, is_static_class=False, static_prefix=
    private string _Filler254 ="$";
    
    
    
    
    // [DEBUG] Field: D82UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D82UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler255, is_external=, is_static_class=False, static_prefix=
    private string _Filler255 ="RJ";
    
    
    
    
    // [DEBUG] Field: D82ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D82ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler256, is_external=, is_static_class=False, static_prefix=
    private string _Filler256 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD82FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler254.PadRight(1));
        result.Append(_D82UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler255.PadRight(2));
        result.Append(_D82ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler256.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD82FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller254(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD82UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller255(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD82ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller256(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD82FileAsString();
    }
    // Set<>String Override function
    public void SetD82File(string value)
    {
        SetD82FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller254()
    {
        return _Filler254;
    }
    
    // Standard Setter
    public void SetFiller254(string value)
    {
        _Filler254 = value;
    }
    
    // Get<>AsString()
    public string GetFiller254AsString()
    {
        return _Filler254.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller254AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler254 = value;
    }
    
    // Standard Getter
    public int GetD82UserNo()
    {
        return _D82UserNo;
    }
    
    // Standard Setter
    public void SetD82UserNo(int value)
    {
        _D82UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD82UserNoAsString()
    {
        return _D82UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD82UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D82UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller255()
    {
        return _Filler255;
    }
    
    // Standard Setter
    public void SetFiller255(string value)
    {
        _Filler255 = value;
    }
    
    // Get<>AsString()
    public string GetFiller255AsString()
    {
        return _Filler255.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller255AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler255 = value;
    }
    
    // Standard Getter
    public int GetD82ReportNo()
    {
        return _D82ReportNo;
    }
    
    // Standard Setter
    public void SetD82ReportNo(int value)
    {
        _D82ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD82ReportNoAsString()
    {
        return _D82ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD82ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D82ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller256()
    {
        return _Filler256;
    }
    
    // Standard Setter
    public void SetFiller256(string value)
    {
        _Filler256 = value;
    }
    
    // Get<>AsString()
    public string GetFiller256AsString()
    {
        return _Filler256.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller256AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler256 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}