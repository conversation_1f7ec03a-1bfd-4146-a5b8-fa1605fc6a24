using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing NewFields Data Structure

public class NewFields
{
    private static int _size = 6;
    // [DEBUG] Class: NewFields, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsVector, is_external=, is_static_class=False, static_prefix=
    private string _WsVector =" ";
    
    
    
    
    // [DEBUG] Field: WsEyMsgNo, is_external=, is_static_class=False, static_prefix=
    private int _WsEyMsgNo =1;
    
    
    
    
    
    // Serialization methods
    public string GetNewFieldsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsVector.PadRight(1));
        result.Append(_WsEyMsgNo.ToString().PadLeft(5, '0'));
        
        return result.ToString();
    }
    
    public void SetNewFieldsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsVector(extracted);
        }
        offset += 1;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsEyMsgNo(parsedInt);
        }
        offset += 5;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetNewFieldsAsString();
    }
    // Set<>String Override function
    public void SetNewFields(string value)
    {
        SetNewFieldsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsVector()
    {
        return _WsVector;
    }
    
    // Standard Setter
    public void SetWsVector(string value)
    {
        _WsVector = value;
    }
    
    // Get<>AsString()
    public string GetWsVectorAsString()
    {
        return _WsVector.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsVectorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsVector = value;
    }
    
    // Standard Getter
    public int GetWsEyMsgNo()
    {
        return _WsEyMsgNo;
    }
    
    // Standard Setter
    public void SetWsEyMsgNo(int value)
    {
        _WsEyMsgNo = value;
    }
    
    // Get<>AsString()
    public string GetWsEyMsgNoAsString()
    {
        return _WsEyMsgNo.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWsEyMsgNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsEyMsgNo = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
