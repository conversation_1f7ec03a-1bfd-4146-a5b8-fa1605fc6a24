using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing ElcgmioLinkage1 Data Structure

public class ElcgmioLinkage1
{
    private static int _size = 32;
    // [DEBUG] Class: ElcgmioLinkage1, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: LName, is_external=, is_static_class=False, static_prefix=
    private string _LName ="";
    
    
    
    
    // [DEBUG] Field: LAct, is_external=, is_static_class=False, static_prefix=
    private LAct _LAct = new LAct();
    
    
    
    
    // [DEBUG] Field: LReturnCode, is_external=, is_static_class=False, static_prefix=
    private string _LReturnCode ="";
    
    
    // 88-level condition checks for LReturnCode
    public bool IsX24Successful()
    {
        if (this._LReturnCode == "'00'") return true;
        return false;
    }
    public bool IsX24OpenOk()
    {
        if (this._LReturnCode == "'00'") return true;
        if (this._LReturnCode == "'05'") return true;
        if (this._LReturnCode == "'41'") return true;
        return false;
    }
    public bool IsX24EndOfFile()
    {
        if (this._LReturnCode == "'10'") return true;
        return false;
    }
    public bool IsX24InvalidKey()
    {
        if (this._LReturnCode == "'23'") return true;
        return false;
    }
    public bool IsX24DuplicateKey()
    {
        if (this._LReturnCode == "'22'") return true;
        return false;
    }
    public bool IsX24FileDoesntExist()
    {
        if (this._LReturnCode == "'35'") return true;
        return false;
    }
    public bool IsX24RecordLocked()
    {
        if (this._LReturnCode == "'9D'") return true;
        return false;
    }
    public bool IsX24FileWasAlreadyOpen()
    {
        if (this._LReturnCode == "41") return true;
        return false;
    }
    
    
    // [DEBUG] Field: LReturnCode2, is_external=, is_static_class=False, static_prefix=
    private LReturnCode2 _LReturnCode2 = new LReturnCode2();
    
    
    
    
    // [DEBUG] Field: LPriceDate, is_external=, is_static_class=False, static_prefix=
    private int _LPriceDate =0;
    
    
    
    
    // [DEBUG] Field: LPriceDate2, is_external=, is_static_class=False, static_prefix=
    private LPriceDate2 _LPriceDate2 = new LPriceDate2();
    
    
    
    
    // [DEBUG] Field: LReport, is_external=, is_static_class=False, static_prefix=
    private string _LReport ="";
    
    
    
    
    // [DEBUG] Field: LPriceTypeStatus, is_external=, is_static_class=False, static_prefix=
    private string _LPriceTypeStatus ="";
    
    
    // 88-level condition checks for LPriceTypeStatus
    public bool IsPriceFound()
    {
        if (!string.IsNullOrEmpty(this.GetLPriceTypeStatus()) && this.GetLPriceTypeStatus().Length == 1)
        {
            var ch = this.GetLPriceTypeStatus()[0];
            if (ch >= '0' && ch <= '2') return true;
        }
        return false;
    }
    public bool IsSameDayPriceFound()
    {
        if (this._LPriceTypeStatus == "'0'") return true;
        return false;
    }
    public bool IsPrevDayPriceFound()
    {
        if (this._LPriceTypeStatus == "'1'") return true;
        return false;
    }
    public bool IsPrevSessionPriceFound()
    {
        if (this._LPriceTypeStatus == "'2'") return true;
        return false;
    }
    public bool IsPriceNotFound()
    {
        if (!string.IsNullOrEmpty(this.GetLPriceTypeStatus()) && this.GetLPriceTypeStatus().Length == 1)
        {
            var ch = this.GetLPriceTypeStatus()[0];
            if (ch >= '3' && ch <= '7') return true;
        }
        return false;
    }
    public bool IsSameDayPriceNotFound()
    {
        if (this._LPriceTypeStatus == "'3'") return true;
        return false;
    }
    public bool IsPrevDayPriceNotFound()
    {
        if (this._LPriceTypeStatus == "'4'") return true;
        return false;
    }
    public bool IsPrevSessionPriceNotFound()
    {
        if (this._LPriceTypeStatus == "'5'") return true;
        return false;
    }
    public bool IsFundRecordNotFound()
    {
        if (this._LPriceTypeStatus == "'6'") return true;
        return false;
    }
    public bool IsPriceTypeRecordNotFound()
    {
        if (this._LPriceTypeStatus == "'7'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: LRequests, is_external=, is_static_class=False, static_prefix=
    private LRequests _LRequests = new LRequests();
    
    
    
    
    
    // Serialization methods
    public string GetElcgmioLinkage1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LName.PadRight(8));
        result.Append(_LAct.GetLActAsString());
        result.Append(_LReturnCode.PadRight(2));
        result.Append(_LReturnCode2.GetLReturnCode2AsString());
        result.Append(_LPriceDate.ToString().PadLeft(6, '0'));
        result.Append(_LPriceDate2.GetLPriceDate2AsString());
        result.Append(_LReport.PadRight(1));
        result.Append(_LPriceTypeStatus.PadRight(1));
        result.Append(_LRequests.GetLRequestsAsString());
        
        return result.ToString();
    }
    
    public void SetElcgmioLinkage1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetLName(extracted);
        }
        offset += 8;
        if (offset + 3 <= data.Length)
        {
            _LAct.SetLActAsString(data.Substring(offset, 3));
        }
        else
        {
            _LAct.SetLActAsString(data.Substring(offset));
        }
        offset += 3;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetLReturnCode(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            _LReturnCode2.SetLReturnCode2AsString(data.Substring(offset, 2));
        }
        else
        {
            _LReturnCode2.SetLReturnCode2AsString(data.Substring(offset));
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetLPriceDate(parsedInt);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            _LPriceDate2.SetLPriceDate2AsString(data.Substring(offset, 6));
        }
        else
        {
            _LPriceDate2.SetLPriceDate2AsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetLReport(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetLPriceTypeStatus(extracted);
        }
        offset += 1;
        if (offset + 3 <= data.Length)
        {
            _LRequests.SetLRequestsAsString(data.Substring(offset, 3));
        }
        else
        {
            _LRequests.SetLRequestsAsString(data.Substring(offset));
        }
        offset += 3;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetElcgmioLinkage1AsString();
    }
    // Set<>String Override function
    public void SetElcgmioLinkage1(string value)
    {
        SetElcgmioLinkage1AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetLName()
    {
        return _LName;
    }
    
    // Standard Setter
    public void SetLName(string value)
    {
        _LName = value;
    }
    
    // Get<>AsString()
    public string GetLNameAsString()
    {
        return _LName.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetLNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LName = value;
    }
    
    // Standard Getter
    public LAct GetLAct()
    {
        return _LAct;
    }
    
    // Standard Setter
    public void SetLAct(LAct value)
    {
        _LAct = value;
    }
    
    // Get<>AsString()
    public string GetLActAsString()
    {
        return _LAct != null ? _LAct.GetLActAsString() : "";
    }
    
    // Set<>AsString()
    public void SetLActAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_LAct == null)
        {
            _LAct = new LAct();
        }
        _LAct.SetLActAsString(value);
    }
    
    // Standard Getter
    public string GetLReturnCode()
    {
        return _LReturnCode;
    }
    
    // Standard Setter
    public void SetLReturnCode(string value)
    {
        _LReturnCode = value;
    }
    
    // Get<>AsString()
    public string GetLReturnCodeAsString()
    {
        return _LReturnCode.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetLReturnCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LReturnCode = value;
    }
    
    // Standard Getter
    public LReturnCode2 GetLReturnCode2()
    {
        return _LReturnCode2;
    }
    
    // Standard Setter
    public void SetLReturnCode2(LReturnCode2 value)
    {
        _LReturnCode2 = value;
    }
    
    // Get<>AsString()
    public string GetLReturnCode2AsString()
    {
        return _LReturnCode2 != null ? _LReturnCode2.GetLReturnCode2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetLReturnCode2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_LReturnCode2 == null)
        {
            _LReturnCode2 = new LReturnCode2();
        }
        _LReturnCode2.SetLReturnCode2AsString(value);
    }
    
    // Standard Getter
    public int GetLPriceDate()
    {
        return _LPriceDate;
    }
    
    // Standard Setter
    public void SetLPriceDate(int value)
    {
        _LPriceDate = value;
    }
    
    // Get<>AsString()
    public string GetLPriceDateAsString()
    {
        return _LPriceDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetLPriceDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _LPriceDate = parsed;
    }
    
    // Standard Getter
    public LPriceDate2 GetLPriceDate2()
    {
        return _LPriceDate2;
    }
    
    // Standard Setter
    public void SetLPriceDate2(LPriceDate2 value)
    {
        _LPriceDate2 = value;
    }
    
    // Get<>AsString()
    public string GetLPriceDate2AsString()
    {
        return _LPriceDate2 != null ? _LPriceDate2.GetLPriceDate2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetLPriceDate2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_LPriceDate2 == null)
        {
            _LPriceDate2 = new LPriceDate2();
        }
        _LPriceDate2.SetLPriceDate2AsString(value);
    }
    
    // Standard Getter
    public string GetLReport()
    {
        return _LReport;
    }
    
    // Standard Setter
    public void SetLReport(string value)
    {
        _LReport = value;
    }
    
    // Get<>AsString()
    public string GetLReportAsString()
    {
        return _LReport.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetLReportAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LReport = value;
    }
    
    // Standard Getter
    public string GetLPriceTypeStatus()
    {
        return _LPriceTypeStatus;
    }
    
    // Standard Setter
    public void SetLPriceTypeStatus(string value)
    {
        _LPriceTypeStatus = value;
    }
    
    // Get<>AsString()
    public string GetLPriceTypeStatusAsString()
    {
        return _LPriceTypeStatus.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetLPriceTypeStatusAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LPriceTypeStatus = value;
    }
    
    // Standard Getter
    public LRequests GetLRequests()
    {
        return _LRequests;
    }
    
    // Standard Setter
    public void SetLRequests(LRequests value)
    {
        _LRequests = value;
    }
    
    // Get<>AsString()
    public string GetLRequestsAsString()
    {
        return _LRequests != null ? _LRequests.GetLRequestsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetLRequestsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_LRequests == null)
        {
            _LRequests = new LRequests();
        }
        _LRequests.SetLRequestsAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetLAct(string value)
    {
        _LAct.SetLActAsString(value);
    }
    // Nested Class: LAct
    public class LAct
    {
        private static int _size = 3;
        
        // Fields in the class
        
        
        // [DEBUG] Field: LAction, is_external=, is_static_class=False, static_prefix=
        private string _LAction ="";
        
        
        
        
        // [DEBUG] Field: LKey, is_external=, is_static_class=False, static_prefix=
        private string _LKey ="";
        
        
        
        
    public LAct() {}
    
    public LAct(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetLAction(data.Substring(offset, 2).Trim());
        offset += 2;
        SetLKey(data.Substring(offset, 1).Trim());
        offset += 1;
        
    }
    
    // Serialization methods
    public string GetLActAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LAction.PadRight(2));
        result.Append(_LKey.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetLActAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetLAction(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetLKey(extracted);
        }
        offset += 1;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetLAction()
    {
        return _LAction;
    }
    
    // Standard Setter
    public void SetLAction(string value)
    {
        _LAction = value;
    }
    
    // Get<>AsString()
    public string GetLActionAsString()
    {
        return _LAction.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetLActionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LAction = value;
    }
    
    // Standard Getter
    public string GetLKey()
    {
        return _LKey;
    }
    
    // Standard Setter
    public void SetLKey(string value)
    {
        _LKey = value;
    }
    
    // Get<>AsString()
    public string GetLKeyAsString()
    {
        return _LKey.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetLKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LKey = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetLReturnCode2(string value)
{
    _LReturnCode2.SetLReturnCode2AsString(value);
}
// Nested Class: LReturnCode2
public class LReturnCode2
{
    private static int _size = 2;
    
    // Fields in the class
    
    
    // [DEBUG] Field: LX24Status1, is_external=, is_static_class=False, static_prefix=
    private string _LX24Status1 ="";
    
    
    // 88-level condition checks for LX24Status1
    public bool IsX24RunTimeError()
    {
        if (this._LX24Status1 == "'9'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: LX24Status2, is_external=, is_static_class=False, static_prefix=
    private string _LX24Status2 ="";
    
    
    
    
public LReturnCode2() {}

public LReturnCode2(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetLx24Status1(data.Substring(offset, 1).Trim());
    offset += 1;
    SetLx24Status2(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetLReturnCode2AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_LX24Status1.PadRight(1));
    result.Append(_LX24Status2.PadRight(1));
    
    return result.ToString();
}

public void SetLReturnCode2AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetLx24Status1(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetLx24Status2(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetLx24Status1()
{
    return _LX24Status1;
}

// Standard Setter
public void SetLx24Status1(string value)
{
    _LX24Status1 = value;
}

// Get<>AsString()
public string GetLx24Status1AsString()
{
    return _LX24Status1.PadRight(1);
}

// Set<>AsString()
public void SetLx24Status1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LX24Status1 = value;
}

// Standard Getter
public string GetLx24Status2()
{
    return _LX24Status2;
}

// Standard Setter
public void SetLx24Status2(string value)
{
    _LX24Status2 = value;
}

// Get<>AsString()
public string GetLx24Status2AsString()
{
    return _LX24Status2.PadRight(1);
}

// Set<>AsString()
public void SetLx24Status2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LX24Status2 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetLPriceDate2(string value)
{
    _LPriceDate2.SetLPriceDate2AsString(value);
}
// Nested Class: LPriceDate2
public class LPriceDate2
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: LPriceDateYy, is_external=, is_static_class=False, static_prefix=
    private int _LPriceDateYy =0;
    
    
    
    
    // [DEBUG] Field: LPriceDateMm, is_external=, is_static_class=False, static_prefix=
    private int _LPriceDateMm =0;
    
    
    
    
    // [DEBUG] Field: LPriceDateDd, is_external=, is_static_class=False, static_prefix=
    private int _LPriceDateDd =0;
    
    
    
    
public LPriceDate2() {}

public LPriceDate2(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetLPriceDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetLPriceDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetLPriceDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetLPriceDate2AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_LPriceDateYy.ToString().PadLeft(2, '0'));
    result.Append(_LPriceDateMm.ToString().PadLeft(2, '0'));
    result.Append(_LPriceDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetLPriceDate2AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetLPriceDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetLPriceDateMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetLPriceDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetLPriceDateYy()
{
    return _LPriceDateYy;
}

// Standard Setter
public void SetLPriceDateYy(int value)
{
    _LPriceDateYy = value;
}

// Get<>AsString()
public string GetLPriceDateYyAsString()
{
    return _LPriceDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetLPriceDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _LPriceDateYy = parsed;
}

// Standard Getter
public int GetLPriceDateMm()
{
    return _LPriceDateMm;
}

// Standard Setter
public void SetLPriceDateMm(int value)
{
    _LPriceDateMm = value;
}

// Get<>AsString()
public string GetLPriceDateMmAsString()
{
    return _LPriceDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetLPriceDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _LPriceDateMm = parsed;
}

// Standard Getter
public int GetLPriceDateDd()
{
    return _LPriceDateDd;
}

// Standard Setter
public void SetLPriceDateDd(int value)
{
    _LPriceDateDd = value;
}

// Get<>AsString()
public string GetLPriceDateDdAsString()
{
    return _LPriceDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetLPriceDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _LPriceDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetLRequests(string value)
{
    _LRequests.SetLRequestsAsString(value);
}
// Nested Class: LRequests
public class LRequests
{
    private static int _size = 3;
    
    // Fields in the class
    
    
    // [DEBUG] Field: LBalances, is_external=, is_static_class=False, static_prefix=
    private int _LBalances =1;
    
    
    // 88-level condition checks for LBalances
    public bool IsBalancesNotRequested()
    {
        if (this._LBalances == 0) return true;
        return false;
    }
    public bool IsBalancesRequested()
    {
        if (this._LBalances == 1) return true;
        return false;
    }
    
    
    // [DEBUG] Field: LAcquisitions, is_external=, is_static_class=False, static_prefix=
    private int _LAcquisitions =1;
    
    
    // 88-level condition checks for LAcquisitions
    public bool IsAcquisitionsNotRequested()
    {
        if (this._LAcquisitions == 0) return true;
        return false;
    }
    public bool IsAcquisitionsRequested()
    {
        if (this._LAcquisitions == 1) return true;
        return false;
    }
    
    
    // [DEBUG] Field: LDisposals, is_external=, is_static_class=False, static_prefix=
    private int _LDisposals =1;
    
    
    // 88-level condition checks for LDisposals
    public bool IsDisposalsNotRequested()
    {
        if (this._LDisposals == 0) return true;
        return false;
    }
    public bool IsDisposalsRequested()
    {
        if (this._LDisposals == 1) return true;
        return false;
    }
    
    
public LRequests() {}

public LRequests(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetLBalances(int.Parse(data.Substring(offset, 1).Trim()));
    offset += 1;
    SetLAcquisitions(int.Parse(data.Substring(offset, 1).Trim()));
    offset += 1;
    SetLDisposals(int.Parse(data.Substring(offset, 1).Trim()));
    offset += 1;
    
}

// Serialization methods
public string GetLRequestsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_LBalances.ToString().PadLeft(1, '0'));
    result.Append(_LAcquisitions.ToString().PadLeft(1, '0'));
    result.Append(_LDisposals.ToString().PadLeft(1, '0'));
    
    return result.ToString();
}

public void SetLRequestsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetLBalances(parsedInt);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetLAcquisitions(parsedInt);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetLDisposals(parsedInt);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public int GetLBalances()
{
    return _LBalances;
}

// Standard Setter
public void SetLBalances(int value)
{
    _LBalances = value;
}

// Get<>AsString()
public string GetLBalancesAsString()
{
    return _LBalances.ToString().PadLeft(1, '0');
}

// Set<>AsString()
public void SetLBalancesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _LBalances = parsed;
}

// Standard Getter
public int GetLAcquisitions()
{
    return _LAcquisitions;
}

// Standard Setter
public void SetLAcquisitions(int value)
{
    _LAcquisitions = value;
}

// Get<>AsString()
public string GetLAcquisitionsAsString()
{
    return _LAcquisitions.ToString().PadLeft(1, '0');
}

// Set<>AsString()
public void SetLAcquisitionsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _LAcquisitions = parsed;
}

// Standard Getter
public int GetLDisposals()
{
    return _LDisposals;
}

// Standard Setter
public void SetLDisposals(int value)
{
    _LDisposals = value;
}

// Get<>AsString()
public string GetLDisposalsAsString()
{
    return _LDisposals.ToString().PadLeft(1, '0');
}

// Set<>AsString()
public void SetLDisposalsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _LDisposals = parsed;
}



public static int GetSize()
{
    return _size;
}

}

}}