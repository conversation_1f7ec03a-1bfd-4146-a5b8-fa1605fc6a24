using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdailyDTO
{// <Section> Class for Gvar
public class Gvar
{
public Gvar() {}

// Fields in the class


// [DEBUG] Field: WProgramName, is_external=, is_static_class=False, static_prefix=
private string _WProgramName ="CGTDAILY";




// [DEBUG] Field: VersionNumber, is_external=, is_static_class=False, static_prefix=
private string _VersionNumber ="1.0";




// [DEBUG] Field: WFileReturnCode, is_external=, is_static_class=False, static_prefix=
private string _WFileReturnCode ="";




// [DEBUG] Field: ExportFileName, is_external=, is_static_class=False, static_prefix=
private string _ExportFileName ="";




// [DEBUG] Field: ReportFile, is_external=, is_static_class=False, static_prefix=
private ReportFile _ReportFile = new ReportFile();




// [DEBUG] Field: DateStamp, is_external=, is_static_class=False, static_prefix=
private DateStamp _DateStamp = new DateStamp();




// [DEBUG] Field: WExportCount, is_external=, is_static_class=False, static_prefix=
private int _WExportCount =0;




// [DEBUG] Field: WReadCount, is_external=, is_static_class=False, static_prefix=
private int _WReadCount =0;




// [DEBUG] Field: WLastCall, is_external=, is_static_class=False, static_prefix=
private string _WLastCall ="";




// [DEBUG] Field: WSub, is_external=, is_static_class=False, static_prefix=
private int _WSub =0;




// [DEBUG] Field: WHeaderRecord, is_external=, is_static_class=False, static_prefix=
private WHeaderRecord _WHeaderRecord = new WHeaderRecord();




// [DEBUG] Field: WInitialisedRecord, is_external=, is_static_class=False, static_prefix=
private WInitialisedRecord _WInitialisedRecord = new WInitialisedRecord();




// [DEBUG] Field: D13Record, is_external=, is_static_class=False, static_prefix=
private string _D13Record ="";




// [DEBUG] Field: D13SedolHeaderRecord, is_external=, is_static_class=False, static_prefix=
private D13SedolHeaderRecord _D13SedolHeaderRecord = new D13SedolHeaderRecord();




// [DEBUG] Field: D13BalAcqDispRecord, is_external=, is_static_class=False, static_prefix=
private D13BalAcqDispRecord _D13BalAcqDispRecord = new D13BalAcqDispRecord();




// [DEBUG] Field: CgtabortLinkage, is_external=, is_static_class=False, static_prefix=
private CgtabortLinkage _CgtabortLinkage = new CgtabortLinkage();




// [DEBUG] Field: EqtpathLinkage, is_external=, is_static_class=False, static_prefix=
private EqtpathLinkage _EqtpathLinkage = new EqtpathLinkage();




// [DEBUG] Field: ADMIN_DATA_PATH, is_external=, is_static_class=False, static_prefix=
public const string ADMIN_DATA_PATH = "EQADMIN";




// [DEBUG] Field: USER_DATA_PATH, is_external=, is_static_class=False, static_prefix=
public const string USER_DATA_PATH = "EQUSER";




// [DEBUG] Field: MASTER_DATA_PATH, is_external=, is_static_class=False, static_prefix=
public const string MASTER_DATA_PATH = "EQMASTER";




// [DEBUG] Field: MAX_NO_OF_COSTS, is_external=, is_static_class=False, static_prefix=
public const int MAX_NO_OF_COSTS = 200;




// [DEBUG] Field: MAX_MASTER_RECORD_LEN, is_external=, is_static_class=False, static_prefix=
public const int MAX_MASTER_RECORD_LEN = 16270;




// [DEBUG] Field: ElcgmioLinkage1, is_external=, is_static_class=False, static_prefix=
private ElcgmioLinkage1 _ElcgmioLinkage1 = new ElcgmioLinkage1();




// [DEBUG] Field: FULL_COMP, is_external=, is_static_class=False, static_prefix=
public const int FULL_COMP = 6;




// [DEBUG] Field: PARTIAL_COMP, is_external=, is_static_class=False, static_prefix=
public const int PARTIAL_COMP = 9;




// [DEBUG] Field: SEDOL_WHATIF_COMP, is_external=, is_static_class=False, static_prefix=
public const int SEDOL_WHATIF_COMP = 7;




// [DEBUG] Field: SINGLE_SEDOL_COMP, is_external=, is_static_class=False, static_prefix=
public const int SINGLE_SEDOL_COMP = 7;




// [DEBUG] Field: YEAR_END_COMP, is_external=, is_static_class=False, static_prefix=
public const int YEAR_END_COMP = 8;




// [DEBUG] Field: ElcgmioLinkage2, is_external=, is_static_class=False, static_prefix=
private ElcgmioLinkage2 _ElcgmioLinkage2 = new ElcgmioLinkage2();




// [DEBUG] Field: Cgtdate2LinkageDate1, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate1 _Cgtdate2LinkageDate1 = new Cgtdate2LinkageDate1();




// [DEBUG] Field: Cgtdate2LinkageDate2, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate2 _Cgtdate2LinkageDate2 = new Cgtdate2LinkageDate2();




// [DEBUG] Field: Cgtdate2LinkageDate3, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate3 _Cgtdate2LinkageDate3 = new Cgtdate2LinkageDate3();




// [DEBUG] Field: Cgtdate2LinkageDate4, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate4 _Cgtdate2LinkageDate4 = new Cgtdate2LinkageDate4();




// [DEBUG] Field: Cgtdate2LinkageDate5, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate5 _Cgtdate2LinkageDate5 = new Cgtdate2LinkageDate5();




// [DEBUG] Field: Cgtdate2LinkageDate6, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate6 _Cgtdate2LinkageDate6 = new Cgtdate2LinkageDate6();




// [DEBUG] Field: Cgtdate2LinkageDate7, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate7 _Cgtdate2LinkageDate7 = new Cgtdate2LinkageDate7();




// [DEBUG] Field: Cgtdate2LinkageDate8, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate8 _Cgtdate2LinkageDate8 = new Cgtdate2LinkageDate8();




// [DEBUG] Field: Cgtdate2LinkageDate9, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate9 _Cgtdate2LinkageDate9 = new Cgtdate2LinkageDate9();




// [DEBUG] Field: Cgtdate2LinkageDate10, is_external=, is_static_class=False, static_prefix=
private Cgtdate2LinkageDate10 _Cgtdate2LinkageDate10 = new Cgtdate2LinkageDate10();




// Getter and Setter methods

// Standard Getter
public string GetWProgramName()
{
    return _WProgramName;
}

// Standard Setter
public void SetWProgramName(string value)
{
    _WProgramName = value;
}

// Get<>AsString()
public string GetWProgramNameAsString()
{
    return _WProgramName.PadRight(8);
}

// Set<>AsString()
public void SetWProgramNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WProgramName = value;
}

// Standard Getter
public string GetVersionNumber()
{
    return _VersionNumber;
}

// Standard Setter
public void SetVersionNumber(string value)
{
    _VersionNumber = value;
}

// Get<>AsString()
public string GetVersionNumberAsString()
{
    return _VersionNumber.PadRight(3);
}

// Set<>AsString()
public void SetVersionNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _VersionNumber = value;
}

// Standard Getter
public string GetWFileReturnCode()
{
    return _WFileReturnCode;
}

// Standard Setter
public void SetWFileReturnCode(string value)
{
    _WFileReturnCode = value;
}

// Get<>AsString()
public string GetWFileReturnCodeAsString()
{
    return _WFileReturnCode.PadRight(2);
}

// Set<>AsString()
public void SetWFileReturnCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WFileReturnCode = value;
}

// Standard Getter
public string GetExportFileName()
{
    return _ExportFileName;
}

// Standard Setter
public void SetExportFileName(string value)
{
    _ExportFileName = value;
}

// Get<>AsString()
public string GetExportFileNameAsString()
{
    return _ExportFileName.PadRight(256);
}

// Set<>AsString()
public void SetExportFileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _ExportFileName = value;
}

// Standard Getter
public ReportFile GetReportFile()
{
    return _ReportFile;
}

// Standard Setter
public void SetReportFile(ReportFile value)
{
    _ReportFile = value;
}

// Get<>AsString()
public string GetReportFileAsString()
{
    return _ReportFile != null ? _ReportFile.GetReportFileAsString() : "";
}

// Set<>AsString()
public void SetReportFileAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ReportFile == null)
    {
        _ReportFile = new ReportFile();
    }
    _ReportFile.SetReportFileAsString(value);
}

// Standard Getter
public DateStamp GetDateStamp()
{
    return _DateStamp;
}

// Standard Setter
public void SetDateStamp(DateStamp value)
{
    _DateStamp = value;
}

// Get<>AsString()
public string GetDateStampAsString()
{
    return _DateStamp != null ? _DateStamp.GetDateStampAsString() : "";
}

// Set<>AsString()
public void SetDateStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_DateStamp == null)
    {
        _DateStamp = new DateStamp();
    }
    _DateStamp.SetDateStampAsString(value);
}

// Standard Getter
public int GetWExportCount()
{
    return _WExportCount;
}

// Standard Setter
public void SetWExportCount(int value)
{
    _WExportCount = value;
}

// Get<>AsString()
public string GetWExportCountAsString()
{
    return _WExportCount.ToString().PadLeft(0, '0');
}

// Set<>AsString()
public void SetWExportCountAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WExportCount = parsed;
}

// Standard Getter
public int GetWReadCount()
{
    return _WReadCount;
}

// Standard Setter
public void SetWReadCount(int value)
{
    _WReadCount = value;
}

// Get<>AsString()
public string GetWReadCountAsString()
{
    return _WReadCount.ToString().PadLeft(0, '0');
}

// Set<>AsString()
public void SetWReadCountAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WReadCount = parsed;
}

// Standard Getter
public string GetWLastCall()
{
    return _WLastCall;
}

// Standard Setter
public void SetWLastCall(string value)
{
    _WLastCall = value;
}

// Get<>AsString()
public string GetWLastCallAsString()
{
    return _WLastCall.PadRight(1);
}

// Set<>AsString()
public void SetWLastCallAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WLastCall = value;
}

// Standard Getter
public int GetWSub()
{
    return _WSub;
}

// Standard Setter
public void SetWSub(int value)
{
    _WSub = value;
}

// Get<>AsString()
public string GetWSubAsString()
{
    return _WSub.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetWSubAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WSub = parsed;
}

// Standard Getter
public WHeaderRecord GetWHeaderRecord()
{
    return _WHeaderRecord;
}

// Standard Setter
public void SetWHeaderRecord(WHeaderRecord value)
{
    _WHeaderRecord = value;
}

// Get<>AsString()
public string GetWHeaderRecordAsString()
{
    return _WHeaderRecord != null ? _WHeaderRecord.GetWHeaderRecordAsString() : "";
}

// Set<>AsString()
public void SetWHeaderRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WHeaderRecord == null)
    {
        _WHeaderRecord = new WHeaderRecord();
    }
    _WHeaderRecord.SetWHeaderRecordAsString(value);
}

// Standard Getter
public WInitialisedRecord GetWInitialisedRecord()
{
    return _WInitialisedRecord;
}

// Standard Setter
public void SetWInitialisedRecord(WInitialisedRecord value)
{
    _WInitialisedRecord = value;
}

// Get<>AsString()
public string GetWInitialisedRecordAsString()
{
    return _WInitialisedRecord != null ? _WInitialisedRecord.GetWInitialisedRecordAsString() : "";
}

// Set<>AsString()
public void SetWInitialisedRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WInitialisedRecord == null)
    {
        _WInitialisedRecord = new WInitialisedRecord();
    }
    _WInitialisedRecord.SetWInitialisedRecordAsString(value);
}

// Standard Getter
public string GetD13Record()
{
    return _D13Record;
}

// Standard Setter
public void SetD13Record(string value)
{
    _D13Record = value;
}

// Get<>AsString()
public string GetD13RecordAsString()
{
    return _D13Record.PadRight(1870);
}

// Set<>AsString()
public void SetD13RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D13Record = value;
}

// Standard Getter
public D13SedolHeaderRecord GetD13SedolHeaderRecord()
{
    return _D13SedolHeaderRecord;
}

// Standard Setter
public void SetD13SedolHeaderRecord(D13SedolHeaderRecord value)
{
    _D13SedolHeaderRecord = value;
}

// Get<>AsString()
public string GetD13SedolHeaderRecordAsString()
{
    return _D13SedolHeaderRecord != null ? _D13SedolHeaderRecord.GetD13SedolHeaderRecordAsString() : "";
}

// Set<>AsString()
public void SetD13SedolHeaderRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D13SedolHeaderRecord == null)
    {
        _D13SedolHeaderRecord = new D13SedolHeaderRecord();
    }
    _D13SedolHeaderRecord.SetD13SedolHeaderRecordAsString(value);
}

// Standard Getter
public D13BalAcqDispRecord GetD13BalAcqDispRecord()
{
    return _D13BalAcqDispRecord;
}

// Standard Setter
public void SetD13BalAcqDispRecord(D13BalAcqDispRecord value)
{
    _D13BalAcqDispRecord = value;
}

// Get<>AsString()
public string GetD13BalAcqDispRecordAsString()
{
    return _D13BalAcqDispRecord != null ? _D13BalAcqDispRecord.GetD13BalAcqDispRecordAsString() : "";
}

// Set<>AsString()
public void SetD13BalAcqDispRecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D13BalAcqDispRecord == null)
    {
        _D13BalAcqDispRecord = new D13BalAcqDispRecord();
    }
    _D13BalAcqDispRecord.SetD13BalAcqDispRecordAsString(value);
}

// Standard Getter
public CgtabortLinkage GetCgtabortLinkage()
{
    return _CgtabortLinkage;
}

// Standard Setter
public void SetCgtabortLinkage(CgtabortLinkage value)
{
    _CgtabortLinkage = value;
}

// Get<>AsString()
public string GetCgtabortLinkageAsString()
{
    return _CgtabortLinkage != null ? _CgtabortLinkage.GetCgtabortLinkageAsString() : "";
}

// Set<>AsString()
public void SetCgtabortLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtabortLinkage == null)
    {
        _CgtabortLinkage = new CgtabortLinkage();
    }
    _CgtabortLinkage.SetCgtabortLinkageAsString(value);
}

// Standard Getter
public EqtpathLinkage GetEqtpathLinkage()
{
    return _EqtpathLinkage;
}

// Standard Setter
public void SetEqtpathLinkage(EqtpathLinkage value)
{
    _EqtpathLinkage = value;
}

// Get<>AsString()
public string GetEqtpathLinkageAsString()
{
    return _EqtpathLinkage != null ? _EqtpathLinkage.GetEqtpathLinkageAsString() : "";
}

// Set<>AsString()
public void SetEqtpathLinkageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_EqtpathLinkage == null)
    {
        _EqtpathLinkage = new EqtpathLinkage();
    }
    _EqtpathLinkage.SetEqtpathLinkageAsString(value);
}

// Standard Getter
public ElcgmioLinkage1 GetElcgmioLinkage1()
{
    return _ElcgmioLinkage1;
}

// Standard Setter
public void SetElcgmioLinkage1(ElcgmioLinkage1 value)
{
    _ElcgmioLinkage1 = value;
}

// Get<>AsString()
public string GetElcgmioLinkage1AsString()
{
    return _ElcgmioLinkage1 != null ? _ElcgmioLinkage1.GetElcgmioLinkage1AsString() : "";
}

// Set<>AsString()
public void SetElcgmioLinkage1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ElcgmioLinkage1 == null)
    {
        _ElcgmioLinkage1 = new ElcgmioLinkage1();
    }
    _ElcgmioLinkage1.SetElcgmioLinkage1AsString(value);
}

// Standard Getter
public ElcgmioLinkage2 GetElcgmioLinkage2()
{
    return _ElcgmioLinkage2;
}

// Standard Setter
public void SetElcgmioLinkage2(ElcgmioLinkage2 value)
{
    _ElcgmioLinkage2 = value;
}

// Get<>AsString()
public string GetElcgmioLinkage2AsString()
{
    return _ElcgmioLinkage2 != null ? _ElcgmioLinkage2.GetElcgmioLinkage2AsString() : "";
}

// Set<>AsString()
public void SetElcgmioLinkage2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_ElcgmioLinkage2 == null)
    {
        _ElcgmioLinkage2 = new ElcgmioLinkage2();
    }
    _ElcgmioLinkage2.SetElcgmioLinkage2AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate1 GetCgtdate2LinkageDate1()
{
    return _Cgtdate2LinkageDate1;
}

// Standard Setter
public void SetCgtdate2LinkageDate1(Cgtdate2LinkageDate1 value)
{
    _Cgtdate2LinkageDate1 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate1AsString()
{
    return _Cgtdate2LinkageDate1 != null ? _Cgtdate2LinkageDate1.GetCgtdate2LinkageDate1AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate1 == null)
    {
        _Cgtdate2LinkageDate1 = new Cgtdate2LinkageDate1();
    }
    _Cgtdate2LinkageDate1.SetCgtdate2LinkageDate1AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate2 GetCgtdate2LinkageDate2()
{
    return _Cgtdate2LinkageDate2;
}

// Standard Setter
public void SetCgtdate2LinkageDate2(Cgtdate2LinkageDate2 value)
{
    _Cgtdate2LinkageDate2 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate2AsString()
{
    return _Cgtdate2LinkageDate2 != null ? _Cgtdate2LinkageDate2.GetCgtdate2LinkageDate2AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate2 == null)
    {
        _Cgtdate2LinkageDate2 = new Cgtdate2LinkageDate2();
    }
    _Cgtdate2LinkageDate2.SetCgtdate2LinkageDate2AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate3 GetCgtdate2LinkageDate3()
{
    return _Cgtdate2LinkageDate3;
}

// Standard Setter
public void SetCgtdate2LinkageDate3(Cgtdate2LinkageDate3 value)
{
    _Cgtdate2LinkageDate3 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate3AsString()
{
    return _Cgtdate2LinkageDate3 != null ? _Cgtdate2LinkageDate3.GetCgtdate2LinkageDate3AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate3 == null)
    {
        _Cgtdate2LinkageDate3 = new Cgtdate2LinkageDate3();
    }
    _Cgtdate2LinkageDate3.SetCgtdate2LinkageDate3AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate4 GetCgtdate2LinkageDate4()
{
    return _Cgtdate2LinkageDate4;
}

// Standard Setter
public void SetCgtdate2LinkageDate4(Cgtdate2LinkageDate4 value)
{
    _Cgtdate2LinkageDate4 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate4AsString()
{
    return _Cgtdate2LinkageDate4 != null ? _Cgtdate2LinkageDate4.GetCgtdate2LinkageDate4AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate4 == null)
    {
        _Cgtdate2LinkageDate4 = new Cgtdate2LinkageDate4();
    }
    _Cgtdate2LinkageDate4.SetCgtdate2LinkageDate4AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate5 GetCgtdate2LinkageDate5()
{
    return _Cgtdate2LinkageDate5;
}

// Standard Setter
public void SetCgtdate2LinkageDate5(Cgtdate2LinkageDate5 value)
{
    _Cgtdate2LinkageDate5 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate5AsString()
{
    return _Cgtdate2LinkageDate5 != null ? _Cgtdate2LinkageDate5.GetCgtdate2LinkageDate5AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate5AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate5 == null)
    {
        _Cgtdate2LinkageDate5 = new Cgtdate2LinkageDate5();
    }
    _Cgtdate2LinkageDate5.SetCgtdate2LinkageDate5AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate6 GetCgtdate2LinkageDate6()
{
    return _Cgtdate2LinkageDate6;
}

// Standard Setter
public void SetCgtdate2LinkageDate6(Cgtdate2LinkageDate6 value)
{
    _Cgtdate2LinkageDate6 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate6AsString()
{
    return _Cgtdate2LinkageDate6 != null ? _Cgtdate2LinkageDate6.GetCgtdate2LinkageDate6AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate6 == null)
    {
        _Cgtdate2LinkageDate6 = new Cgtdate2LinkageDate6();
    }
    _Cgtdate2LinkageDate6.SetCgtdate2LinkageDate6AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate7 GetCgtdate2LinkageDate7()
{
    return _Cgtdate2LinkageDate7;
}

// Standard Setter
public void SetCgtdate2LinkageDate7(Cgtdate2LinkageDate7 value)
{
    _Cgtdate2LinkageDate7 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate7AsString()
{
    return _Cgtdate2LinkageDate7 != null ? _Cgtdate2LinkageDate7.GetCgtdate2LinkageDate7AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate7 == null)
    {
        _Cgtdate2LinkageDate7 = new Cgtdate2LinkageDate7();
    }
    _Cgtdate2LinkageDate7.SetCgtdate2LinkageDate7AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate8 GetCgtdate2LinkageDate8()
{
    return _Cgtdate2LinkageDate8;
}

// Standard Setter
public void SetCgtdate2LinkageDate8(Cgtdate2LinkageDate8 value)
{
    _Cgtdate2LinkageDate8 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate8AsString()
{
    return _Cgtdate2LinkageDate8 != null ? _Cgtdate2LinkageDate8.GetCgtdate2LinkageDate8AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate8 == null)
    {
        _Cgtdate2LinkageDate8 = new Cgtdate2LinkageDate8();
    }
    _Cgtdate2LinkageDate8.SetCgtdate2LinkageDate8AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate9 GetCgtdate2LinkageDate9()
{
    return _Cgtdate2LinkageDate9;
}

// Standard Setter
public void SetCgtdate2LinkageDate9(Cgtdate2LinkageDate9 value)
{
    _Cgtdate2LinkageDate9 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate9AsString()
{
    return _Cgtdate2LinkageDate9 != null ? _Cgtdate2LinkageDate9.GetCgtdate2LinkageDate9AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate9 == null)
    {
        _Cgtdate2LinkageDate9 = new Cgtdate2LinkageDate9();
    }
    _Cgtdate2LinkageDate9.SetCgtdate2LinkageDate9AsString(value);
}

// Standard Getter
public Cgtdate2LinkageDate10 GetCgtdate2LinkageDate10()
{
    return _Cgtdate2LinkageDate10;
}

// Standard Setter
public void SetCgtdate2LinkageDate10(Cgtdate2LinkageDate10 value)
{
    _Cgtdate2LinkageDate10 = value;
}

// Get<>AsString()
public string GetCgtdate2LinkageDate10AsString()
{
    return _Cgtdate2LinkageDate10 != null ? _Cgtdate2LinkageDate10.GetCgtdate2LinkageDate10AsString() : "";
}

// Set<>AsString()
public void SetCgtdate2LinkageDate10AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Cgtdate2LinkageDate10 == null)
    {
        _Cgtdate2LinkageDate10 = new Cgtdate2LinkageDate10();
    }
    _Cgtdate2LinkageDate10.SetCgtdate2LinkageDate10AsString(value);
}


}}