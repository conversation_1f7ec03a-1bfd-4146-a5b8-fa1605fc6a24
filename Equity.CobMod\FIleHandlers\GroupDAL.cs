using System;
using Legacy4.Equity.CobMod.Models;
using WK.UK.CCH.Equity.CobolDataTransformation;
namespace Legacy4.Equity.CobMod.Helper
{
    public class GroupDAL
    {
        private GroupDA _groupDA;

        public void ProcessGroupAction(string action,  GroupDataLinkage linkage)
        {
            // Simulate timing start
            Console.WriteLine("Timing start for Group");

            switch (action)
            {
                case CommonLinkage.OPEN_INPUT:
                    _groupDA = new GroupDA();
                    break;

                case CommonLinkage.READ_NEXT:
                    if (_groupDA != null)
                    {
                        linkage.RecordArea = _groupDA.ReadNext();
                    }
                    break;

                case CommonLinkage.CLOSE_FILE:
                    // Do nothing
                    break;

                default:
                    throw new ArgumentException("Invalid Group action");
            }

            // Simulate timing end
            Console.WriteLine("Timing end for Group");
        }
    }
}
