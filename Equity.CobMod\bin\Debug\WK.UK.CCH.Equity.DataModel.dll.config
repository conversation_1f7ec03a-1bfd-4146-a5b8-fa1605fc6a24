<?xml version="1.0"?>
<configuration>
    <configSections>
    </configSections>
    <connectionStrings>
        <add name="WK.UK.CCH.Equity.DataModel.Properties.Settings.EquityNetConnectionString" connectionString="Data Source=EQVMSQL2K5;Initial Catalog=Equity-e;Integrated Security=True" providerName="System.Data.SqlClient"/>
        <add name="WK.UK.CCH.Equity.DataModel.Properties.Settings.EquityNetConnectionString" connectionString="Data Source=MichaelPendred;Initial Catalog=Equity-e;Integrated Security=True;Pooling=False" providerName="System.Data.SqlClient"/>
        <add name="WK.UK.CCH.Equity.DataModel.Properties.Settings.EquityNetConnectionString1" connectionString="Data Source=EQDEVSQL;Initial Catalog=EquityNet;Integrated Security=True;Pooling=False" providerName="System.Data.SqlClient"/>
    </connectionStrings>
<startup><supportedRuntime version="v2.0.50727"/></startup></configuration>
