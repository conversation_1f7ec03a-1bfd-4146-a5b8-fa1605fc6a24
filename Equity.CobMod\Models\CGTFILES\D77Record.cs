using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D77Record Data Structure

public class D77Record
{
    private static int _size = 42;
    // [DEBUG] Class: D77Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler98, is_external=, is_static_class=False, static_prefix=
    private string _Filler98 ="";
    
    
    
    
    // [DEBUG] Field: D77FundCode, is_external=, is_static_class=False, static_prefix=
    private string _D77FundCode ="";
    
    
    
    
    // [DEBUG] Field: Filler99, is_external=, is_static_class=False, static_prefix=
    private string _Filler99 ="";
    
    
    
    
    // [DEBUG] Field: D77SedolCode, is_external=, is_static_class=False, static_prefix=
    private string _D77SedolCode ="";
    
    
    
    
    // [DEBUG] Field: Filler100, is_external=, is_static_class=False, static_prefix=
    private string _Filler100 ="";
    
    
    
    
    // [DEBUG] Field: D77Holding, is_external=, is_static_class=False, static_prefix=
    private decimal _D77Holding =0;
    
    
    
    
    // [DEBUG] Field: Filler101, is_external=, is_static_class=False, static_prefix=
    private string _Filler101 ="";
    
    
    
    
    // [DEBUG] Field: D77DisposalDate, is_external=, is_static_class=False, static_prefix=
    private string _D77DisposalDate ="";
    
    
    
    
    // [DEBUG] Field: Filler102, is_external=, is_static_class=False, static_prefix=
    private string _Filler102 ="";
    
    
    
    
    // [DEBUG] Field: D77TodaysDate, is_external=, is_static_class=False, static_prefix=
    private string _D77TodaysDate ="";
    
    
    
    
    // [DEBUG] Field: Filler103, is_external=, is_static_class=False, static_prefix=
    private string _Filler103 ="";
    
    
    
    
    // [DEBUG] Field: D77CapitalGain, is_external=, is_static_class=False, static_prefix=
    private decimal _D77CapitalGain =0;
    
    
    
    
    // [DEBUG] Field: Filler104, is_external=, is_static_class=False, static_prefix=
    private string _Filler104 ="";
    
    
    
    
    // [DEBUG] Field: D77CapitalLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D77CapitalLoss =0;
    
    
    
    
    // [DEBUG] Field: Filler105, is_external=, is_static_class=False, static_prefix=
    private string _Filler105 ="";
    
    
    
    
    // [DEBUG] Field: D77HoldingFlag, is_external=, is_static_class=False, static_prefix=
    private string _D77HoldingFlag ="";
    
    
    
    
    // [DEBUG] Field: Filler106, is_external=, is_static_class=False, static_prefix=
    private string _Filler106 ="";
    
    
    
    
    // [DEBUG] Field: D77TrancheFlag, is_external=, is_static_class=False, static_prefix=
    private string _D77TrancheFlag ="";
    
    
    
    
    // [DEBUG] Field: Filler107, is_external=, is_static_class=False, static_prefix=
    private string _Filler107 ="";
    
    
    
    
    // [DEBUG] Field: D77HoldingSign, is_external=, is_static_class=False, static_prefix=
    private string _D77HoldingSign ="";
    
    
    
    
    // [DEBUG] Field: Filler108, is_external=, is_static_class=False, static_prefix=
    private string _Filler108 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD77RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler98.PadRight(0));
        result.Append(_D77FundCode.PadRight(0));
        result.Append(_Filler99.PadRight(0));
        result.Append(_D77SedolCode.PadRight(0));
        result.Append(_Filler100.PadRight(0));
        result.Append(_D77Holding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler101.PadRight(0));
        result.Append(_D77DisposalDate.PadRight(0));
        result.Append(_Filler102.PadRight(0));
        result.Append(_D77TodaysDate.PadRight(0));
        result.Append(_Filler103.PadRight(0));
        result.Append(_D77CapitalGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler104.PadRight(0));
        result.Append(_D77CapitalLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler105.PadRight(0));
        result.Append(_D77HoldingFlag.PadRight(0));
        result.Append(_Filler106.PadRight(0));
        result.Append(_D77TrancheFlag.PadRight(0));
        result.Append(_Filler107.PadRight(0));
        result.Append(_D77HoldingSign.PadRight(0));
        result.Append(_Filler108.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD77RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller98(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD77FundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller99(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD77SedolCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller100(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD77Holding(parsedDec);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller101(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD77DisposalDate(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller102(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD77TodaysDate(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller103(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD77CapitalGain(parsedDec);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller104(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD77CapitalLoss(parsedDec);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller105(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD77HoldingFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller106(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD77TrancheFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller107(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD77HoldingSign(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller108(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD77RecordAsString();
    }
    // Set<>String Override function
    public void SetD77Record(string value)
    {
        SetD77RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller98()
    {
        return _Filler98;
    }
    
    // Standard Setter
    public void SetFiller98(string value)
    {
        _Filler98 = value;
    }
    
    // Get<>AsString()
    public string GetFiller98AsString()
    {
        return _Filler98.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller98AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler98 = value;
    }
    
    // Standard Getter
    public string GetD77FundCode()
    {
        return _D77FundCode;
    }
    
    // Standard Setter
    public void SetD77FundCode(string value)
    {
        _D77FundCode = value;
    }
    
    // Get<>AsString()
    public string GetD77FundCodeAsString()
    {
        return _D77FundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD77FundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D77FundCode = value;
    }
    
    // Standard Getter
    public string GetFiller99()
    {
        return _Filler99;
    }
    
    // Standard Setter
    public void SetFiller99(string value)
    {
        _Filler99 = value;
    }
    
    // Get<>AsString()
    public string GetFiller99AsString()
    {
        return _Filler99.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller99AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler99 = value;
    }
    
    // Standard Getter
    public string GetD77SedolCode()
    {
        return _D77SedolCode;
    }
    
    // Standard Setter
    public void SetD77SedolCode(string value)
    {
        _D77SedolCode = value;
    }
    
    // Get<>AsString()
    public string GetD77SedolCodeAsString()
    {
        return _D77SedolCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD77SedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D77SedolCode = value;
    }
    
    // Standard Getter
    public string GetFiller100()
    {
        return _Filler100;
    }
    
    // Standard Setter
    public void SetFiller100(string value)
    {
        _Filler100 = value;
    }
    
    // Get<>AsString()
    public string GetFiller100AsString()
    {
        return _Filler100.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller100AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler100 = value;
    }
    
    // Standard Getter
    public decimal GetD77Holding()
    {
        return _D77Holding;
    }
    
    // Standard Setter
    public void SetD77Holding(decimal value)
    {
        _D77Holding = value;
    }
    
    // Get<>AsString()
    public string GetD77HoldingAsString()
    {
        return _D77Holding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD77HoldingAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D77Holding = parsed;
    }
    
    // Standard Getter
    public string GetFiller101()
    {
        return _Filler101;
    }
    
    // Standard Setter
    public void SetFiller101(string value)
    {
        _Filler101 = value;
    }
    
    // Get<>AsString()
    public string GetFiller101AsString()
    {
        return _Filler101.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller101AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler101 = value;
    }
    
    // Standard Getter
    public string GetD77DisposalDate()
    {
        return _D77DisposalDate;
    }
    
    // Standard Setter
    public void SetD77DisposalDate(string value)
    {
        _D77DisposalDate = value;
    }
    
    // Get<>AsString()
    public string GetD77DisposalDateAsString()
    {
        return _D77DisposalDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD77DisposalDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D77DisposalDate = value;
    }
    
    // Standard Getter
    public string GetFiller102()
    {
        return _Filler102;
    }
    
    // Standard Setter
    public void SetFiller102(string value)
    {
        _Filler102 = value;
    }
    
    // Get<>AsString()
    public string GetFiller102AsString()
    {
        return _Filler102.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller102AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler102 = value;
    }
    
    // Standard Getter
    public string GetD77TodaysDate()
    {
        return _D77TodaysDate;
    }
    
    // Standard Setter
    public void SetD77TodaysDate(string value)
    {
        _D77TodaysDate = value;
    }
    
    // Get<>AsString()
    public string GetD77TodaysDateAsString()
    {
        return _D77TodaysDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD77TodaysDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D77TodaysDate = value;
    }
    
    // Standard Getter
    public string GetFiller103()
    {
        return _Filler103;
    }
    
    // Standard Setter
    public void SetFiller103(string value)
    {
        _Filler103 = value;
    }
    
    // Get<>AsString()
    public string GetFiller103AsString()
    {
        return _Filler103.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller103AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler103 = value;
    }
    
    // Standard Getter
    public decimal GetD77CapitalGain()
    {
        return _D77CapitalGain;
    }
    
    // Standard Setter
    public void SetD77CapitalGain(decimal value)
    {
        _D77CapitalGain = value;
    }
    
    // Get<>AsString()
    public string GetD77CapitalGainAsString()
    {
        return _D77CapitalGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD77CapitalGainAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D77CapitalGain = parsed;
    }
    
    // Standard Getter
    public string GetFiller104()
    {
        return _Filler104;
    }
    
    // Standard Setter
    public void SetFiller104(string value)
    {
        _Filler104 = value;
    }
    
    // Get<>AsString()
    public string GetFiller104AsString()
    {
        return _Filler104.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller104AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler104 = value;
    }
    
    // Standard Getter
    public decimal GetD77CapitalLoss()
    {
        return _D77CapitalLoss;
    }
    
    // Standard Setter
    public void SetD77CapitalLoss(decimal value)
    {
        _D77CapitalLoss = value;
    }
    
    // Get<>AsString()
    public string GetD77CapitalLossAsString()
    {
        return _D77CapitalLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD77CapitalLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D77CapitalLoss = parsed;
    }
    
    // Standard Getter
    public string GetFiller105()
    {
        return _Filler105;
    }
    
    // Standard Setter
    public void SetFiller105(string value)
    {
        _Filler105 = value;
    }
    
    // Get<>AsString()
    public string GetFiller105AsString()
    {
        return _Filler105.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller105AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler105 = value;
    }
    
    // Standard Getter
    public string GetD77HoldingFlag()
    {
        return _D77HoldingFlag;
    }
    
    // Standard Setter
    public void SetD77HoldingFlag(string value)
    {
        _D77HoldingFlag = value;
    }
    
    // Get<>AsString()
    public string GetD77HoldingFlagAsString()
    {
        return _D77HoldingFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD77HoldingFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D77HoldingFlag = value;
    }
    
    // Standard Getter
    public string GetFiller106()
    {
        return _Filler106;
    }
    
    // Standard Setter
    public void SetFiller106(string value)
    {
        _Filler106 = value;
    }
    
    // Get<>AsString()
    public string GetFiller106AsString()
    {
        return _Filler106.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller106AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler106 = value;
    }
    
    // Standard Getter
    public string GetD77TrancheFlag()
    {
        return _D77TrancheFlag;
    }
    
    // Standard Setter
    public void SetD77TrancheFlag(string value)
    {
        _D77TrancheFlag = value;
    }
    
    // Get<>AsString()
    public string GetD77TrancheFlagAsString()
    {
        return _D77TrancheFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD77TrancheFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D77TrancheFlag = value;
    }
    
    // Standard Getter
    public string GetFiller107()
    {
        return _Filler107;
    }
    
    // Standard Setter
    public void SetFiller107(string value)
    {
        _Filler107 = value;
    }
    
    // Get<>AsString()
    public string GetFiller107AsString()
    {
        return _Filler107.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller107AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler107 = value;
    }
    
    // Standard Getter
    public string GetD77HoldingSign()
    {
        return _D77HoldingSign;
    }
    
    // Standard Setter
    public void SetD77HoldingSign(string value)
    {
        _D77HoldingSign = value;
    }
    
    // Get<>AsString()
    public string GetD77HoldingSignAsString()
    {
        return _D77HoldingSign.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD77HoldingSignAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D77HoldingSign = value;
    }
    
    // Standard Getter
    public string GetFiller108()
    {
        return _Filler108;
    }
    
    // Standard Setter
    public void SetFiller108(string value)
    {
        _Filler108 = value;
    }
    
    // Get<>AsString()
    public string GetFiller108AsString()
    {
        return _Filler108.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller108AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler108 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}