using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtPartProcessedKeys Data Structure

public class WtPartProcessedKeys
{
    private static int _size = 51;
    // [DEBUG] Class: WtPartProcessedKeys, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler450, is_external=, is_static_class=False, static_prefix=
    private string _Filler450 ="KEYS PART PROCESSED============";
    
    
    
    
    // [DEBUG] Field: WtpMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtpMaxTableSize =4000;
    
    
    
    
    // [DEBUG] Field: WtpOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtpOccurs =4000;
    
    
    
    
    // [DEBUG] Field: WtpKeysUnprocessedTable, is_external=, is_static_class=False, static_prefix=
    private WtpKeysUnprocessedTable _WtpKeysUnprocessedTable = new WtpKeysUnprocessedTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtPartProcessedKeysAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler450.PadRight(32));
        result.Append(_WtpMaxTableSize.ToString().PadLeft(4, '0'));
        result.Append(_WtpOccurs.ToString().PadLeft(4, '0'));
        result.Append(_WtpKeysUnprocessedTable.GetWtpKeysUnprocessedTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtPartProcessedKeysAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 32 <= data.Length)
        {
            string extracted = data.Substring(offset, 32).Trim();
            SetFiller450(extracted);
        }
        offset += 32;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtpMaxTableSize(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtpOccurs(parsedInt);
        }
        offset += 4;
        if (offset + 11 <= data.Length)
        {
            _WtpKeysUnprocessedTable.SetWtpKeysUnprocessedTableAsString(data.Substring(offset, 11));
        }
        else
        {
            _WtpKeysUnprocessedTable.SetWtpKeysUnprocessedTableAsString(data.Substring(offset));
        }
        offset += 11;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtPartProcessedKeysAsString();
    }
    // Set<>String Override function
    public void SetWtPartProcessedKeys(string value)
    {
        SetWtPartProcessedKeysAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller450()
    {
        return _Filler450;
    }
    
    // Standard Setter
    public void SetFiller450(string value)
    {
        _Filler450 = value;
    }
    
    // Get<>AsString()
    public string GetFiller450AsString()
    {
        return _Filler450.PadRight(32);
    }
    
    // Set<>AsString()
    public void SetFiller450AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler450 = value;
    }
    
    // Standard Getter
    public int GetWtpMaxTableSize()
    {
        return _WtpMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtpMaxTableSize(int value)
    {
        _WtpMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtpMaxTableSizeAsString()
    {
        return _WtpMaxTableSize.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtpMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtpMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtpOccurs()
    {
        return _WtpOccurs;
    }
    
    // Standard Setter
    public void SetWtpOccurs(int value)
    {
        _WtpOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtpOccursAsString()
    {
        return _WtpOccurs.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtpOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtpOccurs = parsed;
    }
    
    // Standard Getter
    public WtpKeysUnprocessedTable GetWtpKeysUnprocessedTable()
    {
        return _WtpKeysUnprocessedTable;
    }
    
    // Standard Setter
    public void SetWtpKeysUnprocessedTable(WtpKeysUnprocessedTable value)
    {
        _WtpKeysUnprocessedTable = value;
    }
    
    // Get<>AsString()
    public string GetWtpKeysUnprocessedTableAsString()
    {
        return _WtpKeysUnprocessedTable != null ? _WtpKeysUnprocessedTable.GetWtpKeysUnprocessedTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtpKeysUnprocessedTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtpKeysUnprocessedTable == null)
        {
            _WtpKeysUnprocessedTable = new WtpKeysUnprocessedTable();
        }
        _WtpKeysUnprocessedTable.SetWtpKeysUnprocessedTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtpKeysUnprocessedTable(string value)
    {
        _WtpKeysUnprocessedTable.SetWtpKeysUnprocessedTableAsString(value);
    }
    // Nested Class: WtpKeysUnprocessedTable
    public class WtpKeysUnprocessedTable
    {
        private static int _size = 11;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtpCalSedol, is_external=, is_static_class=False, static_prefix=
        private string[] _WtpCalSedol = new string[4000];
        
        
        
        
    public WtpKeysUnprocessedTable() {}
    
    public WtpKeysUnprocessedTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        for (int i = 0; i < 4000; i++)
        {
            string value = data.Substring(offset, 11);
            _WtpCalSedol[i] = value.Trim();
            offset += 11;
        }
        
    }
    
    // Serialization methods
    public string GetWtpKeysUnprocessedTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 4000; i++)
        {
            result.Append(_WtpCalSedol[i].PadRight(11));
        }
        
        return result.ToString();
    }
    
    public void SetWtpKeysUnprocessedTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 4000; i++)
        {
            if (offset + 11 > data.Length) break;
            string val = data.Substring(offset, 11);
            
            _WtpCalSedol[i] = val.Trim();
            offset += 11;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtpCalSedol
    public string GetWtpCalSedolAt(int index)
    {
        return _WtpCalSedol[index];
    }
    
    public void SetWtpCalSedolAt(int index, string value)
    {
        _WtpCalSedol[index] = value;
    }
    
    public string GetWtpCalSedolAsStringAt(int index)
    {
        return _WtpCalSedol[index].PadRight(11);
    }
    
    public void SetWtpCalSedolAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _WtpCalSedol[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetWtpCalSedol()
    {
        return _WtpCalSedol != null && _WtpCalSedol.Length > 0
        ? _WtpCalSedol[0]
        : default(string);
    }
    
    public void SetWtpCalSedol(string value)
    {
        if (_WtpCalSedol == null || _WtpCalSedol.Length == 0)
        _WtpCalSedol = new string[1];
        _WtpCalSedol[0] = value;
    }
    
    public string GetWtpCalSedolAsString()
    {
        return _WtpCalSedol != null && _WtpCalSedol.Length > 0
        ? _WtpCalSedol[0].ToString()
        : string.Empty;
    }
    
    public void SetWtpCalSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_WtpCalSedol == null || _WtpCalSedol.Length == 0)
        _WtpCalSedol = new string[1];
        
        _WtpCalSedol[0] = value;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}