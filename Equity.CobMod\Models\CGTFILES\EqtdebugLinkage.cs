using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing EqtdebugLinkage Data Structure

public class EqtdebugLinkage
{
    private static int _size = 240;
    // [DEBUG] Class: EqtdebugLinkage, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: EqtdebugText, is_external=, is_static_class=False, static_prefix=
    private EqtdebugText _EqtdebugText = new EqtdebugText();
    
    
    
    
    
    // Serialization methods
    public string GetEqtdebugLinkageAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_EqtdebugText.GetEqtdebugTextAsString());
        
        return result.ToString();
    }
    
    public void SetEqtdebugLinkageAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 240 <= data.Length)
        {
            _EqtdebugText.SetEqtdebugTextAsString(data.Substring(offset, 240));
        }
        else
        {
            _EqtdebugText.SetEqtdebugTextAsString(data.Substring(offset));
        }
        offset += 240;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetEqtdebugLinkageAsString();
    }
    // Set<>String Override function
    public void SetEqtdebugLinkage(string value)
    {
        SetEqtdebugLinkageAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public EqtdebugText GetEqtdebugText()
    {
        return _EqtdebugText;
    }
    
    // Standard Setter
    public void SetEqtdebugText(EqtdebugText value)
    {
        _EqtdebugText = value;
    }
    
    // Get<>AsString()
    public string GetEqtdebugTextAsString()
    {
        return _EqtdebugText != null ? _EqtdebugText.GetEqtdebugTextAsString() : "";
    }
    
    // Set<>AsString()
    public void SetEqtdebugTextAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_EqtdebugText == null)
        {
            _EqtdebugText = new EqtdebugText();
        }
        _EqtdebugText.SetEqtdebugTextAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetEqtdebugText(string value)
    {
        _EqtdebugText.SetEqtdebugTextAsString(value);
    }
    // Nested Class: EqtdebugText
    public class EqtdebugText
    {
        private static int _size = 240;
        
        // Fields in the class
        
        
        // [DEBUG] Field: EqtdebugTextLine1, is_external=, is_static_class=False, static_prefix=
        private string _EqtdebugTextLine1 ="";
        
        
        // 88-level condition checks for EqtdebugTextLine1
        public bool IsEqtdebugTextLine1Empty()
        {
            if (this._EqtdebugTextLine1 == "SPACES") return true;
            return false;
        }
        
        
        // [DEBUG] Field: EqtdebugTextLine2, is_external=, is_static_class=False, static_prefix=
        private string _EqtdebugTextLine2 ="";
        
        
        // 88-level condition checks for EqtdebugTextLine2
        public bool IsEqtdebugTextLine2Empty()
        {
            if (this._EqtdebugTextLine2 == "SPACES") return true;
            return false;
        }
        
        
        // [DEBUG] Field: EqtdebugTextLine3, is_external=, is_static_class=False, static_prefix=
        private string _EqtdebugTextLine3 ="";
        
        
        // 88-level condition checks for EqtdebugTextLine3
        public bool IsEqtdebugTextLine3Empty()
        {
            if (this._EqtdebugTextLine3 == "SPACES") return true;
            return false;
        }
        
        
    public EqtdebugText() {}
    
    public EqtdebugText(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetEqtdebugTextLine1(data.Substring(offset, 80).Trim());
        offset += 80;
        SetEqtdebugTextLine2(data.Substring(offset, 80).Trim());
        offset += 80;
        SetEqtdebugTextLine3(data.Substring(offset, 80).Trim());
        offset += 80;
        
    }
    
    // Serialization methods
    public string GetEqtdebugTextAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_EqtdebugTextLine1.PadRight(80));
        result.Append(_EqtdebugTextLine2.PadRight(80));
        result.Append(_EqtdebugTextLine3.PadRight(80));
        
        return result.ToString();
    }
    
    public void SetEqtdebugTextAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 80 <= data.Length)
        {
            string extracted = data.Substring(offset, 80).Trim();
            SetEqtdebugTextLine1(extracted);
        }
        offset += 80;
        if (offset + 80 <= data.Length)
        {
            string extracted = data.Substring(offset, 80).Trim();
            SetEqtdebugTextLine2(extracted);
        }
        offset += 80;
        if (offset + 80 <= data.Length)
        {
            string extracted = data.Substring(offset, 80).Trim();
            SetEqtdebugTextLine3(extracted);
        }
        offset += 80;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetEqtdebugTextLine1()
    {
        return _EqtdebugTextLine1;
    }
    
    // Standard Setter
    public void SetEqtdebugTextLine1(string value)
    {
        _EqtdebugTextLine1 = value;
    }
    
    // Get<>AsString()
    public string GetEqtdebugTextLine1AsString()
    {
        return _EqtdebugTextLine1.PadRight(80);
    }
    
    // Set<>AsString()
    public void SetEqtdebugTextLine1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EqtdebugTextLine1 = value;
    }
    
    // Standard Getter
    public string GetEqtdebugTextLine2()
    {
        return _EqtdebugTextLine2;
    }
    
    // Standard Setter
    public void SetEqtdebugTextLine2(string value)
    {
        _EqtdebugTextLine2 = value;
    }
    
    // Get<>AsString()
    public string GetEqtdebugTextLine2AsString()
    {
        return _EqtdebugTextLine2.PadRight(80);
    }
    
    // Set<>AsString()
    public void SetEqtdebugTextLine2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EqtdebugTextLine2 = value;
    }
    
    // Standard Getter
    public string GetEqtdebugTextLine3()
    {
        return _EqtdebugTextLine3;
    }
    
    // Standard Setter
    public void SetEqtdebugTextLine3(string value)
    {
        _EqtdebugTextLine3 = value;
    }
    
    // Get<>AsString()
    public string GetEqtdebugTextLine3AsString()
    {
        return _EqtdebugTextLine3.PadRight(80);
    }
    
    // Set<>AsString()
    public void SetEqtdebugTextLine3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EqtdebugTextLine3 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}