﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using Legacy4.Equity.CobMod.Models;

namespace Legacy4.Equity.CobMod.Facade
{
    /// <summary>
    /// C# implementation of the EquityRouter COBOL program
    /// </summary>
    public class EquityRouter
    {
        // External DLL imports for COBOL programs
        [DllImport("ACGT1_LOGON.dll", CharSet = CharSet.Ansi)]
        private static extern void ACGT1_LOGON(string cgt1WinLinkage, string equityParameters);

        [DllImport("BCGT1_LOGOFF.dll", CharSet = CharSet.Ansi)]
        private static extern void BCGT1_LOGOFF(string cgt1WinLinkage, string equityParameters);

        [DllImport("EQTFUND.dll", CharSet = CharSet.Ansi)]
        private static extern void EQTFUND(string equityParameters);

        [DllImport("EQTCTRY.dll", CharSet = CharSet.Ansi)]
        private static extern void EQTCTRY(string equityParameters);

        [DllImport("EQTGROUP.dll", CharSet = CharSet.Ansi)]
        private static extern void EQTGROUP(string equityParameters);

        [DllImport("EQTRPI.dll", CharSet = CharSet.Ansi)]
        private static extern void EQTRPI(string equityParameters);

        [DllImport("EQTSTOCK.dll", CharSet = CharSet.Ansi)]
        private static extern void EQTSTOCK(string equityParameters);

        [DllImport("EQTPRICE.dll", CharSet = CharSet.Ansi)]
        private static extern void EQTPRICE(string equityParameters);

        [DllImport("EQTBAL.dll", CharSet = CharSet.Ansi)]
        private static extern void EQTBAL(string equityParameters);

        [DllImport("EQTTRANS.dll", CharSet = CharSet.Ansi)]
        private static extern void EQTTRANS(string equityParameters);

        [DllImport("EQTCALC.dll", CharSet = CharSet.Ansi)]
        private static extern void EQTCALC(string equityParameters);

        [DllImport("EQTSEDOL.dll", CharSet = CharSet.Ansi)]
        private static extern void EQTSEDOL(string equityParameters);

        [DllImport("EQTREP.dll", CharSet = CharSet.Ansi)]
        private static extern void EQTREP(string equityParameters);

        [DllImport("EQTYE.dll", CharSet = CharSet.Ansi)]
        private static extern void EQTYE(string equityParameters);

        // Class-level variables to represent the COBOL working-storage section
        private CGT1WinLinkage cgt1WinLinkage = new CGT1WinLinkage();
        private EquityParameters equityParameters = new EquityParameters();
        private EquityGlobalParms equityGlobalParms = new EquityGlobalParms();
        private TimingLinkage timingLinkage = new TimingLinkage();

        /// <summary>
        /// Entry point for the EquityRouter program
        /// </summary>
        /// <param name="equityRouterLinkage">The linkage parameters</param>
        public void Execute(EquityRouterLinkage equityRouterLinkage)
        {
            if (equityRouterLinkage == null)
                throw new ArgumentNullException(nameof(equityRouterLinkage));

            // Set up external linkage fields
            equityGlobalParms.GeneralParms.UserNo = equityRouterLinkage.UserNo;
            equityGlobalParms.GeneralParms.LogSwitch = equityRouterLinkage.LogSwitch;
            equityGlobalParms.Paths.UserDataPath = equityRouterLinkage.UserDataPath;
            equityGlobalParms.GeneralParms.PLOnDistributions = equityRouterLinkage.PLDistributions;
            equityGlobalParms.GeneralParms.FullPricingExport = equityRouterLinkage.FullPricingExport;

            // Evaluate the action to perform
            switch (equityRouterLinkage.Action.Trim())
            {
                case "ACGT1_LOGON":
                    Console.WriteLine("CALL 'ACGT1_LOGON");
                    FormatCGT1WinLinkage(equityRouterLinkage);
                    FormatEquityParameters(equityRouterLinkage);

                    ACGT1_LOGON(cgt1WinLinkage.ToString(), equityParameters.ToString());

                    equityRouterLinkage.UserNo = cgt1WinLinkage.UserNo;
                    equityRouterLinkage.Year = equityParameters.GeneralParms.Year;
                    break;

                case "BCGT1_LOGOFF":
                    Console.WriteLine("CALL 'BCGT1_LOGOFF");
                    FormatCGT1WinLinkage(equityRouterLinkage);

                    BCGT1_LOGOFF(cgt1WinLinkage.ToString(), equityParameters.ToString());

                    equityRouterLinkage.UserNo = string.Empty.PadRight(4);
                    equityRouterLinkage.Year = string.Empty.PadRight(2);
                    break;

                case "AEQUITY2_BATCH_PROCESS":
                    Console.WriteLine("AEQUITY2_BATCH_PROCESS");
                    FormatEquityParameters(equityRouterLinkage);

                    // Evaluate the batch action
                    switch (equityParameters.BatchParameters.Action)
                    {
                        case EquityParameters.LOAD_FUNDS:
                            EQTFUND(equityParameters.ToString());
                            break;
                        case EquityParameters.LOAD_COUNTRIES:
                            EQTCTRY(equityParameters.ToString());
                            break;
                        case EquityParameters.LOAD_GROUPS:
                            EQTGROUP(equityParameters.ToString());
                            break;
                        case EquityParameters.LOAD_RPI:
                            EQTRPI(equityParameters.ToString());
                            break;
                        case EquityParameters.LOAD_STOCKS:
                            EQTSTOCK(equityParameters.ToString());
                            break;
                        case EquityParameters.LOAD_PRICES:
                            EQTPRICE(equityParameters.ToString());
                            break;
                        case EquityParameters.LOAD_BALANCES:
                            EQTBAL(equityParameters.ToString());
                            break;
                        case EquityParameters.LOAD_TRANSACTIONS:
                            EQTTRANS(equityParameters.ToString());
                            break;
                        case EquityParameters.FULL_CALCULATION:
                        case EquityParameters.PARTIAL_CALCULATION:
                            EQTCALC(equityParameters.ToString());
                            break;
                        case EquityParameters.SEDOL_WHATIF_CALCULATION:
                        case EquityParameters.XFUND_WHATIF_CALCULATION:
                        case EquityParameters.ON_LINE_CALCULATION:
                            EQTSEDOL(equityParameters.ToString());
                            if (equityParameters.BatchParameters.Action == EquityParameters.XFUND_WHATIF_CALCULATION)
                            {
                                FormatReturnParameters(equityRouterLinkage);
                            }
                            break;
                        case EquityParameters.CALCULATION_ERROR:
                        case EquityParameters.FORMAT_SCHEDULE:
                        case EquityParameters.TAPERED_GAINS_SCHD:
                        case EquityParameters.U_PERCENTAGE_GAIN_LOSS:
                        case EquityParameters.U_EXPORT_FILE:
                        case EquityParameters.U_FULL_EXTRACT:
                        case EquityParameters.TRANCHE_EXPORT:
                        case EquityParameters.GAIN_LOSS_SUMMARY:
                        case EquityParameters.CH_GAIN_LOSS_SUMMARY:
                        case EquityParameters.SCHEDULE_D_BF_GAINS:
                        case EquityParameters.GAIN_LOSS_EXTRACT:
                        case EquityParameters.REALISED_REPORT_CG01:
                        case EquityParameters.CONFIGURABLE_REALISED_REPORT:
                        case EquityParameters.GAINS_LOSSES_REALISED_REPORT:
                        case EquityParameters.UNREALISED_REPORT_CG02:
                        case EquityParameters.FULL_UNREALISED_REPORT:
                        case EquityParameters.HOLDINGS_ANALYSIS:
                        case EquityParameters.INDEXATION_ANALYSIS:
                        case EquityParameters.TRANSACTION_SUMMARY:
                        case EquityParameters.GAINS_TODAY_EXPORT:
                        case EquityParameters.HOLDINGS_EXPORT:
                        case EquityParameters.SF_GAINLOSS_EXPORT:
                        case EquityParameters.TSB_GAINLOSS_EXPORT:
                        case EquityParameters.ACQUISITION_EXPORT:
                        case EquityParameters.PRICING_EXPORT:
                        case EquityParameters.MASTERFILE_EXPORTER:
                        case EquityParameters.TRANSACTION_HISTORY_EXPORTER:
                        case EquityParameters.ADMIN_EXPORTER:
                        case EquityParameters.FULL_HOLDINGS_EXPORT:
                        case EquityParameters.RESULTS_DB:
                        case EquityParameters.INTERCONNECTED_FUNDS:
                        case EquityParameters.DAILY_TRANSACTION_EXPORT:
                        case EquityParameters.GAIN_LOSS_SUMMARY_LR:
                            EQTREP(equityParameters.ToString());
                            break;
                        case EquityParameters.YEAR_END:
                            EQTYE(equityParameters.ToString());
                            break;
                    }
                    break;

                case "ACGT0112_RESET_RCF":
                    Console.WriteLine("CALL 'ACGT0112_RESET_RCF'");
                    // Commented out in original code, not implemented
                    break;

                case "ACGT0603_GRANT_ACCESS":
                    Console.WriteLine("CALL 'ACGT0603_GRANT_ACCESS");
                    // Commented out in original code, not implemented
                    break;

                case "BCGT0603_REVOKE_ACCESS":
                    Console.WriteLine("CALL 'BCGT0603_REVOKE_ACCESS");
                    // Commented out in original code, not implemented
                    break;

                case "CCGT0603_CHECK_DEFAULT_ACCESS":
                    Console.WriteLine("CALL 'CCGT0603_CHECK_DEFAULT_ACCESS");
                    // Commented out in original code, not implemented
                    break;

                case "ACGTAXESS_CHECK":
                    Console.WriteLine("CALL 'ACGTAXESS_CHECK");
                    // Commented out in original code, not implemented
                    break;

                case "ACGTPASS_ENCRYPT_DECRYPT_PW":
                    Console.WriteLine("CALL 'ACGTPASS_ENCRYPT_DECRYPT_PW");
                    // Commented out in original code, not implemented
                    break;

                case "AEQFNDDEL_DELETE_USER_FUNDS":
                    Console.WriteLine("CALL 'AEQFNDDEL_DELETE_USER_FUNDS");
                    // Commented out in original code, not implemented
                    break;

                case "AEQFUNDCP_COPY_ALL_FUNDS":
                    Console.WriteLine("CALL 'AEQFUNDCP_COPY_ALL_FUNDS");
                    // Commented out in original code, not implemented
                    break;

                case "BEQFUNDCP_COPY_SINGLE_FUND":
                    Console.WriteLine("BEQFUNDCP_COPY_SINGLE_FUND");
                    // Commented out in original code, not implemented
                    break;

                case "AEQRESETP_RESET_PRICE":
                    Console.WriteLine("AEQRESETP_RESET_PRICE");
                    // Commented out in original code, not implemented
                    break;

                case "AEQUFUND_USER_FUNDS_UPDATE":
                    Console.WriteLine("AEQUFUND_USER_FUNDS_UPDATE");
                    // Commented out in original code, not implemented
                    break;

                case "AEQUITY_ENTRY":
                    Console.WriteLine("AEQUITY_ENTRY");
                    // Commented out in original code, not implemented
                    break;

                case "BEQUITY2_GENERAL_FILE_HANDLER":
                    Console.WriteLine("BEQUITY2_GENERAL_FILE_HANDLER");
                    // Commented out in original code, not implemented
                    break;

                case "CEQUITY2_MASTER_FILE_HANDLER":
                    Console.WriteLine("CEQUITY2_MASTER_FILE_HANDLER");
                    // Commented out in original code, not implemented
                    break;

                case "DEQUITY2_GET_PREVIOUS_MAST_KEY":
                    Console.WriteLine("DEQUITY2_GET_PREVIOUS_MAST_KEY");
                    // Commented out in original code, not implemented
                    break;

                case "AEQUITY3_CGTHEX_CONVERT_FORMAT":
                    Console.WriteLine("AEQUITY3_CGTHEX_CONVERT_FORMAT");
                    // Commented out in original code, not implemented
                    break;

                case "BEQUITY3_INITIALISE_HEADER":
                    Console.WriteLine("BEQUITY3_INITIALISE_HEADER");
                    // Commented out in original code, not implemented
                    break;

                case "CEQUITY3_INITIALISE_BAL_ACQN":
                    Console.WriteLine("CEQUITY3_INITIALISE_BAL_ACQN");
                    // Commented out in original code, not implemented
                    break;

                case "DEQUITY3_INITIALISE_DISPOSAL":
                    Console.WriteLine("DEQUITY3_INITIALISE_DISPOSAL");
                    // Commented out in original code, not implemented
                    break;

                case "EEQUITY4_CANCEL_ALL_PROGRAMS":
                    Console.WriteLine("EEQUITY4_CANCEL_ALL_PROGRAMS");
                    // Commented out in original code, not implemented
                    break;

                case "AEQUITYB_ENTRY":
                    Console.WriteLine("AEQUITYB_ENTRY");
                    // Commented out in original code, not implemented
                    break;
            }

            // Copy the return code back to the linkage
            equityRouterLinkage.ReturnCode = equityParameters.GeneralParms.ReturnCodeX;
        }

        /// <summary>
        /// Formats the CGT1-WIN-LINKAGE from EquityRouterLinkage
        /// </summary>
        private void FormatCGT1WinLinkage(EquityRouterLinkage equityRouterLinkage)
        {
            cgt1WinLinkage.UserId = equityRouterLinkage.UserID;
            cgt1WinLinkage.Password = equityRouterLinkage.Password;
            cgt1WinLinkage.UserPath = equityRouterLinkage.UserDataPath;
        }

        /// <summary>
        /// Formats the EQUITY-PARAMETERS from EquityRouterLinkage
        /// </summary>
        private void FormatEquityParameters(EquityRouterLinkage equityRouterLinkage)
        {
            equityParameters.GeneralParms.UserNo = equityRouterLinkage.UserNo;
            equityParameters.GeneralParms.Year = equityRouterLinkage.Year;
            equityParameters.BatchParameters.Action = int.Parse(equityRouterLinkage.Action);
            equityParameters.BatchParameters.GenerationNo = equityRouterLinkage.GenerationNo;
            equityParameters.BatchParameters.Action = int.Parse(equityRouterLinkage.ReportAction);
            equityParameters.BatchParameters.ScheduleType = equityRouterLinkage.ScheduleType;
            equityParameters.BatchParameters.AnalysisSchedule = equityRouterLinkage.AnalysisSchedule;
            equityParameters.BatchParameters.RealisedVersion = equityRouterLinkage.RealisedVersion;
            equityParameters.BatchParameters.ShowSedolNumbers = equityRouterLinkage.SedolNumbers;
            equityParameters.BatchParameters.ShowSedolNumbersLR = equityRouterLinkage.SedolNumbersLR;
            equityParameters.BatchParameters.CGTSCOT1Format = equityRouterLinkage.ScanxFormat;
            equityParameters.CalcParameters.Fund = equityRouterLinkage.CalcFundCode;
            equityParameters.CalcParameters.Sedol = equityRouterLinkage.CalcSedolCode;
            equityParameters.CalcParameters.DisposalDate = equityRouterLinkage.CalcDisposalDate;

            if (decimal.TryParse(equityRouterLinkage.CalcMarketPrice, out decimal marketPrice))
            {
                equityParameters.CalcParameters.MarketPrice = marketPrice;
            }
            else
            {
                equityParameters.CalcParameters.MarketPrice = 0m;
            }

            if (decimal.TryParse(equityRouterLinkage.CalcParentPrice, out decimal parentPrice))
            {
                equityParameters.CalcParameters.ParentPrice = parentPrice;
            }
            else
            {
                equityParameters.CalcParameters.ParentPrice = 0m;
            }

            if (decimal.TryParse(equityRouterLinkage.CalcUnitsSold, out decimal unitsSold))
            {
                equityParameters.CalcParameters.UnitsDisposed = unitsSold;
                equityParameters.CalcParameters.UnitsDisposedX = unitsSold.ToString("0.00");
            }
            else
            {
                equityParameters.CalcParameters.UnitsDisposed = 0m;
                equityParameters.CalcParameters.UnitsDisposedX = "0.00";
            }

            if (decimal.TryParse(equityRouterLinkage.CalcProfit, out decimal profit))
            {
                equityParameters.CalcParameters.Profit = profit;
            }
            else
            {
                equityParameters.CalcParameters.Profit = 0m;
            }

            if (decimal.TryParse(equityRouterLinkage.CalcGain, out decimal gain))
            {
                equityParameters.CalcParameters.Gain = gain;
            }
            else
            {
                equityParameters.CalcParameters.Gain = 0m;
            }

            equityParameters.BatchParameters.SegregateAssets = equityRouterLinkage.SegregateAssets;
            equityParameters.BatchParameters.CGTSCOT1Format = equityRouterLinkage.CGTSCOT1Format;
            equityParameters.GeneralParms.UnrealisedGainsFlag = equityRouterLinkage.UnrealisedGains;
            equityParameters.BatchParameters.Derivatives = equityRouterLinkage.Derivatives;
            equityParameters.BatchParameters.IrishCGT = equityRouterLinkage.IrishCGT;
            equityParameters.BatchParameters.IrishCGTProRata = equityRouterLinkage.IrishCGTProRata;
        }

        /// <summary>
        /// Formats the return parameters from EQUITY-PARAMETERS to EquityRouterLinkage
        /// </summary>
        private void FormatReturnParameters(EquityRouterLinkage equityRouterLinkage)
        {
            // Copy values back to the linkage
            equityRouterLinkage.CalcUnitsSold9 = equityParameters.CalcParameters.UnitsDisposed;

            // Format with leading sign
            decimal profit = equityParameters.CalcParameters.Profit;
            equityRouterLinkage.CalcProfit9 = profit;

            decimal gain = equityParameters.CalcParameters.Gain;
            equityRouterLinkage.CalcGain9 = gain;

            // Update string representations
            equityRouterLinkage.UpdateStringFields();
        }
    }
}