﻿//// Cgttemp.cs
//using System;
//using EquityProject.CgttempDTO;
//using Equity.CobMod.Utils;
//using EquityProject.EqtpathPGM;
//using EquityProject.CgtdelPGM;
//using WK.UK.CCH.Equity.LegacyPackage;
//// using System.IO; // Keep if needed for other things, but file handler abstracts it

//namespace EquityProject.CgttempPGM
//{
//    // Cgttemp Class Definition

//    //Cgttemp Class Constructor
//    public class Cgttemp
//    {
//        // Declare Cgttemp Class private variables
//        private Fvar _fvar = new Fvar();
//        private Gvar _gvar = new Gvar();
//        private Ivar _ivar = new Ivar();

//        // Handler for the temporary indexed file
//        private ITempFileHandler _tempFileHandler;

//        // Constructor
//        public Cgttemp()
//        {
//            // Initialize the actual file handler.
//            _tempFileHandler = new SimpleIndexedFileHandler();
//        }

//        // Declare Cgttemp Class getters setters
//        public Fvar GetFvar() { return _fvar; }
//        public Gvar GetGvar() { return _gvar; }
//        public Ivar GetIvar() { return _ivar; }



//        // Run() method
//        // Corresponds to PROCEDURE DIVISION USING CGTTEMP-LINKAGE
//        public void Run(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Implement main program logic here
//            // Corresponds to MAINLINE SECTION.

//            // COBOL: EVALUATE CGTTEMP-ACTION
//            switch (ivar.GetCgttempLinkage().GetCgttempAction())
//            {
//                // COBOL: WHEN CGTTEMP-OPEN-OUTPUT
//                case Ivar.CGTTEMP_OPEN_OUTPUT:
//                    // COBOL: MOVE CGTTEMP-USER-NO TO REPORT-USER-NO
//                    gvar.GetTempFile().SetReportUserNo(ivar.GetCgttempLinkage().GetCgttempUserNoAsString());
//                    // COBOL: MOVE CGTTEMP-REPORT-NUMBER TO REPORT-GEN-NO
//                    gvar.GetTempFile().SetReportGenNo(ivar.GetCgttempLinkage().GetCgttempReportNumberAsString());

//                    // COBOL: MOVE USER-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
//                    gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);
//                    // COBOL: MOVE TEMP-FILE TO EQTPATH-FILE-NAME
//                    gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetTempFile().GetTempFileAsString());
//                    // COBOL: PERFORM X-CALL-EQTPATH
//                    Xcalleqtpath(fvar, gvar, ivar);
//                    // COBOL: MOVE EQTPATH-PATH-FILE-NAME TO TEMP-FILE-NAME
//                    gvar.SetTempFileName(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

//                    // COBOL: OPEN OUTPUT TEMPORARY-FILE
//                    // Use the file handler
//                    gvar.SetWFileReturnCode(_tempFileHandler.OpenOutput(gvar.GetTempFileName()));
//                    break;

//                // COBOL: WHEN CGTTEMP-WRITE
//                case Ivar.CGTTEMP_WRITE:
//                    // COBOL: WRITE TEMP-RECORD FROM CGTTEMP-RECORD
//                    // Move data from linkage to the file record structure
//                    fvar.SetTempRecordAsString(ivar.GetCgttempLinkage().GetCgttempRecordAsString());
//                    // Use the file handler
//                    gvar.SetWFileReturnCode(_tempFileHandler.Write(fvar.GetTempRecord()));
//                    break;

//                // COBOL: WHEN CGTTEMP-CLOSE
//                case Ivar.CGTTEMP_CLOSE:
//                    // COBOL: CLOSE TEMPORARY-FILE
//                    // Use the file handler
//                    gvar.SetWFileReturnCode(_tempFileHandler.Close());
//                    break;

//                // COBOL: WHEN CGTTEMP-OPEN-IO
//                case Ivar.CGTTEMP_OPEN_IO:
//                    // COBOL: MOVE CGTTEMP-USER-NO TO REPORT-USER-NO
//                    gvar.GetTempFile().SetReportUserNo(ivar.GetCgttempLinkage().GetCgttempUserNoAsString());
//                    // COBOL: MOVE CGTTEMP-REPORT-NUMBER TO REPORT-GEN-NO
//                    gvar.GetTempFile().SetReportGenNo(ivar.GetCgttempLinkage().GetCgttempReportNumberAsString());

//                    // COBOL: MOVE USER-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
//                    gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);
//                    // COBOL: MOVE TEMP-FILE TO EQTPATH-FILE-NAME
//                    gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetTempFile().GetTempFileAsString());
//                    // COBOL: PERFORM X-CALL-EQTPATH
//                    Xcalleqtpath(fvar, gvar, ivar);
//                    // COBOL: MOVE EQTPATH-PATH-FILE-NAME TO TEMP-FILE-NAME
//                    gvar.SetTempFileName(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

//                    // COBOL: OPEN I-O TEMPORARY-FILE
//                    // Use the file handler
//                    gvar.SetWFileReturnCode(_tempFileHandler.OpenIO(gvar.GetTempFileName()));
//                    break;

//                // COBOL: WHEN CGTTEMP-READ
//                case Ivar.CGTTEMP_READ:
//                    // COBOL: READ TEMPORARY-FILE NEXT INTO CGTTEMP-RECORD
//                    TempRecord readRecord;
//                    // Use the file handler
//                    gvar.SetWFileReturnCode(_tempFileHandler.ReadNext(out readRecord));
//                    // If read was successful, move data into linkage record
//                    if (gvar.GetWFileReturnCode() == SimpleIndexedFileHandler.STATUS_SUCCESS)
//                    {
//                        ivar.GetCgttempLinkage().SetCgttempRecordAsString(readRecord.ToString());
//                    }
//                    else
//                    {
//                        // Clear linkage record on error/EOF (common COBOL pattern)
//                        ivar.GetCgttempLinkage().SetCgttempRecord(new CgttempLinkage.CgttempRecord());
//                    }
//                    break;

//                // COBOL: WHEN CGTTEMP-DELETE-FILE
//                case Ivar.CGTTEMP_DELETE_FILE:
//                    // COBOL: MOVE CGTTEMP-USER-NO TO REPORT-USER-NO
//                    gvar.GetTempFile().SetReportUserNo(ivar.GetCgttempLinkage().GetCgttempUserNoAsString());
//                    // COBOL: MOVE CGTTEMP-REPORT-NUMBER TO REPORT-GEN-NO
//                    gvar.GetTempFile().SetReportGenNo(ivar.GetCgttempLinkage().GetCgttempReportNumberAsString());

//                    // COBOL: MOVE USER-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
//                    gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);
//                    // COBOL: MOVE TEMP-FILE TO EQTPATH-FILE-NAME
//                    gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetTempFile().GetTempFileAsString());
//                    // COBOL: PERFORM X-CALL-EQTPATH
//                    Xcalleqtpath(fvar, gvar, ivar);
//                    // COBOL: MOVE EQTPATH-PATH-FILE-NAME TO L-DEL-FILE
//                    gvar.GetCgtdelLinkage().SetLDelFile(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());
//                    // COBOL: PERFORM X1-CALL-CGTDEL
//                    X1callcgtdel(fvar, gvar, ivar);

//                    // COBOL: MOVE USER-DATA-PATH TO EQTPATH-PATH-ENV-VARIABLE
//                    gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);
//                    // COBOL: MOVE '.IDX' TO TEMP-FILE(9:4)
//                    // This modifies the filler4 field in the TempFile DTO
//                    gvar.GetTempFile().SetFiller4(".IDX");
//                    // COBOL: MOVE TEMP-FILE TO EQTPATH-FILE-NAME
//                    gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetTempFile().GetTempFileAsString());
//                    // COBOL: PERFORM X-CALL-EQTPATH
//                    Xcalleqtpath(fvar, gvar, ivar);
//                    // COBOL: MOVE EQTPATH-PATH-FILE-NAME TO L-DEL-FILE
//                    gvar.GetCgtdelLinkage().SetLDelFile(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());
//                    // COBOL: PERFORM X1-CALL-CGTDEL
//                    X1callcgtdel(fvar, gvar, ivar);
//                    // COBOL: MOVE '.DAT' TO TEMP-FILE(9:4)
//                    // Restore the original extension
//                    gvar.GetTempFile().SetFiller4(".DAT");

//                    // COBOL: MOVE L-DEL-RESULT TO W-FILE-RETURN-CODE
//                    gvar.SetWFileReturnCode(gvar.GetCgtdelLinkage().GetLDelResultAsString());
//                    break;

//                // COBOL: WHEN CGTTEMP-REWRITE
//                case Ivar.CGTTEMP_REWRITE:
//                    // COBOL: REWRITE TEMP-RECORD FROM CGTTEMP-RECORD
//                    // Move data from linkage to the file record structure
//                    fvar.SetTempRecordAsString(ivar.GetCgttempLinkage().GetCgttempRecordAsString());
//                    // Use the file handler
//                    gvar.SetWFileReturnCode(_tempFileHandler.Rewrite(fvar.GetTempRecord()));
//                    break;

//                // COBOL: WHEN CGTTEMP-START-NOT-LESS-THAN
//                case Ivar.CGTTEMP_START_NOT_LESS_THAN:
//                    // COBOL: MOVE CGTTEMP-RECORD TO TEMP-RECORD
//                    // Move key/record data from linkage to the file record structure
//                    fvar.SetTempRecordAsString(ivar.GetCgttempLinkage().GetCgttempRecordAsString());
//                    // Use the file handler with the key from the file record structure
//                    gvar.SetWFileReturnCode(_tempFileHandler.Start(fvar.GetTempRecord().GetTempKey(), StartMode.NotLessThan));
//                    break;

//                // COBOL: WHEN CGTTEMP-READ-ON-KEY
//                case Ivar.CGTTEMP_READ_ON_KEY:
//                    // COBOL: MOVE CGTTEMP-RECORD TO TEMP-RECORD
//                    // Move key/record data from linkage to the file record structure
//                    fvar.SetTempRecordAsString(ivar.GetCgttempLinkage().GetCgttempRecordAsString());
//                    TempRecord readOnKeyRecord;
//                    // Use the file handler with the key from the file record structure
//                    gvar.SetWFileReturnCode(_tempFileHandler.ReadOnKey(fvar.GetTempRecord().GetTempKey(), out readOnKeyRecord));
//                    // If read was successful, move data into linkage record
//                    if (gvar.GetWFileReturnCode() == SimpleIndexedFileHandler.STATUS_SUCCESS)
//                    {
//                        ivar.GetCgttempLinkage().SetCgttempRecordAsString(readOnKeyRecord.ToString());
//                    }
//                    else
//                    {
//                        // Clear linkage record on error/not found (common COBOL pattern)
//                        ivar.GetCgttempLinkage().SetCgttempRecord(new CgttempLinkage.CgttempRecord());
//                    }
//                    break;

//                default:
//                    // Handle unknown action if necessary
//                    Console.WriteLine($"[CGTTEMP] Unknown action received: {ivar.GetCgttempLinkage().GetCgttempAction()}");
//                    gvar.SetWFileReturnCode(SimpleIndexedFileHandler.STATUS_LOGIC_ERROR); // Indicate error
//                    break;
//            }
//            // COBOL: END-EVALUATE.

//            // COBOL: MOVE W-FILE-RETURN-CODE TO CGTTEMP-STATUS.
//            ivar.GetCgttempLinkage().SetCgttempStatus(gvar.GetWFileReturnCode());

//            // COBOL: EXIT PROGRAM.
//            // In C#, this is implicit when the method returns.
//        }

//        // CallSub() method
//        // This method exists in the template but is not called by the provided COBOL PROCEDURE DIVISION.
//        // Keeping it as per the template structure.
//        public void CallSub(Ivar ivar)
//        {
//            // Implement subroutine call logic here
//            Console.WriteLine("[CGTTEMP] CallSub method called (not used by COBOL logic).");
//        }

//        /// <summary>
//        /// Method equivalent to COBOL paragraph x1CallCgtdel.
//        /// </summary>
//        /// <remarks>
//        /// This method performs the CALL 'CGTDEL' USING CGTDEL-LINKAGE, by creating an 
//        /// instance of the called program and invoking its Run method with the specified 
//        /// linkage parameter from the global variable context.
//        /// </remarks>
//        public void X1callcgtdel(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Create a new instance of the external program CGTDEL.
//            Cgtdel cgtdel = new Cgtdel();
//            // Call the Run method with the CGTDEL-LINKAGE parameter.
//            cgtdel.GetIvar().SetCgtdelLinkageAsString(gvar.GetCgtdelLinkage().GetCgtdelLinkageAsString());  
//            cgtdel.CallSub(cgtdel.GetIvar());
//        }
//        /// <summary>
//        /// xCallEqtpath
//        /// </summary>
//        /// <remarks>
//        /// Converts a COBOL CALL statement to C# with equivalent functionality.
//        /// </remarks>
//        public void Xcalleqtpath(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Create a new instance of the EQTPATH external program
//            Eqtpath eqtpath = new Eqtpath();
//            eqtpath.GetIvar().SetEqtpathLinkageAsString(gvar.GetEqtpathLinkage().GetEqtpathFileNameAsString());
//            // Call the Run method with the EQTPATH-LINKAGE parameter as specified in the COBOL USING clause
//            eqtpath.CallSub(eqtpath.GetIvar());
//        }
//    }
//}