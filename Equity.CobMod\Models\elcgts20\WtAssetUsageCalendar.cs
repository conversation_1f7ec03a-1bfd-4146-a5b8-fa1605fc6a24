using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtAssetUsageCalendar Data Structure

public class WtAssetUsageCalendar
{
    private static int _size = 58;
    // [DEBUG] Class: WtAssetUsageCalendar, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler476, is_external=, is_static_class=False, static_prefix=
    private string _Filler476 ="ASSET-USAGE-CALENDAR============";
    
    
    
    
    // [DEBUG] Field: WtacMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtacMaxTableSize =100;
    
    
    
    
    // [DEBUG] Field: WtacOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtacOccurs =0;
    
    
    
    
    // [DEBUG] Field: WtacFundSedol, is_external=, is_static_class=False, static_prefix=
    private string _WtacFundSedol ="";
    
    
    
    
    // [DEBUG] Field: WtacAssetUseCalendarTable, is_external=, is_static_class=False, static_prefix=
    private WtacAssetUseCalendarTable _WtacAssetUseCalendarTable = new WtacAssetUseCalendarTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtAssetUsageCalendarAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler476.PadRight(32));
        result.Append(_WtacMaxTableSize.ToString().PadLeft(3, '0'));
        result.Append(_WtacOccurs.ToString().PadLeft(3, '0'));
        result.Append(_WtacFundSedol.PadRight(11));
        result.Append(_WtacAssetUseCalendarTable.GetWtacAssetUseCalendarTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtAssetUsageCalendarAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 32 <= data.Length)
        {
            string extracted = data.Substring(offset, 32).Trim();
            SetFiller476(extracted);
        }
        offset += 32;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtacMaxTableSize(parsedInt);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtacOccurs(parsedInt);
        }
        offset += 3;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetWtacFundSedol(extracted);
        }
        offset += 11;
        if (offset + 9 <= data.Length)
        {
            _WtacAssetUseCalendarTable.SetWtacAssetUseCalendarTableAsString(data.Substring(offset, 9));
        }
        else
        {
            _WtacAssetUseCalendarTable.SetWtacAssetUseCalendarTableAsString(data.Substring(offset));
        }
        offset += 9;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtAssetUsageCalendarAsString();
    }
    // Set<>String Override function
    public void SetWtAssetUsageCalendar(string value)
    {
        SetWtAssetUsageCalendarAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller476()
    {
        return _Filler476;
    }
    
    // Standard Setter
    public void SetFiller476(string value)
    {
        _Filler476 = value;
    }
    
    // Get<>AsString()
    public string GetFiller476AsString()
    {
        return _Filler476.PadRight(32);
    }
    
    // Set<>AsString()
    public void SetFiller476AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler476 = value;
    }
    
    // Standard Getter
    public int GetWtacMaxTableSize()
    {
        return _WtacMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtacMaxTableSize(int value)
    {
        _WtacMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtacMaxTableSizeAsString()
    {
        return _WtacMaxTableSize.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetWtacMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtacMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtacOccurs()
    {
        return _WtacOccurs;
    }
    
    // Standard Setter
    public void SetWtacOccurs(int value)
    {
        _WtacOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtacOccursAsString()
    {
        return _WtacOccurs.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetWtacOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtacOccurs = parsed;
    }
    
    // Standard Getter
    public string GetWtacFundSedol()
    {
        return _WtacFundSedol;
    }
    
    // Standard Setter
    public void SetWtacFundSedol(string value)
    {
        _WtacFundSedol = value;
    }
    
    // Get<>AsString()
    public string GetWtacFundSedolAsString()
    {
        return _WtacFundSedol.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetWtacFundSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtacFundSedol = value;
    }
    
    // Standard Getter
    public WtacAssetUseCalendarTable GetWtacAssetUseCalendarTable()
    {
        return _WtacAssetUseCalendarTable;
    }
    
    // Standard Setter
    public void SetWtacAssetUseCalendarTable(WtacAssetUseCalendarTable value)
    {
        _WtacAssetUseCalendarTable = value;
    }
    
    // Get<>AsString()
    public string GetWtacAssetUseCalendarTableAsString()
    {
        return _WtacAssetUseCalendarTable != null ? _WtacAssetUseCalendarTable.GetWtacAssetUseCalendarTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtacAssetUseCalendarTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtacAssetUseCalendarTable == null)
        {
            _WtacAssetUseCalendarTable = new WtacAssetUseCalendarTable();
        }
        _WtacAssetUseCalendarTable.SetWtacAssetUseCalendarTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtacAssetUseCalendarTable(string value)
    {
        _WtacAssetUseCalendarTable.SetWtacAssetUseCalendarTableAsString(value);
    }
    // Nested Class: WtacAssetUseCalendarTable
    public class WtacAssetUseCalendarTable
    {
        private static int _size = 9;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtacElement, is_external=, is_static_class=False, static_prefix=
        private WtacAssetUseCalendarTable.WtacElement[] _WtacElement = new WtacAssetUseCalendarTable.WtacElement[100];
        
        public void InitializeWtacElementArray()
        {
            for (int i = 0; i < 100; i++)
            {
                _WtacElement[i] = new WtacAssetUseCalendarTable.WtacElement();
            }
        }
        
        
        
    public WtacAssetUseCalendarTable() {}
    
    public WtacAssetUseCalendarTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWtacElementArray();
        for (int i = 0; i < 100; i++)
        {
            _WtacElement[i].SetWtacElementAsString(data.Substring(offset, 9));
            offset += 9;
        }
        
    }
    
    // Serialization methods
    public string GetWtacAssetUseCalendarTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 100; i++)
        {
            result.Append(_WtacElement[i].GetWtacElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtacAssetUseCalendarTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 100; i++)
        {
            if (offset + 9 > data.Length) break;
            string val = data.Substring(offset, 9);
            
            _WtacElement[i].SetWtacElementAsString(val);
            offset += 9;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtacElement
    public WtacElement GetWtacElementAt(int index)
    {
        return _WtacElement[index];
    }
    
    public void SetWtacElementAt(int index, WtacElement value)
    {
        _WtacElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtacElement GetWtacElement()
    {
        return _WtacElement != null && _WtacElement.Length > 0
        ? _WtacElement[0]
        : new WtacElement();
    }
    
    public void SetWtacElement(WtacElement value)
    {
        if (_WtacElement == null || _WtacElement.Length == 0)
        _WtacElement = new WtacElement[1];
        _WtacElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtacElement
    public class WtacElement
    {
        private static int _size = 9;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtacDate, is_external=, is_static_class=False, static_prefix=
        private WtacElement.WtacDate _WtacDate = new WtacElement.WtacDate();
        
        
        
        
        // [DEBUG] Field: WtacBusinessAsset, is_external=, is_static_class=False, static_prefix=
        private string _WtacBusinessAsset ="";
        
        
        
        
    public WtacElement() {}
    
    public WtacElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WtacDate.SetWtacDateAsString(data.Substring(offset, WtacDate.GetSize()));
        offset += 8;
        SetWtacBusinessAsset(data.Substring(offset, 1).Trim());
        offset += 1;
        
    }
    
    // Serialization methods
    public string GetWtacElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtacDate.GetWtacDateAsString());
        result.Append(_WtacBusinessAsset.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWtacElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            _WtacDate.SetWtacDateAsString(data.Substring(offset, 8));
        }
        else
        {
            _WtacDate.SetWtacDateAsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtacBusinessAsset(extracted);
        }
        offset += 1;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WtacDate GetWtacDate()
    {
        return _WtacDate;
    }
    
    // Standard Setter
    public void SetWtacDate(WtacDate value)
    {
        _WtacDate = value;
    }
    
    // Get<>AsString()
    public string GetWtacDateAsString()
    {
        return _WtacDate != null ? _WtacDate.GetWtacDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtacDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtacDate == null)
        {
            _WtacDate = new WtacDate();
        }
        _WtacDate.SetWtacDateAsString(value);
    }
    
    // Standard Getter
    public string GetWtacBusinessAsset()
    {
        return _WtacBusinessAsset;
    }
    
    // Standard Setter
    public void SetWtacBusinessAsset(string value)
    {
        _WtacBusinessAsset = value;
    }
    
    // Get<>AsString()
    public string GetWtacBusinessAssetAsString()
    {
        return _WtacBusinessAsset.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtacBusinessAssetAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtacBusinessAsset = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtacDate
    public class WtacDate
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtacCc, is_external=, is_static_class=False, static_prefix=
        private int _WtacCc =0;
        
        
        
        
        // [DEBUG] Field: WtacDateYymm, is_external=, is_static_class=False, static_prefix=
        private WtacDate.WtacDateYymm _WtacDateYymm = new WtacDate.WtacDateYymm();
        
        
        
        
    public WtacDate() {}
    
    public WtacDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtacCc(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        _WtacDateYymm.SetWtacDateYymmAsString(data.Substring(offset, WtacDateYymm.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetWtacDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtacCc.ToString().PadLeft(2, '0'));
        result.Append(_WtacDateYymm.GetWtacDateYymmAsString());
        
        return result.ToString();
    }
    
    public void SetWtacDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtacCc(parsedInt);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _WtacDateYymm.SetWtacDateYymmAsString(data.Substring(offset, 6));
        }
        else
        {
            _WtacDateYymm.SetWtacDateYymmAsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWtacCc()
    {
        return _WtacCc;
    }
    
    // Standard Setter
    public void SetWtacCc(int value)
    {
        _WtacCc = value;
    }
    
    // Get<>AsString()
    public string GetWtacCcAsString()
    {
        return _WtacCc.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWtacCcAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtacCc = parsed;
    }
    
    // Standard Getter
    public WtacDateYymm GetWtacDateYymm()
    {
        return _WtacDateYymm;
    }
    
    // Standard Setter
    public void SetWtacDateYymm(WtacDateYymm value)
    {
        _WtacDateYymm = value;
    }
    
    // Get<>AsString()
    public string GetWtacDateYymmAsString()
    {
        return _WtacDateYymm != null ? _WtacDateYymm.GetWtacDateYymmAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtacDateYymmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtacDateYymm == null)
        {
            _WtacDateYymm = new WtacDateYymm();
        }
        _WtacDateYymm.SetWtacDateYymmAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtacDateYymm
    public class WtacDateYymm
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtacYy, is_external=, is_static_class=False, static_prefix=
        private int _WtacYy =0;
        
        
        
        
        // [DEBUG] Field: WtacMm, is_external=, is_static_class=False, static_prefix=
        private int _WtacMm =0;
        
        
        
        
        // [DEBUG] Field: WtacDd, is_external=, is_static_class=False, static_prefix=
        private int _WtacDd =0;
        
        
        
        
    public WtacDateYymm() {}
    
    public WtacDateYymm(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtacYy(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetWtacMm(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetWtacDd(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetWtacDateYymmAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtacYy.ToString().PadLeft(2, '0'));
        result.Append(_WtacMm.ToString().PadLeft(2, '0'));
        result.Append(_WtacDd.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetWtacDateYymmAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtacYy(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtacMm(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtacDd(parsedInt);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWtacYy()
    {
        return _WtacYy;
    }
    
    // Standard Setter
    public void SetWtacYy(int value)
    {
        _WtacYy = value;
    }
    
    // Get<>AsString()
    public string GetWtacYyAsString()
    {
        return _WtacYy.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWtacYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtacYy = parsed;
    }
    
    // Standard Getter
    public int GetWtacMm()
    {
        return _WtacMm;
    }
    
    // Standard Setter
    public void SetWtacMm(int value)
    {
        _WtacMm = value;
    }
    
    // Get<>AsString()
    public string GetWtacMmAsString()
    {
        return _WtacMm.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWtacMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtacMm = parsed;
    }
    
    // Standard Getter
    public int GetWtacDd()
    {
        return _WtacDd;
    }
    
    // Standard Setter
    public void SetWtacDd(int value)
    {
        _WtacDd = value;
    }
    
    // Get<>AsString()
    public string GetWtacDdAsString()
    {
        return _WtacDd.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWtacDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtacDd = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
}

}}