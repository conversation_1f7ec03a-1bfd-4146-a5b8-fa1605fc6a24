using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing EqgetdtsLinkage Data Structure

public class EqgetdtsLinkage
{
    private static int _size = 34;
    // [DEBUG] Class: EqgetdtsLinkage, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: EqgetdtsParametersIn, is_external=, is_static_class=False, static_prefix=
    private EqgetdtsParametersIn _EqgetdtsParametersIn = new EqgetdtsParametersIn();
    
    
    
    
    // [DEBUG] Field: EqgetdtsParametersOut, is_external=, is_static_class=False, static_prefix=
    private EqgetdtsParametersOut _EqgetdtsParametersOut = new EqgetdtsParametersOut();
    
    
    
    
    
    // Serialization methods
    public string GetEqgetdtsLinkageAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_EqgetdtsParametersIn.GetEqgetdtsParametersInAsString());
        result.Append(_EqgetdtsParametersOut.GetEqgetdtsParametersOutAsString());
        
        return result.ToString();
    }
    
    public void SetEqgetdtsLinkageAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            _EqgetdtsParametersIn.SetEqgetdtsParametersInAsString(data.Substring(offset, 4));
        }
        else
        {
            _EqgetdtsParametersIn.SetEqgetdtsParametersInAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 30 <= data.Length)
        {
            _EqgetdtsParametersOut.SetEqgetdtsParametersOutAsString(data.Substring(offset, 30));
        }
        else
        {
            _EqgetdtsParametersOut.SetEqgetdtsParametersOutAsString(data.Substring(offset));
        }
        offset += 30;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetEqgetdtsLinkageAsString();
    }
    // Set<>String Override function
    public void SetEqgetdtsLinkage(string value)
    {
        SetEqgetdtsLinkageAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public EqgetdtsParametersIn GetEqgetdtsParametersIn()
    {
        return _EqgetdtsParametersIn;
    }
    
    // Standard Setter
    public void SetEqgetdtsParametersIn(EqgetdtsParametersIn value)
    {
        _EqgetdtsParametersIn = value;
    }
    
    // Get<>AsString()
    public string GetEqgetdtsParametersInAsString()
    {
        return _EqgetdtsParametersIn != null ? _EqgetdtsParametersIn.GetEqgetdtsParametersInAsString() : "";
    }
    
    // Set<>AsString()
    public void SetEqgetdtsParametersInAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_EqgetdtsParametersIn == null)
        {
            _EqgetdtsParametersIn = new EqgetdtsParametersIn();
        }
        _EqgetdtsParametersIn.SetEqgetdtsParametersInAsString(value);
    }
    
    // Standard Getter
    public EqgetdtsParametersOut GetEqgetdtsParametersOut()
    {
        return _EqgetdtsParametersOut;
    }
    
    // Standard Setter
    public void SetEqgetdtsParametersOut(EqgetdtsParametersOut value)
    {
        _EqgetdtsParametersOut = value;
    }
    
    // Get<>AsString()
    public string GetEqgetdtsParametersOutAsString()
    {
        return _EqgetdtsParametersOut != null ? _EqgetdtsParametersOut.GetEqgetdtsParametersOutAsString() : "";
    }
    
    // Set<>AsString()
    public void SetEqgetdtsParametersOutAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_EqgetdtsParametersOut == null)
        {
            _EqgetdtsParametersOut = new EqgetdtsParametersOut();
        }
        _EqgetdtsParametersOut.SetEqgetdtsParametersOutAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetEqgetdtsParametersIn(string value)
    {
        _EqgetdtsParametersIn.SetEqgetdtsParametersInAsString(value);
    }
    // Nested Class: EqgetdtsParametersIn
    public class EqgetdtsParametersIn
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: EqgetdtsCalendarId, is_external=, is_static_class=False, static_prefix=
        private string _EqgetdtsCalendarId ="";
        
        
        
        
        // [DEBUG] Field: EqgetdtsMasterFileYear, is_external=, is_static_class=False, static_prefix=
        private string _EqgetdtsMasterFileYear ="";
        
        
        
        
        // [DEBUG] Field: EqgetdtsMasterFileYearN, is_external=, is_static_class=False, static_prefix=
        private int _EqgetdtsMasterFileYearN =0;
        
        
        
        
    public EqgetdtsParametersIn() {}
    
    public EqgetdtsParametersIn(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetEqgetdtsCalendarId(data.Substring(offset, 0).Trim());
        offset += 0;
        SetEqgetdtsMasterFileYear(data.Substring(offset, 0).Trim());
        offset += 0;
        SetEqgetdtsMasterFileYearN(int.Parse(data.Substring(offset, 4).Trim()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetEqgetdtsParametersInAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_EqgetdtsCalendarId.PadRight(0));
        result.Append(_EqgetdtsMasterFileYear.PadRight(0));
        result.Append(_EqgetdtsMasterFileYearN.ToString().PadLeft(4, '0'));
        
        return result.ToString();
    }
    
    public void SetEqgetdtsParametersInAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetEqgetdtsCalendarId(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetEqgetdtsMasterFileYear(extracted);
        }
        offset += 0;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetEqgetdtsMasterFileYearN(parsedInt);
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetEqgetdtsCalendarId()
    {
        return _EqgetdtsCalendarId;
    }
    
    // Standard Setter
    public void SetEqgetdtsCalendarId(string value)
    {
        _EqgetdtsCalendarId = value;
    }
    
    // Get<>AsString()
    public string GetEqgetdtsCalendarIdAsString()
    {
        return _EqgetdtsCalendarId.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetEqgetdtsCalendarIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EqgetdtsCalendarId = value;
    }
    
    // Standard Getter
    public string GetEqgetdtsMasterFileYear()
    {
        return _EqgetdtsMasterFileYear;
    }
    
    // Standard Setter
    public void SetEqgetdtsMasterFileYear(string value)
    {
        _EqgetdtsMasterFileYear = value;
    }
    
    // Get<>AsString()
    public string GetEqgetdtsMasterFileYearAsString()
    {
        return _EqgetdtsMasterFileYear.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetEqgetdtsMasterFileYearAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EqgetdtsMasterFileYear = value;
    }
    
    // Standard Getter
    public int GetEqgetdtsMasterFileYearN()
    {
        return _EqgetdtsMasterFileYearN;
    }
    
    // Standard Setter
    public void SetEqgetdtsMasterFileYearN(int value)
    {
        _EqgetdtsMasterFileYearN = value;
    }
    
    // Get<>AsString()
    public string GetEqgetdtsMasterFileYearNAsString()
    {
        return _EqgetdtsMasterFileYearN.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetEqgetdtsMasterFileYearNAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _EqgetdtsMasterFileYearN = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetEqgetdtsParametersOut(string value)
{
    _EqgetdtsParametersOut.SetEqgetdtsParametersOutAsString(value);
}
// Nested Class: EqgetdtsParametersOut
public class EqgetdtsParametersOut
{
    private static int _size = 30;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtPeriodEndCalendar, is_external=, is_static_class=False, static_prefix=
    private EqgetdtsParametersOut.WtPeriodEndCalendar _WtPeriodEndCalendar = new EqgetdtsParametersOut.WtPeriodEndCalendar();
    
    
    
    
public EqgetdtsParametersOut() {}

public EqgetdtsParametersOut(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WtPeriodEndCalendar.SetWtPeriodEndCalendarAsString(data.Substring(offset, WtPeriodEndCalendar.GetSize()));
    offset += 30;
    
}

// Serialization methods
public string GetEqgetdtsParametersOutAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtPeriodEndCalendar.GetWtPeriodEndCalendarAsString());
    
    return result.ToString();
}

public void SetEqgetdtsParametersOutAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 30 <= data.Length)
    {
        _WtPeriodEndCalendar.SetWtPeriodEndCalendarAsString(data.Substring(offset, 30));
    }
    else
    {
        _WtPeriodEndCalendar.SetWtPeriodEndCalendarAsString(data.Substring(offset));
    }
    offset += 30;
}

// Getter and Setter methods

// Standard Getter
public WtPeriodEndCalendar GetWtPeriodEndCalendar()
{
    return _WtPeriodEndCalendar;
}

// Standard Setter
public void SetWtPeriodEndCalendar(WtPeriodEndCalendar value)
{
    _WtPeriodEndCalendar = value;
}

// Get<>AsString()
public string GetWtPeriodEndCalendarAsString()
{
    return _WtPeriodEndCalendar != null ? _WtPeriodEndCalendar.GetWtPeriodEndCalendarAsString() : "";
}

// Set<>AsString()
public void SetWtPeriodEndCalendarAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtPeriodEndCalendar == null)
    {
        _WtPeriodEndCalendar = new WtPeriodEndCalendar();
    }
    _WtPeriodEndCalendar.SetWtPeriodEndCalendarAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WtPeriodEndCalendar
public class WtPeriodEndCalendar
{
    private static int _size = 30;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler377, is_external=, is_static_class=False, static_prefix=
    private string _Filler377 ="PEND CALENDARS==";
    
    
    
    
    // [DEBUG] Field: WtcalMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtcalMaxTableSize =500;
    
    
    
    
    // [DEBUG] Field: WtcalOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtcalOccurs =500;
    
    
    
    
    // [DEBUG] Field: WtcalPeriodEndDatesTable, is_external=, is_static_class=False, static_prefix=
    private WtPeriodEndCalendar.WtcalPeriodEndDatesTable _WtcalPeriodEndDatesTable = new WtPeriodEndCalendar.WtcalPeriodEndDatesTable();
    
    
    
    
public WtPeriodEndCalendar() {}

public WtPeriodEndCalendar(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller377(data.Substring(offset, 16).Trim());
    offset += 16;
    SetWtcalMaxTableSize(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetWtcalOccurs(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    _WtcalPeriodEndDatesTable.SetWtcalPeriodEndDatesTableAsString(data.Substring(offset, WtcalPeriodEndDatesTable.GetSize()));
    offset += 8;
    
}

// Serialization methods
public string GetWtPeriodEndCalendarAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler377.PadRight(16));
    result.Append(_WtcalMaxTableSize.ToString().PadLeft(3, '0'));
    result.Append(_WtcalOccurs.ToString().PadLeft(3, '0'));
    result.Append(_WtcalPeriodEndDatesTable.GetWtcalPeriodEndDatesTableAsString());
    
    return result.ToString();
}

public void SetWtPeriodEndCalendarAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        SetFiller377(extracted);
    }
    offset += 16;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtcalMaxTableSize(parsedInt);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtcalOccurs(parsedInt);
    }
    offset += 3;
    if (offset + 8 <= data.Length)
    {
        _WtcalPeriodEndDatesTable.SetWtcalPeriodEndDatesTableAsString(data.Substring(offset, 8));
    }
    else
    {
        _WtcalPeriodEndDatesTable.SetWtcalPeriodEndDatesTableAsString(data.Substring(offset));
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller377()
{
    return _Filler377;
}

// Standard Setter
public void SetFiller377(string value)
{
    _Filler377 = value;
}

// Get<>AsString()
public string GetFiller377AsString()
{
    return _Filler377.PadRight(16);
}

// Set<>AsString()
public void SetFiller377AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler377 = value;
}

// Standard Getter
public int GetWtcalMaxTableSize()
{
    return _WtcalMaxTableSize;
}

// Standard Setter
public void SetWtcalMaxTableSize(int value)
{
    _WtcalMaxTableSize = value;
}

// Get<>AsString()
public string GetWtcalMaxTableSizeAsString()
{
    return _WtcalMaxTableSize.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetWtcalMaxTableSizeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtcalMaxTableSize = parsed;
}

// Standard Getter
public int GetWtcalOccurs()
{
    return _WtcalOccurs;
}

// Standard Setter
public void SetWtcalOccurs(int value)
{
    _WtcalOccurs = value;
}

// Get<>AsString()
public string GetWtcalOccursAsString()
{
    return _WtcalOccurs.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetWtcalOccursAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtcalOccurs = parsed;
}

// Standard Getter
public WtcalPeriodEndDatesTable GetWtcalPeriodEndDatesTable()
{
    return _WtcalPeriodEndDatesTable;
}

// Standard Setter
public void SetWtcalPeriodEndDatesTable(WtcalPeriodEndDatesTable value)
{
    _WtcalPeriodEndDatesTable = value;
}

// Get<>AsString()
public string GetWtcalPeriodEndDatesTableAsString()
{
    return _WtcalPeriodEndDatesTable != null ? _WtcalPeriodEndDatesTable.GetWtcalPeriodEndDatesTableAsString() : "";
}

// Set<>AsString()
public void SetWtcalPeriodEndDatesTableAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtcalPeriodEndDatesTable == null)
    {
        _WtcalPeriodEndDatesTable = new WtcalPeriodEndDatesTable();
    }
    _WtcalPeriodEndDatesTable.SetWtcalPeriodEndDatesTableAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WtcalPeriodEndDatesTable
public class WtcalPeriodEndDatesTable
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtcalElement, is_external=, is_static_class=False, static_prefix=
    private WtcalPeriodEndDatesTable.WtcalElement[] _WtcalElement = new WtcalPeriodEndDatesTable.WtcalElement[500];
    
    public void InitializeWtcalElementArray()
    {
        for (int i = 0; i < 500; i++)
        {
            _WtcalElement[i] = new WtcalPeriodEndDatesTable.WtcalElement();
        }
    }
    
    
    
public WtcalPeriodEndDatesTable() {}

public WtcalPeriodEndDatesTable(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    InitializeWtcalElementArray();
    for (int i = 0; i < 500; i++)
    {
        _WtcalElement[i].SetWtcalElementAsString(data.Substring(offset, 8));
        offset += 8;
    }
    
}

// Serialization methods
public string GetWtcalPeriodEndDatesTableAsString()
{
    StringBuilder result = new StringBuilder();
    
    for (int i = 0; i < 500; i++)
    {
        result.Append(_WtcalElement[i].GetWtcalElementAsString());
    }
    
    return result.ToString();
}

public void SetWtcalPeriodEndDatesTableAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    for (int i = 0; i < 500; i++)
    {
        if (offset + 8 > data.Length) break;
        string val = data.Substring(offset, 8);
        
        _WtcalElement[i].SetWtcalElementAsString(val);
        offset += 8;
    }
}

// Getter and Setter methods

// Array Accessors for WtcalElement
public WtcalElement GetWtcalElementAt(int index)
{
    return _WtcalElement[index];
}

public void SetWtcalElementAt(int index, WtcalElement value)
{
    _WtcalElement[index] = value;
}

// Flattened accessors (index 0)
public WtcalElement GetWtcalElement()
{
    return _WtcalElement != null && _WtcalElement.Length > 0
    ? _WtcalElement[0]
    : new WtcalElement();
}

public void SetWtcalElement(WtcalElement value)
{
    if (_WtcalElement == null || _WtcalElement.Length == 0)
    _WtcalElement = new WtcalElement[1];
    _WtcalElement[0] = value;
}





public static int GetSize()
{
    return _size;
}

// Nested Class: WtcalElement
public class WtcalElement
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtcalPeriodEndDate, is_external=, is_static_class=False, static_prefix=
    private WtcalElement.WtcalPeriodEndDate _WtcalPeriodEndDate = new WtcalElement.WtcalPeriodEndDate();
    
    
    
    
public WtcalElement() {}

public WtcalElement(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WtcalPeriodEndDate.SetWtcalPeriodEndDateAsString(data.Substring(offset, WtcalPeriodEndDate.GetSize()));
    offset += 8;
    
}

// Serialization methods
public string GetWtcalElementAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtcalPeriodEndDate.GetWtcalPeriodEndDateAsString());
    
    return result.ToString();
}

public void SetWtcalElementAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 8 <= data.Length)
    {
        _WtcalPeriodEndDate.SetWtcalPeriodEndDateAsString(data.Substring(offset, 8));
    }
    else
    {
        _WtcalPeriodEndDate.SetWtcalPeriodEndDateAsString(data.Substring(offset));
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public WtcalPeriodEndDate GetWtcalPeriodEndDate()
{
    return _WtcalPeriodEndDate;
}

// Standard Setter
public void SetWtcalPeriodEndDate(WtcalPeriodEndDate value)
{
    _WtcalPeriodEndDate = value;
}

// Get<>AsString()
public string GetWtcalPeriodEndDateAsString()
{
    return _WtcalPeriodEndDate != null ? _WtcalPeriodEndDate.GetWtcalPeriodEndDateAsString() : "";
}

// Set<>AsString()
public void SetWtcalPeriodEndDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtcalPeriodEndDate == null)
    {
        _WtcalPeriodEndDate = new WtcalPeriodEndDate();
    }
    _WtcalPeriodEndDate.SetWtcalPeriodEndDateAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WtcalPeriodEndDate
public class WtcalPeriodEndDate
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtcalPeriodEndDateCc, is_external=, is_static_class=False, static_prefix=
    private string _WtcalPeriodEndDateCc ="";
    
    
    
    
    // [DEBUG] Field: WtcalPeriodEndDateYymmdd, is_external=, is_static_class=False, static_prefix=
    private WtcalPeriodEndDate.WtcalPeriodEndDateYymmdd _WtcalPeriodEndDateYymmdd = new WtcalPeriodEndDate.WtcalPeriodEndDateYymmdd();
    
    
    
    
public WtcalPeriodEndDate() {}

public WtcalPeriodEndDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtcalPeriodEndDateCc(data.Substring(offset, 2).Trim());
    offset += 2;
    _WtcalPeriodEndDateYymmdd.SetWtcalPeriodEndDateYymmddAsString(data.Substring(offset, WtcalPeriodEndDateYymmdd.GetSize()));
    offset += 6;
    
}

// Serialization methods
public string GetWtcalPeriodEndDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtcalPeriodEndDateCc.PadRight(2));
    result.Append(_WtcalPeriodEndDateYymmdd.GetWtcalPeriodEndDateYymmddAsString());
    
    return result.ToString();
}

public void SetWtcalPeriodEndDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWtcalPeriodEndDateCc(extracted);
    }
    offset += 2;
    if (offset + 6 <= data.Length)
    {
        _WtcalPeriodEndDateYymmdd.SetWtcalPeriodEndDateYymmddAsString(data.Substring(offset, 6));
    }
    else
    {
        _WtcalPeriodEndDateYymmdd.SetWtcalPeriodEndDateYymmddAsString(data.Substring(offset));
    }
    offset += 6;
}

// Getter and Setter methods

// Standard Getter
public string GetWtcalPeriodEndDateCc()
{
    return _WtcalPeriodEndDateCc;
}

// Standard Setter
public void SetWtcalPeriodEndDateCc(string value)
{
    _WtcalPeriodEndDateCc = value;
}

// Get<>AsString()
public string GetWtcalPeriodEndDateCcAsString()
{
    return _WtcalPeriodEndDateCc.PadRight(2);
}

// Set<>AsString()
public void SetWtcalPeriodEndDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtcalPeriodEndDateCc = value;
}

// Standard Getter
public WtcalPeriodEndDateYymmdd GetWtcalPeriodEndDateYymmdd()
{
    return _WtcalPeriodEndDateYymmdd;
}

// Standard Setter
public void SetWtcalPeriodEndDateYymmdd(WtcalPeriodEndDateYymmdd value)
{
    _WtcalPeriodEndDateYymmdd = value;
}

// Get<>AsString()
public string GetWtcalPeriodEndDateYymmddAsString()
{
    return _WtcalPeriodEndDateYymmdd != null ? _WtcalPeriodEndDateYymmdd.GetWtcalPeriodEndDateYymmddAsString() : "";
}

// Set<>AsString()
public void SetWtcalPeriodEndDateYymmddAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtcalPeriodEndDateYymmdd == null)
    {
        _WtcalPeriodEndDateYymmdd = new WtcalPeriodEndDateYymmdd();
    }
    _WtcalPeriodEndDateYymmdd.SetWtcalPeriodEndDateYymmddAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WtcalPeriodEndDateYymmdd
public class WtcalPeriodEndDateYymmdd
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtcalPeriodEndDateYy, is_external=, is_static_class=False, static_prefix=
    private string _WtcalPeriodEndDateYy ="";
    
    
    
    
    // [DEBUG] Field: WtcalPeriodEndDateMm, is_external=, is_static_class=False, static_prefix=
    private string _WtcalPeriodEndDateMm ="";
    
    
    
    
    // [DEBUG] Field: WtcalPeriodEndDateDd, is_external=, is_static_class=False, static_prefix=
    private string _WtcalPeriodEndDateDd ="";
    
    
    
    
public WtcalPeriodEndDateYymmdd() {}

public WtcalPeriodEndDateYymmdd(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtcalPeriodEndDateYy(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWtcalPeriodEndDateMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWtcalPeriodEndDateDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetWtcalPeriodEndDateYymmddAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtcalPeriodEndDateYy.PadRight(2));
    result.Append(_WtcalPeriodEndDateMm.PadRight(2));
    result.Append(_WtcalPeriodEndDateDd.PadRight(2));
    
    return result.ToString();
}

public void SetWtcalPeriodEndDateYymmddAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWtcalPeriodEndDateYy(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWtcalPeriodEndDateMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWtcalPeriodEndDateDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetWtcalPeriodEndDateYy()
{
    return _WtcalPeriodEndDateYy;
}

// Standard Setter
public void SetWtcalPeriodEndDateYy(string value)
{
    _WtcalPeriodEndDateYy = value;
}

// Get<>AsString()
public string GetWtcalPeriodEndDateYyAsString()
{
    return _WtcalPeriodEndDateYy.PadRight(2);
}

// Set<>AsString()
public void SetWtcalPeriodEndDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtcalPeriodEndDateYy = value;
}

// Standard Getter
public string GetWtcalPeriodEndDateMm()
{
    return _WtcalPeriodEndDateMm;
}

// Standard Setter
public void SetWtcalPeriodEndDateMm(string value)
{
    _WtcalPeriodEndDateMm = value;
}

// Get<>AsString()
public string GetWtcalPeriodEndDateMmAsString()
{
    return _WtcalPeriodEndDateMm.PadRight(2);
}

// Set<>AsString()
public void SetWtcalPeriodEndDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtcalPeriodEndDateMm = value;
}

// Standard Getter
public string GetWtcalPeriodEndDateDd()
{
    return _WtcalPeriodEndDateDd;
}

// Standard Setter
public void SetWtcalPeriodEndDateDd(string value)
{
    _WtcalPeriodEndDateDd = value;
}

// Get<>AsString()
public string GetWtcalPeriodEndDateDdAsString()
{
    return _WtcalPeriodEndDateDd.PadRight(2);
}

// Set<>AsString()
public void SetWtcalPeriodEndDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtcalPeriodEndDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}
}
}
}

}}
