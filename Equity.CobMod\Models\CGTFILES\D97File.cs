using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D97File Data Structure

public class D97File
{
    private static int _size = 12;
    // [DEBUG] Class: D97File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler282, is_external=, is_static_class=False, static_prefix=
    private string _Filler282 ="$";
    
    
    
    
    // [DEBUG] Field: D97UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D97UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler283, is_external=, is_static_class=False, static_prefix=
    private string _Filler283 ="RN";
    
    
    
    
    // [DEBUG] Field: D97ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D97ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler284, is_external=, is_static_class=False, static_prefix=
    private string _Filler284 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD97FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler282.PadRight(1));
        result.Append(_D97UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler283.PadRight(2));
        result.Append(_D97ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler284.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD97FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller282(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD97UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller283(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD97ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller284(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD97FileAsString();
    }
    // Set<>String Override function
    public void SetD97File(string value)
    {
        SetD97FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller282()
    {
        return _Filler282;
    }
    
    // Standard Setter
    public void SetFiller282(string value)
    {
        _Filler282 = value;
    }
    
    // Get<>AsString()
    public string GetFiller282AsString()
    {
        return _Filler282.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller282AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler282 = value;
    }
    
    // Standard Getter
    public int GetD97UserNo()
    {
        return _D97UserNo;
    }
    
    // Standard Setter
    public void SetD97UserNo(int value)
    {
        _D97UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD97UserNoAsString()
    {
        return _D97UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD97UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D97UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller283()
    {
        return _Filler283;
    }
    
    // Standard Setter
    public void SetFiller283(string value)
    {
        _Filler283 = value;
    }
    
    // Get<>AsString()
    public string GetFiller283AsString()
    {
        return _Filler283.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller283AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler283 = value;
    }
    
    // Standard Getter
    public int GetD97ReportNo()
    {
        return _D97ReportNo;
    }
    
    // Standard Setter
    public void SetD97ReportNo(int value)
    {
        _D97ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD97ReportNoAsString()
    {
        return _D97ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD97ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D97ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller284()
    {
        return _Filler284;
    }
    
    // Standard Setter
    public void SetFiller284(string value)
    {
        _Filler284 = value;
    }
    
    // Get<>AsString()
    public string GetFiller284AsString()
    {
        return _Filler284.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller284AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler284 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}