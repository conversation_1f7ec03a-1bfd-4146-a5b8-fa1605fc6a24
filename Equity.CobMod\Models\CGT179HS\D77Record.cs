using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt179HsDTO
{// DTO class representing D77Record Data Structure

public class D77Record
{
    private static int _size = 94;
    // [DEBUG] Class: D77Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler1, is_external=, is_static_class=False, static_prefix=
    private string _Filler1 ="";
    
    
    
    
    // [DEBUG] Field: D77FundCode, is_external=, is_static_class=False, static_prefix=
    private string _D77FundCode ="";
    
    
    
    
    // [DEBUG] Field: Filler2, is_external=, is_static_class=False, static_prefix=
    private string _Filler2 ="";
    
    
    
    
    // [DEBUG] Field: D77SedolCode, is_external=, is_static_class=False, static_prefix=
    private string _D77SedolCode ="";
    
    
    
    
    // [DEBUG] Field: Filler3, is_external=, is_static_class=False, static_prefix=
    private string _Filler3 ="";
    
    
    
    
    // [DEBUG] Field: D77Holding, is_external=, is_static_class=False, static_prefix=
    private decimal _D77Holding =0;
    
    
    
    
    // [DEBUG] Field: Filler4, is_external=, is_static_class=False, static_prefix=
    private string _Filler4 ="";
    
    
    
    
    // [DEBUG] Field: D77DisposalDate, is_external=, is_static_class=False, static_prefix=
    private string _D77DisposalDate ="";
    
    
    
    
    // [DEBUG] Field: Filler5, is_external=, is_static_class=False, static_prefix=
    private string _Filler5 ="";
    
    
    
    
    // [DEBUG] Field: D77TodaysDate, is_external=, is_static_class=False, static_prefix=
    private string _D77TodaysDate ="";
    
    
    
    
    // [DEBUG] Field: Filler6, is_external=, is_static_class=False, static_prefix=
    private string _Filler6 ="";
    
    
    
    
    // [DEBUG] Field: D77CapitalGain, is_external=, is_static_class=False, static_prefix=
    private decimal _D77CapitalGain =0;
    
    
    
    
    // [DEBUG] Field: Filler7, is_external=, is_static_class=False, static_prefix=
    private string _Filler7 ="";
    
    
    
    
    // [DEBUG] Field: D77CapitalLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D77CapitalLoss =0;
    
    
    
    
    // [DEBUG] Field: Filler8, is_external=, is_static_class=False, static_prefix=
    private string _Filler8 ="";
    
    
    
    
    // [DEBUG] Field: D77HoldingFlag, is_external=, is_static_class=False, static_prefix=
    private string _D77HoldingFlag ="";
    
    
    
    
    // [DEBUG] Field: Filler9, is_external=, is_static_class=False, static_prefix=
    private string _Filler9 ="";
    
    
    
    
    // [DEBUG] Field: D77TransReference, is_external=, is_static_class=False, static_prefix=
    private string _D77TransReference ="";
    
    
    
    
    // [DEBUG] Field: Filler10, is_external=, is_static_class=False, static_prefix=
    private string _Filler10 ="";
    
    
    
    
    // [DEBUG] Field: D77TransBaseCost, is_external=, is_static_class=False, static_prefix=
    private decimal _D77TransBaseCost =0;
    
    
    
    
    // [DEBUG] Field: Filler11, is_external=, is_static_class=False, static_prefix=
    private string _Filler11 ="";
    
    
    
    
    // [DEBUG] Field: D77TransIndexation, is_external=, is_static_class=False, static_prefix=
    private decimal _D77TransIndexation =0;
    
    
    
    
    // [DEBUG] Field: Filler12, is_external=, is_static_class=False, static_prefix=
    private string _Filler12 ="";
    
    
    
    
    // [DEBUG] Field: D77TransProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _D77TransProceeds =0;
    
    
    
    
    // [DEBUG] Field: Filler13, is_external=, is_static_class=False, static_prefix=
    private string _Filler13 ="";
    
    
    
    
    // [DEBUG] Field: D77HoldingSign, is_external=, is_static_class=False, static_prefix=
    private string _D77HoldingSign ="";
    
    
    
    
    // [DEBUG] Field: Filler14, is_external=, is_static_class=False, static_prefix=
    private string _Filler14 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD77RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler1.PadRight(0));
        result.Append(_D77FundCode.PadRight(0));
        result.Append(_Filler2.PadRight(0));
        result.Append(_D77SedolCode.PadRight(0));
        result.Append(_Filler3.PadRight(0));
        result.Append(_D77Holding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler4.PadRight(0));
        result.Append(_D77DisposalDate.PadRight(0));
        result.Append(_Filler5.PadRight(0));
        result.Append(_D77TodaysDate.PadRight(0));
        result.Append(_Filler6.PadRight(0));
        result.Append(_D77CapitalGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler7.PadRight(0));
        result.Append(_D77CapitalLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler8.PadRight(0));
        result.Append(_D77HoldingFlag.PadRight(0));
        result.Append(_Filler9.PadRight(0));
        result.Append(_D77TransReference.PadRight(10));
        result.Append(_Filler10.PadRight(0));
        result.Append(_D77TransBaseCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler11.PadRight(0));
        result.Append(_D77TransIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler12.PadRight(0));
        result.Append(_D77TransProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler13.PadRight(0));
        result.Append(_D77HoldingSign.PadRight(0));
        result.Append(_Filler14.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD77RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller1(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD77FundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller2(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD77SedolCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller3(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD77Holding(parsedDec);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller4(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD77DisposalDate(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller5(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD77TodaysDate(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller6(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD77CapitalGain(parsedDec);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller7(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD77CapitalLoss(parsedDec);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller8(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD77HoldingFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller9(extracted);
        }
        offset += 0;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetD77TransReference(extracted);
        }
        offset += 10;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller10(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD77TransBaseCost(parsedDec);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller11(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD77TransIndexation(parsedDec);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller12(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD77TransProceeds(parsedDec);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller13(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD77HoldingSign(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller14(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD77RecordAsString();
    }
    // Set<>String Override function
    public void SetD77Record(string value)
    {
        SetD77RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller1()
    {
        return _Filler1;
    }
    
    // Standard Setter
    public void SetFiller1(string value)
    {
        _Filler1 = value;
    }
    
    // Get<>AsString()
    public string GetFiller1AsString()
    {
        return _Filler1.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler1 = value;
    }
    
    // Standard Getter
    public string GetD77FundCode()
    {
        return _D77FundCode;
    }
    
    // Standard Setter
    public void SetD77FundCode(string value)
    {
        _D77FundCode = value;
    }
    
    // Get<>AsString()
    public string GetD77FundCodeAsString()
    {
        return _D77FundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD77FundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D77FundCode = value;
    }
    
    // Standard Getter
    public string GetFiller2()
    {
        return _Filler2;
    }
    
    // Standard Setter
    public void SetFiller2(string value)
    {
        _Filler2 = value;
    }
    
    // Get<>AsString()
    public string GetFiller2AsString()
    {
        return _Filler2.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler2 = value;
    }
    
    // Standard Getter
    public string GetD77SedolCode()
    {
        return _D77SedolCode;
    }
    
    // Standard Setter
    public void SetD77SedolCode(string value)
    {
        _D77SedolCode = value;
    }
    
    // Get<>AsString()
    public string GetD77SedolCodeAsString()
    {
        return _D77SedolCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD77SedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D77SedolCode = value;
    }
    
    // Standard Getter
    public string GetFiller3()
    {
        return _Filler3;
    }
    
    // Standard Setter
    public void SetFiller3(string value)
    {
        _Filler3 = value;
    }
    
    // Get<>AsString()
    public string GetFiller3AsString()
    {
        return _Filler3.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler3 = value;
    }
    
    // Standard Getter
    public decimal GetD77Holding()
    {
        return _D77Holding;
    }
    
    // Standard Setter
    public void SetD77Holding(decimal value)
    {
        _D77Holding = value;
    }
    
    // Get<>AsString()
    public string GetD77HoldingAsString()
    {
        return _D77Holding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD77HoldingAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D77Holding = parsed;
    }
    
    // Standard Getter
    public string GetFiller4()
    {
        return _Filler4;
    }
    
    // Standard Setter
    public void SetFiller4(string value)
    {
        _Filler4 = value;
    }
    
    // Get<>AsString()
    public string GetFiller4AsString()
    {
        return _Filler4.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler4 = value;
    }
    
    // Standard Getter
    public string GetD77DisposalDate()
    {
        return _D77DisposalDate;
    }
    
    // Standard Setter
    public void SetD77DisposalDate(string value)
    {
        _D77DisposalDate = value;
    }
    
    // Get<>AsString()
    public string GetD77DisposalDateAsString()
    {
        return _D77DisposalDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD77DisposalDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D77DisposalDate = value;
    }
    
    // Standard Getter
    public string GetFiller5()
    {
        return _Filler5;
    }
    
    // Standard Setter
    public void SetFiller5(string value)
    {
        _Filler5 = value;
    }
    
    // Get<>AsString()
    public string GetFiller5AsString()
    {
        return _Filler5.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler5 = value;
    }
    
    // Standard Getter
    public string GetD77TodaysDate()
    {
        return _D77TodaysDate;
    }
    
    // Standard Setter
    public void SetD77TodaysDate(string value)
    {
        _D77TodaysDate = value;
    }
    
    // Get<>AsString()
    public string GetD77TodaysDateAsString()
    {
        return _D77TodaysDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD77TodaysDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D77TodaysDate = value;
    }
    
    // Standard Getter
    public string GetFiller6()
    {
        return _Filler6;
    }
    
    // Standard Setter
    public void SetFiller6(string value)
    {
        _Filler6 = value;
    }
    
    // Get<>AsString()
    public string GetFiller6AsString()
    {
        return _Filler6.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler6 = value;
    }
    
    // Standard Getter
    public decimal GetD77CapitalGain()
    {
        return _D77CapitalGain;
    }
    
    // Standard Setter
    public void SetD77CapitalGain(decimal value)
    {
        _D77CapitalGain = value;
    }
    
    // Get<>AsString()
    public string GetD77CapitalGainAsString()
    {
        return _D77CapitalGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD77CapitalGainAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D77CapitalGain = parsed;
    }
    
    // Standard Getter
    public string GetFiller7()
    {
        return _Filler7;
    }
    
    // Standard Setter
    public void SetFiller7(string value)
    {
        _Filler7 = value;
    }
    
    // Get<>AsString()
    public string GetFiller7AsString()
    {
        return _Filler7.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler7 = value;
    }
    
    // Standard Getter
    public decimal GetD77CapitalLoss()
    {
        return _D77CapitalLoss;
    }
    
    // Standard Setter
    public void SetD77CapitalLoss(decimal value)
    {
        _D77CapitalLoss = value;
    }
    
    // Get<>AsString()
    public string GetD77CapitalLossAsString()
    {
        return _D77CapitalLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD77CapitalLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D77CapitalLoss = parsed;
    }
    
    // Standard Getter
    public string GetFiller8()
    {
        return _Filler8;
    }
    
    // Standard Setter
    public void SetFiller8(string value)
    {
        _Filler8 = value;
    }
    
    // Get<>AsString()
    public string GetFiller8AsString()
    {
        return _Filler8.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler8 = value;
    }
    
    // Standard Getter
    public string GetD77HoldingFlag()
    {
        return _D77HoldingFlag;
    }
    
    // Standard Setter
    public void SetD77HoldingFlag(string value)
    {
        _D77HoldingFlag = value;
    }
    
    // Get<>AsString()
    public string GetD77HoldingFlagAsString()
    {
        return _D77HoldingFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD77HoldingFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D77HoldingFlag = value;
    }
    
    // Standard Getter
    public string GetFiller9()
    {
        return _Filler9;
    }
    
    // Standard Setter
    public void SetFiller9(string value)
    {
        _Filler9 = value;
    }
    
    // Get<>AsString()
    public string GetFiller9AsString()
    {
        return _Filler9.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler9 = value;
    }
    
    // Standard Getter
    public string GetD77TransReference()
    {
        return _D77TransReference;
    }
    
    // Standard Setter
    public void SetD77TransReference(string value)
    {
        _D77TransReference = value;
    }
    
    // Get<>AsString()
    public string GetD77TransReferenceAsString()
    {
        return _D77TransReference.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetD77TransReferenceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D77TransReference = value;
    }
    
    // Standard Getter
    public string GetFiller10()
    {
        return _Filler10;
    }
    
    // Standard Setter
    public void SetFiller10(string value)
    {
        _Filler10 = value;
    }
    
    // Get<>AsString()
    public string GetFiller10AsString()
    {
        return _Filler10.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler10 = value;
    }
    
    // Standard Getter
    public decimal GetD77TransBaseCost()
    {
        return _D77TransBaseCost;
    }
    
    // Standard Setter
    public void SetD77TransBaseCost(decimal value)
    {
        _D77TransBaseCost = value;
    }
    
    // Get<>AsString()
    public string GetD77TransBaseCostAsString()
    {
        return _D77TransBaseCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD77TransBaseCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D77TransBaseCost = parsed;
    }
    
    // Standard Getter
    public string GetFiller11()
    {
        return _Filler11;
    }
    
    // Standard Setter
    public void SetFiller11(string value)
    {
        _Filler11 = value;
    }
    
    // Get<>AsString()
    public string GetFiller11AsString()
    {
        return _Filler11.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller11AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler11 = value;
    }
    
    // Standard Getter
    public decimal GetD77TransIndexation()
    {
        return _D77TransIndexation;
    }
    
    // Standard Setter
    public void SetD77TransIndexation(decimal value)
    {
        _D77TransIndexation = value;
    }
    
    // Get<>AsString()
    public string GetD77TransIndexationAsString()
    {
        return _D77TransIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD77TransIndexationAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D77TransIndexation = parsed;
    }
    
    // Standard Getter
    public string GetFiller12()
    {
        return _Filler12;
    }
    
    // Standard Setter
    public void SetFiller12(string value)
    {
        _Filler12 = value;
    }
    
    // Get<>AsString()
    public string GetFiller12AsString()
    {
        return _Filler12.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller12AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler12 = value;
    }
    
    // Standard Getter
    public decimal GetD77TransProceeds()
    {
        return _D77TransProceeds;
    }
    
    // Standard Setter
    public void SetD77TransProceeds(decimal value)
    {
        _D77TransProceeds = value;
    }
    
    // Get<>AsString()
    public string GetD77TransProceedsAsString()
    {
        return _D77TransProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD77TransProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D77TransProceeds = parsed;
    }
    
    // Standard Getter
    public string GetFiller13()
    {
        return _Filler13;
    }
    
    // Standard Setter
    public void SetFiller13(string value)
    {
        _Filler13 = value;
    }
    
    // Get<>AsString()
    public string GetFiller13AsString()
    {
        return _Filler13.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller13AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler13 = value;
    }
    
    // Standard Getter
    public string GetD77HoldingSign()
    {
        return _D77HoldingSign;
    }
    
    // Standard Setter
    public void SetD77HoldingSign(string value)
    {
        _D77HoldingSign = value;
    }
    
    // Get<>AsString()
    public string GetD77HoldingSignAsString()
    {
        return _D77HoldingSign.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD77HoldingSignAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D77HoldingSign = value;
    }
    
    // Standard Getter
    public string GetFiller14()
    {
        return _Filler14;
    }
    
    // Standard Setter
    public void SetFiller14(string value)
    {
        _Filler14 = value;
    }
    
    // Get<>AsString()
    public string GetFiller14AsString()
    {
        return _Filler14.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller14AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler14 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}