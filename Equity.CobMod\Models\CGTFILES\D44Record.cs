using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D44Record Data Structure

public class D44Record
{
    private static int _size = 80;
    // [DEBUG] Class: D44Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D44TransEnd, is_external=, is_static_class=False, static_prefix=
    private string _D44TransEnd ="";
    
    
    
    
    // [DEBUG] Field: Filler29, is_external=, is_static_class=False, static_prefix=
    private string _Filler29 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD44RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D44TransEnd.PadRight(11));
        result.Append(_Filler29.PadRight(69));
        
        return result.ToString();
    }
    
    public void SetD44RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetD44TransEnd(extracted);
        }
        offset += 11;
        if (offset + 69 <= data.Length)
        {
            string extracted = data.Substring(offset, 69).Trim();
            SetFiller29(extracted);
        }
        offset += 69;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD44RecordAsString();
    }
    // Set<>String Override function
    public void SetD44Record(string value)
    {
        SetD44RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD44TransEnd()
    {
        return _D44TransEnd;
    }
    
    // Standard Setter
    public void SetD44TransEnd(string value)
    {
        _D44TransEnd = value;
    }
    
    // Get<>AsString()
    public string GetD44TransEndAsString()
    {
        return _D44TransEnd.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetD44TransEndAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D44TransEnd = value;
    }
    
    // Standard Getter
    public string GetFiller29()
    {
        return _Filler29;
    }
    
    // Standard Setter
    public void SetFiller29(string value)
    {
        _Filler29 = value;
    }
    
    // Get<>AsString()
    public string GetFiller29AsString()
    {
        return _Filler29.PadRight(69);
    }
    
    // Set<>AsString()
    public void SetFiller29AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler29 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}