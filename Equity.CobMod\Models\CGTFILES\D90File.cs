using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D90File Data Structure

public class D90File
{
    private static int _size = 12;
    // [DEBUG] Class: D90File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler267, is_external=, is_static_class=False, static_prefix=
    private string _Filler267 ="$";
    
    
    
    
    // [DEBUG] Field: D90UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D90UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler268, is_external=, is_static_class=False, static_prefix=
    private string _Filler268 ="CLD.REP";
    
    
    
    
    
    // Serialization methods
    public string GetD90FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler267.PadRight(1));
        result.Append(_D90UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler268.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD90FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller267(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD90UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller268(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD90FileAsString();
    }
    // Set<>String Override function
    public void SetD90File(string value)
    {
        SetD90FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller267()
    {
        return _Filler267;
    }
    
    // Standard Setter
    public void SetFiller267(string value)
    {
        _Filler267 = value;
    }
    
    // Get<>AsString()
    public string GetFiller267AsString()
    {
        return _Filler267.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller267AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler267 = value;
    }
    
    // Standard Getter
    public int GetD90UserNo()
    {
        return _D90UserNo;
    }
    
    // Standard Setter
    public void SetD90UserNo(int value)
    {
        _D90UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD90UserNoAsString()
    {
        return _D90UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD90UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D90UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller268()
    {
        return _Filler268;
    }
    
    // Standard Setter
    public void SetFiller268(string value)
    {
        _Filler268 = value;
    }
    
    // Get<>AsString()
    public string GetFiller268AsString()
    {
        return _Filler268.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller268AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler268 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}