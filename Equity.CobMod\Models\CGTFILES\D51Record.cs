using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D51Record Data Structure

public class D51Record
{
    private static int _size = 190;
    // [DEBUG] Class: D51Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D51SedolCode, is_external=, is_static_class=False, static_prefix=
    private string _D51SedolCode ="";
    
    
    
    
    // [DEBUG] Field: D51Issuer, is_external=, is_static_class=False, static_prefix=
    private string _D51Issuer ="";
    
    
    
    
    // [DEBUG] Field: D51Description, is_external=, is_static_class=False, static_prefix=
    private string _D51Description ="";
    
    
    
    
    // [DEBUG] Field: D51SecurityShortName, is_external=, is_static_class=False, static_prefix=
    private string _D51SecurityShortName ="";
    
    
    
    
    // [DEBUG] Field: D51SortCode, is_external=, is_static_class=False, static_prefix=
    private string _D51SortCode ="";
    
    
    
    
    // [DEBUG] Field: D51Comments, is_external=, is_static_class=False, static_prefix=
    private string _D51Comments ="";
    
    
    
    
    // [DEBUG] Field: D51SecurityType, is_external=, is_static_class=False, static_prefix=
    private string _D51SecurityType ="";
    
    
    
    
    // [DEBUG] Field: D51QuotedUnquoted, is_external=, is_static_class=False, static_prefix=
    private string _D51QuotedUnquoted ="";
    
    
    
    
    // [DEBUG] Field: D51CountryCode, is_external=, is_static_class=False, static_prefix=
    private string _D51CountryCode ="";
    
    
    
    
    // [DEBUG] Field: D51GroupCode, is_external=, is_static_class=False, static_prefix=
    private string _D51GroupCode ="";
    
    
    
    
    // [DEBUG] Field: D51IndustrialClass, is_external=, is_static_class=False, static_prefix=
    private int _D51IndustrialClass =0;
    
    
    
    
    // [DEBUG] Field: D51IssuedCapital, is_external=, is_static_class=False, static_prefix=
    private decimal _D51IssuedCapital =0;
    
    
    
    
    // [DEBUG] Field: D51PricePercentInd, is_external=, is_static_class=False, static_prefix=
    private string _D51PricePercentInd ="";
    
    
    
    
    // [DEBUG] Field: D51QualifyingScheme, is_external=, is_static_class=False, static_prefix=
    private string _D51QualifyingScheme ="";
    
    
    
    
    // [DEBUG] Field: D51S54Asset, is_external=, is_static_class=False, static_prefix=
    private string _D51S54Asset ="";
    
    
    
    
    // [DEBUG] Field: D51DateOfIssue, is_external=, is_static_class=False, static_prefix=
    private D51DateOfIssue _D51DateOfIssue = new D51DateOfIssue();
    
    
    
    
    // [DEBUG] Field: D51IndexFromIssue, is_external=, is_static_class=False, static_prefix=
    private string _D51IndexFromIssue ="";
    
    
    
    
    // [DEBUG] Field: D51LrBasis, is_external=, is_static_class=False, static_prefix=
    private string _D51LrBasis ="";
    
    
    
    
    // [DEBUG] Field: D51MaturityDate, is_external=, is_static_class=False, static_prefix=
    private D51MaturityDate _D51MaturityDate = new D51MaturityDate();
    
    
    
    
    // [DEBUG] Field: D51ParValue, is_external=, is_static_class=False, static_prefix=
    private decimal _D51ParValue =0;
    
    
    
    
    
    // Serialization methods
    public string GetD51RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D51SedolCode.PadRight(7));
        result.Append(_D51Issuer.PadRight(35));
        result.Append(_D51Description.PadRight(40));
        result.Append(_D51SecurityShortName.PadRight(18));
        result.Append(_D51SortCode.PadRight(15));
        result.Append(_D51Comments.PadRight(40));
        result.Append(_D51SecurityType.PadRight(1));
        result.Append(_D51QuotedUnquoted.PadRight(1));
        result.Append(_D51CountryCode.PadRight(3));
        result.Append(_D51GroupCode.PadRight(3));
        result.Append(_D51IndustrialClass.ToString().PadLeft(2, '0'));
        result.Append(_D51IssuedCapital.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D51PricePercentInd.PadRight(1));
        result.Append(_D51QualifyingScheme.PadRight(1));
        result.Append(_D51S54Asset.PadRight(1));
        result.Append(_D51DateOfIssue.GetD51DateOfIssueAsString());
        result.Append(_D51IndexFromIssue.PadRight(1));
        result.Append(_D51LrBasis.PadRight(1));
        result.Append(_D51MaturityDate.GetD51MaturityDateAsString());
        result.Append(_D51ParValue.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
        
        return result.ToString();
    }
    
    public void SetD51RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetD51SedolCode(extracted);
        }
        offset += 7;
        if (offset + 35 <= data.Length)
        {
            string extracted = data.Substring(offset, 35).Trim();
            SetD51Issuer(extracted);
        }
        offset += 35;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD51Description(extracted);
        }
        offset += 40;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            SetD51SecurityShortName(extracted);
        }
        offset += 18;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            SetD51SortCode(extracted);
        }
        offset += 15;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD51Comments(extracted);
        }
        offset += 40;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD51SecurityType(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD51QuotedUnquoted(extracted);
        }
        offset += 1;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD51CountryCode(extracted);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD51GroupCode(extracted);
        }
        offset += 3;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD51IndustrialClass(parsedInt);
        }
        offset += 2;
        if (offset + 9 <= data.Length)
        {
            string extracted = data.Substring(offset, 9).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD51IssuedCapital(parsedDec);
        }
        offset += 9;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD51PricePercentInd(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD51QualifyingScheme(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD51S54Asset(extracted);
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            _D51DateOfIssue.SetD51DateOfIssueAsString(data.Substring(offset, 0));
        }
        else
        {
            _D51DateOfIssue.SetD51DateOfIssueAsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD51IndexFromIssue(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD51LrBasis(extracted);
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            _D51MaturityDate.SetD51MaturityDateAsString(data.Substring(offset, 0));
        }
        else
        {
            _D51MaturityDate.SetD51MaturityDateAsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD51ParValue(parsedDec);
        }
        offset += 11;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD51RecordAsString();
    }
    // Set<>String Override function
    public void SetD51Record(string value)
    {
        SetD51RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD51SedolCode()
    {
        return _D51SedolCode;
    }
    
    // Standard Setter
    public void SetD51SedolCode(string value)
    {
        _D51SedolCode = value;
    }
    
    // Get<>AsString()
    public string GetD51SedolCodeAsString()
    {
        return _D51SedolCode.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetD51SedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51SedolCode = value;
    }
    
    // Standard Getter
    public string GetD51Issuer()
    {
        return _D51Issuer;
    }
    
    // Standard Setter
    public void SetD51Issuer(string value)
    {
        _D51Issuer = value;
    }
    
    // Get<>AsString()
    public string GetD51IssuerAsString()
    {
        return _D51Issuer.PadRight(35);
    }
    
    // Set<>AsString()
    public void SetD51IssuerAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51Issuer = value;
    }
    
    // Standard Getter
    public string GetD51Description()
    {
        return _D51Description;
    }
    
    // Standard Setter
    public void SetD51Description(string value)
    {
        _D51Description = value;
    }
    
    // Get<>AsString()
    public string GetD51DescriptionAsString()
    {
        return _D51Description.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD51DescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51Description = value;
    }
    
    // Standard Getter
    public string GetD51SecurityShortName()
    {
        return _D51SecurityShortName;
    }
    
    // Standard Setter
    public void SetD51SecurityShortName(string value)
    {
        _D51SecurityShortName = value;
    }
    
    // Get<>AsString()
    public string GetD51SecurityShortNameAsString()
    {
        return _D51SecurityShortName.PadRight(18);
    }
    
    // Set<>AsString()
    public void SetD51SecurityShortNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51SecurityShortName = value;
    }
    
    // Standard Getter
    public string GetD51SortCode()
    {
        return _D51SortCode;
    }
    
    // Standard Setter
    public void SetD51SortCode(string value)
    {
        _D51SortCode = value;
    }
    
    // Get<>AsString()
    public string GetD51SortCodeAsString()
    {
        return _D51SortCode.PadRight(15);
    }
    
    // Set<>AsString()
    public void SetD51SortCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51SortCode = value;
    }
    
    // Standard Getter
    public string GetD51Comments()
    {
        return _D51Comments;
    }
    
    // Standard Setter
    public void SetD51Comments(string value)
    {
        _D51Comments = value;
    }
    
    // Get<>AsString()
    public string GetD51CommentsAsString()
    {
        return _D51Comments.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD51CommentsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51Comments = value;
    }
    
    // Standard Getter
    public string GetD51SecurityType()
    {
        return _D51SecurityType;
    }
    
    // Standard Setter
    public void SetD51SecurityType(string value)
    {
        _D51SecurityType = value;
    }
    
    // Get<>AsString()
    public string GetD51SecurityTypeAsString()
    {
        return _D51SecurityType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD51SecurityTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51SecurityType = value;
    }
    
    // Standard Getter
    public string GetD51QuotedUnquoted()
    {
        return _D51QuotedUnquoted;
    }
    
    // Standard Setter
    public void SetD51QuotedUnquoted(string value)
    {
        _D51QuotedUnquoted = value;
    }
    
    // Get<>AsString()
    public string GetD51QuotedUnquotedAsString()
    {
        return _D51QuotedUnquoted.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD51QuotedUnquotedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51QuotedUnquoted = value;
    }
    
    // Standard Getter
    public string GetD51CountryCode()
    {
        return _D51CountryCode;
    }
    
    // Standard Setter
    public void SetD51CountryCode(string value)
    {
        _D51CountryCode = value;
    }
    
    // Get<>AsString()
    public string GetD51CountryCodeAsString()
    {
        return _D51CountryCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD51CountryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51CountryCode = value;
    }
    
    // Standard Getter
    public string GetD51GroupCode()
    {
        return _D51GroupCode;
    }
    
    // Standard Setter
    public void SetD51GroupCode(string value)
    {
        _D51GroupCode = value;
    }
    
    // Get<>AsString()
    public string GetD51GroupCodeAsString()
    {
        return _D51GroupCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD51GroupCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51GroupCode = value;
    }
    
    // Standard Getter
    public int GetD51IndustrialClass()
    {
        return _D51IndustrialClass;
    }
    
    // Standard Setter
    public void SetD51IndustrialClass(int value)
    {
        _D51IndustrialClass = value;
    }
    
    // Get<>AsString()
    public string GetD51IndustrialClassAsString()
    {
        return _D51IndustrialClass.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD51IndustrialClassAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D51IndustrialClass = parsed;
    }
    
    // Standard Getter
    public decimal GetD51IssuedCapital()
    {
        return _D51IssuedCapital;
    }
    
    // Standard Setter
    public void SetD51IssuedCapital(decimal value)
    {
        _D51IssuedCapital = value;
    }
    
    // Get<>AsString()
    public string GetD51IssuedCapitalAsString()
    {
        return _D51IssuedCapital.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD51IssuedCapitalAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D51IssuedCapital = parsed;
    }
    
    // Standard Getter
    public string GetD51PricePercentInd()
    {
        return _D51PricePercentInd;
    }
    
    // Standard Setter
    public void SetD51PricePercentInd(string value)
    {
        _D51PricePercentInd = value;
    }
    
    // Get<>AsString()
    public string GetD51PricePercentIndAsString()
    {
        return _D51PricePercentInd.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD51PricePercentIndAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51PricePercentInd = value;
    }
    
    // Standard Getter
    public string GetD51QualifyingScheme()
    {
        return _D51QualifyingScheme;
    }
    
    // Standard Setter
    public void SetD51QualifyingScheme(string value)
    {
        _D51QualifyingScheme = value;
    }
    
    // Get<>AsString()
    public string GetD51QualifyingSchemeAsString()
    {
        return _D51QualifyingScheme.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD51QualifyingSchemeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51QualifyingScheme = value;
    }
    
    // Standard Getter
    public string GetD51S54Asset()
    {
        return _D51S54Asset;
    }
    
    // Standard Setter
    public void SetD51S54Asset(string value)
    {
        _D51S54Asset = value;
    }
    
    // Get<>AsString()
    public string GetD51S54AssetAsString()
    {
        return _D51S54Asset.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD51S54AssetAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51S54Asset = value;
    }
    
    // Standard Getter
    public D51DateOfIssue GetD51DateOfIssue()
    {
        return _D51DateOfIssue;
    }
    
    // Standard Setter
    public void SetD51DateOfIssue(D51DateOfIssue value)
    {
        _D51DateOfIssue = value;
    }
    
    // Get<>AsString()
    public string GetD51DateOfIssueAsString()
    {
        return _D51DateOfIssue != null ? _D51DateOfIssue.GetD51DateOfIssueAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD51DateOfIssueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D51DateOfIssue == null)
        {
            _D51DateOfIssue = new D51DateOfIssue();
        }
        _D51DateOfIssue.SetD51DateOfIssueAsString(value);
    }
    
    // Standard Getter
    public string GetD51IndexFromIssue()
    {
        return _D51IndexFromIssue;
    }
    
    // Standard Setter
    public void SetD51IndexFromIssue(string value)
    {
        _D51IndexFromIssue = value;
    }
    
    // Get<>AsString()
    public string GetD51IndexFromIssueAsString()
    {
        return _D51IndexFromIssue.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD51IndexFromIssueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51IndexFromIssue = value;
    }
    
    // Standard Getter
    public string GetD51LrBasis()
    {
        return _D51LrBasis;
    }
    
    // Standard Setter
    public void SetD51LrBasis(string value)
    {
        _D51LrBasis = value;
    }
    
    // Get<>AsString()
    public string GetD51LrBasisAsString()
    {
        return _D51LrBasis.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD51LrBasisAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51LrBasis = value;
    }
    
    // Standard Getter
    public D51MaturityDate GetD51MaturityDate()
    {
        return _D51MaturityDate;
    }
    
    // Standard Setter
    public void SetD51MaturityDate(D51MaturityDate value)
    {
        _D51MaturityDate = value;
    }
    
    // Get<>AsString()
    public string GetD51MaturityDateAsString()
    {
        return _D51MaturityDate != null ? _D51MaturityDate.GetD51MaturityDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD51MaturityDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D51MaturityDate == null)
        {
            _D51MaturityDate = new D51MaturityDate();
        }
        _D51MaturityDate.SetD51MaturityDateAsString(value);
    }
    
    // Standard Getter
    public decimal GetD51ParValue()
    {
        return _D51ParValue;
    }
    
    // Standard Setter
    public void SetD51ParValue(decimal value)
    {
        _D51ParValue = value;
    }
    
    // Get<>AsString()
    public string GetD51ParValueAsString()
    {
        return _D51ParValue.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD51ParValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D51ParValue = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD51DateOfIssue(string value)
    {
        _D51DateOfIssue.SetD51DateOfIssueAsString(value);
    }
    // Nested Class: D51DateOfIssue
    public class D51DateOfIssue
    {
        private static int _size = 0;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D51DateOfIssueYy, is_external=, is_static_class=False, static_prefix=
        private string _D51DateOfIssueYy ="";
        
        
        
        
        // [DEBUG] Field: D51DateOfIssueMm, is_external=, is_static_class=False, static_prefix=
        private string _D51DateOfIssueMm ="";
        
        
        
        
        // [DEBUG] Field: D51DateOfIssueDd, is_external=, is_static_class=False, static_prefix=
        private string _D51DateOfIssueDd ="";
        
        
        
        
    public D51DateOfIssue() {}
    
    public D51DateOfIssue(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD51DateOfIssueYy(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD51DateOfIssueMm(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD51DateOfIssueDd(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD51DateOfIssueAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D51DateOfIssueYy.PadRight(0));
        result.Append(_D51DateOfIssueMm.PadRight(0));
        result.Append(_D51DateOfIssueDd.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD51DateOfIssueAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD51DateOfIssueYy(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD51DateOfIssueMm(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD51DateOfIssueDd(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD51DateOfIssueYy()
    {
        return _D51DateOfIssueYy;
    }
    
    // Standard Setter
    public void SetD51DateOfIssueYy(string value)
    {
        _D51DateOfIssueYy = value;
    }
    
    // Get<>AsString()
    public string GetD51DateOfIssueYyAsString()
    {
        return _D51DateOfIssueYy.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD51DateOfIssueYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51DateOfIssueYy = value;
    }
    
    // Standard Getter
    public string GetD51DateOfIssueMm()
    {
        return _D51DateOfIssueMm;
    }
    
    // Standard Setter
    public void SetD51DateOfIssueMm(string value)
    {
        _D51DateOfIssueMm = value;
    }
    
    // Get<>AsString()
    public string GetD51DateOfIssueMmAsString()
    {
        return _D51DateOfIssueMm.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD51DateOfIssueMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51DateOfIssueMm = value;
    }
    
    // Standard Getter
    public string GetD51DateOfIssueDd()
    {
        return _D51DateOfIssueDd;
    }
    
    // Standard Setter
    public void SetD51DateOfIssueDd(string value)
    {
        _D51DateOfIssueDd = value;
    }
    
    // Get<>AsString()
    public string GetD51DateOfIssueDdAsString()
    {
        return _D51DateOfIssueDd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD51DateOfIssueDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D51DateOfIssueDd = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetD51MaturityDate(string value)
{
    _D51MaturityDate.SetD51MaturityDateAsString(value);
}
// Nested Class: D51MaturityDate
public class D51MaturityDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D51MaturityDateCc, is_external=, is_static_class=False, static_prefix=
    private string _D51MaturityDateCc ="";
    
    
    
    
    // [DEBUG] Field: D51MaturityDateYy, is_external=, is_static_class=False, static_prefix=
    private string _D51MaturityDateYy ="";
    
    
    
    
    // [DEBUG] Field: D51MaturityDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D51MaturityDateMm ="";
    
    
    
    
    // [DEBUG] Field: D51MaturityDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D51MaturityDateDd ="";
    
    
    
    
public D51MaturityDate() {}

public D51MaturityDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD51MaturityDateCc(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD51MaturityDateYy(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD51MaturityDateMm(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD51MaturityDateDd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD51MaturityDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D51MaturityDateCc.PadRight(0));
    result.Append(_D51MaturityDateYy.PadRight(0));
    result.Append(_D51MaturityDateMm.PadRight(0));
    result.Append(_D51MaturityDateDd.PadRight(0));
    
    return result.ToString();
}

public void SetD51MaturityDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD51MaturityDateCc(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD51MaturityDateYy(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD51MaturityDateMm(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD51MaturityDateDd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD51MaturityDateCc()
{
    return _D51MaturityDateCc;
}

// Standard Setter
public void SetD51MaturityDateCc(string value)
{
    _D51MaturityDateCc = value;
}

// Get<>AsString()
public string GetD51MaturityDateCcAsString()
{
    return _D51MaturityDateCc.PadRight(0);
}

// Set<>AsString()
public void SetD51MaturityDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D51MaturityDateCc = value;
}

// Standard Getter
public string GetD51MaturityDateYy()
{
    return _D51MaturityDateYy;
}

// Standard Setter
public void SetD51MaturityDateYy(string value)
{
    _D51MaturityDateYy = value;
}

// Get<>AsString()
public string GetD51MaturityDateYyAsString()
{
    return _D51MaturityDateYy.PadRight(0);
}

// Set<>AsString()
public void SetD51MaturityDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D51MaturityDateYy = value;
}

// Standard Getter
public string GetD51MaturityDateMm()
{
    return _D51MaturityDateMm;
}

// Standard Setter
public void SetD51MaturityDateMm(string value)
{
    _D51MaturityDateMm = value;
}

// Get<>AsString()
public string GetD51MaturityDateMmAsString()
{
    return _D51MaturityDateMm.PadRight(0);
}

// Set<>AsString()
public void SetD51MaturityDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D51MaturityDateMm = value;
}

// Standard Getter
public string GetD51MaturityDateDd()
{
    return _D51MaturityDateDd;
}

// Standard Setter
public void SetD51MaturityDateDd(string value)
{
    _D51MaturityDateDd = value;
}

// Get<>AsString()
public string GetD51MaturityDateDdAsString()
{
    return _D51MaturityDateDd.PadRight(0);
}

// Set<>AsString()
public void SetD51MaturityDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D51MaturityDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}

}}