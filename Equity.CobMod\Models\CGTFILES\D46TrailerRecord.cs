using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D46TrailerRecord Data Structure

public class D46TrailerRecord
{
    private static int _size = 192;
    // [DEBUG] Class: D46TrailerRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler73, is_external=, is_static_class=False, static_prefix=
    private string _Filler73 ="";
    
    
    // 88-level condition checks for Filler73
    public bool IsTrailerFound()
    {
        if (this._Filler73 == "'END 9999999INVESTOR+ '") return true;
        return false;
    }
    
    
    // [DEBUG] Field: Filler74, is_external=, is_static_class=False, static_prefix=
    private string _Filler74 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD46TrailerRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler73.PadRight(21));
        result.Append(_Filler74.PadRight(171));
        
        return result.ToString();
    }
    
    public void SetD46TrailerRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 21 <= data.Length)
        {
            string extracted = data.Substring(offset, 21).Trim();
            SetFiller73(extracted);
        }
        offset += 21;
        if (offset + 171 <= data.Length)
        {
            string extracted = data.Substring(offset, 171).Trim();
            SetFiller74(extracted);
        }
        offset += 171;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD46TrailerRecordAsString();
    }
    // Set<>String Override function
    public void SetD46TrailerRecord(string value)
    {
        SetD46TrailerRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller73()
    {
        return _Filler73;
    }
    
    // Standard Setter
    public void SetFiller73(string value)
    {
        _Filler73 = value;
    }
    
    // Get<>AsString()
    public string GetFiller73AsString()
    {
        return _Filler73.PadRight(21);
    }
    
    // Set<>AsString()
    public void SetFiller73AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler73 = value;
    }
    
    // Standard Getter
    public string GetFiller74()
    {
        return _Filler74;
    }
    
    // Standard Setter
    public void SetFiller74(string value)
    {
        _Filler74 = value;
    }
    
    // Get<>AsString()
    public string GetFiller74AsString()
    {
        return _Filler74.PadRight(171);
    }
    
    // Set<>AsString()
    public void SetFiller74AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler74 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}