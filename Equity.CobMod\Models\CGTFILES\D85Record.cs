using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D85Record Data Structure

public class D85Record
{
    private static int _size = 165;
    // [DEBUG] Class: D85Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D85PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D85PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D85ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D85ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD85RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D85PrintControl.PadRight(1));
        result.Append(_D85ReportLine.PadRight(164));
        
        return result.ToString();
    }
    
    public void SetD85RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD85PrintControl(extracted);
        }
        offset += 1;
        if (offset + 164 <= data.Length)
        {
            string extracted = data.Substring(offset, 164).Trim();
            SetD85ReportLine(extracted);
        }
        offset += 164;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD85RecordAsString();
    }
    // Set<>String Override function
    public void SetD85Record(string value)
    {
        SetD85RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD85PrintControl()
    {
        return _D85PrintControl;
    }
    
    // Standard Setter
    public void SetD85PrintControl(string value)
    {
        _D85PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD85PrintControlAsString()
    {
        return _D85PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD85PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D85PrintControl = value;
    }
    
    // Standard Getter
    public string GetD85ReportLine()
    {
        return _D85ReportLine;
    }
    
    // Standard Setter
    public void SetD85ReportLine(string value)
    {
        _D85ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD85ReportLineAsString()
    {
        return _D85ReportLine.PadRight(164);
    }
    
    // Set<>AsString()
    public void SetD85ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D85ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}