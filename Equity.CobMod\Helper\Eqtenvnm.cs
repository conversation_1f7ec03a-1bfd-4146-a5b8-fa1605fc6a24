﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using EquityProject.EqtenvnmDTO;

namespace EquityProject.EqtenvnmPGM
{
    public class Eqtenvnm
    {
        /// <summary>
        /// Retrieves the value of a specified environment variable.
        /// Corresponds to the PROCEDURE DIVISION of the COBOL EQTENVNM program.
        /// </summary>
        /// <param name="parameters">
        /// An instance of <see cref="Eqtenvnm"/> containing
        /// the environment variable name as input and receiving the value and return code as output.
        /// </param>
        public static void GetEnvironmentVariable(EqtenvnmLinkage parameters)
        {
            // COBOL: MOVE SPACES TO EQTENVNM-VALUE
            // COBOL: MOVE ZERO TO EQTENVNM-RETURN-CODE
            // These are handled by the DTO's constructor, ensuring initial state.
            // If the DTO is reused, these lines would be needed here:
            parameters.Value = new string(' ', 128);
            parameters.ReturnCode = 0;

            // COBOL: INSPECT EQTENVNM-NAME REPLACING ALL SPACES BY LOW-VALUES.
            // COBOL: DISPLAY EQTENVNM-NAME UPON ENVIRONMENT-NAME
            // In COBOL, this prepares the name for lookup.
            // In C#, we need to get a clean string for Environment.GetEnvironmentVariable.
            // COBOL's PIC X(8) and INSPECT might mean trailing spaces or nulls.
            // TrimEnd removes trailing spaces and null characters (LOW-VALUES).
            string variableName = parameters.Name?.TrimEnd(' ', '\0') ?? string.Empty;

            if (string.IsNullOrEmpty(variableName))
            {
                // If the name is empty after trimming, it's an invalid request.
                parameters.ReturnCode = 9;
                return; // Equivalent to EXIT PROGRAM
            }

            try
            {
                // COBOL: ACCEPT EQTENVNM-VALUE FROM ENVIRONMENT-VALUE
                string envValue = Environment.GetEnvironmentVariable(variableName);

                if (envValue == null)
                {
                    // COBOL: ON EXCEPTION MOVE 9 TO EQTENVNM-RETURN-CODE.
                    // Environment variable not found.
                    parameters.ReturnCode = 9;
                    // Value is already initialized to spaces by the DTO constructor/reset above.
                }
                else
                {
                    // Success
                    parameters.ReturnCode = 0;

                    // Populate EQTENVNM-VALUE, handling fixed length (PIC X(128))
                    if (envValue.Length > 128)
                    {
                        // Truncate if the value is longer than 128 characters
                        parameters.Value = envValue.Substring(0, 128);
                    }
                    else
                    {
                        // Pad with spaces if the value is shorter than 128 characters
                        parameters.Value = envValue.PadRight(128, ' ');
                    }
                }
            }
            catch (System.Security.SecurityException)
            {
                // Catch specific security exceptions if permissions are denied to read environment variables.
                parameters.ReturnCode = 9;
                // Value is already initialized to spaces.
            }
            catch (Exception ex)
            {
                // Catch any other unexpected exceptions during environment variable access.
                // Log the exception for debugging in a real application.
                Console.Error.WriteLine($"An unexpected error occurred: {ex.Message}");
                parameters.ReturnCode = 9;
                // Value is already initialized to spaces.
            }

            // COBOL: EXIT PROGRAM.
            // The method simply returns.
        }
    }
}
