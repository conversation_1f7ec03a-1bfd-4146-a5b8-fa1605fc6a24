using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D83File Data Structure

public class D83File
{
    private static int _size = 128;
    // [DEBUG] Class: D83File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler257, is_external=, is_static_class=False, static_prefix=
    private string _Filler257 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD83FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler257.PadRight(128));
        
        return result.ToString();
    }
    
    public void SetD83FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 128 <= data.Length)
        {
            string extracted = data.Substring(offset, 128).Trim();
            SetFiller257(extracted);
        }
        offset += 128;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD83FileAsString();
    }
    // Set<>String Override function
    public void SetD83File(string value)
    {
        SetD83FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller257()
    {
        return _Filler257;
    }
    
    // Standard Setter
    public void SetFiller257(string value)
    {
        _Filler257 = value;
    }
    
    // Get<>AsString()
    public string GetFiller257AsString()
    {
        return _Filler257.PadRight(128);
    }
    
    // Set<>AsString()
    public void SetFiller257AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler257 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}