using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D95Record Data Structure

public class D95Record
{
    private static int _size = 171;
    // [DEBUG] Class: D95Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D95PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D95PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D95ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D95ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD95RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D95PrintControl.PadRight(1));
        result.Append(_D95ReportLine.PadRight(170));
        
        return result.ToString();
    }
    
    public void SetD95RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD95PrintControl(extracted);
        }
        offset += 1;
        if (offset + 170 <= data.Length)
        {
            string extracted = data.Substring(offset, 170).Trim();
            SetD95ReportLine(extracted);
        }
        offset += 170;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD95RecordAsString();
    }
    // Set<>String Override function
    public void SetD95Record(string value)
    {
        SetD95RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD95PrintControl()
    {
        return _D95PrintControl;
    }
    
    // Standard Setter
    public void SetD95PrintControl(string value)
    {
        _D95PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD95PrintControlAsString()
    {
        return _D95PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD95PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D95PrintControl = value;
    }
    
    // Standard Getter
    public string GetD95ReportLine()
    {
        return _D95ReportLine;
    }
    
    // Standard Setter
    public void SetD95ReportLine(string value)
    {
        _D95ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD95ReportLineAsString()
    {
        return _D95ReportLine.PadRight(170);
    }
    
    // Set<>AsString()
    public void SetD95ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D95ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}