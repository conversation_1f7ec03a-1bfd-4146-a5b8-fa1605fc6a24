﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using EquityProject.CommonDTO;
using EquityProject.EqtdebugDTO;
using EquityProject.EquityGlobalParmsDTO;

namespace EquityProject.EqtdebugPGM
{
    public class Eqtdebug
    {
        // --- Working-Storage Section Variables ---
        // W-PROGRAM-NAME is implicitly handled by the class name or could be a const
        private const string W_PROGRAM_NAME = "EQTDEBUG";
        private int wLinesDisplayed = 0;
        private int wNewLines = 0;
        private int wFillLines = 0;
        // W-SUB is not used in the provided COBOL snippet
        // private int wSub = 0; 
        // char is used temporarily by Get-Character call, Console.ReadKey handles this
        // private char charx; 

        // 78 Get-Character VALUE X"83". - Handled by Console.ReadKey
        private const int MAX_LINES = 24;

        // Declare Eqtdebug Class private variables for Gvar and Ivar instances
        // These are usually populated by the calling mechanism/framework
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();

        // --- Implicit COPY members ---
        // COPY LINKAGE1.COB -> represented by _gvar.GetCommonLinkage()
        // COPY EQTGPARM.EXT -> represented by _gvar.GetEquityGlobalParms() 
        //                      (Assuming EquityGlobalParms is added to Gvar or accessible via it)
        // COPY EQTDEBUG.COB -> represented by _ivar.GetEqtdebugLinkage()

        // --- Adding EquityGlobalParms to Gvar for this context ---
        // NOTE: This assumes Gvar is the appropriate place to hold these global params.
        // If EquityGlobalParms is managed differently in the actual framework, adjust accordingly.
        // Ideally, Gvar.cs should be updated, but modifying it here for demonstration.
        private EquityGlobalParms _equityGlobalParms = new EquityGlobalParms();
        
        // Constructor
        public Eqtdebug() 
        {
             // Initialize Gvar, Ivar or EquityGlobalParms if necessary,
             // but typically they are set externally before Run is called.
        }

        // Make EquityGlobalParms accessible, mirroring how Gvar/Ivar might be accessed
        public EquityGlobalParms GetEquityGlobalParms() { return _equityGlobalParms; }
        public void SetEquityGlobalParms(EquityGlobalParms value) { _equityGlobalParms = value; }

        // Declare Eqtdebug Class getters setters (as provided in the template)
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }


        // Main logic method corresponding to PROCEDURE DIVISION
        // Updated Run signature to potentially accept initialized gvar/ivar if needed,
        // otherwise it uses the internal _gvar, _ivar.
        public void Run(Gvar gvar, Ivar ivar, EquityGlobalParms equityGlobalParms)
        {
            // Use the passed-in instances
            this._gvar = gvar;
            this._ivar = ivar;
            this._equityGlobalParms = equityGlobalParms; // Use passed-in global params

            // Access Linkage Section data
            EqtdebugLinkage eqtdebugLinkage = ivar.GetEqtdebugLinkage();
            EqtdebugLinkage.EqtdebugText eqtdebugText = eqtdebugLinkage.GetEqtdebugText(); // Convenience

            // Access Global Parameters data (assuming it's available via _equityGlobalParms)
            EquityGlobalParms.GGeneralParms generalParms = _equityGlobalParms.GetGGeneralParms();

            // Access Common Linkage data (assuming it's available via _gvar)
            CommonLinkage commonLinkage = gvar.GetCommonLinkage();


            // IF DISPLAY-MODE-PAUSE OR DISPLAY-MODE-SCROLL
            if (generalParms.IsDisplayModePause() || generalParms.IsDisplayModeScroll())
            {
                wNewLines = 0; // Reset counter for this run

                // See how many lines need to be displayed
                // IF NOT EQTDEBUG-TEXT-LINE1-EMPTY
                if (!string.IsNullOrEmpty(eqtdebugText.GetEqtdebugTextLine1()))
                {
                    wNewLines = 1;
                    // IF NOT EQTDEBUG-TEXT-LINE2-EMPTY
                    if (!string.IsNullOrEmpty(eqtdebugText.GetEqtdebugTextLine2()))
                    {
                        wNewLines++;
                        // IF NOT EQTDEBUG-TEXT-LINE3-EMPTY
                        if (!string.IsNullOrEmpty(eqtdebugText.GetEqtdebugTextLine3()))
                        {
                            wNewLines++;
                        }
                    }
                }

                // IF DISPLAY-MODE-PAUSE
                if (generalParms.IsDisplayModePause())
                {
                    // IF W-NEW-LINES > 0 AND W-LINES-DISPLAYED + W-NEW-LINES > MAX-LINES - 1
                    if (wNewLines > 0 && wLinesDisplayed + wNewLines > MAX_LINES - 1)
                    {
                        // COMPUTE W-FILL-LINES = MAX-LINES - 1 - W-LINES-DISPLAYED
                        wFillLines = MAX_LINES - 1 - wLinesDisplayed;

                        // PERFORM W-FILL-LINES TIMES
                        for (int i = 0; i < wFillLines; i++)
                        {
                            // DISPLAY ' '
                            Console.WriteLine(" ");
                        }

                        // DISPLAY '*** press any key to continue ***'
                        Console.WriteLine("*** press any key to continue ***");

                        // call Get-Character using char
                        Console.ReadKey(true); // true = intercept, don't display key

                        // MOVE 0 TO W-LINES-DISPLAYED
                        wLinesDisplayed = 0;
                    }
                }

                // Now display the required number of lines
                // Simplified logic: Display lines if they exist up to wNewLines count
                if (wNewLines >= 1)
                {
                    Console.WriteLine(eqtdebugText.GetEqtdebugTextLine1());
                    wLinesDisplayed++;
                }
                if (wNewLines >= 2)
                {
                    Console.WriteLine(eqtdebugText.GetEqtdebugTextLine2());
                    wLinesDisplayed++;
                }
                if (wNewLines >= 3)
                {
                    Console.WriteLine(eqtdebugText.GetEqtdebugTextLine3());
                    wLinesDisplayed++;
                }

                // Original COBOL structure was slightly different but achieved the same result
                /* 
                // IF W-NEW-LINES = 1
                if (wNewLines == 1)
                {
                    Console.WriteLine(eqtdebugText.GetEqtdebugTextLine1());
                    wLinesDisplayed++;
                    // IF W-NEW-LINES = 2 (This nested IF is logically incorrect in COBOL - unreachable)
                    // Corrected logic assumes sequential display if lines exist
                }
                else if (wNewLines == 2) // Corrected logic
                {
                     Console.WriteLine(eqtdebugText.GetEqtdebugTextLine1());
                     wLinesDisplayed++;
                     Console.WriteLine(eqtdebugText.GetEqtdebugTextLine2());
                     wLinesDisplayed++;
                }
                 else if (wNewLines == 3) // Corrected logic
                {
                     Console.WriteLine(eqtdebugText.GetEqtdebugTextLine1());
                     wLinesDisplayed++;
                     Console.WriteLine(eqtdebugText.GetEqtdebugTextLine2());
                     wLinesDisplayed++;
                     Console.WriteLine(eqtdebugText.GetEqtdebugTextLine3());
                     wLinesDisplayed++;
                }
                */

            }
            // ELSE IF LOG-MODE
            else if (generalParms.IsLogMode())
            {
                // MOVE G-USER-NO TO L-USER-NO
                try
                {
                    // G-USER-NO is string, L-USER-NO is int
                    if (int.TryParse(generalParms.GetGUserNo().Trim(), out int userNo))
                    {
                       commonLinkage.SetLUserNo(userNo);
                    }
                    else
                    {
                       // Handle error: G-USER-NO is not a valid integer
                       Console.Error.WriteLine($"Error: Could not parse G-USER-NO '{generalParms.GetGUserNo()}' to integer.");
                       commonLinkage.SetLUserNo(0); // Set to default or handle as error
                    }
                }
                catch (Exception ex)
                {
                     // Handle potential exceptions during parsing or setting
                     Console.Error.WriteLine($"Error processing G-USER-NO: {ex.Message}");
                     commonLinkage.SetLUserNo(0); // Set to default or handle as error
                }


                // CALL 'TRACEPGM' USING EQTDEBUG-LINKAGE COMMON-LINKAGE
                // Assuming TRACEPGM is another class/method available
                // Replace TracePgm.Call with the actual call mechanism
                 TracePgm.Call(eqtdebugLinkage, commonLinkage);
            }

            // MOVE SPACES TO EQTDEBUG-TEXT.
            // Clear the input structure's text lines
            eqtdebugText.SetEqtdebugTextLine1(" ");
            eqtdebugText.SetEqtdebugTextLine2(" ");
            eqtdebugText.SetEqtdebugTextLine3(" ");

            // EXIT PROGRAM. (Implicit return)
            // STOP RUN. (Handled by the application's lifecycle)
        }

        // Placeholder for the external call
        // The actual implementation depends on how TRACEPGM is exposed
        public static class TracePgm
        {
            public static void Call(EqtdebugLinkage debugLinkage, CommonLinkage commonLinkage)
            {
                Console.WriteLine("--- TRACEPGM Called (Placeholder) ---");
                Console.WriteLine($"   User No: {commonLinkage.GetLUserNoAsString()}");
                Console.WriteLine($"   Line 1: {debugLinkage.GetEqtdebugText().GetEqtdebugTextLine1()}");
                Console.WriteLine($"   Line 2: {debugLinkage.GetEqtdebugText().GetEqtdebugTextLine2()}");
                Console.WriteLine($"   Line 3: {debugLinkage.GetEqtdebugText().GetEqtdebugTextLine3()}");
                Console.WriteLine("--- End TRACEPGM Call ---");
                // In a real scenario, this would likely involve:
                // 1. Instantiating a TracePgm class.
                // 2. Calling a method on it, passing the required linkage data.
                // Example:
                // var tracePgmInstance = new TracePgmProgram(); // Or however it's structured
                // tracePgmInstance.Run(debugLinkage, commonLinkage); 
            }
        }

        // --- Methods from Template (kept for consistency) ---

        // CallSub() method - Not used in this specific COBOL snippet
        public void CallSub(Ivar ivar)
        {
            _ivar.SetEqtdebugLinkageAsString(ivar.GetEqtdebugLinkageAsString());
            // Implement subroutine call logic here if needed
        }
    }
}
