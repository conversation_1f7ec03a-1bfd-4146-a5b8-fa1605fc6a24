using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtRestructureHoldings Data Structure

public class WtRestructureHoldings
{
    private static int _size = 176;
    // [DEBUG] Class: WtRestructureHoldings, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler479, is_external=, is_static_class=False, static_prefix=
    private string _Filler479 ="RESTRUCTURE-HOLDINGS TABLE======";
    
    
    
    
    // [DEBUG] Field: WtrshMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtrshMaxTableSize =2700;
    
    
    
    
    // [DEBUG] Field: WtrshOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtrshOccurs =0;
    
    
    
    
    // [DEBUG] Field: WtrshHoldingsTable, is_external=, is_static_class=False, static_prefix=
    private WtrshHoldingsTable _WtrshHoldingsTable = new WtrshHoldingsTable();
    
    
    
    
    // [DEBUG] Field: WtrshSaveElement, is_external=, is_static_class=False, static_prefix=
    private string _WtrshSaveElement ="";
    
    
    
    
    
    // Serialization methods
    public string GetWtRestructureHoldingsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler479.PadRight(32));
        result.Append(_WtrshMaxTableSize.ToString().PadLeft(4, '0'));
        result.Append(_WtrshOccurs.ToString().PadLeft(4, '0'));
        result.Append(_WtrshHoldingsTable.GetWtrshHoldingsTableAsString());
        result.Append(_WtrshSaveElement.PadRight(100));
        
        return result.ToString();
    }
    
    public void SetWtRestructureHoldingsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 32 <= data.Length)
        {
            string extracted = data.Substring(offset, 32).Trim();
            SetFiller479(extracted);
        }
        offset += 32;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtrshMaxTableSize(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtrshOccurs(parsedInt);
        }
        offset += 4;
        if (offset + 36 <= data.Length)
        {
            _WtrshHoldingsTable.SetWtrshHoldingsTableAsString(data.Substring(offset, 36));
        }
        else
        {
            _WtrshHoldingsTable.SetWtrshHoldingsTableAsString(data.Substring(offset));
        }
        offset += 36;
        if (offset + 100 <= data.Length)
        {
            string extracted = data.Substring(offset, 100).Trim();
            SetWtrshSaveElement(extracted);
        }
        offset += 100;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtRestructureHoldingsAsString();
    }
    // Set<>String Override function
    public void SetWtRestructureHoldings(string value)
    {
        SetWtRestructureHoldingsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller479()
    {
        return _Filler479;
    }
    
    // Standard Setter
    public void SetFiller479(string value)
    {
        _Filler479 = value;
    }
    
    // Get<>AsString()
    public string GetFiller479AsString()
    {
        return _Filler479.PadRight(32);
    }
    
    // Set<>AsString()
    public void SetFiller479AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler479 = value;
    }
    
    // Standard Getter
    public int GetWtrshMaxTableSize()
    {
        return _WtrshMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtrshMaxTableSize(int value)
    {
        _WtrshMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtrshMaxTableSizeAsString()
    {
        return _WtrshMaxTableSize.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtrshMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtrshMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtrshOccurs()
    {
        return _WtrshOccurs;
    }
    
    // Standard Setter
    public void SetWtrshOccurs(int value)
    {
        _WtrshOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtrshOccursAsString()
    {
        return _WtrshOccurs.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtrshOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtrshOccurs = parsed;
    }
    
    // Standard Getter
    public WtrshHoldingsTable GetWtrshHoldingsTable()
    {
        return _WtrshHoldingsTable;
    }
    
    // Standard Setter
    public void SetWtrshHoldingsTable(WtrshHoldingsTable value)
    {
        _WtrshHoldingsTable = value;
    }
    
    // Get<>AsString()
    public string GetWtrshHoldingsTableAsString()
    {
        return _WtrshHoldingsTable != null ? _WtrshHoldingsTable.GetWtrshHoldingsTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtrshHoldingsTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtrshHoldingsTable == null)
        {
            _WtrshHoldingsTable = new WtrshHoldingsTable();
        }
        _WtrshHoldingsTable.SetWtrshHoldingsTableAsString(value);
    }
    
    // Standard Getter
    public string GetWtrshSaveElement()
    {
        return _WtrshSaveElement;
    }
    
    // Standard Setter
    public void SetWtrshSaveElement(string value)
    {
        _WtrshSaveElement = value;
    }
    
    // Get<>AsString()
    public string GetWtrshSaveElementAsString()
    {
        return _WtrshSaveElement.PadRight(100);
    }
    
    // Set<>AsString()
    public void SetWtrshSaveElementAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtrshSaveElement = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtrshHoldingsTable(string value)
    {
        _WtrshHoldingsTable.SetWtrshHoldingsTableAsString(value);
    }
    // Nested Class: WtrshHoldingsTable
    public class WtrshHoldingsTable
    {
        private static int _size = 36;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtrshElement, is_external=, is_static_class=False, static_prefix=
        private WtrshHoldingsTable.WtrshElement[] _WtrshElement = new WtrshHoldingsTable.WtrshElement[2700];
        
        public void InitializeWtrshElementArray()
        {
            for (int i = 0; i < 2700; i++)
            {
                _WtrshElement[i] = new WtrshHoldingsTable.WtrshElement();
            }
        }
        
        
        
    public WtrshHoldingsTable() {}
    
    public WtrshHoldingsTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWtrshElementArray();
        for (int i = 0; i < 2700; i++)
        {
            _WtrshElement[i].SetWtrshElementAsString(data.Substring(offset, 36));
            offset += 36;
        }
        
    }
    
    // Serialization methods
    public string GetWtrshHoldingsTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 2700; i++)
        {
            result.Append(_WtrshElement[i].GetWtrshElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtrshHoldingsTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 2700; i++)
        {
            if (offset + 36 > data.Length) break;
            string val = data.Substring(offset, 36);
            
            _WtrshElement[i].SetWtrshElementAsString(val);
            offset += 36;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtrshElement
    public WtrshElement GetWtrshElementAt(int index)
    {
        return _WtrshElement[index];
    }
    
    public void SetWtrshElementAt(int index, WtrshElement value)
    {
        _WtrshElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtrshElement GetWtrshElement()
    {
        return _WtrshElement != null && _WtrshElement.Length > 0
        ? _WtrshElement[0]
        : new WtrshElement();
    }
    
    public void SetWtrshElement(WtrshElement value)
    {
        if (_WtrshElement == null || _WtrshElement.Length == 0)
        _WtrshElement = new WtrshElement[1];
        _WtrshElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtrshElement
    public class WtrshElement
    {
        private static int _size = 36;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtrshKey, is_external=, is_static_class=False, static_prefix=
        private WtrshElement.WtrshKey _WtrshKey = new WtrshElement.WtrshKey();
        
        
        
        
        // [DEBUG] Field: WtrshDetails, is_external=, is_static_class=False, static_prefix=
        private WtrshElement.WtrshDetails _WtrshDetails = new WtrshElement.WtrshDetails();
        
        
        
        
    public WtrshElement() {}
    
    public WtrshElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WtrshKey.SetWtrshKeyAsString(data.Substring(offset, WtrshKey.GetSize()));
        offset += 19;
        _WtrshDetails.SetWtrshDetailsAsString(data.Substring(offset, WtrshDetails.GetSize()));
        offset += 17;
        
    }
    
    // Serialization methods
    public string GetWtrshElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtrshKey.GetWtrshKeyAsString());
        result.Append(_WtrshDetails.GetWtrshDetailsAsString());
        
        return result.ToString();
    }
    
    public void SetWtrshElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 19 <= data.Length)
        {
            _WtrshKey.SetWtrshKeyAsString(data.Substring(offset, 19));
        }
        else
        {
            _WtrshKey.SetWtrshKeyAsString(data.Substring(offset));
        }
        offset += 19;
        if (offset + 17 <= data.Length)
        {
            _WtrshDetails.SetWtrshDetailsAsString(data.Substring(offset, 17));
        }
        else
        {
            _WtrshDetails.SetWtrshDetailsAsString(data.Substring(offset));
        }
        offset += 17;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WtrshKey GetWtrshKey()
    {
        return _WtrshKey;
    }
    
    // Standard Setter
    public void SetWtrshKey(WtrshKey value)
    {
        _WtrshKey = value;
    }
    
    // Get<>AsString()
    public string GetWtrshKeyAsString()
    {
        return _WtrshKey != null ? _WtrshKey.GetWtrshKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtrshKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtrshKey == null)
        {
            _WtrshKey = new WtrshKey();
        }
        _WtrshKey.SetWtrshKeyAsString(value);
    }
    
    // Standard Getter
    public WtrshDetails GetWtrshDetails()
    {
        return _WtrshDetails;
    }
    
    // Standard Setter
    public void SetWtrshDetails(WtrshDetails value)
    {
        _WtrshDetails = value;
    }
    
    // Get<>AsString()
    public string GetWtrshDetailsAsString()
    {
        return _WtrshDetails != null ? _WtrshDetails.GetWtrshDetailsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtrshDetailsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtrshDetails == null)
        {
            _WtrshDetails = new WtrshDetails();
        }
        _WtrshDetails.SetWtrshDetailsAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtrshKey
    public class WtrshKey
    {
        private static int _size = 19;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtrshDate, is_external=, is_static_class=False, static_prefix=
        private string _WtrshDate ="";
        
        
        
        
        // [DEBUG] Field: WtrshCalSedol, is_external=, is_static_class=False, static_prefix=
        private WtrshKey.WtrshCalSedol _WtrshCalSedol = new WtrshKey.WtrshCalSedol();
        
        
        
        
    public WtrshKey() {}
    
    public WtrshKey(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtrshDate(data.Substring(offset, 8).Trim());
        offset += 8;
        _WtrshCalSedol.SetWtrshCalSedolAsString(data.Substring(offset, WtrshCalSedol.GetSize()));
        offset += 11;
        
    }
    
    // Serialization methods
    public string GetWtrshKeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtrshDate.PadRight(8));
        result.Append(_WtrshCalSedol.GetWtrshCalSedolAsString());
        
        return result.ToString();
    }
    
    public void SetWtrshKeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWtrshDate(extracted);
        }
        offset += 8;
        if (offset + 11 <= data.Length)
        {
            _WtrshCalSedol.SetWtrshCalSedolAsString(data.Substring(offset, 11));
        }
        else
        {
            _WtrshCalSedol.SetWtrshCalSedolAsString(data.Substring(offset));
        }
        offset += 11;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtrshDate()
    {
        return _WtrshDate;
    }
    
    // Standard Setter
    public void SetWtrshDate(string value)
    {
        _WtrshDate = value;
    }
    
    // Get<>AsString()
    public string GetWtrshDateAsString()
    {
        return _WtrshDate.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWtrshDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtrshDate = value;
    }
    
    // Standard Getter
    public WtrshCalSedol GetWtrshCalSedol()
    {
        return _WtrshCalSedol;
    }
    
    // Standard Setter
    public void SetWtrshCalSedol(WtrshCalSedol value)
    {
        _WtrshCalSedol = value;
    }
    
    // Get<>AsString()
    public string GetWtrshCalSedolAsString()
    {
        return _WtrshCalSedol != null ? _WtrshCalSedol.GetWtrshCalSedolAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtrshCalSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtrshCalSedol == null)
        {
            _WtrshCalSedol = new WtrshCalSedol();
        }
        _WtrshCalSedol.SetWtrshCalSedolAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtrshCalSedol
    public class WtrshCalSedol
    {
        private static int _size = 11;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtrshFundCode, is_external=, is_static_class=False, static_prefix=
        private string _WtrshFundCode ="";
        
        
        
        
        // [DEBUG] Field: WtrshSedolCode, is_external=, is_static_class=False, static_prefix=
        private string _WtrshSedolCode ="";
        
        
        
        
    public WtrshCalSedol() {}
    
    public WtrshCalSedol(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtrshFundCode(data.Substring(offset, 4).Trim());
        offset += 4;
        SetWtrshSedolCode(data.Substring(offset, 7).Trim());
        offset += 7;
        
    }
    
    // Serialization methods
    public string GetWtrshCalSedolAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtrshFundCode.PadRight(4));
        result.Append(_WtrshSedolCode.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetWtrshCalSedolAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtrshFundCode(extracted);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetWtrshSedolCode(extracted);
        }
        offset += 7;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtrshFundCode()
    {
        return _WtrshFundCode;
    }
    
    // Standard Setter
    public void SetWtrshFundCode(string value)
    {
        _WtrshFundCode = value;
    }
    
    // Get<>AsString()
    public string GetWtrshFundCodeAsString()
    {
        return _WtrshFundCode.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtrshFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtrshFundCode = value;
    }
    
    // Standard Getter
    public string GetWtrshSedolCode()
    {
        return _WtrshSedolCode;
    }
    
    // Standard Setter
    public void SetWtrshSedolCode(string value)
    {
        _WtrshSedolCode = value;
    }
    
    // Get<>AsString()
    public string GetWtrshSedolCodeAsString()
    {
        return _WtrshSedolCode.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetWtrshSedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtrshSedolCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
// Nested Class: WtrshDetails
public class WtrshDetails
{
    private static int _size = 17;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtrshAcquisitionsFlag, is_external=, is_static_class=False, static_prefix=
    private string _WtrshAcquisitionsFlag ="";
    
    
    // 88-level condition checks for WtrshAcquisitionsFlag
    public bool IsWtrshAcquisitionsPresent()
    {
        if (this._WtrshAcquisitionsFlag == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WtrshDisposalsFlag, is_external=, is_static_class=False, static_prefix=
    private string _WtrshDisposalsFlag ="";
    
    
    // 88-level condition checks for WtrshDisposalsFlag
    public bool IsWtrshDisposalsPresent()
    {
        if (this._WtrshDisposalsFlag == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WtrshWtsPointer, is_external=, is_static_class=False, static_prefix=
    private string _WtrshWtsPointer ="";
    
    
    
    
    // [DEBUG] Field: WtrshHoldingLevel, is_external=, is_static_class=False, static_prefix=
    private int _WtrshHoldingLevel =0;
    
    
    
    
    // [DEBUG] Field: WtrshEarliestBfDate, is_external=, is_static_class=False, static_prefix=
    private string _WtrshEarliestBfDate ="";
    
    
    // 88-level condition checks for WtrshEarliestBfDate
    public bool IsWtrshEarliestBfDateNotSet()
    {
        if (this._WtrshEarliestBfDate == "'99999999'") return true;
        return false;
    }
    
    
public WtrshDetails() {}

public WtrshDetails(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtrshAcquisitionsFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWtrshDisposalsFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWtrshWtsPointer(data.Substring(offset, 4).Trim());
    offset += 4;
    SetWtrshHoldingLevel(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetWtrshEarliestBfDate(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetWtrshDetailsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtrshAcquisitionsFlag.PadRight(1));
    result.Append(_WtrshDisposalsFlag.PadRight(1));
    result.Append(_WtrshWtsPointer.PadRight(4));
    result.Append(_WtrshHoldingLevel.ToString().PadLeft(3, '0'));
    result.Append(_WtrshEarliestBfDate.PadRight(8));
    
    return result.ToString();
}

public void SetWtrshDetailsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtrshAcquisitionsFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtrshDisposalsFlag(extracted);
    }
    offset += 1;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWtrshWtsPointer(extracted);
    }
    offset += 4;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtrshHoldingLevel(parsedInt);
    }
    offset += 3;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWtrshEarliestBfDate(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetWtrshAcquisitionsFlag()
{
    return _WtrshAcquisitionsFlag;
}

// Standard Setter
public void SetWtrshAcquisitionsFlag(string value)
{
    _WtrshAcquisitionsFlag = value;
}

// Get<>AsString()
public string GetWtrshAcquisitionsFlagAsString()
{
    return _WtrshAcquisitionsFlag.PadRight(1);
}

// Set<>AsString()
public void SetWtrshAcquisitionsFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtrshAcquisitionsFlag = value;
}

// Standard Getter
public string GetWtrshDisposalsFlag()
{
    return _WtrshDisposalsFlag;
}

// Standard Setter
public void SetWtrshDisposalsFlag(string value)
{
    _WtrshDisposalsFlag = value;
}

// Get<>AsString()
public string GetWtrshDisposalsFlagAsString()
{
    return _WtrshDisposalsFlag.PadRight(1);
}

// Set<>AsString()
public void SetWtrshDisposalsFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtrshDisposalsFlag = value;
}

// Standard Getter
public string GetWtrshWtsPointer()
{
    return _WtrshWtsPointer;
}

// Standard Setter
public void SetWtrshWtsPointer(string value)
{
    _WtrshWtsPointer = value;
}

// Get<>AsString()
public string GetWtrshWtsPointerAsString()
{
    return _WtrshWtsPointer.PadRight(4);
}

// Set<>AsString()
public void SetWtrshWtsPointerAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtrshWtsPointer = value;
}

// Standard Getter
public int GetWtrshHoldingLevel()
{
    return _WtrshHoldingLevel;
}

// Standard Setter
public void SetWtrshHoldingLevel(int value)
{
    _WtrshHoldingLevel = value;
}

// Get<>AsString()
public string GetWtrshHoldingLevelAsString()
{
    return _WtrshHoldingLevel.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetWtrshHoldingLevelAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtrshHoldingLevel = parsed;
}

// Standard Getter
public string GetWtrshEarliestBfDate()
{
    return _WtrshEarliestBfDate;
}

// Standard Setter
public void SetWtrshEarliestBfDate(string value)
{
    _WtrshEarliestBfDate = value;
}

// Get<>AsString()
public string GetWtrshEarliestBfDateAsString()
{
    return _WtrshEarliestBfDate.PadRight(8);
}

// Set<>AsString()
public void SetWtrshEarliestBfDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtrshEarliestBfDate = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}

}}
