using System;
using System.Text;
using EquityProject.CgtnameDTO;
namespace EquityProject.CgtnamePGM
{
    // Cgtname Class Definition

    //Cgtname Class Constructor
    public class Cgtname
    {
        // Declare Cgtname Class private variables
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();

        // Constructor
        public Cgtname()
        {
            // Initialize WWords array
            _gvar.GetWWords().InitializeWWordArray();
        }

        // Declare {program_name} Class getters setters
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        /// <summary>
        /// Helper method to call external subroutines
        /// </summary>
        /// <param name="ivar">The Ivar parameter to pass to the subroutine</param>
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
        }


        // Run() method
        public void Run(Gvar gvar, Ivar ivar)
        {
            // Call the main entry point
            Main(gvar, ivar);
        }

        // Methods representing paragraphs under procedure division
        /// <summary>
        /// This method performs initialization and conditional actions based on parameters.
        /// Then ends normal program execution.
        /// </summary>
        /// <param name="gvar">Global variables storage</param>
        /// <param name="ivar">Working data storage class</param>
        /// <remarks>
        /// Method mimics the functionality of the main paragraph in original COBOL code
        /// </remarks>
        ///
        /// </remarks>
        public void Main(Gvar gvar, Ivar ivar)
        {
            // Call A-Initialise paragraph
            AInitialise(gvar, ivar);

            // If CONVERT-TO-UPPER flag is true, call B-ConvertToUpper paragraph
            if (ivar.GetIlnameLinkage().GetIlnameParameters().IsConvertToUpper())
            {
                BConvertToUpper(gvar, ivar);
            }

            // If KEEP-CHARS-ONLY flag is true, call C-Translate paragraph
            if (ivar.GetIlnameLinkage().GetIlnameParameters().IsKeepCharsOnly())
            {
                CTranslate(gvar, ivar);
            }

            // If REMOVE-SPACES flag is true, call D-RemoveSpaces paragraph
            if (ivar.GetIlnameLinkage().GetIlnameParameters().IsRemoveSpaces())
            {
                // Call DRemoveSpaces paragraph
                DRemoveSpaces(gvar, ivar);
            }
            else
            {
                // Otherwise, move ILNAME-INPUT-STRING to ILNAME-OUTPUT-STRING
                // Copy the input string to output string
                ivar.GetIlnameLinkage().SetIlnameOutputStringAsString(
                    ivar.GetIlnameLinkage().GetIlnameInputStringAsString());
            }

            // END of program - EXIT PROGRAM, STOP RUN
            // Note: In C#, we simply return from the method
        }
        /// <summary>
        /// AInitialise paragraph performs initial validation and conversion on the ILNAME parameters.
        /// Initialisation of string sizes is capped at 200.  Transform lower case strings, converting to upper case.
        /// </summary>
        /// <remarks>
        /// Converts the COBOL paragraph 'AInitialise' to C# method 'AInitialise'.
        ///
        /// COBOL Paragraph: AInitialise
        ///
        /// REDEFINES parameters within gvar by substituting uppercase instances for lower case instances.
        /// Ensures that the string sizes are capped at 200.
        ///
        /// If ILNAME-INPUT-STRING-SIZE is 0 or greater than 200, it is set to 200.
        /// If ILNAME-KEEP-STRING-SIZE is greater than 200, it is set to 200.
        /// INSPECTS and converts the ILNAME-PARAMETERS string to upper case.
        /// </remarks>
        /// <param name="gvar">Globals data class for accessing global variables</param>
        /// <param name="ivar">Input variable data class for accessing input variables</param>
        public void AInitialise(Gvar gvar, Ivar ivar)
        {
            // COBOL: IF ILNAME-INPUT-STRING-SIZE = 0 OR > 200
            //        MOVE 200 TO ILNAME-INPUT-STRING-SIZE
            var ilnameInputStringSize = ivar.GetIlnameLinkage().GetIlnameInputStringSize();
            if (ilnameInputStringSize == 0 || ilnameInputStringSize > 200)
            {
                ivar.GetIlnameLinkage().SetIlnameInputStringSize(200);
            }

            // COBOL: IF ILNAME-KEEP-STRING-SIZE > 200
            //        MOVE 200 TO ILNAME-KEEP-STRING-SIZE
            var ilnameKeepStringSize = ivar.GetIlnameLinkage().GetIlnameKeepStringSize();
            if (ilnameKeepStringSize > 200)
            {
                ivar.GetIlnameLinkage().SetIlnameKeepStringSize(200);
            }

            // COBOL: INSPECT ILNAME-PARAMETERS
            //        CONVERTING 'abcdefghijklmnopqrstuvwxyz'
            //        TO 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
            var ilnameParameters = ivar.GetIlnameLinkage().GetIlnameParametersAsString();
            string lowerCase = "abcdefghijklmnopqrstuvwxyz";
            string upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

            // Convert each lowercase character to uppercase
            for (int i = 0; i < lowerCase.Length; i++)
            {
                ilnameParameters = ilnameParameters.Replace(lowerCase[i], upperCase[i]);
            }

            // Set the converted parameters back
            ivar.GetIlnameLinkage().SetIlnameParametersAsString(ilnameParameters);
        }
        /// <summary>
        ///  COBOL paragraph name: bConvertToUpper
        /// </summary>
        /// <remarks>
        /// This method implements the logic to convert characters of a string to uppercase
        /// while ignoring spaces. It iterates over each character of the string and converts it
        /// if it is not a space. The conversion is performed using the specified conversion
        /// table and inspect statement.
        /// </remarks>
        /// <param name="gvar">Global variables object containing the necessary variables.</param>
        /// <param name="ivar">Input variables object containing the data to be processed.</param>
        public void BConvertToUpper(Gvar gvar, Ivar ivar)
        {
            // COBOL: PERFORM VARYING W-SUB FROM 1 BY 1
            //        UNTIL W-SUB > ILNAME-INPUT-STRING-SIZE
            //        IF ILNAME-INPUT-STRING-CHAR (W-SUB) NOT = SPACES
            //           INSPECT ILNAME-INPUT-STRING-CHAR (W-SUB)
            //                   CONVERTING 'abcdefghijklmnopqrstuvwxyz'
            //                   TO 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
            //        END-IF
            //        END-PERFORM

            string lowerCase = "abcdefghijklmnopqrstuvwxyz";
            string upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

            for (int wSub = 1; wSub <= ivar.GetIlnameLinkage().GetIlnameInputStringSize(); wSub++)
            {
                // Get current character (COBOL arrays are 1-based, C# arrays are 0-based)
                string currentChar = ivar.GetIlnameLinkage().GetIlnameInputString().GetIlnameInputStringCharAt(wSub - 1);

                // Check if the current character is not a space
                if (currentChar != " ")
                {
                    // Convert lowercase to uppercase
                    for (int i = 0; i < lowerCase.Length; i++)
                    {
                        if (currentChar == lowerCase[i].ToString())
                        {
                            currentChar = upperCase[i].ToString();
                            break;
                        }
                    }

                    // Set the converted character back
                    ivar.GetIlnameLinkage().GetIlnameInputString().SetIlnameInputStringCharAt(wSub - 1, currentChar);
                }
            }
        }

        /// <summary>
        /// cTranslate - COBOL Paragraph
        /// </summary>
        /// <remarks>
        /// Original COBOL statements:
        /// MOVE      ILNAME-KEEP-STRING-SIZE   TO   W-SUB
        /// ADD       1                         TO   W-SUB
        /// PERFORM   VARYING   W-SUB   FROM   W-SUB   BY   1
        /// UNTIL   W-SUB   >   200
        /// MOVE   X'FF'   TO   ILNAME-KEEP-CHAR ( W-SUB )
        /// END-PERFORM.
        /// PERFORM   VARYING   W-SUB   FROM   1   BY   1
        /// UNTIL   W-SUB   >   ILNAME-INPUT-STRING-SIZE
        /// IF   ILNAME-INPUT-STRING-CHAR ( W-SUB )   NOT   =   SPACES
        /// SEARCH   ALL   ILNAME-KEEP-CHAR
        /// AT   END   MOVE   SPACES   TO
        /// ILNAME-INPUT-STRING-CHAR ( W-SUB )
        /// WHEN   ILNAME-KEEP-CHAR ( KEEP-INDEX )   =
        /// ILNAME-INPUT-STRING-CHAR ( W-SUB )
        /// IF   ILNAME-INPUT-STRING-CHAR ( W-SUB )   =   '.'
        /// PERFORM   C1-FULL-STOP-CHECK
        /// END-IF
        /// IF   ILNAME-INPUT-STRING-CHAR ( W-SUB )   =   '/'
        /// PERFORM   C2-SLASH-CHECK
        /// END-IF
        /// END-SEARCH
        /// END-IF
        /// END-PERFORM.
        /// </remarks>
        public void CTranslate(Gvar gvar, Ivar ivar)
        {
            // COBOL: MOVE ILNAME-KEEP-STRING-SIZE TO W-SUB
            //        ADD 1 TO W-SUB
            //        PERFORM VARYING W-SUB FROM W-SUB BY 1
            //        UNTIL W-SUB > 200
            //        MOVE X'FF' TO ILNAME-KEEP-CHAR (W-SUB)
            //        END-PERFORM

            int wSub = ivar.GetIlnameLinkage().GetIlnameKeepStringSize();
            wSub += 1;

            for (int i = wSub; i <= 200; i++)
            {
                // COBOL X'FF' is hex FF (255 in decimal) - use a special marker character
                ivar.GetIlnameLinkage().GetIlnameKeepChars().SetIlnameKeepCharAt(i - 1, "\xFF");
            }

            // COBOL: PERFORM VARYING W-SUB FROM 1 BY 1
            //        UNTIL W-SUB > ILNAME-INPUT-STRING-SIZE
            //        IF ILNAME-INPUT-STRING-CHAR (W-SUB) NOT = SPACES
            //           SEARCH ALL ILNAME-KEEP-CHAR
            //           AT END MOVE SPACES TO ILNAME-INPUT-STRING-CHAR (W-SUB)
            //           WHEN ILNAME-KEEP-CHAR (KEEP-INDEX) = ILNAME-INPUT-STRING-CHAR (W-SUB)
            //              IF ILNAME-INPUT-STRING-CHAR (W-SUB) = '.'
            //                 PERFORM C1-FULL-STOP-CHECK
            //              END-IF
            //              IF ILNAME-INPUT-STRING-CHAR (W-SUB) = '/'
            //                 PERFORM C2-SLASH-CHECK
            //              END-IF
            //           END-SEARCH
            //        END-IF
            //        END-PERFORM

            for (int i = 1; i <= ivar.GetIlnameLinkage().GetIlnameInputStringSize(); i++)
            {
                string inputChar = ivar.GetIlnameLinkage().GetIlnameInputString().GetIlnameInputStringCharAt(i - 1);

                if (inputChar != " ")
                {
                    bool found = false;

                    // Search all ILNAME-KEEP-CHAR array
                    for (int keepIndex = 0; keepIndex < 200; keepIndex++)
                    {
                        string keepChar = ivar.GetIlnameLinkage().GetIlnameKeepChars().GetIlnameKeepCharAt(keepIndex);

                        if (keepChar == inputChar)
                        {
                            found = true;
                            gvar.SetWSub(i); // Set W-SUB for use in C1/C2 methods

                            if (inputChar == ".")
                            {
                                C1FullStopCheck(gvar, ivar);
                            }

                            if (inputChar == "/")
                            {
                                C2SlashCheck(gvar, ivar);
                            }
                            break;
                        }
                    }

                    // AT END - if not found in keep chars, replace with spaces
                    if (!found)
                    {
                        ivar.GetIlnameLinkage().GetIlnameInputString().SetIlnameInputStringCharAt(i - 1, " ");
                    }
                }
            }
        }

        /// <summary>
        /// c1FullStopCheck - COBOL Paragraph
        /// </summary>
        /// <remarks>
        /// Original COBOL statements:
        /// IF   W-SUB   =   1   OR   ILNAME-INPUT-STRING-SIZE
        /// MOVE   SPACES   TO   ILNAME-INPUT-STRING-CHAR ( W-SUB )
        /// GO   TO   C1-EXIT
        /// END-IF
        /// IF   ILNAME-INPUT-STRING-CHAR ( W-SUB   -   1 )   <   '0'   OR   >   '9'
        /// OR   ILNAME-INPUT-STRING-CHAR ( W-SUB   +   1 )   <   '0'   OR   >   '9'
        /// MOVE   SPACES   TO   ILNAME-INPUT-STRING-CHAR ( W-SUB )
        /// END-IF.
        /// </remarks>
        public void C1FullStopCheck(Gvar gvar, Ivar ivar)
        {
            // COBOL: IF W-SUB = 1 OR ILNAME-INPUT-STRING-SIZE
            //        MOVE SPACES TO ILNAME-INPUT-STRING-CHAR (W-SUB)
            //        GO TO C1-EXIT
            //        END-IF
            //        IF ILNAME-INPUT-STRING-CHAR (W-SUB - 1) < '0' OR > '9'
            //        OR ILNAME-INPUT-STRING-CHAR (W-SUB + 1) < '0' OR > '9'
            //        MOVE SPACES TO ILNAME-INPUT-STRING-CHAR (W-SUB)
            //        END-IF

            int wSub = gvar.GetWSub();
            int inputStringSize = ivar.GetIlnameLinkage().GetIlnameInputStringSize();

            // Check if at beginning or end of string
            if (wSub == 1 || wSub == inputStringSize)
            {
                ivar.GetIlnameLinkage().GetIlnameInputString().SetIlnameInputStringCharAt(wSub - 1, " ");
                return; // GO TO C1-EXIT
            }

            // Check characters before and after the period
            string charBefore = ivar.GetIlnameLinkage().GetIlnameInputString().GetIlnameInputStringCharAt(wSub - 2);
            string charAfter = ivar.GetIlnameLinkage().GetIlnameInputString().GetIlnameInputStringCharAt(wSub);

            // Check if surrounding characters are not digits
            bool beforeIsNotDigit = string.Compare(charBefore, "0") < 0 || string.Compare(charBefore, "9") > 0;
            bool afterIsNotDigit = string.Compare(charAfter, "0") < 0 || string.Compare(charAfter, "9") > 0;

            if (beforeIsNotDigit || afterIsNotDigit)
            {
                ivar.GetIlnameLinkage().GetIlnameInputString().SetIlnameInputStringCharAt(wSub - 1, " ");
            }
        }

        /// <summary>
        /// c2SlashCheck - COBOL Paragraph
        /// </summary>
        /// <remarks>
        /// Original COBOL statements:
        /// IF   W-SUB   =   1   OR   ILNAME-INPUT-STRING-SIZE
        /// MOVE   SPACES   TO   ILNAME-INPUT-STRING-CHAR ( W-SUB )
        /// GO   TO   C2-EXIT
        /// END-IF
        /// IF   ILNAME-INPUT-STRING-CHAR ( W-SUB   -   1 )   <   '0'   OR   >   '9'
        /// OR   ILNAME-INPUT-STRING-CHAR ( W-SUB   +   1 )   <   '0'   OR   >   '9'
        /// MOVE   SPACES   TO   ILNAME-INPUT-STRING-CHAR ( W-SUB )
        /// END-IF.
        /// </remarks>
        public void C2SlashCheck(Gvar gvar, Ivar ivar)
        {
            // COBOL: IF W-SUB = 1 OR ILNAME-INPUT-STRING-SIZE
            //        MOVE SPACES TO ILNAME-INPUT-STRING-CHAR (W-SUB)
            //        GO TO C2-EXIT
            //        END-IF
            //        IF ILNAME-INPUT-STRING-CHAR (W-SUB - 1) < '0' OR > '9'
            //        OR ILNAME-INPUT-STRING-CHAR (W-SUB + 1) < '0' OR > '9'
            //        MOVE SPACES TO ILNAME-INPUT-STRING-CHAR (W-SUB)
            //        END-IF

            int wSub = gvar.GetWSub();
            int inputStringSize = ivar.GetIlnameLinkage().GetIlnameInputStringSize();

            // Check if at beginning or end of string
            if (wSub == 1 || wSub == inputStringSize)
            {
                ivar.GetIlnameLinkage().GetIlnameInputString().SetIlnameInputStringCharAt(wSub - 1, " ");
                return; // GO TO C2-EXIT
            }

            // Check characters before and after the slash
            string charBefore = ivar.GetIlnameLinkage().GetIlnameInputString().GetIlnameInputStringCharAt(wSub - 2);
            string charAfter = ivar.GetIlnameLinkage().GetIlnameInputString().GetIlnameInputStringCharAt(wSub);

            // Check if surrounding characters are not digits
            bool beforeIsNotDigit = string.Compare(charBefore, "0") < 0 || string.Compare(charBefore, "9") > 0;
            bool afterIsNotDigit = string.Compare(charAfter, "0") < 0 || string.Compare(charAfter, "9") > 0;

            if (beforeIsNotDigit || afterIsNotDigit)
            {
                ivar.GetIlnameLinkage().GetIlnameInputString().SetIlnameInputStringCharAt(wSub - 1, " ");
            }
        }

        /// <summary>
        /// dRemoveSpaces - COBOL Paragraph
        /// </summary>
        /// <remarks>
        /// Original COBOL statements:
        /// MOVE       0        TO   W-WORD-COUNT
        /// MOVE       1        TO   W-NEXT-WORD
        /// MOVE       1        TO   W-SUB
        /// MOVE      SPACES    TO   W-WORDS
        /// PERFORM   UNTIL   W-NEXT-WORD   >   50
        /// OR      W-SUB   >   ILNAME-INPUT-STRING-SIZE
        /// UNSTRING   ILNAME-INPUT-STRING
        /// DELIMITED   BY   SPACES
        /// INTO   W-WORD ( W-NEXT-WORD )
        /// COUNT   IN   W-COUNT ( W-NEXT-WORD )
        /// POINTER   W-SUB
        /// IF   W-WORD ( W-NEXT-WORD )   NOT   =   SPACES
        /// ADD   1   TO   W-WORD-COUNT
        /// ADD   1   TO   W-NEXT-WORD
        /// END-IF
        /// END-PERFORM.
        /// MOVE   1   TO   W-OP-SUB.
        /// MOVE      SPACES   TO   ILNAME-OUTPUT-STRING
        /// PERFORM   VARYING   W-SUB   FROM   1   BY   1
        /// UNTIL   W-SUB   >   W-WORD-COUNT
        /// PERFORM   VARYING   W-SUB-SUB   FROM   1   BY   1
        /// UNTIL   W-SUB-SUB   >   W-COUNT ( W-SUB )   OR   50
        /// MOVE    W-WORD-CHAR ( W-SUB   W-SUB-SUB )   TO
        /// ILNAME-OUTPUT-STRING-CHAR ( W-OP-SUB )
        /// ADD     1   TO   W-OP-SUB
        /// END-PERFORM
        /// ADD     1   TO   W-OP-SUB
        /// END-PERFORM.
        /// </remarks>
        public void DRemoveSpaces(Gvar gvar, Ivar ivar)
        {
            // COBOL: MOVE 0 TO W-WORD-COUNT
            //        MOVE 1 TO W-NEXT-WORD
            //        MOVE 1 TO W-SUB
            //        MOVE SPACES TO W-WORDS
            gvar.SetWWordCount(0);
            gvar.SetWNextWord(1);
            gvar.SetWSub(1);
            gvar.GetWWords().SetWWordsAsString(""); // Clear W-WORDS

            // COBOL: PERFORM UNTIL W-NEXT-WORD > 50
            //        OR W-SUB > ILNAME-INPUT-STRING-SIZE
            //        UNSTRING ILNAME-INPUT-STRING
            //                 DELIMITED BY SPACES
            //                 INTO W-WORD (W-NEXT-WORD)
            //                 COUNT IN W-COUNT (W-NEXT-WORD)
            //                 POINTER W-SUB
            //        IF W-WORD (W-NEXT-WORD) NOT = SPACES
            //           ADD 1 TO W-WORD-COUNT
            //           ADD 1 TO W-NEXT-WORD
            //        END-IF
            //        END-PERFORM

            string inputString = ivar.GetIlnameLinkage().GetIlnameInputStringAsString();
            string[] words = inputString.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

            int wordIndex = 0;
            foreach (string word in words)
            {
                if (wordIndex >= 50) break; // Limit to 50 words

                // Store word in W-WORD array
                if (gvar.GetWWords().GetWWordAt(wordIndex) == null)
                {
                    gvar.GetWWords().SetWWordAt(wordIndex, new WWords.WWord());
                }
                for (int i = 0; i < word.Length && i < 50; i++)
                {
                    gvar.GetWWords().GetWWordAt(wordIndex).SetWWordCharAt(i, word[i].ToString());
                }

                // Set word count
                gvar.SetWCountAt(wordIndex, word.Length);
                gvar.SetWWordCount(gvar.GetWWordCount() + 1);
                wordIndex++;
            }

            // COBOL: MOVE 1 TO W-OP-SUB
            //        MOVE SPACES TO ILNAME-OUTPUT-STRING
            gvar.SetWOpSub(1);
            ivar.GetIlnameLinkage().SetIlnameOutputStringAsString(""); // Clear output string

            // COBOL: PERFORM VARYING W-SUB FROM 1 BY 1
            //        UNTIL W-SUB > W-WORD-COUNT
            //        PERFORM VARYING W-SUB-SUB FROM 1 BY 1
            //        UNTIL W-SUB-SUB > W-COUNT (W-SUB) OR 50
            //           MOVE W-WORD-CHAR (W-SUB W-SUB-SUB) TO
            //                ILNAME-OUTPUT-STRING-CHAR (W-OP-SUB)
            //           ADD 1 TO W-OP-SUB
            //        END-PERFORM
            //        ADD 1 TO W-OP-SUB
            //        END-PERFORM

            int opSub = 1;
            for (int wSub = 1; wSub <= gvar.GetWWordCount(); wSub++)
            {
                for (int wSubSub = 1; wSubSub <= gvar.GetWCountAt(wSub - 1) && wSubSub <= 50; wSubSub++)
                {
                    if (opSub <= 200) // Ensure we don't exceed output string size
                    {
                        string charToMove = gvar.GetWWords().GetWWordAt(wSub - 1).GetWWordCharAt(wSubSub - 1);
                        ivar.GetIlnameLinkage().GetIlnameOutputString().SetIlnameOutputStringCharAt(opSub - 1, charToMove);
                        opSub++;
                    }
                }
                // Add space between words
                if (opSub <= 200)
                {
                    ivar.GetIlnameLinkage().GetIlnameOutputString().SetIlnameOutputStringCharAt(opSub - 1, " ");
                    opSub++;
                }
            }
        }

    }
}
