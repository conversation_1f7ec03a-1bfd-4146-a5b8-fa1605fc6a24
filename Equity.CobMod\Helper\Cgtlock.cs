using System;
using System.Text;
using EquityProject.CgtlockDTO;
namespace EquityProject.CgtlockPGM
{
    // Cgtlock Class Definition

    //Cgtlock Class Constructor
    public class Cgtlock
    {
        // Declare Cgtlock Class private variables
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();

        // Declare {program_name} Class getters setters
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        /// <summary>
        /// Helper method to call external subroutines
        /// </summary>
        /// <param name="ivar">The Ivar parameter to pass to the subroutine</param>
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
        }


        // Run() method
        public void Run(Gvar gvar, Ivar ivar)
        {
            // Call the main entry point
            // No mainline procedure found, add your entry point here
        }

        // Methods representing paragraphs under procedure division
        /// <summary>
        /// COBOL paragraph: aControl
        /// Handles the control logic for CGTLOCK actions.
        /// </summary>
        /// <param name="gvar">Global variables class containing program-scoped variables.</param>
        /// <param name="ivar">Interface variables class containing interface-level variables.</param>
        /// <remarks>
        /// This method converts the COBOL paragraph `aControl` to C#.
        /// It handles the control logic for CGTLOCK actions based on the value of CGTLOCK-ACTION.
        /// </remarks>
        public void AControl(Gvar gvar, Ivar ivar)
        {
            // Move SPACES to CGTLOCK-MESSAGE
            ivar.GetCgtlockLinkage().SetCgtlockMessageAsString("");

            // Move CGTLOCK-SUCCESSFUL to CGTLOCK-STATUS
            ivar.GetCgtlockLinkage().SetCgtlockStatus(Ivar.CGTLOCK_SUCCESSFUL);


            // Evaluate CGTLOCK-ACTION
            switch (ivar.GetCgtlockLinkage().GetCgtlockAction())
            {
                case Ivar.CGTLOCK_INITIALISE:
                    // Perform B-Initialise
                    BInitialise(gvar, ivar);
                    break;
                case Ivar.CGTLOCK_LOCK:
                    // Perform C-Lock-Funds
                    CLockFunds(gvar, ivar);
                    break;
                case Ivar.CGTLOCK_UNLOCK:
                    // Perform D-Unlock-Funds
                    DUnlockFunds(gvar, ivar);
                    break;
                case Ivar.CGTLOCK_TERMINATE:
                    // Perform E-Terminate
                    ETerminate(gvar, ivar);
                    break;
                default:
                    // Move CGTLOCK-ILLEGAL-ACTION to CGTLOCK-STATUS
                    ivar.GetCgtlockLinkage().SetCgtlockStatus(Ivar.CGTLOCK_ILLEGAL_ACTION);
                    break;
            }
        }

        /// <summary>
        /// bInitialise paragraph from COBOL
        /// </summary>
        /// <param name="gvar">The global data variable</param>
        /// <param name="ivar">The input variable</param>
        /// <remarks>
        /// Converts COBOL logic to C# methods maintaining the same business rules and logic flow.
        /// Uses method signatures with relevant parameters to access required data
        /// </remarks>
        public void BInitialise(Gvar gvar, Ivar ivar)
        {
            // Check if CGTLOCK-UNINITIALISED is true
            if (gvar.IsCgtlockUninitialised())
            {
                // Perform B1OpenFundFile method
                B1OpenFundFile(gvar, ivar);

                // Check if OPEN-OK or FILE-WAS-ALREADY-OPEN
                if (gvar.GetCgtfilesLinkage().IsOpenOk() ||
                     gvar.GetCgtfilesLinkage().IsFileWasAlreadyOpen())
                {
                    // Move CGTLOCK-SUCCESSFUL to CGTLOCK-STATUS
                    ivar.GetCgtlockLinkage().SetCgtlockStatus(Ivar.CGTLOCK_SUCCESSFUL);

                    // Set CGTLOCK-INITIALISED to TRUE
                    gvar.SetWProgramStatus(1);

                    // Check if FILE-WAS-ALREADY-OPEN
                    if (gvar.IsFundFileWasAlreadyOpen())
                    {
                        // Set FUND-FILE-WAS-ALREADY-OPEN to TRUE
                        gvar.SetWFundFileStatus(0);
                    }
                    else
                    {
                        // Set FUND-FILE-WAS-CLOSED to TRUE
                        gvar.SetWFundFileStatus(1);
                    }
                }
                else
                {
                    // Move CGTLOCK-OPEN-FAILED to CGTLOCK-STATUS
                    ivar.GetCgtlockLinkage().SetCgtlockStatus(Ivar.CGTLOCK_OPEN_FAILED);

                    // Set CGTLOCK-UNINITIALISED to TRUE
                    gvar.SetWProgramStatus(0);

                    // Create the CGTLOCK-MESSAGE string
                    var cgtlockMessage = "CGTLOCK FUND FILE OPEN FAILED STATUS " +
                                         gvar.GetCgtfilesLinkage().GetLFileReturnCode();
                    ivar.GetCgtlockLinkage()
                        .SetCgtlockMessage(cgtlockMessage);
                }
            }
            else
            {
                // Move CGTLOCK-SUCCESSFUL to CGTLOCK-STATUS
                ivar.GetCgtlockLinkage()
                     .SetCgtlockStatus(Ivar.CGTLOCK_SUCCESSFUL);
            }

        }
        /// <summary>
        /// This method is the equivalent of the COBOL paragraph named B1-COPEN-FUND-FILE.
        /// It performs the operations to move values and call the XCallCgtfiles method.
        /// </summary>
        /// <remarks>
        /// This method sets the file action and file name and then calls the XCallCgtfiles method.
        /// </remarks>
        /// <param name="gvar">Global variables collection.</param>
        /// <param name="ivar">Input variables collection.</param>
        public void B1OpenFundFile(Gvar gvar, Ivar ivar)
        {
            // Equivalent of COBOL: MOVE OPEN-I-O TO L-FILE-ACTION
            // Use the provided variable mappings and getter/setter methods
            // Note that OPEN-I-O is a 78-level constant in COBOL.
            var lFileAction = gvar.GetCgtfilesLinkage().GetLFileAction();

            // Update the L-FILE-ACTION with the value of OPEN-I-O
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.OPEN_I_O);

            // Equivalent of COBOL: MOVE FUND-FILE TO L-FILE-NAME
            // Use the provided variable mappings and getter/setter methods
            // Note that FUND-FILE is a 78-level constant in COBOL.
            var lFileName = gvar.GetCgtfilesLinkage().GetLFileName();

            // Update the L-FILE-NAME with the value of FUND-FILE
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.FUND_FILE);

            // Equivalent of COBOL: PERFORM X-CALL-CGTFILES
            // Use the PascalCase method name for the PERFORM statement
            XCallCgtfiles(gvar, ivar);
        }
        /// <summary>
        /// COBOL Paragraph cLockFunds Implementation.
        /// </summary>
        ///
        /// <param name="gvar">Global variables</param>
        /// <param name="ivar">Input variables</param>
        ///
        /// <remarks>
        /// This method implements the logic from the COBOL paragraph cLockFunds.
        /// </remarks>
        public void CLockFunds(Gvar gvar, Ivar ivar)
        {
            // MOVE   SPACES   TO   D4-RECORD
            ivar.SetD4RecordAsString("");

            // IF   CGTLOCK-INITIALISED
            if (gvar.IsCgtlockInitialised())
            {
                // MOVE   CGTLOCK-FUND     TO   L-FILE-RECORD-AREA
                gvar.SetLFileRecordAreaAsString(ivar.GetCgtlockLinkage().GetCgtlockFundAsString());

                // MOVE   READ-WITH-LOCK   TO   L-FILE-ACTION
                gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_WITH_LOCK);

                // PERFORM   X-CALL-CGTFILES
                XCallCgtfiles(gvar, ivar);

                // IF   SUCCESSFUL
                if (gvar.GetCgtfilesLinkage().IsSuccessful())
                {
                    // MOVE   CGTLOCK-SUCCESSFUL   TO   CGTLOCK-STATUS
                    ivar.GetCgtlockLinkage().SetCgtlockStatus(Ivar.CGTLOCK_SUCCESSFUL);

                    // MOVE   L-FILE-RECORD-AREA   TO   D4-RECORD
                    ivar.SetD4RecordAsString(gvar.GetLFileRecordAreaAsString());
                }
                else
                {
                    // STRING   'CGTLOCK UNABLE TO LOCK FUND '
                    // CGTLOCK-FUND
                    // ' STATUS '
                    // L-FILE-RETURN-CODE
                    // DELIMITED   BY   SIZE
                    // INTO   CGTLOCK-MESSAGE
                    string message = "CGTLOCK UNABLE TO LOCK FUND " +
                        gvar.GetCgtfilesLinkage().GetLFileReturnCode();

                    ivar.GetCgtlockLinkage().SetCgtlockMessage(message);

                    // MOVE   CGTLOCK-LOCK-FAILURE   TO   CGTLOCK-STATUS
                    ivar.GetCgtlockLinkage().SetCgtlockStatus(Ivar.CGTLOCK_LOCK_FAILURE);
                }
            }
            else
            {
                // STRING   'CGTLOCK CANT LOCK FUND '
                //     CGTLOCK-FUND
                //     ' WHEN NOT INITIALISED'
                //     DELIMITED   BY   SIZE
                //     INTO   CGTLOCK-MESSAGE
                string message = "CGTLOCK CANT LOCK FUND " + ivar.GetCgtlockLinkage().GetCgtlockFund() + " WHEN NOT INITIALISED";

                ivar.GetCgtlockLinkage().SetCgtlockMessage(message);

                // MOVE   CGTLOCK-ILLOGICAL-REQUEST   TO   CGTLOCK-STATUS
                ivar.GetCgtlockLinkage().SetCgtlockStatus(Ivar.CGTLOCK_ILLOGICAL_REQUEST);
            }
        }
        /// <summary>
        /// dUnlockFunds paragraph, performing the unlocking of funds.
        ///
        /// Perform relevant encoded logic for unlock funds scenario
        /// </summary>
        /// <param name="gvar">gvar parameters</param>
        /// <param name="ivar">ivar parameters</param>
        /// <remarks>
        ///  Key modifications:
        ///  - Converted COBOL IF statement to C# if statement
        ///  - Converted COBOL MOVE statement to C# setter method
        ///  - Converted COBOL PERFORM statement to C# method call
        ///  - Converted COBOL STRING statement to C# StringBuilder with appropriate delimiters
        /// </remarks>
        public void DUnlockFunds(Gvar gvar, Ivar ivar)
        {
            // If CGTLOCK-INITIALISED condition is true
            if (gvar.IsCgtlockInitialised())
            {
                // Move UNLOCK-RECORD to L-FILE-ACTION
                gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.UNLOCK_RECORD);

                // Perform X-CALL-CGTFILES paragraph
                XCallCgtfiles(gvar, ivar);

                // Check if the operation was successful
                if (gvar.GetCgtfilesLinkage().IsSuccessful())
                {
                    // Move CGTLOCK-SUCCESSFUL to CGTLOCK-STATUS
                    ivar.GetCgtlockLinkage().SetCgtlockAction(Ivar.CGTLOCK_SUCCESSFUL); // Set constant value to CgtlockStatus property
                    
                }
                else
                {
                    // Construct the error message for unsuccessful operation
                    System.Text.StringBuilder messageBuilder = new System.Text.StringBuilder();
                    messageBuilder.Append("CGTLOCK UNLOCK FAILED STATUS ");
                    messageBuilder.Append(gvar.GetCgtfilesLinkage().GetLFileReturnCode());// Get the return code value
                    ivar.GetCgtlockLinkage().SetCgtlockMessageAsString(messageBuilder.ToString()); // Set the constructed message to INTO
                    ivar.GetCgtlockLinkage().SetCgtlockStatus(Ivar.CGTLOCK_LOCK_FAILURE); // Set constant value to CgtlockStatus property
                }
            }
            else
            {
                // If CGTLOCK-INITIALISED is false, construct a different error message
                System.Text.StringBuilder messageBuilder = new System.Text.StringBuilder();
                messageBuilder.Append("CGTLOCK CANT UNLOCK WHEN NOT INITIALISED");
                ivar.GetCgtlockLinkage().SetCgtlockMessageAsString(messageBuilder.ToString()); // Set the constructed message to INTO
                ivar.GetCgtlockLinkage().SetCgtlockStatus(Ivar.CGTLOCK_ILLOGICAL_REQUEST); // Set constant value to CgtlockStatus property
            }
        }
        /// <summary>
        /// This method terminates the CGTLOCK process. It checks if the CGTLOCK is initialized,
        /// opens or closes files as necessary, and sets the appropriate status and messages.
        /// </summary>
        /// <param name="gvar">
        /// Parameter object containing COBOL working-storage variables.
        /// </param>
        /// <param name="ivar">
        /// Parameter object containing COBOL linkage-section variables.
        /// </param>
        /// <remarks>
        /// This method converts the COBOL paragraph "ETERMINATE".
        /// </remarks>
        public void ETerminate(Gvar gvar, Ivar ivar)
        {
            // If CGTLOCK is initialized
            if (gvar.IsCgtlockInitialised())
            {
                // Move UNLOCK-RECORD to L-FILE-ACTION
                gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.UNLOCK_RECORD);
                // Perform X-CALL-CGTFILES
                XCallCgtfiles(gvar, ivar);
                // If FUND-FILE-WAS-CLOSED
                if (gvar.IsFundFileWasClosed())
                {
                    // Move CLOSE-FILE to L-FILE-ACTION
                    gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.CLOSE_FILE);
                    // Perform X-CALL-CGTFILES
                    XCallCgtfiles(gvar, ivar);

                    // If SUCCESSFUL
                    if (gvar.GetCgtfilesLinkage().IsSuccessful())
                    {
                        // Move CGTLOCK-SUCCESSFUL to CGTLOCK-STATUS
                        ivar.GetCgtlockLinkage().SetCgtlockStatus(Ivar.CGTLOCK_SUCCESSFUL);
                    }
                    else
                    {
                        // Create error message
                        var errorMessage =
                            "CGTLOCK FUND FILE CLOSE FAILED STATUS " +
                            gvar.GetCgtfilesLinkage().GetLFileReturnCode();
                        // Move error message to CGTLOCK-MESSAGE
                        ivar.GetCgtlockLinkage().SetCgtlockMessage(errorMessage);
                        // Move CGTLOCK-CLOSE-FAILED to CGTLOCK-STATUS
                        ivar.GetCgtlockLinkage().SetCgtlockStatus(Ivar.CGTLOCK_CLOSE_FAILED);
                    }
                }
                else
                {
                    // Move CGTLOCK-SUCCESSFUL to CGTLOCK-STATUS
                    ivar.GetCgtlockLinkage().SetCgtlockStatus(Ivar.CGTLOCK_SUCCESSFUL);
                }
            }
            else
            {
                // Create error message
                var errorMessage = "CGTLOCK CANT TERMINATE WHEN NOT INITIALISED";
                // Move error message to CGTLOCK-MESSAGE
                ivar.GetCgtlockLinkage().SetCgtlockMessage(errorMessage);
                // Move CGTLOCK-ILLOGICAL-REQUEST to CGTLOCK-STATUS
                ivar.GetCgtlockLinkage().SetCgtlockStatus(Ivar.CGTLOCK_ILLOGICAL_REQUEST);
            }

            // Set CGTLOCK-UNINITIALISED to TRUE
            gvar.SetWProgramStatus(0);
        }
        /// <summary>
        /// Performs the xCallCgtfiles COBOL paragraph.
        /// </summary>
        /// <remarks>Author OriginalCodeApprover@COBOLco</remarks>
        /// <param name="gvar">A container for accessing global variables</param>
        /// <param name="ivar">A container for accessing local variables</param>
        public void XCallCgtfiles(Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the Cgtfiles class
            /* must uncomment
            Cgtfiles cgtfiles = new Cgtfiles();

            // Call the Run method with the parameters specified in the USING clause.
            cgtfiles.Run(gvar.GetCgtfilesLinkage(), gvar.GetLFileRecordArea(), gvar.GetCommonLinkage());*/
        }

    }
}
