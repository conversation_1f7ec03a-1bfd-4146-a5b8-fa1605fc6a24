using System;
using System.Text;
using EquityProject.Elcgts20DTO;
using EquityProject.CommonDTO;

namespace EquityProject.Elcgts20PGM
{
    /// <summary>
    /// ELCGTS20 - Capital Gains Tax Calculation Program
    /// Converted from COBOL to C#
    /// Original COBOL Program: elcgts20.cbl
    /// </summary>
    public class Elcgts20
    {
        // Main DTO instances
        private Fvar fvar;
        private Gvar gvar;
        private Ivar ivar;

        // Constructor
        public Elcgts20()
        {
            fvar = new Fvar();
            gvar = new Gvar();
            ivar = new Ivar();
        }

        /// <summary>
        /// Main entry point - equivalent to PROCEDURE DIVISION USING PARM-INFO
        /// </summary>
        /// <param name="parmInfo">Parameter information from calling program</param>
        public void Execute(ParmInfo parmInfo)
        {
            try
            {
                // Set parameter information
                ivar.SetParmInfo(parmInfo);

                // Execute main control logic
                AMainlineSection();
            }
            catch (Exception ex)
            {
                // Handle fatal errors
                HandleFatalError(ex);
            }
        }

        /// <summary>
        /// A-MAINLINE SECTION - Main control logic
        /// </summary>
        private void AMainlineSection()
        {
            A10Control();
        }

        /// <summary>
        /// A10-CONTROL - Main control procedure
        /// </summary>
        private void A10Control()
        {
            // Check for vector 'M' and perform EY-RETURN if needed
            if (gvar.GetWsStorage().GetWsVector().Equals("M"))
            {
                EyReturn();
                return;
            }

            // Initialize error flag
            gvar.GetWsStorage().SetWsErrflagAsString(" ");
            gvar.GetWsStorage().SetWsLastRaBaCalSedolAsString(" ");

            // Check parameter length and set parameter character
            if (ivar.GetParmInfo().GetParmLength() != 0)
            {
                gvar.GetWsStorage().SetWsParmChar1AsString(
                    ivar.GetParmInfo().GetParmChars().GetParmChar1());
            }

            // Handle different run types
            if (!IsNormalRun())
            {
                gvar.GetWsStorage().SetDisplayPgmAsString("ELCGTMFF");
                gvar.GetWsStorage().SetWsNowAsString(
                    ivar.GetParmInfo().GetParmDateTimeStamp());
            }

            // Initialize if normal run or first call
            if (IsNormalRun() || IsFirstCall())
            {
                BInitialiseSection();
            }

            // Set start needed flag
            gvar.GetWsStorage().SetWsStartNeededAsString("Y");

            // Initialize working indices
            gvar.GetWiInterface().SetWiK(1);
            gvar.GetWiInterface().SetWiP(1);
            gvar.GetWiInterface().SetWiCf(1);

            // Handle partial run
            if (IsPartialRun())
            {
                string eyMessage = "I" + gvar.GetWsStorage().GetWsParmFn() +
                                 " PARTIAL CALC RUN";
                gvar.GetWsStorage().SetWsEyAsString(eyMessage);
                EyMsgControl();
            }

            // Handle calculation call
            if (IsCalcCall())
            {
                if (!IsXFundCalc())
                {
                    AcOpenInitRptSection();
                }
            }

            // Open schedule files if needed
            if (IsCalcCall() && !IsScheduleFileOpen())
            {
                OpenScheduleFiles();
            }

            // Main processing logic
            if (IsXFundCalc())
            {
                AbCrossFundSedolCalcSection();
            }
            else
            {
                if (IsNormalRun() || IsCalcEvery())
                {
                    // Process funds until end or fatal error
                    while (!IsFatalError() && !IsEndOfFunds())
                    {
                        CProcessFundSection();
                    }
                }
                else
                {
                    // Handle specific fund processing
                    ProcessSpecificFund();
                }
            }

            // Schedule header processing
            if (gvar.GetWtTables().GetWttOccurs() == 0)
            {
                BdScheduleHeaderSection();
            }

            // Close reports and files
            if (IsCloseRpt())
            {
                CloseReportsAndFiles();
            }

            // Handle exit request
            if (IsExitRequest())
            {
                gvar.GetWsStorage().SetWsErrflagAsString("E");
            }

            // Terminate processing
            if (IsNormalRun() || IsLastCall() || IsCalcEvery() || IsFatalError())
            {
                DTerminateSection();
            }

            // Close calculation reports
            if (IsCalcCall())
            {
                DeTotalCloseRptSection();
            }

            // Final goback
            A90Goback();
        }

        /// <summary>
        /// A90-GOBACK - Final exit point
        /// </summary>
        private void A90Goback()
        {
            // Set final parameter character based on error status
            if (IsFatalError())
            {
                ivar.GetParmInfo().GetParmChars().SetParmChar1AsString("Z");
            }
            else
            {
                ivar.GetParmInfo().GetParmChars().SetParmChar1AsString("Q");
            }
        }

        #region Helper Methods for Run Type Checking

        /// <summary>
        /// Check if this is a normal run
        /// </summary>
        private bool IsNormalRun()
        {
            return gvar.GetWsStorage().GetWsParmChar1().Equals("N");
        }

        /// <summary>
        /// Check if this is the first call
        /// </summary>
        private bool IsFirstCall()
        {
            return gvar.GetWsStorage().GetWsParmChar1().Equals("F");
        }

        /// <summary>
        /// Check if this is a partial run
        /// </summary>
        private bool IsPartialRun()
        {
            return gvar.GetWsStorage().GetWsParmChar1().Equals("P");
        }

        /// <summary>
        /// Check if this is a calculation call
        /// </summary>
        private bool IsCalcCall()
        {
            return gvar.GetWsStorage().GetWsParmChar1().Equals("C");
        }

        /// <summary>
        /// Check if this is a calculation every call
        /// </summary>
        private bool IsCalcEvery()
        {
            return gvar.GetWsStorage().GetWsParmChar1().Equals("E");
        }

        /// <summary>
        /// Check if this is the last call
        /// </summary>
        private bool IsLastCall()
        {
            return gvar.GetWsStorage().GetWsParmChar1().Equals("L");
        }

        /// <summary>
        /// Check if this is a cross-fund calculation
        /// </summary>
        private bool IsXFundCalc()
        {
            return gvar.GetWsStorage().GetWsParmChar1().Equals("X");
        }

        /// <summary>
        /// Check if close report is requested
        /// </summary>
        private bool IsCloseRpt()
        {
            return gvar.GetWsStorage().GetWsParmChar1().Equals("R");
        }

        /// <summary>
        /// Check if exit is requested
        /// </summary>
        private bool IsExitRequest()
        {
            return gvar.GetWsStorage().GetWsErrflag().Equals("E");
        }

        /// <summary>
        /// Check if there's a fatal error
        /// </summary>
        private bool IsFatalError()
        {
            return gvar.GetWsStorage().GetWsErrflag().Equals("F");
        }

        /// <summary>
        /// Check if schedule file is open
        /// </summary>
        private bool IsScheduleFileOpen()
        {
            return gvar.GetWsStorage().GetWsDrOpen().Equals("Y");
        }

        /// <summary>
        /// Check if we've reached end of funds
        /// </summary>
        private bool IsEndOfFunds()
        {
            return gvar.GetWtCompany().GetCfCoAcLk().Equals("HIGH-VALUES");
        }

        #endregion

        #region Error Handling

        /// <summary>
        /// Handle fatal errors
        /// </summary>
        private void HandleFatalError(Exception ex)
        {
            gvar.GetWsStorage().SetWsErrflagAsString("F");
            string errorMessage = "FATAL ERROR: " + ex.Message;
            gvar.GetWsStorage().SetWsEyAsString(errorMessage);
            EyMsgControl();
        }

        /// <summary>
        /// EY-RETURN - Early return processing
        /// </summary>
        private void EyReturn()
        {
            // Implementation for early return logic
            // This would typically involve cleanup and exit
        }

        /// <summary>
        /// EY-MSG-CONTROL - Message control processing
        /// </summary>
        private void EyMsgControl()
        {
            // Implementation for message control
            // This would typically log or display messages
            string message = gvar.GetWsStorage().GetWsEy();
            if (!string.IsNullOrEmpty(message))
            {
                // Log or process the message
                Console.WriteLine(message);
            }
        }

        #endregion

        #region Main Processing Sections

        /// <summary>
        /// AB-CROSS-FUND-SEDOL-CALC SECTION - Cross fund SEDOL calculation
        /// </summary>
        private void AbCrossFundSedolCalcSection()
        {
            // Move parameters for cross-fund calculation
            gvar.GetWsStorage().SetWsXFundKeyAsString(
                ivar.GetParmInfo().GetParmCalSedol());
            gvar.GetWsStorage().SetWsXFundDateAsString(
                ivar.GetParmInfo().GetParmXFundDate());
            gvar.GetWsStorage().SetWsXFundPriceAsString(
                ivar.GetParmInfo().GetParmXFundPrice());

            // Open unrealized schedule header if needed
            if (!gvar.GetWsStorage().GetWsDuOpen().Equals("Y"))
            {
                BcUnrSchedHeaderSection();
            }

            // Process cross-fund logic
            if (!string.IsNullOrEmpty(gvar.GetWsStorage().GetWsXFundFund()))
            {
                gvar.GetWtCompany().SetCfCoAcLkAsString(
                    gvar.GetWsStorage().GetWsXFundFund());
                EiGetCfSection();

                if (gvar.GetWtTables().GetWtcCal(gvar.GetWiInterface().GetWiCf())
                    .Equals(gvar.GetWsStorage().GetWsXFundFund()))
                {
                    gvar.GetWsStorage().SetWsSummaryFundFlagAsString(
                        gvar.GetWtTables().GetWtcSummaryFundFlag(
                            gvar.GetWiInterface().GetWiCf()));

                    gvar.GetWsStorage().SetWsCurrentCalSedolAsString(
                        gvar.GetWsStorage().GetWsXFundKey());

                    // Process SEDOL for calculation
                    while (!gvar.GetWsStorage().GetWsCurrentContractNo().Equals("LOW-VALUES")
                           && !IsFatalError())
                    {
                        EProcessSedolForCalSection();
                    }
                }
                else
                {
                    // Handle case where fund doesn't match
                    if (string.IsNullOrEmpty(gvar.GetWsStorage().GetWsLastFund()) ||
                        gvar.GetWsStorage().GetWsLastFund().Equals("LOW-VALUES"))
                    {
                        CaGetCoSection();
                    }

                    // Process fund until end or fatal error
                    while (!gvar.GetWtCompany().GetCfCoAcLk().Equals("HIGH-VALUES")
                           && !IsFatalError())
                    {
                        CProcessFundSection();
                    }
                }
            }

            // Clear calculation flag
            gvar.GetWtTables().SetWtcCalAsString(
                gvar.GetWiInterface().GetWiCf(), " ");
        }

        /// <summary>
        /// AC-OPEN-INIT-RPT SECTION - Open and initialize report files
        /// </summary>
        private void AcOpenInitRptSection()
        {
            try
            {
                // Set up file names for micro I/O
                if (IsMicroIo())
                {
                    fvar.GetIlostRecord().SetIlostUserNoAsString(
                        gvar.GetWsStorage().GetWsParmFn1To5());
                    fvar.GetIlosuRecord().SetIlosuUserNoAsString(
                        gvar.GetWsStorage().GetWsParmFn1To5());
                }

                // Set up offshore report file paths
                gvar.GetEqtpathLinkage().SetEqtpathFileNameAsString(
                    fvar.GetOffshoreRptFile().GetOffshoreRptFile());
                XCallEqtpathSection();
                fvar.GetOffshoreRptFile().SetOffshoreRptAsString(
                    gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

                gvar.GetEqtpathLinkage().SetEqtpathFileNameAsString(
                    fvar.GetOffshoreRptFileDd().GetOffshoreRptFileDd());
                XCallEqtpathSection();
                fvar.GetOffshoreRptFileDd().SetOffshoreRptDdAsString(
                    gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

                // Set up ILOST report file path
                gvar.GetEqtpathLinkage().SetEqtpathFileNameAsString(
                    fvar.GetIlostReportFile().GetIlostReportFile());
                XCallEqtpathSection();
                fvar.GetIlostReportFile().SetIlostReportAsString(
                    gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

                // Set up ILOSU report file path
                gvar.GetEqtpathLinkage().SetEqtpathFileNameAsString(
                    fvar.GetIlosuReportFile().GetIlosuReportFile());
                XCallEqtpathSection();
                fvar.GetIlosuReportFile().SetIlosuReportAsString(
                    gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

                // Open offshore file
                OpenOffshoreFile();

                // Initialize working storage items
                InitializeWorkingStorageItems();
            }
            catch (Exception ex)
            {
                gvar.GetWsStorage().SetWsErrflagAsString("F");
                gvar.GetWsStorage().SetWsEyAsString("ERROR OPENING REPORT FILES: " + ex.Message);
                EyMsgControl();
            }
        }

        /// <summary>
        /// B-INITIALISE SECTION - Main initialization
        /// PERFORMED BY A-MAINLINE
        /// PERFORMS BA
        /// </summary>
        private void BInitialiseSection()
        {
            // Move parameters - PERFORM BE-MOVE-PARM VARYING WS-NUMBER FROM 1 BY 1 UNTIL WS-NUMBER > 24 OR WS-NUMBER > PARM-LENGTH
            for (int wsNumber = 1; wsNumber <= 24 && wsNumber <= ivar.GetParmInfo().GetParmLength(); wsNumber++)
            {
                BeMoveParamSection(wsNumber);
            }

            // MOVE 0 TO WS-NUMBER, W-TOTAL-TO-MATURITY-DATE
            gvar.GetWsStorage().SetWsNumberAsString("0");
            gvar.GetWsStorage().SetWTotalToMaturityDateAsString("0");

            // IF WS-PARM-CHAR2 NOT = 'R' MOVE SPACES TO WS-PARM-CHAR2
            if (!gvar.GetWsStorage().GetWsParmChar2().Equals("R"))
            {
                gvar.GetWsStorage().SetWsParmChar2AsString(" ");
            }

            // Set up I/O program names based on file type
            if (IsIndexedFundFile())
            {
                gvar.GetWsStorage().SetWsTioPgmAsString("ELCGCIO ");
            }
            else
            {
                gvar.GetWsStorage().SetWsTioPgmAsString("ELCGTIO ");
            }

            gvar.GetWsStorage().SetWsIoPgmAsString("ELCGTX24");
            gvar.GetWsStorage().SetWsDioPgmAsString("ELCGCIO");

            if (IsStandardMf())
            {
                gvar.GetWsStorage().SetWsIoPgmAsString("ELCGTK24"); // never used
            }

            if (IsTrialIo())
            {
                gvar.GetWsStorage().SetWsDioPgmAsString("ELCGDIO"); // never used
            }

            if (IsMicroIo())
            {
                gvar.GetWsStorage().SetWsIoPgmAsString("ELCGMIO");
                gvar.GetWsStorage().SetWsCioPgmAsString("ELCGGIO");
                gvar.GetWsStorage().SetWsTioPgmAsString("ELCGGIO");
                gvar.GetWsStorage().SetWsDioPgmAsString("ELCGGIO");
            }

            // MOVE WHEN-COMPILED TO W-WHEN-COMPILED
            gvar.GetWsStorage().SetWWhenCompiledAsString(GetWhenCompiled());

            // Accept current date and time if normal run
            if (IsNormalRun())
            {
                AcceptCurrentDateTime();
            }

            // MOVE SPACES TO WS-NOW-SPACE
            gvar.GetWsStorage().SetWsNowSpaceAsString(" ");

            // Set master file names
            SetMasterFileNames();

            // Open message file if parameter length not > 87
            if (ivar.GetParmInfo().GetParmLength() <= 87)
            {
                OpenMessageFile();
            }

            // Display startup message if first call
            if (IsFirstCall())
            {
                AcceptCurrentDateTime();
            }

            DisplayStartupMessage();

            // Set up working table pointers
            gvar.GetWiInterface().SetWiTp(1);

            // Handle deemed disposals flag
            HandleDeemedDisposalsFlag();

            DisplayYearReportInfo();

            // Initialize working table cost entries
            InitializeWorkingTableCostEntries();

            // Open error file
            OpenErrorFile();

            // Load RPI (Retail Price Index) data
            BaLoadRpiSection();

            // Initialize master file handling
            InitializeMasterFileHandling();

            // Get company information
            CaGetCoSection();

            // Initialize schedule headers
            if (IsNormalRun())
            {
                BdScheduleHeaderSection();
            }

            if (IsNormalRun() && IsPrintUnrealSchedule())
            {
                BcUnrSchedHeaderSection();
            }

            // Initialize price handling
            InitializePriceHandling();
        }

        #endregion

        #region Supporting Methods

        /// <summary>
        /// Check if micro I/O is enabled
        /// </summary>
        private bool IsMicroIo()
        {
            return gvar.GetWsStorage().GetWsMicroIo().Equals("Y");
        }

        /// <summary>
        /// Check if indexed fund file is used
        /// </summary>
        private bool IsIndexedFundFile()
        {
            return gvar.GetWsStorage().GetWsIndexedFundFile().Equals("Y");
        }

        /// <summary>
        /// Check if standard MF is used
        /// </summary>
        private bool IsStandardMf()
        {
            return gvar.GetWsStorage().GetWsStandardMf().Equals("Y");
        }

        /// <summary>
        /// Check if trial I/O is enabled
        /// </summary>
        private bool IsTrialIo()
        {
            return gvar.GetWsStorage().GetWsTrialIo().Equals("Y");
        }

        /// <summary>
        /// Get when compiled timestamp
        /// </summary>
        private string GetWhenCompiled()
        {
            return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// Accept current date and time
        /// </summary>
        private void AcceptCurrentDateTime()
        {
            DateTime now = DateTime.Now;
            gvar.GetWsStorage().SetWsNowDateAsString(now.ToString("yyMMdd"));
            gvar.GetWsStorage().SetWsNowTimeAsString(now.ToString("HHmmss"));
        }

        /// <summary>
        /// Set master file names
        /// </summary>
        private void SetMasterFileNames()
        {
            gvar.GetWsCioLink().SetWsCioActAsString("SET-MASTER-FILE-NAMES");
            gvar.GetWsCioLink().SetWsCioNameAsString("XXXXXXXX");

            // Call CIO program with parameter filename
            XCallCioWithParmFn();

            // Call TIO program if different from CIO
            if (!gvar.GetWsStorage().GetWsTioPgm().Equals(gvar.GetWsStorage().GetWsCioPgm()))
            {
                XCallTioWithParmFn();
            }
        }

        /// <summary>
        /// Open message file
        /// </summary>
        private void OpenMessageFile()
        {
            gvar.GetWsCioLink().SetWsCioActAsString("OPEN-OUTPUT");
            gvar.GetWsCioLink().SetWsCioNameAsString("CGTMSG");
            XCallDioWithDispEyMsg();

            if (!gvar.GetWsCioLink().GetWsCioRet().Equals("00"))
            {
                Console.WriteLine("*** ELCGTS20 CANNOT OPEN RUNMSG FILE; RUN ABORTED ***");
                ivar.GetParmInfo().GetParmChars().SetParmChar1AsString("Z");
                gvar.GetWsStorage().SetWsReturnCodeAsString("12");
                return; // GOBACK equivalent
            }
            else
            {
                gvar.GetWsStorage().SetWsMsgOpenAsString("Y");
            }
        }

        /// <summary>
        /// Display startup message
        /// </summary>
        private void DisplayStartupMessage()
        {
            string compileInfo = gvar.GetWsStorage().GetWWhenCompiled();
            string dateInfo = gvar.GetWsStorage().GetWsDateDd() + "/" +
                            gvar.GetWsStorage().GetWsDateMm() + "/" +
                            gvar.GetWsStorage().GetWsDateYy();
            string timeInfo = gvar.GetWsStorage().GetWsTimeHh() + ":" +
                            gvar.GetWsStorage().GetWsTimeSs();

            string startupMessage = "IELCGTS20: V2.4 " + compileInfo + "; RUNTIME " +
                                  dateInfo + " " + timeInfo;

            gvar.GetWsStorage().SetWsEyAsString(startupMessage);
            EyMsgControl();
        }

        /// <summary>
        /// Handle deemed disposals flag
        /// </summary>
        private void HandleDeemedDisposalsFlag()
        {
            string ddText;
            if (IsDeemedDisposalsOn())
            {
                ddText = "On";
            }
            else
            {
                ddText = "Off";
            }
            gvar.GetWsStorage().SetWDdTextAsString(ddText);
        }

        /// <summary>
        /// Display year and report information
        /// </summary>
        private void DisplayYearReportInfo()
        {
            string yearReportMessage = "IYear: " + gvar.GetWsStorage().GetWsParmYy() +
                                     " Report: " + gvar.GetWsStorage().GetWsParmFn8() +
                                     " Deemed: " + gvar.GetWsStorage().GetWDdText();

            gvar.GetWsStorage().SetWsEyAsString(yearReportMessage);
            EyMsgControl();
        }

        /// <summary>
        /// Initialize working table cost entries
        /// </summary>
        private void InitializeWorkingTableCostEntries()
        {
            gvar.GetWiInterface().SetWiWttc(1);
            int wiWttc = gvar.GetWiInterface().GetWiWttc();

            gvar.GetWtTables().SetWttcBFBalanceUnits(wiWttc, 0);
            gvar.GetWtTables().SetWttcBalanceUnitsYtd(wiWttc, 0);
            gvar.GetWtTables().SetWttcBFCostBalance(wiWttc, 0);
            gvar.GetWtTables().SetWttcCostBalanceYtd(wiWttc, 0);
            gvar.GetWtTables().SetWttcApportioning(wiWttc, 0);
            gvar.GetWtTables().SetWttcCostNo(wiWttc, 0);
            gvar.GetWtTables().SetWttcUnitsPresentAsString(wiWttc, " ");
            gvar.GetWtTables().SetWttcCostDateAsString(wiWttc, "000000");
            gvar.GetWtTables().SetWttcIndexDateAsString(wiWttc, "000000");

            // Set pointers
            gvar.GetWtTables().SetWttcPointer(wiWttc, wiWttc);
            gvar.GetWtTables().SetWttcAdjPointer(wiWttc, wiWttc);
            gvar.GetWsStorage().SetWsWttcEndAsString(wiWttc.ToString());
        }

        /// <summary>
        /// Open error file with retry logic
        /// </summary>
        private void OpenErrorFile()
        {
            gvar.GetWsCioLink().SetWsCioActAsString("OPEN-OUTPUT");
            gvar.GetWsCioLink().SetWsCioNameAsString("CGTERR");
            XCallCioWithErrRec();

            // If file status is 41 (file already open), try to close all files from previous failing run
            if (gvar.GetWsCioLink().GetWsCioRet().Equals("41"))
            {
                CloseAllFilesFromPreviousRun();

                // Try to reopen error file
                gvar.GetWsCioLink().SetWsCioActAsString("OPEN-OUTPUT");
                gvar.GetWsCioLink().SetWsCioNameAsString("CGTERR");
                XCallCioWithErrRec();
            }

            if (!gvar.GetWsCioLink().GetWsCioRet().Equals("00"))
            {
                string errorMsg = "XERROR FILE OPEN FAILED -STAT " + gvar.GetWsCioLink().GetWsCioRet();
                gvar.GetWsStorage().SetWsEyAsString(errorMsg);
                gvar.GetWsStorage().SetWsErrflagAsString("F");
                EyMsgControl();
            }
            else
            {
                gvar.GetWsStorage().SetWsErrOpenAsString("Y");
            }
        }

        /// <summary>
        /// Close all files from previous failing run
        /// </summary>
        private void CloseAllFilesFromPreviousRun()
        {
            // Close file operation
            gvar.GetWsCioLink().SetWsCioActAsString("CLOSE-FILE");
            XCallCioWithErrRec();

            // Close offshore files
            CloseOffshoreFiles();

            // Close various CGT files
            CloseCgtFiles();

            // Close master file
            CloseMasterFile();
        }

        /// <summary>
        /// Close offshore files
        /// </summary>
        private void CloseOffshoreFiles()
        {
            // These would be actual file close operations in a real implementation
            // For now, we'll just set the flags
            gvar.GetWsStorage().SetWsOffshoreOpenAsString("N");
            gvar.GetWsStorage().SetWsOffshor2OpenAsString("N");
            gvar.GetWsStorage().SetWsIlostOpenAsString("N");
            gvar.GetWsStorage().SetWsIlosuOpenAsString("N");
        }

        /// <summary>
        /// Close CGT files
        /// </summary>
        private void CloseCgtFiles()
        {
            string[] cgtFiles = { "CGTGLDAT", "CGTDR", "CGTDT", "CGTDP", "CGTERR", "CGTDU", "CGTDN", "CGTDX" };

            foreach (string fileName in cgtFiles)
            {
                gvar.GetWsCioLink().SetWsCioNameAsString(fileName);
                X2CallCioForSchedule();
            }
        }

        /// <summary>
        /// Close master file
        /// </summary>
        private void CloseMasterFile()
        {
            gvar.GetElcgmioLinkage1().SetLActAsString("CLOSE-FILE");

            if (IsPartialRun())
            {
                gvar.GetElcgmioLinkage1().SetLKeyAsString("PARTIAL-COMP");
            }
            else
            {
                gvar.GetElcgmioLinkage1().SetLKeyAsString("FULL-COMP");
            }

            gvar.GetElcgmioLinkage1().SetLNameAsString("CGTMAST");
            X8CallMasterFileHandlerSection();
        }

        /// <summary>
        /// External call methods
        /// </summary>
        private void XCallCioWithParmFn()
        {
            // Implementation for calling CIO with parameter filename
            // This would typically involve external program calls
        }

        private void XCallTioWithParmFn()
        {
            // Implementation for calling TIO with parameter filename
        }

        private void XCallDioWithDispEyMsg()
        {
            // Implementation for calling DIO with display message
        }

        private void XCallCioWithErrRec()
        {
            // Implementation for calling CIO with error record
        }

        private void X2CallCioForSchedule()
        {
            // Implementation for calling CIO for schedule operations
        }

        /// <summary>
        /// Check if print unrealized schedule is enabled
        /// </summary>
        private bool IsPrintUnrealSchedule()
        {
            return gvar.GetWsStorage().GetWsPrintUnrealSchedule().Equals("Y");
        }

        /// <summary>
        /// Open offshore file
        /// </summary>
        private void OpenOffshoreFile()
        {
            // Implementation for opening offshore file
            // This would typically involve file I/O operations
        }

        /// <summary>
        /// Initialize working storage items
        /// </summary>
        private void InitializeWorkingStorageItems()
        {
            gvar.GetWsStorage().SetWsOldFundAsString(" ");
            gvar.GetWsStorage().SetWsOldStockAsString(" ");
            gvar.GetWsStorage().SetWsOldFundDdAsString(" ");
            gvar.GetWsStorage().SetWsOldStockDdAsString(" ");
            gvar.GetWsStorage().SetWsIlostOldFundAsString(" ");
            gvar.GetWsStorage().SetWsIlostOldStockAsString(" ");
            gvar.GetWsStorage().SetWsIlosuOldFundAsString(" ");
            gvar.GetWsStorage().SetWsIlosuOldStockAsString(" ");
        }

        /// <summary>
        /// Initialize components
        /// </summary>
        private void InitializeComponents()
        {
            // Initialize working indices
            gvar.GetWiInterface().SetWiTp(1);

            // Initialize deemed disposals if enabled
            if (IsDeemedDisposalsOn())
            {
                string message = "IDEEMED DISPOSALS PROCESSING IS ON";
                gvar.GetWsStorage().SetWsEyAsString(message);
                EyMsgControl();
            }

            // Initialize cost tables
            gvar.GetWiInterface().SetWiWttc(1);
            gvar.GetWtTables().SetWttcBFBalanceUnits(
                gvar.GetWiInterface().GetWiWttc(), 0);
            gvar.GetWtTables().SetWttcBFCostBalance(
                gvar.GetWiInterface().GetWiWttc(), 0);
        }

        /// <summary>
        /// Check if deemed disposals are enabled
        /// </summary>
        private bool IsDeemedDisposalsOn()
        {
            return gvar.GetWsStorage().GetWsDeemedDisposalsFlag().Equals("Y");
        }

        /// <summary>
        /// BA-LOAD-RPI SECTION - Load Retail Price Index data
        /// PERFORMED BY B
        /// PERFORMS S
        /// </summary>
        private void BaLoadRpiSection()
        {
            // BA-10 paragraph
            Ba10LoadRpiLoop();
        }

        /// <summary>
        /// BA-10 - Main RPI loading loop
        /// </summary>
        private void Ba10LoadRpiLoop()
        {
            while (true)
            {
                // MOVE READ-NEXT TO WS-CIO-ACT
                gvar.GetWsCioLink().SetWsCioActAsString("READ-next");

                // MOVE 'CGTRPI' TO WS-CIO-NAME
                gvar.GetWsCioLink().SetWsCioNameAsString("CGTRPI");

                // CALL WS-CIO-PGM USING WS-CIO-LINK RETAIL-PRICE-INDEX-RECORD
                XCallCioWithRpiRecord();

                // IF WS-CIO-RET1 NOT = 0 GO TO BA-EXIT
                if (gvar.GetWsCioLink().GetWsCioRet1() != 0)
                {
                    break; // BA-EXIT
                }

                // IF WI-R NOT > WTR-MAX-TABLE-SIZE
                if (gvar.GetWiInterface().GetWiR() <= gvar.GetWtRetailPriceIndexes().GetWtrMaxTableSize())
                {
                    // IF RPI-VALUE = 0 - handle zero RPI value
                    if (gvar.GetRetailPriceIndexRecord().GetRpiValue() == 0)
                    {
                        string errorMsg = "ERPI OF ZERO FOR " +
                                        gvar.GetRetailPriceIndexRecord().GetRpiDateYyMm() +
                                        " IGNORED";
                        gvar.GetWsStorage().SetWsEyAsString(errorMsg);
                        EyMsgControl();
                    }
                    else
                    {
                        // Process valid RPI record
                        ProcessValidRpiRecord();

                        // SET WI-R UP BY 1
                        gvar.GetWiInterface().SetWiR(gvar.GetWiInterface().GetWiR() + 1);
                    }
                }
                else
                {
                    // Table overflow error
                    gvar.GetWsStorage().SetWsErrorAsString("1");
                    EzErrors();
                }
            }
        }

        /// <summary>
        /// Process valid RPI record
        /// </summary>
        private void ProcessValidRpiRecord()
        {
            // Y2K: MOVE RPI-DATE-YYMM TO CGTDATE2-YYMMDD1(1:4)
            string rpiDateYyMm = gvar.GetRetailPriceIndexRecord().GetRpiDateYyMm();
            gvar.GetCgtdate2LinkageDate1().SetCgtdate2Yymmdd1AsString(rpiDateYyMm + "01");

            // Y2K: MOVE "01" TO CGTDATE2-DD1
            // Already handled above by appending "01"

            // Y2K: CALL "CGTDATE2" USING CGTDATE2-LINKAGE-DATE1
            CallCgtdate2();

            // Y2K: MOVE CGTDATE2-C-CCYYMM1 TO WTR-DATE (WI-R)
            int wiR = gvar.GetWiInterface().GetWiR();
            gvar.GetWtRetailPriceIndexes().SetWtrDate(wiR,
                gvar.GetCgtdate2LinkageDate1().GetCgtdate2CCcyymm1());

            // MOVE RPI-VALUE TO WTR-VALUE (WI-R)
            gvar.GetWtRetailPriceIndexes().SetWtrValue(wiR,
                gvar.GetRetailPriceIndexRecord().GetRpiValue());

            // MJ1: The RPI-DELETE-FLAG is not used on the micro system so
            // MJ1: in conjunction with BB have used this byte to carry the
            // MJ1: micro estimated byte.
            // MOVE RPI-DELETE-FLAG TO WTR-EST (WI-R)
            gvar.GetWtRetailPriceIndexes().SetWtrEstAsString(wiR,
                gvar.GetRetailPriceIndexRecord().GetRpiDeleteFlag());
        }

        /// <summary>
        /// Call CGTDATE2 for date conversion
        /// </summary>
        private void CallCgtdate2()
        {
            // Implementation for calling CGTDATE2 program
            // This would typically involve date conversion operations
            // For now, we'll simulate the conversion
            string yymmdd = gvar.GetCgtdate2LinkageDate1().GetCgtdate2Yymmdd1();

            // Convert YYMMDD to CCYYMMDD format
            if (yymmdd.Length >= 6)
            {
                string yy = yymmdd.Substring(0, 2);
                string mmdd = yymmdd.Substring(2, 4);

                // Assume 20xx for years 00-29, 19xx for years 30-99
                int year = int.Parse(yy);
                string ccyy = (year <= 29) ? "20" + yy : "19" + yy;

                gvar.GetCgtdate2LinkageDate1().SetCgtdate2CCcyymm1AsString(ccyy + mmdd);
            }
        }

        /// <summary>
        /// EZ-ERRORS - Handle errors
        /// </summary>
        private void EzErrors()
        {
            // Implementation for error handling
            string errorMsg = "TABLE OVERFLOW ERROR IN RPI LOADING";
            gvar.GetWsStorage().SetWsEyAsString(errorMsg);
            gvar.GetWsStorage().SetWsErrflagAsString("F");
            EyMsgControl();
        }

        /// <summary>
        /// External call for RPI record processing
        /// </summary>
        private void XCallCioWithRpiRecord()
        {
            // Implementation for calling CIO with RPI record
            // This would typically involve file I/O operations
        }

        /// <summary>
        /// Initialize master file handling
        /// </summary>
        private void InitializeMasterFileHandling()
        {
            // Open master file for processing
            gvar.GetElcgmioLinkage1().SetLActAsString("open-input");
            gvar.GetElcgmioLinkage1().SetLNameAsString("CGTMAST");
            X8CallMasterFileHandlerSection();

            if (!gvar.GetElcgmioLinkage1().GetLReturnCode().Equals("00"))
            {
                string errorMsg = "XMASTERFILE OPEN FAILED -STAT " +
                                gvar.GetElcgmioLinkage1().GetLReturnCode();
                gvar.GetWsStorage().SetWsEyAsString(errorMsg);
                gvar.GetWsStorage().SetWsErrflagAsString("F");
                EyMsgControl();
            }
            else
            {
                gvar.GetWsStorage().SetWsMfOpenAsString("Y");
            }
        }

        /// <summary>
        /// Initialize price handling
        /// </summary>
        private void InitializePriceHandling()
        {
            // Initialize CGT price handling
            gvar.GetCgtpriceLinkage().SetCgtpriceActionInitialise(true);
            XCallCgtpriceSection();
        }

        #endregion

        #region Core Processing Sections

        /// <summary>
        /// C-PROCESS-FUND SECTION - Process fund data
        /// PERFORMED BY A
        /// PERFORMS CA E
        /// </summary>
        private void CProcessFundSection()
        {
            // MOVE SPACES TO WS-MISSING-UG-RPI-FLAG
            gvar.GetWsStorage().SetWsMissingUgRpiFlagAsString(" ");

            // MOVE SPACES TO WS-ERRFLAG
            gvar.GetWsStorage().SetWsErrflagAsString(" ");

            // MOVE LOW-VALUES TO WS-CURRENT-KEY
            gvar.GetWsStorage().SetWsCurrentKeyAsString("\0");

            // MOVE CF-CO-AC-LK TO WS-CURRENT-CAL
            gvar.GetWsStorage().SetWsCurrentCalAsString(gvar.GetCfRecord().GetCfCoAcLk());

            // IF CF-SUMMARY-FUND
            if (gvar.GetCfRecord().GetCfSummaryFund())
            {
                // MOVE 'S' TO WS-SUMMARY-FUND-FLAG
                gvar.GetWsStorage().SetWsSummaryFundFlagAsString("S");
            }
            else
            {
                // MOVE SPACES TO WS-SUMMARY-FUND-FLAG
                gvar.GetWsStorage().SetWsSummaryFundFlagAsString(" ");
            }

            // SET WI-K TO 1
            gvar.GetWiInterface().SetWiK(1);

            // IF X-FUND-CALC
            if (IsXFundCalc())
            {
                // MOVE WS-X-FUND-SEDOL TO WS-CURRENT-SEDOL
                gvar.GetWsStorage().SetWsCurrentSedolAsString(gvar.GetWsStorage().GetWsXFundSedol());
            }

            // IF MICRO-IO
            if (IsMicroIo())
            {
                HandleDeemedDisposalState();
                DisplayFundProcessingMessage();
            }

            // PERFORM E-PROCESS-SEDOL-FOR-CAL UNTIL WS-ERRFLAG NOT = SPACES
            while (gvar.GetWsStorage().GetWsErrflag().Equals(" "))
            {
                EProcessSedolForCalSection();
            }

            // IF NOT X-FUND-CALC
            if (!IsXFundCalc())
            {
                HandleNonXFundCalcErrors();
            }

            // Get company information
            CaGetCoSection();

            // Set start needed flag for non-V3 versions
            gvar.GetWsStorage().SetWsStartNeededAsString("Y");
        }

        /// <summary>
        /// Handle deemed disposal state evaluation
        /// </summary>
        private void HandleDeemedDisposalState()
        {
            // EVALUATE CF-DEEMED-DISPOSAL-STATE
            int deemedDisposalState = gvar.GetCfRecord().GetCfDeemedDisposalState();
            string ddText;

            switch (deemedDisposalState)
            {
                case 1:
                    ddText = "No O/R)";
                    break;
                case 2:
                    ddText = "On)";
                    break;
                case 3:
                    ddText = "Off)";
                    break;
                default:
                    ddText = "Unknown)";
                    break;
            }

            gvar.GetWsStorage().SetWDdTextAsString(ddText);
        }

        /// <summary>
        /// Display fund processing message
        /// </summary>
        private void DisplayFundProcessingMessage()
        {
            // STRING 'P...PROCESSING FUND ' WS-CURRENT-CAL
            //        '  (Type:' CF-FUND-TYPE
            //        ', Deemed:' W-DD-TEXT
            //   DELIMITED BY SIZE INTO WS-EY
            string processingMessage = "P...PROCESSING FUND " +
                                     gvar.GetWsStorage().GetWsCurrentCal() +
                                     "  (Type:" + gvar.GetCfRecord().GetCfFundType() +
                                     ", Deemed:" + gvar.GetWsStorage().GetWDdText();

            gvar.GetWsStorage().SetWsEyAsString(processingMessage);
            EyMsgControl();
        }

        /// <summary>
        /// Handle non-X-fund calculation errors
        /// </summary>
        private void HandleNonXFundCalcErrors()
        {
            // IF WS-ERRFLAG = 'E'
            if (gvar.GetWsStorage().GetWsErrflag().Equals("E"))
            {
                // MOVE WS-CURRENT-CAL TO W-SEARCH-FUND-CODE
                gvar.GetWsStorage().SetWSearchFundCodeAsString(gvar.GetWsStorage().GetWsCurrentCal());

                // PERFORM E2-IS-FUND-PROCESSED
                E2IsFundProcessed();

                // IF FUND-NOT-PROCESSED
                if (IsFundNotProcessed())
                {
                    CreateErrorRecord();
                }
            }
        }

        /// <summary>
        /// Create error record for unprocessed fund
        /// </summary>
        private void CreateErrorRecord()
        {
            // MOVE SPACES TO WTS-ELEMENT (1)
            gvar.GetWtSchedule().SetWtsElementAsString(1, " ");

            // MOVE '00' TO WTS-RECORD-CODE (1)
            gvar.GetWtSchedule().SetWtsRecordCodeAsString(1, "00");

            // MOVE WS-CURRENT-CAL TO WTS-CO-AC-LK (1)
            gvar.GetWtSchedule().SetWtsCoAcLkAsString(1, gvar.GetWsStorage().GetWsCurrentCal());

            // MOVE 14 TO WTS-ERROR (1)
            gvar.GetWtSchedule().SetWtsError(1, 14);

            // MOVE 0 TO WTS-UNMATCHED-PROCEEDS (1)
            gvar.GetWtSchedule().SetWtsUnmatchedProceeds(1, 0);

            // MOVE 0 TO WTS-UNMATCHED-COST (1)
            gvar.GetWtSchedule().SetWtsUnmatchedCost(1, 0);

            // MOVE 0 TO WTS-UNMATCHED-UNITS (1)
            gvar.GetWtSchedule().SetWtsUnmatchedUnits(1, 0);

            // MOVE 0 TO WTS-30-DAY-UNITS (1)
            gvar.GetWtSchedule().SetWts30DayUnits(1, 0);

            // MOVE 0 TO WTS-FA03-FORWARD-MATCHED-UNITS (1)
            gvar.GetWtSchedule().SetWtsFa03ForwardMatchedUnits(1, 0);

            // SET NO-30-DAY-MATCH(1) TO TRUE
            gvar.GetWtSchedule().SetNo30DayMatch(1, true);

            // SET WTS-SAME-DAY-PRICE-FOUND(1) TO TRUE
            gvar.GetWtSchedule().SetWtsSameDayPriceFound(1, true);

            // ADD 1 TO WS-ERROR-TABLE-NO
            int errorTableNo = gvar.GetWsStorage().GetWsErrorTableNo() + 1;
            gvar.GetWsStorage().SetWsErrorTableNoAsString(errorTableNo.ToString());

            // PERFORM EJ-WRITE-ERRORS
            EjWriteErrors();
        }
                SetFundProcessingFlags();

                // Process fund holdings
                ProcessFundHoldings();

                // Update last fund processed
                gvar.GetWsStorage().SetWsLastFundAsString(
                    gvar.GetWsStorage().GetWsCurrentFund());
            }

            // Get next fund
            CaGetCoSection();
        }

        /// <summary>
        /// Process fund change
        /// </summary>
        private void ProcessFundChange()
        {
            // Clear previous fund data
            ClearPreviousFundData();

            // Initialize new fund data
            InitializeNewFundData();

            // Log fund change
            string message = "PROCESSING FUND: " + gvar.GetWsStorage().GetWsCurrentFund();
            gvar.GetWsStorage().SetWsEyAsString(message);
            EyMsgControl();
        }

        /// <summary>
        /// Check if fund is valid for processing
        /// </summary>
        private bool IsFundValidForProcessing()
        {
            // Check if fund exists in company table
            return gvar.GetWiInterface().GetWiCf() > 0 &&
                   gvar.GetWiInterface().GetWiCf() <= gvar.GetWtTables().GetWtcOccurs();
        }

        /// <summary>
        /// Set fund processing flags
        /// </summary>
        private void SetFundProcessingFlags()
        {
            int wiCf = gvar.GetWiInterface().GetWiCf();

            // Set summary fund flag
            gvar.GetWsStorage().SetWsSummaryFundFlagAsString(
                gvar.GetWtTables().GetWtcSummaryFundFlag(wiCf));

            // Set calculation flag
            gvar.GetWtTables().SetWtcCalAsString(wiCf, "Y");

            // Set fund type flags
            SetFundTypeFlags(wiCf);
        }

        /// <summary>
        /// Set fund type flags
        /// </summary>
        private void SetFundTypeFlags(int fundIndex)
        {
            string fundType = gvar.GetWtTables().GetWtcFundType(fundIndex);

            // Set various fund type flags based on fund type
            switch (fundType)
            {
                case "U":
                    gvar.GetWsStorage().SetWsUnitTrustFlagAsString("Y");
                    break;
                case "I":
                    gvar.GetWsStorage().SetWsInvestmentTrustFlagAsString("Y");
                    break;
                case "O":
                    gvar.GetWsStorage().SetWsOeicFlagAsString("Y");
                    break;
                default:
                    // Default handling
                    break;
            }
        }

        /// <summary>
        /// Process fund holdings
        /// </summary>
        private void ProcessFundHoldings()
        {
            // Initialize holding processing
            InitializeHoldingProcessing();

            // Process each holding in the fund
            while (!IsEndOfHoldings() && !IsFatalError())
            {
                ProcessSingleHolding();
                GetNextHolding();
            }

            // Finalize fund processing
            FinalizeFundProcessing();
        }

        /// <summary>
        /// Initialize holding processing
        /// </summary>
        private void InitializeHoldingProcessing()
        {
            // Reset holding counters
            gvar.GetWiInterface().SetWiH(1);

            // Get first holding
            GetFirstHolding();
        }

        /// <summary>
        /// Check if we've reached end of holdings
        /// </summary>
        private bool IsEndOfHoldings()
        {
            return gvar.GetWsStorage().GetWsCurrentSedol().Equals("HIGH-VALUES") ||
                   string.IsNullOrEmpty(gvar.GetWsStorage().GetWsCurrentSedol());
        }

        /// <summary>
        /// Process a single holding
        /// </summary>
        private void ProcessSingleHolding()
        {
            // Set current SEDOL for processing
            gvar.GetWsStorage().SetWsCurrentCalSedolAsString(
                gvar.GetWsStorage().GetWsCurrentSedol());

            // Process SEDOL for calculation
            EProcessSedolForCalSection();
        }

        /// <summary>
        /// Get first holding
        /// </summary>
        private void GetFirstHolding()
        {
            // Implementation to get first holding from master file
            // This would typically involve reading from the master file
            ReadNextMasterRecord();
        }

        /// <summary>
        /// Get next holding
        /// </summary>
        private void GetNextHolding()
        {
            // Implementation to get next holding
            ReadNextMasterRecord();
        }

        /// <summary>
        /// Read next master record
        /// </summary>
        private void ReadNextMasterRecord()
        {
            gvar.GetElcgmioLinkage1().SetLActAsString("read-next");
            gvar.GetElcgmioLinkage1().SetLNameAsString("CGTMAST");
            X8CallMasterFileHandlerSection();

            if (gvar.GetElcgmioLinkage1().GetLReturnCode().Equals("00"))
            {
                // Process the record
                ProcessMasterRecord();
            }
            else if (gvar.GetElcgmioLinkage1().GetLReturnCode().Equals("10"))
            {
                // End of file
                gvar.GetWsStorage().SetWsCurrentSedolAsString("HIGH-VALUES");
            }
            else
            {
                // Error reading file
                string errorMsg = "ERROR READING MASTER FILE - STAT " +
                                gvar.GetElcgmioLinkage1().GetLReturnCode();
                gvar.GetWsStorage().SetWsEyAsString(errorMsg);
                gvar.GetWsStorage().SetWsErrflagAsString("F");
                EyMsgControl();
            }
        }

        /// <summary>
        /// Process master record
        /// </summary>
        private void ProcessMasterRecord()
        {
            // Extract SEDOL from master record
            gvar.GetWsStorage().SetWsCurrentSedolAsString(
                gvar.GetCgtMasterRecord().GetCgtmSedol());

            // Extract fund from master record
            gvar.GetWsStorage().SetWsCurrentFundAsString(
                gvar.GetCgtMasterRecord().GetCgtmFund());
        }

        #endregion

        #region External Call Sections

        /// <summary>
        /// X-CALL-EQTPATH SECTION - Call EQTPATH program
        /// </summary>
        private void XCallEqtpathSection()
        {
            // Implementation for calling EQTPATH program
            // This would typically involve external program calls
        }

        /// <summary>
        /// X-CALL-CIO SECTION - Call CIO (Common I/O) program
        /// </summary>
        private void XCallCioSection()
        {
            // Implementation for calling CIO program
            // This would typically involve file I/O operations
        }

        /// <summary>
        /// X1-CALL-CGTDATE3 SECTION - Call CGTDATE3 program
        /// </summary>
        private void X1CallCgtdate3Section()
        {
            // Implementation for calling CGTDATE3 program
            // This would typically involve date conversion operations
        }

        /// <summary>
        /// X8-CALL-MASTER-FILE-HANDLER SECTION - Call master file handler
        /// </summary>
        private void X8CallMasterFileHandlerSection()
        {
            // Implementation for calling master file handler
            // This would typically involve master file operations
        }

        /// <summary>
        /// X-CALL-CGTPRICE SECTION - Call CGTPRICE program
        /// </summary>
        private void XCallCgtpriceSection()
        {
            // Implementation for calling CGTPRICE program
            // This would typically involve price calculation operations
        }

        #endregion

        #region Additional Processing Methods

        /// <summary>
        /// CA-GET-CO SECTION - Get company information
        /// PERFORMED BY C
        /// </summary>
        private void CaGetCoSection()
        {
            // CA-10 paragraph
            Ca10GetCompanyLoop();
        }

        /// <summary>
        /// CA-10 - Main company getting loop
        /// </summary>
        private void Ca10GetCompanyLoop()
        {
            while (true)
            {
                // MOVE WS-LAST-FUND TO CF-CO-AC-LK
                gvar.GetCfRecord().SetCfCoAcLkAsString(gvar.GetWsStorage().GetWsLastFund());

                // MOVE START-GREATER-THAN TO WS-CIO-ACT
                gvar.GetWsCioLink().SetWsCioActAsString("START-GREATER-THAN");

                // MOVE USER-FUND-FILE TO WS-CIO-NAME
                gvar.GetWsCioLink().SetWsCioNameAsString(gvar.GetWsStorage().GetUserFundFile());

                // MOVE READ-NEXT TO WS-CIO-ACT
                gvar.GetWsCioLink().SetWsCioActAsString("READ-NEXT");

                // PERFORM X9-CALL-MASTER-FILE-HANDLER
                X9CallMasterFileHandler();

                // IF WS-CIO-RET1 NOT = '0'
                if (!gvar.GetWsCioLink().GetWsCioRet1().ToString().Equals("0"))
                {
                    // MOVE HIGH-VALUES TO CF-CO-AC-LK
                    gvar.GetCfRecord().SetCfCoAcLkAsString("HIGH-VALUES");
                    break;
                }

                // MOVE CF-CO-AC-LK TO WS-LAST-FUND
                gvar.GetWsStorage().SetWsLastFundAsString(gvar.GetCfRecord().GetCfCoAcLk());

                // IF CF-CO-AC-LK NOT = HIGH-VALUES AND NOT = SPACES
                if (!gvar.GetCfRecord().GetCfCoAcLk().Equals("HIGH-VALUES") &&
                    !gvar.GetCfRecord().GetCfCoAcLk().Equals(" "))
                {
                    // IF CF-CALCULATION-REQUEST = '0'
                    if (gvar.GetCfRecord().GetCfCalculationRequest().Equals("0"))
                    {
                        // GO TO CA-10 (continue loop)
                        continue;
                    }
                    else
                    {
                        // PERFORM BBA-SET-CO
                        BbaSetCoSection();
                        break;
                    }
                }
                else
                {
                    break;
                }
            }
        }

        /// <summary>
        /// BBA-SET-CO SECTION - Set company information
        /// PERFORMED BY CA-10
        /// </summary>
        private void BbaSetCoSection()
        {
            // SET WI-CF TO 1
            gvar.GetWiInterface().SetWiCf(1);
            int wiCf = gvar.GetWiInterface().GetWiCf();

            // MOVE CF-CO-AC-LK TO WTC-CAL (WI-CF)
            gvar.GetWtCompany().SetWtcCal(wiCf, gvar.GetCfRecord().GetCfCoAcLk());

            // MOVE CF-PARALLEL-POOLING TO WTC-PARALLEL-POOLING (WI-CF)
            gvar.GetWtCompany().SetWtcParallelPooling(wiCf, gvar.GetCfRecord().GetCfParallelPooling());

            // MOVE CF-PRE-65-FIXED-INTEREST TO WTC-PRE-65-FIXED-INTEREST (WI-CF)
            gvar.GetWtCompany().SetWtcPre65FixedInterest(wiCf, gvar.GetCfRecord().GetCfPre65FixedInterest());

            // MOVE CF-PRE-65-ORDINARY TO WTC-PRE-65-ORDINARY (WI-CF)
            gvar.GetWtCompany().SetWtcPre65Ordinary(wiCf, gvar.GetCfRecord().GetCfPre65Ordinary());

            // IF IRISH-LIFE-FUND
            if (IsIrishLifeFund())
            {
                // SET WTC-NO-SMALL-DSTBNS(WI-CF) TO TRUE
                gvar.GetWtCompany().SetWtcNoSmallDstbns(wiCf, true);
            }
            else
            {
                // MOVE CF-CALCULATION-REQUEST TO WTC-CALCULATION-REQUEST (WI-CF)
                gvar.GetWtCompany().SetWtcCalculationRequest(wiCf, gvar.GetCfRecord().GetCfCalculationRequest());
            }

            // IF CF-SUMMARY-FUND
            if (gvar.GetCfRecord().GetCfSummaryFund())
            {
                // MOVE 'S' TO WTC-SUMMARY-FUND-FLAG (WI-CF)
                gvar.GetWtCompany().SetWtcSummaryFundFlagAsString(wiCf, "S");
            }
            else
            {
                // MOVE SPACES TO WTC-SUMMARY-FUND-FLAG (WI-CF)
                gvar.GetWtCompany().SetWtcSummaryFundFlagAsString(wiCf, " ");
            }

            // IF X-FUND-CALC
            if (IsXFundCalc())
            {
                // MOVE WS-X-FUND-DATE TO CF-CGT-PERIOD-END-DATE
                gvar.GetCfRecord().SetCfCgtPeriodEndDateAsString(gvar.GetWsStorage().GetWsXFundDate());
            }

            // MOVE CF-CGT-PERIOD-START-DATE TO WTC-START-DATE (WI-CF)
            gvar.GetWtCompany().SetWtcStartDate(wiCf, gvar.GetCfRecord().GetCfCgtPeriodStartDate());

            // MOVE CF-CGT-PERIOD-END-DATE TO WTC-END-DATE (WI-CF)
            gvar.GetWtCompany().SetWtcEndDate(wiCf, gvar.GetCfRecord().GetCfCgtPeriodEndDate());

            // MOVE CF-CGT-PERIOD-END-DATE TO WTC-END-PLUS-MONTH-DATE (WI-CF)
            gvar.GetWtCompany().SetWtcEndPlusMonthDate(wiCf, gvar.GetCfRecord().GetCfCgtPeriodEndDate());

            // MOVE CF-CGT-PERIOD-END-DATE TO WTC-END-PLUS-6MTH-DATE (WI-CF)
            gvar.GetWtCompany().SetWtcEndPlus6MthDate(wiCf, gvar.GetCfRecord().GetCfCgtPeriodEndDate());

            // Move additional CF fields to WTC
            gvar.GetWtCompany().SetWtcFundNameAsString(wiCf, gvar.GetCfRecord().GetCfFundName());
            gvar.GetWtCompany().SetWtcFundTypeAsString(wiCf, gvar.GetCfRecord().GetCfFundType());
            gvar.GetWtCompany().SetWtcDeemedDisposalState(wiCf, gvar.GetCfRecord().GetCfDeemedDisposalState());
            gvar.GetWtCompany().SetWtcPriceTypeIdAsString(wiCf, gvar.GetCfRecord().GetCfPriceTypeId());
            gvar.GetWtCompany().SetWtcUseEarlierPrice(wiCf, gvar.GetCfRecord().GetCfUseEarlierPrice());
            gvar.GetWtCompany().SetWtcOlabFund(wiCf, gvar.GetCfRecord().GetCfOlabFund());
            gvar.GetWtCompany().SetWtcLifeSummaryFund(wiCf, gvar.GetCfRecord().GetCfLifeSummaryFund());

            // PERFORM BBAA-LOAD-CALENDAR
            BbaaLoadCalendar();

            // Handle date calculations for end plus month
            HandleEndPlusMonthCalculation(wiCf);

            // Handle date calculations for end plus 6 months
            HandleEndPlus6MonthCalculation(wiCf);
        }

        /// <summary>
        /// Check if Irish Life Fund
        /// </summary>
        private bool IsIrishLifeFund()
        {
            return gvar.GetWsStorage().GetWsIrishLifeFlag().Equals("Y");
        }

        /// <summary>
        /// BBAA-LOAD-CALENDAR - Load calendar information
        /// </summary>
        private void BbaaLoadCalendar()
        {
            // Implementation for loading calendar information
            // This would typically involve calendar-specific operations
        }

        /// <summary>
        /// Handle end plus month date calculation
        /// </summary>
        private void HandleEndPlusMonthCalculation(int wiCf)
        {
            // ADD 1 TO WTC-END-PLUS-MONTH-MM (WI-CF)
            int endPlusMonthMm = gvar.GetWtCompany().GetWtcEndPlusMonthMm(wiCf) + 1;
            gvar.GetWtCompany().SetWtcEndPlusMonthMm(wiCf, endPlusMonthMm);

            // IF WTC-END-PLUS-MONTH-MM (WI-CF) > 12
            if (gvar.GetWtCompany().GetWtcEndPlusMonthMm(wiCf) > 12)
            {
                // MOVE 1 TO WTC-END-PLUS-MONTH-MM (WI-CF)
                gvar.GetWtCompany().SetWtcEndPlusMonthMm(wiCf, 1);

                // Y2K date handling
                HandleY2KDateIncrement(wiCf, "MONTH");
            }
        }

        /// <summary>
        /// Handle end plus 6 month date calculation
        /// </summary>
        private void HandleEndPlus6MonthCalculation(int wiCf)
        {
            // ADD 6 TO WTC-END-PLUS-6MTH-MM (WI-CF)
            int endPlus6MthMm = gvar.GetWtCompany().GetWtcEndPlus6MthMm(wiCf) + 6;
            gvar.GetWtCompany().SetWtcEndPlus6MthMm(wiCf, endPlus6MthMm);

            // IF WTC-END-PLUS-6MTH-MM (WI-CF) > 12
            if (gvar.GetWtCompany().GetWtcEndPlus6MthMm(wiCf) > 12)
            {
                // SUBTRACT 12 FROM WTC-END-PLUS-6MTH-MM (WI-CF)
                int adjustedMonth = gvar.GetWtCompany().GetWtcEndPlus6MthMm(wiCf) - 12;
                gvar.GetWtCompany().SetWtcEndPlus6MthMm(wiCf, adjustedMonth);

                // Y2K date handling
                HandleY2KDateIncrement(wiCf, "6MONTH");
            }
        }

        /// <summary>
        /// Handle Y2K date increment
        /// </summary>
        private void HandleY2KDateIncrement(int wiCf, string dateType)
        {
            string yyValue;

            if (dateType.Equals("MONTH"))
            {
                yyValue = gvar.GetWtCompany().GetWtcEndPlusMonthYy(wiCf).ToString();
            }
            else
            {
                yyValue = gvar.GetWtCompany().GetWtcEndPlus6MthYy(wiCf).ToString();
            }

            // MOVE WTC-END-PLUS-MONTH-YY (WI-CF) TO CGTDATE2-YYMMDD1
            gvar.GetCgtdate2LinkageDate1().SetCgtdate2Yymmdd1AsString(yyValue + "0101");

            // CALL "CGTDATE2" USING CGTDATE2-LINKAGE-DATE1
            CallCgtdate2();

            // ADD 1 TO CGTDATE2-C-CCYY1
            int ccyy = gvar.GetCgtdate2LinkageDate1().GetCgtdate2CCcyy1() + 1;
            gvar.GetCgtdate2LinkageDate1().SetCgtdate2CCcyy1AsString(ccyy.ToString());

            // MOVE CGTDATE2-C-YY1 TO WTC-END-PLUS-MONTH-YY (WI-CF)
            string newYy = gvar.GetCgtdate2LinkageDate1().GetCgtdate2CYy1();

            if (dateType.Equals("MONTH"))
            {
                gvar.GetWtCompany().SetWtcEndPlusMonthYyAsString(wiCf, newYy);
            }
            else
            {
                gvar.GetWtCompany().SetWtcEndPlus6MthYyAsString(wiCf, newYy);
            }
        }
            }
        }

        /// <summary>
        /// Process company record
        /// </summary>
        private void ProcessCompanyRecord()
        {
            // Extract company information from record
            gvar.GetWtCompany().SetCfCoAcLkAsString(
                gvar.GetCgtCompanyRecord().GetCgtcoFund());
        }

        /// <summary>
        /// Handle company read error
        /// </summary>
        private void HandleCompanyReadError()
        {
            string errorMsg = "ERROR READING COMPANY FILE - STAT " +
                            gvar.GetElcgmioLinkage1().GetLReturnCode();
            gvar.GetWsStorage().SetWsEyAsString(errorMsg);
            gvar.GetWsStorage().SetWsErrflagAsString("F");
            EyMsgControl();
        }

        /// <summary>
        /// EI-GET-CF SECTION - Get company fund information
        /// </summary>
        private void EiGetCfSection()
        {
            // Search for fund in company table
            int cfIndex = 1;
            bool foundFund = false;

            while (cfIndex <= gvar.GetWtTables().GetWtcOccurs() && !foundFund)
            {
                if (gvar.GetWtTables().GetWtcFund(cfIndex).Equals(
                    gvar.GetWtCompany().GetCfCoAcLk()))
                {
                    gvar.GetWiInterface().SetWiCf(cfIndex);
                    foundFund = true;
                }
                else
                {
                    cfIndex++;
                }
            }

            if (!foundFund)
            {
                gvar.GetWiInterface().SetWiCf(0);
            }
        }

        /// <summary>
        /// E-PROCESS-SEDOL-FOR-CAL SECTION - Process SEDOL for calculation
        /// </summary>
        private void EProcessSedolForCalSection()
        {
            // Initialize SEDOL processing
            InitializeSedolProcessing();

            // Process transactions for this SEDOL
            ProcessSedolTransactions();

            // Finalize SEDOL processing
            FinalizeSedolProcessing();
        }

        /// <summary>
        /// Initialize SEDOL processing
        /// </summary>
        private void InitializeSedolProcessing()
        {
            // Set up SEDOL-specific processing variables
            gvar.GetWsStorage().SetWsProcessingSedolAsString(
                gvar.GetWsStorage().GetWsCurrentCalSedol());
        }

        /// <summary>
        /// Process SEDOL transactions
        /// </summary>
        private void ProcessSedolTransactions()
        {
            // Implementation for processing transactions for a specific SEDOL
            // This would involve reading transaction records and performing calculations
        }

        /// <summary>
        /// Finalize SEDOL processing
        /// </summary>
        private void FinalizeSedolProcessing()
        {
            // Clean up after SEDOL processing
            gvar.GetWsStorage().SetWsProcessingSedolAsString(" ");
        }

        /// <summary>
        /// Clear previous fund data
        /// </summary>
        private void ClearPreviousFundData()
        {
            // Clear working storage items related to previous fund
            gvar.GetWsStorage().SetWsOldFundAsString(" ");
            gvar.GetWsStorage().SetWsOldStockAsString(" ");
        }

        /// <summary>
        /// Initialize new fund data
        /// </summary>
        private void InitializeNewFundData()
        {
            // Initialize working storage items for new fund
            gvar.GetWsStorage().SetWsCurrentFundAsString(
                gvar.GetWtCompany().GetCfCoAcLk());
        }

        /// <summary>
        /// Finalize fund processing
        /// </summary>
        private void FinalizeFundProcessing()
        {
            // Perform end-of-fund processing
            // This might include writing totals, closing files, etc.
        }

        /// <summary>
        /// Process specific fund
        /// </summary>
        private void ProcessSpecificFund()
        {
            // Implementation for processing a specific fund
            // This would be used for partial runs or specific fund processing
        }

        /// <summary>
        /// Open schedule files
        /// </summary>
        private void OpenScheduleFiles()
        {
            // Implementation for opening schedule files
            gvar.GetWsStorage().SetWsDrOpenAsString("Y");
        }

        /// <summary>
        /// BD-SCHEDULE-HEADER SECTION - Schedule header processing
        /// </summary>
        private void BdScheduleHeaderSection()
        {
            // Implementation for schedule header processing
        }

        /// <summary>
        /// BC-UNR-SCHED-HEADER SECTION - Unrealized schedule header processing
        /// </summary>
        private void BcUnrSchedHeaderSection()
        {
            // Implementation for unrealized schedule header processing
            gvar.GetWsStorage().SetWsDuOpenAsString("Y");
        }

        /// <summary>
        /// BE-MOVE-PARM SECTION - Move parameter character
        /// PERFORMED BY B-INITIALISE
        /// </summary>
        private void BeMoveParamSection(int wsNumber)
        {
            // MOVE PARM-CH (WS-NUMBER) TO WS-PARM-CH (WS-NUMBER)
            string parmChar = ivar.GetParmInfo().GetParmChars().GetParmCh(wsNumber);
            gvar.GetWsStorage().SetWsParmChAsString(wsNumber, parmChar);
        }

        /// <summary>
        /// Close reports and files
        /// </summary>
        private void CloseReportsAndFiles()
        {
            // Implementation for closing all open reports and files
        }

        /// <summary>
        /// D-TERMINATE SECTION - Termination processing
        /// </summary>
        private void DTerminateSection()
        {
            // Close master file if open
            if (gvar.GetWsStorage().GetWsMfOpen().Equals("Y"))
            {
                gvar.GetElcgmioLinkage1().SetLActAsString("close-file");
                gvar.GetElcgmioLinkage1().SetLNameAsString("CGTMAST");
                X8CallMasterFileHandlerSection();
            }

            // Close other files and perform cleanup
            PerformCleanup();
        }

        /// <summary>
        /// DE-TOTAL-CLOSE-RPT SECTION - Total and close reports
        /// </summary>
        private void DeTotalCloseRptSection()
        {
            // Implementation for totaling and closing reports
        }

        /// <summary>
        /// Perform cleanup
        /// </summary>
        private void PerformCleanup()
        {
            // General cleanup operations
            gvar.GetWsStorage().SetWsMfOpenAsString("N");
            gvar.GetWsStorage().SetWsDrOpenAsString("N");
            gvar.GetWsStorage().SetWsDuOpenAsString("N");
        }

        #endregion
    }
}
