using System;
using System.Text;
using EquityProject.Elcgts20DTO;
using EquityProject.CommonDTO;

namespace EquityProject.Elcgts20PGM
{
    /// <summary>
    /// ELCGTS20 - Capital Gains Tax Calculation Program
    /// Converted from COBOL to C#
    /// Original COBOL Program: elcgts20.cbl
    /// </summary>
    public class Elcgts20
    {
        // Main DTO instances
        private Fvar fvar;
        private Gvar gvar;
        private Ivar ivar;

        // Constructor
        public Elcgts20()
        {
            fvar = new Fvar();
            gvar = new Gvar();
            ivar = new Ivar();
        }

        /// <summary>
        /// Main entry point - equivalent to PROCEDURE DIVISION USING PARM-INFO
        /// </summary>
        /// <param name="parmInfo">Parameter information from calling program</param>
        public void Execute(ParmInfo parmInfo)
        {
            try
            {
                // Set parameter information
                ivar.SetParmInfo(parmInfo);

                // Execute main control logic
                AMainlineSection();
            }
            catch (Exception ex)
            {
                // Handle fatal errors
                HandleFatalError(ex);
            }
        }

        /// <summary>
        /// A-MAINLINE SECTION - Main control logic
        /// </summary>
        private void AMainlineSection()
        {
            A10Control();
        }

        /// <summary>
        /// A10-CONTROL - Main control procedure
        /// </summary>
        private void A10Control()
        {
            // Check for vector 'M' and perform EY-RETURN if needed
            if (gvar.GetWsStorage().GetWsVector().Equals("M"))
            {
                EyReturn();
                return;
            }

            // Initialize error flag
            gvar.GetWsStorage().SetWsErrflagAsString(" ");
            gvar.GetWsStorage().SetWsLastRaBaCalSedolAsString(" ");

            // Check parameter length and set parameter character
            if (ivar.GetParmInfo().GetParmLength() != 0)
            {
                gvar.GetWsStorage().SetWsParmChar1AsString(
                    ivar.GetParmInfo().GetParmChars().GetParmChar1());
            }

            // Handle different run types
            if (!IsNormalRun())
            {
                gvar.GetWsStorage().SetDisplayPgmAsString("ELCGTMFF");
                gvar.GetWsStorage().SetWsNowAsString(
                    ivar.GetParmInfo().GetParmDateTimeStamp());
            }

            // Initialize if normal run or first call
            if (IsNormalRun() || IsFirstCall())
            {
                BInitialiseSection();
            }

            // Set start needed flag
            gvar.GetWsStorage().SetWsStartNeededAsString("Y");

            // Initialize working indices
            gvar.GetWiInterface().SetWiK(1);
            gvar.GetWiInterface().SetWiP(1);
            gvar.GetWiInterface().SetWiCf(1);

            // Handle partial run
            if (IsPartialRun())
            {
                string eyMessage = "I" + gvar.GetWsStorage().GetWsParmFn() +
                                 " PARTIAL CALC RUN";
                gvar.GetWsStorage().SetWsEyAsString(eyMessage);
                EyMsgControl();
            }

            // Handle calculation call
            if (IsCalcCall())
            {
                if (!IsXFundCalc())
                {
                    AcOpenInitRptSection();
                }
            }

            // Open schedule files if needed
            if (IsCalcCall() && !IsScheduleFileOpen())
            {
                OpenScheduleFiles();
            }

            // Main processing logic
            if (IsXFundCalc())
            {
                AbCrossFundSedolCalcSection();
            }
            else
            {
                if (IsNormalRun() || IsCalcEvery())
                {
                    // Process funds until end or fatal error
                    while (!IsFatalError() && !IsEndOfFunds())
                    {
                        CProcessFundSection();
                    }
                }
                else
                {
                    // Handle specific fund processing
                    ProcessSpecificFund();
                }
            }

            // Schedule header processing
            if (gvar.GetWtTables().GetWttOccurs() == 0)
            {
                BdScheduleHeaderSection();
            }

            // Close reports and files
            if (IsCloseRpt())
            {
                CloseReportsAndFiles();
            }

            // Handle exit request
            if (IsExitRequest())
            {
                gvar.GetWsStorage().SetWsErrflagAsString("E");
            }

            // Terminate processing
            if (IsNormalRun() || IsLastCall() || IsCalcEvery() || IsFatalError())
            {
                DTerminateSection();
            }

            // Close calculation reports
            if (IsCalcCall())
            {
                DeTotalCloseRptSection();
            }

            // Final goback
            A90Goback();
        }

        /// <summary>
        /// A90-GOBACK - Final exit point
        /// </summary>
        private void A90Goback()
        {
            // Set final parameter character based on error status
            if (IsFatalError())
            {
                ivar.GetParmInfo().GetParmChars().SetParmChar1AsString("Z");
            }
            else
            {
                ivar.GetParmInfo().GetParmChars().SetParmChar1AsString("Q");
            }
        }

        #region Helper Methods for Run Type Checking

        /// <summary>
        /// Check if this is a normal run
        /// </summary>
        private bool IsNormalRun()
        {
            return gvar.GetWsStorage().GetWsParmChar1().Equals("N");
        }

        /// <summary>
        /// Check if this is the first call
        /// </summary>
        private bool IsFirstCall()
        {
            return gvar.GetWsStorage().GetWsParmChar1().Equals("F");
        }

        /// <summary>
        /// Check if this is a partial run
        /// </summary>
        private bool IsPartialRun()
        {
            return gvar.GetWsStorage().GetWsParmChar1().Equals("P");
        }

        /// <summary>
        /// Check if this is a calculation call
        /// </summary>
        private bool IsCalcCall()
        {
            return gvar.GetWsStorage().GetWsParmChar1().Equals("C");
        }

        /// <summary>
        /// Check if this is a calculation every call
        /// </summary>
        private bool IsCalcEvery()
        {
            return gvar.GetWsStorage().GetWsParmChar1().Equals("E");
        }

        /// <summary>
        /// Check if this is the last call
        /// </summary>
        private bool IsLastCall()
        {
            return gvar.GetWsStorage().GetWsParmChar1().Equals("L");
        }

        /// <summary>
        /// Check if this is a cross-fund calculation
        /// </summary>
        private bool IsXFundCalc()
        {
            return gvar.GetWsStorage().GetWsParmChar1().Equals("X");
        }

        /// <summary>
        /// Check if close report is requested
        /// </summary>
        private bool IsCloseRpt()
        {
            return gvar.GetWsStorage().GetWsParmChar1().Equals("R");
        }

        /// <summary>
        /// Check if exit is requested
        /// </summary>
        private bool IsExitRequest()
        {
            return gvar.GetWsStorage().GetWsErrflag().Equals("E");
        }

        /// <summary>
        /// Check if there's a fatal error
        /// </summary>
        private bool IsFatalError()
        {
            return gvar.GetWsStorage().GetWsErrflag().Equals("F");
        }

        /// <summary>
        /// Check if schedule file is open
        /// </summary>
        private bool IsScheduleFileOpen()
        {
            return gvar.GetWsStorage().GetWsDrOpen().Equals("Y");
        }

        /// <summary>
        /// Check if we've reached end of funds
        /// </summary>
        private bool IsEndOfFunds()
        {
            return gvar.GetWtCompany().GetCfCoAcLk().Equals("HIGH-VALUES");
        }

        #endregion

        #region Error Handling

        /// <summary>
        /// Handle fatal errors
        /// </summary>
        private void HandleFatalError(Exception ex)
        {
            gvar.GetWsStorage().SetWsErrflagAsString("F");
            string errorMessage = "FATAL ERROR: " + ex.Message;
            gvar.GetWsStorage().SetWsEyAsString(errorMessage);
            EyMsgControl();
        }

        /// <summary>
        /// EY-RETURN - Early return processing
        /// </summary>
        private void EyReturn()
        {
            // Implementation for early return logic
            // This would typically involve cleanup and exit
        }

        /// <summary>
        /// EY-MSG-CONTROL - Message control processing
        /// </summary>
        private void EyMsgControl()
        {
            // Implementation for message control
            // This would typically log or display messages
            string message = gvar.GetWsStorage().GetWsEy();
            if (!string.IsNullOrEmpty(message))
            {
                // Log or process the message
                Console.WriteLine(message);
            }
        }

        #endregion

        #region Main Processing Sections

        /// <summary>
        /// AB-CROSS-FUND-SEDOL-CALC SECTION - Cross fund SEDOL calculation
        /// </summary>
        private void AbCrossFundSedolCalcSection()
        {
            // Move parameters for cross-fund calculation
            gvar.GetWsStorage().SetWsXFundKeyAsString(
                ivar.GetParmInfo().GetParmCalSedol());
            gvar.GetWsStorage().SetWsXFundDateAsString(
                ivar.GetParmInfo().GetParmXFundDate());
            gvar.GetWsStorage().SetWsXFundPriceAsString(
                ivar.GetParmInfo().GetParmXFundPrice());

            // Open unrealized schedule header if needed
            if (!gvar.GetWsStorage().GetWsDuOpen().Equals("Y"))
            {
                BcUnrSchedHeaderSection();
            }

            // Process cross-fund logic
            if (!string.IsNullOrEmpty(gvar.GetWsStorage().GetWsXFundFund()))
            {
                gvar.GetWtCompany().SetCfCoAcLkAsString(
                    gvar.GetWsStorage().GetWsXFundFund());
                EiGetCfSection();

                if (gvar.GetWtTables().GetWtcCal(gvar.GetWiInterface().GetWiCf())
                    .Equals(gvar.GetWsStorage().GetWsXFundFund()))
                {
                    gvar.GetWsStorage().SetWsSummaryFundFlagAsString(
                        gvar.GetWtTables().GetWtcSummaryFundFlag(
                            gvar.GetWiInterface().GetWiCf()));

                    gvar.GetWsStorage().SetWsCurrentCalSedolAsString(
                        gvar.GetWsStorage().GetWsXFundKey());

                    // Process SEDOL for calculation
                    while (!gvar.GetWsStorage().GetWsCurrentContractNo().Equals("LOW-VALUES")
                           && !IsFatalError())
                    {
                        EProcessSedolForCalSection();
                    }
                }
                else
                {
                    // Handle case where fund doesn't match
                    if (string.IsNullOrEmpty(gvar.GetWsStorage().GetWsLastFund()) ||
                        gvar.GetWsStorage().GetWsLastFund().Equals("LOW-VALUES"))
                    {
                        CaGetCoSection();
                    }

                    // Process fund until end or fatal error
                    while (!gvar.GetWtCompany().GetCfCoAcLk().Equals("HIGH-VALUES")
                           && !IsFatalError())
                    {
                        CProcessFundSection();
                    }
                }
            }

            // Clear calculation flag
            gvar.GetWtTables().SetWtcCalAsString(
                gvar.GetWiInterface().GetWiCf(), " ");
        }

        /// <summary>
        /// AC-OPEN-INIT-RPT SECTION - Open and initialize report files
        /// </summary>
        private void AcOpenInitRptSection()
        {
            try
            {
                // Set up file names for micro I/O
                if (IsMicroIo())
                {
                    fvar.GetIlostRecord().SetIlostUserNoAsString(
                        gvar.GetWsStorage().GetWsParmFn1To5());
                    fvar.GetIlosuRecord().SetIlosuUserNoAsString(
                        gvar.GetWsStorage().GetWsParmFn1To5());
                }

                // Set up offshore report file paths
                gvar.GetEqtpathLinkage().SetEqtpathFileNameAsString(
                    fvar.GetOffshoreRptFile().GetOffshoreRptFile());
                XCallEqtpathSection();
                fvar.GetOffshoreRptFile().SetOffshoreRptAsString(
                    gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

                gvar.GetEqtpathLinkage().SetEqtpathFileNameAsString(
                    fvar.GetOffshoreRptFileDd().GetOffshoreRptFileDd());
                XCallEqtpathSection();
                fvar.GetOffshoreRptFileDd().SetOffshoreRptDdAsString(
                    gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

                // Set up ILOST report file path
                gvar.GetEqtpathLinkage().SetEqtpathFileNameAsString(
                    fvar.GetIlostReportFile().GetIlostReportFile());
                XCallEqtpathSection();
                fvar.GetIlostReportFile().SetIlostReportAsString(
                    gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

                // Set up ILOSU report file path
                gvar.GetEqtpathLinkage().SetEqtpathFileNameAsString(
                    fvar.GetIlosuReportFile().GetIlosuReportFile());
                XCallEqtpathSection();
                fvar.GetIlosuReportFile().SetIlosuReportAsString(
                    gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

                // Open offshore file
                OpenOffshoreFile();

                // Initialize working storage items
                InitializeWorkingStorageItems();
            }
            catch (Exception ex)
            {
                gvar.GetWsStorage().SetWsErrflagAsString("F");
                gvar.GetWsStorage().SetWsEyAsString("ERROR OPENING REPORT FILES: " + ex.Message);
                EyMsgControl();
            }
        }

        /// <summary>
        /// B-INITIALISE SECTION - Main initialization
        /// </summary>
        private void BInitialiseSection()
        {
            // Move parameters
            for (int wsNumber = 1; wsNumber <= 24 && wsNumber <= ivar.GetParmInfo().GetParmLength(); wsNumber++)
            {
                BeMoveParamSection(wsNumber);
            }

            // Initialize various components
            InitializeComponents();

            // Load RPI (Retail Price Index) data
            BaLoadRpiSection();

            // Initialize master file handling
            InitializeMasterFileHandling();

            // Get company information
            CaGetCoSection();

            // Initialize schedule headers
            if (IsNormalRun())
            {
                BdScheduleHeaderSection();
            }

            if (IsNormalRun() && IsPrintUnrealSchedule())
            {
                BcUnrSchedHeaderSection();
            }

            // Initialize price handling
            InitializePriceHandling();
        }

        #endregion

        #region Supporting Methods

        /// <summary>
        /// Check if micro I/O is enabled
        /// </summary>
        private bool IsMicroIo()
        {
            return gvar.GetWsStorage().GetWsMicroIo().Equals("Y");
        }

        /// <summary>
        /// Check if print unrealized schedule is enabled
        /// </summary>
        private bool IsPrintUnrealSchedule()
        {
            return gvar.GetWsStorage().GetWsPrintUnrealSchedule().Equals("Y");
        }

        /// <summary>
        /// Open offshore file
        /// </summary>
        private void OpenOffshoreFile()
        {
            // Implementation for opening offshore file
            // This would typically involve file I/O operations
        }

        /// <summary>
        /// Initialize working storage items
        /// </summary>
        private void InitializeWorkingStorageItems()
        {
            gvar.GetWsStorage().SetWsOldFundAsString(" ");
            gvar.GetWsStorage().SetWsOldStockAsString(" ");
            gvar.GetWsStorage().SetWsOldFundDdAsString(" ");
            gvar.GetWsStorage().SetWsOldStockDdAsString(" ");
            gvar.GetWsStorage().SetWsIlostOldFundAsString(" ");
            gvar.GetWsStorage().SetWsIlostOldStockAsString(" ");
            gvar.GetWsStorage().SetWsIlosuOldFundAsString(" ");
            gvar.GetWsStorage().SetWsIlosuOldStockAsString(" ");
        }

        /// <summary>
        /// Initialize components
        /// </summary>
        private void InitializeComponents()
        {
            // Initialize working indices
            gvar.GetWiInterface().SetWiTp(1);

            // Initialize deemed disposals if enabled
            if (IsDeemedDisposalsOn())
            {
                string message = "IDEEMED DISPOSALS PROCESSING IS ON";
                gvar.GetWsStorage().SetWsEyAsString(message);
                EyMsgControl();
            }

            // Initialize cost tables
            gvar.GetWiInterface().SetWiWttc(1);
            gvar.GetWtTables().SetWttcBFBalanceUnits(
                gvar.GetWiInterface().GetWiWttc(), 0);
            gvar.GetWtTables().SetWttcBFCostBalance(
                gvar.GetWiInterface().GetWiWttc(), 0);
        }

        /// <summary>
        /// Check if deemed disposals are enabled
        /// </summary>
        private bool IsDeemedDisposalsOn()
        {
            return gvar.GetWsStorage().GetWsDeemedDisposalsFlag().Equals("Y");
        }

        /// <summary>
        /// BA-LOAD-RPI SECTION - Load Retail Price Index data
        /// </summary>
        private void BaLoadRpiSection()
        {
            // Set up for reading RPI file
            gvar.GetWsCioLink().SetWsCioActAsString("READ-next");
            gvar.GetWsCioLink().SetWsCioNameAsString("CGTRPI");

            // Read RPI records until end of file
            while (gvar.GetWsCioLink().GetWsCioRet().Equals("00"))
            {
                XCallCioSection();

                if (!gvar.GetWsCioLink().GetWsCioRet().Equals("00"))
                {
                    break;
                }

                // Process RPI record
                ProcessRpiRecord();

                // Increment RPI index
                gvar.GetWiInterface().SetWiR(gvar.GetWiInterface().GetWiR() + 1);
            }

            // Close RPI file
            gvar.GetWsCioLink().SetWsCioActAsString("close-file");
            gvar.GetWsCioLink().SetWsCioNameAsString("CGTRPI");
            XCallCioSection();
        }

        /// <summary>
        /// Process RPI record
        /// </summary>
        private void ProcessRpiRecord()
        {
            // Convert RPI date format
            string rpiDateYyMm = gvar.GetRetailPriceIndexRecord().GetRpiDateYyMm();
            gvar.GetCgtdate2LinkageDate1().SetCgtdate2Yymmdd1AsString(rpiDateYyMm + "01");

            // Call date conversion
            X1CallCgtdate3Section();

            // Store RPI data in working tables
            int wiR = gvar.GetWiInterface().GetWiR();
            gvar.GetWtRetailPriceIndexes().SetWtrpiDateCcyymmdd(wiR,
                gvar.GetCgtdate2LinkageDate1().GetCgtdate2Ccyymmdd1());
            gvar.GetWtRetailPriceIndexes().SetWtrpiIndex(wiR,
                gvar.GetRetailPriceIndexRecord().GetRpiIndex());
        }

        /// <summary>
        /// Initialize master file handling
        /// </summary>
        private void InitializeMasterFileHandling()
        {
            // Open master file for processing
            gvar.GetElcgmioLinkage1().SetLActAsString("open-input");
            gvar.GetElcgmioLinkage1().SetLNameAsString("CGTMAST");
            X8CallMasterFileHandlerSection();

            if (!gvar.GetElcgmioLinkage1().GetLReturnCode().Equals("00"))
            {
                string errorMsg = "XMASTERFILE OPEN FAILED -STAT " +
                                gvar.GetElcgmioLinkage1().GetLReturnCode();
                gvar.GetWsStorage().SetWsEyAsString(errorMsg);
                gvar.GetWsStorage().SetWsErrflagAsString("F");
                EyMsgControl();
            }
            else
            {
                gvar.GetWsStorage().SetWsMfOpenAsString("Y");
            }
        }

        /// <summary>
        /// Initialize price handling
        /// </summary>
        private void InitializePriceHandling()
        {
            // Initialize CGT price handling
            gvar.GetCgtpriceLinkage().SetCgtpriceActionInitialise(true);
            XCallCgtpriceSection();
        }

        #endregion

        #region Core Processing Sections

        /// <summary>
        /// C-PROCESS-FUND SECTION - Main fund processing
        /// </summary>
        private void CProcessFundSection()
        {
            // Initialize fund processing
            gvar.GetWsStorage().SetWsCurrentFundAsString(
                gvar.GetWtCompany().GetCfCoAcLk());

            // Check if fund has changed
            if (!gvar.GetWsStorage().GetWsCurrentFund().Equals(
                gvar.GetWsStorage().GetWsLastFund()))
            {
                // Process fund change
                ProcessFundChange();
            }

            // Get fund information
            EiGetCfSection();

            // Check if fund is valid for processing
            if (IsFundValidForProcessing())
            {
                // Set fund processing flags
                SetFundProcessingFlags();

                // Process fund holdings
                ProcessFundHoldings();

                // Update last fund processed
                gvar.GetWsStorage().SetWsLastFundAsString(
                    gvar.GetWsStorage().GetWsCurrentFund());
            }

            // Get next fund
            CaGetCoSection();
        }

        /// <summary>
        /// Process fund change
        /// </summary>
        private void ProcessFundChange()
        {
            // Clear previous fund data
            ClearPreviousFundData();

            // Initialize new fund data
            InitializeNewFundData();

            // Log fund change
            string message = "PROCESSING FUND: " + gvar.GetWsStorage().GetWsCurrentFund();
            gvar.GetWsStorage().SetWsEyAsString(message);
            EyMsgControl();
        }

        /// <summary>
        /// Check if fund is valid for processing
        /// </summary>
        private bool IsFundValidForProcessing()
        {
            // Check if fund exists in company table
            return gvar.GetWiInterface().GetWiCf() > 0 &&
                   gvar.GetWiInterface().GetWiCf() <= gvar.GetWtTables().GetWtcOccurs();
        }

        /// <summary>
        /// Set fund processing flags
        /// </summary>
        private void SetFundProcessingFlags()
        {
            int wiCf = gvar.GetWiInterface().GetWiCf();

            // Set summary fund flag
            gvar.GetWsStorage().SetWsSummaryFundFlagAsString(
                gvar.GetWtTables().GetWtcSummaryFundFlag(wiCf));

            // Set calculation flag
            gvar.GetWtTables().SetWtcCalAsString(wiCf, "Y");

            // Set fund type flags
            SetFundTypeFlags(wiCf);
        }

        /// <summary>
        /// Set fund type flags
        /// </summary>
        private void SetFundTypeFlags(int fundIndex)
        {
            string fundType = gvar.GetWtTables().GetWtcFundType(fundIndex);

            // Set various fund type flags based on fund type
            switch (fundType)
            {
                case "U":
                    gvar.GetWsStorage().SetWsUnitTrustFlagAsString("Y");
                    break;
                case "I":
                    gvar.GetWsStorage().SetWsInvestmentTrustFlagAsString("Y");
                    break;
                case "O":
                    gvar.GetWsStorage().SetWsOeicFlagAsString("Y");
                    break;
                default:
                    // Default handling
                    break;
            }
        }

        /// <summary>
        /// Process fund holdings
        /// </summary>
        private void ProcessFundHoldings()
        {
            // Initialize holding processing
            InitializeHoldingProcessing();

            // Process each holding in the fund
            while (!IsEndOfHoldings() && !IsFatalError())
            {
                ProcessSingleHolding();
                GetNextHolding();
            }

            // Finalize fund processing
            FinalizeFundProcessing();
        }

        /// <summary>
        /// Initialize holding processing
        /// </summary>
        private void InitializeHoldingProcessing()
        {
            // Reset holding counters
            gvar.GetWiInterface().SetWiH(1);

            // Get first holding
            GetFirstHolding();
        }

        /// <summary>
        /// Check if we've reached end of holdings
        /// </summary>
        private bool IsEndOfHoldings()
        {
            return gvar.GetWsStorage().GetWsCurrentSedol().Equals("HIGH-VALUES") ||
                   string.IsNullOrEmpty(gvar.GetWsStorage().GetWsCurrentSedol());
        }

        /// <summary>
        /// Process a single holding
        /// </summary>
        private void ProcessSingleHolding()
        {
            // Set current SEDOL for processing
            gvar.GetWsStorage().SetWsCurrentCalSedolAsString(
                gvar.GetWsStorage().GetWsCurrentSedol());

            // Process SEDOL for calculation
            EProcessSedolForCalSection();
        }

        /// <summary>
        /// Get first holding
        /// </summary>
        private void GetFirstHolding()
        {
            // Implementation to get first holding from master file
            // This would typically involve reading from the master file
            ReadNextMasterRecord();
        }

        /// <summary>
        /// Get next holding
        /// </summary>
        private void GetNextHolding()
        {
            // Implementation to get next holding
            ReadNextMasterRecord();
        }

        /// <summary>
        /// Read next master record
        /// </summary>
        private void ReadNextMasterRecord()
        {
            gvar.GetElcgmioLinkage1().SetLActAsString("read-next");
            gvar.GetElcgmioLinkage1().SetLNameAsString("CGTMAST");
            X8CallMasterFileHandlerSection();

            if (gvar.GetElcgmioLinkage1().GetLReturnCode().Equals("00"))
            {
                // Process the record
                ProcessMasterRecord();
            }
            else if (gvar.GetElcgmioLinkage1().GetLReturnCode().Equals("10"))
            {
                // End of file
                gvar.GetWsStorage().SetWsCurrentSedolAsString("HIGH-VALUES");
            }
            else
            {
                // Error reading file
                string errorMsg = "ERROR READING MASTER FILE - STAT " +
                                gvar.GetElcgmioLinkage1().GetLReturnCode();
                gvar.GetWsStorage().SetWsEyAsString(errorMsg);
                gvar.GetWsStorage().SetWsErrflagAsString("F");
                EyMsgControl();
            }
        }

        /// <summary>
        /// Process master record
        /// </summary>
        private void ProcessMasterRecord()
        {
            // Extract SEDOL from master record
            gvar.GetWsStorage().SetWsCurrentSedolAsString(
                gvar.GetCgtMasterRecord().GetCgtmSedol());

            // Extract fund from master record
            gvar.GetWsStorage().SetWsCurrentFundAsString(
                gvar.GetCgtMasterRecord().GetCgtmFund());
        }

        #endregion

        #region External Call Sections

        /// <summary>
        /// X-CALL-EQTPATH SECTION - Call EQTPATH program
        /// </summary>
        private void XCallEqtpathSection()
        {
            // Implementation for calling EQTPATH program
            // This would typically involve external program calls
        }

        /// <summary>
        /// X-CALL-CIO SECTION - Call CIO (Common I/O) program
        /// </summary>
        private void XCallCioSection()
        {
            // Implementation for calling CIO program
            // This would typically involve file I/O operations
        }

        /// <summary>
        /// X1-CALL-CGTDATE3 SECTION - Call CGTDATE3 program
        /// </summary>
        private void X1CallCgtdate3Section()
        {
            // Implementation for calling CGTDATE3 program
            // This would typically involve date conversion operations
        }

        /// <summary>
        /// X8-CALL-MASTER-FILE-HANDLER SECTION - Call master file handler
        /// </summary>
        private void X8CallMasterFileHandlerSection()
        {
            // Implementation for calling master file handler
            // This would typically involve master file operations
        }

        /// <summary>
        /// X-CALL-CGTPRICE SECTION - Call CGTPRICE program
        /// </summary>
        private void XCallCgtpriceSection()
        {
            // Implementation for calling CGTPRICE program
            // This would typically involve price calculation operations
        }

        #endregion

        #region Additional Processing Methods

        /// <summary>
        /// CA-GET-CO SECTION - Get company information
        /// </summary>
        private void CaGetCoSection()
        {
            // Read next company record
            gvar.GetElcgmioLinkage1().SetLActAsString("read-next");
            gvar.GetElcgmioLinkage1().SetLNameAsString("CGTCO");
            X8CallMasterFileHandlerSection();

            if (gvar.GetElcgmioLinkage1().GetLReturnCode().Equals("00"))
            {
                // Process company record
                ProcessCompanyRecord();
            }
            else if (gvar.GetElcgmioLinkage1().GetLReturnCode().Equals("10"))
            {
                // End of file
                gvar.GetWtCompany().SetCfCoAcLkAsString("HIGH-VALUES");
            }
            else
            {
                // Error reading file
                HandleCompanyReadError();
            }
        }

        /// <summary>
        /// Process company record
        /// </summary>
        private void ProcessCompanyRecord()
        {
            // Extract company information from record
            gvar.GetWtCompany().SetCfCoAcLkAsString(
                gvar.GetCgtCompanyRecord().GetCgtcoFund());
        }

        /// <summary>
        /// Handle company read error
        /// </summary>
        private void HandleCompanyReadError()
        {
            string errorMsg = "ERROR READING COMPANY FILE - STAT " +
                            gvar.GetElcgmioLinkage1().GetLReturnCode();
            gvar.GetWsStorage().SetWsEyAsString(errorMsg);
            gvar.GetWsStorage().SetWsErrflagAsString("F");
            EyMsgControl();
        }

        /// <summary>
        /// EI-GET-CF SECTION - Get company fund information
        /// </summary>
        private void EiGetCfSection()
        {
            // Search for fund in company table
            int cfIndex = 1;
            bool foundFund = false;

            while (cfIndex <= gvar.GetWtTables().GetWtcOccurs() && !foundFund)
            {
                if (gvar.GetWtTables().GetWtcFund(cfIndex).Equals(
                    gvar.GetWtCompany().GetCfCoAcLk()))
                {
                    gvar.GetWiInterface().SetWiCf(cfIndex);
                    foundFund = true;
                }
                else
                {
                    cfIndex++;
                }
            }

            if (!foundFund)
            {
                gvar.GetWiInterface().SetWiCf(0);
            }
        }

        /// <summary>
        /// E-PROCESS-SEDOL-FOR-CAL SECTION - Process SEDOL for calculation
        /// </summary>
        private void EProcessSedolForCalSection()
        {
            // Initialize SEDOL processing
            InitializeSedolProcessing();

            // Process transactions for this SEDOL
            ProcessSedolTransactions();

            // Finalize SEDOL processing
            FinalizeSedolProcessing();
        }

        /// <summary>
        /// Initialize SEDOL processing
        /// </summary>
        private void InitializeSedolProcessing()
        {
            // Set up SEDOL-specific processing variables
            gvar.GetWsStorage().SetWsProcessingSedolAsString(
                gvar.GetWsStorage().GetWsCurrentCalSedol());
        }

        /// <summary>
        /// Process SEDOL transactions
        /// </summary>
        private void ProcessSedolTransactions()
        {
            // Implementation for processing transactions for a specific SEDOL
            // This would involve reading transaction records and performing calculations
        }

        /// <summary>
        /// Finalize SEDOL processing
        /// </summary>
        private void FinalizeSedolProcessing()
        {
            // Clean up after SEDOL processing
            gvar.GetWsStorage().SetWsProcessingSedolAsString(" ");
        }

        /// <summary>
        /// Clear previous fund data
        /// </summary>
        private void ClearPreviousFundData()
        {
            // Clear working storage items related to previous fund
            gvar.GetWsStorage().SetWsOldFundAsString(" ");
            gvar.GetWsStorage().SetWsOldStockAsString(" ");
        }

        /// <summary>
        /// Initialize new fund data
        /// </summary>
        private void InitializeNewFundData()
        {
            // Initialize working storage items for new fund
            gvar.GetWsStorage().SetWsCurrentFundAsString(
                gvar.GetWtCompany().GetCfCoAcLk());
        }

        /// <summary>
        /// Finalize fund processing
        /// </summary>
        private void FinalizeFundProcessing()
        {
            // Perform end-of-fund processing
            // This might include writing totals, closing files, etc.
        }

        /// <summary>
        /// Process specific fund
        /// </summary>
        private void ProcessSpecificFund()
        {
            // Implementation for processing a specific fund
            // This would be used for partial runs or specific fund processing
        }

        /// <summary>
        /// Open schedule files
        /// </summary>
        private void OpenScheduleFiles()
        {
            // Implementation for opening schedule files
            gvar.GetWsStorage().SetWsDrOpenAsString("Y");
        }

        /// <summary>
        /// BD-SCHEDULE-HEADER SECTION - Schedule header processing
        /// </summary>
        private void BdScheduleHeaderSection()
        {
            // Implementation for schedule header processing
        }

        /// <summary>
        /// BC-UNR-SCHED-HEADER SECTION - Unrealized schedule header processing
        /// </summary>
        private void BcUnrSchedHeaderSection()
        {
            // Implementation for unrealized schedule header processing
            gvar.GetWsStorage().SetWsDuOpenAsString("Y");
        }

        /// <summary>
        /// BE-MOVE-PARAM SECTION - Move parameter
        /// </summary>
        private void BeMoveParamSection(int paramNumber)
        {
            // Implementation for moving parameters
            // This would extract specific parameter values based on paramNumber
        }

        /// <summary>
        /// Close reports and files
        /// </summary>
        private void CloseReportsAndFiles()
        {
            // Implementation for closing all open reports and files
        }

        /// <summary>
        /// D-TERMINATE SECTION - Termination processing
        /// </summary>
        private void DTerminateSection()
        {
            // Close master file if open
            if (gvar.GetWsStorage().GetWsMfOpen().Equals("Y"))
            {
                gvar.GetElcgmioLinkage1().SetLActAsString("close-file");
                gvar.GetElcgmioLinkage1().SetLNameAsString("CGTMAST");
                X8CallMasterFileHandlerSection();
            }

            // Close other files and perform cleanup
            PerformCleanup();
        }

        /// <summary>
        /// DE-TOTAL-CLOSE-RPT SECTION - Total and close reports
        /// </summary>
        private void DeTotalCloseRptSection()
        {
            // Implementation for totaling and closing reports
        }

        /// <summary>
        /// Perform cleanup
        /// </summary>
        private void PerformCleanup()
        {
            // General cleanup operations
            gvar.GetWsStorage().SetWsMfOpenAsString("N");
            gvar.GetWsStorage().SetWsDrOpenAsString("N");
            gvar.GetWsStorage().SetWsDuOpenAsString("N");
        }

        #endregion
    }
}
