using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D70HeaderRecord Data Structure

public class D70HeaderRecord
{
    private static int _size = 63;
    // [DEBUG] Class: D70HeaderRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler82, is_external=, is_static_class=False, static_prefix=
    private string _Filler82 ="";
    
    
    // 88-level condition checks for Filler82
    public bool IsD70HeaderFound()
    {
        if (this._Filler82 == "'0000000MICRO CGT PRICE HDR'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D70HeaderDate, is_external=, is_static_class=False, static_prefix=
    private int _D70HeaderDate =0;
    
    
    
    
    // [DEBUG] Field: D70HeaderId, is_external=, is_static_class=False, static_prefix=
    private string _D70HeaderId ="";
    
    
    
    
    
    // Serialization methods
    public string GetD70HeaderRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler82.PadRight(27));
        result.Append(_D70HeaderDate.ToString().PadLeft(6, '0'));
        result.Append(_D70HeaderId.PadRight(30));
        
        return result.ToString();
    }
    
    public void SetD70HeaderRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 27 <= data.Length)
        {
            string extracted = data.Substring(offset, 27).Trim();
            SetFiller82(extracted);
        }
        offset += 27;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD70HeaderDate(parsedInt);
        }
        offset += 6;
        if (offset + 30 <= data.Length)
        {
            string extracted = data.Substring(offset, 30).Trim();
            SetD70HeaderId(extracted);
        }
        offset += 30;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD70HeaderRecordAsString();
    }
    // Set<>String Override function
    public void SetD70HeaderRecord(string value)
    {
        SetD70HeaderRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller82()
    {
        return _Filler82;
    }
    
    // Standard Setter
    public void SetFiller82(string value)
    {
        _Filler82 = value;
    }
    
    // Get<>AsString()
    public string GetFiller82AsString()
    {
        return _Filler82.PadRight(27);
    }
    
    // Set<>AsString()
    public void SetFiller82AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler82 = value;
    }
    
    // Standard Getter
    public int GetD70HeaderDate()
    {
        return _D70HeaderDate;
    }
    
    // Standard Setter
    public void SetD70HeaderDate(int value)
    {
        _D70HeaderDate = value;
    }
    
    // Get<>AsString()
    public string GetD70HeaderDateAsString()
    {
        return _D70HeaderDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD70HeaderDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D70HeaderDate = parsed;
    }
    
    // Standard Getter
    public string GetD70HeaderId()
    {
        return _D70HeaderId;
    }
    
    // Standard Setter
    public void SetD70HeaderId(string value)
    {
        _D70HeaderId = value;
    }
    
    // Get<>AsString()
    public string GetD70HeaderIdAsString()
    {
        return _D70HeaderId.PadRight(30);
    }
    
    // Set<>AsString()
    public void SetD70HeaderIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D70HeaderId = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}