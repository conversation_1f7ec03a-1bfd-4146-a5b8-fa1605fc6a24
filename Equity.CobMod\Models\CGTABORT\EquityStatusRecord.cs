using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtabortDTO
{// DTO class representing EquityStatusRecord Data Structure

public class EquityStatusRecord
{
    private static int _size = 6;
    // [DEBUG] Class: EquityStatusRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: EquityStatus, is_external=, is_static_class=False, static_prefix=
    private int _EquityStatus =0;
    
    
    
    
    // [DEBUG] Field: EquityErrorWarningFlag, is_external=, is_static_class=False, static_prefix=
    private string _EquityErrorWarningFlag ="";
    
    
    
    
    // [DEBUG] Field: EquityMessage, is_external=, is_static_class=False, static_prefix=
    private string _EquityMessage ="";
    
    
    
    
    
    // Serialization methods
    public string GetEquityStatusRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_EquityStatus.ToString().PadLeft(6, '0'));
        result.Append(_EquityErrorWarningFlag.PadRight(0));
        result.Append(_EquityMessage.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetEquityStatusRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetEquityStatus(parsedInt);
        }
        offset += 6;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetEquityErrorWarningFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetEquityMessage(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetEquityStatusRecordAsString();
    }
    // Set<>String Override function
    public void SetEquityStatusRecord(string value)
    {
        SetEquityStatusRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetEquityStatus()
    {
        return _EquityStatus;
    }
    
    // Standard Setter
    public void SetEquityStatus(int value)
    {
        _EquityStatus = value;
    }
    
    // Get<>AsString()
    public string GetEquityStatusAsString()
    {
        return _EquityStatus.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetEquityStatusAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _EquityStatus = parsed;
    }
    
    // Standard Getter
    public string GetEquityErrorWarningFlag()
    {
        return _EquityErrorWarningFlag;
    }
    
    // Standard Setter
    public void SetEquityErrorWarningFlag(string value)
    {
        _EquityErrorWarningFlag = value;
    }
    
    // Get<>AsString()
    public string GetEquityErrorWarningFlagAsString()
    {
        return _EquityErrorWarningFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetEquityErrorWarningFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EquityErrorWarningFlag = value;
    }
    
    // Standard Getter
    public string GetEquityMessage()
    {
        return _EquityMessage;
    }
    
    // Standard Setter
    public void SetEquityMessage(string value)
    {
        _EquityMessage = value;
    }
    
    // Get<>AsString()
    public string GetEquityMessageAsString()
    {
        return _EquityMessage.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetEquityMessageAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EquityMessage = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
