using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WsSaveCgt2 Data Structure

public class WsSaveCgt2
{
    private static int _size = 270;
    // [DEBUG] Class: WsSaveCgt2, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: FixedPortion2, is_external=, is_static_class=False, static_prefix=
    private string _FixedPortion2 ="";
    
    
    
    
    // [DEBUG] Field: Filler349, is_external=, is_static_class=False, static_prefix=
    private string _Filler349 ="";
    
    
    
    
    // [DEBUG] Field: BalanceCosts2, is_external=, is_static_class=False, static_prefix=
    private string[] _BalanceCosts2 = new string[200];
    
    
    
    
    
    // Serialization methods
    public string GetWsSaveCgt2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_FixedPortion2.PadRight(270));
        result.Append(_Filler349.PadRight(0));
        for (int i = 0; i < 200; i++)
        {
            result.Append(_BalanceCosts2[i].PadRight(0));
        }
        
        return result.ToString();
    }
    
    public void SetWsSaveCgt2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 270 <= data.Length)
        {
            string extracted = data.Substring(offset, 270).Trim();
            SetFixedPortion2(extracted);
        }
        offset += 270;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller349(extracted);
        }
        offset += 0;
        for (int i = 0; i < 200; i++)
        {
            if (offset + 0 > data.Length) break;
            string val = data.Substring(offset, 0);
            
            _BalanceCosts2[i] = val.Trim();
            offset += 0;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsSaveCgt2AsString();
    }
    // Set<>String Override function
    public void SetWsSaveCgt2(string value)
    {
        SetWsSaveCgt2AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFixedPortion2()
    {
        return _FixedPortion2;
    }
    
    // Standard Setter
    public void SetFixedPortion2(string value)
    {
        _FixedPortion2 = value;
    }
    
    // Get<>AsString()
    public string GetFixedPortion2AsString()
    {
        return _FixedPortion2.PadRight(270);
    }
    
    // Set<>AsString()
    public void SetFixedPortion2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _FixedPortion2 = value;
    }
    
    // Standard Getter
    public string GetFiller349()
    {
        return _Filler349;
    }
    
    // Standard Setter
    public void SetFiller349(string value)
    {
        _Filler349 = value;
    }
    
    // Get<>AsString()
    public string GetFiller349AsString()
    {
        return _Filler349.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller349AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler349 = value;
    }
    
    // Array Accessors for BalanceCosts2
    public string GetBalanceCosts2At(int index)
    {
        return _BalanceCosts2[index];
    }
    
    public void SetBalanceCosts2At(int index, string value)
    {
        _BalanceCosts2[index] = value;
    }
    
    public string GetBalanceCosts2AsStringAt(int index)
    {
        return _BalanceCosts2[index].PadRight(0);
    }
    
    public void SetBalanceCosts2AsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _BalanceCosts2[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetBalanceCosts2()
    {
        return _BalanceCosts2 != null && _BalanceCosts2.Length > 0
        ? _BalanceCosts2[0]
        : default(string);
    }
    
    public void SetBalanceCosts2(string value)
    {
        if (_BalanceCosts2 == null || _BalanceCosts2.Length == 0)
        _BalanceCosts2 = new string[1];
        _BalanceCosts2[0] = value;
    }
    
    public string GetBalanceCosts2AsString()
    {
        return _BalanceCosts2 != null && _BalanceCosts2.Length > 0
        ? _BalanceCosts2[0].ToString()
        : string.Empty;
    }
    
    public void SetBalanceCosts2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_BalanceCosts2 == null || _BalanceCosts2.Length == 0)
        _BalanceCosts2 = new string[1];
        
        _BalanceCosts2[0] = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}