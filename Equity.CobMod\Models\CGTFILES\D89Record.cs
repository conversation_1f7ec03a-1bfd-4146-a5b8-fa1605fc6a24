using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D89Record Data Structure

public class D89Record
{
    private static int _size = 42;
    // [DEBUG] Class: D89Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D89GroupCode, is_external=, is_static_class=False, static_prefix=
    private string _D89GroupCode ="";
    
    
    
    
    // [DEBUG] Field: D89GroupDescription, is_external=, is_static_class=False, static_prefix=
    private string _D89GroupDescription ="";
    
    
    
    
    
    // Serialization methods
    public string GetD89RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D89GroupCode.PadRight(2));
        result.Append(_D89GroupDescription.PadRight(40));
        
        return result.ToString();
    }
    
    public void SetD89RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD89GroupCode(extracted);
        }
        offset += 2;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD89GroupDescription(extracted);
        }
        offset += 40;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD89RecordAsString();
    }
    // Set<>String Override function
    public void SetD89Record(string value)
    {
        SetD89RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD89GroupCode()
    {
        return _D89GroupCode;
    }
    
    // Standard Setter
    public void SetD89GroupCode(string value)
    {
        _D89GroupCode = value;
    }
    
    // Get<>AsString()
    public string GetD89GroupCodeAsString()
    {
        return _D89GroupCode.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD89GroupCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D89GroupCode = value;
    }
    
    // Standard Getter
    public string GetD89GroupDescription()
    {
        return _D89GroupDescription;
    }
    
    // Standard Setter
    public void SetD89GroupDescription(string value)
    {
        _D89GroupDescription = value;
    }
    
    // Get<>AsString()
    public string GetD89GroupDescriptionAsString()
    {
        return _D89GroupDescription.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD89GroupDescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D89GroupDescription = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}