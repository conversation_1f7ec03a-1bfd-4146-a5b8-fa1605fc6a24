using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D80File Data Structure

public class D80File
{
    private static int _size = 12;
    // [DEBUG] Class: D80File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler249, is_external=, is_static_class=False, static_prefix=
    private string _Filler249 ="$";
    
    
    
    
    // [DEBUG] Field: D80UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D80UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler250, is_external=, is_static_class=False, static_prefix=
    private string _Filler250 ="DIS.DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD80FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler249.PadRight(1));
        result.Append(_D80UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler250.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD80FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller249(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD80UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller250(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD80FileAsString();
    }
    // Set<>String Override function
    public void SetD80File(string value)
    {
        SetD80FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller249()
    {
        return _Filler249;
    }
    
    // Standard Setter
    public void SetFiller249(string value)
    {
        _Filler249 = value;
    }
    
    // Get<>AsString()
    public string GetFiller249AsString()
    {
        return _Filler249.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller249AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler249 = value;
    }
    
    // Standard Getter
    public int GetD80UserNo()
    {
        return _D80UserNo;
    }
    
    // Standard Setter
    public void SetD80UserNo(int value)
    {
        _D80UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD80UserNoAsString()
    {
        return _D80UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD80UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D80UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller250()
    {
        return _Filler250;
    }
    
    // Standard Setter
    public void SetFiller250(string value)
    {
        _Filler250 = value;
    }
    
    // Get<>AsString()
    public string GetFiller250AsString()
    {
        return _Filler250.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller250AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler250 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}