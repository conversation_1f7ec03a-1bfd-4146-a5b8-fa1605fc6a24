using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing OffshoreRptFile Data Structure

public class OffshoreRptFile
{
    private static int _size = 12;
    // [DEBUG] Class: OffshoreRptFile, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: OffshUserNo, is_external=, is_static_class=False, static_prefix=
    private string _OffshUserNo ="";
    
    
    
    
    // [DEBUG] Field: Filler102, is_external=, is_static_class=False, static_prefix=
    private string _Filler102 ="RK";
    
    
    
    
    // [DEBUG] Field: OffshReportNo, is_external=, is_static_class=False, static_prefix=
    private int _OffshReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler103, is_external=, is_static_class=False, static_prefix=
    private string _Filler103 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetOffshoreRptFileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_OffshUserNo.PadRight(5));
        result.Append(_Filler102.PadRight(2));
        result.Append(_OffshReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler103.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetOffshoreRptFileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            SetOffshUserNo(extracted);
        }
        offset += 5;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller102(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetOffshReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller103(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetOffshoreRptFileAsString();
    }
    // Set<>String Override function
    public void SetOffshoreRptFile(string value)
    {
        SetOffshoreRptFileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetOffshUserNo()
    {
        return _OffshUserNo;
    }
    
    // Standard Setter
    public void SetOffshUserNo(string value)
    {
        _OffshUserNo = value;
    }
    
    // Get<>AsString()
    public string GetOffshUserNoAsString()
    {
        return _OffshUserNo.PadRight(5);
    }
    
    // Set<>AsString()
    public void SetOffshUserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _OffshUserNo = value;
    }
    
    // Standard Getter
    public string GetFiller102()
    {
        return _Filler102;
    }
    
    // Standard Setter
    public void SetFiller102(string value)
    {
        _Filler102 = value;
    }
    
    // Get<>AsString()
    public string GetFiller102AsString()
    {
        return _Filler102.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller102AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler102 = value;
    }
    
    // Standard Getter
    public int GetOffshReportNo()
    {
        return _OffshReportNo;
    }
    
    // Standard Setter
    public void SetOffshReportNo(int value)
    {
        _OffshReportNo = value;
    }
    
    // Get<>AsString()
    public string GetOffshReportNoAsString()
    {
        return _OffshReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetOffshReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _OffshReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller103()
    {
        return _Filler103;
    }
    
    // Standard Setter
    public void SetFiller103(string value)
    {
        _Filler103 = value;
    }
    
    // Get<>AsString()
    public string GetFiller103AsString()
    {
        return _Filler103.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller103AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler103 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}