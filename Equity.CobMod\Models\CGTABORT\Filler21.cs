using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtabortDTO
{// DTO class representing Filler21 Data Structure

public class Filler21
{
    private static int _size = 560;
    // [DEBUG] Class: Filler21, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: EquityErrorMessageTable, is_external=, is_static_class=False, static_prefix=
    private EquityErrorMessageTable[] _EquityErrorMessageTable = new EquityErrorMessageTable[10];
    
    public void InitializeEquityErrorMessageTableArray()
    {
        for (int i = 0; i < 10; i++)
        {
            _EquityErrorMessageTable[i] = new EquityErrorMessageTable();
        }
    }
    
    
    
    
    // Serialization methods
    public string GetFiller21AsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 10; i++)
        {
            result.Append(_EquityErrorMessageTable[i].GetEquityErrorMessageTableAsString());
        }
        
        return result.ToString();
    }
    
    public void SetFiller21AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 10; i++)
        {
            if (offset + 56 > data.Length) break;
            string val = data.Substring(offset, 56);
            
            _EquityErrorMessageTable[i].SetEquityErrorMessageTableAsString(val);
            offset += 56;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetFiller21AsString();
    }
    // Set<>String Override function
    public void SetFiller21(string value)
    {
        SetFiller21AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Array Accessors for EquityErrorMessageTable
    public EquityErrorMessageTable GetEquityErrorMessageTableAt(int index)
    {
        return _EquityErrorMessageTable[index];
    }
    
    public void SetEquityErrorMessageTableAt(int index, EquityErrorMessageTable value)
    {
        _EquityErrorMessageTable[index] = value;
    }
    
    // Flattened accessors (index 0)
    public EquityErrorMessageTable GetEquityErrorMessageTable()
    {
        return _EquityErrorMessageTable != null && _EquityErrorMessageTable.Length > 0
        ? _EquityErrorMessageTable[0]
        : new EquityErrorMessageTable();
    }
    
    public void SetEquityErrorMessageTable(EquityErrorMessageTable value)
    {
        if (_EquityErrorMessageTable == null || _EquityErrorMessageTable.Length == 0)
        _EquityErrorMessageTable = new EquityErrorMessageTable[1];
        _EquityErrorMessageTable[0] = value;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetEquityErrorMessageTable(string value)
    {
        if (!string.IsNullOrEmpty(value) && value.Length < EquityErrorMessageTable.GetSize() * _EquityErrorMessageTable.Length)
        {
            value = value.PadRight(EquityErrorMessageTable.GetSize() * _EquityErrorMessageTable.Length);
        }
        
        int offset = 0;
        for (int i = 0; i < _EquityErrorMessageTable.Length; i++)
        {
            if (offset + EquityErrorMessageTable.GetSize() > value.Length) break;
            string chunk = value.Substring(offset, EquityErrorMessageTable.GetSize());
            _EquityErrorMessageTable[i].SetEquityErrorMessageTableAsString(chunk);
            offset += EquityErrorMessageTable.GetSize();
        }
    }
    // Nested Class: EquityErrorMessageTable
    public class EquityErrorMessageTable
    {
        private static int _size = 56;
        
        // Fields in the class
        
        
        // [DEBUG] Field: EquityErrorCode, is_external=, is_static_class=False, static_prefix=
        private int _EquityErrorCode =0;
        
        
        
        
        // [DEBUG] Field: EquityErrorMessage, is_external=, is_static_class=False, static_prefix=
        private string _EquityErrorMessage ="";
        
        
        
        
    public EquityErrorMessageTable() {}
    
    public EquityErrorMessageTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetEquityErrorCode(int.Parse(data.Substring(offset, 6).Trim()));
        offset += 6;
        SetEquityErrorMessage(data.Substring(offset, 50).Trim());
        offset += 50;
        
    }
    
    // Serialization methods
    public string GetEquityErrorMessageTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_EquityErrorCode.ToString().PadLeft(6, '0'));
        result.Append(_EquityErrorMessage.PadRight(50));
        
        return result.ToString();
    }
    
    public void SetEquityErrorMessageTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetEquityErrorCode(parsedInt);
        }
        offset += 6;
        if (offset + 50 <= data.Length)
        {
            string extracted = data.Substring(offset, 50).Trim();
            SetEquityErrorMessage(extracted);
        }
        offset += 50;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetEquityErrorCode()
    {
        return _EquityErrorCode;
    }
    
    // Standard Setter
    public void SetEquityErrorCode(int value)
    {
        _EquityErrorCode = value;
    }
    
    // Get<>AsString()
    public string GetEquityErrorCodeAsString()
    {
        return _EquityErrorCode.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetEquityErrorCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _EquityErrorCode = parsed;
    }
    
    // Standard Getter
    public string GetEquityErrorMessage()
    {
        return _EquityErrorMessage;
    }
    
    // Standard Setter
    public void SetEquityErrorMessage(string value)
    {
        _EquityErrorMessage = value;
    }
    
    // Get<>AsString()
    public string GetEquityErrorMessageAsString()
    {
        return _EquityErrorMessage.PadRight(50);
    }
    
    // Set<>AsString()
    public void SetEquityErrorMessageAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EquityErrorMessage = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}
