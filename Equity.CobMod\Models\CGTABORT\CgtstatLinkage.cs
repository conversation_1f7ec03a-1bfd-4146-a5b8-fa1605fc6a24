using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtabortDTO
{// DTO class representing CgtstatLinkage Data Structure

public class CgtstatLinkage
{
    private static int _size = 190;
    // [DEBUG] Class: CgtstatLinkage, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: LFileStatus, is_external=, is_static_class=False, static_prefix=
    private string _LFileStatus ="";
    
    
    
    
    // [DEBUG] Field: LFileStatusText, is_external=, is_static_class=False, static_prefix=
    private string _LFileStatusText ="";
    
    
    
    
    
    // Serialization methods
    public string GetCgtstatLinkageAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LFileStatus.PadRight(0));
        result.Append(_LFileStatusText.PadRight(190));
        
        return result.ToString();
    }
    
    public void SetCgtstatLinkageAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetLFileStatus(extracted);
        }
        offset += 0;
        if (offset + 190 <= data.Length)
        {
            string extracted = data.Substring(offset, 190).Trim();
            SetLFileStatusText(extracted);
        }
        offset += 190;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtstatLinkageAsString();
    }
    // Set<>String Override function
    public void SetCgtstatLinkage(string value)
    {
        SetCgtstatLinkageAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetLFileStatus()
    {
        return _LFileStatus;
    }
    
    // Standard Setter
    public void SetLFileStatus(string value)
    {
        _LFileStatus = value;
    }
    
    // Get<>AsString()
    public string GetLFileStatusAsString()
    {
        return _LFileStatus.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetLFileStatusAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LFileStatus = value;
    }
    
    // Standard Getter
    public string GetLFileStatusText()
    {
        return _LFileStatusText;
    }
    
    // Standard Setter
    public void SetLFileStatusText(string value)
    {
        _LFileStatusText = value;
    }
    
    // Get<>AsString()
    public string GetLFileStatusTextAsString()
    {
        return _LFileStatusText.PadRight(190);
    }
    
    // Set<>AsString()
    public void SetLFileStatusTextAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LFileStatusText = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
