using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// DTO class representing ReportFile Data Structure

public class ReportFile
{
    private static int _size = 12;
    // [DEBUG] Class: ReportFile, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler12, is_external=, is_static_class=False, static_prefix=
    private string _Filler12 ="$";
    
    
    
    
    // [DEBUG] Field: ReportUserNo, is_external=, is_static_class=False, static_prefix=
    private string _ReportUserNo ="9999";
    
    
    
    
    // [DEBUG] Field: Filler13, is_external=, is_static_class=False, static_prefix=
    private string _Filler13 ="RT";
    
    
    
    
    // [DEBUG] Field: ReportGenNo, is_external=, is_static_class=False, static_prefix=
    private string _ReportGenNo ="G";
    
    
    
    
    // [DEBUG] Field: Filler14, is_external=, is_static_class=False, static_prefix=
    private string _Filler14 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetReportFileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler12.PadRight(1));
        result.Append(_ReportUserNo.PadRight(4));
        result.Append(_Filler13.PadRight(2));
        result.Append(_ReportGenNo.PadRight(1));
        result.Append(_Filler14.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetReportFileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller12(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetReportUserNo(extracted);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller13(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetReportGenNo(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller14(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportFileAsString();
    }
    // Set<>String Override function
    public void SetReportFile(string value)
    {
        SetReportFileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller12()
    {
        return _Filler12;
    }
    
    // Standard Setter
    public void SetFiller12(string value)
    {
        _Filler12 = value;
    }
    
    // Get<>AsString()
    public string GetFiller12AsString()
    {
        return _Filler12.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller12AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler12 = value;
    }
    
    // Standard Getter
    public string GetReportUserNo()
    {
        return _ReportUserNo;
    }
    
    // Standard Setter
    public void SetReportUserNo(string value)
    {
        _ReportUserNo = value;
    }
    
    // Get<>AsString()
    public string GetReportUserNoAsString()
    {
        return _ReportUserNo.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetReportUserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportUserNo = value;
    }
    
    // Standard Getter
    public string GetFiller13()
    {
        return _Filler13;
    }
    
    // Standard Setter
    public void SetFiller13(string value)
    {
        _Filler13 = value;
    }
    
    // Get<>AsString()
    public string GetFiller13AsString()
    {
        return _Filler13.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller13AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler13 = value;
    }
    
    // Standard Getter
    public string GetReportGenNo()
    {
        return _ReportGenNo;
    }
    
    // Standard Setter
    public void SetReportGenNo(string value)
    {
        _ReportGenNo = value;
    }
    
    // Get<>AsString()
    public string GetReportGenNoAsString()
    {
        return _ReportGenNo.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetReportGenNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportGenNo = value;
    }
    
    // Standard Getter
    public string GetFiller14()
    {
        return _Filler14;
    }
    
    // Standard Setter
    public void SetFiller14(string value)
    {
        _Filler14 = value;
    }
    
    // Get<>AsString()
    public string GetFiller14AsString()
    {
        return _Filler14.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller14AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler14 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}