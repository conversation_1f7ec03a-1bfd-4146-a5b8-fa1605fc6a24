﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using EquityProject.CgtabortPGM;
using EquityProject.CgtschedDTO;
using EquityProject.EqtpathPGM;
using EquityProject.EquityGlobalParmsDTO;

namespace Equity.CobMod.Helper
{
    public class Cgtsched
    {
        // Declare Cgtsched Class private variables
        private Fvar _fvar = new Fvar();
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();
        private StreamWriter reportFileStream;
        // Declare {program_name} Class getters setters
        public Fvar GetFvar() { return _fvar; }
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        /// <summary>
        /// Helper method to call external subroutines
        /// </summary>
        /// <param name="ivar">The Ivar parameter to pass to the subroutine</param>
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
        }


        // Run() method
        public void Run(Fvar fvar, Gvar gvar, Ivar ivar)

        {
            // Call the main entry point
            // No mainline procedure found, add your entry point here
            Acontrol(fvar, gvar, ivar);
        }

        // Methods representing paragraphs under procedure division
        /// <summary>
        /// Method equivalent to the COBOL paragraph 'aControl'.
        /// </summary>
        /// <remarks>
        /// This method evaluates the 'L-FILE-ACTION' and performs operations
        /// based on its value accordingly to the original COBOL logic flow.
        /// </remarks>
        public void Acontrol(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileAction())
            {
                case var action when action.Equals(ivar.GetCgtfilesLinkage().GetLFileAction()):
                    Bopen(fvar, gvar, ivar); // PERFORM B-OPEN
                    break;

                case var action when action.Equals(Ivar.WRITE_RECORD):
                    Cwrite(fvar, gvar, ivar); // PERFORM C-WRITE
                    break;

                case var action when action.Equals(Ivar.CLOSE_FILE):
                    Cwrite(fvar, gvar, ivar); // PERFORM C-WRITE
                    Dclose(fvar, gvar, ivar); // PERFORM D-CLOSE
                    break;
            }
        }
        /// <summary>
        /// COBOL paragraph name: bOpen
        /// </summary>
        /// <remarks>
        /// This method is equivalent to the COBOL paragraph bOpen, converting file opening logic.
        /// </remarks>
        public void Bopen(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            switch (ivar.GetCgtfilesLinkage().GetLFileName())
            {
                case var fileName when fileName == Ivar.REALISED_SCHEDULE_FILE:
                    gvar.GetD21PrintRecord().SetD21UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    gvar.GetD21PrintRecord().SetD21ReportNo(Int32.Parse(ivar.GetCgtfilesLinkage().GetLReportNo()));
                    gvar.SetWsReportFile(gvar.GetD21PrintRecordAsString());
                    break;

                case var fileName when fileName == Ivar.UNREALISED_SCHEDULE_FILE:
                    gvar.GetD22PrintRecord().SetD22UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    gvar.GetD22PrintRecord().SetD22ReportNo(Int32.Parse(ivar.GetCgtfilesLinkage().GetLReportNo()));
                    gvar.SetWsReportFile(gvar.GetD22PrintRecordAsString());
                    break;

                case var fileName when fileName == Ivar.NOTIONAL_SALE_SCHEDULE_FILE:
                    gvar.GetD82PrintRecord().SetD82UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    gvar.GetD82PrintRecord().SetD82ReportNo(Int32.Parse(ivar.GetCgtfilesLinkage().GetLReportNo()));
                    gvar.SetWsReportFile(gvar.GetD82PrintRecordAsString());
                    break;

                case var fileName when fileName == Ivar.REALISED_TAX_SCHEDULE_FILE:
                    gvar.GetD100PrintRecord().SetD100UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    gvar.GetD100PrintRecord().SetD100ReportNo(Int32.Parse(ivar.GetCgtfilesLinkage().GetLReportNo()));
                    gvar.SetWsReportFile(gvar.GetD100PrintRecordAsString());
                    break;

                case var fileName when fileName == Ivar.UNREALISED_TAX_SCHEDULE_FILE:
                    gvar.GetD102PrintRecord().SetD102UserNo(ivar.GetCommonLinkage().GetLUserNo());
                    gvar.GetD102PrintRecord().SetD102ReportNo(Int32.Parse(ivar.GetCgtfilesLinkage().GetLReportNo()));
                    gvar.SetWsReportFile(gvar.GetD102PrintRecordAsString());
                    break;
            }

            gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);
            gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetWsReportFile());

            PerformXCallEqtpath(gvar); // Perform X-CALL-EQTPATH

            gvar.SetWsReportFileName(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

            OpenOutputReportFile(gvar.GetWsReportFileAsString(),ivar);

            if (ivar.GetCgtfilesLinkage().GetLFileReturnCodeAsString() != string.Empty)
            {
                PerformXAbort(gvar,ivar); // Perform X-ABORT
            }

            gvar.SetFirstWriteFlagAsString("true");
            gvar.SetWFirstPrintRecordAsString("true");

            fvar.SetPrintRecordAsString(string.Empty);
            fvar.SetPrintRecordAsString(gvar.GetWPrintRecordBufferAsString());
        }

        /// <summary>
        /// This method should be implemented to handle the logic for calling EQTPATH.
        /// </summary>


        /// <summary>
        /// This method should handle file opening logic.
        /// </summary>


        /// <summary>
        /// This method should be implemented to handle the abort logic.
        /// </summary>
        /// <summary>
        /// COBOL paragraph name: cWrite
        /// Handles writing and formatting to different file types based on the settings and conditions.
        /// </summary>
        /// <remarks>
        /// This method replicates the COBOL logic provided in the cWrite paragraph.
        /// Handles various cases for different file types and manages print record advancements.
        /// </remarks>
        public void Cwrite(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            if (gvar.GetFirstWriteFlagAsString() == "True")
            {
                gvar.SetFirstWriteFlag("N");
                // UNSTRING L-FILE-RECORD-AREA INTO WS-RUBBISH H1-R-REF (etc.)
                string lFileRecordAreaStr = ivar.GetLFileRecordArea().GetLFileRecordAreaAsString();
                if (lFileRecordAreaStr.Length > 0)
                    _gvar.SetWsRubbish(lFileRecordAreaStr.Substring(0, 1));

                string refData = "";
                if (lFileRecordAreaStr.Length > 1)
                {
                    refData = lFileRecordAreaStr.Substring(1, Math.Min(30, lFileRecordAreaStr.Length - 1));
                }
                switch (ivar.GetCgtfilesLinkage().GetLFileName())
                {
                    case var fileName when fileName == Ivar.REALISED_SCHEDULE_FILE:
                        //UnstringRecord(ivar.GetLFileRecordAreaAsString(), gvar.GetWsRubbish(), gvar.GetH1RHeading().GetH1RRef());
                        gvar.GetH1RHeading().SetH1RRef(refData);
                        gvar.SetH1Heading(gvar.GetH1RHeadingAsString());
                        break;

                    case var fileName when fileName == Ivar.UNREALISED_SCHEDULE_FILE:
                        //UnstringRecord(ivar.GetLFileRecordAreaAsString(), gvar.GetWsRubbish(), gvar.GetH1UHeading().GetH1URef());
                        gvar.GetH1UHeading().SetH1URef(refData);
                        gvar.SetH1Heading(gvar.GetH1UHeadingAsString());
                        break;

                    case var fileName when fileName == Ivar.NOTIONAL_SALE_SCHEDULE_FILE:
                        //UnstringRecord(ivar.GetLFileRecordAreaAsString(), gvar.GetWsRubbish(), gvar.GetH1NHeading().GetH1NRef());
                        gvar.GetH1NHeading().SetH1NRef(refData);
                        gvar.SetH1Heading(gvar.GetH1NHeadingAsString());
                        break;

                    case var fileName when fileName == Ivar.REALISED_TAX_SCHEDULE_FILE:
                        //UnstringRecord(ivar.GetLFileRecordAreaAsString(), gvar.GetWsRubbish(), gvar.GetH1THeading().GetH1TRef());
                        gvar.GetH1THeading().SetH1TRef(refData);
                        gvar.SetH1Heading(gvar.GetH1THeadingAsString());
                        break;

                    case var fileName when fileName == Ivar.UNREALISED_TAX_SCHEDULE_FILE:
                        //UnstringRecord(ivar.GetLFileRecordAreaAsString(), gvar.GetWsRubbish(), gvar.GetH1XHeading().GetH1XRef());
                        gvar.GetH1XHeading().SetH1XRef(refData);
                        gvar.SetH1Heading(gvar.GetH1XHeadingAsString());
                        break;
                }
            }
            else
            {
                gvar.SetWPrintRecordBufferAsString(fvar.GetPrintRecordAsString());
                fvar.SetPrintRecordAsString(ivar.GetLFileRecordAreaAsString());
                fvar.SetPrintRecordAsString(fvar.GetPrintRecord().GetPrintLaserControlAsString());

                if (gvar.GetWFirstPrintRecordAsString() != null && gvar.GetWFirstPrintRecordAsString() != string.Empty)
                {
                    gvar.SetWFirstPrintRecord("N");
                    return; // Go to C-EXIT
                }

                if (Int32.Parse(gvar.GetFiller60().GetWPrintBufferCharAt(1)) == '+')
                {
                    gvar.SetWOverlayPrintLineAsString("Y");
                    for (var sub = 1; sub <= 181; sub++)
                    {
                        if (gvar.GetFiller60().GetWPrintBufferCharAt(sub).Trim() != string.Empty && fvar.GetPrintRecTable().GetPrintRecordCharAt(sub).Trim() != string.Empty)
                        {
                            gvar.SetWOverlayPrintLineAsString("N");
                        }
                    }

                    if (gvar.IsOkToOverlayPrintLine())
                    {
                        for (var sub = 1; sub <= 181; sub++)
                        {
                            if (gvar.GetFiller60().GetWPrintBufferCharAt(sub) != string.Empty && fvar.GetPrintRecTable().GetPrintRecordCharAt(sub) == string.Empty)
                            {
                                fvar.GetPrintRecTable().SetPrintRecordCharAt(sub, gvar.GetFiller60().GetWPrintBufferCharAt(sub));
                            }
                        }
                        gvar.SetWPrintRecordBuffer(fvar.GetPrintRecordAsString());
                        return; // Go to C-EXIT
                    }
                }
            }
            string lineToWrite = fvar.GetPrintRecord().GetFiller1();
            switch (fvar.GetPrintRecord().GetPrintControl())
            {
                case " ":
                    // WRITE PRINT-RECORD AFTER ADVANCING 1 LINE
                    // Logic for advancing 1 line
                    reportFileStream.WriteLine(lineToWrite);
                    gvar.SetWsLineNumber(gvar.GetWsLineNumber() + 1);
                    break;

                case "0":
                    fvar.SetPrintRecordAsString(fvar.GetPrintRecord().GetPrintControlAsString());
                    // WRITE PRINT-RECORD AFTER ADVANCING 2 LINES
                    // Logic for advancing 2 lines
                    reportFileStream.WriteLine(); // Blank line
                    reportFileStream.WriteLine(lineToWrite);
                    gvar.SetWsLineNumber(gvar.GetWsLineNumber() + 2);
                    break;

                case "-":
                    fvar.SetPrintRecordAsString(fvar.GetPrintRecord().GetPrintControlAsString());
                    // WRITE PRINT-RECORD AFTER ADVANCING 3 LINES
                    // Logic for advancing 3 lines
                    reportFileStream.WriteLine();
                    reportFileStream.WriteLine();
                    reportFileStream.WriteLine(lineToWrite);
                    gvar.SetWsLineNumber(gvar.GetWsLineNumber() + 3);
                    break;

                case "+":
                    fvar.SetPrintRecordAsString(fvar.GetPrintRecord().GetPrintControl());
                    // WRITE PRINT-RECORD AFTER ADVANCING 0 LINES
                    reportFileStream.Write(lineToWrite + "\r");
                    // Logic for advancing 0 lines
                    break;

                case "1":
                    fvar.SetPrintRecordAsString(fvar.GetPrintRecord().GetPrintControl());
                    fvar.SetPrintRecordAsString(gvar.GetH1Heading());
                    // WRITE PRINT-RECORD AFTER ADVANCING PAGE
                    reportFileStream.Write("\f"); // Form feed
                    reportFileStream.WriteLine(_gvar.GetH1Heading()); // Assumes H1-HEADING is set
                    gvar.SetWsLineNumber(2);
                    break;

                case "2":
                    fvar.SetPrintRecordAsString(fvar.GetPrintRecord().GetPrintControl());
                    gvar.SetWStoredPrintRecord(fvar.GetPrintRecordAsString());
                    reportFileStream.WriteLine(); // Blank line
                    reportFileStream.WriteLine(_gvar.GetH2Heading().GetH2HeadingAsString());
                    fvar.SetPrintRecordAsString(gvar.GetH2HeadingAsString());
                    // WRITE PRINT-RECORD AFTER ADVANCING 2 LINES
                    fvar.SetPrintRecordAsString(gvar.GetWStoredPrintRecord());
                    // WRITE PRINT-RECORD AFTER ADVANCING 1 LINE
                    fvar.SetPrintRecordAsString(gvar.GetD1DetailAsString());
                    // WRITE PRINT-RECORD AFTER ADVANCING 3 LINES
                    fvar.SetPrintRecordAsString(gvar.GetD2DetailAsString());
                    // WRITE PRINT-RECORD AFTER ADVANCING 1 LINE
                    // lineToWrite is already set from _fvar.GetPrintRecord().GetFiller1()
                    reportFileStream.WriteLine(lineToWrite);

                    reportFileStream.WriteLine(); // Blank line
                    reportFileStream.WriteLine(); // Blank line
                    reportFileStream.WriteLine(_gvar.GetD1Detail().GetD1DetailAsString());

                    reportFileStream.WriteLine(_gvar.GetD2Detail().GetD2DetailAsString());

                    gvar.SetWsLineNumber(gvar.GetWsLineNumber() + 7);
                    break;

                case "3":
                    fvar.SetPrintRecordAsString(fvar.GetPrintRecord().GetPrintControl());
                    // WRITE PRINT-RECORD AFTER ADVANCING 2 LINES
                    reportFileStream.WriteLine(); // Blank line
                    reportFileStream.WriteLine(lineToWrite);

                    gvar.SetWsLineNumber(gvar.GetWsLineNumber() + 2);
                    break;

                case "4":
                    fvar.SetPrintRecordAsString(fvar.GetPrintRecord().GetPrintControl());
                    gvar.SetWsAdvance(60 > gvar.GetWsLineNumber() ? 60 - gvar.GetWsLineNumber() : 1);
                    // WRITE PRINT-RECORD AFTER ADVANCING WS-ADVANCE LINES
                    reportFileStream.WriteLine(lineToWrite);
                    gvar.SetWsLineNumber(60);
                    break;

                default:
                    fvar.SetPrintRecordAsString(fvar.GetPrintRecord().GetPrintControl());
                    // WRITE PRINT-RECORD AFTER ADVANCING 3 LINES
                    reportFileStream.WriteLine();
                    reportFileStream.WriteLine();
                    reportFileStream.WriteLine(lineToWrite);
                    break;
            }

            if (!ivar.GetCgtfilesLinkage().IsSuccessful())
            {
                XAbort(fvar, gvar, ivar); // PERFORM X-ABORT
            }
        }

        private void UnstringRecord(string v1, string v2, string v3)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Helper for unstringing record fields.
        /// </summary>
        /// <remarks>
        /// This mimics the COBOL UNSTRING operation.
        /// </remarks>


        // Note: The methods `XAbort` and `PrintRecordWritingLogic` would need to be implemented elsewhere.
        /// <summary>
        /// Method equivalent to the COBOL paragraph: dClose
        /// </summary>
        /// <remarks>
        /// Closes the report file and checks for successful operation.
        /// If not successful, performs X-ABORT.
        /// </remarks>
        public void Dclose(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Attempt to close the REPORT-FILE
            //CloseReportFile(gvar.GetWsReportFileAsString());
            if (reportFileStream != null)
            {
                try
                {
                    reportFileStream.Close();
                    reportFileStream.Dispose();
                    reportFileStream = null;
                    ivar.GetCgtfilesLinkage().SetLFileReturnCode("00"); // Success
                }
                catch (IOException)
                {
                    ivar.GetCgtfilesLinkage().SetLFileReturnCode("34"); // Error on close
                }
            }
            else
            {
                ivar.GetCgtfilesLinkage().SetLFileReturnCode("42"); // File not open
            }

            // Check if the CLOSE operation was not successful
            if (ivar.GetCgtfilesLinkage().IsSuccessful())
            {
                // Perform the X-ABORT logic
                Xabort(fvar, gvar, ivar); // PERFORM X-ABORT
            }
        }


        /// <summary>
        /// A stub representing the operation to close the REPORT-FILE.
        /// </summary>
        /// <remarks>
        /// This is a placeholder for the actual file closing logic.
        /// </remarks>


        /// <summary>
        /// A stub representing the check for successful operation.
        /// </summary>
        /// <remarks>
        /// This function checks if the operation was successful.
        /// </remarks>
        private bool IsSuccessful(string successFlag)
        {
            // Implementation to check success goes here
            return successFlag.Equals("SUCCESSFUL");
        }

        /// <summary>
        /// A stub representing the X-ABORT operation.
        /// </summary>
        /// <remarks>
        /// This will trigger the abort logic if needed.
        /// </remarks>
        /// <summary>
        /// Represents the COBOL paragraph xAbort.
        /// </summary>
        /// <remarks>
        /// This method moves values to the abort linkage and calls the external program "CGTABORT".
        /// </remarks>
        public void Xabort(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move L-FILE-RETURN-CODE to L-ABORT-FILE-STATUS
            gvar.GetCgtabortLinkage().SetLAbortFileStatus(ivar.GetCgtfilesLinkage().GetLFileReturnCode());

            // Move WS-PROGRAM-NAME to L-ABORT-PROGRAM-NAME
            gvar.GetCgtabortLinkage().SetLAbortProgramName(gvar.GetWsProgramName());

            // Move L-FILE-NAME to L-ABORT-FILE-NAME
            gvar.GetCgtabortLinkage().SetLAbortFileName(ivar.GetCgtfilesLinkage().GetLFileName());

            // Call "CGTABORT" using COMMON-LINKAGE CGTABORT-LINKAGE
            Cgtabort cgtabort = new Cgtabort();
            cgtabort.GetIvar().SetCgtabortLinkageAsString(ivar.GetCgtfilesLinkageAsString());
            cgtabort.GetIvar().SetCommonLinkageAsString(ivar.GetCgtfilesLinkageAsString());
            cgtabort.Run(cgtabort.GetGvar(),cgtabort.GetIvar());
        }
        /// <summary>
        /// This method corresponds to the COBOL paragraph 'xCallEqtpath'.
        /// It performs a CALL to the 'EQTPATH' program using the specified linkage.
        /// </summary>
        /// <remarks>
        /// Call 'EQTPATH' using EQTPATH-LINKAGE.
        /// </remarks>
        public void Xcalleqtpath(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the EQTPATH external program
            Eqtpath eqtpathProgram = new Eqtpath();
            eqtpathProgram.GetIvar().SetEqtpathLinkageAsString(gvar.GetEqtpathLinkageAsString());
            // Call the Run method with the EQTPATH-LINKAGE parameter
            EquityGlobalParms eqtpathGlobalParms = new EquityGlobalParms();
            eqtpathProgram.Run(eqtpathProgram.GetGvar(),eqtpathProgram.GetIvar(), eqtpathGlobalParms);
        }
        // Helper methods
        
    private void PerformXCallEqtpath(Gvar gvar)
        {
            // Implementation for calling X-CALL-EQTPATH
            Eqtpath eqtpath = new Eqtpath();
            eqtpath.GetIvar().SetEqtpathLinkageAsString(gvar.GetEqtpathLinkageAsString());
            eqtpath.Run(eqtpath.GetGvar(), eqtpath.GetIvar(),new EquityGlobalParms());

        }

        private void OpenOutputReportFile(object reportFilePath,Ivar ivar)
        {
            try
            {
                StreamWriter reportFileStream = new StreamWriter((string)reportFilePath); // Default is overwrite (like OPEN OUTPUT)
                ivar.GetCgtfilesLinkage().SetLFileReturnCode("00"); // Success
            }
            catch (DirectoryNotFoundException)
            {
                ivar.GetCgtfilesLinkage().SetLFileReturnCode("35"); // File missing (path issue)
                // Optionally set L-STATUS-1 and L-STATUS-2 if more detail is needed
            }
            catch (IOException ex)
            {
                // More generic IO error, could be permissions, disk full etc.
                // For simplicity, using "35", but a more detailed mapping could be done.
                ivar.GetCgtfilesLinkage().SetLFileReturnCode("35");
                // Example: if (ex.Message.Contains("disk is full")) { ... LStatus2 = X"07" ... }
            }
            catch (UnauthorizedAccessException)
            {
                ivar.GetCgtfilesLinkage().SetLFileReturnCode("39"); // Permission problem (closest COBOL equivalent)
            }
            catch (Exception) // Catch-all for other errors
            {
                ivar.GetCgtfilesLinkage().SetLFileReturnCode("90"); // Undefined error
            }

        }

        private void PerformXAbort(Gvar gvar,Ivar ivar)
        {
            // Implementation for calling X-ABORT
            Cgtabort cgtabort = new Cgtabort();
            cgtabort.GetIvar().SetCgtabortLinkageAsString(gvar.GetCgtabortLinkageAsString());
            cgtabort.GetIvar().SetCommonLinkageAsString(ivar.GetCgtfilesLinkageAsString());
            cgtabort.Run(cgtabort.GetGvar(), cgtabort.GetIvar());
        }

        private void XAbort(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Implementation of X-ABORT logic goes here
            Cgtabort cgtabort = new Cgtabort();
            cgtabort.GetIvar().SetCgtabortLinkageAsString(gvar.GetCgtabortLinkageAsString());
            cgtabort.GetIvar().SetCommonLinkageAsString(ivar.GetCgtfilesLinkageAsString());
            cgtabort.Run(cgtabort.GetGvar(), cgtabort.GetIvar());
        }
    }
}