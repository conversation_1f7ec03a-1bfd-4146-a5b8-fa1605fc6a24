using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing D153Record Data Structure

public class D153Record
{
    private static int _size = 44;
    // [DEBUG] Class: D153Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D153Key, is_external=, is_static_class=False, static_prefix=
    private D153Key _D153Key = new D153Key();
    
    
    
    
    // [DEBUG] Field: D153Description, is_external=, is_static_class=False, static_prefix=
    private string _D153Description ="";
    
    
    
    
    // [DEBUG] Field: D153StartYear, is_external=, is_static_class=False, static_prefix=
    private int _D153StartYear =0;
    
    
    
    
    // [DEBUG] Field: D153PriorPeriodStartDate, is_external=, is_static_class=False, static_prefix=
    private D153PriorPeriodStartDate _D153PriorPeriodStartDate = new D153PriorPeriodStartDate();
    
    
    
    
    // [DEBUG] Field: D153PriorPeriodEndDate, is_external=, is_static_class=False, static_prefix=
    private D153PriorPeriodEndDate _D153PriorPeriodEndDate = new D153PriorPeriodEndDate();
    
    
    
    
    // [DEBUG] Field: D153InverseKey, is_external=, is_static_class=False, static_prefix=
    private string _D153InverseKey ="";
    
    
    
    
    
    // Serialization methods
    public string GetD153RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D153Key.GetD153KeyAsString());
        result.Append(_D153Description.PadRight(40));
        result.Append(_D153StartYear.ToString().PadLeft(4, '0'));
        result.Append(_D153PriorPeriodStartDate.GetD153PriorPeriodStartDateAsString());
        result.Append(_D153PriorPeriodEndDate.GetD153PriorPeriodEndDateAsString());
        result.Append(_D153InverseKey.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD153RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            _D153Key.SetD153KeyAsString(data.Substring(offset, 0));
        }
        else
        {
            _D153Key.SetD153KeyAsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD153Description(extracted);
        }
        offset += 40;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD153StartYear(parsedInt);
        }
        offset += 4;
        if (offset + 0 <= data.Length)
        {
            _D153PriorPeriodStartDate.SetD153PriorPeriodStartDateAsString(data.Substring(offset, 0));
        }
        else
        {
            _D153PriorPeriodStartDate.SetD153PriorPeriodStartDateAsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D153PriorPeriodEndDate.SetD153PriorPeriodEndDateAsString(data.Substring(offset, 0));
        }
        else
        {
            _D153PriorPeriodEndDate.SetD153PriorPeriodEndDateAsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD153InverseKey(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD153RecordAsString();
    }
    // Set<>String Override function
    public void SetD153Record(string value)
    {
        SetD153RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D153Key GetD153Key()
    {
        return _D153Key;
    }
    
    // Standard Setter
    public void SetD153Key(D153Key value)
    {
        _D153Key = value;
    }
    
    // Get<>AsString()
    public string GetD153KeyAsString()
    {
        return _D153Key != null ? _D153Key.GetD153KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD153KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D153Key == null)
        {
            _D153Key = new D153Key();
        }
        _D153Key.SetD153KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD153Description()
    {
        return _D153Description;
    }
    
    // Standard Setter
    public void SetD153Description(string value)
    {
        _D153Description = value;
    }
    
    // Get<>AsString()
    public string GetD153DescriptionAsString()
    {
        return _D153Description.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD153DescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D153Description = value;
    }
    
    // Standard Getter
    public int GetD153StartYear()
    {
        return _D153StartYear;
    }
    
    // Standard Setter
    public void SetD153StartYear(int value)
    {
        _D153StartYear = value;
    }
    
    // Get<>AsString()
    public string GetD153StartYearAsString()
    {
        return _D153StartYear.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD153StartYearAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D153StartYear = parsed;
    }
    
    // Standard Getter
    public D153PriorPeriodStartDate GetD153PriorPeriodStartDate()
    {
        return _D153PriorPeriodStartDate;
    }
    
    // Standard Setter
    public void SetD153PriorPeriodStartDate(D153PriorPeriodStartDate value)
    {
        _D153PriorPeriodStartDate = value;
    }
    
    // Get<>AsString()
    public string GetD153PriorPeriodStartDateAsString()
    {
        return _D153PriorPeriodStartDate != null ? _D153PriorPeriodStartDate.GetD153PriorPeriodStartDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD153PriorPeriodStartDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D153PriorPeriodStartDate == null)
        {
            _D153PriorPeriodStartDate = new D153PriorPeriodStartDate();
        }
        _D153PriorPeriodStartDate.SetD153PriorPeriodStartDateAsString(value);
    }
    
    // Standard Getter
    public D153PriorPeriodEndDate GetD153PriorPeriodEndDate()
    {
        return _D153PriorPeriodEndDate;
    }
    
    // Standard Setter
    public void SetD153PriorPeriodEndDate(D153PriorPeriodEndDate value)
    {
        _D153PriorPeriodEndDate = value;
    }
    
    // Get<>AsString()
    public string GetD153PriorPeriodEndDateAsString()
    {
        return _D153PriorPeriodEndDate != null ? _D153PriorPeriodEndDate.GetD153PriorPeriodEndDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD153PriorPeriodEndDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D153PriorPeriodEndDate == null)
        {
            _D153PriorPeriodEndDate = new D153PriorPeriodEndDate();
        }
        _D153PriorPeriodEndDate.SetD153PriorPeriodEndDateAsString(value);
    }
    
    // Standard Getter
    public string GetD153InverseKey()
    {
        return _D153InverseKey;
    }
    
    // Standard Setter
    public void SetD153InverseKey(string value)
    {
        _D153InverseKey = value;
    }
    
    // Get<>AsString()
    public string GetD153InverseKeyAsString()
    {
        return _D153InverseKey.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD153InverseKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D153InverseKey = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD153Key(string value)
    {
        _D153Key.SetD153KeyAsString(value);
    }
    // Nested Class: D153Key
    public class D153Key
    {
        private static int _size = 0;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D153CalendarId, is_external=, is_static_class=False, static_prefix=
        private string _D153CalendarId ="";
        
        
        
        
    public D153Key() {}
    
    public D153Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD153CalendarId(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD153KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D153CalendarId.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD153KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD153CalendarId(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD153CalendarId()
    {
        return _D153CalendarId;
    }
    
    // Standard Setter
    public void SetD153CalendarId(string value)
    {
        _D153CalendarId = value;
    }
    
    // Get<>AsString()
    public string GetD153CalendarIdAsString()
    {
        return _D153CalendarId.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD153CalendarIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D153CalendarId = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetD153PriorPeriodStartDate(string value)
{
    _D153PriorPeriodStartDate.SetD153PriorPeriodStartDateAsString(value);
}
// Nested Class: D153PriorPeriodStartDate
public class D153PriorPeriodStartDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D153PriorPeriodStartCc, is_external=, is_static_class=False, static_prefix=
    private string _D153PriorPeriodStartCc ="";
    
    
    
    
    // [DEBUG] Field: D153PriorPeriodStartYymmdd, is_external=, is_static_class=False, static_prefix=
    private string _D153PriorPeriodStartYymmdd ="";
    
    
    
    
public D153PriorPeriodStartDate() {}

public D153PriorPeriodStartDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD153PriorPeriodStartCc(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD153PriorPeriodStartYymmdd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD153PriorPeriodStartDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D153PriorPeriodStartCc.PadRight(0));
    result.Append(_D153PriorPeriodStartYymmdd.PadRight(0));
    
    return result.ToString();
}

public void SetD153PriorPeriodStartDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD153PriorPeriodStartCc(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD153PriorPeriodStartYymmdd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD153PriorPeriodStartCc()
{
    return _D153PriorPeriodStartCc;
}

// Standard Setter
public void SetD153PriorPeriodStartCc(string value)
{
    _D153PriorPeriodStartCc = value;
}

// Get<>AsString()
public string GetD153PriorPeriodStartCcAsString()
{
    return _D153PriorPeriodStartCc.PadRight(0);
}

// Set<>AsString()
public void SetD153PriorPeriodStartCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D153PriorPeriodStartCc = value;
}

// Standard Getter
public string GetD153PriorPeriodStartYymmdd()
{
    return _D153PriorPeriodStartYymmdd;
}

// Standard Setter
public void SetD153PriorPeriodStartYymmdd(string value)
{
    _D153PriorPeriodStartYymmdd = value;
}

// Get<>AsString()
public string GetD153PriorPeriodStartYymmddAsString()
{
    return _D153PriorPeriodStartYymmdd.PadRight(0);
}

// Set<>AsString()
public void SetD153PriorPeriodStartYymmddAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D153PriorPeriodStartYymmdd = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD153PriorPeriodEndDate(string value)
{
    _D153PriorPeriodEndDate.SetD153PriorPeriodEndDateAsString(value);
}
// Nested Class: D153PriorPeriodEndDate
public class D153PriorPeriodEndDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D153PriorPeriodEndCc, is_external=, is_static_class=False, static_prefix=
    private string _D153PriorPeriodEndCc ="";
    
    
    
    
    // [DEBUG] Field: D153PriorPeriodEndYymmdd, is_external=, is_static_class=False, static_prefix=
    private string _D153PriorPeriodEndYymmdd ="";
    
    
    
    
public D153PriorPeriodEndDate() {}

public D153PriorPeriodEndDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD153PriorPeriodEndCc(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD153PriorPeriodEndYymmdd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD153PriorPeriodEndDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D153PriorPeriodEndCc.PadRight(0));
    result.Append(_D153PriorPeriodEndYymmdd.PadRight(0));
    
    return result.ToString();
}

public void SetD153PriorPeriodEndDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD153PriorPeriodEndCc(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD153PriorPeriodEndYymmdd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD153PriorPeriodEndCc()
{
    return _D153PriorPeriodEndCc;
}

// Standard Setter
public void SetD153PriorPeriodEndCc(string value)
{
    _D153PriorPeriodEndCc = value;
}

// Get<>AsString()
public string GetD153PriorPeriodEndCcAsString()
{
    return _D153PriorPeriodEndCc.PadRight(0);
}

// Set<>AsString()
public void SetD153PriorPeriodEndCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D153PriorPeriodEndCc = value;
}

// Standard Getter
public string GetD153PriorPeriodEndYymmdd()
{
    return _D153PriorPeriodEndYymmdd;
}

// Standard Setter
public void SetD153PriorPeriodEndYymmdd(string value)
{
    _D153PriorPeriodEndYymmdd = value;
}

// Get<>AsString()
public string GetD153PriorPeriodEndYymmddAsString()
{
    return _D153PriorPeriodEndYymmdd.PadRight(0);
}

// Set<>AsString()
public void SetD153PriorPeriodEndYymmddAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D153PriorPeriodEndYymmdd = value;
}



public static int GetSize()
{
    return _size;
}

}

}}