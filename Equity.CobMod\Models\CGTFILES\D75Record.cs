using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D75Record Data Structure

public class D75Record
{
    private static int _size = 200;
    // [DEBUG] Class: D75Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D75RecordArea, is_external=, is_static_class=False, static_prefix=
    private string _D75RecordArea ="";
    
    
    
    
    // [DEBUG] Field: Filler89, is_external=, is_static_class=False, static_prefix=
    private Filler89 _Filler89 = new Filler89();
    
    
    
    
    
    // Serialization methods
    public string GetD75RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D75RecordArea.PadRight(161));
        result.Append(_Filler89.GetFiller89AsString());
        
        return result.ToString();
    }
    
    public void SetD75RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 161 <= data.Length)
        {
            string extracted = data.Substring(offset, 161).Trim();
            SetD75RecordArea(extracted);
        }
        offset += 161;
        if (offset + 39 <= data.Length)
        {
            _Filler89.SetFiller89AsString(data.Substring(offset, 39));
        }
        else
        {
            _Filler89.SetFiller89AsString(data.Substring(offset));
        }
        offset += 39;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD75RecordAsString();
    }
    // Set<>String Override function
    public void SetD75Record(string value)
    {
        SetD75RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD75RecordArea()
    {
        return _D75RecordArea;
    }
    
    // Standard Setter
    public void SetD75RecordArea(string value)
    {
        _D75RecordArea = value;
    }
    
    // Get<>AsString()
    public string GetD75RecordAreaAsString()
    {
        return _D75RecordArea.PadRight(161);
    }
    
    // Set<>AsString()
    public void SetD75RecordAreaAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D75RecordArea = value;
    }
    
    // Standard Getter
    public Filler89 GetFiller89()
    {
        return _Filler89;
    }
    
    // Standard Setter
    public void SetFiller89(Filler89 value)
    {
        _Filler89 = value;
    }
    
    // Get<>AsString()
    public string GetFiller89AsString()
    {
        return _Filler89 != null ? _Filler89.GetFiller89AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller89AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler89 == null)
        {
            _Filler89 = new Filler89();
        }
        _Filler89.SetFiller89AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller89(string value)
    {
        _Filler89.SetFiller89AsString(value);
    }
    // Nested Class: Filler89
    public class Filler89
    {
        private static int _size = 39;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler90, is_external=, is_static_class=False, static_prefix=
        private string _Filler90 ="";
        
        
        
        
        // [DEBUG] Field: D75FundCode, is_external=, is_static_class=False, static_prefix=
        private string _D75FundCode ="";
        
        
        
        
        // [DEBUG] Field: Filler91, is_external=, is_static_class=False, static_prefix=
        private string _Filler91 ="";
        
        
        
        
        // [DEBUG] Field: D75DatastreamStockId, is_external=, is_static_class=False, static_prefix=
        private string _D75DatastreamStockId ="";
        
        
        
        
        // [DEBUG] Field: Filler92, is_external=, is_static_class=False, static_prefix=
        private string _Filler92 ="";
        
        
        
        
        // [DEBUG] Field: D75MicrocgtBookCost, is_external=, is_static_class=False, static_prefix=
        private decimal _D75MicrocgtBookCost =0;
        
        
        
        
        // [DEBUG] Field: Filler93, is_external=, is_static_class=False, static_prefix=
        private string _Filler93 ="";
        
        
        
        
        // [DEBUG] Field: D75UnGain, is_external=, is_static_class=False, static_prefix=
        private decimal _D75UnGain =0;
        
        
        
        
        // [DEBUG] Field: Filler94, is_external=, is_static_class=False, static_prefix=
        private string _Filler94 ="";
        
        
        
        
        // [DEBUG] Field: D75UnLoss, is_external=, is_static_class=False, static_prefix=
        private decimal _D75UnLoss =0;
        
        
        
        
        // [DEBUG] Field: Filler95, is_external=, is_static_class=False, static_prefix=
        private string _Filler95 ="";
        
        
        
        
        // [DEBUG] Field: D75SecurityType, is_external=, is_static_class=False, static_prefix=
        private string _D75SecurityType ="";
        
        
        
        
        // [DEBUG] Field: Filler96, is_external=, is_static_class=False, static_prefix=
        private string _Filler96 ="";
        
        
        
        
        // [DEBUG] Field: D75HoldingSign, is_external=, is_static_class=False, static_prefix=
        private string _D75HoldingSign ="";
        
        
        
        
        // [DEBUG] Field: Filler97, is_external=, is_static_class=False, static_prefix=
        private string _Filler97 ="";
        
        
        
        
    public Filler89() {}
    
    public Filler89(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller90(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD75FundCode(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller91(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD75DatastreamStockId(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller92(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD75MicrocgtBookCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetFiller93(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD75UnGain(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetFiller94(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD75UnLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetFiller95(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD75SecurityType(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller96(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD75HoldingSign(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller97(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetFiller89AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler90.PadRight(0));
        result.Append(_D75FundCode.PadRight(0));
        result.Append(_Filler91.PadRight(0));
        result.Append(_D75DatastreamStockId.PadRight(0));
        result.Append(_Filler92.PadRight(0));
        result.Append(_D75MicrocgtBookCost.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler93.PadRight(0));
        result.Append(_D75UnGain.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler94.PadRight(0));
        result.Append(_D75UnLoss.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler95.PadRight(0));
        result.Append(_D75SecurityType.PadRight(0));
        result.Append(_Filler96.PadRight(0));
        result.Append(_D75HoldingSign.PadRight(0));
        result.Append(_Filler97.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetFiller89AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller90(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD75FundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller91(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD75DatastreamStockId(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller92(extracted);
        }
        offset += 0;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD75MicrocgtBookCost(parsedDec);
        }
        offset += 13;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller93(extracted);
        }
        offset += 0;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD75UnGain(parsedDec);
        }
        offset += 13;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller94(extracted);
        }
        offset += 0;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD75UnLoss(parsedDec);
        }
        offset += 13;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller95(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD75SecurityType(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller96(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD75HoldingSign(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller97(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller90()
    {
        return _Filler90;
    }
    
    // Standard Setter
    public void SetFiller90(string value)
    {
        _Filler90 = value;
    }
    
    // Get<>AsString()
    public string GetFiller90AsString()
    {
        return _Filler90.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller90AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler90 = value;
    }
    
    // Standard Getter
    public string GetD75FundCode()
    {
        return _D75FundCode;
    }
    
    // Standard Setter
    public void SetD75FundCode(string value)
    {
        _D75FundCode = value;
    }
    
    // Get<>AsString()
    public string GetD75FundCodeAsString()
    {
        return _D75FundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD75FundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D75FundCode = value;
    }
    
    // Standard Getter
    public string GetFiller91()
    {
        return _Filler91;
    }
    
    // Standard Setter
    public void SetFiller91(string value)
    {
        _Filler91 = value;
    }
    
    // Get<>AsString()
    public string GetFiller91AsString()
    {
        return _Filler91.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller91AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler91 = value;
    }
    
    // Standard Getter
    public string GetD75DatastreamStockId()
    {
        return _D75DatastreamStockId;
    }
    
    // Standard Setter
    public void SetD75DatastreamStockId(string value)
    {
        _D75DatastreamStockId = value;
    }
    
    // Get<>AsString()
    public string GetD75DatastreamStockIdAsString()
    {
        return _D75DatastreamStockId.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD75DatastreamStockIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D75DatastreamStockId = value;
    }
    
    // Standard Getter
    public string GetFiller92()
    {
        return _Filler92;
    }
    
    // Standard Setter
    public void SetFiller92(string value)
    {
        _Filler92 = value;
    }
    
    // Get<>AsString()
    public string GetFiller92AsString()
    {
        return _Filler92.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller92AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler92 = value;
    }
    
    // Standard Getter
    public decimal GetD75MicrocgtBookCost()
    {
        return _D75MicrocgtBookCost;
    }
    
    // Standard Setter
    public void SetD75MicrocgtBookCost(decimal value)
    {
        _D75MicrocgtBookCost = value;
    }
    
    // Get<>AsString()
    public string GetD75MicrocgtBookCostAsString()
    {
        return _D75MicrocgtBookCost.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD75MicrocgtBookCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D75MicrocgtBookCost = parsed;
    }
    
    // Standard Getter
    public string GetFiller93()
    {
        return _Filler93;
    }
    
    // Standard Setter
    public void SetFiller93(string value)
    {
        _Filler93 = value;
    }
    
    // Get<>AsString()
    public string GetFiller93AsString()
    {
        return _Filler93.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller93AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler93 = value;
    }
    
    // Standard Getter
    public decimal GetD75UnGain()
    {
        return _D75UnGain;
    }
    
    // Standard Setter
    public void SetD75UnGain(decimal value)
    {
        _D75UnGain = value;
    }
    
    // Get<>AsString()
    public string GetD75UnGainAsString()
    {
        return _D75UnGain.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD75UnGainAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D75UnGain = parsed;
    }
    
    // Standard Getter
    public string GetFiller94()
    {
        return _Filler94;
    }
    
    // Standard Setter
    public void SetFiller94(string value)
    {
        _Filler94 = value;
    }
    
    // Get<>AsString()
    public string GetFiller94AsString()
    {
        return _Filler94.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller94AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler94 = value;
    }
    
    // Standard Getter
    public decimal GetD75UnLoss()
    {
        return _D75UnLoss;
    }
    
    // Standard Setter
    public void SetD75UnLoss(decimal value)
    {
        _D75UnLoss = value;
    }
    
    // Get<>AsString()
    public string GetD75UnLossAsString()
    {
        return _D75UnLoss.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD75UnLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D75UnLoss = parsed;
    }
    
    // Standard Getter
    public string GetFiller95()
    {
        return _Filler95;
    }
    
    // Standard Setter
    public void SetFiller95(string value)
    {
        _Filler95 = value;
    }
    
    // Get<>AsString()
    public string GetFiller95AsString()
    {
        return _Filler95.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller95AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler95 = value;
    }
    
    // Standard Getter
    public string GetD75SecurityType()
    {
        return _D75SecurityType;
    }
    
    // Standard Setter
    public void SetD75SecurityType(string value)
    {
        _D75SecurityType = value;
    }
    
    // Get<>AsString()
    public string GetD75SecurityTypeAsString()
    {
        return _D75SecurityType.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD75SecurityTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D75SecurityType = value;
    }
    
    // Standard Getter
    public string GetFiller96()
    {
        return _Filler96;
    }
    
    // Standard Setter
    public void SetFiller96(string value)
    {
        _Filler96 = value;
    }
    
    // Get<>AsString()
    public string GetFiller96AsString()
    {
        return _Filler96.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller96AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler96 = value;
    }
    
    // Standard Getter
    public string GetD75HoldingSign()
    {
        return _D75HoldingSign;
    }
    
    // Standard Setter
    public void SetD75HoldingSign(string value)
    {
        _D75HoldingSign = value;
    }
    
    // Get<>AsString()
    public string GetD75HoldingSignAsString()
    {
        return _D75HoldingSign.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD75HoldingSignAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D75HoldingSign = value;
    }
    
    // Standard Getter
    public string GetFiller97()
    {
        return _Filler97;
    }
    
    // Standard Setter
    public void SetFiller97(string value)
    {
        _Filler97 = value;
    }
    
    // Get<>AsString()
    public string GetFiller97AsString()
    {
        return _Filler97.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller97AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler97 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}