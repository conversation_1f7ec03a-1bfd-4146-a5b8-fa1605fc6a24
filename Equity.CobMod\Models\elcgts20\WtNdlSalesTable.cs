using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtNdlSalesTable Data Structure

public class WtNdlSalesTable
{
    private static int _size = 96;
    // [DEBUG] Class: WtNdlSalesTable, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler485, is_external=, is_static_class=False, static_prefix=
    private string _Filler485 ="NDL SALES TABLE===============";
    
    
    
    
    // [DEBUG] Field: WtndlsMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtndlsMaxTableSize =2000;
    
    
    
    
    // [DEBUG] Field: WtndlsOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtndlsOccurs =0;
    
    
    
    
    // [DEBUG] Field: WtndlsFundRecordTable, is_external=, is_static_class=False, static_prefix=
    private WtndlsFundRecordTable _WtndlsFundRecordTable = new WtndlsFundRecordTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtNdlSalesTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler485.PadRight(42));
        result.Append(_WtndlsMaxTableSize.ToString().PadLeft(5, '0'));
        result.Append(_WtndlsOccurs.ToString().PadLeft(5, '0'));
        result.Append(_WtndlsFundRecordTable.GetWtndlsFundRecordTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtNdlSalesTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 42 <= data.Length)
        {
            string extracted = data.Substring(offset, 42).Trim();
            SetFiller485(extracted);
        }
        offset += 42;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtndlsMaxTableSize(parsedInt);
        }
        offset += 5;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtndlsOccurs(parsedInt);
        }
        offset += 5;
        if (offset + 44 <= data.Length)
        {
            _WtndlsFundRecordTable.SetWtndlsFundRecordTableAsString(data.Substring(offset, 44));
        }
        else
        {
            _WtndlsFundRecordTable.SetWtndlsFundRecordTableAsString(data.Substring(offset));
        }
        offset += 44;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtNdlSalesTableAsString();
    }
    // Set<>String Override function
    public void SetWtNdlSalesTable(string value)
    {
        SetWtNdlSalesTableAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller485()
    {
        return _Filler485;
    }
    
    // Standard Setter
    public void SetFiller485(string value)
    {
        _Filler485 = value;
    }
    
    // Get<>AsString()
    public string GetFiller485AsString()
    {
        return _Filler485.PadRight(42);
    }
    
    // Set<>AsString()
    public void SetFiller485AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler485 = value;
    }
    
    // Standard Getter
    public int GetWtndlsMaxTableSize()
    {
        return _WtndlsMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtndlsMaxTableSize(int value)
    {
        _WtndlsMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtndlsMaxTableSizeAsString()
    {
        return _WtndlsMaxTableSize.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWtndlsMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtndlsMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtndlsOccurs()
    {
        return _WtndlsOccurs;
    }
    
    // Standard Setter
    public void SetWtndlsOccurs(int value)
    {
        _WtndlsOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtndlsOccursAsString()
    {
        return _WtndlsOccurs.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWtndlsOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtndlsOccurs = parsed;
    }
    
    // Standard Getter
    public WtndlsFundRecordTable GetWtndlsFundRecordTable()
    {
        return _WtndlsFundRecordTable;
    }
    
    // Standard Setter
    public void SetWtndlsFundRecordTable(WtndlsFundRecordTable value)
    {
        _WtndlsFundRecordTable = value;
    }
    
    // Get<>AsString()
    public string GetWtndlsFundRecordTableAsString()
    {
        return _WtndlsFundRecordTable != null ? _WtndlsFundRecordTable.GetWtndlsFundRecordTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtndlsFundRecordTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtndlsFundRecordTable == null)
        {
            _WtndlsFundRecordTable = new WtndlsFundRecordTable();
        }
        _WtndlsFundRecordTable.SetWtndlsFundRecordTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtndlsFundRecordTable(string value)
    {
        _WtndlsFundRecordTable.SetWtndlsFundRecordTableAsString(value);
    }
    // Nested Class: WtndlsFundRecordTable
    public class WtndlsFundRecordTable
    {
        private static int _size = 44;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtndlsElement, is_external=, is_static_class=False, static_prefix=
        private WtndlsFundRecordTable.WtndlsElement[] _WtndlsElement = new WtndlsFundRecordTable.WtndlsElement[2000];
        
        public void InitializeWtndlsElementArray()
        {
            for (int i = 0; i < 2000; i++)
            {
                _WtndlsElement[i] = new WtndlsFundRecordTable.WtndlsElement();
            }
        }
        
        
        
    public WtndlsFundRecordTable() {}
    
    public WtndlsFundRecordTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWtndlsElementArray();
        for (int i = 0; i < 2000; i++)
        {
            _WtndlsElement[i].SetWtndlsElementAsString(data.Substring(offset, 44));
            offset += 44;
        }
        
    }
    
    // Serialization methods
    public string GetWtndlsFundRecordTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 2000; i++)
        {
            result.Append(_WtndlsElement[i].GetWtndlsElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtndlsFundRecordTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 2000; i++)
        {
            if (offset + 44 > data.Length) break;
            string val = data.Substring(offset, 44);
            
            _WtndlsElement[i].SetWtndlsElementAsString(val);
            offset += 44;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtndlsElement
    public WtndlsElement GetWtndlsElementAt(int index)
    {
        return _WtndlsElement[index];
    }
    
    public void SetWtndlsElementAt(int index, WtndlsElement value)
    {
        _WtndlsElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtndlsElement GetWtndlsElement()
    {
        return _WtndlsElement != null && _WtndlsElement.Length > 0
        ? _WtndlsElement[0]
        : new WtndlsElement();
    }
    
    public void SetWtndlsElement(WtndlsElement value)
    {
        if (_WtndlsElement == null || _WtndlsElement.Length == 0)
        _WtndlsElement = new WtndlsElement[1];
        _WtndlsElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtndlsElement
    public class WtndlsElement
    {
        private static int _size = 44;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtndlsPurchaseIndex, is_external=, is_static_class=False, static_prefix=
        private string _WtndlsPurchaseIndex ="";
        
        
        
        
        // [DEBUG] Field: WtndlsSaleKey, is_external=, is_static_class=False, static_prefix=
        private WtndlsElement.WtndlsSaleKey _WtndlsSaleKey = new WtndlsElement.WtndlsSaleKey();
        
        
        
        
        // [DEBUG] Field: WtndlsNdlUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WtndlsNdlUnits =0;
        
        
        
        
        // [DEBUG] Field: WtndlsNdlLoss, is_external=, is_static_class=False, static_prefix=
        private decimal _WtndlsNdlLoss =0;
        
        
        
        
    public WtndlsElement() {}
    
    public WtndlsElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtndlsPurchaseIndex(data.Substring(offset, 4).Trim());
        offset += 4;
        _WtndlsSaleKey.SetWtndlsSaleKeyAsString(data.Substring(offset, WtndlsSaleKey.GetSize()));
        offset += 10;
        SetWtndlsNdlUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetWtndlsNdlLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        
    }
    
    // Serialization methods
    public string GetWtndlsElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtndlsPurchaseIndex.PadRight(4));
        result.Append(_WtndlsSaleKey.GetWtndlsSaleKeyAsString());
        result.Append(_WtndlsNdlUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtndlsNdlLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        
        return result.ToString();
    }
    
    public void SetWtndlsElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtndlsPurchaseIndex(extracted);
        }
        offset += 4;
        if (offset + 10 <= data.Length)
        {
            _WtndlsSaleKey.SetWtndlsSaleKeyAsString(data.Substring(offset, 10));
        }
        else
        {
            _WtndlsSaleKey.SetWtndlsSaleKeyAsString(data.Substring(offset));
        }
        offset += 10;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtndlsNdlUnits(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtndlsNdlLoss(parsedDec);
        }
        offset += 15;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtndlsPurchaseIndex()
    {
        return _WtndlsPurchaseIndex;
    }
    
    // Standard Setter
    public void SetWtndlsPurchaseIndex(string value)
    {
        _WtndlsPurchaseIndex = value;
    }
    
    // Get<>AsString()
    public string GetWtndlsPurchaseIndexAsString()
    {
        return _WtndlsPurchaseIndex.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtndlsPurchaseIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtndlsPurchaseIndex = value;
    }
    
    // Standard Getter
    public WtndlsSaleKey GetWtndlsSaleKey()
    {
        return _WtndlsSaleKey;
    }
    
    // Standard Setter
    public void SetWtndlsSaleKey(WtndlsSaleKey value)
    {
        _WtndlsSaleKey = value;
    }
    
    // Get<>AsString()
    public string GetWtndlsSaleKeyAsString()
    {
        return _WtndlsSaleKey != null ? _WtndlsSaleKey.GetWtndlsSaleKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtndlsSaleKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtndlsSaleKey == null)
        {
            _WtndlsSaleKey = new WtndlsSaleKey();
        }
        _WtndlsSaleKey.SetWtndlsSaleKeyAsString(value);
    }
    
    // Standard Getter
    public decimal GetWtndlsNdlUnits()
    {
        return _WtndlsNdlUnits;
    }
    
    // Standard Setter
    public void SetWtndlsNdlUnits(decimal value)
    {
        _WtndlsNdlUnits = value;
    }
    
    // Get<>AsString()
    public string GetWtndlsNdlUnitsAsString()
    {
        return _WtndlsNdlUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtndlsNdlUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtndlsNdlUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetWtndlsNdlLoss()
    {
        return _WtndlsNdlLoss;
    }
    
    // Standard Setter
    public void SetWtndlsNdlLoss(decimal value)
    {
        _WtndlsNdlLoss = value;
    }
    
    // Get<>AsString()
    public string GetWtndlsNdlLossAsString()
    {
        return _WtndlsNdlLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtndlsNdlLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtndlsNdlLoss = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtndlsSaleKey
    public class WtndlsSaleKey
    {
        private static int _size = 10;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtndlsFundCode, is_external=, is_static_class=False, static_prefix=
        private string _WtndlsFundCode ="";
        
        
        
        
        // [DEBUG] Field: WtndlsSedolCode, is_external=, is_static_class=False, static_prefix=
        private string _WtndlsSedolCode ="";
        
        
        
        
        // [DEBUG] Field: WtndlsContractNo, is_external=, is_static_class=False, static_prefix=
        private string _WtndlsContractNo ="";
        
        
        
        
        // [DEBUG] Field: WtndlsRecordCode, is_external=, is_static_class=False, static_prefix=
        private string _WtndlsRecordCode ="";
        
        
        
        
    public WtndlsSaleKey() {}
    
    public WtndlsSaleKey(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtndlsFundCode(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWtndlsSedolCode(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWtndlsContractNo(data.Substring(offset, 10).Trim());
        offset += 10;
        SetWtndlsRecordCode(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetWtndlsSaleKeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtndlsFundCode.PadRight(0));
        result.Append(_WtndlsSedolCode.PadRight(0));
        result.Append(_WtndlsContractNo.PadRight(10));
        result.Append(_WtndlsRecordCode.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetWtndlsSaleKeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWtndlsFundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWtndlsSedolCode(extracted);
        }
        offset += 0;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetWtndlsContractNo(extracted);
        }
        offset += 10;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWtndlsRecordCode(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtndlsFundCode()
    {
        return _WtndlsFundCode;
    }
    
    // Standard Setter
    public void SetWtndlsFundCode(string value)
    {
        _WtndlsFundCode = value;
    }
    
    // Get<>AsString()
    public string GetWtndlsFundCodeAsString()
    {
        return _WtndlsFundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWtndlsFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtndlsFundCode = value;
    }
    
    // Standard Getter
    public string GetWtndlsSedolCode()
    {
        return _WtndlsSedolCode;
    }
    
    // Standard Setter
    public void SetWtndlsSedolCode(string value)
    {
        _WtndlsSedolCode = value;
    }
    
    // Get<>AsString()
    public string GetWtndlsSedolCodeAsString()
    {
        return _WtndlsSedolCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWtndlsSedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtndlsSedolCode = value;
    }
    
    // Standard Getter
    public string GetWtndlsContractNo()
    {
        return _WtndlsContractNo;
    }
    
    // Standard Setter
    public void SetWtndlsContractNo(string value)
    {
        _WtndlsContractNo = value;
    }
    
    // Get<>AsString()
    public string GetWtndlsContractNoAsString()
    {
        return _WtndlsContractNo.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetWtndlsContractNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtndlsContractNo = value;
    }
    
    // Standard Getter
    public string GetWtndlsRecordCode()
    {
        return _WtndlsRecordCode;
    }
    
    // Standard Setter
    public void SetWtndlsRecordCode(string value)
    {
        _WtndlsRecordCode = value;
    }
    
    // Get<>AsString()
    public string GetWtndlsRecordCodeAsString()
    {
        return _WtndlsRecordCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWtndlsRecordCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtndlsRecordCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}

}}