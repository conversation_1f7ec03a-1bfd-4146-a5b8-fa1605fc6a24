using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D96File Data Structure

public class D96File
{
    private static int _size = 12;
    // [DEBUG] Class: D96File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler279, is_external=, is_static_class=False, static_prefix=
    private string _Filler279 ="$";
    
    
    
    
    // [DEBUG] Field: D96UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D96UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler280, is_external=, is_static_class=False, static_prefix=
    private string _Filler280 ="RM";
    
    
    
    
    // [DEBUG] Field: D96ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D96ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler281, is_external=, is_static_class=False, static_prefix=
    private string _Filler281 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD96FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler279.PadRight(1));
        result.Append(_D96UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler280.PadRight(2));
        result.Append(_D96ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler281.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD96FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller279(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD96UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller280(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD96ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller281(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD96FileAsString();
    }
    // Set<>String Override function
    public void SetD96File(string value)
    {
        SetD96FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller279()
    {
        return _Filler279;
    }
    
    // Standard Setter
    public void SetFiller279(string value)
    {
        _Filler279 = value;
    }
    
    // Get<>AsString()
    public string GetFiller279AsString()
    {
        return _Filler279.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller279AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler279 = value;
    }
    
    // Standard Getter
    public int GetD96UserNo()
    {
        return _D96UserNo;
    }
    
    // Standard Setter
    public void SetD96UserNo(int value)
    {
        _D96UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD96UserNoAsString()
    {
        return _D96UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD96UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D96UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller280()
    {
        return _Filler280;
    }
    
    // Standard Setter
    public void SetFiller280(string value)
    {
        _Filler280 = value;
    }
    
    // Get<>AsString()
    public string GetFiller280AsString()
    {
        return _Filler280.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller280AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler280 = value;
    }
    
    // Standard Getter
    public int GetD96ReportNo()
    {
        return _D96ReportNo;
    }
    
    // Standard Setter
    public void SetD96ReportNo(int value)
    {
        _D96ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD96ReportNoAsString()
    {
        return _D96ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD96ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D96ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller281()
    {
        return _Filler281;
    }
    
    // Standard Setter
    public void SetFiller281(string value)
    {
        _Filler281 = value;
    }
    
    // Get<>AsString()
    public string GetFiller281AsString()
    {
        return _Filler281.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller281AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler281 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}