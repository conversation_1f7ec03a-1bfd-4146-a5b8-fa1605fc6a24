using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdailyDTO
{// DTO class representing ReportFile Data Structure

public class ReportFile
{
    private static int _size = 12;
    // [DEBUG] Class: ReportFile, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler10, is_external=, is_static_class=False, static_prefix=
    private string _Filler10 ="$";
    
    
    
    
    // [DEBUG] Field: ReportUserNo, is_external=, is_static_class=False, static_prefix=
    private string _ReportUserNo ="9999";
    
    
    
    
    // [DEBUG] Field: Filler11, is_external=, is_static_class=False, static_prefix=
    private string _Filler11 ="TE";
    
    
    
    
    // [DEBUG] Field: ReportGenNo, is_external=, is_static_class=False, static_prefix=
    private string _ReportGenNo ="G";
    
    
    
    
    // [DEBUG] Field: Filler12, is_external=, is_static_class=False, static_prefix=
    private string _Filler12 =".DAT";
    
    
    
    
    
    // Serialization methods
    public string GetReportFileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler10.PadRight(1));
        result.Append(_ReportUserNo.PadRight(4));
        result.Append(_Filler11.PadRight(2));
        result.Append(_ReportGenNo.PadRight(1));
        result.Append(_Filler12.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetReportFileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller10(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetReportUserNo(extracted);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller11(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetReportGenNo(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller12(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetReportFileAsString();
    }
    // Set<>String Override function
    public void SetReportFile(string value)
    {
        SetReportFileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller10()
    {
        return _Filler10;
    }
    
    // Standard Setter
    public void SetFiller10(string value)
    {
        _Filler10 = value;
    }
    
    // Get<>AsString()
    public string GetFiller10AsString()
    {
        return _Filler10.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler10 = value;
    }
    
    // Standard Getter
    public string GetReportUserNo()
    {
        return _ReportUserNo;
    }
    
    // Standard Setter
    public void SetReportUserNo(string value)
    {
        _ReportUserNo = value;
    }
    
    // Get<>AsString()
    public string GetReportUserNoAsString()
    {
        return _ReportUserNo.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetReportUserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportUserNo = value;
    }
    
    // Standard Getter
    public string GetFiller11()
    {
        return _Filler11;
    }
    
    // Standard Setter
    public void SetFiller11(string value)
    {
        _Filler11 = value;
    }
    
    // Get<>AsString()
    public string GetFiller11AsString()
    {
        return _Filler11.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller11AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler11 = value;
    }
    
    // Standard Getter
    public string GetReportGenNo()
    {
        return _ReportGenNo;
    }
    
    // Standard Setter
    public void SetReportGenNo(string value)
    {
        _ReportGenNo = value;
    }
    
    // Get<>AsString()
    public string GetReportGenNoAsString()
    {
        return _ReportGenNo.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetReportGenNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ReportGenNo = value;
    }
    
    // Standard Getter
    public string GetFiller12()
    {
        return _Filler12;
    }
    
    // Standard Setter
    public void SetFiller12(string value)
    {
        _Filler12 = value;
    }
    
    // Get<>AsString()
    public string GetFiller12AsString()
    {
        return _Filler12.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller12AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler12 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}