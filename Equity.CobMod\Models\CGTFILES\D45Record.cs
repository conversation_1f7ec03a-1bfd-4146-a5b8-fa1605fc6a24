using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D45Record Data Structure

public class D45Record
{
    private static int _size = 16467;
    // [DEBUG] Class: D45Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: YeD45HeaderFront, is_external=, is_static_class=False, static_prefix=
    private YeD45HeaderFront _YeD45HeaderFront = new YeD45HeaderFront();
    
    
    
    
    // [DEBUG] Field: YeD45HeaderIds, is_external=, is_static_class=False, static_prefix=
    private YeD45HeaderIds _YeD45HeaderIds = new YeD45HeaderIds();
    
    
    
    
    // [DEBUG] Field: YeD45HeaderFiller, is_external=, is_static_class=False, static_prefix=
    private YeD45HeaderFiller _YeD45HeaderFiller = new YeD45HeaderFiller();
    
    
    
    
    
    // Serialization methods
    public string GetD45RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_YeD45HeaderFront.GetYeD45HeaderFrontAsString());
        result.Append(_YeD45HeaderIds.GetYeD45HeaderIdsAsString());
        result.Append(_YeD45HeaderFiller.GetYeD45HeaderFillerAsString());
        
        return result.ToString();
    }
    
    public void SetD45RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 454 <= data.Length)
        {
            _YeD45HeaderFront.SetYeD45HeaderFrontAsString(data.Substring(offset, 454));
        }
        else
        {
            _YeD45HeaderFront.SetYeD45HeaderFrontAsString(data.Substring(offset));
        }
        offset += 454;
        if (offset + 56 <= data.Length)
        {
            _YeD45HeaderIds.SetYeD45HeaderIdsAsString(data.Substring(offset, 56));
        }
        else
        {
            _YeD45HeaderIds.SetYeD45HeaderIdsAsString(data.Substring(offset));
        }
        offset += 56;
        if (offset + 15957 <= data.Length)
        {
            _YeD45HeaderFiller.SetYeD45HeaderFillerAsString(data.Substring(offset, 15957));
        }
        else
        {
            _YeD45HeaderFiller.SetYeD45HeaderFillerAsString(data.Substring(offset));
        }
        offset += 15957;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD45RecordAsString();
    }
    // Set<>String Override function
    public void SetD45Record(string value)
    {
        SetD45RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public YeD45HeaderFront GetYeD45HeaderFront()
    {
        return _YeD45HeaderFront;
    }
    
    // Standard Setter
    public void SetYeD45HeaderFront(YeD45HeaderFront value)
    {
        _YeD45HeaderFront = value;
    }
    
    // Get<>AsString()
    public string GetYeD45HeaderFrontAsString()
    {
        return _YeD45HeaderFront != null ? _YeD45HeaderFront.GetYeD45HeaderFrontAsString() : "";
    }
    
    // Set<>AsString()
    public void SetYeD45HeaderFrontAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_YeD45HeaderFront == null)
        {
            _YeD45HeaderFront = new YeD45HeaderFront();
        }
        _YeD45HeaderFront.SetYeD45HeaderFrontAsString(value);
    }
    
    // Standard Getter
    public YeD45HeaderIds GetYeD45HeaderIds()
    {
        return _YeD45HeaderIds;
    }
    
    // Standard Setter
    public void SetYeD45HeaderIds(YeD45HeaderIds value)
    {
        _YeD45HeaderIds = value;
    }
    
    // Get<>AsString()
    public string GetYeD45HeaderIdsAsString()
    {
        return _YeD45HeaderIds != null ? _YeD45HeaderIds.GetYeD45HeaderIdsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetYeD45HeaderIdsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_YeD45HeaderIds == null)
        {
            _YeD45HeaderIds = new YeD45HeaderIds();
        }
        _YeD45HeaderIds.SetYeD45HeaderIdsAsString(value);
    }
    
    // Standard Getter
    public YeD45HeaderFiller GetYeD45HeaderFiller()
    {
        return _YeD45HeaderFiller;
    }
    
    // Standard Setter
    public void SetYeD45HeaderFiller(YeD45HeaderFiller value)
    {
        _YeD45HeaderFiller = value;
    }
    
    // Get<>AsString()
    public string GetYeD45HeaderFillerAsString()
    {
        return _YeD45HeaderFiller != null ? _YeD45HeaderFiller.GetYeD45HeaderFillerAsString() : "";
    }
    
    // Set<>AsString()
    public void SetYeD45HeaderFillerAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_YeD45HeaderFiller == null)
        {
            _YeD45HeaderFiller = new YeD45HeaderFiller();
        }
        _YeD45HeaderFiller.SetYeD45HeaderFillerAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetYeD45HeaderFront(string value)
    {
        _YeD45HeaderFront.SetYeD45HeaderFrontAsString(value);
    }
    // Nested Class: YeD45HeaderFront
    public class YeD45HeaderFront
    {
        private static int _size = 454;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D45Key, is_external=, is_static_class=False, static_prefix=
        private YeD45HeaderFront.D45Key _D45Key = new YeD45HeaderFront.D45Key();
        
        
        
        
        // [DEBUG] Field: D451TransactionCategory, is_external=, is_static_class=False, static_prefix=
        private string _D451TransactionCategory ="";
        
        
        
        
        // [DEBUG] Field: D451DateTimeStamp, is_external=, is_static_class=False, static_prefix=
        private YeD45HeaderFront.D451DateTimeStamp _D451DateTimeStamp = new YeD45HeaderFront.D451DateTimeStamp();
        
        
        
        
        // [DEBUG] Field: Filler30, is_external=, is_static_class=False, static_prefix=
        private YeD45HeaderFront.Filler30 _Filler30 = new YeD45HeaderFront.Filler30();
        
        
        
        
        // [DEBUG] Field: D451CalcFlag, is_external=, is_static_class=False, static_prefix=
        private string _D451CalcFlag ="";
        
        
        
        
        // [DEBUG] Field: D451CalcDateR, is_external=, is_static_class=False, static_prefix=
        private string _D451CalcDateR ="";
        
        
        
        
        // [DEBUG] Field: D451CalcDateU, is_external=, is_static_class=False, static_prefix=
        private string _D451CalcDateU ="";
        
        
        
        
        // [DEBUG] Field: Filler31, is_external=, is_static_class=False, static_prefix=
        private string _Filler31 ="";
        
        
        
        
        // [DEBUG] Field: D451SecuritySortCode, is_external=, is_static_class=False, static_prefix=
        private string _D451SecuritySortCode ="";
        
        
        
        
        // [DEBUG] Field: D451CountryCode, is_external=, is_static_class=False, static_prefix=
        private string _D451CountryCode ="";
        
        
        
        
        // [DEBUG] Field: D451MainGroupCode, is_external=, is_static_class=False, static_prefix=
        private YeD45HeaderFront.D451MainGroupCode _D451MainGroupCode = new YeD45HeaderFront.D451MainGroupCode();
        
        
        
        
        // [DEBUG] Field: D451IndustrialClass, is_external=, is_static_class=False, static_prefix=
        private int _D451IndustrialClass =0;
        
        
        
        
        // [DEBUG] Field: D451SecurityType, is_external=, is_static_class=False, static_prefix=
        private string _D451SecurityType ="";
        
        
        // 88-level condition checks for D451SecurityType
        public bool IsD451SecurityOther()
        {
            if (this._D451SecurityType == "'A'") return true;
            return false;
        }
        public bool IsD451SecurityGilts()
        {
            if (this._D451SecurityType == "'B'") return true;
            return false;
        }
        public bool IsD451CorporateBonds()
        {
            if (this._D451SecurityType == "'C'") return true;
            return false;
        }
        public bool IsD451FixedInterest()
        {
            if (this._D451SecurityType == "'F'") return true;
            return false;
        }
        public bool IsD451Privatisation()
        {
            if (this._D451SecurityType == "'P'") return true;
            return false;
        }
        public bool IsD451SecurityTaxExemptGilt()
        {
            if (this._D451SecurityType == "'X'") return true;
            return false;
        }
        public bool IsD451RelevantSecurities()
        {
            if (this._D451SecurityType == "'B'") return true;
            if (this._D451SecurityType == "'C'") return true;
            if (this._D451SecurityType == "'X'") return true;
            if (this._D451SecurityType == "'F'") return true;
            return false;
        }
        public bool IsD451Gilts()
        {
            if (this._D451SecurityType == "'B'") return true;
            if (this._D451SecurityType == "'X'") return true;
            return false;
        }
        public bool IsD451IntegralUnits()
        {
            if (this._D451SecurityType == "'A'") return true;
            if (this._D451SecurityType == "'P'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: D451CurrentMarketPrice, is_external=, is_static_class=False, static_prefix=
        private decimal _D451CurrentMarketPrice =0;
        
        
        
        
        // [DEBUG] Field: D451PricePctIndicator, is_external=, is_static_class=False, static_prefix=
        private string _D451PricePctIndicator ="";
        
        
        
        
        // [DEBUG] Field: D451BfAccountingValue, is_external=, is_static_class=False, static_prefix=
        private decimal _D451BfAccountingValue =0;
        
        
        
        
        // [DEBUG] Field: D451AccountingValueYtd, is_external=, is_static_class=False, static_prefix=
        private decimal _D451AccountingValueYtd =0;
        
        
        
        
        // [DEBUG] Field: D451ProfitLoss, is_external=, is_static_class=False, static_prefix=
        private decimal _D451ProfitLoss =0;
        
        
        
        
        // [DEBUG] Field: D451IssuersName, is_external=, is_static_class=False, static_prefix=
        private string _D451IssuersName ="";
        
        
        
        
        // [DEBUG] Field: D451StockDescription, is_external=, is_static_class=False, static_prefix=
        private string _D451StockDescription ="";
        
        
        
        
        // [DEBUG] Field: D451IssuedCapital, is_external=, is_static_class=False, static_prefix=
        private decimal _D451IssuedCapital =0;
        
        
        
        
        // [DEBUG] Field: D451MovementIndicator, is_external=, is_static_class=False, static_prefix=
        private int _D451MovementIndicator =0;
        
        
        
        
        // [DEBUG] Field: D451Bf2PctHoldingDate, is_external=, is_static_class=False, static_prefix=
        private int _D451Bf2PctHoldingDate =0;
        
        
        
        
        // [DEBUG] Field: D4512PctHoldingDateYtd, is_external=, is_static_class=False, static_prefix=
        private int _D4512PctHoldingDateYtd =0;
        
        
        // 88-level condition checks for D4512PctHoldingDateYtd
        public bool IsD4512PctDateYtdNotSet()
        {
            if (this._D4512PctHoldingDateYtd == 999999) return true;
            return false;
        }
        public bool IsD4512PctDateYtdSet()
        {
            if (this._D4512PctHoldingDateYtd >= 101 && this._D4512PctHoldingDateYtd <= 991231) return true;
            return false;
        }
        
        
        // [DEBUG] Field: D451SecurityIndicator, is_external=, is_static_class=False, static_prefix=
        private string _D451SecurityIndicator ="";
        
        
        // 88-level condition checks for D451SecurityIndicator
        public bool IsD451QuotedSecurity()
        {
            if (this._D451SecurityIndicator == "'0'") return true;
            if (this._D451SecurityIndicator == "LOW-VALUES") return true;
            return false;
        }
        public bool IsD451UnquotedSecurity()
        {
            if (this._D451SecurityIndicator == "'1'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: D451TotalUnitsYtd, is_external=, is_static_class=False, static_prefix=
        private decimal _D451TotalUnitsYtd =0;
        
        
        
        
        // [DEBUG] Field: D451CapitalGainLoss, is_external=, is_static_class=False, static_prefix=
        private decimal _D451CapitalGainLoss =0;
        
        
        
        
        // [DEBUG] Field: D451UnrealGainLoss, is_external=, is_static_class=False, static_prefix=
        private decimal _D451UnrealGainLoss =0;
        
        
        
        
        // [DEBUG] Field: D451UnrealProfitLoss, is_external=, is_static_class=False, static_prefix=
        private decimal _D451UnrealProfitLoss =0;
        
        
        
        
        // [DEBUG] Field: Filler32, is_external=, is_static_class=False, static_prefix=
        private string _Filler32 ="";
        
        
        
        
        // [DEBUG] Field: D451OverRideFlag, is_external=, is_static_class=False, static_prefix=
        private string _D451OverRideFlag ="";
        
        
        
        
        // [DEBUG] Field: D451DateOfIssueR, is_external=, is_static_class=False, static_prefix=
        private YeD45HeaderFront.D451DateOfIssueR _D451DateOfIssueR = new YeD45HeaderFront.D451DateOfIssueR();
        
        
        
        
        // [DEBUG] Field: D451DateOfIssue, is_external=, is_static_class=False, static_prefix=
        private int _D451DateOfIssue =0;
        
        
        
        
        // [DEBUG] Field: D451IndexFromIssue, is_external=, is_static_class=False, static_prefix=
        private string _D451IndexFromIssue ="";
        
        
        
        
        // [DEBUG] Field: D451DeemedGainLoss, is_external=, is_static_class=False, static_prefix=
        private decimal _D451DeemedGainLoss =0;
        
        
        
        
        // [DEBUG] Field: D451DeemedProfitLoss, is_external=, is_static_class=False, static_prefix=
        private decimal _D451DeemedProfitLoss =0;
        
        
        
        
        // [DEBUG] Field: D451HoldingFlag, is_external=, is_static_class=False, static_prefix=
        private string _D451HoldingFlag ="";
        
        
        
        
        // [DEBUG] Field: D451BondOverride, is_external=, is_static_class=False, static_prefix=
        private string _D451BondOverride ="";
        
        
        
        
        // [DEBUG] Field: D451LrBasis, is_external=, is_static_class=False, static_prefix=
        private string _D451LrBasis ="";
        
        
        // 88-level condition checks for D451LrBasis
        public bool IsD451BondProcessing()
        {
            if (this._D451LrBasis == "'A'") return true;
            if (this._D451LrBasis == "'M'") return true;
            return false;
        }
        public bool IsD451MarkToMarket()
        {
            if (this._D451LrBasis == "'M'") return true;
            return false;
        }
        public bool IsD451Accrual()
        {
            if (this._D451LrBasis == "'A'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: D451BondMaturityDateR, is_external=, is_static_class=False, static_prefix=
        private YeD45HeaderFront.D451BondMaturityDateR _D451BondMaturityDateR = new YeD45HeaderFront.D451BondMaturityDateR();
        
        
        
        
        // [DEBUG] Field: D451BondMaturityDate, is_external=, is_static_class=False, static_prefix=
        private string _D451BondMaturityDate ="";
        
        
        
        
        // [DEBUG] Field: D451BondParValueX, is_external=, is_static_class=False, static_prefix=
        private string _D451BondParValueX ="";
        
        
        
        
        // [DEBUG] Field: D451BondParValue, is_external=, is_static_class=False, static_prefix=
        private decimal _D451BondParValue =0;
        
        
        
        
        // [DEBUG] Field: D451BondGainOverride, is_external=, is_static_class=False, static_prefix=
        private string _D451BondGainOverride ="";
        
        
        // 88-level condition checks for D451BondGainOverride
        public bool IsD451GainRealised()
        {
            if (this._D451BondGainOverride == "'R'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: D451BondGainLoss, is_external=, is_static_class=False, static_prefix=
        private decimal _D451BondGainLoss =0;
        
        
        
        
        // [DEBUG] Field: D451BondProfitLoss, is_external=, is_static_class=False, static_prefix=
        private decimal _D451BondProfitLoss =0;
        
        
        
        
        // [DEBUG] Field: D451AssetUsage, is_external=, is_static_class=False, static_prefix=
        private string _D451AssetUsage ="";
        
        
        
        
        // [DEBUG] Field: Filler33, is_external=, is_static_class=False, static_prefix=
        private string _Filler33 ="";
        
        
        
        
    public YeD45HeaderFront() {}
    
    public YeD45HeaderFront(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _D45Key.SetD45KeyAsString(data.Substring(offset, D45Key.GetSize()));
        offset += 25;
        SetD451TransactionCategory(data.Substring(offset, 2).Trim());
        offset += 2;
        _D451DateTimeStamp.SetD451DateTimeStampAsString(data.Substring(offset, D451DateTimeStamp.GetSize()));
        offset += 14;
        _Filler30.SetFiller30AsString(data.Substring(offset, Filler30.GetSize()));
        offset += 14;
        SetD451CalcFlag(data.Substring(offset, 4).Trim());
        offset += 4;
        SetD451CalcDateR(data.Substring(offset, 6).Trim());
        offset += 6;
        SetD451CalcDateU(data.Substring(offset, 6).Trim());
        offset += 6;
        SetFiller31(data.Substring(offset, 6).Trim());
        offset += 6;
        SetD451SecuritySortCode(data.Substring(offset, 15).Trim());
        offset += 15;
        SetD451CountryCode(data.Substring(offset, 3).Trim());
        offset += 3;
        _D451MainGroupCode.SetD451MainGroupCodeAsString(data.Substring(offset, D451MainGroupCode.GetSize()));
        offset += 3;
        SetD451IndustrialClass(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetD451SecurityType(data.Substring(offset, 1).Trim());
        offset += 1;
        SetD451CurrentMarketPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 10)));
        offset += 10;
        SetD451PricePctIndicator(data.Substring(offset, 1).Trim());
        offset += 1;
        SetD451BfAccountingValue(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
        offset += 17;
        SetD451AccountingValueYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
        offset += 17;
        SetD451ProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetD451IssuersName(data.Substring(offset, 35).Trim());
        offset += 35;
        SetD451StockDescription(data.Substring(offset, 40).Trim());
        offset += 40;
        SetD451IssuedCapital(PackedDecimalConverter.ToDecimal(data.Substring(offset, 10)));
        offset += 10;
        SetD451MovementIndicator(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetD451Bf2PctHoldingDate(int.Parse(data.Substring(offset, 6).Trim()));
        offset += 6;
        SetD4512PctHoldingDateYtd(int.Parse(data.Substring(offset, 6).Trim()));
        offset += 6;
        SetD451SecurityIndicator(data.Substring(offset, 1).Trim());
        offset += 1;
        SetD451TotalUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetD451CapitalGainLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetD451UnrealGainLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetD451UnrealProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetFiller32(data.Substring(offset, 1).Trim());
        offset += 1;
        SetD451OverRideFlag(data.Substring(offset, 1).Trim());
        offset += 1;
        _D451DateOfIssueR.SetD451DateOfIssueRAsString(data.Substring(offset, D451DateOfIssueR.GetSize()));
        offset += 6;
        SetD451DateOfIssue(int.Parse(data.Substring(offset, 6).Trim()));
        offset += 6;
        SetD451IndexFromIssue(data.Substring(offset, 1).Trim());
        offset += 1;
        SetD451DeemedGainLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetD451DeemedProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetD451HoldingFlag(data.Substring(offset, 1).Trim());
        offset += 1;
        SetD451BondOverride(data.Substring(offset, 1).Trim());
        offset += 1;
        SetD451LrBasis(data.Substring(offset, 1).Trim());
        offset += 1;
        _D451BondMaturityDateR.SetD451BondMaturityDateRAsString(data.Substring(offset, D451BondMaturityDateR.GetSize()));
        offset += 8;
        SetD451BondMaturityDate(data.Substring(offset, 8).Trim());
        offset += 8;
        SetD451BondParValueX(data.Substring(offset, 6).Trim());
        offset += 6;
        SetD451BondParValue(PackedDecimalConverter.ToDecimal(data.Substring(offset, 6)));
        offset += 6;
        SetD451BondGainOverride(data.Substring(offset, 1).Trim());
        offset += 1;
        SetD451BondGainLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetD451BondProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetD451AssetUsage(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller33(data.Substring(offset, 29).Trim());
        offset += 29;
        
    }
    
    // Serialization methods
    public string GetYeD45HeaderFrontAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D45Key.GetD45KeyAsString());
        result.Append(_D451TransactionCategory.PadRight(2));
        result.Append(_D451DateTimeStamp.GetD451DateTimeStampAsString());
        result.Append(_Filler30.GetFiller30AsString());
        result.Append(_D451CalcFlag.PadRight(4));
        result.Append(_D451CalcDateR.PadRight(6));
        result.Append(_D451CalcDateU.PadRight(6));
        result.Append(_Filler31.PadRight(6));
        result.Append(_D451SecuritySortCode.PadRight(15));
        result.Append(_D451CountryCode.PadRight(3));
        result.Append(_D451MainGroupCode.GetD451MainGroupCodeAsString());
        result.Append(_D451IndustrialClass.ToString().PadLeft(2, '0'));
        result.Append(_D451SecurityType.PadRight(1));
        result.Append(_D451CurrentMarketPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D451PricePctIndicator.PadRight(1));
        result.Append(_D451BfAccountingValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D451AccountingValueYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D451ProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D451IssuersName.PadRight(35));
        result.Append(_D451StockDescription.PadRight(40));
        result.Append(_D451IssuedCapital.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D451MovementIndicator.ToString().PadLeft(1, '0'));
        result.Append(_D451Bf2PctHoldingDate.ToString().PadLeft(6, '0'));
        result.Append(_D4512PctHoldingDateYtd.ToString().PadLeft(6, '0'));
        result.Append(_D451SecurityIndicator.PadRight(1));
        result.Append(_D451TotalUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D451CapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D451UnrealGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D451UnrealProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler32.PadRight(1));
        result.Append(_D451OverRideFlag.PadRight(1));
        result.Append(_D451DateOfIssueR.GetD451DateOfIssueRAsString());
        result.Append(_D451DateOfIssue.ToString().PadLeft(6, '0'));
        result.Append(_D451IndexFromIssue.PadRight(1));
        result.Append(_D451DeemedGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D451DeemedProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D451HoldingFlag.PadRight(1));
        result.Append(_D451BondOverride.PadRight(1));
        result.Append(_D451LrBasis.PadRight(1));
        result.Append(_D451BondMaturityDateR.GetD451BondMaturityDateRAsString());
        result.Append(_D451BondMaturityDate.PadRight(8));
        result.Append(_D451BondParValueX.PadRight(6));
        result.Append(_D451BondParValue.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D451BondGainOverride.PadRight(1));
        result.Append(_D451BondGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D451BondProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D451AssetUsage.PadRight(0));
        result.Append(_Filler33.PadRight(29));
        
        return result.ToString();
    }
    
    public void SetYeD45HeaderFrontAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 25 <= data.Length)
        {
            _D45Key.SetD45KeyAsString(data.Substring(offset, 25));
        }
        else
        {
            _D45Key.SetD45KeyAsString(data.Substring(offset));
        }
        offset += 25;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD451TransactionCategory(extracted);
        }
        offset += 2;
        if (offset + 14 <= data.Length)
        {
            _D451DateTimeStamp.SetD451DateTimeStampAsString(data.Substring(offset, 14));
        }
        else
        {
            _D451DateTimeStamp.SetD451DateTimeStampAsString(data.Substring(offset));
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            _Filler30.SetFiller30AsString(data.Substring(offset, 14));
        }
        else
        {
            _Filler30.SetFiller30AsString(data.Substring(offset));
        }
        offset += 14;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD451CalcFlag(extracted);
        }
        offset += 4;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetD451CalcDateR(extracted);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetD451CalcDateU(extracted);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetFiller31(extracted);
        }
        offset += 6;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            SetD451SecuritySortCode(extracted);
        }
        offset += 15;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD451CountryCode(extracted);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            _D451MainGroupCode.SetD451MainGroupCodeAsString(data.Substring(offset, 3));
        }
        else
        {
            _D451MainGroupCode.SetD451MainGroupCodeAsString(data.Substring(offset));
        }
        offset += 3;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD451IndustrialClass(parsedInt);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD451SecurityType(extracted);
        }
        offset += 1;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD451CurrentMarketPrice(parsedDec);
        }
        offset += 10;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD451PricePctIndicator(extracted);
        }
        offset += 1;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD451BfAccountingValue(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD451AccountingValueYtd(parsedDec);
        }
        offset += 17;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD451ProfitLoss(parsedDec);
        }
        offset += 15;
        if (offset + 35 <= data.Length)
        {
            string extracted = data.Substring(offset, 35).Trim();
            SetD451IssuersName(extracted);
        }
        offset += 35;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD451StockDescription(extracted);
        }
        offset += 40;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD451IssuedCapital(parsedDec);
        }
        offset += 10;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD451MovementIndicator(parsedInt);
        }
        offset += 1;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD451Bf2PctHoldingDate(parsedInt);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD4512PctHoldingDateYtd(parsedInt);
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD451SecurityIndicator(extracted);
        }
        offset += 1;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD451TotalUnitsYtd(parsedDec);
        }
        offset += 13;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD451CapitalGainLoss(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD451UnrealGainLoss(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD451UnrealProfitLoss(parsedDec);
        }
        offset += 15;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller32(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD451OverRideFlag(extracted);
        }
        offset += 1;
        if (offset + 6 <= data.Length)
        {
            _D451DateOfIssueR.SetD451DateOfIssueRAsString(data.Substring(offset, 6));
        }
        else
        {
            _D451DateOfIssueR.SetD451DateOfIssueRAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD451DateOfIssue(parsedInt);
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD451IndexFromIssue(extracted);
        }
        offset += 1;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD451DeemedGainLoss(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD451DeemedProfitLoss(parsedDec);
        }
        offset += 15;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD451HoldingFlag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD451BondOverride(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD451LrBasis(extracted);
        }
        offset += 1;
        if (offset + 8 <= data.Length)
        {
            _D451BondMaturityDateR.SetD451BondMaturityDateRAsString(data.Substring(offset, 8));
        }
        else
        {
            _D451BondMaturityDateR.SetD451BondMaturityDateRAsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetD451BondMaturityDate(extracted);
        }
        offset += 8;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetD451BondParValueX(extracted);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD451BondParValue(parsedDec);
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD451BondGainOverride(extracted);
        }
        offset += 1;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD451BondGainLoss(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD451BondProfitLoss(parsedDec);
        }
        offset += 15;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD451AssetUsage(extracted);
        }
        offset += 0;
        if (offset + 29 <= data.Length)
        {
            string extracted = data.Substring(offset, 29).Trim();
            SetFiller33(extracted);
        }
        offset += 29;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public D45Key GetD45Key()
    {
        return _D45Key;
    }
    
    // Standard Setter
    public void SetD45Key(D45Key value)
    {
        _D45Key = value;
    }
    
    // Get<>AsString()
    public string GetD45KeyAsString()
    {
        return _D45Key != null ? _D45Key.GetD45KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD45KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D45Key == null)
        {
            _D45Key = new D45Key();
        }
        _D45Key.SetD45KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD451TransactionCategory()
    {
        return _D451TransactionCategory;
    }
    
    // Standard Setter
    public void SetD451TransactionCategory(string value)
    {
        _D451TransactionCategory = value;
    }
    
    // Get<>AsString()
    public string GetD451TransactionCategoryAsString()
    {
        return _D451TransactionCategory.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD451TransactionCategoryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451TransactionCategory = value;
    }
    
    // Standard Getter
    public D451DateTimeStamp GetD451DateTimeStamp()
    {
        return _D451DateTimeStamp;
    }
    
    // Standard Setter
    public void SetD451DateTimeStamp(D451DateTimeStamp value)
    {
        _D451DateTimeStamp = value;
    }
    
    // Get<>AsString()
    public string GetD451DateTimeStampAsString()
    {
        return _D451DateTimeStamp != null ? _D451DateTimeStamp.GetD451DateTimeStampAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD451DateTimeStampAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D451DateTimeStamp == null)
        {
            _D451DateTimeStamp = new D451DateTimeStamp();
        }
        _D451DateTimeStamp.SetD451DateTimeStampAsString(value);
    }
    
    // Standard Getter
    public Filler30 GetFiller30()
    {
        return _Filler30;
    }
    
    // Standard Setter
    public void SetFiller30(Filler30 value)
    {
        _Filler30 = value;
    }
    
    // Get<>AsString()
    public string GetFiller30AsString()
    {
        return _Filler30 != null ? _Filler30.GetFiller30AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller30AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler30 == null)
        {
            _Filler30 = new Filler30();
        }
        _Filler30.SetFiller30AsString(value);
    }
    
    // Standard Getter
    public string GetD451CalcFlag()
    {
        return _D451CalcFlag;
    }
    
    // Standard Setter
    public void SetD451CalcFlag(string value)
    {
        _D451CalcFlag = value;
    }
    
    // Get<>AsString()
    public string GetD451CalcFlagAsString()
    {
        return _D451CalcFlag.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD451CalcFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451CalcFlag = value;
    }
    
    // Standard Getter
    public string GetD451CalcDateR()
    {
        return _D451CalcDateR;
    }
    
    // Standard Setter
    public void SetD451CalcDateR(string value)
    {
        _D451CalcDateR = value;
    }
    
    // Get<>AsString()
    public string GetD451CalcDateRAsString()
    {
        return _D451CalcDateR.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetD451CalcDateRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451CalcDateR = value;
    }
    
    // Standard Getter
    public string GetD451CalcDateU()
    {
        return _D451CalcDateU;
    }
    
    // Standard Setter
    public void SetD451CalcDateU(string value)
    {
        _D451CalcDateU = value;
    }
    
    // Get<>AsString()
    public string GetD451CalcDateUAsString()
    {
        return _D451CalcDateU.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetD451CalcDateUAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451CalcDateU = value;
    }
    
    // Standard Getter
    public string GetFiller31()
    {
        return _Filler31;
    }
    
    // Standard Setter
    public void SetFiller31(string value)
    {
        _Filler31 = value;
    }
    
    // Get<>AsString()
    public string GetFiller31AsString()
    {
        return _Filler31.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetFiller31AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler31 = value;
    }
    
    // Standard Getter
    public string GetD451SecuritySortCode()
    {
        return _D451SecuritySortCode;
    }
    
    // Standard Setter
    public void SetD451SecuritySortCode(string value)
    {
        _D451SecuritySortCode = value;
    }
    
    // Get<>AsString()
    public string GetD451SecuritySortCodeAsString()
    {
        return _D451SecuritySortCode.PadRight(15);
    }
    
    // Set<>AsString()
    public void SetD451SecuritySortCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451SecuritySortCode = value;
    }
    
    // Standard Getter
    public string GetD451CountryCode()
    {
        return _D451CountryCode;
    }
    
    // Standard Setter
    public void SetD451CountryCode(string value)
    {
        _D451CountryCode = value;
    }
    
    // Get<>AsString()
    public string GetD451CountryCodeAsString()
    {
        return _D451CountryCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD451CountryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451CountryCode = value;
    }
    
    // Standard Getter
    public D451MainGroupCode GetD451MainGroupCode()
    {
        return _D451MainGroupCode;
    }
    
    // Standard Setter
    public void SetD451MainGroupCode(D451MainGroupCode value)
    {
        _D451MainGroupCode = value;
    }
    
    // Get<>AsString()
    public string GetD451MainGroupCodeAsString()
    {
        return _D451MainGroupCode != null ? _D451MainGroupCode.GetD451MainGroupCodeAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD451MainGroupCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D451MainGroupCode == null)
        {
            _D451MainGroupCode = new D451MainGroupCode();
        }
        _D451MainGroupCode.SetD451MainGroupCodeAsString(value);
    }
    
    // Standard Getter
    public int GetD451IndustrialClass()
    {
        return _D451IndustrialClass;
    }
    
    // Standard Setter
    public void SetD451IndustrialClass(int value)
    {
        _D451IndustrialClass = value;
    }
    
    // Get<>AsString()
    public string GetD451IndustrialClassAsString()
    {
        return _D451IndustrialClass.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD451IndustrialClassAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D451IndustrialClass = parsed;
    }
    
    // Standard Getter
    public string GetD451SecurityType()
    {
        return _D451SecurityType;
    }
    
    // Standard Setter
    public void SetD451SecurityType(string value)
    {
        _D451SecurityType = value;
    }
    
    // Get<>AsString()
    public string GetD451SecurityTypeAsString()
    {
        return _D451SecurityType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD451SecurityTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451SecurityType = value;
    }
    
    // Standard Getter
    public decimal GetD451CurrentMarketPrice()
    {
        return _D451CurrentMarketPrice;
    }
    
    // Standard Setter
    public void SetD451CurrentMarketPrice(decimal value)
    {
        _D451CurrentMarketPrice = value;
    }
    
    // Get<>AsString()
    public string GetD451CurrentMarketPriceAsString()
    {
        return _D451CurrentMarketPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD451CurrentMarketPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D451CurrentMarketPrice = parsed;
    }
    
    // Standard Getter
    public string GetD451PricePctIndicator()
    {
        return _D451PricePctIndicator;
    }
    
    // Standard Setter
    public void SetD451PricePctIndicator(string value)
    {
        _D451PricePctIndicator = value;
    }
    
    // Get<>AsString()
    public string GetD451PricePctIndicatorAsString()
    {
        return _D451PricePctIndicator.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD451PricePctIndicatorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451PricePctIndicator = value;
    }
    
    // Standard Getter
    public decimal GetD451BfAccountingValue()
    {
        return _D451BfAccountingValue;
    }
    
    // Standard Setter
    public void SetD451BfAccountingValue(decimal value)
    {
        _D451BfAccountingValue = value;
    }
    
    // Get<>AsString()
    public string GetD451BfAccountingValueAsString()
    {
        return _D451BfAccountingValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD451BfAccountingValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D451BfAccountingValue = parsed;
    }
    
    // Standard Getter
    public decimal GetD451AccountingValueYtd()
    {
        return _D451AccountingValueYtd;
    }
    
    // Standard Setter
    public void SetD451AccountingValueYtd(decimal value)
    {
        _D451AccountingValueYtd = value;
    }
    
    // Get<>AsString()
    public string GetD451AccountingValueYtdAsString()
    {
        return _D451AccountingValueYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD451AccountingValueYtdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D451AccountingValueYtd = parsed;
    }
    
    // Standard Getter
    public decimal GetD451ProfitLoss()
    {
        return _D451ProfitLoss;
    }
    
    // Standard Setter
    public void SetD451ProfitLoss(decimal value)
    {
        _D451ProfitLoss = value;
    }
    
    // Get<>AsString()
    public string GetD451ProfitLossAsString()
    {
        return _D451ProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD451ProfitLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D451ProfitLoss = parsed;
    }
    
    // Standard Getter
    public string GetD451IssuersName()
    {
        return _D451IssuersName;
    }
    
    // Standard Setter
    public void SetD451IssuersName(string value)
    {
        _D451IssuersName = value;
    }
    
    // Get<>AsString()
    public string GetD451IssuersNameAsString()
    {
        return _D451IssuersName.PadRight(35);
    }
    
    // Set<>AsString()
    public void SetD451IssuersNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451IssuersName = value;
    }
    
    // Standard Getter
    public string GetD451StockDescription()
    {
        return _D451StockDescription;
    }
    
    // Standard Setter
    public void SetD451StockDescription(string value)
    {
        _D451StockDescription = value;
    }
    
    // Get<>AsString()
    public string GetD451StockDescriptionAsString()
    {
        return _D451StockDescription.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD451StockDescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451StockDescription = value;
    }
    
    // Standard Getter
    public decimal GetD451IssuedCapital()
    {
        return _D451IssuedCapital;
    }
    
    // Standard Setter
    public void SetD451IssuedCapital(decimal value)
    {
        _D451IssuedCapital = value;
    }
    
    // Get<>AsString()
    public string GetD451IssuedCapitalAsString()
    {
        return _D451IssuedCapital.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD451IssuedCapitalAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D451IssuedCapital = parsed;
    }
    
    // Standard Getter
    public int GetD451MovementIndicator()
    {
        return _D451MovementIndicator;
    }
    
    // Standard Setter
    public void SetD451MovementIndicator(int value)
    {
        _D451MovementIndicator = value;
    }
    
    // Get<>AsString()
    public string GetD451MovementIndicatorAsString()
    {
        return _D451MovementIndicator.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD451MovementIndicatorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D451MovementIndicator = parsed;
    }
    
    // Standard Getter
    public int GetD451Bf2PctHoldingDate()
    {
        return _D451Bf2PctHoldingDate;
    }
    
    // Standard Setter
    public void SetD451Bf2PctHoldingDate(int value)
    {
        _D451Bf2PctHoldingDate = value;
    }
    
    // Get<>AsString()
    public string GetD451Bf2PctHoldingDateAsString()
    {
        return _D451Bf2PctHoldingDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD451Bf2PctHoldingDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D451Bf2PctHoldingDate = parsed;
    }
    
    // Standard Getter
    public int GetD4512PctHoldingDateYtd()
    {
        return _D4512PctHoldingDateYtd;
    }
    
    // Standard Setter
    public void SetD4512PctHoldingDateYtd(int value)
    {
        _D4512PctHoldingDateYtd = value;
    }
    
    // Get<>AsString()
    public string GetD4512PctHoldingDateYtdAsString()
    {
        return _D4512PctHoldingDateYtd.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD4512PctHoldingDateYtdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D4512PctHoldingDateYtd = parsed;
    }
    
    // Standard Getter
    public string GetD451SecurityIndicator()
    {
        return _D451SecurityIndicator;
    }
    
    // Standard Setter
    public void SetD451SecurityIndicator(string value)
    {
        _D451SecurityIndicator = value;
    }
    
    // Get<>AsString()
    public string GetD451SecurityIndicatorAsString()
    {
        return _D451SecurityIndicator.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD451SecurityIndicatorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451SecurityIndicator = value;
    }
    
    // Standard Getter
    public decimal GetD451TotalUnitsYtd()
    {
        return _D451TotalUnitsYtd;
    }
    
    // Standard Setter
    public void SetD451TotalUnitsYtd(decimal value)
    {
        _D451TotalUnitsYtd = value;
    }
    
    // Get<>AsString()
    public string GetD451TotalUnitsYtdAsString()
    {
        return _D451TotalUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD451TotalUnitsYtdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D451TotalUnitsYtd = parsed;
    }
    
    // Standard Getter
    public decimal GetD451CapitalGainLoss()
    {
        return _D451CapitalGainLoss;
    }
    
    // Standard Setter
    public void SetD451CapitalGainLoss(decimal value)
    {
        _D451CapitalGainLoss = value;
    }
    
    // Get<>AsString()
    public string GetD451CapitalGainLossAsString()
    {
        return _D451CapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD451CapitalGainLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D451CapitalGainLoss = parsed;
    }
    
    // Standard Getter
    public decimal GetD451UnrealGainLoss()
    {
        return _D451UnrealGainLoss;
    }
    
    // Standard Setter
    public void SetD451UnrealGainLoss(decimal value)
    {
        _D451UnrealGainLoss = value;
    }
    
    // Get<>AsString()
    public string GetD451UnrealGainLossAsString()
    {
        return _D451UnrealGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD451UnrealGainLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D451UnrealGainLoss = parsed;
    }
    
    // Standard Getter
    public decimal GetD451UnrealProfitLoss()
    {
        return _D451UnrealProfitLoss;
    }
    
    // Standard Setter
    public void SetD451UnrealProfitLoss(decimal value)
    {
        _D451UnrealProfitLoss = value;
    }
    
    // Get<>AsString()
    public string GetD451UnrealProfitLossAsString()
    {
        return _D451UnrealProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD451UnrealProfitLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D451UnrealProfitLoss = parsed;
    }
    
    // Standard Getter
    public string GetFiller32()
    {
        return _Filler32;
    }
    
    // Standard Setter
    public void SetFiller32(string value)
    {
        _Filler32 = value;
    }
    
    // Get<>AsString()
    public string GetFiller32AsString()
    {
        return _Filler32.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller32AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler32 = value;
    }
    
    // Standard Getter
    public string GetD451OverRideFlag()
    {
        return _D451OverRideFlag;
    }
    
    // Standard Setter
    public void SetD451OverRideFlag(string value)
    {
        _D451OverRideFlag = value;
    }
    
    // Get<>AsString()
    public string GetD451OverRideFlagAsString()
    {
        return _D451OverRideFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD451OverRideFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451OverRideFlag = value;
    }
    
    // Standard Getter
    public D451DateOfIssueR GetD451DateOfIssueR()
    {
        return _D451DateOfIssueR;
    }
    
    // Standard Setter
    public void SetD451DateOfIssueR(D451DateOfIssueR value)
    {
        _D451DateOfIssueR = value;
    }
    
    // Get<>AsString()
    public string GetD451DateOfIssueRAsString()
    {
        return _D451DateOfIssueR != null ? _D451DateOfIssueR.GetD451DateOfIssueRAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD451DateOfIssueRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D451DateOfIssueR == null)
        {
            _D451DateOfIssueR = new D451DateOfIssueR();
        }
        _D451DateOfIssueR.SetD451DateOfIssueRAsString(value);
    }
    
    // Standard Getter
    public int GetD451DateOfIssue()
    {
        return _D451DateOfIssue;
    }
    
    // Standard Setter
    public void SetD451DateOfIssue(int value)
    {
        _D451DateOfIssue = value;
    }
    
    // Get<>AsString()
    public string GetD451DateOfIssueAsString()
    {
        return _D451DateOfIssue.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD451DateOfIssueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D451DateOfIssue = parsed;
    }
    
    // Standard Getter
    public string GetD451IndexFromIssue()
    {
        return _D451IndexFromIssue;
    }
    
    // Standard Setter
    public void SetD451IndexFromIssue(string value)
    {
        _D451IndexFromIssue = value;
    }
    
    // Get<>AsString()
    public string GetD451IndexFromIssueAsString()
    {
        return _D451IndexFromIssue.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD451IndexFromIssueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451IndexFromIssue = value;
    }
    
    // Standard Getter
    public decimal GetD451DeemedGainLoss()
    {
        return _D451DeemedGainLoss;
    }
    
    // Standard Setter
    public void SetD451DeemedGainLoss(decimal value)
    {
        _D451DeemedGainLoss = value;
    }
    
    // Get<>AsString()
    public string GetD451DeemedGainLossAsString()
    {
        return _D451DeemedGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD451DeemedGainLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D451DeemedGainLoss = parsed;
    }
    
    // Standard Getter
    public decimal GetD451DeemedProfitLoss()
    {
        return _D451DeemedProfitLoss;
    }
    
    // Standard Setter
    public void SetD451DeemedProfitLoss(decimal value)
    {
        _D451DeemedProfitLoss = value;
    }
    
    // Get<>AsString()
    public string GetD451DeemedProfitLossAsString()
    {
        return _D451DeemedProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD451DeemedProfitLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D451DeemedProfitLoss = parsed;
    }
    
    // Standard Getter
    public string GetD451HoldingFlag()
    {
        return _D451HoldingFlag;
    }
    
    // Standard Setter
    public void SetD451HoldingFlag(string value)
    {
        _D451HoldingFlag = value;
    }
    
    // Get<>AsString()
    public string GetD451HoldingFlagAsString()
    {
        return _D451HoldingFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD451HoldingFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451HoldingFlag = value;
    }
    
    // Standard Getter
    public string GetD451BondOverride()
    {
        return _D451BondOverride;
    }
    
    // Standard Setter
    public void SetD451BondOverride(string value)
    {
        _D451BondOverride = value;
    }
    
    // Get<>AsString()
    public string GetD451BondOverrideAsString()
    {
        return _D451BondOverride.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD451BondOverrideAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451BondOverride = value;
    }
    
    // Standard Getter
    public string GetD451LrBasis()
    {
        return _D451LrBasis;
    }
    
    // Standard Setter
    public void SetD451LrBasis(string value)
    {
        _D451LrBasis = value;
    }
    
    // Get<>AsString()
    public string GetD451LrBasisAsString()
    {
        return _D451LrBasis.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD451LrBasisAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451LrBasis = value;
    }
    
    // Standard Getter
    public D451BondMaturityDateR GetD451BondMaturityDateR()
    {
        return _D451BondMaturityDateR;
    }
    
    // Standard Setter
    public void SetD451BondMaturityDateR(D451BondMaturityDateR value)
    {
        _D451BondMaturityDateR = value;
    }
    
    // Get<>AsString()
    public string GetD451BondMaturityDateRAsString()
    {
        return _D451BondMaturityDateR != null ? _D451BondMaturityDateR.GetD451BondMaturityDateRAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD451BondMaturityDateRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D451BondMaturityDateR == null)
        {
            _D451BondMaturityDateR = new D451BondMaturityDateR();
        }
        _D451BondMaturityDateR.SetD451BondMaturityDateRAsString(value);
    }
    
    // Standard Getter
    public string GetD451BondMaturityDate()
    {
        return _D451BondMaturityDate;
    }
    
    // Standard Setter
    public void SetD451BondMaturityDate(string value)
    {
        _D451BondMaturityDate = value;
    }
    
    // Get<>AsString()
    public string GetD451BondMaturityDateAsString()
    {
        return _D451BondMaturityDate.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetD451BondMaturityDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451BondMaturityDate = value;
    }
    
    // Standard Getter
    public string GetD451BondParValueX()
    {
        return _D451BondParValueX;
    }
    
    // Standard Setter
    public void SetD451BondParValueX(string value)
    {
        _D451BondParValueX = value;
    }
    
    // Get<>AsString()
    public string GetD451BondParValueXAsString()
    {
        return _D451BondParValueX.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetD451BondParValueXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451BondParValueX = value;
    }
    
    // Standard Getter
    public decimal GetD451BondParValue()
    {
        return _D451BondParValue;
    }
    
    // Standard Setter
    public void SetD451BondParValue(decimal value)
    {
        _D451BondParValue = value;
    }
    
    // Get<>AsString()
    public string GetD451BondParValueAsString()
    {
        return _D451BondParValue.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD451BondParValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D451BondParValue = parsed;
    }
    
    // Standard Getter
    public string GetD451BondGainOverride()
    {
        return _D451BondGainOverride;
    }
    
    // Standard Setter
    public void SetD451BondGainOverride(string value)
    {
        _D451BondGainOverride = value;
    }
    
    // Get<>AsString()
    public string GetD451BondGainOverrideAsString()
    {
        return _D451BondGainOverride.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD451BondGainOverrideAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451BondGainOverride = value;
    }
    
    // Standard Getter
    public decimal GetD451BondGainLoss()
    {
        return _D451BondGainLoss;
    }
    
    // Standard Setter
    public void SetD451BondGainLoss(decimal value)
    {
        _D451BondGainLoss = value;
    }
    
    // Get<>AsString()
    public string GetD451BondGainLossAsString()
    {
        return _D451BondGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD451BondGainLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D451BondGainLoss = parsed;
    }
    
    // Standard Getter
    public decimal GetD451BondProfitLoss()
    {
        return _D451BondProfitLoss;
    }
    
    // Standard Setter
    public void SetD451BondProfitLoss(decimal value)
    {
        _D451BondProfitLoss = value;
    }
    
    // Get<>AsString()
    public string GetD451BondProfitLossAsString()
    {
        return _D451BondProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD451BondProfitLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D451BondProfitLoss = parsed;
    }
    
    // Standard Getter
    public string GetD451AssetUsage()
    {
        return _D451AssetUsage;
    }
    
    // Standard Setter
    public void SetD451AssetUsage(string value)
    {
        _D451AssetUsage = value;
    }
    
    // Get<>AsString()
    public string GetD451AssetUsageAsString()
    {
        return _D451AssetUsage.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD451AssetUsageAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451AssetUsage = value;
    }
    
    // Standard Getter
    public string GetFiller33()
    {
        return _Filler33;
    }
    
    // Standard Setter
    public void SetFiller33(string value)
    {
        _Filler33 = value;
    }
    
    // Get<>AsString()
    public string GetFiller33AsString()
    {
        return _Filler33.PadRight(29);
    }
    
    // Set<>AsString()
    public void SetFiller33AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler33 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D45Key
    public class D45Key
    {
        private static int _size = 25;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D451Key, is_external=, is_static_class=False, static_prefix=
        private D45Key.D451Key _D451Key = new D45Key.D451Key();
        
        
        
        
    public D45Key() {}
    
    public D45Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _D451Key.SetD451KeyAsString(data.Substring(offset, D451Key.GetSize()));
        offset += 25;
        
    }
    
    // Serialization methods
    public string GetD45KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D451Key.GetD451KeyAsString());
        
        return result.ToString();
    }
    
    public void SetD45KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 25 <= data.Length)
        {
            _D451Key.SetD451KeyAsString(data.Substring(offset, 25));
        }
        else
        {
            _D451Key.SetD451KeyAsString(data.Substring(offset));
        }
        offset += 25;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public D451Key GetD451Key()
    {
        return _D451Key;
    }
    
    // Standard Setter
    public void SetD451Key(D451Key value)
    {
        _D451Key = value;
    }
    
    // Get<>AsString()
    public string GetD451KeyAsString()
    {
        return _D451Key != null ? _D451Key.GetD451KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD451KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D451Key == null)
        {
            _D451Key = new D451Key();
        }
        _D451Key.SetD451KeyAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D451Key
    public class D451Key
    {
        private static int _size = 25;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D451CalSedol, is_external=, is_static_class=False, static_prefix=
        private D451Key.D451CalSedol _D451CalSedol = new D451Key.D451CalSedol();
        
        
        
        
        // [DEBUG] Field: D451ContractNo, is_external=, is_static_class=False, static_prefix=
        private string _D451ContractNo ="";
        
        
        
        
        // [DEBUG] Field: D451RecordCode, is_external=, is_static_class=False, static_prefix=
        private int _D451RecordCode =0;
        
        
        
        
        // [DEBUG] Field: D451RecordCodeX, is_external=, is_static_class=False, static_prefix=
        private D451Key.D451RecordCodeX _D451RecordCodeX = new D451Key.D451RecordCodeX();
        
        
        
        
    public D451Key() {}
    
    public D451Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _D451CalSedol.SetD451CalSedolAsString(data.Substring(offset, D451CalSedol.GetSize()));
        offset += 11;
        SetD451ContractNo(data.Substring(offset, 10).Trim());
        offset += 10;
        SetD451RecordCode(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        _D451RecordCodeX.SetD451RecordCodeXAsString(data.Substring(offset, D451RecordCodeX.GetSize()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetD451KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D451CalSedol.GetD451CalSedolAsString());
        result.Append(_D451ContractNo.PadRight(10));
        result.Append(_D451RecordCode.ToString().PadLeft(2, '0'));
        result.Append(_D451RecordCodeX.GetD451RecordCodeXAsString());
        
        return result.ToString();
    }
    
    public void SetD451KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 11 <= data.Length)
        {
            _D451CalSedol.SetD451CalSedolAsString(data.Substring(offset, 11));
        }
        else
        {
            _D451CalSedol.SetD451CalSedolAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetD451ContractNo(extracted);
        }
        offset += 10;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD451RecordCode(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            _D451RecordCodeX.SetD451RecordCodeXAsString(data.Substring(offset, 2));
        }
        else
        {
            _D451RecordCodeX.SetD451RecordCodeXAsString(data.Substring(offset));
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public D451CalSedol GetD451CalSedol()
    {
        return _D451CalSedol;
    }
    
    // Standard Setter
    public void SetD451CalSedol(D451CalSedol value)
    {
        _D451CalSedol = value;
    }
    
    // Get<>AsString()
    public string GetD451CalSedolAsString()
    {
        return _D451CalSedol != null ? _D451CalSedol.GetD451CalSedolAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD451CalSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D451CalSedol == null)
        {
            _D451CalSedol = new D451CalSedol();
        }
        _D451CalSedol.SetD451CalSedolAsString(value);
    }
    
    // Standard Getter
    public string GetD451ContractNo()
    {
        return _D451ContractNo;
    }
    
    // Standard Setter
    public void SetD451ContractNo(string value)
    {
        _D451ContractNo = value;
    }
    
    // Get<>AsString()
    public string GetD451ContractNoAsString()
    {
        return _D451ContractNo.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetD451ContractNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451ContractNo = value;
    }
    
    // Standard Getter
    public int GetD451RecordCode()
    {
        return _D451RecordCode;
    }
    
    // Standard Setter
    public void SetD451RecordCode(int value)
    {
        _D451RecordCode = value;
    }
    
    // Get<>AsString()
    public string GetD451RecordCodeAsString()
    {
        return _D451RecordCode.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD451RecordCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D451RecordCode = parsed;
    }
    
    // Standard Getter
    public D451RecordCodeX GetD451RecordCodeX()
    {
        return _D451RecordCodeX;
    }
    
    // Standard Setter
    public void SetD451RecordCodeX(D451RecordCodeX value)
    {
        _D451RecordCodeX = value;
    }
    
    // Get<>AsString()
    public string GetD451RecordCodeXAsString()
    {
        return _D451RecordCodeX != null ? _D451RecordCodeX.GetD451RecordCodeXAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD451RecordCodeXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D451RecordCodeX == null)
        {
            _D451RecordCodeX = new D451RecordCodeX();
        }
        _D451RecordCodeX.SetD451RecordCodeXAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D451CalSedol
    public class D451CalSedol
    {
        private static int _size = 11;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D451CoAcLk, is_external=, is_static_class=False, static_prefix=
        private string _D451CoAcLk ="";
        
        
        
        
        // [DEBUG] Field: D451Sedol, is_external=, is_static_class=False, static_prefix=
        private string _D451Sedol ="";
        
        
        
        
    public D451CalSedol() {}
    
    public D451CalSedol(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD451CoAcLk(data.Substring(offset, 4).Trim());
        offset += 4;
        SetD451Sedol(data.Substring(offset, 7).Trim());
        offset += 7;
        
    }
    
    // Serialization methods
    public string GetD451CalSedolAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D451CoAcLk.PadRight(4));
        result.Append(_D451Sedol.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD451CalSedolAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD451CoAcLk(extracted);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetD451Sedol(extracted);
        }
        offset += 7;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD451CoAcLk()
    {
        return _D451CoAcLk;
    }
    
    // Standard Setter
    public void SetD451CoAcLk(string value)
    {
        _D451CoAcLk = value;
    }
    
    // Get<>AsString()
    public string GetD451CoAcLkAsString()
    {
        return _D451CoAcLk.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD451CoAcLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451CoAcLk = value;
    }
    
    // Standard Getter
    public string GetD451Sedol()
    {
        return _D451Sedol;
    }
    
    // Standard Setter
    public void SetD451Sedol(string value)
    {
        _D451Sedol = value;
    }
    
    // Get<>AsString()
    public string GetD451SedolAsString()
    {
        return _D451Sedol.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetD451SedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D451Sedol = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: D451RecordCodeX
public class D451RecordCodeX
{
    private static int _size = 2;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D451RecordCodeX1, is_external=, is_static_class=False, static_prefix=
    private string _D451RecordCodeX1 ="";
    
    
    
    
    // [DEBUG] Field: D451RecordCodeX2, is_external=, is_static_class=False, static_prefix=
    private string _D451RecordCodeX2 ="";
    
    
    
    
public D451RecordCodeX() {}

public D451RecordCodeX(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD451RecordCodeX1(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD451RecordCodeX2(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetD451RecordCodeXAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D451RecordCodeX1.PadRight(1));
    result.Append(_D451RecordCodeX2.PadRight(1));
    
    return result.ToString();
}

public void SetD451RecordCodeXAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD451RecordCodeX1(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD451RecordCodeX2(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetD451RecordCodeX1()
{
    return _D451RecordCodeX1;
}

// Standard Setter
public void SetD451RecordCodeX1(string value)
{
    _D451RecordCodeX1 = value;
}

// Get<>AsString()
public string GetD451RecordCodeX1AsString()
{
    return _D451RecordCodeX1.PadRight(1);
}

// Set<>AsString()
public void SetD451RecordCodeX1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451RecordCodeX1 = value;
}

// Standard Getter
public string GetD451RecordCodeX2()
{
    return _D451RecordCodeX2;
}

// Standard Setter
public void SetD451RecordCodeX2(string value)
{
    _D451RecordCodeX2 = value;
}

// Get<>AsString()
public string GetD451RecordCodeX2AsString()
{
    return _D451RecordCodeX2.PadRight(1);
}

// Set<>AsString()
public void SetD451RecordCodeX2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451RecordCodeX2 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}
// Nested Class: D451DateTimeStamp
public class D451DateTimeStamp
{
    private static int _size = 14;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D451DateStamp, is_external=, is_static_class=False, static_prefix=
    private string _D451DateStamp ="";
    
    
    
    
    // [DEBUG] Field: D451TimeStamp, is_external=, is_static_class=False, static_prefix=
    private string _D451TimeStamp ="";
    
    
    
    
public D451DateTimeStamp() {}

public D451DateTimeStamp(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD451DateStamp(data.Substring(offset, 6).Trim());
    offset += 6;
    SetD451TimeStamp(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetD451DateTimeStampAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D451DateStamp.PadRight(6));
    result.Append(_D451TimeStamp.PadRight(8));
    
    return result.ToString();
}

public void SetD451DateTimeStampAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetD451DateStamp(extracted);
    }
    offset += 6;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD451TimeStamp(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetD451DateStamp()
{
    return _D451DateStamp;
}

// Standard Setter
public void SetD451DateStamp(string value)
{
    _D451DateStamp = value;
}

// Get<>AsString()
public string GetD451DateStampAsString()
{
    return _D451DateStamp.PadRight(6);
}

// Set<>AsString()
public void SetD451DateStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451DateStamp = value;
}

// Standard Getter
public string GetD451TimeStamp()
{
    return _D451TimeStamp;
}

// Standard Setter
public void SetD451TimeStamp(string value)
{
    _D451TimeStamp = value;
}

// Get<>AsString()
public string GetD451TimeStampAsString()
{
    return _D451TimeStamp.PadRight(8);
}

// Set<>AsString()
public void SetD451TimeStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451TimeStamp = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler30
public class Filler30
{
    private static int _size = 14;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D451Stamp, is_external=, is_static_class=False, static_prefix=
    private string _D451Stamp ="";
    
    
    
    
    // [DEBUG] Field: D451Partly, is_external=, is_static_class=False, static_prefix=
    private string _D451Partly ="";
    
    
    
    
public Filler30() {}

public Filler30(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD451Stamp(data.Substring(offset, 13).Trim());
    offset += 13;
    SetD451Partly(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetFiller30AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D451Stamp.PadRight(13));
    result.Append(_D451Partly.PadRight(1));
    
    return result.ToString();
}

public void SetFiller30AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetD451Stamp(extracted);
    }
    offset += 13;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD451Partly(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetD451Stamp()
{
    return _D451Stamp;
}

// Standard Setter
public void SetD451Stamp(string value)
{
    _D451Stamp = value;
}

// Get<>AsString()
public string GetD451StampAsString()
{
    return _D451Stamp.PadRight(13);
}

// Set<>AsString()
public void SetD451StampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451Stamp = value;
}

// Standard Getter
public string GetD451Partly()
{
    return _D451Partly;
}

// Standard Setter
public void SetD451Partly(string value)
{
    _D451Partly = value;
}

// Get<>AsString()
public string GetD451PartlyAsString()
{
    return _D451Partly.PadRight(1);
}

// Set<>AsString()
public void SetD451PartlyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451Partly = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D451MainGroupCode
public class D451MainGroupCode
{
    private static int _size = 3;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D451GroupPrefix, is_external=, is_static_class=False, static_prefix=
    private string _D451GroupPrefix ="";
    
    
    
    
    // [DEBUG] Field: D451GroupCode, is_external=, is_static_class=False, static_prefix=
    private string _D451GroupCode ="";
    
    
    
    
public D451MainGroupCode() {}

public D451MainGroupCode(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD451GroupPrefix(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD451GroupCode(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD451MainGroupCodeAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D451GroupPrefix.PadRight(1));
    result.Append(_D451GroupCode.PadRight(2));
    
    return result.ToString();
}

public void SetD451MainGroupCodeAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD451GroupPrefix(extracted);
    }
    offset += 1;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD451GroupCode(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD451GroupPrefix()
{
    return _D451GroupPrefix;
}

// Standard Setter
public void SetD451GroupPrefix(string value)
{
    _D451GroupPrefix = value;
}

// Get<>AsString()
public string GetD451GroupPrefixAsString()
{
    return _D451GroupPrefix.PadRight(1);
}

// Set<>AsString()
public void SetD451GroupPrefixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451GroupPrefix = value;
}

// Standard Getter
public string GetD451GroupCode()
{
    return _D451GroupCode;
}

// Standard Setter
public void SetD451GroupCode(string value)
{
    _D451GroupCode = value;
}

// Get<>AsString()
public string GetD451GroupCodeAsString()
{
    return _D451GroupCode.PadRight(2);
}

// Set<>AsString()
public void SetD451GroupCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451GroupCode = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D451DateOfIssueR
public class D451DateOfIssueR
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D451DateOfIssueYy, is_external=, is_static_class=False, static_prefix=
    private string _D451DateOfIssueYy ="";
    
    
    
    
    // [DEBUG] Field: D451DateOfIssueMm, is_external=, is_static_class=False, static_prefix=
    private string _D451DateOfIssueMm ="";
    
    
    
    
    // [DEBUG] Field: D451DateOfIssueDd, is_external=, is_static_class=False, static_prefix=
    private string _D451DateOfIssueDd ="";
    
    
    
    
public D451DateOfIssueR() {}

public D451DateOfIssueR(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD451DateOfIssueYy(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD451DateOfIssueMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD451DateOfIssueDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD451DateOfIssueRAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D451DateOfIssueYy.PadRight(2));
    result.Append(_D451DateOfIssueMm.PadRight(2));
    result.Append(_D451DateOfIssueDd.PadRight(2));
    
    return result.ToString();
}

public void SetD451DateOfIssueRAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD451DateOfIssueYy(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD451DateOfIssueMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD451DateOfIssueDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD451DateOfIssueYy()
{
    return _D451DateOfIssueYy;
}

// Standard Setter
public void SetD451DateOfIssueYy(string value)
{
    _D451DateOfIssueYy = value;
}

// Get<>AsString()
public string GetD451DateOfIssueYyAsString()
{
    return _D451DateOfIssueYy.PadRight(2);
}

// Set<>AsString()
public void SetD451DateOfIssueYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451DateOfIssueYy = value;
}

// Standard Getter
public string GetD451DateOfIssueMm()
{
    return _D451DateOfIssueMm;
}

// Standard Setter
public void SetD451DateOfIssueMm(string value)
{
    _D451DateOfIssueMm = value;
}

// Get<>AsString()
public string GetD451DateOfIssueMmAsString()
{
    return _D451DateOfIssueMm.PadRight(2);
}

// Set<>AsString()
public void SetD451DateOfIssueMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451DateOfIssueMm = value;
}

// Standard Getter
public string GetD451DateOfIssueDd()
{
    return _D451DateOfIssueDd;
}

// Standard Setter
public void SetD451DateOfIssueDd(string value)
{
    _D451DateOfIssueDd = value;
}

// Get<>AsString()
public string GetD451DateOfIssueDdAsString()
{
    return _D451DateOfIssueDd.PadRight(2);
}

// Set<>AsString()
public void SetD451DateOfIssueDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451DateOfIssueDd = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D451BondMaturityDateR
public class D451BondMaturityDateR
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D451BondMaturityDateCcyy, is_external=, is_static_class=False, static_prefix=
    private string _D451BondMaturityDateCcyy ="";
    
    
    
    
    // [DEBUG] Field: D451BondMaturityDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D451BondMaturityDateMm ="";
    
    
    
    
    // [DEBUG] Field: D451BondMaturityDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D451BondMaturityDateDd ="";
    
    
    
    
public D451BondMaturityDateR() {}

public D451BondMaturityDateR(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD451BondMaturityDateCcyy(data.Substring(offset, 4).Trim());
    offset += 4;
    SetD451BondMaturityDateMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD451BondMaturityDateDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD451BondMaturityDateRAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D451BondMaturityDateCcyy.PadRight(4));
    result.Append(_D451BondMaturityDateMm.PadRight(2));
    result.Append(_D451BondMaturityDateDd.PadRight(2));
    
    return result.ToString();
}

public void SetD451BondMaturityDateRAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetD451BondMaturityDateCcyy(extracted);
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD451BondMaturityDateMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD451BondMaturityDateDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD451BondMaturityDateCcyy()
{
    return _D451BondMaturityDateCcyy;
}

// Standard Setter
public void SetD451BondMaturityDateCcyy(string value)
{
    _D451BondMaturityDateCcyy = value;
}

// Get<>AsString()
public string GetD451BondMaturityDateCcyyAsString()
{
    return _D451BondMaturityDateCcyy.PadRight(4);
}

// Set<>AsString()
public void SetD451BondMaturityDateCcyyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451BondMaturityDateCcyy = value;
}

// Standard Getter
public string GetD451BondMaturityDateMm()
{
    return _D451BondMaturityDateMm;
}

// Standard Setter
public void SetD451BondMaturityDateMm(string value)
{
    _D451BondMaturityDateMm = value;
}

// Get<>AsString()
public string GetD451BondMaturityDateMmAsString()
{
    return _D451BondMaturityDateMm.PadRight(2);
}

// Set<>AsString()
public void SetD451BondMaturityDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451BondMaturityDateMm = value;
}

// Standard Getter
public string GetD451BondMaturityDateDd()
{
    return _D451BondMaturityDateDd;
}

// Standard Setter
public void SetD451BondMaturityDateDd(string value)
{
    _D451BondMaturityDateDd = value;
}

// Get<>AsString()
public string GetD451BondMaturityDateDdAsString()
{
    return _D451BondMaturityDateDd.PadRight(2);
}

// Set<>AsString()
public void SetD451BondMaturityDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451BondMaturityDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetYeD45HeaderIds(string value)
{
    _YeD45HeaderIds.SetYeD45HeaderIdsAsString(value);
}
// Nested Class: YeD45HeaderIds
public class YeD45HeaderIds
{
    private static int _size = 56;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D451HeaderIds, is_external=, is_static_class=False, static_prefix=
    private YeD45HeaderIds.D451HeaderIds _D451HeaderIds = new YeD45HeaderIds.D451HeaderIds();
    
    
    
    
public YeD45HeaderIds() {}

public YeD45HeaderIds(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D451HeaderIds.SetD451HeaderIdsAsString(data.Substring(offset, D451HeaderIds.GetSize()));
    offset += 56;
    
}

// Serialization methods
public string GetYeD45HeaderIdsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D451HeaderIds.GetD451HeaderIdsAsString());
    
    return result.ToString();
}

public void SetYeD45HeaderIdsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 56 <= data.Length)
    {
        _D451HeaderIds.SetD451HeaderIdsAsString(data.Substring(offset, 56));
    }
    else
    {
        _D451HeaderIds.SetD451HeaderIdsAsString(data.Substring(offset));
    }
    offset += 56;
}

// Getter and Setter methods

// Standard Getter
public D451HeaderIds GetD451HeaderIds()
{
    return _D451HeaderIds;
}

// Standard Setter
public void SetD451HeaderIds(D451HeaderIds value)
{
    _D451HeaderIds = value;
}

// Get<>AsString()
public string GetD451HeaderIdsAsString()
{
    return _D451HeaderIds != null ? _D451HeaderIds.GetD451HeaderIdsAsString() : "";
}

// Set<>AsString()
public void SetD451HeaderIdsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D451HeaderIds == null)
    {
        _D451HeaderIds = new D451HeaderIds();
    }
    _D451HeaderIds.SetD451HeaderIdsAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D451HeaderIds
public class D451HeaderIds
{
    private static int _size = 56;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D451HoldingId, is_external=, is_static_class=False, static_prefix=
    private string _D451HoldingId ="";
    
    
    
    
    // [DEBUG] Field: D451FundId, is_external=, is_static_class=False, static_prefix=
    private string _D451FundId ="";
    
    
    
    
    // [DEBUG] Field: D451StockId, is_external=, is_static_class=False, static_prefix=
    private string _D451StockId ="";
    
    
    
    
    // [DEBUG] Field: D451BondOverrideId, is_external=, is_static_class=False, static_prefix=
    private string _D451BondOverrideId ="";
    
    
    
    
    // [DEBUG] Field: D451AssetUsageOverrideId, is_external=, is_static_class=False, static_prefix=
    private string _D451AssetUsageOverrideId ="";
    
    
    
    
    // [DEBUG] Field: Filler34, is_external=, is_static_class=False, static_prefix=
    private string _Filler34 ="";
    
    
    
    
    // [DEBUG] Field: Filler35, is_external=, is_static_class=False, static_prefix=
    private string _Filler35 ="";
    
    
    
    
public D451HeaderIds() {}

public D451HeaderIds(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD451HoldingId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetD451FundId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetD451StockId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetD451BondOverrideId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetD451AssetUsageOverrideId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetFiller34(data.Substring(offset, 8).Trim());
    offset += 8;
    SetFiller35(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetD451HeaderIdsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D451HoldingId.PadRight(8));
    result.Append(_D451FundId.PadRight(8));
    result.Append(_D451StockId.PadRight(8));
    result.Append(_D451BondOverrideId.PadRight(8));
    result.Append(_D451AssetUsageOverrideId.PadRight(8));
    result.Append(_Filler34.PadRight(8));
    result.Append(_Filler35.PadRight(8));
    
    return result.ToString();
}

public void SetD451HeaderIdsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD451HoldingId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD451FundId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD451StockId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD451BondOverrideId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD451AssetUsageOverrideId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller34(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller35(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetD451HoldingId()
{
    return _D451HoldingId;
}

// Standard Setter
public void SetD451HoldingId(string value)
{
    _D451HoldingId = value;
}

// Get<>AsString()
public string GetD451HoldingIdAsString()
{
    return _D451HoldingId.PadRight(8);
}

// Set<>AsString()
public void SetD451HoldingIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451HoldingId = value;
}

// Standard Getter
public string GetD451FundId()
{
    return _D451FundId;
}

// Standard Setter
public void SetD451FundId(string value)
{
    _D451FundId = value;
}

// Get<>AsString()
public string GetD451FundIdAsString()
{
    return _D451FundId.PadRight(8);
}

// Set<>AsString()
public void SetD451FundIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451FundId = value;
}

// Standard Getter
public string GetD451StockId()
{
    return _D451StockId;
}

// Standard Setter
public void SetD451StockId(string value)
{
    _D451StockId = value;
}

// Get<>AsString()
public string GetD451StockIdAsString()
{
    return _D451StockId.PadRight(8);
}

// Set<>AsString()
public void SetD451StockIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451StockId = value;
}

// Standard Getter
public string GetD451BondOverrideId()
{
    return _D451BondOverrideId;
}

// Standard Setter
public void SetD451BondOverrideId(string value)
{
    _D451BondOverrideId = value;
}

// Get<>AsString()
public string GetD451BondOverrideIdAsString()
{
    return _D451BondOverrideId.PadRight(8);
}

// Set<>AsString()
public void SetD451BondOverrideIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451BondOverrideId = value;
}

// Standard Getter
public string GetD451AssetUsageOverrideId()
{
    return _D451AssetUsageOverrideId;
}

// Standard Setter
public void SetD451AssetUsageOverrideId(string value)
{
    _D451AssetUsageOverrideId = value;
}

// Get<>AsString()
public string GetD451AssetUsageOverrideIdAsString()
{
    return _D451AssetUsageOverrideId.PadRight(8);
}

// Set<>AsString()
public void SetD451AssetUsageOverrideIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D451AssetUsageOverrideId = value;
}

// Standard Getter
public string GetFiller34()
{
    return _Filler34;
}

// Standard Setter
public void SetFiller34(string value)
{
    _Filler34 = value;
}

// Get<>AsString()
public string GetFiller34AsString()
{
    return _Filler34.PadRight(8);
}

// Set<>AsString()
public void SetFiller34AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler34 = value;
}

// Standard Getter
public string GetFiller35()
{
    return _Filler35;
}

// Standard Setter
public void SetFiller35(string value)
{
    _Filler35 = value;
}

// Get<>AsString()
public string GetFiller35AsString()
{
    return _Filler35.PadRight(8);
}

// Set<>AsString()
public void SetFiller35AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler35 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetYeD45HeaderFiller(string value)
{
    _YeD45HeaderFiller.SetYeD45HeaderFillerAsString(value);
}
// Nested Class: YeD45HeaderFiller
public class YeD45HeaderFiller
{
    private static int _size = 15957;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler36, is_external=, is_static_class=False, static_prefix=
    private string _Filler36 ="";
    
    
    
    
public YeD45HeaderFiller() {}

public YeD45HeaderFiller(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller36(data.Substring(offset, 15957).Trim());
    offset += 15957;
    
}

// Serialization methods
public string GetYeD45HeaderFillerAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler36.PadRight(15957));
    
    return result.ToString();
}

public void SetYeD45HeaderFillerAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 15957 <= data.Length)
    {
        string extracted = data.Substring(offset, 15957).Trim();
        SetFiller36(extracted);
    }
    offset += 15957;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller36()
{
    return _Filler36;
}

// Standard Setter
public void SetFiller36(string value)
{
    _Filler36 = value;
}

// Get<>AsString()
public string GetFiller36AsString()
{
    return _Filler36.PadRight(15957);
}

// Set<>AsString()
public void SetFiller36AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler36 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}