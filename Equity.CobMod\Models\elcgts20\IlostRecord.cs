using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing IlostRecord Data Structure

public class IlostRecord
{
    private static int _size = 186;
    // [DEBUG] Class: IlostRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler3, is_external=, is_static_class=False, static_prefix=
    private string _Filler3 ="";
    
    
    
    
    // [DEBUG] Field: IlostPrintLine, is_external=, is_static_class=False, static_prefix=
    private string _IlostPrintLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetIlostRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler3.PadRight(1));
        result.Append(_IlostPrintLine.PadRight(185));
        
        return result.ToString();
    }
    
    public void SetIlostRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller3(extracted);
        }
        offset += 1;
        if (offset + 185 <= data.Length)
        {
            string extracted = data.Substring(offset, 185).Trim();
            SetIlostPrintLine(extracted);
        }
        offset += 185;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetIlostRecordAsString();
    }
    // Set<>String Override function
    public void SetIlostRecord(string value)
    {
        SetIlostRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller3()
    {
        return _Filler3;
    }
    
    // Standard Setter
    public void SetFiller3(string value)
    {
        _Filler3 = value;
    }
    
    // Get<>AsString()
    public string GetFiller3AsString()
    {
        return _Filler3.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler3 = value;
    }
    
    // Standard Getter
    public string GetIlostPrintLine()
    {
        return _IlostPrintLine;
    }
    
    // Standard Setter
    public void SetIlostPrintLine(string value)
    {
        _IlostPrintLine = value;
    }
    
    // Get<>AsString()
    public string GetIlostPrintLineAsString()
    {
        return _IlostPrintLine.PadRight(185);
    }
    
    // Set<>AsString()
    public void SetIlostPrintLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _IlostPrintLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}