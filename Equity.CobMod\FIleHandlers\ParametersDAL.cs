﻿using System;
using System.Collections;
using System.Text;
using Legacy4.Equity.CobMod.Models;
using WK.UK.CCH.Equity.CobolDataTransformation;

namespace Legacy4.Equity.CobMod.FIleHandlers
{
    public struct FieldLayoutEntry
    {
        public int Offset;
        public int Length;

        public FieldLayoutEntry(int offset, int length)
        {
            Offset = offset;
            Length = length;
        }
    }
    public class ParametersDAL
    {
        // Object references (simulating the COBOL working-storage objects)
        private ParametersDA oParametersDA;
        private string sRecordArea;
        private static readonly Hashtable FieldLayout = new Hashtable
        {
            { "PDA-ProduceUnrealisedSchedule", new FieldLayoutEntry(0, 1) },
            { "PDA-ContentCode", new FieldLayoutEntry(1, 2) },
            { "PDA-UseBookValuesForProfit", new FieldLayoutEntry(3, 1) },
            { "PDA-IndexationCalcRounding", new FieldLayoutEntry(4, 1) },
            { "PDA-CalculationProgramCode", new FieldLayoutEntry(5, 8) },
            { "PDA-ProduceGainLossReport", new FieldLayoutEntry(13, 1) },
            { "PDA-RealisedProgramCode", new FieldLayoutEntry(14, 8) },
            { "PDA-UnrealisedProgramCode", new FieldLayoutEntry(22, 8) },
            { "PDA-CostOfSaleSchedule", new FieldLayoutEntry(30, 1) },
            { "PDA-Losses", new FieldLayoutEntry(31, 1) },
            { "PDA-PeriodEndCalendars", new FieldLayoutEntry(32, 1) },
            { "PDA-HoldingFlagText", new FieldLayoutEntry(33, 18) },
            { "PDA-DeemedDisposalFlag", new FieldLayoutEntry(51, 1) },
            { "PDA-ReportSelectionHolding", new FieldLayoutEntry(52, 1) },
            { "PDA-ReportSelectionProceeds", new FieldLayoutEntry(53, 1) },
            { "PDA-ReportSelectionBookCost", new FieldLayoutEntry(54, 1) },
            { "PDA-ReportSelectionCGTCost", new FieldLayoutEntry(55, 1) },
            { "PDA-ReportSelectionIndxnUsed", new FieldLayoutEntry(56, 1) },
            { "PDA-ReportSelectionCapitalGain", new FieldLayoutEntry(57, 1) },
            { "PDA-ReportSelectionProfit", new FieldLayoutEntry(58, 1) },
            { "PDA-ReportSelectionCGTCostUsed", new FieldLayoutEntry(59, 1) },
            { "PDA-AddNIPILiabilityToProceeds", new FieldLayoutEntry(60, 1) },
            { "PDA-CG01Content", new FieldLayoutEntry(61, 1) },
            { "PDA-CG02Content", new FieldLayoutEntry(62, 1) },
            { "PDA-CG03Content", new FieldLayoutEntry(63, 1) },
            { "PDA-D8YEDeletions", new FieldLayoutEntry(64, 1) },
            { "PDA-ReportOffsetFlag", new FieldLayoutEntry(65, 1) },
            { "PDA-ReportHeaderOffsetLines", new FieldLayoutEntry(66, 2) },
            { "PDA-ReportFooterOffsetLines", new FieldLayoutEntry(68, 2) }
        };
        /// <summary>
        /// Processes a file action (OPEN-INPUT, READ-RECORD, CLOSE-FILE) and converts a COBOL record
        /// to a C# D8Record using our copybook-derived classes.
        /// </summary>
        /// <param name="lFileAction">The file action command (e.g. "OPEN-INPUT", "READ-RECORD", etc.)</param>
        /// <param name="lFileRecordArea">A reference string that will contain the converted record</param>
        public void Run(string lFileAction, ref string lFileRecordArea)
        {
            // Simulate the COBOL EVALUATE statement.
            switch (lFileAction)
            {
                case "OPEN-INPUT":
                    // Instantiate the Data Access Object (equivalent to oParametersDA = ParametersDA::"New"())
                    oParametersDA = new ParametersDA();
                    break;

                case "READ-RECORD":
                    // Simulate reading a record from the file
                    sRecordArea = oParametersDA.ReadNext();

                    // In COBOL: "move sRecordArea to ParametersDARecord"
                    // Here we assume that sRecordArea is parsed into its PDA fields.
                    // For demonstration purposes, we simulate extraction with stub methods.
                    D8Record record = new D8Record();

                    // Map the PDA fields (extracted from sRecordArea) to the D8Record properties.
                    // (These assignment statements simulate the COBOL MOVE operations.)
                    record.D8ProduceUSchedule = ExtractField(sRecordArea, "PDA-ProduceUnrealisedSchedule");
                    record.D8RealSchdContent = ExtractField(sRecordArea, "PDA-ContentCode");
                    record.D8UseBvForProfit = ExtractField(sRecordArea, "PDA-UseBookValuesForProfit");
                    record.D8IndexCalcRounding = ExtractField(sRecordArea, "PDA-IndexationCalcRounding");
                    record.D8CalculationProgram = ExtractField(sRecordArea, "PDA-CalculationProgramCode");
                    record.D8ProduceGainlossReport = ExtractField(sRecordArea, "PDA-ProduceGainLossReport");
                    record.D8RealisedProgram = ExtractField(sRecordArea, "PDA-RealisedProgramCode");
                    record.D8UnrealisedProgram = ExtractField(sRecordArea, "PDA-UnrealisedProgramCode");
                    record.D8ThreadneedleSchedules = ExtractField(sRecordArea, "PDA-CostOfSaleSchedule");
                    record.D8UseLosses = ExtractField(sRecordArea, "PDA-Losses");
                    record.D8UsePeriodEndCalendars = ExtractField(sRecordArea, "PDA-PeriodEndCalendars");

                    // The IV1 (flag) fields:
                    record.D8HoldingFlagText = ExtractField(sRecordArea, "PDA-HoldingFlagText");
                    record.D8Cg01Content = ExtractField(sRecordArea, "PDA-CG01Content");
                    record.D8Cg02Content = ExtractField(sRecordArea, "PDA-CG02Content");
                    record.D8Cg03Content = ExtractField(sRecordArea, "PDA-CG03Content");
                    record.D8YeDeletions = ExtractField(sRecordArea, "PDA-D8YEDeletions");
                    record.D8ReportOffsetFlag = ExtractField(sRecordArea, "PDA-ReportOffsetFlag");
                    record.D8ReportHeaderOffsetLines = int.Parse(ExtractField(sRecordArea, "PDA-ReportHeaderOffsetLines"));
                    record.D8ReportFooterOffsetLines = int.Parse(ExtractField(sRecordArea, "PDA-ReportFooterOffsetLines"));

                    // Additional fields:
                    record.D8DeemedDisposalFlag = ExtractField(sRecordArea, "PDA-DeemedDisposalFlag");
                    // The ReportSelection array (8 occurrences)
                    record.D8RealisedReportSelections[0] = ExtractField(sRecordArea, "PDA-ReportSelectionHolding");
                    record.D8RealisedReportSelections[1] = ExtractField(sRecordArea, "PDA-ReportSelectionProceeds");
                    record.D8RealisedReportSelections[2] = ExtractField(sRecordArea, "PDA-ReportSelectionBookCost");
                    record.D8RealisedReportSelections[3] = ExtractField(sRecordArea, "PDA-ReportSelectionCGTCost");
                    record.D8RealisedReportSelections[4] = ExtractField(sRecordArea, "PDA-ReportSelectionIndxnUsed");
                    record.D8RealisedReportSelections[5] = ExtractField(sRecordArea, "PDA-ReportSelectionCapitalGain");
                    record.D8RealisedReportSelections[6] = ExtractField(sRecordArea, "PDA-ReportSelectionProfit");
                    record.D8RealisedReportSelections[7] = ExtractField(sRecordArea, "PDA-ReportSelectionCGTCostUsed");

                    record.D8AddLiabToNipiProceedsFlag = ExtractField(sRecordArea, "PDA-AddNIPILiabilityToProceeds");

                    // Finally, convert the fully populated record to the file record area.
                    lFileRecordArea = ConvertRecordToString(record);
                    break;

                case "CLOSE-FILE":
                    // In this case, no action is taken (as in the COBOL CLOSE-FILE branch).
                    break;

                default:
                    // Other file actions could be added here.
                    break;
            }
        }

        /// <summary>
        /// Extracts a field from the fixed-length recordArea string based on the field name.
        /// </summary>
        /// <param name="recordArea">The fixed-length record string containing all fields.</param>
        /// <param name="fieldName">The name of the field to extract.</param>
        /// <returns>The extracted field value as a string (trimmed), or an empty string if not found.</returns>
        public static string ExtractField(string recordArea, string fieldName)
        {
            if (!FieldLayout.ContainsKey(fieldName))
            {
                throw new ArgumentException("Field '" + fieldName + "' is not defined in the layout.", "fieldName");
            }

            FieldLayoutEntry layout = (FieldLayoutEntry)FieldLayout[fieldName];

            // Check that the recordArea is long enough.
            if (recordArea.Length < layout.Offset + layout.Length)
            {
                throw new ArgumentException("The record area is shorter than expected for the requested field.", "recordArea");
            }

            // Extract the substring and trim it.
            string extracted = recordArea.Substring(layout.Offset, layout.Length);
            return extracted.Trim();
        }

        /// <summary>
        /// Converts the D8Record instance into a string representation suitable for output.
        /// In a real implementation, this method would format all fields appropriately.
        /// </summary>
        private string ConvertRecordToString(D8Record record)
        {
            // Build the fixed-length record using a StringBuilder.
            StringBuilder sb = new StringBuilder();

            // Each Append corresponds to a MOVE statement from the COBOL code.
            // Field widths based on the COBOL PIC clauses:
            sb.Append(FormatField(record.D8ProduceUSchedule, 1));       // PIC X(001)
            sb.Append(FormatField(record.D8RealSchdContent, 2));          // PIC X(002)
            sb.Append(FormatField(record.D8UseBvForProfit, 1));           // PIC X(001)
            sb.Append(FormatField(record.D8IndexCalcRounding, 1));        // PIC X(001)
            sb.Append(FormatField(record.D8CalculationProgram, 8));       // PIC X(008)
            sb.Append(FormatField(record.D8ProduceGainlossReport, 1));    // PIC X(001)
            sb.Append(FormatField(record.D8RealisedProgram, 8));          // PIC X(008)
            sb.Append(FormatField(record.D8UnrealisedProgram, 8));        // PIC X(008)
            sb.Append(FormatField(record.D8ThreadneedleSchedules, 1));    // PIC X(001)
            sb.Append(FormatField(record.D8UseLosses, 1));                // PIC X(001)
            sb.Append(FormatField(record.D8UsePeriodEndCalendars, 1));    // PIC X(001)
            sb.Append(FormatField(record.D8HoldingFlagText, 18));         // PIC X(018)
            sb.Append(FormatField(record.D8DeemedDisposalFlag, 1));       // PIC X(001)

            // ReportSelection array: 8 occurrences, each PIC X(001)
            for (int i = 0; i < 8; i++)
            {
                string fieldValue = (record.D8RealisedReportSelections != null && record.D8RealisedReportSelections.Length > i)
                    ? record.D8RealisedReportSelections[i]
                    : "";
                sb.Append(FormatField(fieldValue, 1));
            }

            sb.Append(FormatField(record.D8AddLiabToNipiProceedsFlag, 1)); // PIC X(001)
            sb.Append(FormatField(record.D8Cg01Content, 1));               // PIC X(001)
            sb.Append(FormatField(record.D8Cg02Content, 1));               // PIC X(001)
            sb.Append(FormatField(record.D8Cg03Content, 1));               // PIC X(001)
            sb.Append(FormatField(record.D8YeDeletions, 1));               // PIC X(001)
            sb.Append(FormatField(record.D8ReportOffsetFlag, 1));          // PIC X(001)

            // Report header and footer offset lines: PIC 9(002)
            sb.Append(FormatField(record.D8ReportHeaderOffsetLines.ToString(), 2, true));
            sb.Append(FormatField(record.D8ReportFooterOffsetLines.ToString(), 2, true));

            return sb.ToString();
        }

        /// <summary>
        /// Formats a field value to a fixed length.
        /// </summary>
        /// <param name="value">The field value as a string.</param>
        /// <param name="length">The fixed length for the field.</param>
        /// <param name="padLeftWithZero">
        /// If true, pads the value on the left with zeros (useful for numeric fields);
        /// otherwise, pads on the right with spaces.
        /// </param>
        /// <returns>A string of exactly the specified length.</returns>
        private static string FormatField(string value, int length, bool padLeftWithZero = false)
        {
            if (value == null)
                value = string.Empty;

            if (padLeftWithZero)
            {
                // Pad on the left with '0' and ensure the result has exactly the specified length.
                return value.PadLeft(length, '0').Substring(0, length);
            }
            else
            {
                // Pad on the right with spaces.
                return value.PadRight(length).Substring(0, length);
            }
        }
    }
}
