using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// DTO class representing LFileRecordArea Data Structure

public class LFileRecordArea
{
    private static int _size = 485;
    // [DEBUG] Class: LFileRecordArea, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: FixedPortion, is_external=, is_static_class=False, static_prefix=
    private string _FixedPortion ="";
    
    
    
    
    // [DEBUG] Field: FixedPortionR, is_external=, is_static_class=False, static_prefix=
    private FixedPortionR _FixedPortionR = new FixedPortionR();
    
    
    
    
    // [DEBUG] Field: LrTransactionCategory, is_external=, is_static_class=False, static_prefix=
    private string _LrTransactionCategory ="";
    
    
    
    
    // [DEBUG] Field: Filler36, is_external=, is_static_class=False, static_prefix=
    private string _Filler36 ="";
    
    
    
    
    // [DEBUG] Field: Filler37, is_external=, is_static_class=False, static_prefix=
    private string _Filler37 ="";
    
    
    
    
    // [DEBUG] Field: BalanceCosts, is_external=, is_static_class=False, static_prefix=
    private string[] _BalanceCosts = new string[200];
    
    
    
    
    
    // Serialization methods
    public string GetLFileRecordAreaAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_FixedPortion.PadRight(270));
        result.Append(_FixedPortionR.GetFixedPortionRAsString());
        result.Append(_LrTransactionCategory.PadRight(0));
        result.Append(_Filler36.PadRight(0));
        result.Append(_Filler37.PadRight(0));
        for (int i = 0; i < 200; i++)
        {
            result.Append(_BalanceCosts[i].PadRight(0));
        }
        
        return result.ToString();
    }
    
    public void SetLFileRecordAreaAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 270 <= data.Length)
        {
            string extracted = data.Substring(offset, 270).Trim();
            SetFixedPortion(extracted);
        }
        offset += 270;
        if (offset + 215 <= data.Length)
        {
            _FixedPortionR.SetFixedPortionRAsString(data.Substring(offset, 215));
        }
        else
        {
            _FixedPortionR.SetFixedPortionRAsString(data.Substring(offset));
        }
        offset += 215;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetLrTransactionCategory(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller36(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller37(extracted);
        }
        offset += 0;
        for (int i = 0; i < 200; i++)
        {
            if (offset + 0 > data.Length) break;
            string val = data.Substring(offset, 0);
            
            _BalanceCosts[i] = val.Trim();
            offset += 0;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetLFileRecordAreaAsString();
    }
    // Set<>String Override function
    public void SetLFileRecordArea(string value)
    {
        SetLFileRecordAreaAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFixedPortion()
    {
        return _FixedPortion;
    }
    
    // Standard Setter
    public void SetFixedPortion(string value)
    {
        _FixedPortion = value;
    }
    
    // Get<>AsString()
    public string GetFixedPortionAsString()
    {
        return _FixedPortion.PadRight(270);
    }
    
    // Set<>AsString()
    public void SetFixedPortionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _FixedPortion = value;
    }
    
    // Standard Getter
    public FixedPortionR GetFixedPortionR()
    {
        return _FixedPortionR;
    }
    
    // Standard Setter
    public void SetFixedPortionR(FixedPortionR value)
    {
        _FixedPortionR = value;
    }
    
    // Get<>AsString()
    public string GetFixedPortionRAsString()
    {
        return _FixedPortionR != null ? _FixedPortionR.GetFixedPortionRAsString() : "";
    }
    
    // Set<>AsString()
    public void SetFixedPortionRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_FixedPortionR == null)
        {
            _FixedPortionR = new FixedPortionR();
        }
        _FixedPortionR.SetFixedPortionRAsString(value);
    }
    
    // Standard Getter
    public string GetLrTransactionCategory()
    {
        return _LrTransactionCategory;
    }
    
    // Standard Setter
    public void SetLrTransactionCategory(string value)
    {
        _LrTransactionCategory = value;
    }
    
    // Get<>AsString()
    public string GetLrTransactionCategoryAsString()
    {
        return _LrTransactionCategory.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetLrTransactionCategoryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LrTransactionCategory = value;
    }
    
    // Standard Getter
    public string GetFiller36()
    {
        return _Filler36;
    }
    
    // Standard Setter
    public void SetFiller36(string value)
    {
        _Filler36 = value;
    }
    
    // Get<>AsString()
    public string GetFiller36AsString()
    {
        return _Filler36.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller36AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler36 = value;
    }
    
    // Standard Getter
    public string GetFiller37()
    {
        return _Filler37;
    }
    
    // Standard Setter
    public void SetFiller37(string value)
    {
        _Filler37 = value;
    }
    
    // Get<>AsString()
    public string GetFiller37AsString()
    {
        return _Filler37.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller37AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler37 = value;
    }
    
    // Array Accessors for BalanceCosts
    public string GetBalanceCostsAt(int index)
    {
        return _BalanceCosts[index];
    }
    
    public void SetBalanceCostsAt(int index, string value)
    {
        _BalanceCosts[index] = value;
    }
    
    public string GetBalanceCostsAsStringAt(int index)
    {
        return _BalanceCosts[index].PadRight(0);
    }
    
    public void SetBalanceCostsAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _BalanceCosts[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetBalanceCosts()
    {
        return _BalanceCosts != null && _BalanceCosts.Length > 0
        ? _BalanceCosts[0]
        : default(string);
    }
    
    public void SetBalanceCosts(string value)
    {
        if (_BalanceCosts == null || _BalanceCosts.Length == 0)
        _BalanceCosts = new string[1];
        _BalanceCosts[0] = value;
    }
    
    public string GetBalanceCostsAsString()
    {
        return _BalanceCosts != null && _BalanceCosts.Length > 0
        ? _BalanceCosts[0].ToString()
        : string.Empty;
    }
    
    public void SetBalanceCostsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_BalanceCosts == null || _BalanceCosts.Length == 0)
        _BalanceCosts = new string[1];
        
        _BalanceCosts[0] = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFixedPortionR(string value)
    {
        _FixedPortionR.SetFixedPortionRAsString(value);
    }
    // Nested Class: FixedPortionR
    public class FixedPortionR
    {
        private static int _size = 215;
        
        // Fields in the class
        
        
        // [DEBUG] Field: LFund, is_external=, is_static_class=False, static_prefix=
        private string _LFund ="";
        
        
        
        
        // [DEBUG] Field: LSedol, is_external=, is_static_class=False, static_prefix=
        private string _LSedol ="";
        
        
        
        
        // [DEBUG] Field: Filler34, is_external=, is_static_class=False, static_prefix=
        private string _Filler34 ="";
        
        
        
        
        // [DEBUG] Field: FundType, is_external=, is_static_class=False, static_prefix=
        private string _FundType ="";
        
        
        
        
        // [DEBUG] Field: Filler35, is_external=, is_static_class=False, static_prefix=
        private string _Filler35 ="";
        
        
        
        
    public FixedPortionR() {}
    
    public FixedPortionR(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetLFund(data.Substring(offset, 0).Trim());
        offset += 0;
        SetLSedol(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller34(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFundType(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller35(data.Substring(offset, 215).Trim());
        offset += 215;
        
    }
    
    // Serialization methods
    public string GetFixedPortionRAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LFund.PadRight(0));
        result.Append(_LSedol.PadRight(0));
        result.Append(_Filler34.PadRight(0));
        result.Append(_FundType.PadRight(0));
        result.Append(_Filler35.PadRight(215));
        
        return result.ToString();
    }
    
    public void SetFixedPortionRAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetLFund(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetLSedol(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller34(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFundType(extracted);
        }
        offset += 0;
        if (offset + 215 <= data.Length)
        {
            string extracted = data.Substring(offset, 215).Trim();
            SetFiller35(extracted);
        }
        offset += 215;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetLFund()
    {
        return _LFund;
    }
    
    // Standard Setter
    public void SetLFund(string value)
    {
        _LFund = value;
    }
    
    // Get<>AsString()
    public string GetLFundAsString()
    {
        return _LFund.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetLFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LFund = value;
    }
    
    // Standard Getter
    public string GetLSedol()
    {
        return _LSedol;
    }
    
    // Standard Setter
    public void SetLSedol(string value)
    {
        _LSedol = value;
    }
    
    // Get<>AsString()
    public string GetLSedolAsString()
    {
        return _LSedol.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetLSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LSedol = value;
    }
    
    // Standard Getter
    public string GetFiller34()
    {
        return _Filler34;
    }
    
    // Standard Setter
    public void SetFiller34(string value)
    {
        _Filler34 = value;
    }
    
    // Get<>AsString()
    public string GetFiller34AsString()
    {
        return _Filler34.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller34AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler34 = value;
    }
    
    // Standard Getter
    public string GetFundType()
    {
        return _FundType;
    }
    
    // Standard Setter
    public void SetFundType(string value)
    {
        _FundType = value;
    }
    
    // Get<>AsString()
    public string GetFundTypeAsString()
    {
        return _FundType.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFundTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _FundType = value;
    }
    
    // Standard Getter
    public string GetFiller35()
    {
        return _Filler35;
    }
    
    // Standard Setter
    public void SetFiller35(string value)
    {
        _Filler35 = value;
    }
    
    // Get<>AsString()
    public string GetFiller35AsString()
    {
        return _Filler35.PadRight(215);
    }
    
    // Set<>AsString()
    public void SetFiller35AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler35 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}