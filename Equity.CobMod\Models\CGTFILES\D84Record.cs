using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D84Record Data Structure

public class D84Record
{
    private static int _size = 151;
    // [DEBUG] Class: D84Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D84PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D84PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D84ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D84ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD84RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D84PrintControl.PadRight(1));
        result.Append(_D84ReportLine.PadRight(150));
        
        return result.ToString();
    }
    
    public void SetD84RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD84PrintControl(extracted);
        }
        offset += 1;
        if (offset + 150 <= data.Length)
        {
            string extracted = data.Substring(offset, 150).Trim();
            SetD84ReportLine(extracted);
        }
        offset += 150;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD84RecordAsString();
    }
    // Set<>String Override function
    public void SetD84Record(string value)
    {
        SetD84RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD84PrintControl()
    {
        return _D84PrintControl;
    }
    
    // Standard Setter
    public void SetD84PrintControl(string value)
    {
        _D84PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD84PrintControlAsString()
    {
        return _D84PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD84PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D84PrintControl = value;
    }
    
    // Standard Getter
    public string GetD84ReportLine()
    {
        return _D84ReportLine;
    }
    
    // Standard Setter
    public void SetD84ReportLine(string value)
    {
        _D84ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD84ReportLineAsString()
    {
        return _D84ReportLine.PadRight(150);
    }
    
    // Set<>AsString()
    public void SetD84ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D84ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}