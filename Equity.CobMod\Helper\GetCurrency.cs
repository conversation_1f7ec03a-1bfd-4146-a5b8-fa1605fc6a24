﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using EquityProject.GetCurrencyDTO;

namespace EquityProject.GetCurrencyPGM
{
    /// <summary>
    /// Provides currency determination logic, mimicking the COBOL GetCurrency program.
    /// This class maintains internal state for change detection across calls.
    /// </summary>
    public class GetCurrency
    {
        // Corresponds to COBOL's LastGetCurrencyPrintString (PIC X(10) value spaces).
        // This is internal state that persists across calls to *this instance* of the service.
        private string _lastGetCurrencyPrintString;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetCurrency"/> class.
        /// Sets the initial state for change detection.
        /// </summary>
        public GetCurrency()
        {
            // COBOL: LastGetCurrencyPrintString pic x(10) value spaces.
            // This means on the very first call to the COBOL program, LastGetCurrencyPrintString is all spaces.
            _lastGetCurrencyPrintString = new string(' ', 10);
        }

        /// <summary>
        /// Processes the fund type to determine the currency print string and its change status.
        /// Mimics the PROCEDURE DIVISION of the COBOL GetCurrency program.
        /// </summary>
        /// <param name="parameters">
        /// An instance of <see cref="GetCurrencyLinkage"/> containing the input fund type
        /// and receiving the output print string and status.
        /// </param>
        public void ProcessCurrency(GetCurrencyLinkage parameters)
        {
            // COBOL: move GetCurrencyPrintString to LastGetCurrencyPrintString
            // This means the *previous* output string (which is currently in parameters.PrintString
            // from the DTO's constructor or a prior call's output) becomes the *new* last string.
            // On the very first call, parameters.PrintString will be "          " (10 spaces).
            _lastGetCurrencyPrintString = parameters.PrintString;

            string newPrintString; // Temporary variable to hold the string before final assignment

            // COBOL: if Euro-Fund ... else ... end-if
            if (parameters.InterpretedFundType == FundType.Euro)
            {
                // COBOL: move '§Euro' to GetCurrencyPrintString
                newPrintString = "§Euro";
            }
            else
            {
                // COBOL: move '§Pound' to GetCurrencyPrintString
                newPrintString = "§Pound";
            }

            // Ensure the newPrintString matches COBOL's PIC X(10) fixed length.
            // If the string is longer, truncate it. If shorter, pad it with spaces.
            if (newPrintString.Length > 10)
            {
                newPrintString = newPrintString.Substring(0, 10);
            }
            else if (newPrintString.Length < 10)
            {
                newPrintString = newPrintString.PadRight(10, ' ');
            }

            // Assign the determined and formatted string to the DTO's output property
            parameters.PrintString = newPrintString;

            // COBOL: if GetCurrencyPrintString <> LastGetCurrencyPrintString
            if (parameters.PrintString != _lastGetCurrencyPrintString)
            {
                // COBOL: set Currency-Has-Changed to true
                parameters.Status = CurrencyChangeStatus.Changed;
            }
            else
            {
                // COBOL: set Currency-Has-Not-Changed to true
                parameters.Status = CurrencyChangeStatus.NotChanged;
            }

            // COBOL: goback. (Method simply returns)
        }
    }
}
