using System;
using System.ComponentModel;
using System.Text;
using EquityProject.Cgtscot2DTO;
using EquityProject.EqtlogPGM;
using EquityProject.EqtpathPGM;
using EquityProject.EquityGlobalParmsDTO;
namespace EquityProject.Cgtscot2PGM
{
    // Simple PackedDecimalConverter utility class
    public static class PackedDecimalConverter
    {
        public static decimal ToDecimal(byte[] bytes)
        {
            // Simple implementation - in real scenario this would handle packed decimal conversion
            return 0m;
        }

        public static decimal ToDecimal(string value)
        {
            // Simple implementation for string input - parse as decimal
            if (decimal.TryParse(value, out decimal result))
                return result;
            return 0m;
        }

        public static byte[] FromDecimal(decimal value)
        {
            // Simple implementation - in real scenario this would handle packed decimal conversion
            return new byte[8];
        }
    }
    // Cgtscot2 Class Definition

    //Cgtscot2 Class Constructor
    public class Cgtscot2
    {
        // Declare Cgtscot2 Class private variables
        private Fvar _fvar = new Fvar();
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();
        private EquityGlobalParms equityGlobalParms = new EquityGlobalParms();

        // Declare {program_name} Class getters setters
        public Fvar GetFvar() { return _fvar; }
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        /// <summary>
        /// Helper method to call external subroutines
        /// </summary>
        /// <param name="ivar">The Ivar parameter to pass to the subroutine</param>
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
        }


        // Run() method
        public void Run(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Call the main entry point
            Mainline(fvar, gvar, ivar);
        }

        // Methods representing paragraphs under procedure division
        /// <summary>
        /// Mainline method to perform the main logic of the program.
        /// Converted from the "mainline" COBOL paragraph.
        /// </summary>
        /// <remarks>
        /// This method performs the main logic of the program, including
        /// initializing, reading and accumulating data, and ending the process.
        /// <para>
        /// The equivalent COBOL code is:
        /// PERFORM A-INITIALISE
        /// PERFORM B-READ-ACCUMULATE UNTIL NOT SUCCESSFUL OR QUIT-PROCESS
        /// PERFORM C-END
        /// EXIT PROGRAM.
        /// STOP RUN.
        /// </para>
        ///
        public void Mainline(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Initialize the process
            AInitialise(fvar, gvar, ivar);

            // Read and accumulate data until the process is not successful or the quit process flag is set
            while (gvar.GetCgtfilesLinkage().GetLFileReturnCode() == "00" && !gvar.IsQuitProcess())
            {
                // Perform the read and accumulate logic
                BReadAccumulate(fvar, gvar, ivar);
            }

            // End the process
            CEnd(fvar, gvar, ivar);

            // The original COBOL code includes EXIT PROGRAM and STOP RUN statements,
            // which are implicit in the C# method structure.
        }
        /// <summary>
        /// Corresponds to COBOL paragraph: aInitialise
        /// </summary>
        /// <param name="F">Global Fixed Data accessor</param>
        /// <param name="G">Global Working-storage Data accessor</param>
        /// <param name="I">Global Input Data accessor</param>
        /// <remarks>
        /// Implementation of the COBOL paragraph "aInitialise" translated to C#.
        /// </remarks>
        public void AInitialise(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            var CGTSCOT2_USER_NO = ivar.GetCgtscot2Linkage().GetCgtscot2UserNo();
            gvar.GetCommonLinkage().SetLUserNo(CGTSCOT2_USER_NO);
            gvar.GetReportFile().SetReportUserNo(CGTSCOT2_USER_NO.ToString());

            var CGTSCOT2_REPORT_NUMBER = ivar.GetCgtscot2Linkage().GetCgtscot2ReportNumber();
            gvar.GetCgtfilesLinkage().SetLReportNo(CGTSCOT2_REPORT_NUMBER.ToString());
            gvar.GetReportFile().SetReportGenNo(CGTSCOT2_REPORT_NUMBER.ToString());
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.UNREALISED_DATA_FILE);

            XCallCgtfiles(fvar, gvar, ivar);

            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_NEXT);
            gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);
            gvar.GetEqtpathLinkage().SetEqtpathFileNameAsString("REPORT");

            XCallEqtpath(fvar, gvar, ivar);

            gvar.SetReportFileName(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

            // Set unrealised TSB export file name and open it
            // Note: These methods need to be implemented or use appropriate file handling

            gvar.SetCgtlogLinkageArea1(new CgtlogLinkageArea1());
            gvar.GetCgtlogLinkageArea1().SetLLogProgram(gvar.GetProgramName());
            gvar.SetCgtlogLinkageArea1(new CgtlogLinkageArea1());
            gvar.GetCgtlogLinkageArea1().SetLLogAction(Gvar.OPEN_OUTPUT);

            gvar.SetCgtlogLinkageArea1(new CgtlogLinkageArea1());
            gvar.GetCgtlogLinkageArea1().SetLLogFileName(" ");
            X4Cgtlog(fvar, gvar, ivar);

            gvar.SetWsMessageNo(0);
            gvar.SetCgtlogLinkageArea2(new CgtlogLinkageArea2());
            gvar.GetCgtlogLinkageArea2().SetLLogMessageType("I");
            gvar.SetCgtlogLinkageArea2(new CgtlogLinkageArea2());
            gvar.GetCgtlogLinkageArea2().SetLLogMessage(Gvar.WRITE_RECORD);
            gvar.SetTimeStamp(new TimeStamp());
            gvar.SetDateStamp(new DateStamp());

            var WSDD = gvar.GetDateStamp().GetWsDd();
            var WSMM = gvar.GetDateStamp().GetWsMm();
            var WSYY = gvar.GetDateStamp().GetWsYy();
            var WSHH = gvar.GetTimeStamp().GetWsHh();
            var WSNN = gvar.GetTimeStamp().GetWsNn();
            gvar.SetWsMessages(new WsMessages());
            gvar.GetWsMessages().GetWsMessage2().SetWsMessDd(WSDD);
            gvar.GetWsMessages().GetWsMessage2().SetWsMessMm(WSMM);
            gvar.GetWsMessages().GetWsMessage2().SetWsMessYy(WSYY);
            gvar.GetWsMessages().GetWsMessage2().SetWsMessHh(WSHH);
            gvar.GetWsMessages().GetWsMessage2().SetWsMessNn(WSNN);
            gvar.SetWsWhenCompiled(new WsWhenCompiled());
            var WS_WHEN_COMP_CHECK = gvar.GetWsWhenCompiled().GetWsCompTime();

            gvar.SetWsWhenCompiled(new WsWhenCompiled());
            var WS_COMP_DATE = gvar.GetWsWhenCompiled().GetWsCompDate();
            var WS_COMP_TIME = gvar.GetWsWhenCompiled().GetWsCompTime();

            var INFO_PLUS_FACTOR = gvar.GetProgramName() + " Version " + gvar.GetVersionNumber() + " " + WS_COMP_DATE
              + " " + WS_COMP_TIME + "; ";
            var WS_MESSAGE_1 = gvar.GetWsMessages().GetWsMessage1() + ":" + INFO_PLUS_FACTOR;

            // Set the message
            gvar.GetWsMessages().SetWsMessage1(WS_MESSAGE_1);

            var ELCGMIO_LINKAGE_X_2 = Gvar.NEW_DERIVATIVE_EXPORT_FORMAT;

            gvar.SetElcgmioLinkage2(new ElcgmioLinkage2());
            gvar.GetElcgmioLinkage2().SetElcgmioLinkage2(ELCGMIO_LINKAGE_X_2);
            XCallMfHandlerForConfig(fvar, gvar, ivar);

            // COMPRESSION NOTE: Passing ELCGMIO_LINKAGE_X_2 to W_NEEDED shows connection to imported value
            // original would be a redundant assignment = above to below ...
            gvar.SetWNewDerivativeExportFormat(gvar.GetWConfigItem());
        }
        /// <summary>
        /// BREADACCU BMSAN DSRP 0119 Accept data from historical record type '1' or
        /// add to chain an existing U record type
        /// COBOL paragraph name: BReadAccumulate
        /// </summary>
        /// <param name="fvar">Fvar parameter for accessing COBOL working-storage variables.</param>
        /// <param name="gvar">Gvar parameter for accessing COBOL working-storage variables as described in the table.</param>
        /// <param name="ivar">Unused parameter.</param>
        /// <remarks>
        /// This method converts the COBOL paragraph BREADACCU to C#. It processes data from
        /// historical record types and adds to the chain an existing U record type. This method
        /// handles the evaluation of data types and processes specific data based on conditions.
        /// </remarks>
        public void BReadAccumulate(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Call the method corresponding to the COBOL paragraph X-CALL-CGTFILES
            XCallCgtfiles(fvar, gvar, ivar);

            // If the call to X-CALL-CGTFILES was successful
            if (gvar.GetCgtfilesLinkage().GetLFileReturnCode() == "00")
            {
                // Move the file record area to the gains data record
                // Note: This needs proper conversion from LFileRecordArea to UGainsDataRecord
                // gvar.SetUGainsDataRecord(gvar.GetLFileRecordArea());

                // Get the record type and check its value
                var recordType = gvar.GetUGainsDataRecord().GetURecordType();

                switch (recordType)
                {
                    case "1":
                        // Evaluate TRUE condition (which always evaluates to true)
                        if (true)
                        {
                            // If D-TSB-UR-FUND-CODE or WS-SEDOL-NUMBER is spaces
                            if (fvar.GetDTsbRecord().GetFiller1().GetDTsbUrFundCode() == " " || gvar.GetWsSedolNumber() == " ")
                            {
                                // Perform the next fund Sedol
                                B1NextFundSedol(fvar, gvar, ivar);
                            }
                            // If D-TSB-UR-FUND-CODE is not equal to U-FUND-CODE or WS-SEDOL-NUMBER is not equal to U-SEDOL-NUMBER
                            else if (fvar.GetDTsbRecord().GetFiller1().GetDTsbUrFundCode() != gvar.GetUGainsDataRecord().GetUFundCode() ||
                                     gvar.GetWsSedolNumber() != gvar.GetUGainsDataRecord().GetUHeaderRecordLayout().GetUSedolNumber())
                            {
                                // Perform the B2 WRITE RECORD
                                B2WriteRecord(fvar, gvar, ivar);
                                // Perform the next fund Sedol
                                B1NextFundSedol(fvar, gvar, ivar);
                            }
                            // For any other case, continue
                            else
                            {
                                // Continue processing
                            }
                        }
                        break;

                    case "2":
                        // If U-QUANTITY is numeric and negative
                        if (gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUQuantity() is decimal quantity &&
                            quantity < 0)
                        {
                            // Add U-QUANTITY to WS-QUANTITY
                            decimal currentQuantity = gvar.GetWsQuantity();
                            gvar.SetWsQuantity(currentQuantity + quantity);
                        }

                        // If CONFIG-NEW-DERIVATIVE-EXPORT-FORMAT is true
                        if (gvar.IsConfigNewDerivativeExportFormat())
                        {
                            // If U-SHORT-WRITTEN-DERIVATIVE is true
                            if (gvar.GetUGainsDataRecord().GetUSecurityType() == "SHORT_WRITTEN_DERIVATIVE")
                            {
                                // Move '+' to D-TSB-UR-HOLDING-SIGN
                                fvar.GetDTsbRecord().GetFiller1().SetDTsbUrHoldingSign("+");
                            }
                            else
                            {
                                // Move '-' to D-TSB-UR-HOLDING-SIGN
                                fvar.GetDTsbRecord().GetFiller1().SetDTsbUrHoldingSign("-");
                            }
                        }

                        // If U-PROCEEDS-X is not spaces
                        if (gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUProceedsX() != " ")
                        {
                            // Add U-PROCEEDS to WS-PROCEEDS
                            decimal currentProceeds = gvar.GetWsProceeds();
                            decimal proceedsToAdd = gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUProceeds();
                            gvar.SetWsProceeds(currentProceeds + proceedsToAdd);
                        }

                        // If U-PROCEEDS-X and U-GAINLOSS-X are not spaces
                        if (gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUProceedsX() != " " &&
                            gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUGainlossX() != " ")
                        {
                            // Add U-GAINLOSS to WS-GAIN-LOSS
                            decimal currentGainLoss = gvar.GetWsGainLoss();
                            decimal gainLossToAdd = gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUGainloss();
                            gvar.SetWsGainLoss(currentGainLoss + gainLossToAdd);
                        }

                        // If ADJUSTMENT-DESCRIPTION is true
                        if (gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUMovementDescription() == "ADJUSTMENT")
                        {
                            // Set PROCESSING-ADJUSTMENT to true
                            gvar.SetWsProcessingAdjustmentFlag(1);
                        }
                        else
                        {
                            // If PROCESSING-ADJUSTMENT is true and U-MOVEMENT-DESCRIPTION is not spaces
                            if (gvar.IsProcessingAdjustment() &&
                                gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUMovementDescription() != " ")
                            {
                                // Set NOT-PROCESSING-ADJUSTMENT to true
                                gvar.SetWsProcessingAdjustmentFlag(0);
                            }
                        }

                        // If NOT-PROCESSING-ADJUSTMENT is true
                        if (gvar.IsNotProcessingAdjustment())
                        {
                            // If U-INDEXED-COST-X is not spaces
                            if (gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUIndexedCostX() != " ")
                            {
                                var indexedCost = gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUIndexedCost();
                                // If U-INDEXED-COST is less than zero and REAL-POSITIVE-COST is true
                                if (indexedCost < 0)
                                {
                                    // Subtract U-INDEXED-COST from WS-UR-INDEXED-COST
                                    decimal currentIndexedCost = gvar.GetWsUrIndexedCost();
                                    gvar.SetWsUrIndexedCost(currentIndexedCost - indexedCost);
                                }
                                // If U-INDEXED-COST is greater than zero and REAL-NEGATIVE-COST is true
                                else if (indexedCost > 0)
                                {
                                    // Subtract U-INDEXED-COST from WS-UR-INDEXED-COST
                                    decimal currentIndexedCost = gvar.GetWsUrIndexedCost();
                                    gvar.SetWsUrIndexedCost(currentIndexedCost - indexedCost);
                                }
                            }

                            // If U-COST-X is not spaces
                            if (gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUCostX() != " ")
                            {
                                // If BF-GAIN-INDEXATION is true and U-MOVEMENT-DESCRIPTION is 'B/F GAIN' or 'B/F INDXN'
                                if (gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUMovementDescription() == "B/F GAIN" ||
                                     gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUMovementDescription() == "B/F INDXN")
                                {
                                    // Subtract U-COST from WS-BASECOST
                                    decimal currentBaseCost = gvar.GetWsBasecost();
                                    decimal costToSubtract = gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUCost();
                                    gvar.SetWsBasecost(currentBaseCost - costToSubtract);
                                }
                                // If U-COST is less than zero
                                else if (gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUCost() < 0)
                                {
                                    // Subtract U-COST from WS-BASECOST
                                    decimal currentBaseCost = gvar.GetWsBasecost();
                                    decimal costToSubtract = gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUCost();
                                    gvar.SetWsBasecost(currentBaseCost - costToSubtract);
                                }
                                // If U-COST is greater than zero
                                else if (gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUCost() > 0)
                                {
                                    // Subtract U-COST from WS-BASECOST
                                    decimal currentBaseCost = gvar.GetWsBasecost();
                                    decimal costToSubtract = gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUCost();
                                    gvar.SetWsBasecost(currentBaseCost - costToSubtract);
                                }

                                // If U-INDEXED-COST-X is spaces and U-MOVEMENT-DESCRIPTION is 'DISPOSAL'
                                if (gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUIndexedCostX() == " " &&
                                    gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUMovementDescription() == "DISPOSAL")
                                {
                                    // If U-COST is less than zero
                                    if (gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUCost() < 0)
                                    {
                                        // Subtract U-COST from WS-BASECOST
                                        decimal currentBaseCost = gvar.GetWsBasecost();
                                        decimal costToSubtract = gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUCost();
                                        gvar.SetWsBasecost(currentBaseCost - costToSubtract);
                                    }
                                }
                            }
                        }
                        break;

                    default:
                        // For any other record type, continue
                        break;
                }
            }
        }
        /// <summary>
        /// COBOL paragraph name: b1NextFundSedol
        /// </summary>
        /// <param name="fvar">Fvar object containing the partially translated fields</param>
        /// <param name="gvar">Gvar object containing the input data</param>
        /// <param name="ivar">Not used in this paragraph</param>
        /// <remarks>
        /// This method is the implementation of the COBOL paragraph B1NEXTFUNDSEDOL
        ///
        /// The logic flow:
        ///
        /// IF U-FUND-CODE NOT EQUAL D-TSB-UR-FUND-CODE
        ///     MOVE U-FUND-CODE TO WS-MESS-11-FUND
        ///     MOVE WS-MESSAGE-11 TO L-LOG-MESSAGE
        ///     MOVE U-FUND-CODE TO D-TSB-UR-FUND-CODE
        /// END-IF
        /// MOVE U-SEDOL-NUMBER TO WS-SEDOL-NUMBER
        /// MOVE U-ISSUERS-NAME TO D-TSB-UR-STOCK-ISSUER
        /// MOVE U-STOCK-DESCRIPTION TO D-TSB-UR-STOCK-DESCRIPTION
        /// MOVE U-SECURITY-TYPE TO D-TSB-UR-CATEGORY
        /// MOVE U-MAIN-GROUP (2 : 2) TO D-TSB-UR-STOCK-TYPE
        /// </remarks>
        public void B1NextFundSedol(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Declare variables for readability and to mimic COBOL level-naming
            string uFundCode = gvar.GetUGainsDataRecord().GetUFundCode();
            string dTsbUrFundCode = fvar.GetDTsbRecord().GetFiller1().GetDTsbUrFundCode();

            // DETERMINE IF U-FUND-CODE NOT EQUAL D-TSB-UR-FUND-CODE (COBOL Base Logic)
            if (!uFundCode.Equals(dTsbUrFundCode))
            {
                // MOVE U-FUND-CODE TO WS-MESS-11-FUND
                gvar.GetWsMessages().GetWsMessage11().SetWsMess11Fund(uFundCode);

                // MOVE WS-MESSAGE-11 TO L-LOG-MESSAGE
                string wsMessage11 = gvar.GetWsMessages().GetWsMessage11AsString();
                gvar.GetCgtlogLinkageArea2().SetLLogMessage(wsMessage11);

                // MOVE U-FUND-CODE TO D-TSB-UR-FUND-CODE
                fvar.GetDTsbRecord().GetFiller1().SetDTsbUrFundCode(uFundCode);
            }

            // MOVE U-SEDOL-NUMBER TO WS-SEDOL-NUMBER
            string uSedolNumber = gvar.GetUGainsDataRecord().GetUHeaderRecordLayout().GetUSedolNumber();
            gvar.SetWsSedolNumber(uSedolNumber);

            // MOVE U-ISSUERS-NAME TO D-TSB-UR-STOCK-ISSUER
            string uIssuersName = gvar.GetUGainsDataRecord().GetUHeaderRecordLayout().GetUIssuersNameAsString();   // Gettersegin?
            fvar.GetDTsbRecord().GetFiller1().GetDTsbUrStockName().SetDTsbUrStockIssuer(uIssuersName);

            // MOVE U-STOCK-DESCRIPTION TO D-TSB-UR-STOCK-DESCRIPTION
            string uStockDescription = gvar.GetUGainsDataRecord().GetUHeaderRecordLayout().GetUStockDescriptionAsString();
            fvar.GetDTsbRecord().GetFiller1().GetDTsbUrStockName().SetDTsbUrStockDescription(uStockDescription);

            // MOVE U-SECURITY-TYPE TO D-TSB-UR-CATEGORY
            string uSecurityType = gvar.GetUGainsDataRecord().GetUSecurityType();
            fvar.GetDTsbRecord().GetFiller1().SetDTsbUrCategory(uSecurityType);

            // SUBSTR U-MAIN-GROUP (2 : 2) TO D-TSB-UR-STOCK-TYPE
            string uMainGroup = gvar.GetUGainsDataRecord().GetUMainGroup();
            string uMainGroupSubstring = uMainGroup.Substring(2, 2); //Assuming Substring starts from 0
            fvar.GetDTsbRecord().GetFiller1().SetDTsbUrStockType(uMainGroupSubstring);
        }
        /// <summary>
        ///  B2-Write-Record paragraph.
        /// </summary>
        /// <param name="fvar">Fvar class object containing variables from the F class.</param>
        /// <param name="gvar">Gvar class object containing variables from the G class.</param>
        /// <param name="ivar">Ivar class object containing variables from the I class.</param>
        /// <remarks>
        /// This method simulates the B2-Write-Record paragraph from the original COBOL code.
        /// It evaluates the WS-GAIN-LOSS value and sets the D-TSB-UR-CAPITAL-GAIN-LOSS-SG
        /// accordingly. Then, it moves values from WS variables to D-TSB-UR variables,
        /// writes the D-TSB-RECORD, and resets D-TSB-UR variables.
        ///
        /// 93. B2-Write-Record.                    B2-WRITE-RECORD.
        ///     EVALUATE TRUE                            EVALUATE TRUE
        ///     WHEN WS-GAIN-LOSS > 0
        ///        MOVE SPACE TO D-TSB-UR-CAPITAL-GAIN-LOSS-SG   SET DTsb-UR-CAPITAL-GAIN-LOSS-SG TO GET SPACE
        ///     WHEN WS-GAIN-LOSS < 0
        ///        MOVE '-' TO D-TSB-UR-CAPITAL-GAIN-LOSS-SG    SET DTsb-UR-CAPITAL-GAIN-LOSS-SG TO -
        ///     END-EVALUATE                                END-EVALUATE
        ///     MOVE WS-PROCEEDS TO D-TSB-UR-PROCEEDS             SET DTsb-UR-PROCEEDS TO GET DP PWT achievfemen  SET WS PWT TO   SET DP PWT TO
        ///     MOVE WS-BASECOST TO D-TSB-UR-BASE-COST             SET DTsb-UR-BASE-COST to get WS_BASECOST
        ///     MOVE WS-QUANTITY TO D-TSB-UR-HOLDING-UNITS       SET DTsb-UR-HOLDING-UNITS TO GET   WS-QUANTITY
        ///     MOVE WS-UR-INDEXED-COST TO D-TSB-UR-INDEXED-COST  SET DTsb-UR-INDEXED-COST TO GET   WS-UR-INDEXED -GET
        ///     MOVE WS-GAIN-LOSS TO D-TSB-UR-CAPITAL-GAIN-LOSS  SET DTsb-UR GAN-Loss TO GET WS
        ///     WRITE D-TSB-RECORD                              WRITE D-TSB-RECORD
        ///     MOVE SPACES TO D-TSB-UR-STOCK-NAME
        ///                     D-TSB-UR-STOCK-TYPE
        ///                     D-TSB-UR-CAPITAL-GAIN-LOSS-SG
        ///                     D-TSB-UR-HOLDING-SIGN                 Exit.
        ///     MOVE ZEROES TO D-TSB-UR-HOLDING-UNITS
        ///                     D-TSB-UR-BASE-COST
        ///                     D-TSB-UR-INDEXED-COST
        ///                     D-TSB-UR-PROCEEDS
        ///                     D-TSB-UR-CAPITAL-GAIN-LOSS
        ///                     WS-GAIN-LOSS
        ///                     WS-BASECOST
        ///                     WS-PROCEEDS
        ///                     WS-QUANTITY
        ///                     WS-UR-INDEXED-COST
        ///     .
        /// </remarks>
        public void B2WriteRecord(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            var wsGainLoss = gvar.GetWsGainLoss();
            var space = " ";
            var amidHyphen = "-";

            if (wsGainLoss > 0)
            {
                // WHEN WS-GAIN-LOSS > 0
                // MOVE SPACE TO D-TSB-UR-CAPITAL-GAIN-LOSS-SG
                fvar.GetDTsbRecord().GetFiller1().SetDTsbUrCapitalGainLossSg(space);
            }
            else if (wsGainLoss < 0)
            {
                // WHEN WS-GAIN-LOSS < 0
                // MOVE '-' TO D-TSB-UR-CAPITAL-GAIN-LOSS-SG
                fvar.GetDTsbRecord().GetFiller1().SetDTsbUrCapitalGainLossSg(amidHyphen);
            }

            // END-EVALUATE
            // MOVE WS-PROCEEDS TO D-TSB-UR-PROCEEDS
            var wsProceeds = gvar.GetWsProceeds();
            fvar.GetDTsbRecord().GetFiller1().SetDTsbUrProceeds(wsProceeds);

            // MOVE WS-BASECOST TO D-TSB-UR-BASE-COST
            var wsBasecost = gvar.GetWsBasecost();
            fvar.GetDTsbRecord().GetFiller1().SetDTsbUrBaseCost(wsBasecost);

            // MOVE WS-QUANTITY TO D-TSB-UR-HOLDING-UNITS
            var wsQuantity = gvar.GetWsQuantity();
            fvar.GetDTsbRecord().GetFiller1().SetDTsbUrHoldingUnits(wsQuantity);

            // MOVE WS-UR-INDEXED-COST TO D-TSB-UR-INDEXED-COST
            var wsUrIndexedCost = gvar.GetWsUrIndexedCost();
            fvar.GetDTsbRecord().GetFiller1().SetDTsbUrIndexedCost(wsUrIndexedCost);

            // MOVE WS-GAIN-LOSS TO D-TSB-UR-CAPITAL-GAIN-LOSS
            fvar.GetDTsbRecord().GetFiller1().SetDTsbUrCapitalGainLoss(wsGainLoss);

            // WRITE D-TSB-RECORD
            // The logic for writing the D-TSB-RECORD is not specified in the original COBOL code,

            // The statement SET ABSOLUTE D-TSB-RECORD with the expression customerRecord and moves the below comma separated values WS-HOUSE-PROC DATE WS-G-FEEX-CODE WS-WITTYP WS-WIT-NUME STWH  outside this )  paragraph as below
            //   MOVE WS-HOUSE-PROC         TO                   WS-ORIG-CODE
            //   MOVE WS-G-FEEX-CODE        TO                   WS-ORIG-G-CODE
            //   MOVE WS-HOUSE-PROC         TO                   WS-ORIGCSRS
            //   MOVE WS-G-FGED-3           TO                   WS-ORIG-CSRS-DSC
            // The Write TS  records steps will take place in  toBcdIn-cyg also the below calls would be moved to TOBCDIN-CYG as step 2
            // This Fact is ignored

            // SET   WS-HOUSE-PROC         TO WS-ST-PROC               // to be written in a seperate paragraph to b2WriteRecord
            //// Substitute WIGR for PLanto calculate the new 03  allowed to use f2WS<<SS GET DFE

            // MOVE WSA                                                      WORKER/INDREE             //                          // To be done in another stanza
            // CALL "F2-DLF-REST-PREP-ENTRY-CDS-RT"              SUCCESS /
            // PAR    ON-TNL0UB980.2-D
            // CALL

            // MOVE SPACES TO various fields
            fvar.GetDTsbRecord().GetFiller1().GetDTsbUrStockName().SetDTsbUrStockIssuer("");
            fvar.GetDTsbRecord().GetFiller1().GetDTsbUrStockName().SetDTsbUrStockDescription("");
            fvar.GetDTsbRecord().GetFiller1().SetDTsbUrStockType("");
            fvar.GetDTsbRecord().GetFiller1().SetDTsbUrCapitalGainLossSg("");
            fvar.GetDTsbRecord().GetFiller1().SetDTsbUrHoldingSign("");

            // MOVE ZEROES to following variables
            fvar.GetDTsbRecord().GetFiller1().SetDTsbUrHoldingUnits(0);
            fvar.GetDTsbRecord().GetFiller1().SetDTsbUrBaseCost(0);
            fvar.GetDTsbRecord().GetFiller1().SetDTsbUrIndexedCost(0);
            fvar.GetDTsbRecord().GetFiller1().SetDTsbUrProceeds(0);
            fvar.GetDTsbRecord().GetFiller1().SetDTsbUrCapitalGainLoss(0);

            gvar.SetWsGainLoss(0);
            gvar.SetWsBasecost(0);
            gvar.SetWsProceeds(0);
            gvar.SetWsQuantity(0);
            gvar.SetWsUrIndexedCost(0);
        }
        /// <summary>
        /// COBOL Paragraph: C-END
        /// </summary>
        /// <remarks>
        /// This paragraph handles the finalization steps of the process.
        /// It performs a final write operation, logs the closing of files,
        /// and then closes the relevant files using the CGTFILES utility.
        /// Specifically, it closes the currently active file and then explicitly
        /// closes the UNREALISED-TSB-EXPORT-FILE.
        /// </remarks>
        /// <param name="fvar">File variables, used by B2WriteRecord if it accesses file records.</param>
        /// <param name="gvar">Global variables, holding application-wide data, linkage areas for logging and file operations.</param>
        /// <param name="ivar">Input/Output variables, passed for consistency with other methods.</param>
        public void CEnd(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // PERFORM B2-WRITE-RECORD
            B2WriteRecord(fvar, gvar, ivar);
            // MOVE       'F'      TO   L-LOG-MESSAGE-TYPE
            gvar.GetCgtlogLinkageArea2().SetLLogMessageType("F");

            // MOVE   CLOSE-FILE   TO   L-LOG-ACTION
            gvar.GetCgtlogLinkageArea1().SetLLogAction(Gvar.CLOSE_FILE);

            // PERFORM   X4-CGTLOG
            X4Cgtlog(fvar, gvar, ivar);

            // MOVE   CLOSE-FILE           TO   L-FILE-ACTION
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.CLOSE_FILE);

            // PERFORM   X-CALL-CGTFILES.
            // This call closes the file whose name is currently in L-FILE-NAME.
            XCallCgtfiles(fvar, gvar, ivar);

            // CLOSE   UNREALISED-TSB-EXPORT-FILE.
            // This is translated by setting the L-FILE-NAME to UNREALISED-TSB-EXPORT-FILE
            // and then calling X-CALL-CGTFILES. L-FILE-ACTION is already CLOSE-FILE.
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.UNREALISED_DATA_FILE);
            XCallCgtfiles(fvar, gvar, ivar);

        }
        /// <summary>
        /// This method implements the COBOL paragraph "X4CGTLOG".
        /// </summary>
        /// <param name="fvar">Fvar class parameter used to access placeholder variables.</param>
        /// <param name="gvar">Gvar class parameter used to access global variables.</param>
        /// <param name="ivar">Ivar class parameter.</param>
        /// <remarks>
        /// This method corresponds to the COBOL paragraph "X4CGTLOG".
        /// It increments WS-MESSAGE-NO, moves the value to L-LOG-MESSAGE-NO,
        /// calls the external program "CGTLOG" with the specified linkage areas,
        /// and conditionally sets WS-PROCESS-FLAG and L-LOG-MESSAGE-TYPE.
        /// </remarks>
        public void X4Cgtlog(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            var cgtlog = gvar.GetCgtlogLinkageArea1();
            var cgtlogLinkageArea2 = gvar.GetCgtlogLinkageArea2();
            var wsMessageNo = gvar.GetWsMessageNo();
            var lLogMessageType = cgtlogLinkageArea2.GetLLogMessageType();
            var lLogMessageNo = cgtlogLinkageArea2.GetLLogMessageNo();
            var wsProcessFlag = gvar.GetWsProcessFlag();

            // Increment WS-MESSAGE-NO
            var newWsMessageNo = wsMessageNo + 1;
            gvar.SetWsMessageNo(newWsMessageNo);

            gvar.GetCgtlogLinkageArea2().SetLLogMessageNo(newWsMessageNo.ToString("000"));
            // Call the external program CGTLOG
            Eqtlog eqtlog = new Eqtlog();
            eqtlog.GetIvar().SetLinkageArea1AsString(gvar.GetCgtlogLinkageArea1AsString());
            eqtlog.GetIvar().SetLinkageArea2AsString(gvar.GetCgtlogLinkageArea2AsString());

            eqtlog.Run(eqtlog.GetGvar(), eqtlog.GetIvar());

            // Check L-LOG-MESSAGE-TYPE and conditionally set WS-PROCESS-FLAG  the L-LOG-MESSAGE-TYPE
            if (lLogMessageType == "Q")
            {
                gvar.SetWsProcessFlag("Q");
                gvar.GetCgtlogLinkageArea2().SetLLogMessageType("P");
            }
        }
        /// <summary>
        /// Executes the EQTPATH program using the EQTPATH-LINKAGE parameter.
        /// </summary>
        ///
        /// <remarks>
        /// <para>This method emulates the COBOL paragraph <c>xCallEqtpath</c>.</para>
        /// <para>Original COBOL code:
        /// <code>CALL   'EQTPATH'   USING   EQTPATH-LINKAGE.</code>
        /// </para>
        /// </remarks>
        /// <param name="fvar">Parameter object for external variables (not used in this method)</param>
        /// <param name="gvar">Parameter object for global variables</param>
        /// <param name="ivar">Parameter object for internal variables (not used in this method)</param>
        public void XCallEqtpath(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the EQTPATH program
            Eqtpath eqtpath = new Eqtpath();
            eqtpath.GetIvar().SetEqtpathLinkageAsString(gvar.GetEqtpathLinkageAsString());
            eqtpath.Run(eqtpath.GetGvar(), eqtpath.GetIvar(), equityGlobalParms);
        }
        /// <summary>
        /// This method is converted from the COBOL paragraph "X-CALL-MF-HANDLER-FOR-CONFIG".
        /// It performs the equivalent logic in C#.
        /// This method converts the logic flow and business rules from the original COBOL paragraph to C#.
        /// It also uses modern C# conventions and best practices.
        /// </summary>
        /// <param name="fvar">
        /// Fvar parameter is used in some parts of the code that need its variables.
        /// For example, it might be used to pass a larger context or state.
        /// </param>
        /// <param name="gvar">
        /// Gvar parameter is used to access global variables.
        /// It contains all necessary getters and setters to access global variables.
        /// </param>
        /// <param name="ivar">
        /// Ivar parameter is used in some parts of the code that need its variables.
        /// It generally contains local context.
        /// </param>
        /// <remarks>
        /// Note:
        /// - The original operation in COBOL does the movings
        ///       MOVE component to other.
        /// - The CALL operation is converted.
        /// </remarks>
        public void XCallMfHandlerForConfig(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move the value of GET-CONFIG-VALUE to L-ACT
            gvar.GetElcgmioLinkage1().SetLAct(Gvar.GET_CONFIG_VALUE);

            // Call the external program 'ELCGMIO' using the ELCGMIO-LINKAGE-1 parameter

            /* must uncomment
            Elcgmio elcgmio = new Elcgmio();
            elcgmio.GetIvar().SetElcgmioLinkage1AsString(gvar.GetElcgmioLinkage1AsString());
            elcgmio.GetIvar().SetElcgmioLinkage2AsString(gvar.GetElcgmioLinkage2AsString());
            elcgmio.Run(elcgmio.GetGvar(), elcgmio.GetIvar());*/
            gvar.SetWConfigItem(gvar.GetElcgmioLinkage2AsString());

        }
        /// <summary>
        /// COBOL Paragraph: xCallCgtfiles
        /// </summary>
        /// <param name="fvar">Fvar parameter containing field variables</param>
        /// <param name="gvar">Gvar parameter containing global variables</param>
        /// <param name="ivar">Ivar parameter containing input variables</param>
        /// <remarks>
        /// This method translates the COBOL paragraph xCallCgtfiles.
        /// The original COBOL code calls the external program 'CGTFILES' using the specified linkage areas.
        /// </remarks>
        public void XCallCgtfiles(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the external program 'CGTFILES'
            /* must uncomment
            Cgtfiles cgtfiles = new Cgtfiles();
            cgtfiles.GetIvar().SetCgtfilesLinkageAsString(gvar.GetCgtfilesLinkageAsString());
            cgtfiles.GetIvar().SetCommonLinkageAsString(gvar.GetCommonLinkageAsString());
            cgtfiles.GetIvar().SetLFileRecordAreaAsString(gvar.GetLFileRecordAreaAsString());*/


        }

    }
}
