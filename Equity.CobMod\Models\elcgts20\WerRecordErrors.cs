using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WerRecordErrors Data Structure

public class WerRecordErrors
{
    private static int _size = 6960;
    // [DEBUG] Class: WerRecordErrors, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WerMsgs, is_external=, is_static_class=False, static_prefix=
    private WerMsgs _WerMsgs = new WerMsgs();
    
    
    
    
    // [DEBUG] Field: WerErrmsg, is_external=, is_static_class=False, static_prefix=
    private string[] _WerErrmsg = new string[58];
    
    
    
    
    // [DEBUG] Field: WErrorMessage, is_external=, is_static_class=False, static_prefix=
    private string[] _WErrorMessage = new string[58];
    
    
    
    
    
    // Serialization methods
    public string GetWerRecordErrorsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WerMsgs.GetWerMsgsAsString());
        for (int i = 0; i < 58; i++)
        {
            result.Append(_WerErrmsg[i].PadRight(40));
        }
        for (int i = 0; i < 58; i++)
        {
            result.Append(_WErrorMessage[i].PadRight(40));
        }
        
        return result.ToString();
    }
    
    public void SetWerRecordErrorsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2320 <= data.Length)
        {
            _WerMsgs.SetWerMsgsAsString(data.Substring(offset, 2320));
        }
        else
        {
            _WerMsgs.SetWerMsgsAsString(data.Substring(offset));
        }
        offset += 2320;
        for (int i = 0; i < 58; i++)
        {
            if (offset + 40 > data.Length) break;
            string val = data.Substring(offset, 40);
            
            _WerErrmsg[i] = val.Trim();
            offset += 40;
        }
        for (int i = 0; i < 58; i++)
        {
            if (offset + 40 > data.Length) break;
            string val = data.Substring(offset, 40);
            
            _WErrorMessage[i] = val.Trim();
            offset += 40;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWerRecordErrorsAsString();
    }
    // Set<>String Override function
    public void SetWerRecordErrors(string value)
    {
        SetWerRecordErrorsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public WerMsgs GetWerMsgs()
    {
        return _WerMsgs;
    }
    
    // Standard Setter
    public void SetWerMsgs(WerMsgs value)
    {
        _WerMsgs = value;
    }
    
    // Get<>AsString()
    public string GetWerMsgsAsString()
    {
        return _WerMsgs != null ? _WerMsgs.GetWerMsgsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWerMsgsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WerMsgs == null)
        {
            _WerMsgs = new WerMsgs();
        }
        _WerMsgs.SetWerMsgsAsString(value);
    }
    
    // Array Accessors for WerErrmsg
    public string GetWerErrmsgAt(int index)
    {
        return _WerErrmsg[index];
    }
    
    public void SetWerErrmsgAt(int index, string value)
    {
        _WerErrmsg[index] = value;
    }
    
    public string GetWerErrmsgAsStringAt(int index)
    {
        return _WerErrmsg[index].PadRight(40);
    }
    
    public void SetWerErrmsgAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _WerErrmsg[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetWerErrmsg()
    {
        return _WerErrmsg != null && _WerErrmsg.Length > 0
        ? _WerErrmsg[0]
        : default(string);
    }
    
    public void SetWerErrmsg(string value)
    {
        if (_WerErrmsg == null || _WerErrmsg.Length == 0)
        _WerErrmsg = new string[1];
        _WerErrmsg[0] = value;
    }
    
    public string GetWerErrmsgAsString()
    {
        return _WerErrmsg != null && _WerErrmsg.Length > 0
        ? _WerErrmsg[0].ToString()
        : string.Empty;
    }
    
    public void SetWerErrmsgAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_WerErrmsg == null || _WerErrmsg.Length == 0)
        _WerErrmsg = new string[1];
        
        _WerErrmsg[0] = value;
    }
    
    
    // Array Accessors for WErrorMessage
    public string GetWErrorMessageAt(int index)
    {
        return _WErrorMessage[index];
    }
    
    public void SetWErrorMessageAt(int index, string value)
    {
        _WErrorMessage[index] = value;
    }
    
    public string GetWErrorMessageAsStringAt(int index)
    {
        return _WErrorMessage[index].PadRight(40);
    }
    
    public void SetWErrorMessageAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _WErrorMessage[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetWErrorMessage()
    {
        return _WErrorMessage != null && _WErrorMessage.Length > 0
        ? _WErrorMessage[0]
        : default(string);
    }
    
    public void SetWErrorMessage(string value)
    {
        if (_WErrorMessage == null || _WErrorMessage.Length == 0)
        _WErrorMessage = new string[1];
        _WErrorMessage[0] = value;
    }
    
    public string GetWErrorMessageAsString()
    {
        return _WErrorMessage != null && _WErrorMessage.Length > 0
        ? _WErrorMessage[0].ToString()
        : string.Empty;
    }
    
    public void SetWErrorMessageAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_WErrorMessage == null || _WErrorMessage.Length == 0)
        _WErrorMessage = new string[1];
        
        _WErrorMessage[0] = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWerMsgs(string value)
    {
        _WerMsgs.SetWerMsgsAsString(value);
    }
    // Nested Class: WerMsgs
    public class WerMsgs
    {
        private static int _size = 2320;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler1, is_external=, is_static_class=False, static_prefix=
        private string _Filler1 ="TRANSACTION ERROR";
        
        
        
        
        // [DEBUG] Field: Filler2, is_external=, is_static_class=False, static_prefix=
        private string _Filler2 ="HEADER MISSING";
        
        
        
        
        // [DEBUG] Field: Filler3, is_external=, is_static_class=False, static_prefix=
        private string _Filler3 ="PARENT REF. NOT ON FILE";
        
        
        
        
        // [DEBUG] Field: Filler4, is_external=, is_static_class=False, static_prefix=
        private string _Filler4 ="PREVIOUS REF. NOT ON FILE";
        
        
        
        
        // [DEBUG] Field: Filler5, is_external=, is_static_class=False, static_prefix=
        private string _Filler5 ="SALE NOT FOUND";
        
        
        
        
        // [DEBUG] Field: Filler6, is_external=, is_static_class=False, static_prefix=
        private string _Filler6 ="PARENT REF. MISSING";
        
        
        
        
        // [DEBUG] Field: Filler7, is_external=, is_static_class=False, static_prefix=
        private string _Filler7 ="PREVIOUS REF. MISSING";
        
        
        
        
        // [DEBUG] Field: Filler8, is_external=, is_static_class=False, static_prefix=
        private string _Filler8 ="INTERNAL TABLE OVERFLOW";
        
        
        
        
        // [DEBUG] Field: Filler9, is_external=, is_static_class=False, static_prefix=
        private string _Filler9 ="CORRESPONDING PURCHASE NOT FOUND";
        
        
        
        
        // [DEBUG] Field: Filler10, is_external=, is_static_class=False, static_prefix=
        private string _Filler10 ="COMPANY/FUND RECORD MISSING";
        
        
        
        
        // [DEBUG] Field: Filler11, is_external=, is_static_class=False, static_prefix=
        private string _Filler11 ="SALE NOT MATCHED";
        
        
        
        
        // [DEBUG] Field: Filler12, is_external=, is_static_class=False, static_prefix=
        private string _Filler12 ="CALLS NOT MATCHED";
        
        
        
        
        // [DEBUG] Field: Filler13, is_external=, is_static_class=False, static_prefix=
        private string _Filler13 ="PROCEEDS IGNORED";
        
        
        
        
        // [DEBUG] Field: Filler14, is_external=, is_static_class=False, static_prefix=
        private string _Filler14 ="NO MASTER-FILE RECORDS FOR FUND";
        
        
        
        
        // [DEBUG] Field: Filler15, is_external=, is_static_class=False, static_prefix=
        private string _Filler15 ="INSUFFICIENT BALANCES";
        
        
        
        
        // [DEBUG] Field: Filler16, is_external=, is_static_class=False, static_prefix=
        private string _Filler16 ="TRANSFER ERROR";
        
        
        
        
        // [DEBUG] Field: Filler17, is_external=, is_static_class=False, static_prefix=
        private string _Filler17 ="WRITE FAILURE";
        
        
        
        
        // [DEBUG] Field: Filler18, is_external=, is_static_class=False, static_prefix=
        private string _Filler18 ="CALCULATED BALANCE ERROR";
        
        
        
        
        // [DEBUG] Field: Filler19, is_external=, is_static_class=False, static_prefix=
        private string _Filler19 ="BACKWARD INDEXATION (!)";
        
        
        
        
        // [DEBUG] Field: Filler20, is_external=, is_static_class=False, static_prefix=
        private string _Filler20 ="PARENT MKT PRICE MISSING";
        
        
        
        
        // [DEBUG] Field: Filler21, is_external=, is_static_class=False, static_prefix=
        private string _Filler21 ="RIGHTS MKT PRICE MISSING";
        
        
        
        
        // [DEBUG] Field: Filler22, is_external=, is_static_class=False, static_prefix=
        private string _Filler22 ="ALLOTMENT ALREADY MADE";
        
        
        
        
        // [DEBUG] Field: Filler23, is_external=, is_static_class=False, static_prefix=
        private string _Filler23 ="PRICE MISSING";
        
        
        
        
        // [DEBUG] Field: Filler24, is_external=, is_static_class=False, static_prefix=
        private string _Filler24 ="82 VALUE NOT CHECKED ON PP MATCH";
        
        
        
        
        // [DEBUG] Field: Filler25, is_external=, is_static_class=False, static_prefix=
        private string _Filler25 ="O/S LIABILITY MISSING ON ACQUISITION";
        
        
        
        
        // [DEBUG] Field: Filler26, is_external=, is_static_class=False, static_prefix=
        private string _Filler26 ="NO PARENT HOLDING";
        
        
        
        
        // [DEBUG] Field: Filler27, is_external=, is_static_class=False, static_prefix=
        private string _Filler27 ="LOOPING CALL SEQUENCE";
        
        
        
        
        // [DEBUG] Field: Filler28, is_external=, is_static_class=False, static_prefix=
        private string _Filler28 ="NO RCF FOR FUND/YEAR";
        
        
        
        
        // [DEBUG] Field: Filler29, is_external=, is_static_class=False, static_prefix=
        private string _Filler29 ="DATE OF ISSUE NOT SET ON STOCKS FILE";
        
        
        
        
        // [DEBUG] Field: Filler30, is_external=, is_static_class=False, static_prefix=
        private string _Filler30 ="NO HOLDING FOR ACCUMULATION";
        
        
        
        
        // [DEBUG] Field: Filler31, is_external=, is_static_class=False, static_prefix=
        private string _Filler31 ="PAR VALUE MISSING ON STOCK FILE";
        
        
        
        
        // [DEBUG] Field: Filler32, is_external=, is_static_class=False, static_prefix=
        private string _Filler32 ="MATURITY DATE MISSING ON STOCK FILE";
        
        
        
        
        // [DEBUG] Field: Filler33, is_external=, is_static_class=False, static_prefix=
        private string _Filler33 ="MATURITY DATE/PAR VALUE NOT ON STOCKFILE";
        
        
        
        
        // [DEBUG] Field: Filler34, is_external=, is_static_class=False, static_prefix=
        private string _Filler34 ="NO CALENDAR ENTRIES FOR MIXED-USE ASSET ";
        
        
        
        
        // [DEBUG] Field: Filler35, is_external=, is_static_class=False, static_prefix=
        private string _Filler35 ="CALENDAR STARTS AFTER TRANCHE ACQUIRED  ";
        
        
        
        
        // [DEBUG] Field: Filler36, is_external=, is_static_class=False, static_prefix=
        private string _Filler36 ="FUND FILE LOCK FAILED                   ";
        
        
        
        
        // [DEBUG] Field: Filler37, is_external=, is_static_class=False, static_prefix=
        private string _Filler37 ="UNMATCHED UNITS DURING TAPER MATCH      ";
        
        
        
        
        // [DEBUG] Field: Filler38, is_external=, is_static_class=False, static_prefix=
        private string _Filler38 ="NO WTGT UNITS AVAILABLE FOR TAPER MATCH ";
        
        
        
        
        // [DEBUG] Field: Filler39, is_external=, is_static_class=False, static_prefix=
        private string _Filler39 ="NO WTMBD MATCHES TRANCHE BARGAIN DATE   ";
        
        
        
        
        // [DEBUG] Field: Filler40, is_external=, is_static_class=False, static_prefix=
        private string _Filler40 ="WARNING FA03 EXCLUDED SALE GIVES A LOSS ";
        
        
        
        
        // [DEBUG] Field: Filler41, is_external=, is_static_class=False, static_prefix=
        private string _Filler41 ="WARNING FA03 MIXED SALE GIVES A LOSS    ";
        
        
        
        
        // [DEBUG] Field: Filler42, is_external=, is_static_class=False, static_prefix=
        private string _Filler42 ="WARNING NO END DATES FOR CALENDAR/YEAR  ";
        
        
        
        
        // [DEBUG] Field: Filler43, is_external=, is_static_class=False, static_prefix=
        private string _Filler43 ="WARNING PREVIOUS YEAR CALENDAR EMPTY    ";
        
        
        
        
        // [DEBUG] Field: Filler44, is_external=, is_static_class=False, static_prefix=
        private string _Filler44 ="WARNING EARLIER DAY'S PRICE USED        ";
        
        
        
        
        // [DEBUG] Field: Filler45, is_external=, is_static_class=False, static_prefix=
        private string _Filler45 ="WARNING EARLIER PRICE SESSION USED      ";
        
        
        
        
        // [DEBUG] Field: Filler46, is_external=, is_static_class=False, static_prefix=
        private string _Filler46 ="Futures and Options not licensed        ";
        
        
        
        
        // [DEBUG] Field: Filler47, is_external=, is_static_class=False, static_prefix=
        private string _Filler47 ="Option lapsed                           ";
        
        
        
        
        // [DEBUG] Field: Filler48, is_external=, is_static_class=False, static_prefix=
        private string _Filler48 ="Corresponding Option Exercise not found ";
        
        
        
        
        // [DEBUG] Field: Filler49, is_external=, is_static_class=False, static_prefix=
        private string _Filler49 ="Corresponding Stock  Exercise not found ";
        
        
        
        
        // [DEBUG] Field: Filler50, is_external=, is_static_class=False, static_prefix=
        private string _Filler50 ="Invalid derivative transaction category ";
        
        
        
        
        // [DEBUG] Field: Filler51, is_external=, is_static_class=False, static_prefix=
        private string _Filler51 ="Wrong exercise type for security type   ";
        
        
        
        
        // [DEBUG] Field: Filler52, is_external=, is_static_class=False, static_prefix=
        private string _Filler52 ="PURCHASE NOT MATCHED                    ";
        
        
        
        
        // [DEBUG] Field: Filler53, is_external=, is_static_class=False, static_prefix=
        private string _Filler53 ="RIGHTS VALUATION = 0                    ";
        
        
        
        
        // [DEBUG] Field: Filler54, is_external=, is_static_class=False, static_prefix=
        private string _Filler54 ="Irish CGT not licensed                  ";
        
        
        
        
        // [DEBUG] Field: Filler55, is_external=, is_static_class=False, static_prefix=
        private string _Filler55 ="FA12 PURCHASE WITHIN 10 DAYS PERIOD END ";
        
        
        
        
        // [DEBUG] Field: Filler56, is_external=, is_static_class=False, static_prefix=
        private string _Filler56 ="Future not closed out on delivery date  ";
        
        
        
        
        // [DEBUG] Field: Filler57, is_external=, is_static_class=False, static_prefix=
        private string _Filler57 ="Transaction occurred after delivery date";
        
        
        
        
        // [DEBUG] Field: Filler58, is_external=, is_static_class=False, static_prefix=
        private string _Filler58 ="Enhancement larger than tranche value   ";
        
        
        
        
    public WerMsgs() {}
    
    public WerMsgs(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller1(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller2(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller3(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller4(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller5(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller6(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller7(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller8(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller9(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller10(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller11(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller12(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller13(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller14(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller15(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller16(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller17(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller18(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller19(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller20(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller21(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller22(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller23(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller24(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller25(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller26(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller27(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller28(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller29(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller30(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller31(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller32(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller33(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller34(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller35(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller36(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller37(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller38(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller39(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller40(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller41(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller42(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller43(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller44(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller45(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller46(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller47(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller48(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller49(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller50(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller51(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller52(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller53(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller54(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller55(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller56(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller57(data.Substring(offset, 40).Trim());
        offset += 40;
        SetFiller58(data.Substring(offset, 40).Trim());
        offset += 40;
        
    }
    
    // Serialization methods
    public string GetWerMsgsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler1.PadRight(40));
        result.Append(_Filler2.PadRight(40));
        result.Append(_Filler3.PadRight(40));
        result.Append(_Filler4.PadRight(40));
        result.Append(_Filler5.PadRight(40));
        result.Append(_Filler6.PadRight(40));
        result.Append(_Filler7.PadRight(40));
        result.Append(_Filler8.PadRight(40));
        result.Append(_Filler9.PadRight(40));
        result.Append(_Filler10.PadRight(40));
        result.Append(_Filler11.PadRight(40));
        result.Append(_Filler12.PadRight(40));
        result.Append(_Filler13.PadRight(40));
        result.Append(_Filler14.PadRight(40));
        result.Append(_Filler15.PadRight(40));
        result.Append(_Filler16.PadRight(40));
        result.Append(_Filler17.PadRight(40));
        result.Append(_Filler18.PadRight(40));
        result.Append(_Filler19.PadRight(40));
        result.Append(_Filler20.PadRight(40));
        result.Append(_Filler21.PadRight(40));
        result.Append(_Filler22.PadRight(40));
        result.Append(_Filler23.PadRight(40));
        result.Append(_Filler24.PadRight(40));
        result.Append(_Filler25.PadRight(40));
        result.Append(_Filler26.PadRight(40));
        result.Append(_Filler27.PadRight(40));
        result.Append(_Filler28.PadRight(40));
        result.Append(_Filler29.PadRight(40));
        result.Append(_Filler30.PadRight(40));
        result.Append(_Filler31.PadRight(40));
        result.Append(_Filler32.PadRight(40));
        result.Append(_Filler33.PadRight(40));
        result.Append(_Filler34.PadRight(40));
        result.Append(_Filler35.PadRight(40));
        result.Append(_Filler36.PadRight(40));
        result.Append(_Filler37.PadRight(40));
        result.Append(_Filler38.PadRight(40));
        result.Append(_Filler39.PadRight(40));
        result.Append(_Filler40.PadRight(40));
        result.Append(_Filler41.PadRight(40));
        result.Append(_Filler42.PadRight(40));
        result.Append(_Filler43.PadRight(40));
        result.Append(_Filler44.PadRight(40));
        result.Append(_Filler45.PadRight(40));
        result.Append(_Filler46.PadRight(40));
        result.Append(_Filler47.PadRight(40));
        result.Append(_Filler48.PadRight(40));
        result.Append(_Filler49.PadRight(40));
        result.Append(_Filler50.PadRight(40));
        result.Append(_Filler51.PadRight(40));
        result.Append(_Filler52.PadRight(40));
        result.Append(_Filler53.PadRight(40));
        result.Append(_Filler54.PadRight(40));
        result.Append(_Filler55.PadRight(40));
        result.Append(_Filler56.PadRight(40));
        result.Append(_Filler57.PadRight(40));
        result.Append(_Filler58.PadRight(40));
        
        return result.ToString();
    }
    
    public void SetWerMsgsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller1(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller2(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller3(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller4(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller5(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller6(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller7(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller8(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller9(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller10(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller11(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller12(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller13(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller14(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller15(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller16(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller17(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller18(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller19(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller20(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller21(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller22(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller23(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller24(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller25(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller26(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller27(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller28(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller29(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller30(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller31(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller32(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller33(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller34(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller35(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller36(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller37(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller38(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller39(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller40(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller41(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller42(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller43(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller44(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller45(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller46(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller47(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller48(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller49(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller50(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller51(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller52(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller53(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller54(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller55(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller56(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller57(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller58(extracted);
        }
        offset += 40;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller1()
    {
        return _Filler1;
    }
    
    // Standard Setter
    public void SetFiller1(string value)
    {
        _Filler1 = value;
    }
    
    // Get<>AsString()
    public string GetFiller1AsString()
    {
        return _Filler1.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler1 = value;
    }
    
    // Standard Getter
    public string GetFiller2()
    {
        return _Filler2;
    }
    
    // Standard Setter
    public void SetFiller2(string value)
    {
        _Filler2 = value;
    }
    
    // Get<>AsString()
    public string GetFiller2AsString()
    {
        return _Filler2.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler2 = value;
    }
    
    // Standard Getter
    public string GetFiller3()
    {
        return _Filler3;
    }
    
    // Standard Setter
    public void SetFiller3(string value)
    {
        _Filler3 = value;
    }
    
    // Get<>AsString()
    public string GetFiller3AsString()
    {
        return _Filler3.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler3 = value;
    }
    
    // Standard Getter
    public string GetFiller4()
    {
        return _Filler4;
    }
    
    // Standard Setter
    public void SetFiller4(string value)
    {
        _Filler4 = value;
    }
    
    // Get<>AsString()
    public string GetFiller4AsString()
    {
        return _Filler4.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler4 = value;
    }
    
    // Standard Getter
    public string GetFiller5()
    {
        return _Filler5;
    }
    
    // Standard Setter
    public void SetFiller5(string value)
    {
        _Filler5 = value;
    }
    
    // Get<>AsString()
    public string GetFiller5AsString()
    {
        return _Filler5.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler5 = value;
    }
    
    // Standard Getter
    public string GetFiller6()
    {
        return _Filler6;
    }
    
    // Standard Setter
    public void SetFiller6(string value)
    {
        _Filler6 = value;
    }
    
    // Get<>AsString()
    public string GetFiller6AsString()
    {
        return _Filler6.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler6 = value;
    }
    
    // Standard Getter
    public string GetFiller7()
    {
        return _Filler7;
    }
    
    // Standard Setter
    public void SetFiller7(string value)
    {
        _Filler7 = value;
    }
    
    // Get<>AsString()
    public string GetFiller7AsString()
    {
        return _Filler7.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler7 = value;
    }
    
    // Standard Getter
    public string GetFiller8()
    {
        return _Filler8;
    }
    
    // Standard Setter
    public void SetFiller8(string value)
    {
        _Filler8 = value;
    }
    
    // Get<>AsString()
    public string GetFiller8AsString()
    {
        return _Filler8.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler8 = value;
    }
    
    // Standard Getter
    public string GetFiller9()
    {
        return _Filler9;
    }
    
    // Standard Setter
    public void SetFiller9(string value)
    {
        _Filler9 = value;
    }
    
    // Get<>AsString()
    public string GetFiller9AsString()
    {
        return _Filler9.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler9 = value;
    }
    
    // Standard Getter
    public string GetFiller10()
    {
        return _Filler10;
    }
    
    // Standard Setter
    public void SetFiller10(string value)
    {
        _Filler10 = value;
    }
    
    // Get<>AsString()
    public string GetFiller10AsString()
    {
        return _Filler10.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler10 = value;
    }
    
    // Standard Getter
    public string GetFiller11()
    {
        return _Filler11;
    }
    
    // Standard Setter
    public void SetFiller11(string value)
    {
        _Filler11 = value;
    }
    
    // Get<>AsString()
    public string GetFiller11AsString()
    {
        return _Filler11.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller11AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler11 = value;
    }
    
    // Standard Getter
    public string GetFiller12()
    {
        return _Filler12;
    }
    
    // Standard Setter
    public void SetFiller12(string value)
    {
        _Filler12 = value;
    }
    
    // Get<>AsString()
    public string GetFiller12AsString()
    {
        return _Filler12.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller12AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler12 = value;
    }
    
    // Standard Getter
    public string GetFiller13()
    {
        return _Filler13;
    }
    
    // Standard Setter
    public void SetFiller13(string value)
    {
        _Filler13 = value;
    }
    
    // Get<>AsString()
    public string GetFiller13AsString()
    {
        return _Filler13.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller13AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler13 = value;
    }
    
    // Standard Getter
    public string GetFiller14()
    {
        return _Filler14;
    }
    
    // Standard Setter
    public void SetFiller14(string value)
    {
        _Filler14 = value;
    }
    
    // Get<>AsString()
    public string GetFiller14AsString()
    {
        return _Filler14.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller14AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler14 = value;
    }
    
    // Standard Getter
    public string GetFiller15()
    {
        return _Filler15;
    }
    
    // Standard Setter
    public void SetFiller15(string value)
    {
        _Filler15 = value;
    }
    
    // Get<>AsString()
    public string GetFiller15AsString()
    {
        return _Filler15.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller15AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler15 = value;
    }
    
    // Standard Getter
    public string GetFiller16()
    {
        return _Filler16;
    }
    
    // Standard Setter
    public void SetFiller16(string value)
    {
        _Filler16 = value;
    }
    
    // Get<>AsString()
    public string GetFiller16AsString()
    {
        return _Filler16.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller16AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler16 = value;
    }
    
    // Standard Getter
    public string GetFiller17()
    {
        return _Filler17;
    }
    
    // Standard Setter
    public void SetFiller17(string value)
    {
        _Filler17 = value;
    }
    
    // Get<>AsString()
    public string GetFiller17AsString()
    {
        return _Filler17.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller17AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler17 = value;
    }
    
    // Standard Getter
    public string GetFiller18()
    {
        return _Filler18;
    }
    
    // Standard Setter
    public void SetFiller18(string value)
    {
        _Filler18 = value;
    }
    
    // Get<>AsString()
    public string GetFiller18AsString()
    {
        return _Filler18.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller18AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler18 = value;
    }
    
    // Standard Getter
    public string GetFiller19()
    {
        return _Filler19;
    }
    
    // Standard Setter
    public void SetFiller19(string value)
    {
        _Filler19 = value;
    }
    
    // Get<>AsString()
    public string GetFiller19AsString()
    {
        return _Filler19.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller19AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler19 = value;
    }
    
    // Standard Getter
    public string GetFiller20()
    {
        return _Filler20;
    }
    
    // Standard Setter
    public void SetFiller20(string value)
    {
        _Filler20 = value;
    }
    
    // Get<>AsString()
    public string GetFiller20AsString()
    {
        return _Filler20.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller20AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler20 = value;
    }
    
    // Standard Getter
    public string GetFiller21()
    {
        return _Filler21;
    }
    
    // Standard Setter
    public void SetFiller21(string value)
    {
        _Filler21 = value;
    }
    
    // Get<>AsString()
    public string GetFiller21AsString()
    {
        return _Filler21.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller21AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler21 = value;
    }
    
    // Standard Getter
    public string GetFiller22()
    {
        return _Filler22;
    }
    
    // Standard Setter
    public void SetFiller22(string value)
    {
        _Filler22 = value;
    }
    
    // Get<>AsString()
    public string GetFiller22AsString()
    {
        return _Filler22.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller22AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler22 = value;
    }
    
    // Standard Getter
    public string GetFiller23()
    {
        return _Filler23;
    }
    
    // Standard Setter
    public void SetFiller23(string value)
    {
        _Filler23 = value;
    }
    
    // Get<>AsString()
    public string GetFiller23AsString()
    {
        return _Filler23.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller23AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler23 = value;
    }
    
    // Standard Getter
    public string GetFiller24()
    {
        return _Filler24;
    }
    
    // Standard Setter
    public void SetFiller24(string value)
    {
        _Filler24 = value;
    }
    
    // Get<>AsString()
    public string GetFiller24AsString()
    {
        return _Filler24.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller24AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler24 = value;
    }
    
    // Standard Getter
    public string GetFiller25()
    {
        return _Filler25;
    }
    
    // Standard Setter
    public void SetFiller25(string value)
    {
        _Filler25 = value;
    }
    
    // Get<>AsString()
    public string GetFiller25AsString()
    {
        return _Filler25.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller25AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler25 = value;
    }
    
    // Standard Getter
    public string GetFiller26()
    {
        return _Filler26;
    }
    
    // Standard Setter
    public void SetFiller26(string value)
    {
        _Filler26 = value;
    }
    
    // Get<>AsString()
    public string GetFiller26AsString()
    {
        return _Filler26.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller26AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler26 = value;
    }
    
    // Standard Getter
    public string GetFiller27()
    {
        return _Filler27;
    }
    
    // Standard Setter
    public void SetFiller27(string value)
    {
        _Filler27 = value;
    }
    
    // Get<>AsString()
    public string GetFiller27AsString()
    {
        return _Filler27.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller27AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler27 = value;
    }
    
    // Standard Getter
    public string GetFiller28()
    {
        return _Filler28;
    }
    
    // Standard Setter
    public void SetFiller28(string value)
    {
        _Filler28 = value;
    }
    
    // Get<>AsString()
    public string GetFiller28AsString()
    {
        return _Filler28.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller28AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler28 = value;
    }
    
    // Standard Getter
    public string GetFiller29()
    {
        return _Filler29;
    }
    
    // Standard Setter
    public void SetFiller29(string value)
    {
        _Filler29 = value;
    }
    
    // Get<>AsString()
    public string GetFiller29AsString()
    {
        return _Filler29.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller29AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler29 = value;
    }
    
    // Standard Getter
    public string GetFiller30()
    {
        return _Filler30;
    }
    
    // Standard Setter
    public void SetFiller30(string value)
    {
        _Filler30 = value;
    }
    
    // Get<>AsString()
    public string GetFiller30AsString()
    {
        return _Filler30.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller30AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler30 = value;
    }
    
    // Standard Getter
    public string GetFiller31()
    {
        return _Filler31;
    }
    
    // Standard Setter
    public void SetFiller31(string value)
    {
        _Filler31 = value;
    }
    
    // Get<>AsString()
    public string GetFiller31AsString()
    {
        return _Filler31.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller31AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler31 = value;
    }
    
    // Standard Getter
    public string GetFiller32()
    {
        return _Filler32;
    }
    
    // Standard Setter
    public void SetFiller32(string value)
    {
        _Filler32 = value;
    }
    
    // Get<>AsString()
    public string GetFiller32AsString()
    {
        return _Filler32.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller32AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler32 = value;
    }
    
    // Standard Getter
    public string GetFiller33()
    {
        return _Filler33;
    }
    
    // Standard Setter
    public void SetFiller33(string value)
    {
        _Filler33 = value;
    }
    
    // Get<>AsString()
    public string GetFiller33AsString()
    {
        return _Filler33.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller33AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler33 = value;
    }
    
    // Standard Getter
    public string GetFiller34()
    {
        return _Filler34;
    }
    
    // Standard Setter
    public void SetFiller34(string value)
    {
        _Filler34 = value;
    }
    
    // Get<>AsString()
    public string GetFiller34AsString()
    {
        return _Filler34.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller34AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler34 = value;
    }
    
    // Standard Getter
    public string GetFiller35()
    {
        return _Filler35;
    }
    
    // Standard Setter
    public void SetFiller35(string value)
    {
        _Filler35 = value;
    }
    
    // Get<>AsString()
    public string GetFiller35AsString()
    {
        return _Filler35.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller35AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler35 = value;
    }
    
    // Standard Getter
    public string GetFiller36()
    {
        return _Filler36;
    }
    
    // Standard Setter
    public void SetFiller36(string value)
    {
        _Filler36 = value;
    }
    
    // Get<>AsString()
    public string GetFiller36AsString()
    {
        return _Filler36.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller36AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler36 = value;
    }
    
    // Standard Getter
    public string GetFiller37()
    {
        return _Filler37;
    }
    
    // Standard Setter
    public void SetFiller37(string value)
    {
        _Filler37 = value;
    }
    
    // Get<>AsString()
    public string GetFiller37AsString()
    {
        return _Filler37.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller37AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler37 = value;
    }
    
    // Standard Getter
    public string GetFiller38()
    {
        return _Filler38;
    }
    
    // Standard Setter
    public void SetFiller38(string value)
    {
        _Filler38 = value;
    }
    
    // Get<>AsString()
    public string GetFiller38AsString()
    {
        return _Filler38.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller38AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler38 = value;
    }
    
    // Standard Getter
    public string GetFiller39()
    {
        return _Filler39;
    }
    
    // Standard Setter
    public void SetFiller39(string value)
    {
        _Filler39 = value;
    }
    
    // Get<>AsString()
    public string GetFiller39AsString()
    {
        return _Filler39.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller39AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler39 = value;
    }
    
    // Standard Getter
    public string GetFiller40()
    {
        return _Filler40;
    }
    
    // Standard Setter
    public void SetFiller40(string value)
    {
        _Filler40 = value;
    }
    
    // Get<>AsString()
    public string GetFiller40AsString()
    {
        return _Filler40.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller40AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler40 = value;
    }
    
    // Standard Getter
    public string GetFiller41()
    {
        return _Filler41;
    }
    
    // Standard Setter
    public void SetFiller41(string value)
    {
        _Filler41 = value;
    }
    
    // Get<>AsString()
    public string GetFiller41AsString()
    {
        return _Filler41.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller41AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler41 = value;
    }
    
    // Standard Getter
    public string GetFiller42()
    {
        return _Filler42;
    }
    
    // Standard Setter
    public void SetFiller42(string value)
    {
        _Filler42 = value;
    }
    
    // Get<>AsString()
    public string GetFiller42AsString()
    {
        return _Filler42.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller42AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler42 = value;
    }
    
    // Standard Getter
    public string GetFiller43()
    {
        return _Filler43;
    }
    
    // Standard Setter
    public void SetFiller43(string value)
    {
        _Filler43 = value;
    }
    
    // Get<>AsString()
    public string GetFiller43AsString()
    {
        return _Filler43.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller43AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler43 = value;
    }
    
    // Standard Getter
    public string GetFiller44()
    {
        return _Filler44;
    }
    
    // Standard Setter
    public void SetFiller44(string value)
    {
        _Filler44 = value;
    }
    
    // Get<>AsString()
    public string GetFiller44AsString()
    {
        return _Filler44.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller44AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler44 = value;
    }
    
    // Standard Getter
    public string GetFiller45()
    {
        return _Filler45;
    }
    
    // Standard Setter
    public void SetFiller45(string value)
    {
        _Filler45 = value;
    }
    
    // Get<>AsString()
    public string GetFiller45AsString()
    {
        return _Filler45.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller45AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler45 = value;
    }
    
    // Standard Getter
    public string GetFiller46()
    {
        return _Filler46;
    }
    
    // Standard Setter
    public void SetFiller46(string value)
    {
        _Filler46 = value;
    }
    
    // Get<>AsString()
    public string GetFiller46AsString()
    {
        return _Filler46.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller46AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler46 = value;
    }
    
    // Standard Getter
    public string GetFiller47()
    {
        return _Filler47;
    }
    
    // Standard Setter
    public void SetFiller47(string value)
    {
        _Filler47 = value;
    }
    
    // Get<>AsString()
    public string GetFiller47AsString()
    {
        return _Filler47.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller47AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler47 = value;
    }
    
    // Standard Getter
    public string GetFiller48()
    {
        return _Filler48;
    }
    
    // Standard Setter
    public void SetFiller48(string value)
    {
        _Filler48 = value;
    }
    
    // Get<>AsString()
    public string GetFiller48AsString()
    {
        return _Filler48.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller48AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler48 = value;
    }
    
    // Standard Getter
    public string GetFiller49()
    {
        return _Filler49;
    }
    
    // Standard Setter
    public void SetFiller49(string value)
    {
        _Filler49 = value;
    }
    
    // Get<>AsString()
    public string GetFiller49AsString()
    {
        return _Filler49.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller49AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler49 = value;
    }
    
    // Standard Getter
    public string GetFiller50()
    {
        return _Filler50;
    }
    
    // Standard Setter
    public void SetFiller50(string value)
    {
        _Filler50 = value;
    }
    
    // Get<>AsString()
    public string GetFiller50AsString()
    {
        return _Filler50.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller50AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler50 = value;
    }
    
    // Standard Getter
    public string GetFiller51()
    {
        return _Filler51;
    }
    
    // Standard Setter
    public void SetFiller51(string value)
    {
        _Filler51 = value;
    }
    
    // Get<>AsString()
    public string GetFiller51AsString()
    {
        return _Filler51.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller51AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler51 = value;
    }
    
    // Standard Getter
    public string GetFiller52()
    {
        return _Filler52;
    }
    
    // Standard Setter
    public void SetFiller52(string value)
    {
        _Filler52 = value;
    }
    
    // Get<>AsString()
    public string GetFiller52AsString()
    {
        return _Filler52.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller52AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler52 = value;
    }
    
    // Standard Getter
    public string GetFiller53()
    {
        return _Filler53;
    }
    
    // Standard Setter
    public void SetFiller53(string value)
    {
        _Filler53 = value;
    }
    
    // Get<>AsString()
    public string GetFiller53AsString()
    {
        return _Filler53.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller53AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler53 = value;
    }
    
    // Standard Getter
    public string GetFiller54()
    {
        return _Filler54;
    }
    
    // Standard Setter
    public void SetFiller54(string value)
    {
        _Filler54 = value;
    }
    
    // Get<>AsString()
    public string GetFiller54AsString()
    {
        return _Filler54.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller54AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler54 = value;
    }
    
    // Standard Getter
    public string GetFiller55()
    {
        return _Filler55;
    }
    
    // Standard Setter
    public void SetFiller55(string value)
    {
        _Filler55 = value;
    }
    
    // Get<>AsString()
    public string GetFiller55AsString()
    {
        return _Filler55.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller55AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler55 = value;
    }
    
    // Standard Getter
    public string GetFiller56()
    {
        return _Filler56;
    }
    
    // Standard Setter
    public void SetFiller56(string value)
    {
        _Filler56 = value;
    }
    
    // Get<>AsString()
    public string GetFiller56AsString()
    {
        return _Filler56.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller56AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler56 = value;
    }
    
    // Standard Getter
    public string GetFiller57()
    {
        return _Filler57;
    }
    
    // Standard Setter
    public void SetFiller57(string value)
    {
        _Filler57 = value;
    }
    
    // Get<>AsString()
    public string GetFiller57AsString()
    {
        return _Filler57.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller57AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler57 = value;
    }
    
    // Standard Getter
    public string GetFiller58()
    {
        return _Filler58;
    }
    
    // Standard Setter
    public void SetFiller58(string value)
    {
        _Filler58 = value;
    }
    
    // Get<>AsString()
    public string GetFiller58AsString()
    {
        return _Filler58.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller58AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler58 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}