using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D46File Data Structure

public class D46File
{
    private static int _size = 128;
    // [DEBUG] Class: D46File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler213, is_external=, is_static_class=False, static_prefix=
    private string _Filler213 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD46FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler213.PadRight(128));
        
        return result.ToString();
    }
    
    public void SetD46FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 128 <= data.Length)
        {
            string extracted = data.Substring(offset, 128).Trim();
            SetFiller213(extracted);
        }
        offset += 128;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD46FileAsString();
    }
    // Set<>String Override function
    public void SetD46File(string value)
    {
        SetD46FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller213()
    {
        return _Filler213;
    }
    
    // Standard Setter
    public void SetFiller213(string value)
    {
        _Filler213 = value;
    }
    
    // Get<>AsString()
    public string GetFiller213AsString()
    {
        return _Filler213.PadRight(128);
    }
    
    // Set<>AsString()
    public void SetFiller213AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler213 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}