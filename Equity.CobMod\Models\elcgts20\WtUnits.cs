using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtUnits Data Structure

public class WtUnits
{
    private static int _size = 92024;
    // [DEBUG] Class: WtUnits, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler475, is_external=, is_static_class=False, static_prefix=
    private string _Filler475 ="UNITS===========";
    
    
    
    
    // [DEBUG] Field: WtuMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtuMaxTableSize =4000;
    
    
    
    
    // [DEBUG] Field: WtuOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtuOccurs =4000;
    
    
    
    
    // [DEBUG] Field: WtuElement, is_external=, is_static_class=False, static_prefix=
    private WtuElement[] _WtuElement = new WtuElement[4000];
    
    public void InitializeWtuElementArray()
    {
        for (int i = 0; i < 4000; i++)
        {
            _WtuElement[i] = new WtuElement();
        }
    }
    
    
    
    
    // Serialization methods
    public string GetWtUnitsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler475.PadRight(16));
        result.Append(_WtuMaxTableSize.ToString().PadLeft(4, '0'));
        result.Append(_WtuOccurs.ToString().PadLeft(4, '0'));
        for (int i = 0; i < 4000; i++)
        {
            result.Append(_WtuElement[i].GetWtuElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtUnitsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller475(extracted);
        }
        offset += 16;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtuMaxTableSize(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtuOccurs(parsedInt);
        }
        offset += 4;
        for (int i = 0; i < 4000; i++)
        {
            if (offset + 23 > data.Length) break;
            string val = data.Substring(offset, 23);
            
            _WtuElement[i].SetWtuElementAsString(val);
            offset += 23;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtUnitsAsString();
    }
    // Set<>String Override function
    public void SetWtUnits(string value)
    {
        SetWtUnitsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller475()
    {
        return _Filler475;
    }
    
    // Standard Setter
    public void SetFiller475(string value)
    {
        _Filler475 = value;
    }
    
    // Get<>AsString()
    public string GetFiller475AsString()
    {
        return _Filler475.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller475AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler475 = value;
    }
    
    // Standard Getter
    public int GetWtuMaxTableSize()
    {
        return _WtuMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtuMaxTableSize(int value)
    {
        _WtuMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtuMaxTableSizeAsString()
    {
        return _WtuMaxTableSize.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtuMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtuMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtuOccurs()
    {
        return _WtuOccurs;
    }
    
    // Standard Setter
    public void SetWtuOccurs(int value)
    {
        _WtuOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtuOccursAsString()
    {
        return _WtuOccurs.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtuOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtuOccurs = parsed;
    }
    
    // Array Accessors for WtuElement
    public WtuElement GetWtuElementAt(int index)
    {
        return _WtuElement[index];
    }
    
    public void SetWtuElementAt(int index, WtuElement value)
    {
        _WtuElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtuElement GetWtuElement()
    {
        return _WtuElement != null && _WtuElement.Length > 0
        ? _WtuElement[0]
        : new WtuElement();
    }
    
    public void SetWtuElement(WtuElement value)
    {
        if (_WtuElement == null || _WtuElement.Length == 0)
        _WtuElement = new WtuElement[1];
        _WtuElement[0] = value;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtuElement(string value)
    {
        if (!string.IsNullOrEmpty(value) && value.Length < WtuElement.GetSize() * _WtuElement.Length)
        {
            value = value.PadRight(WtuElement.GetSize() * _WtuElement.Length);
        }
        
        int offset = 0;
        for (int i = 0; i < _WtuElement.Length; i++)
        {
            if (offset + WtuElement.GetSize() > value.Length) break;
            string chunk = value.Substring(offset, WtuElement.GetSize());
            _WtuElement[i].SetWtuElementAsString(chunk);
            offset += WtuElement.GetSize();
        }
    }
    // Nested Class: WtuElement
    public class WtuElement
    {
        private static int _size = 23;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtuWttc, is_external=, is_static_class=False, static_prefix=
        private string _WtuWttc ="";
        
        
        
        
        // [DEBUG] Field: WtuCostOfUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WtuCostOfUnits =0;
        
        
        
        
        // [DEBUG] Field: WtuCostOccurs, is_external=, is_static_class=False, static_prefix=
        private int _WtuCostOccurs =0;
        
        
        
        
    public WtuElement() {}
    
    public WtuElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtuWttc(data.Substring(offset, 4).Trim());
        offset += 4;
        SetWtuCostOfUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
        offset += 17;
        SetWtuCostOccurs(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetWtuElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtuWttc.PadRight(4));
        result.Append(_WtuCostOfUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtuCostOccurs.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetWtuElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtuWttc(extracted);
        }
        offset += 4;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtuCostOfUnits(parsedDec);
        }
        offset += 17;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtuCostOccurs(parsedInt);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtuWttc()
    {
        return _WtuWttc;
    }
    
    // Standard Setter
    public void SetWtuWttc(string value)
    {
        _WtuWttc = value;
    }
    
    // Get<>AsString()
    public string GetWtuWttcAsString()
    {
        return _WtuWttc.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtuWttcAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtuWttc = value;
    }
    
    // Standard Getter
    public decimal GetWtuCostOfUnits()
    {
        return _WtuCostOfUnits;
    }
    
    // Standard Setter
    public void SetWtuCostOfUnits(decimal value)
    {
        _WtuCostOfUnits = value;
    }
    
    // Get<>AsString()
    public string GetWtuCostOfUnitsAsString()
    {
        return _WtuCostOfUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtuCostOfUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtuCostOfUnits = parsed;
    }
    
    // Standard Getter
    public int GetWtuCostOccurs()
    {
        return _WtuCostOccurs;
    }
    
    // Standard Setter
    public void SetWtuCostOccurs(int value)
    {
        _WtuCostOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtuCostOccursAsString()
    {
        return _WtuCostOccurs.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWtuCostOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtuCostOccurs = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}
