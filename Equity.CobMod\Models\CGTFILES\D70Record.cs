using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D70Record Data Structure

public class D70Record
{
    private static int _size = 61;
    // [DEBUG] Class: D70Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D70SedolCode, is_external=, is_static_class=False, static_prefix=
    private string _D70SedolCode ="";
    
    
    
    
    // [DEBUG] Field: D70PriceDate, is_external=, is_static_class=False, static_prefix=
    private int _D70PriceDate =0;
    
    
    
    
    // [DEBUG] Field: D70MarketPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _D70MarketPrice =0;
    
    
    
    
    // [DEBUG] Field: D70CurrencyCode, is_external=, is_static_class=False, static_prefix=
    private string _D70CurrencyCode ="";
    
    
    
    
    // [DEBUG] Field: D70SequenceCode, is_external=, is_static_class=False, static_prefix=
    private int _D70SequenceCode =0;
    
    
    
    
    // [DEBUG] Field: D70SequenceCodeX, is_external=, is_static_class=False, static_prefix=
    private string _D70SequenceCodeX ="";
    
    
    
    
    // [DEBUG] Field: Filler81, is_external=, is_static_class=False, static_prefix=
    private string _Filler81 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD70RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D70SedolCode.PadRight(7));
        result.Append(_D70PriceDate.ToString().PadLeft(6, '0'));
        result.Append(_D70MarketPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D70CurrencyCode.PadRight(3));
        result.Append(_D70SequenceCode.ToString().PadLeft(3, '0'));
        result.Append(_D70SequenceCodeX.PadRight(3));
        result.Append(_Filler81.PadRight(33));
        
        return result.ToString();
    }
    
    public void SetD70RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetD70SedolCode(extracted);
        }
        offset += 7;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD70PriceDate(parsedInt);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD70MarketPrice(parsedDec);
        }
        offset += 6;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD70CurrencyCode(extracted);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD70SequenceCode(parsedInt);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD70SequenceCodeX(extracted);
        }
        offset += 3;
        if (offset + 33 <= data.Length)
        {
            string extracted = data.Substring(offset, 33).Trim();
            SetFiller81(extracted);
        }
        offset += 33;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD70RecordAsString();
    }
    // Set<>String Override function
    public void SetD70Record(string value)
    {
        SetD70RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD70SedolCode()
    {
        return _D70SedolCode;
    }
    
    // Standard Setter
    public void SetD70SedolCode(string value)
    {
        _D70SedolCode = value;
    }
    
    // Get<>AsString()
    public string GetD70SedolCodeAsString()
    {
        return _D70SedolCode.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetD70SedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D70SedolCode = value;
    }
    
    // Standard Getter
    public int GetD70PriceDate()
    {
        return _D70PriceDate;
    }
    
    // Standard Setter
    public void SetD70PriceDate(int value)
    {
        _D70PriceDate = value;
    }
    
    // Get<>AsString()
    public string GetD70PriceDateAsString()
    {
        return _D70PriceDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD70PriceDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D70PriceDate = parsed;
    }
    
    // Standard Getter
    public decimal GetD70MarketPrice()
    {
        return _D70MarketPrice;
    }
    
    // Standard Setter
    public void SetD70MarketPrice(decimal value)
    {
        _D70MarketPrice = value;
    }
    
    // Get<>AsString()
    public string GetD70MarketPriceAsString()
    {
        return _D70MarketPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD70MarketPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D70MarketPrice = parsed;
    }
    
    // Standard Getter
    public string GetD70CurrencyCode()
    {
        return _D70CurrencyCode;
    }
    
    // Standard Setter
    public void SetD70CurrencyCode(string value)
    {
        _D70CurrencyCode = value;
    }
    
    // Get<>AsString()
    public string GetD70CurrencyCodeAsString()
    {
        return _D70CurrencyCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD70CurrencyCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D70CurrencyCode = value;
    }
    
    // Standard Getter
    public int GetD70SequenceCode()
    {
        return _D70SequenceCode;
    }
    
    // Standard Setter
    public void SetD70SequenceCode(int value)
    {
        _D70SequenceCode = value;
    }
    
    // Get<>AsString()
    public string GetD70SequenceCodeAsString()
    {
        return _D70SequenceCode.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetD70SequenceCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D70SequenceCode = parsed;
    }
    
    // Standard Getter
    public string GetD70SequenceCodeX()
    {
        return _D70SequenceCodeX;
    }
    
    // Standard Setter
    public void SetD70SequenceCodeX(string value)
    {
        _D70SequenceCodeX = value;
    }
    
    // Get<>AsString()
    public string GetD70SequenceCodeXAsString()
    {
        return _D70SequenceCodeX.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD70SequenceCodeXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D70SequenceCodeX = value;
    }
    
    // Standard Getter
    public string GetFiller81()
    {
        return _Filler81;
    }
    
    // Standard Setter
    public void SetFiller81(string value)
    {
        _Filler81 = value;
    }
    
    // Get<>AsString()
    public string GetFiller81AsString()
    {
        return _Filler81.PadRight(33);
    }
    
    // Set<>AsString()
    public void SetFiller81AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler81 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}