using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D79Record Data Structure

public class D79Record
{
    private static int _size = 27;
    // [DEBUG] Class: D79Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D79Key, is_external=, is_static_class=False, static_prefix=
    private D79Key _D79Key = new D79Key();
    
    
    
    
    
    // Serialization methods
    public string GetD79RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D79Key.GetD79KeyAsString());
        
        return result.ToString();
    }
    
    public void SetD79RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 27 <= data.Length)
        {
            _D79Key.SetD79KeyAsString(data.Substring(offset, 27));
        }
        else
        {
            _D79Key.SetD79KeyAsString(data.Substring(offset));
        }
        offset += 27;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD79RecordAsString();
    }
    // Set<>String Override function
    public void SetD79Record(string value)
    {
        SetD79RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D79Key GetD79Key()
    {
        return _D79Key;
    }
    
    // Standard Setter
    public void SetD79Key(D79Key value)
    {
        _D79Key = value;
    }
    
    // Get<>AsString()
    public string GetD79KeyAsString()
    {
        return _D79Key != null ? _D79Key.GetD79KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD79KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D79Key == null)
        {
            _D79Key = new D79Key();
        }
        _D79Key.SetD79KeyAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD79Key(string value)
    {
        _D79Key.SetD79KeyAsString(value);
    }
    // Nested Class: D79Key
    public class D79Key
    {
        private static int _size = 27;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D79Fund, is_external=, is_static_class=False, static_prefix=
        private string _D79Fund ="";
        
        
        
        
        // [DEBUG] Field: D79BargainDate, is_external=, is_static_class=False, static_prefix=
        private string _D79BargainDate ="";
        
        
        
        
        // [DEBUG] Field: D79Sedol, is_external=, is_static_class=False, static_prefix=
        private string _D79Sedol ="";
        
        
        
        
        // [DEBUG] Field: D79ContractNumber, is_external=, is_static_class=False, static_prefix=
        private string _D79ContractNumber ="";
        
        
        
        
    public D79Key() {}
    
    public D79Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD79Fund(data.Substring(offset, 4).Trim());
        offset += 4;
        SetD79BargainDate(data.Substring(offset, 6).Trim());
        offset += 6;
        SetD79Sedol(data.Substring(offset, 7).Trim());
        offset += 7;
        SetD79ContractNumber(data.Substring(offset, 10).Trim());
        offset += 10;
        
    }
    
    // Serialization methods
    public string GetD79KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D79Fund.PadRight(4));
        result.Append(_D79BargainDate.PadRight(6));
        result.Append(_D79Sedol.PadRight(7));
        result.Append(_D79ContractNumber.PadRight(10));
        
        return result.ToString();
    }
    
    public void SetD79KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD79Fund(extracted);
        }
        offset += 4;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetD79BargainDate(extracted);
        }
        offset += 6;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetD79Sedol(extracted);
        }
        offset += 7;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetD79ContractNumber(extracted);
        }
        offset += 10;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD79Fund()
    {
        return _D79Fund;
    }
    
    // Standard Setter
    public void SetD79Fund(string value)
    {
        _D79Fund = value;
    }
    
    // Get<>AsString()
    public string GetD79FundAsString()
    {
        return _D79Fund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD79FundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D79Fund = value;
    }
    
    // Standard Getter
    public string GetD79BargainDate()
    {
        return _D79BargainDate;
    }
    
    // Standard Setter
    public void SetD79BargainDate(string value)
    {
        _D79BargainDate = value;
    }
    
    // Get<>AsString()
    public string GetD79BargainDateAsString()
    {
        return _D79BargainDate.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetD79BargainDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D79BargainDate = value;
    }
    
    // Standard Getter
    public string GetD79Sedol()
    {
        return _D79Sedol;
    }
    
    // Standard Setter
    public void SetD79Sedol(string value)
    {
        _D79Sedol = value;
    }
    
    // Get<>AsString()
    public string GetD79SedolAsString()
    {
        return _D79Sedol.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetD79SedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D79Sedol = value;
    }
    
    // Standard Getter
    public string GetD79ContractNumber()
    {
        return _D79ContractNumber;
    }
    
    // Standard Setter
    public void SetD79ContractNumber(string value)
    {
        _D79ContractNumber = value;
    }
    
    // Get<>AsString()
    public string GetD79ContractNumberAsString()
    {
        return _D79ContractNumber.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetD79ContractNumberAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D79ContractNumber = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}