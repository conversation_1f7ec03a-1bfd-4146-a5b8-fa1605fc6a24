using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WDerivativeFields Data Structure

public class WDerivativeFields
{
    private static int _size = 573;
    // [DEBUG] Class: WDerivativeFields, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WNrOfDays1, is_external=, is_static_class=False, static_prefix=
    private int _WNrOfDays1 =0;
    
    
    
    
    // [DEBUG] Field: WNrOfDays2, is_external=, is_static_class=False, static_prefix=
    private int _WNrOfDays2 =0;
    
    
    
    
    // [DEBUG] Field: WDiffDays, is_external=, is_static_class=False, static_prefix=
    private int _WDiffDays =0;
    
    
    
    
    // [DEBUG] Field: WDrvFlagProRata, is_external=, is_static_class=False, static_prefix=
    private string _WDrvFlagProRata ="N";
    
    
    // 88-level condition checks for WDrvFlagProRata
    public bool IsWDrvFlagProRataN()
    {
        if (this._WDrvFlagProRata == "'N'") return true;
        return false;
    }
    public bool IsWDrvFlagProRataY()
    {
        if (this._WDrvFlagProRata == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WDrvIndex, is_external=, is_static_class=False, static_prefix=
    private int _WDrvIndex =0;
    
    
    
    
    // [DEBUG] Field: WElement, is_external=, is_static_class=False, static_prefix=
    private string _WElement ="";
    
    
    
    
    
    // Serialization methods
    public string GetWDerivativeFieldsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WNrOfDays1.ToString().PadLeft(8, '0'));
        result.Append(_WNrOfDays2.ToString().PadLeft(8, '0'));
        result.Append(_WDiffDays.ToString().PadLeft(0, '0'));
        result.Append(_WDrvFlagProRata.PadRight(1));
        result.Append(_WDrvIndex.ToString().PadLeft(5, '0'));
        result.Append(_WElement.PadRight(551));
        
        return result.ToString();
    }
    
    public void SetWDerivativeFieldsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWNrOfDays1(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWNrOfDays2(parsedInt);
        }
        offset += 8;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWDiffDays(parsedInt);
        }
        offset += 0;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWDrvFlagProRata(extracted);
        }
        offset += 1;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWDrvIndex(parsedInt);
        }
        offset += 5;
        if (offset + 551 <= data.Length)
        {
            string extracted = data.Substring(offset, 551).Trim();
            SetWElement(extracted);
        }
        offset += 551;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWDerivativeFieldsAsString();
    }
    // Set<>String Override function
    public void SetWDerivativeFields(string value)
    {
        SetWDerivativeFieldsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWNrOfDays1()
    {
        return _WNrOfDays1;
    }
    
    // Standard Setter
    public void SetWNrOfDays1(int value)
    {
        _WNrOfDays1 = value;
    }
    
    // Get<>AsString()
    public string GetWNrOfDays1AsString()
    {
        return _WNrOfDays1.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetWNrOfDays1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WNrOfDays1 = parsed;
    }
    
    // Standard Getter
    public int GetWNrOfDays2()
    {
        return _WNrOfDays2;
    }
    
    // Standard Setter
    public void SetWNrOfDays2(int value)
    {
        _WNrOfDays2 = value;
    }
    
    // Get<>AsString()
    public string GetWNrOfDays2AsString()
    {
        return _WNrOfDays2.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetWNrOfDays2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WNrOfDays2 = parsed;
    }
    
    // Standard Getter
    public int GetWDiffDays()
    {
        return _WDiffDays;
    }
    
    // Standard Setter
    public void SetWDiffDays(int value)
    {
        _WDiffDays = value;
    }
    
    // Get<>AsString()
    public string GetWDiffDaysAsString()
    {
        return _WDiffDays.ToString().PadLeft(0, '0');
    }
    
    // Set<>AsString()
    public void SetWDiffDaysAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WDiffDays = parsed;
    }
    
    // Standard Getter
    public string GetWDrvFlagProRata()
    {
        return _WDrvFlagProRata;
    }
    
    // Standard Setter
    public void SetWDrvFlagProRata(string value)
    {
        _WDrvFlagProRata = value;
    }
    
    // Get<>AsString()
    public string GetWDrvFlagProRataAsString()
    {
        return _WDrvFlagProRata.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWDrvFlagProRataAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WDrvFlagProRata = value;
    }
    
    // Standard Getter
    public int GetWDrvIndex()
    {
        return _WDrvIndex;
    }
    
    // Standard Setter
    public void SetWDrvIndex(int value)
    {
        _WDrvIndex = value;
    }
    
    // Get<>AsString()
    public string GetWDrvIndexAsString()
    {
        return _WDrvIndex.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWDrvIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WDrvIndex = parsed;
    }
    
    // Standard Getter
    public string GetWElement()
    {
        return _WElement;
    }
    
    // Standard Setter
    public void SetWElement(string value)
    {
        _WElement = value;
    }
    
    // Get<>AsString()
    public string GetWElementAsString()
    {
        return _WElement.PadRight(551);
    }
    
    // Set<>AsString()
    public void SetWElementAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WElement = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}