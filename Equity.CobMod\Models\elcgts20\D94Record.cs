using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing D94Record Data Structure

public class D94Record
{
    private static int _size = 157;
    // [DEBUG] Class: D94Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D94Key, is_external=, is_static_class=False, static_prefix=
    private D94Key _D94Key = new D94Key();
    
    
    
    
    // [DEBUG] Field: D94RpiBasis, is_external=, is_static_class=False, static_prefix=
    private string _D94RpiBasis ="";
    
    
    
    
    // [DEBUG] Field: D94TransCatId, is_external=, is_static_class=False, static_prefix=
    private int _D94TransCatId =0;
    
    
    
    
    // [DEBUG] Field: D94Holding, is_external=, is_static_class=False, static_prefix=
    private decimal _D94Holding =0;
    
    
    
    
    // [DEBUG] Field: D94Proceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _D94Proceeds =0;
    
    
    
    
    // [DEBUG] Field: D94GainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D94GainLoss =0;
    
    
    
    
    // [DEBUG] Field: D94CgtCost, is_external=, is_static_class=False, static_prefix=
    private decimal _D94CgtCost =0;
    
    
    
    
    // [DEBUG] Field: D94IndexationUsed, is_external=, is_static_class=False, static_prefix=
    private decimal _D94IndexationUsed =0;
    
    
    
    
    // [DEBUG] Field: D94MarketPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _D94MarketPrice =0;
    
    
    
    
    // [DEBUG] Field: D94Issuer, is_external=, is_static_class=False, static_prefix=
    private string _D94Issuer ="";
    
    
    
    
    // [DEBUG] Field: D94Description, is_external=, is_static_class=False, static_prefix=
    private string _D94Description ="";
    
    
    
    
    // [DEBUG] Field: D94BondDisposal, is_external=, is_static_class=False, static_prefix=
    private string _D94BondDisposal ="";
    
    
    
    
    // [DEBUG] Field: D94Sign, is_external=, is_static_class=False, static_prefix=
    private string _D94Sign ="";
    
    
    
    
    
    // Serialization methods
    public string GetD94RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D94Key.GetD94KeyAsString());
        result.Append(_D94RpiBasis.PadRight(0));
        result.Append(_D94TransCatId.ToString().PadLeft(8, '0'));
        result.Append(_D94Holding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D94Proceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D94GainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D94CgtCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D94IndexationUsed.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D94MarketPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D94Issuer.PadRight(35));
        result.Append(_D94Description.PadRight(40));
        result.Append(_D94BondDisposal.PadRight(1));
        result.Append(_D94Sign.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetD94RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 11 <= data.Length)
        {
            _D94Key.SetD94KeyAsString(data.Substring(offset, 11));
        }
        else
        {
            _D94Key.SetD94KeyAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD94RpiBasis(extracted);
        }
        offset += 0;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD94TransCatId(parsedInt);
        }
        offset += 8;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD94Holding(parsedDec);
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD94Proceeds(parsedDec);
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD94GainLoss(parsedDec);
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD94CgtCost(parsedDec);
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD94IndexationUsed(parsedDec);
        }
        offset += 11;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD94MarketPrice(parsedDec);
        }
        offset += 6;
        if (offset + 35 <= data.Length)
        {
            string extracted = data.Substring(offset, 35).Trim();
            SetD94Issuer(extracted);
        }
        offset += 35;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD94Description(extracted);
        }
        offset += 40;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD94BondDisposal(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD94Sign(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD94RecordAsString();
    }
    // Set<>String Override function
    public void SetD94Record(string value)
    {
        SetD94RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D94Key GetD94Key()
    {
        return _D94Key;
    }
    
    // Standard Setter
    public void SetD94Key(D94Key value)
    {
        _D94Key = value;
    }
    
    // Get<>AsString()
    public string GetD94KeyAsString()
    {
        return _D94Key != null ? _D94Key.GetD94KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD94KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D94Key == null)
        {
            _D94Key = new D94Key();
        }
        _D94Key.SetD94KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD94RpiBasis()
    {
        return _D94RpiBasis;
    }
    
    // Standard Setter
    public void SetD94RpiBasis(string value)
    {
        _D94RpiBasis = value;
    }
    
    // Get<>AsString()
    public string GetD94RpiBasisAsString()
    {
        return _D94RpiBasis.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD94RpiBasisAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D94RpiBasis = value;
    }
    
    // Standard Getter
    public int GetD94TransCatId()
    {
        return _D94TransCatId;
    }
    
    // Standard Setter
    public void SetD94TransCatId(int value)
    {
        _D94TransCatId = value;
    }
    
    // Get<>AsString()
    public string GetD94TransCatIdAsString()
    {
        return _D94TransCatId.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetD94TransCatIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D94TransCatId = parsed;
    }
    
    // Standard Getter
    public decimal GetD94Holding()
    {
        return _D94Holding;
    }
    
    // Standard Setter
    public void SetD94Holding(decimal value)
    {
        _D94Holding = value;
    }
    
    // Get<>AsString()
    public string GetD94HoldingAsString()
    {
        return _D94Holding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD94HoldingAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D94Holding = parsed;
    }
    
    // Standard Getter
    public decimal GetD94Proceeds()
    {
        return _D94Proceeds;
    }
    
    // Standard Setter
    public void SetD94Proceeds(decimal value)
    {
        _D94Proceeds = value;
    }
    
    // Get<>AsString()
    public string GetD94ProceedsAsString()
    {
        return _D94Proceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD94ProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D94Proceeds = parsed;
    }
    
    // Standard Getter
    public decimal GetD94GainLoss()
    {
        return _D94GainLoss;
    }
    
    // Standard Setter
    public void SetD94GainLoss(decimal value)
    {
        _D94GainLoss = value;
    }
    
    // Get<>AsString()
    public string GetD94GainLossAsString()
    {
        return _D94GainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD94GainLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D94GainLoss = parsed;
    }
    
    // Standard Getter
    public decimal GetD94CgtCost()
    {
        return _D94CgtCost;
    }
    
    // Standard Setter
    public void SetD94CgtCost(decimal value)
    {
        _D94CgtCost = value;
    }
    
    // Get<>AsString()
    public string GetD94CgtCostAsString()
    {
        return _D94CgtCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD94CgtCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D94CgtCost = parsed;
    }
    
    // Standard Getter
    public decimal GetD94IndexationUsed()
    {
        return _D94IndexationUsed;
    }
    
    // Standard Setter
    public void SetD94IndexationUsed(decimal value)
    {
        _D94IndexationUsed = value;
    }
    
    // Get<>AsString()
    public string GetD94IndexationUsedAsString()
    {
        return _D94IndexationUsed.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD94IndexationUsedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D94IndexationUsed = parsed;
    }
    
    // Standard Getter
    public decimal GetD94MarketPrice()
    {
        return _D94MarketPrice;
    }
    
    // Standard Setter
    public void SetD94MarketPrice(decimal value)
    {
        _D94MarketPrice = value;
    }
    
    // Get<>AsString()
    public string GetD94MarketPriceAsString()
    {
        return _D94MarketPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD94MarketPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D94MarketPrice = parsed;
    }
    
    // Standard Getter
    public string GetD94Issuer()
    {
        return _D94Issuer;
    }
    
    // Standard Setter
    public void SetD94Issuer(string value)
    {
        _D94Issuer = value;
    }
    
    // Get<>AsString()
    public string GetD94IssuerAsString()
    {
        return _D94Issuer.PadRight(35);
    }
    
    // Set<>AsString()
    public void SetD94IssuerAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D94Issuer = value;
    }
    
    // Standard Getter
    public string GetD94Description()
    {
        return _D94Description;
    }
    
    // Standard Setter
    public void SetD94Description(string value)
    {
        _D94Description = value;
    }
    
    // Get<>AsString()
    public string GetD94DescriptionAsString()
    {
        return _D94Description.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD94DescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D94Description = value;
    }
    
    // Standard Getter
    public string GetD94BondDisposal()
    {
        return _D94BondDisposal;
    }
    
    // Standard Setter
    public void SetD94BondDisposal(string value)
    {
        _D94BondDisposal = value;
    }
    
    // Get<>AsString()
    public string GetD94BondDisposalAsString()
    {
        return _D94BondDisposal.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD94BondDisposalAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D94BondDisposal = value;
    }
    
    // Standard Getter
    public string GetD94Sign()
    {
        return _D94Sign;
    }
    
    // Standard Setter
    public void SetD94Sign(string value)
    {
        _D94Sign = value;
    }
    
    // Get<>AsString()
    public string GetD94SignAsString()
    {
        return _D94Sign.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD94SignAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D94Sign = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD94Key(string value)
    {
        _D94Key.SetD94KeyAsString(value);
    }
    // Nested Class: D94Key
    public class D94Key
    {
        private static int _size = 11;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D94CurrencySort, is_external=, is_static_class=False, static_prefix=
        private int _D94CurrencySort =0;
        
        
        // 88-level condition checks for D94CurrencySort
        public bool IsD94CurrencySterling()
        {
            if (this._D94CurrencySort == 0) return true;
            return false;
        }
        public bool IsD94CurrencyEuro()
        {
            if (this._D94CurrencySort == 1) return true;
            return false;
        }
        
        
        // [DEBUG] Field: D94FundCode, is_external=, is_static_class=False, static_prefix=
        private string _D94FundCode ="";
        
        
        
        
        // [DEBUG] Field: D94SectionType, is_external=, is_static_class=False, static_prefix=
        private string _D94SectionType ="";
        
        
        
        
        // [DEBUG] Field: D94BargainDate, is_external=, is_static_class=False, static_prefix=
        private string _D94BargainDate ="";
        
        
        
        
        // [DEBUG] Field: D94SedolNumber, is_external=, is_static_class=False, static_prefix=
        private string _D94SedolNumber ="";
        
        
        
        
        // [DEBUG] Field: D94ContractNumber, is_external=, is_static_class=False, static_prefix=
        private string _D94ContractNumber ="";
        
        
        
        
    public D94Key() {}
    
    public D94Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD94CurrencySort(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetD94FundCode(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD94SectionType(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD94BargainDate(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD94SedolNumber(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD94ContractNumber(data.Substring(offset, 10).Trim());
        offset += 10;
        
    }
    
    // Serialization methods
    public string GetD94KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D94CurrencySort.ToString().PadLeft(1, '0'));
        result.Append(_D94FundCode.PadRight(0));
        result.Append(_D94SectionType.PadRight(0));
        result.Append(_D94BargainDate.PadRight(0));
        result.Append(_D94SedolNumber.PadRight(0));
        result.Append(_D94ContractNumber.PadRight(10));
        
        return result.ToString();
    }
    
    public void SetD94KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD94CurrencySort(parsedInt);
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD94FundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD94SectionType(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD94BargainDate(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD94SedolNumber(extracted);
        }
        offset += 0;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetD94ContractNumber(extracted);
        }
        offset += 10;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD94CurrencySort()
    {
        return _D94CurrencySort;
    }
    
    // Standard Setter
    public void SetD94CurrencySort(int value)
    {
        _D94CurrencySort = value;
    }
    
    // Get<>AsString()
    public string GetD94CurrencySortAsString()
    {
        return _D94CurrencySort.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD94CurrencySortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D94CurrencySort = parsed;
    }
    
    // Standard Getter
    public string GetD94FundCode()
    {
        return _D94FundCode;
    }
    
    // Standard Setter
    public void SetD94FundCode(string value)
    {
        _D94FundCode = value;
    }
    
    // Get<>AsString()
    public string GetD94FundCodeAsString()
    {
        return _D94FundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD94FundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D94FundCode = value;
    }
    
    // Standard Getter
    public string GetD94SectionType()
    {
        return _D94SectionType;
    }
    
    // Standard Setter
    public void SetD94SectionType(string value)
    {
        _D94SectionType = value;
    }
    
    // Get<>AsString()
    public string GetD94SectionTypeAsString()
    {
        return _D94SectionType.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD94SectionTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D94SectionType = value;
    }
    
    // Standard Getter
    public string GetD94BargainDate()
    {
        return _D94BargainDate;
    }
    
    // Standard Setter
    public void SetD94BargainDate(string value)
    {
        _D94BargainDate = value;
    }
    
    // Get<>AsString()
    public string GetD94BargainDateAsString()
    {
        return _D94BargainDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD94BargainDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D94BargainDate = value;
    }
    
    // Standard Getter
    public string GetD94SedolNumber()
    {
        return _D94SedolNumber;
    }
    
    // Standard Setter
    public void SetD94SedolNumber(string value)
    {
        _D94SedolNumber = value;
    }
    
    // Get<>AsString()
    public string GetD94SedolNumberAsString()
    {
        return _D94SedolNumber.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD94SedolNumberAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D94SedolNumber = value;
    }
    
    // Standard Getter
    public string GetD94ContractNumber()
    {
        return _D94ContractNumber;
    }
    
    // Standard Setter
    public void SetD94ContractNumber(string value)
    {
        _D94ContractNumber = value;
    }
    
    // Get<>AsString()
    public string GetD94ContractNumberAsString()
    {
        return _D94ContractNumber.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetD94ContractNumberAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D94ContractNumber = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}
