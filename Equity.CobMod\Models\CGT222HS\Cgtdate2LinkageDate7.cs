using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// DTO class representing Cgtdate2LinkageDate7 Data Structure

public class Cgtdate2LinkageDate7
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate7, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd7 =0;
    
    
    
    
    // [DEBUG] Field: Filler68, is_external=, is_static_class=False, static_prefix=
    private Filler68 _Filler68 = new Filler68();
    
    
    
    
    // [DEBUG] Field: Filler69, is_external=, is_static_class=False, static_prefix=
    private Filler69 _Filler69 = new Filler69();
    
    
    
    
    // [DEBUG] Field: Filler70, is_external=, is_static_class=False, static_prefix=
    private Filler70 _Filler70 = new Filler70();
    
    
    
    
    // [DEBUG] Field: Filler71, is_external=, is_static_class=False, static_prefix=
    private Filler71 _Filler71 = new Filler71();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate7AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd7.ToString().PadLeft(8, '0'));
        result.Append(_Filler68.GetFiller68AsString());
        result.Append(_Filler69.GetFiller69AsString());
        result.Append(_Filler70.GetFiller70AsString());
        result.Append(_Filler71.GetFiller71AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate7AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd7(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler68.SetFiller68AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler68.SetFiller68AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler69.SetFiller69AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler69.SetFiller69AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler70.SetFiller70AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler70.SetFiller70AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler71.SetFiller71AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler71.SetFiller71AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate7AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate7(string value)
    {
        SetCgtdate2LinkageDate7AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd7()
    {
        return _Cgtdate2Ccyymmdd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd7(int value)
    {
        _Cgtdate2Ccyymmdd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd7AsString()
    {
        return _Cgtdate2Ccyymmdd7.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd7 = parsed;
    }
    
    // Standard Getter
    public Filler68 GetFiller68()
    {
        return _Filler68;
    }
    
    // Standard Setter
    public void SetFiller68(Filler68 value)
    {
        _Filler68 = value;
    }
    
    // Get<>AsString()
    public string GetFiller68AsString()
    {
        return _Filler68 != null ? _Filler68.GetFiller68AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller68AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler68 == null)
        {
            _Filler68 = new Filler68();
        }
        _Filler68.SetFiller68AsString(value);
    }
    
    // Standard Getter
    public Filler69 GetFiller69()
    {
        return _Filler69;
    }
    
    // Standard Setter
    public void SetFiller69(Filler69 value)
    {
        _Filler69 = value;
    }
    
    // Get<>AsString()
    public string GetFiller69AsString()
    {
        return _Filler69 != null ? _Filler69.GetFiller69AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller69AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler69 == null)
        {
            _Filler69 = new Filler69();
        }
        _Filler69.SetFiller69AsString(value);
    }
    
    // Standard Getter
    public Filler70 GetFiller70()
    {
        return _Filler70;
    }
    
    // Standard Setter
    public void SetFiller70(Filler70 value)
    {
        _Filler70 = value;
    }
    
    // Get<>AsString()
    public string GetFiller70AsString()
    {
        return _Filler70 != null ? _Filler70.GetFiller70AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller70AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler70 == null)
        {
            _Filler70 = new Filler70();
        }
        _Filler70.SetFiller70AsString(value);
    }
    
    // Standard Getter
    public Filler71 GetFiller71()
    {
        return _Filler71;
    }
    
    // Standard Setter
    public void SetFiller71(Filler71 value)
    {
        _Filler71 = value;
    }
    
    // Get<>AsString()
    public string GetFiller71AsString()
    {
        return _Filler71 != null ? _Filler71.GetFiller71AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller71AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler71 == null)
        {
            _Filler71 = new Filler71();
        }
        _Filler71.SetFiller71AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller68(string value)
    {
        _Filler68.SetFiller68AsString(value);
    }
    // Nested Class: Filler68
    public class Filler68
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc7 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd7, is_external=, is_static_class=False, static_prefix=
        private Filler68.Cgtdate2Yymmdd7 _Cgtdate2Yymmdd7 = new Filler68.Cgtdate2Yymmdd7();
        
        
        
        
    public Filler68() {}
    
    public Filler68(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc7(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(data.Substring(offset, Cgtdate2Yymmdd7.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller68AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc7.PadRight(2));
        result.Append(_Cgtdate2Yymmdd7.GetCgtdate2Yymmdd7AsString());
        
        return result.ToString();
    }
    
    public void SetFiller68AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc7(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc7()
    {
        return _Cgtdate2Cc7;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc7(string value)
    {
        _Cgtdate2Cc7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc7AsString()
    {
        return _Cgtdate2Cc7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc7 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd7 GetCgtdate2Yymmdd7()
    {
        return _Cgtdate2Yymmdd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd7(Cgtdate2Yymmdd7 value)
    {
        _Cgtdate2Yymmdd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd7AsString()
    {
        return _Cgtdate2Yymmdd7 != null ? _Cgtdate2Yymmdd7.GetCgtdate2Yymmdd7AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd7 == null)
        {
            _Cgtdate2Yymmdd7 = new Cgtdate2Yymmdd7();
        }
        _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd7
    public class Cgtdate2Yymmdd7
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy7 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd7, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd7.Cgtdate2Mmdd7 _Cgtdate2Mmdd7 = new Cgtdate2Yymmdd7.Cgtdate2Mmdd7();
        
        
        
        
    public Cgtdate2Yymmdd7() {}
    
    public Cgtdate2Yymmdd7(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy7(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(data.Substring(offset, Cgtdate2Mmdd7.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd7AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy7.PadRight(2));
        result.Append(_Cgtdate2Mmdd7.GetCgtdate2Mmdd7AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd7AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy7(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy7()
    {
        return _Cgtdate2Yy7;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy7(string value)
    {
        _Cgtdate2Yy7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy7AsString()
    {
        return _Cgtdate2Yy7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy7 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd7 GetCgtdate2Mmdd7()
    {
        return _Cgtdate2Mmdd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd7(Cgtdate2Mmdd7 value)
    {
        _Cgtdate2Mmdd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd7AsString()
    {
        return _Cgtdate2Mmdd7 != null ? _Cgtdate2Mmdd7.GetCgtdate2Mmdd7AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd7 == null)
        {
            _Cgtdate2Mmdd7 = new Cgtdate2Mmdd7();
        }
        _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd7
    public class Cgtdate2Mmdd7
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm7 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd7 ="";
        
        
        
        
    public Cgtdate2Mmdd7() {}
    
    public Cgtdate2Mmdd7(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm7(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd7(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd7AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm7.PadRight(2));
        result.Append(_Cgtdate2Dd7.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd7AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm7(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd7(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm7()
    {
        return _Cgtdate2Mm7;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm7(string value)
    {
        _Cgtdate2Mm7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm7AsString()
    {
        return _Cgtdate2Mm7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm7 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd7()
    {
        return _Cgtdate2Dd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd7(string value)
    {
        _Cgtdate2Dd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd7AsString()
    {
        return _Cgtdate2Dd7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd7 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller69(string value)
{
    _Filler69.SetFiller69AsString(value);
}
// Nested Class: Filler69
public class Filler69
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd7 =0;
    
    
    
    
public Filler69() {}

public Filler69(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy7(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd7(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller69AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy7.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd7.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller69AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy7(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd7(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy7()
{
    return _Cgtdate2CCcyy7;
}

// Standard Setter
public void SetCgtdate2CCcyy7(int value)
{
    _Cgtdate2CCcyy7 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy7AsString()
{
    return _Cgtdate2CCcyy7.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy7 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd7()
{
    return _Cgtdate2CMmdd7;
}

// Standard Setter
public void SetCgtdate2CMmdd7(int value)
{
    _Cgtdate2CMmdd7 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd7AsString()
{
    return _Cgtdate2CMmdd7.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd7 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller70(string value)
{
    _Filler70.SetFiller70AsString(value);
}
// Nested Class: Filler70
public class Filler70
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd7 =0;
    
    
    
    
public Filler70() {}

public Filler70(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller70AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc7.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy7.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm7.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd7.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller70AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc7(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy7(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm7(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd7(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc7()
{
    return _Cgtdate2CCc7;
}

// Standard Setter
public void SetCgtdate2CCc7(int value)
{
    _Cgtdate2CCc7 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc7AsString()
{
    return _Cgtdate2CCc7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc7 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy7()
{
    return _Cgtdate2CYy7;
}

// Standard Setter
public void SetCgtdate2CYy7(int value)
{
    _Cgtdate2CYy7 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy7AsString()
{
    return _Cgtdate2CYy7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy7 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm7()
{
    return _Cgtdate2CMm7;
}

// Standard Setter
public void SetCgtdate2CMm7(int value)
{
    _Cgtdate2CMm7 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm7AsString()
{
    return _Cgtdate2CMm7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm7 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd7()
{
    return _Cgtdate2CDd7;
}

// Standard Setter
public void SetCgtdate2CDd7(int value)
{
    _Cgtdate2CDd7 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd7AsString()
{
    return _Cgtdate2CDd7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd7 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller71(string value)
{
    _Filler71.SetFiller71AsString(value);
}
// Nested Class: Filler71
public class Filler71
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm7 =0;
    
    
    
    
    // [DEBUG] Field: Filler72, is_external=, is_static_class=False, static_prefix=
    private string _Filler72 ="";
    
    
    
    
public Filler71() {}

public Filler71(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm7(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller72(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller71AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm7.ToString().PadLeft(6, '0'));
    result.Append(_Filler72.PadRight(2));
    
    return result.ToString();
}

public void SetFiller71AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm7(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller72(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm7()
{
    return _Cgtdate2CCcyymm7;
}

// Standard Setter
public void SetCgtdate2CCcyymm7(int value)
{
    _Cgtdate2CCcyymm7 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm7AsString()
{
    return _Cgtdate2CCcyymm7.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm7 = parsed;
}

// Standard Getter
public string GetFiller72()
{
    return _Filler72;
}

// Standard Setter
public void SetFiller72(string value)
{
    _Filler72 = value;
}

// Get<>AsString()
public string GetFiller72AsString()
{
    return _Filler72.PadRight(2);
}

// Set<>AsString()
public void SetFiller72AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler72 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}