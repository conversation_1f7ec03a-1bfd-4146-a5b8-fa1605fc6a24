using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing ParRecord Data Structure

public class ParRecord
{
    private static int _size = 34;
    // [DEBUG] Class: ParRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: ParFileKey, is_external=, is_static_class=False, static_prefix=
    private ParFileKey _ParFileKey = new ParFileKey();
    
    
    
    
    
    // Serialization methods
    public string GetParRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_ParFileKey.GetParFileKeyAsString());
        
        return result.ToString();
    }
    
    public void SetParRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 34 <= data.Length)
        {
            _ParFileKey.SetParFileKeyAsString(data.Substring(offset, 34));
        }
        else
        {
            _ParFileKey.SetParFileKeyAsString(data.Substring(offset));
        }
        offset += 34;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetParRecordAsString();
    }
    // Set<>String Override function
    public void SetParRecord(string value)
    {
        SetParRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public ParFileKey GetParFileKey()
    {
        return _ParFileKey;
    }
    
    // Standard Setter
    public void SetParFileKey(ParFileKey value)
    {
        _ParFileKey = value;
    }
    
    // Get<>AsString()
    public string GetParFileKeyAsString()
    {
        return _ParFileKey != null ? _ParFileKey.GetParFileKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetParFileKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_ParFileKey == null)
        {
            _ParFileKey = new ParFileKey();
        }
        _ParFileKey.SetParFileKeyAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetParFileKey(string value)
    {
        _ParFileKey.SetParFileKeyAsString(value);
    }
    // Nested Class: ParFileKey
    public class ParFileKey
    {
        private static int _size = 34;
        
        // Fields in the class
        
        
        // [DEBUG] Field: ParCalSedol, is_external=, is_static_class=False, static_prefix=
        private string _ParCalSedol ="";
        
        
        
        
        // [DEBUG] Field: ParDaughterKey, is_external=, is_static_class=False, static_prefix=
        private ParFileKey.ParDaughterKey _ParDaughterKey = new ParFileKey.ParDaughterKey();
        
        
        
        
    public ParFileKey() {}
    
    public ParFileKey(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetParCalSedol(data.Substring(offset, 11).Trim());
        offset += 11;
        _ParDaughterKey.SetParDaughterKeyAsString(data.Substring(offset, ParDaughterKey.GetSize()));
        offset += 23;
        
    }
    
    // Serialization methods
    public string GetParFileKeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_ParCalSedol.PadRight(11));
        result.Append(_ParDaughterKey.GetParDaughterKeyAsString());
        
        return result.ToString();
    }
    
    public void SetParFileKeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetParCalSedol(extracted);
        }
        offset += 11;
        if (offset + 23 <= data.Length)
        {
            _ParDaughterKey.SetParDaughterKeyAsString(data.Substring(offset, 23));
        }
        else
        {
            _ParDaughterKey.SetParDaughterKeyAsString(data.Substring(offset));
        }
        offset += 23;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetParCalSedol()
    {
        return _ParCalSedol;
    }
    
    // Standard Setter
    public void SetParCalSedol(string value)
    {
        _ParCalSedol = value;
    }
    
    // Get<>AsString()
    public string GetParCalSedolAsString()
    {
        return _ParCalSedol.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetParCalSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParCalSedol = value;
    }
    
    // Standard Getter
    public ParDaughterKey GetParDaughterKey()
    {
        return _ParDaughterKey;
    }
    
    // Standard Setter
    public void SetParDaughterKey(ParDaughterKey value)
    {
        _ParDaughterKey = value;
    }
    
    // Get<>AsString()
    public string GetParDaughterKeyAsString()
    {
        return _ParDaughterKey != null ? _ParDaughterKey.GetParDaughterKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetParDaughterKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_ParDaughterKey == null)
        {
            _ParDaughterKey = new ParDaughterKey();
        }
        _ParDaughterKey.SetParDaughterKeyAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: ParDaughterKey
    public class ParDaughterKey
    {
        private static int _size = 23;
        
        // Fields in the class
        
        
        // [DEBUG] Field: ParDCalSedol, is_external=, is_static_class=False, static_prefix=
        private string _ParDCalSedol ="";
        
        
        
        
        // [DEBUG] Field: ParDContract, is_external=, is_static_class=False, static_prefix=
        private string _ParDContract ="";
        
        
        
        
        // [DEBUG] Field: ParDReccode, is_external=, is_static_class=False, static_prefix=
        private string _ParDReccode ="";
        
        
        
        
    public ParDaughterKey() {}
    
    public ParDaughterKey(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetParDCalSedol(data.Substring(offset, 11).Trim());
        offset += 11;
        SetParDContract(data.Substring(offset, 10).Trim());
        offset += 10;
        SetParDReccode(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetParDaughterKeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_ParDCalSedol.PadRight(11));
        result.Append(_ParDContract.PadRight(10));
        result.Append(_ParDReccode.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetParDaughterKeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetParDCalSedol(extracted);
        }
        offset += 11;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetParDContract(extracted);
        }
        offset += 10;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetParDReccode(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetParDCalSedol()
    {
        return _ParDCalSedol;
    }
    
    // Standard Setter
    public void SetParDCalSedol(string value)
    {
        _ParDCalSedol = value;
    }
    
    // Get<>AsString()
    public string GetParDCalSedolAsString()
    {
        return _ParDCalSedol.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetParDCalSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParDCalSedol = value;
    }
    
    // Standard Getter
    public string GetParDContract()
    {
        return _ParDContract;
    }
    
    // Standard Setter
    public void SetParDContract(string value)
    {
        _ParDContract = value;
    }
    
    // Get<>AsString()
    public string GetParDContractAsString()
    {
        return _ParDContract.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetParDContractAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParDContract = value;
    }
    
    // Standard Getter
    public string GetParDReccode()
    {
        return _ParDReccode;
    }
    
    // Standard Setter
    public void SetParDReccode(string value)
    {
        _ParDReccode = value;
    }
    
    // Get<>AsString()
    public string GetParDReccodeAsString()
    {
        return _ParDReccode.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetParDReccodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ParDReccode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}

}}
