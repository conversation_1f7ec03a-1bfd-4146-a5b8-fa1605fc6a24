using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WsIlostReportItems Data Structure

public class WsIlostReportItems
{
    private static int _size = 1545;
    // [DEBUG] Class: WsIlostReportItems, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsIlostHd1, is_external=, is_static_class=False, static_prefix=
    private WsIlostHd1 _WsIlostHd1 = new WsIlostHd1();
    
    
    
    
    // [DEBUG] Field: WsIlostHd2, is_external=, is_static_class=False, static_prefix=
    private WsIlostHd2 _WsIlostHd2 = new WsIlostHd2();
    
    
    
    
    // [DEBUG] Field: WsIlostHd3, is_external=, is_static_class=False, static_prefix=
    private WsIlostHd3 _WsIlostHd3 = new WsIlostHd3();
    
    
    
    
    // [DEBUG] Field: WsIlostHd4, is_external=, is_static_class=False, static_prefix=
    private WsIlostHd4 _WsIlostHd4 = new WsIlostHd4();
    
    
    
    
    // [DEBUG] Field: WsIlostHd5, is_external=, is_static_class=False, static_prefix=
    private WsIlostHd5 _WsIlostHd5 = new WsIlostHd5();
    
    
    
    
    // [DEBUG] Field: WsIlostDtl, is_external=, is_static_class=False, static_prefix=
    private WsIlostDtl _WsIlostDtl = new WsIlostDtl();
    
    
    
    
    // [DEBUG] Field: WsIlostTotLine, is_external=, is_static_class=False, static_prefix=
    private WsIlostTotLine _WsIlostTotLine = new WsIlostTotLine();
    
    
    
    
    // [DEBUG] Field: WsIlostOldFund, is_external=, is_static_class=False, static_prefix=
    private string _WsIlostOldFund ="";
    
    
    
    
    // [DEBUG] Field: WsIlostOldStock, is_external=, is_static_class=False, static_prefix=
    private string _WsIlostOldStock ="";
    
    
    
    
    // [DEBUG] Field: WsIlostOldCompFund, is_external=, is_static_class=False, static_prefix=
    private string _WsIlostOldCompFund ="";
    
    
    
    
    // [DEBUG] Field: WsIlostOldFromDate, is_external=, is_static_class=False, static_prefix=
    private int _WsIlostOldFromDate =0;
    
    
    
    
    // [DEBUG] Field: WsIlostOldToDate, is_external=, is_static_class=False, static_prefix=
    private int _WsIlostOldToDate =0;
    
    
    
    
    // [DEBUG] Field: WsIlostLineCount, is_external=, is_static_class=False, static_prefix=
    private int _WsIlostLineCount =0;
    
    
    
    
    // [DEBUG] Field: WsIlostPageCount, is_external=, is_static_class=False, static_prefix=
    private int _WsIlostPageCount =0;
    
    
    
    
    // [DEBUG] Field: WsIlostNegHoldings, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostNegHoldings =0;
    
    
    
    
    // [DEBUG] Field: WsIlostNegCgt, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostNegCgt =0;
    
    
    
    
    // [DEBUG] Field: WsLostIndexation, is_external=, is_static_class=False, static_prefix=
    private decimal _WsLostIndexation =0;
    
    
    
    
    // [DEBUG] Field: WsIlostIndexation, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostIndexation =0;
    
    
    
    
    // [DEBUG] Field: WsLostTotIndxn, is_external=, is_static_class=False, static_prefix=
    private decimal _WsLostTotIndxn =0;
    
    
    
    
    // [DEBUG] Field: WsIlostFundHoldTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostFundHoldTot =0;
    
    
    
    
    // [DEBUG] Field: WsIlostFundCgtTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostFundCgtTot =0;
    
    
    
    
    // [DEBUG] Field: WsIlostFundProcTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostFundProcTot =0;
    
    
    
    
    // [DEBUG] Field: WsIlostFundGainTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostFundGainTot =0;
    
    
    
    
    // [DEBUG] Field: WsIlostFundIndxTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostFundIndxTot =0;
    
    
    
    
    // [DEBUG] Field: WsIlostFundIlostTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostFundIlostTot =0;
    
    
    
    
    // [DEBUG] Field: WsIlostFundTotIndxnTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostFundTotIndxnTot =0;
    
    
    
    
    // [DEBUG] Field: WsIlostFirstTime, is_external=, is_static_class=False, static_prefix=
    private string _WsIlostFirstTime ="Y";
    
    
    // 88-level condition checks for WsIlostFirstTime
    public bool IsIlostFirstTime()
    {
        if (this._WsIlostFirstTime == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsIlostRecordWritten, is_external=, is_static_class=False, static_prefix=
    private string _WsIlostRecordWritten ="Y";
    
    
    // 88-level condition checks for WsIlostRecordWritten
    public bool IsIlostRecordWritten()
    {
        if (this._WsIlostRecordWritten == "'Y'") return true;
        return false;
    }
    
    
    
    // Serialization methods
    public string GetWsIlostReportItemsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsIlostHd1.GetWsIlostHd1AsString());
        result.Append(_WsIlostHd2.GetWsIlostHd2AsString());
        result.Append(_WsIlostHd3.GetWsIlostHd3AsString());
        result.Append(_WsIlostHd4.GetWsIlostHd4AsString());
        result.Append(_WsIlostHd5.GetWsIlostHd5AsString());
        result.Append(_WsIlostDtl.GetWsIlostDtlAsString());
        result.Append(_WsIlostTotLine.GetWsIlostTotLineAsString());
        result.Append(_WsIlostOldFund.PadRight(4));
        result.Append(_WsIlostOldStock.PadRight(7));
        result.Append(_WsIlostOldCompFund.PadRight(60));
        result.Append(_WsIlostOldFromDate.ToString().PadLeft(6, '0'));
        result.Append(_WsIlostOldToDate.ToString().PadLeft(6, '0'));
        result.Append(_WsIlostLineCount.ToString().PadLeft(4, '0'));
        result.Append(_WsIlostPageCount.ToString().PadLeft(4, '0'));
        result.Append(_WsIlostNegHoldings.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlostNegCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsLostIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlostIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsLostTotIndxn.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlostFundHoldTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlostFundCgtTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlostFundProcTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlostFundGainTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlostFundIndxTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlostFundIlostTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlostFundTotIndxnTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsIlostFirstTime.PadRight(1));
        result.Append(_WsIlostRecordWritten.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWsIlostReportItemsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 109 <= data.Length)
        {
            _WsIlostHd1.SetWsIlostHd1AsString(data.Substring(offset, 109));
        }
        else
        {
            _WsIlostHd1.SetWsIlostHd1AsString(data.Substring(offset));
        }
        offset += 109;
        if (offset + 174 <= data.Length)
        {
            _WsIlostHd2.SetWsIlostHd2AsString(data.Substring(offset, 174));
        }
        else
        {
            _WsIlostHd2.SetWsIlostHd2AsString(data.Substring(offset));
        }
        offset += 174;
        if (offset + 174 <= data.Length)
        {
            _WsIlostHd3.SetWsIlostHd3AsString(data.Substring(offset, 174));
        }
        else
        {
            _WsIlostHd3.SetWsIlostHd3AsString(data.Substring(offset));
        }
        offset += 174;
        if (offset + 174 <= data.Length)
        {
            _WsIlostHd4.SetWsIlostHd4AsString(data.Substring(offset, 174));
        }
        else
        {
            _WsIlostHd4.SetWsIlostHd4AsString(data.Substring(offset));
        }
        offset += 174;
        if (offset + 185 <= data.Length)
        {
            _WsIlostHd5.SetWsIlostHd5AsString(data.Substring(offset, 185));
        }
        else
        {
            _WsIlostHd5.SetWsIlostHd5AsString(data.Substring(offset));
        }
        offset += 185;
        if (offset + 291 <= data.Length)
        {
            _WsIlostDtl.SetWsIlostDtlAsString(data.Substring(offset, 291));
        }
        else
        {
            _WsIlostDtl.SetWsIlostDtlAsString(data.Substring(offset));
        }
        offset += 291;
        if (offset + 141 <= data.Length)
        {
            _WsIlostTotLine.SetWsIlostTotLineAsString(data.Substring(offset, 141));
        }
        else
        {
            _WsIlostTotLine.SetWsIlostTotLineAsString(data.Substring(offset));
        }
        offset += 141;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWsIlostOldFund(extracted);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetWsIlostOldStock(extracted);
        }
        offset += 7;
        if (offset + 60 <= data.Length)
        {
            string extracted = data.Substring(offset, 60).Trim();
            SetWsIlostOldCompFund(extracted);
        }
        offset += 60;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsIlostOldFromDate(parsedInt);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsIlostOldToDate(parsedInt);
        }
        offset += 6;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsIlostLineCount(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsIlostPageCount(parsedInt);
        }
        offset += 4;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlostNegHoldings(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlostNegCgt(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsLostIndexation(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlostIndexation(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsLostTotIndxn(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlostFundHoldTot(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlostFundCgtTot(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlostFundProcTot(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlostFundGainTot(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlostFundIndxTot(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlostFundIlostTot(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsIlostFundTotIndxnTot(parsedDec);
        }
        offset += 17;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsIlostFirstTime(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsIlostRecordWritten(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsIlostReportItemsAsString();
    }
    // Set<>String Override function
    public void SetWsIlostReportItems(string value)
    {
        SetWsIlostReportItemsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public WsIlostHd1 GetWsIlostHd1()
    {
        return _WsIlostHd1;
    }
    
    // Standard Setter
    public void SetWsIlostHd1(WsIlostHd1 value)
    {
        _WsIlostHd1 = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostHd1AsString()
    {
        return _WsIlostHd1 != null ? _WsIlostHd1.GetWsIlostHd1AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsIlostHd1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsIlostHd1 == null)
        {
            _WsIlostHd1 = new WsIlostHd1();
        }
        _WsIlostHd1.SetWsIlostHd1AsString(value);
    }
    
    // Standard Getter
    public WsIlostHd2 GetWsIlostHd2()
    {
        return _WsIlostHd2;
    }
    
    // Standard Setter
    public void SetWsIlostHd2(WsIlostHd2 value)
    {
        _WsIlostHd2 = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostHd2AsString()
    {
        return _WsIlostHd2 != null ? _WsIlostHd2.GetWsIlostHd2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsIlostHd2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsIlostHd2 == null)
        {
            _WsIlostHd2 = new WsIlostHd2();
        }
        _WsIlostHd2.SetWsIlostHd2AsString(value);
    }
    
    // Standard Getter
    public WsIlostHd3 GetWsIlostHd3()
    {
        return _WsIlostHd3;
    }
    
    // Standard Setter
    public void SetWsIlostHd3(WsIlostHd3 value)
    {
        _WsIlostHd3 = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostHd3AsString()
    {
        return _WsIlostHd3 != null ? _WsIlostHd3.GetWsIlostHd3AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsIlostHd3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsIlostHd3 == null)
        {
            _WsIlostHd3 = new WsIlostHd3();
        }
        _WsIlostHd3.SetWsIlostHd3AsString(value);
    }
    
    // Standard Getter
    public WsIlostHd4 GetWsIlostHd4()
    {
        return _WsIlostHd4;
    }
    
    // Standard Setter
    public void SetWsIlostHd4(WsIlostHd4 value)
    {
        _WsIlostHd4 = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostHd4AsString()
    {
        return _WsIlostHd4 != null ? _WsIlostHd4.GetWsIlostHd4AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsIlostHd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsIlostHd4 == null)
        {
            _WsIlostHd4 = new WsIlostHd4();
        }
        _WsIlostHd4.SetWsIlostHd4AsString(value);
    }
    
    // Standard Getter
    public WsIlostHd5 GetWsIlostHd5()
    {
        return _WsIlostHd5;
    }
    
    // Standard Setter
    public void SetWsIlostHd5(WsIlostHd5 value)
    {
        _WsIlostHd5 = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostHd5AsString()
    {
        return _WsIlostHd5 != null ? _WsIlostHd5.GetWsIlostHd5AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsIlostHd5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsIlostHd5 == null)
        {
            _WsIlostHd5 = new WsIlostHd5();
        }
        _WsIlostHd5.SetWsIlostHd5AsString(value);
    }
    
    // Standard Getter
    public WsIlostDtl GetWsIlostDtl()
    {
        return _WsIlostDtl;
    }
    
    // Standard Setter
    public void SetWsIlostDtl(WsIlostDtl value)
    {
        _WsIlostDtl = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostDtlAsString()
    {
        return _WsIlostDtl != null ? _WsIlostDtl.GetWsIlostDtlAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsIlostDtlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsIlostDtl == null)
        {
            _WsIlostDtl = new WsIlostDtl();
        }
        _WsIlostDtl.SetWsIlostDtlAsString(value);
    }
    
    // Standard Getter
    public WsIlostTotLine GetWsIlostTotLine()
    {
        return _WsIlostTotLine;
    }
    
    // Standard Setter
    public void SetWsIlostTotLine(WsIlostTotLine value)
    {
        _WsIlostTotLine = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostTotLineAsString()
    {
        return _WsIlostTotLine != null ? _WsIlostTotLine.GetWsIlostTotLineAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsIlostTotLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsIlostTotLine == null)
        {
            _WsIlostTotLine = new WsIlostTotLine();
        }
        _WsIlostTotLine.SetWsIlostTotLineAsString(value);
    }
    
    // Standard Getter
    public string GetWsIlostOldFund()
    {
        return _WsIlostOldFund;
    }
    
    // Standard Setter
    public void SetWsIlostOldFund(string value)
    {
        _WsIlostOldFund = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostOldFundAsString()
    {
        return _WsIlostOldFund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWsIlostOldFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsIlostOldFund = value;
    }
    
    // Standard Getter
    public string GetWsIlostOldStock()
    {
        return _WsIlostOldStock;
    }
    
    // Standard Setter
    public void SetWsIlostOldStock(string value)
    {
        _WsIlostOldStock = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostOldStockAsString()
    {
        return _WsIlostOldStock.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetWsIlostOldStockAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsIlostOldStock = value;
    }
    
    // Standard Getter
    public string GetWsIlostOldCompFund()
    {
        return _WsIlostOldCompFund;
    }
    
    // Standard Setter
    public void SetWsIlostOldCompFund(string value)
    {
        _WsIlostOldCompFund = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostOldCompFundAsString()
    {
        return _WsIlostOldCompFund.PadRight(60);
    }
    
    // Set<>AsString()
    public void SetWsIlostOldCompFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsIlostOldCompFund = value;
    }
    
    // Standard Getter
    public int GetWsIlostOldFromDate()
    {
        return _WsIlostOldFromDate;
    }
    
    // Standard Setter
    public void SetWsIlostOldFromDate(int value)
    {
        _WsIlostOldFromDate = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostOldFromDateAsString()
    {
        return _WsIlostOldFromDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetWsIlostOldFromDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsIlostOldFromDate = parsed;
    }
    
    // Standard Getter
    public int GetWsIlostOldToDate()
    {
        return _WsIlostOldToDate;
    }
    
    // Standard Setter
    public void SetWsIlostOldToDate(int value)
    {
        _WsIlostOldToDate = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostOldToDateAsString()
    {
        return _WsIlostOldToDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetWsIlostOldToDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsIlostOldToDate = parsed;
    }
    
    // Standard Getter
    public int GetWsIlostLineCount()
    {
        return _WsIlostLineCount;
    }
    
    // Standard Setter
    public void SetWsIlostLineCount(int value)
    {
        _WsIlostLineCount = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostLineCountAsString()
    {
        return _WsIlostLineCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsIlostLineCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsIlostLineCount = parsed;
    }
    
    // Standard Getter
    public int GetWsIlostPageCount()
    {
        return _WsIlostPageCount;
    }
    
    // Standard Setter
    public void SetWsIlostPageCount(int value)
    {
        _WsIlostPageCount = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostPageCountAsString()
    {
        return _WsIlostPageCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsIlostPageCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsIlostPageCount = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlostNegHoldings()
    {
        return _WsIlostNegHoldings;
    }
    
    // Standard Setter
    public void SetWsIlostNegHoldings(decimal value)
    {
        _WsIlostNegHoldings = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostNegHoldingsAsString()
    {
        return _WsIlostNegHoldings.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlostNegHoldingsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostNegHoldings = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlostNegCgt()
    {
        return _WsIlostNegCgt;
    }
    
    // Standard Setter
    public void SetWsIlostNegCgt(decimal value)
    {
        _WsIlostNegCgt = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostNegCgtAsString()
    {
        return _WsIlostNegCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlostNegCgtAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostNegCgt = parsed;
    }
    
    // Standard Getter
    public decimal GetWsLostIndexation()
    {
        return _WsLostIndexation;
    }
    
    // Standard Setter
    public void SetWsLostIndexation(decimal value)
    {
        _WsLostIndexation = value;
    }
    
    // Get<>AsString()
    public string GetWsLostIndexationAsString()
    {
        return _WsLostIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsLostIndexationAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsLostIndexation = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlostIndexation()
    {
        return _WsIlostIndexation;
    }
    
    // Standard Setter
    public void SetWsIlostIndexation(decimal value)
    {
        _WsIlostIndexation = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostIndexationAsString()
    {
        return _WsIlostIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlostIndexationAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostIndexation = parsed;
    }
    
    // Standard Getter
    public decimal GetWsLostTotIndxn()
    {
        return _WsLostTotIndxn;
    }
    
    // Standard Setter
    public void SetWsLostTotIndxn(decimal value)
    {
        _WsLostTotIndxn = value;
    }
    
    // Get<>AsString()
    public string GetWsLostTotIndxnAsString()
    {
        return _WsLostTotIndxn.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsLostTotIndxnAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsLostTotIndxn = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlostFundHoldTot()
    {
        return _WsIlostFundHoldTot;
    }
    
    // Standard Setter
    public void SetWsIlostFundHoldTot(decimal value)
    {
        _WsIlostFundHoldTot = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostFundHoldTotAsString()
    {
        return _WsIlostFundHoldTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlostFundHoldTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostFundHoldTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlostFundCgtTot()
    {
        return _WsIlostFundCgtTot;
    }
    
    // Standard Setter
    public void SetWsIlostFundCgtTot(decimal value)
    {
        _WsIlostFundCgtTot = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostFundCgtTotAsString()
    {
        return _WsIlostFundCgtTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlostFundCgtTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostFundCgtTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlostFundProcTot()
    {
        return _WsIlostFundProcTot;
    }
    
    // Standard Setter
    public void SetWsIlostFundProcTot(decimal value)
    {
        _WsIlostFundProcTot = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostFundProcTotAsString()
    {
        return _WsIlostFundProcTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlostFundProcTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostFundProcTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlostFundGainTot()
    {
        return _WsIlostFundGainTot;
    }
    
    // Standard Setter
    public void SetWsIlostFundGainTot(decimal value)
    {
        _WsIlostFundGainTot = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostFundGainTotAsString()
    {
        return _WsIlostFundGainTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlostFundGainTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostFundGainTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlostFundIndxTot()
    {
        return _WsIlostFundIndxTot;
    }
    
    // Standard Setter
    public void SetWsIlostFundIndxTot(decimal value)
    {
        _WsIlostFundIndxTot = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostFundIndxTotAsString()
    {
        return _WsIlostFundIndxTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlostFundIndxTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostFundIndxTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlostFundIlostTot()
    {
        return _WsIlostFundIlostTot;
    }
    
    // Standard Setter
    public void SetWsIlostFundIlostTot(decimal value)
    {
        _WsIlostFundIlostTot = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostFundIlostTotAsString()
    {
        return _WsIlostFundIlostTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlostFundIlostTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostFundIlostTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsIlostFundTotIndxnTot()
    {
        return _WsIlostFundTotIndxnTot;
    }
    
    // Standard Setter
    public void SetWsIlostFundTotIndxnTot(decimal value)
    {
        _WsIlostFundTotIndxnTot = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostFundTotIndxnTotAsString()
    {
        return _WsIlostFundTotIndxnTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsIlostFundTotIndxnTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostFundTotIndxnTot = parsed;
    }
    
    // Standard Getter
    public string GetWsIlostFirstTime()
    {
        return _WsIlostFirstTime;
    }
    
    // Standard Setter
    public void SetWsIlostFirstTime(string value)
    {
        _WsIlostFirstTime = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostFirstTimeAsString()
    {
        return _WsIlostFirstTime.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsIlostFirstTimeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsIlostFirstTime = value;
    }
    
    // Standard Getter
    public string GetWsIlostRecordWritten()
    {
        return _WsIlostRecordWritten;
    }
    
    // Standard Setter
    public void SetWsIlostRecordWritten(string value)
    {
        _WsIlostRecordWritten = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostRecordWrittenAsString()
    {
        return _WsIlostRecordWritten.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsIlostRecordWrittenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsIlostRecordWritten = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWsIlostHd1(string value)
    {
        _WsIlostHd1.SetWsIlostHd1AsString(value);
    }
    // Nested Class: WsIlostHd1
    public class WsIlostHd1
    {
        private static int _size = 109;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WsIlostCompName, is_external=, is_static_class=False, static_prefix=
        private string _WsIlostCompName ="";
        
        
        
        
        // [DEBUG] Field: Filler245, is_external=, is_static_class=False, static_prefix=
        private string _Filler245 ="Schedule of Realised Indexation Disallowed";
        
        
        
        
    public WsIlostHd1() {}
    
    public WsIlostHd1(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWsIlostCompName(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller245(data.Substring(offset, 109).Trim());
        offset += 109;
        
    }
    
    // Serialization methods
    public string GetWsIlostHd1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsIlostCompName.PadRight(0));
        result.Append(_Filler245.PadRight(109));
        
        return result.ToString();
    }
    
    public void SetWsIlostHd1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWsIlostCompName(extracted);
        }
        offset += 0;
        if (offset + 109 <= data.Length)
        {
            string extracted = data.Substring(offset, 109).Trim();
            SetFiller245(extracted);
        }
        offset += 109;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsIlostCompName()
    {
        return _WsIlostCompName;
    }
    
    // Standard Setter
    public void SetWsIlostCompName(string value)
    {
        _WsIlostCompName = value;
    }
    
    // Get<>AsString()
    public string GetWsIlostCompNameAsString()
    {
        return _WsIlostCompName.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWsIlostCompNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsIlostCompName = value;
    }
    
    // Standard Getter
    public string GetFiller245()
    {
        return _Filler245;
    }
    
    // Standard Setter
    public void SetFiller245(string value)
    {
        _Filler245 = value;
    }
    
    // Get<>AsString()
    public string GetFiller245AsString()
    {
        return _Filler245.PadRight(109);
    }
    
    // Set<>AsString()
    public void SetFiller245AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler245 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetWsIlostHd2(string value)
{
    _WsIlostHd2.SetWsIlostHd2AsString(value);
}
// Nested Class: WsIlostHd2
public class WsIlostHd2
{
    private static int _size = 174;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler246, is_external=, is_static_class=False, static_prefix=
    private string _Filler246 ="";
    
    
    
    
    // [DEBUG] Field: Filler247, is_external=, is_static_class=False, static_prefix=
    private string _Filler247 ="Fund";
    
    
    
    
    // [DEBUG] Field: Filler248, is_external=, is_static_class=False, static_prefix=
    private string _Filler248 ="Period";
    
    
    
    
    // [DEBUG] Field: Filler249, is_external=, is_static_class=False, static_prefix=
    private string _Filler249 ="Page";
    
    
    
    
public WsIlostHd2() {}

public WsIlostHd2(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller246(data.Substring(offset, 140).Trim());
    offset += 140;
    SetFiller247(data.Substring(offset, 15).Trim());
    offset += 15;
    SetFiller248(data.Substring(offset, 15).Trim());
    offset += 15;
    SetFiller249(data.Substring(offset, 4).Trim());
    offset += 4;
    
}

// Serialization methods
public string GetWsIlostHd2AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler246.PadRight(140));
    result.Append(_Filler247.PadRight(15));
    result.Append(_Filler248.PadRight(15));
    result.Append(_Filler249.PadRight(4));
    
    return result.ToString();
}

public void SetWsIlostHd2AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 140 <= data.Length)
    {
        string extracted = data.Substring(offset, 140).Trim();
        SetFiller246(extracted);
    }
    offset += 140;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetFiller247(extracted);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetFiller248(extracted);
    }
    offset += 15;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetFiller249(extracted);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller246()
{
    return _Filler246;
}

// Standard Setter
public void SetFiller246(string value)
{
    _Filler246 = value;
}

// Get<>AsString()
public string GetFiller246AsString()
{
    return _Filler246.PadRight(140);
}

// Set<>AsString()
public void SetFiller246AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler246 = value;
}

// Standard Getter
public string GetFiller247()
{
    return _Filler247;
}

// Standard Setter
public void SetFiller247(string value)
{
    _Filler247 = value;
}

// Get<>AsString()
public string GetFiller247AsString()
{
    return _Filler247.PadRight(15);
}

// Set<>AsString()
public void SetFiller247AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler247 = value;
}

// Standard Getter
public string GetFiller248()
{
    return _Filler248;
}

// Standard Setter
public void SetFiller248(string value)
{
    _Filler248 = value;
}

// Get<>AsString()
public string GetFiller248AsString()
{
    return _Filler248.PadRight(15);
}

// Set<>AsString()
public void SetFiller248AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler248 = value;
}

// Standard Getter
public string GetFiller249()
{
    return _Filler249;
}

// Standard Setter
public void SetFiller249(string value)
{
    _Filler249 = value;
}

// Get<>AsString()
public string GetFiller249AsString()
{
    return _Filler249.PadRight(4);
}

// Set<>AsString()
public void SetFiller249AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler249 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsIlostHd3(string value)
{
    _WsIlostHd3.SetWsIlostHd3AsString(value);
}
// Nested Class: WsIlostHd3
public class WsIlostHd3
{
    private static int _size = 174;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler250, is_external=, is_static_class=False, static_prefix=
    private string _Filler250 ="";
    
    
    
    
    // [DEBUG] Field: WsIlostCompFund, is_external=, is_static_class=False, static_prefix=
    private string _WsIlostCompFund ="";
    
    
    
    
    // [DEBUG] Field: Filler251, is_external=, is_static_class=False, static_prefix=
    private string _Filler251 =" ";
    
    
    
    
    // [DEBUG] Field: WsIlostFund, is_external=, is_static_class=False, static_prefix=
    private string _WsIlostFund ="";
    
    
    
    
    // [DEBUG] Field: Filler252, is_external=, is_static_class=False, static_prefix=
    private string _Filler252 =" ";
    
    
    
    
    // [DEBUG] Field: WsIlostPeriod, is_external=, is_static_class=False, static_prefix=
    private WsIlostHd3.WsIlostPeriod _WsIlostPeriod = new WsIlostHd3.WsIlostPeriod();
    
    
    
    
    // [DEBUG] Field: Filler254, is_external=, is_static_class=False, static_prefix=
    private string _Filler254 =" ";
    
    
    
    
    // [DEBUG] Field: WsIlostPageNo, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostPageNo =0;
    
    
    
    
public WsIlostHd3() {}

public WsIlostHd3(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller250(data.Substring(offset, 56).Trim());
    offset += 56;
    SetWsIlostCompFund(data.Substring(offset, 60).Trim());
    offset += 60;
    SetFiller251(data.Substring(offset, 24).Trim());
    offset += 24;
    SetWsIlostFund(data.Substring(offset, 4).Trim());
    offset += 4;
    SetFiller252(data.Substring(offset, 7).Trim());
    offset += 7;
    _WsIlostPeriod.SetWsIlostPeriodAsString(data.Substring(offset, WsIlostPeriod.GetSize()));
    offset += 15;
    SetFiller254(data.Substring(offset, 4).Trim());
    offset += 4;
    SetWsIlostPageNo(PackedDecimalConverter.ToDecimal(data.Substring(offset, 4)));
    offset += 4;
    
}

// Serialization methods
public string GetWsIlostHd3AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler250.PadRight(56));
    result.Append(_WsIlostCompFund.PadRight(60));
    result.Append(_Filler251.PadRight(24));
    result.Append(_WsIlostFund.PadRight(4));
    result.Append(_Filler252.PadRight(7));
    result.Append(_WsIlostPeriod.GetWsIlostPeriodAsString());
    result.Append(_Filler254.PadRight(4));
    result.Append(_WsIlostPageNo.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetWsIlostHd3AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 56 <= data.Length)
    {
        string extracted = data.Substring(offset, 56).Trim();
        SetFiller250(extracted);
    }
    offset += 56;
    if (offset + 60 <= data.Length)
    {
        string extracted = data.Substring(offset, 60).Trim();
        SetWsIlostCompFund(extracted);
    }
    offset += 60;
    if (offset + 24 <= data.Length)
    {
        string extracted = data.Substring(offset, 24).Trim();
        SetFiller251(extracted);
    }
    offset += 24;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWsIlostFund(extracted);
    }
    offset += 4;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetFiller252(extracted);
    }
    offset += 7;
    if (offset + 15 <= data.Length)
    {
        _WsIlostPeriod.SetWsIlostPeriodAsString(data.Substring(offset, 15));
    }
    else
    {
        _WsIlostPeriod.SetWsIlostPeriodAsString(data.Substring(offset));
    }
    offset += 15;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetFiller254(extracted);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlostPageNo(parsedDec);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller250()
{
    return _Filler250;
}

// Standard Setter
public void SetFiller250(string value)
{
    _Filler250 = value;
}

// Get<>AsString()
public string GetFiller250AsString()
{
    return _Filler250.PadRight(56);
}

// Set<>AsString()
public void SetFiller250AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler250 = value;
}

// Standard Getter
public string GetWsIlostCompFund()
{
    return _WsIlostCompFund;
}

// Standard Setter
public void SetWsIlostCompFund(string value)
{
    _WsIlostCompFund = value;
}

// Get<>AsString()
public string GetWsIlostCompFundAsString()
{
    return _WsIlostCompFund.PadRight(60);
}

// Set<>AsString()
public void SetWsIlostCompFundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlostCompFund = value;
}

// Standard Getter
public string GetFiller251()
{
    return _Filler251;
}

// Standard Setter
public void SetFiller251(string value)
{
    _Filler251 = value;
}

// Get<>AsString()
public string GetFiller251AsString()
{
    return _Filler251.PadRight(24);
}

// Set<>AsString()
public void SetFiller251AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler251 = value;
}

// Standard Getter
public string GetWsIlostFund()
{
    return _WsIlostFund;
}

// Standard Setter
public void SetWsIlostFund(string value)
{
    _WsIlostFund = value;
}

// Get<>AsString()
public string GetWsIlostFundAsString()
{
    return _WsIlostFund.PadRight(4);
}

// Set<>AsString()
public void SetWsIlostFundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlostFund = value;
}

// Standard Getter
public string GetFiller252()
{
    return _Filler252;
}

// Standard Setter
public void SetFiller252(string value)
{
    _Filler252 = value;
}

// Get<>AsString()
public string GetFiller252AsString()
{
    return _Filler252.PadRight(7);
}

// Set<>AsString()
public void SetFiller252AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler252 = value;
}

// Standard Getter
public WsIlostPeriod GetWsIlostPeriod()
{
    return _WsIlostPeriod;
}

// Standard Setter
public void SetWsIlostPeriod(WsIlostPeriod value)
{
    _WsIlostPeriod = value;
}

// Get<>AsString()
public string GetWsIlostPeriodAsString()
{
    return _WsIlostPeriod != null ? _WsIlostPeriod.GetWsIlostPeriodAsString() : "";
}

// Set<>AsString()
public void SetWsIlostPeriodAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsIlostPeriod == null)
    {
        _WsIlostPeriod = new WsIlostPeriod();
    }
    _WsIlostPeriod.SetWsIlostPeriodAsString(value);
}

// Standard Getter
public string GetFiller254()
{
    return _Filler254;
}

// Standard Setter
public void SetFiller254(string value)
{
    _Filler254 = value;
}

// Get<>AsString()
public string GetFiller254AsString()
{
    return _Filler254.PadRight(4);
}

// Set<>AsString()
public void SetFiller254AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler254 = value;
}

// Standard Getter
public decimal GetWsIlostPageNo()
{
    return _WsIlostPageNo;
}

// Standard Setter
public void SetWsIlostPageNo(decimal value)
{
    _WsIlostPageNo = value;
}

// Get<>AsString()
public string GetWsIlostPageNoAsString()
{
    return _WsIlostPageNo.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlostPageNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostPageNo = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WsIlostPeriod
public class WsIlostPeriod
{
    private static int _size = 15;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsIlostFromDate, is_external=, is_static_class=False, static_prefix=
    private string _WsIlostFromDate ="";
    
    
    
    
    // [DEBUG] Field: Filler253, is_external=, is_static_class=False, static_prefix=
    private string _Filler253 ="-";
    
    
    
    
    // [DEBUG] Field: WsIlostToDate, is_external=, is_static_class=False, static_prefix=
    private string _WsIlostToDate ="";
    
    
    
    
public WsIlostPeriod() {}

public WsIlostPeriod(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsIlostFromDate(data.Substring(offset, 7).Trim());
    offset += 7;
    SetFiller253(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlostToDate(data.Substring(offset, 7).Trim());
    offset += 7;
    
}

// Serialization methods
public string GetWsIlostPeriodAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsIlostFromDate.PadRight(7));
    result.Append(_Filler253.PadRight(1));
    result.Append(_WsIlostToDate.PadRight(7));
    
    return result.ToString();
}

public void SetWsIlostPeriodAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetWsIlostFromDate(extracted);
    }
    offset += 7;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller253(extracted);
    }
    offset += 1;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetWsIlostToDate(extracted);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetWsIlostFromDate()
{
    return _WsIlostFromDate;
}

// Standard Setter
public void SetWsIlostFromDate(string value)
{
    _WsIlostFromDate = value;
}

// Get<>AsString()
public string GetWsIlostFromDateAsString()
{
    return _WsIlostFromDate.PadRight(7);
}

// Set<>AsString()
public void SetWsIlostFromDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlostFromDate = value;
}

// Standard Getter
public string GetFiller253()
{
    return _Filler253;
}

// Standard Setter
public void SetFiller253(string value)
{
    _Filler253 = value;
}

// Get<>AsString()
public string GetFiller253AsString()
{
    return _Filler253.PadRight(1);
}

// Set<>AsString()
public void SetFiller253AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler253 = value;
}

// Standard Getter
public string GetWsIlostToDate()
{
    return _WsIlostToDate;
}

// Standard Setter
public void SetWsIlostToDate(string value)
{
    _WsIlostToDate = value;
}

// Get<>AsString()
public string GetWsIlostToDateAsString()
{
    return _WsIlostToDate.PadRight(7);
}

// Set<>AsString()
public void SetWsIlostToDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlostToDate = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetWsIlostHd4(string value)
{
    _WsIlostHd4.SetWsIlostHd4AsString(value);
}
// Nested Class: WsIlostHd4
public class WsIlostHd4
{
    private static int _size = 174;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler255, is_external=, is_static_class=False, static_prefix=
    private string _Filler255 ="Security Code";
    
    
    
    
    // [DEBUG] Field: Filler256, is_external=, is_static_class=False, static_prefix=
    private string _Filler256 ="Type ";
    
    
    
    
    // [DEBUG] Field: Filler257, is_external=, is_static_class=False, static_prefix=
    private string _Filler257 ="Bargain  ";
    
    
    
    
    // [DEBUG] Field: Filler258, is_external=, is_static_class=False, static_prefix=
    private string _Filler258 ="     Holdings";
    
    
    
    
    // [DEBUG] Field: Filler259, is_external=, is_static_class=False, static_prefix=
    private string _Filler259 ="      \"CGT\"";
    
    
    
    
    // [DEBUG] Field: Filler260, is_external=, is_static_class=False, static_prefix=
    private string _Filler260 ="  Proceeds of";
    
    
    
    
    // [DEBUG] Field: Filler261, is_external=, is_static_class=False, static_prefix=
    private string _Filler261 ="   Capital";
    
    
    
    
    // [DEBUG] Field: Filler262, is_external=, is_static_class=False, static_prefix=
    private string _Filler262 ="Indexation";
    
    
    
    
    // [DEBUG] Field: Filler263, is_external=, is_static_class=False, static_prefix=
    private string _Filler263 ="Indexation";
    
    
    
    
    // [DEBUG] Field: Filler264, is_external=, is_static_class=False, static_prefix=
    private string _Filler264 ="     Total";
    
    
    
    
public WsIlostHd4() {}

public WsIlostHd4(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller255(data.Substring(offset, 15).Trim());
    offset += 15;
    SetFiller256(data.Substring(offset, 6).Trim());
    offset += 6;
    SetFiller257(data.Substring(offset, 14).Trim());
    offset += 14;
    SetFiller258(data.Substring(offset, 23).Trim());
    offset += 23;
    SetFiller259(data.Substring(offset, 19).Trim());
    offset += 19;
    SetFiller260(data.Substring(offset, 24).Trim());
    offset += 24;
    SetFiller261(data.Substring(offset, 21).Trim());
    offset += 21;
    SetFiller262(data.Substring(offset, 21).Trim());
    offset += 21;
    SetFiller263(data.Substring(offset, 21).Trim());
    offset += 21;
    SetFiller264(data.Substring(offset, 10).Trim());
    offset += 10;
    
}

// Serialization methods
public string GetWsIlostHd4AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler255.PadRight(15));
    result.Append(_Filler256.PadRight(6));
    result.Append(_Filler257.PadRight(14));
    result.Append(_Filler258.PadRight(23));
    result.Append(_Filler259.PadRight(19));
    result.Append(_Filler260.PadRight(24));
    result.Append(_Filler261.PadRight(21));
    result.Append(_Filler262.PadRight(21));
    result.Append(_Filler263.PadRight(21));
    result.Append(_Filler264.PadRight(10));
    
    return result.ToString();
}

public void SetWsIlostHd4AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetFiller255(extracted);
    }
    offset += 15;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetFiller256(extracted);
    }
    offset += 6;
    if (offset + 14 <= data.Length)
    {
        string extracted = data.Substring(offset, 14).Trim();
        SetFiller257(extracted);
    }
    offset += 14;
    if (offset + 23 <= data.Length)
    {
        string extracted = data.Substring(offset, 23).Trim();
        SetFiller258(extracted);
    }
    offset += 23;
    if (offset + 19 <= data.Length)
    {
        string extracted = data.Substring(offset, 19).Trim();
        SetFiller259(extracted);
    }
    offset += 19;
    if (offset + 24 <= data.Length)
    {
        string extracted = data.Substring(offset, 24).Trim();
        SetFiller260(extracted);
    }
    offset += 24;
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller261(extracted);
    }
    offset += 21;
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller262(extracted);
    }
    offset += 21;
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller263(extracted);
    }
    offset += 21;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetFiller264(extracted);
    }
    offset += 10;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller255()
{
    return _Filler255;
}

// Standard Setter
public void SetFiller255(string value)
{
    _Filler255 = value;
}

// Get<>AsString()
public string GetFiller255AsString()
{
    return _Filler255.PadRight(15);
}

// Set<>AsString()
public void SetFiller255AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler255 = value;
}

// Standard Getter
public string GetFiller256()
{
    return _Filler256;
}

// Standard Setter
public void SetFiller256(string value)
{
    _Filler256 = value;
}

// Get<>AsString()
public string GetFiller256AsString()
{
    return _Filler256.PadRight(6);
}

// Set<>AsString()
public void SetFiller256AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler256 = value;
}

// Standard Getter
public string GetFiller257()
{
    return _Filler257;
}

// Standard Setter
public void SetFiller257(string value)
{
    _Filler257 = value;
}

// Get<>AsString()
public string GetFiller257AsString()
{
    return _Filler257.PadRight(14);
}

// Set<>AsString()
public void SetFiller257AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler257 = value;
}

// Standard Getter
public string GetFiller258()
{
    return _Filler258;
}

// Standard Setter
public void SetFiller258(string value)
{
    _Filler258 = value;
}

// Get<>AsString()
public string GetFiller258AsString()
{
    return _Filler258.PadRight(23);
}

// Set<>AsString()
public void SetFiller258AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler258 = value;
}

// Standard Getter
public string GetFiller259()
{
    return _Filler259;
}

// Standard Setter
public void SetFiller259(string value)
{
    _Filler259 = value;
}

// Get<>AsString()
public string GetFiller259AsString()
{
    return _Filler259.PadRight(19);
}

// Set<>AsString()
public void SetFiller259AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler259 = value;
}

// Standard Getter
public string GetFiller260()
{
    return _Filler260;
}

// Standard Setter
public void SetFiller260(string value)
{
    _Filler260 = value;
}

// Get<>AsString()
public string GetFiller260AsString()
{
    return _Filler260.PadRight(24);
}

// Set<>AsString()
public void SetFiller260AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler260 = value;
}

// Standard Getter
public string GetFiller261()
{
    return _Filler261;
}

// Standard Setter
public void SetFiller261(string value)
{
    _Filler261 = value;
}

// Get<>AsString()
public string GetFiller261AsString()
{
    return _Filler261.PadRight(21);
}

// Set<>AsString()
public void SetFiller261AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler261 = value;
}

// Standard Getter
public string GetFiller262()
{
    return _Filler262;
}

// Standard Setter
public void SetFiller262(string value)
{
    _Filler262 = value;
}

// Get<>AsString()
public string GetFiller262AsString()
{
    return _Filler262.PadRight(21);
}

// Set<>AsString()
public void SetFiller262AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler262 = value;
}

// Standard Getter
public string GetFiller263()
{
    return _Filler263;
}

// Standard Setter
public void SetFiller263(string value)
{
    _Filler263 = value;
}

// Get<>AsString()
public string GetFiller263AsString()
{
    return _Filler263.PadRight(21);
}

// Set<>AsString()
public void SetFiller263AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler263 = value;
}

// Standard Getter
public string GetFiller264()
{
    return _Filler264;
}

// Standard Setter
public void SetFiller264(string value)
{
    _Filler264 = value;
}

// Get<>AsString()
public string GetFiller264AsString()
{
    return _Filler264.PadRight(10);
}

// Set<>AsString()
public void SetFiller264AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler264 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsIlostHd5(string value)
{
    _WsIlostHd5.SetWsIlostHd5AsString(value);
}
// Nested Class: WsIlostHd5
public class WsIlostHd5
{
    private static int _size = 185;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler265, is_external=, is_static_class=False, static_prefix=
    private string _Filler265 =" ";
    
    
    
    
    // [DEBUG] Field: Filler266, is_external=, is_static_class=False, static_prefix=
    private string _Filler266 =" Date  ";
    
    
    
    
    // [DEBUG] Field: Filler267, is_external=, is_static_class=False, static_prefix=
    private string _Filler267 ="     Disposed";
    
    
    
    
    // [DEBUG] Field: Filler268, is_external=, is_static_class=False, static_prefix=
    private string _Filler268 ="      Costs";
    
    
    
    
    // [DEBUG] Field: Filler269, is_external=, is_static_class=False, static_prefix=
    private string _Filler269 ="     Disposal ";
    
    
    
    
    // [DEBUG] Field: Filler270, is_external=, is_static_class=False, static_prefix=
    private string _Filler270 =" Gain/Loss";
    
    
    
    
    // [DEBUG] Field: Filler271, is_external=, is_static_class=False, static_prefix=
    private string _Filler271 ="      Used";
    
    
    
    
    // [DEBUG] Field: Filler272, is_external=, is_static_class=False, static_prefix=
    private string _Filler272 ="Disallowed";
    
    
    
    
    // [DEBUG] Field: Filler273, is_external=, is_static_class=False, static_prefix=
    private string _Filler273 ="Indexation";
    
    
    
    
public WsIlostHd5() {}

public WsIlostHd5(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller265(data.Substring(offset, 21).Trim());
    offset += 21;
    SetFiller266(data.Substring(offset, 14).Trim());
    offset += 14;
    SetFiller267(data.Substring(offset, 23).Trim());
    offset += 23;
    SetFiller268(data.Substring(offset, 19).Trim());
    offset += 19;
    SetFiller269(data.Substring(offset, 24).Trim());
    offset += 24;
    SetFiller270(data.Substring(offset, 21).Trim());
    offset += 21;
    SetFiller271(data.Substring(offset, 21).Trim());
    offset += 21;
    SetFiller272(data.Substring(offset, 21).Trim());
    offset += 21;
    SetFiller273(data.Substring(offset, 21).Trim());
    offset += 21;
    
}

// Serialization methods
public string GetWsIlostHd5AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler265.PadRight(21));
    result.Append(_Filler266.PadRight(14));
    result.Append(_Filler267.PadRight(23));
    result.Append(_Filler268.PadRight(19));
    result.Append(_Filler269.PadRight(24));
    result.Append(_Filler270.PadRight(21));
    result.Append(_Filler271.PadRight(21));
    result.Append(_Filler272.PadRight(21));
    result.Append(_Filler273.PadRight(21));
    
    return result.ToString();
}

public void SetWsIlostHd5AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller265(extracted);
    }
    offset += 21;
    if (offset + 14 <= data.Length)
    {
        string extracted = data.Substring(offset, 14).Trim();
        SetFiller266(extracted);
    }
    offset += 14;
    if (offset + 23 <= data.Length)
    {
        string extracted = data.Substring(offset, 23).Trim();
        SetFiller267(extracted);
    }
    offset += 23;
    if (offset + 19 <= data.Length)
    {
        string extracted = data.Substring(offset, 19).Trim();
        SetFiller268(extracted);
    }
    offset += 19;
    if (offset + 24 <= data.Length)
    {
        string extracted = data.Substring(offset, 24).Trim();
        SetFiller269(extracted);
    }
    offset += 24;
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller270(extracted);
    }
    offset += 21;
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller271(extracted);
    }
    offset += 21;
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller272(extracted);
    }
    offset += 21;
    if (offset + 21 <= data.Length)
    {
        string extracted = data.Substring(offset, 21).Trim();
        SetFiller273(extracted);
    }
    offset += 21;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller265()
{
    return _Filler265;
}

// Standard Setter
public void SetFiller265(string value)
{
    _Filler265 = value;
}

// Get<>AsString()
public string GetFiller265AsString()
{
    return _Filler265.PadRight(21);
}

// Set<>AsString()
public void SetFiller265AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler265 = value;
}

// Standard Getter
public string GetFiller266()
{
    return _Filler266;
}

// Standard Setter
public void SetFiller266(string value)
{
    _Filler266 = value;
}

// Get<>AsString()
public string GetFiller266AsString()
{
    return _Filler266.PadRight(14);
}

// Set<>AsString()
public void SetFiller266AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler266 = value;
}

// Standard Getter
public string GetFiller267()
{
    return _Filler267;
}

// Standard Setter
public void SetFiller267(string value)
{
    _Filler267 = value;
}

// Get<>AsString()
public string GetFiller267AsString()
{
    return _Filler267.PadRight(23);
}

// Set<>AsString()
public void SetFiller267AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler267 = value;
}

// Standard Getter
public string GetFiller268()
{
    return _Filler268;
}

// Standard Setter
public void SetFiller268(string value)
{
    _Filler268 = value;
}

// Get<>AsString()
public string GetFiller268AsString()
{
    return _Filler268.PadRight(19);
}

// Set<>AsString()
public void SetFiller268AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler268 = value;
}

// Standard Getter
public string GetFiller269()
{
    return _Filler269;
}

// Standard Setter
public void SetFiller269(string value)
{
    _Filler269 = value;
}

// Get<>AsString()
public string GetFiller269AsString()
{
    return _Filler269.PadRight(24);
}

// Set<>AsString()
public void SetFiller269AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler269 = value;
}

// Standard Getter
public string GetFiller270()
{
    return _Filler270;
}

// Standard Setter
public void SetFiller270(string value)
{
    _Filler270 = value;
}

// Get<>AsString()
public string GetFiller270AsString()
{
    return _Filler270.PadRight(21);
}

// Set<>AsString()
public void SetFiller270AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler270 = value;
}

// Standard Getter
public string GetFiller271()
{
    return _Filler271;
}

// Standard Setter
public void SetFiller271(string value)
{
    _Filler271 = value;
}

// Get<>AsString()
public string GetFiller271AsString()
{
    return _Filler271.PadRight(21);
}

// Set<>AsString()
public void SetFiller271AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler271 = value;
}

// Standard Getter
public string GetFiller272()
{
    return _Filler272;
}

// Standard Setter
public void SetFiller272(string value)
{
    _Filler272 = value;
}

// Get<>AsString()
public string GetFiller272AsString()
{
    return _Filler272.PadRight(21);
}

// Set<>AsString()
public void SetFiller272AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler272 = value;
}

// Standard Getter
public string GetFiller273()
{
    return _Filler273;
}

// Standard Setter
public void SetFiller273(string value)
{
    _Filler273 = value;
}

// Get<>AsString()
public string GetFiller273AsString()
{
    return _Filler273.PadRight(21);
}

// Set<>AsString()
public void SetFiller273AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler273 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsIlostDtl(string value)
{
    _WsIlostDtl.SetWsIlostDtlAsString(value);
}
// Nested Class: WsIlostDtl
public class WsIlostDtl
{
    private static int _size = 291;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsIlostSedol, is_external=, is_static_class=False, static_prefix=
    private string _WsIlostSedol ="";
    
    
    
    
    // [DEBUG] Field: WsIlostDetail1, is_external=, is_static_class=False, static_prefix=
    private WsIlostDtl.WsIlostDetail1 _WsIlostDetail1 = new WsIlostDtl.WsIlostDetail1();
    
    
    
    
    // [DEBUG] Field: WsIlostDetail2, is_external=, is_static_class=False, static_prefix=
    private WsIlostDtl.WsIlostDetail2 _WsIlostDetail2 = new WsIlostDtl.WsIlostDetail2();
    
    
    
    
public WsIlostDtl() {}

public WsIlostDtl(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsIlostSedol(data.Substring(offset, 10).Trim());
    offset += 10;
    _WsIlostDetail1.SetWsIlostDetail1AsString(data.Substring(offset, WsIlostDetail1.GetSize()));
    offset += 144;
    _WsIlostDetail2.SetWsIlostDetail2AsString(data.Substring(offset, WsIlostDetail2.GetSize()));
    offset += 137;
    
}

// Serialization methods
public string GetWsIlostDtlAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsIlostSedol.PadRight(10));
    result.Append(_WsIlostDetail1.GetWsIlostDetail1AsString());
    result.Append(_WsIlostDetail2.GetWsIlostDetail2AsString());
    
    return result.ToString();
}

public void SetWsIlostDtlAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetWsIlostSedol(extracted);
    }
    offset += 10;
    if (offset + 144 <= data.Length)
    {
        _WsIlostDetail1.SetWsIlostDetail1AsString(data.Substring(offset, 144));
    }
    else
    {
        _WsIlostDetail1.SetWsIlostDetail1AsString(data.Substring(offset));
    }
    offset += 144;
    if (offset + 137 <= data.Length)
    {
        _WsIlostDetail2.SetWsIlostDetail2AsString(data.Substring(offset, 137));
    }
    else
    {
        _WsIlostDetail2.SetWsIlostDetail2AsString(data.Substring(offset));
    }
    offset += 137;
}

// Getter and Setter methods

// Standard Getter
public string GetWsIlostSedol()
{
    return _WsIlostSedol;
}

// Standard Setter
public void SetWsIlostSedol(string value)
{
    _WsIlostSedol = value;
}

// Get<>AsString()
public string GetWsIlostSedolAsString()
{
    return _WsIlostSedol.PadRight(10);
}

// Set<>AsString()
public void SetWsIlostSedolAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlostSedol = value;
}

// Standard Getter
public WsIlostDetail1 GetWsIlostDetail1()
{
    return _WsIlostDetail1;
}

// Standard Setter
public void SetWsIlostDetail1(WsIlostDetail1 value)
{
    _WsIlostDetail1 = value;
}

// Get<>AsString()
public string GetWsIlostDetail1AsString()
{
    return _WsIlostDetail1 != null ? _WsIlostDetail1.GetWsIlostDetail1AsString() : "";
}

// Set<>AsString()
public void SetWsIlostDetail1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsIlostDetail1 == null)
    {
        _WsIlostDetail1 = new WsIlostDetail1();
    }
    _WsIlostDetail1.SetWsIlostDetail1AsString(value);
}

// Standard Getter
public WsIlostDetail2 GetWsIlostDetail2()
{
    return _WsIlostDetail2;
}

// Standard Setter
public void SetWsIlostDetail2(WsIlostDetail2 value)
{
    _WsIlostDetail2 = value;
}

// Get<>AsString()
public string GetWsIlostDetail2AsString()
{
    return _WsIlostDetail2 != null ? _WsIlostDetail2.GetWsIlostDetail2AsString() : "";
}

// Set<>AsString()
public void SetWsIlostDetail2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsIlostDetail2 == null)
    {
        _WsIlostDetail2 = new WsIlostDetail2();
    }
    _WsIlostDetail2.SetWsIlostDetail2AsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WsIlostDetail1
public class WsIlostDetail1
{
    private static int _size = 144;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsIlostSecurity, is_external=, is_static_class=False, static_prefix=
    private string _WsIlostSecurity ="";
    
    
    
    
    // [DEBUG] Field: WsIlostIssuer, is_external=, is_static_class=False, static_prefix=
    private string _WsIlostIssuer ="";
    
    
    
    
    // [DEBUG] Field: Filler274, is_external=, is_static_class=False, static_prefix=
    private string _Filler274 ="";
    
    
    
    
    // [DEBUG] Field: WsIlostDescription, is_external=, is_static_class=False, static_prefix=
    private string _WsIlostDescription ="";
    
    
    
    
    // [DEBUG] Field: Filler275, is_external=, is_static_class=False, static_prefix=
    private string _Filler275 ="";
    
    
    
    
public WsIlostDetail1() {}

public WsIlostDetail1(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsIlostSecurity(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWsIlostIssuer(data.Substring(offset, 35).Trim());
    offset += 35;
    SetFiller274(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlostDescription(data.Substring(offset, 35).Trim());
    offset += 35;
    SetFiller275(data.Substring(offset, 71).Trim());
    offset += 71;
    
}

// Serialization methods
public string GetWsIlostDetail1AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsIlostSecurity.PadRight(2));
    result.Append(_WsIlostIssuer.PadRight(35));
    result.Append(_Filler274.PadRight(1));
    result.Append(_WsIlostDescription.PadRight(35));
    result.Append(_Filler275.PadRight(71));
    
    return result.ToString();
}

public void SetWsIlostDetail1AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWsIlostSecurity(extracted);
    }
    offset += 2;
    if (offset + 35 <= data.Length)
    {
        string extracted = data.Substring(offset, 35).Trim();
        SetWsIlostIssuer(extracted);
    }
    offset += 35;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller274(extracted);
    }
    offset += 1;
    if (offset + 35 <= data.Length)
    {
        string extracted = data.Substring(offset, 35).Trim();
        SetWsIlostDescription(extracted);
    }
    offset += 35;
    if (offset + 71 <= data.Length)
    {
        string extracted = data.Substring(offset, 71).Trim();
        SetFiller275(extracted);
    }
    offset += 71;
}

// Getter and Setter methods

// Standard Getter
public string GetWsIlostSecurity()
{
    return _WsIlostSecurity;
}

// Standard Setter
public void SetWsIlostSecurity(string value)
{
    _WsIlostSecurity = value;
}

// Get<>AsString()
public string GetWsIlostSecurityAsString()
{
    return _WsIlostSecurity.PadRight(2);
}

// Set<>AsString()
public void SetWsIlostSecurityAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlostSecurity = value;
}

// Standard Getter
public string GetWsIlostIssuer()
{
    return _WsIlostIssuer;
}

// Standard Setter
public void SetWsIlostIssuer(string value)
{
    _WsIlostIssuer = value;
}

// Get<>AsString()
public string GetWsIlostIssuerAsString()
{
    return _WsIlostIssuer.PadRight(35);
}

// Set<>AsString()
public void SetWsIlostIssuerAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlostIssuer = value;
}

// Standard Getter
public string GetFiller274()
{
    return _Filler274;
}

// Standard Setter
public void SetFiller274(string value)
{
    _Filler274 = value;
}

// Get<>AsString()
public string GetFiller274AsString()
{
    return _Filler274.PadRight(1);
}

// Set<>AsString()
public void SetFiller274AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler274 = value;
}

// Standard Getter
public string GetWsIlostDescription()
{
    return _WsIlostDescription;
}

// Standard Setter
public void SetWsIlostDescription(string value)
{
    _WsIlostDescription = value;
}

// Get<>AsString()
public string GetWsIlostDescriptionAsString()
{
    return _WsIlostDescription.PadRight(35);
}

// Set<>AsString()
public void SetWsIlostDescriptionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlostDescription = value;
}

// Standard Getter
public string GetFiller275()
{
    return _Filler275;
}

// Standard Setter
public void SetFiller275(string value)
{
    _Filler275 = value;
}

// Get<>AsString()
public string GetFiller275AsString()
{
    return _Filler275.PadRight(71);
}

// Set<>AsString()
public void SetFiller275AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler275 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WsIlostDetail2
public class WsIlostDetail2
{
    private static int _size = 137;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler276, is_external=, is_static_class=False, static_prefix=
    private string _Filler276 ="";
    
    
    
    
    // [DEBUG] Field: WsIlostType, is_external=, is_static_class=False, static_prefix=
    private string _WsIlostType ="";
    
    
    
    
    // [DEBUG] Field: WsIlostBargDate, is_external=, is_static_class=False, static_prefix=
    private string _WsIlostBargDate ="";
    
    
    
    
    // [DEBUG] Field: WsIlostHoldings, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostHoldings =0;
    
    
    
    
    // [DEBUG] Field: Filler277, is_external=, is_static_class=False, static_prefix=
    private string _Filler277 ="";
    
    
    
    
    // [DEBUG] Field: WsIlostCgt, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostCgt =0;
    
    
    
    
    // [DEBUG] Field: Filler278, is_external=, is_static_class=False, static_prefix=
    private string _Filler278 ="";
    
    
    
    
    // [DEBUG] Field: WsIlostProc, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostProc =0;
    
    
    
    
    // [DEBUG] Field: Filler279, is_external=, is_static_class=False, static_prefix=
    private string _Filler279 ="";
    
    
    
    
    // [DEBUG] Field: WsIlostGain, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostGain =0;
    
    
    
    
    // [DEBUG] Field: Filler280, is_external=, is_static_class=False, static_prefix=
    private string _Filler280 ="";
    
    
    
    
    // [DEBUG] Field: WsIlostIndx, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostIndx =0;
    
    
    
    
    // [DEBUG] Field: Filler281, is_external=, is_static_class=False, static_prefix=
    private string _Filler281 ="";
    
    
    
    
    // [DEBUG] Field: WsIlost, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlost =0;
    
    
    
    
    // [DEBUG] Field: Filler282, is_external=, is_static_class=False, static_prefix=
    private string _Filler282 ="";
    
    
    
    
    // [DEBUG] Field: WsIlostTotIndxn, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostTotIndxn =0;
    
    
    
    
public WsIlostDetail2() {}

public WsIlostDetail2(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller276(data.Substring(offset, 6).Trim());
    offset += 6;
    SetWsIlostType(data.Substring(offset, 5).Trim());
    offset += 5;
    SetWsIlostBargDate(data.Substring(offset, 8).Trim());
    offset += 8;
    SetWsIlostHoldings(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller277(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlostCgt(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller278(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlostProc(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller279(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlostGain(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller280(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlostIndx(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller281(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller282(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlostTotIndxn(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    
}

// Serialization methods
public string GetWsIlostDetail2AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler276.PadRight(6));
    result.Append(_WsIlostType.PadRight(5));
    result.Append(_WsIlostBargDate.PadRight(8));
    result.Append(_WsIlostHoldings.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler277.PadRight(1));
    result.Append(_WsIlostCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler278.PadRight(1));
    result.Append(_WsIlostProc.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler279.PadRight(1));
    result.Append(_WsIlostGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler280.PadRight(1));
    result.Append(_WsIlostIndx.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler281.PadRight(1));
    result.Append(_WsIlost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler282.PadRight(1));
    result.Append(_WsIlostTotIndxn.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetWsIlostDetail2AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetFiller276(extracted);
    }
    offset += 6;
    if (offset + 5 <= data.Length)
    {
        string extracted = data.Substring(offset, 5).Trim();
        SetWsIlostType(extracted);
    }
    offset += 5;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWsIlostBargDate(extracted);
    }
    offset += 8;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlostHoldings(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller277(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlostCgt(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller278(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlostProc(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller279(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlostGain(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller280(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlostIndx(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller281(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlost(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller282(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlostTotIndxn(parsedDec);
    }
    offset += 16;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller276()
{
    return _Filler276;
}

// Standard Setter
public void SetFiller276(string value)
{
    _Filler276 = value;
}

// Get<>AsString()
public string GetFiller276AsString()
{
    return _Filler276.PadRight(6);
}

// Set<>AsString()
public void SetFiller276AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler276 = value;
}

// Standard Getter
public string GetWsIlostType()
{
    return _WsIlostType;
}

// Standard Setter
public void SetWsIlostType(string value)
{
    _WsIlostType = value;
}

// Get<>AsString()
public string GetWsIlostTypeAsString()
{
    return _WsIlostType.PadRight(5);
}

// Set<>AsString()
public void SetWsIlostTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlostType = value;
}

// Standard Getter
public string GetWsIlostBargDate()
{
    return _WsIlostBargDate;
}

// Standard Setter
public void SetWsIlostBargDate(string value)
{
    _WsIlostBargDate = value;
}

// Get<>AsString()
public string GetWsIlostBargDateAsString()
{
    return _WsIlostBargDate.PadRight(8);
}

// Set<>AsString()
public void SetWsIlostBargDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsIlostBargDate = value;
}

// Standard Getter
public decimal GetWsIlostHoldings()
{
    return _WsIlostHoldings;
}

// Standard Setter
public void SetWsIlostHoldings(decimal value)
{
    _WsIlostHoldings = value;
}

// Get<>AsString()
public string GetWsIlostHoldingsAsString()
{
    return _WsIlostHoldings.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlostHoldingsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostHoldings = parsed;
}

// Standard Getter
public string GetFiller277()
{
    return _Filler277;
}

// Standard Setter
public void SetFiller277(string value)
{
    _Filler277 = value;
}

// Get<>AsString()
public string GetFiller277AsString()
{
    return _Filler277.PadRight(1);
}

// Set<>AsString()
public void SetFiller277AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler277 = value;
}

// Standard Getter
public decimal GetWsIlostCgt()
{
    return _WsIlostCgt;
}

// Standard Setter
public void SetWsIlostCgt(decimal value)
{
    _WsIlostCgt = value;
}

// Get<>AsString()
public string GetWsIlostCgtAsString()
{
    return _WsIlostCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlostCgtAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostCgt = parsed;
}

// Standard Getter
public string GetFiller278()
{
    return _Filler278;
}

// Standard Setter
public void SetFiller278(string value)
{
    _Filler278 = value;
}

// Get<>AsString()
public string GetFiller278AsString()
{
    return _Filler278.PadRight(1);
}

// Set<>AsString()
public void SetFiller278AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler278 = value;
}

// Standard Getter
public decimal GetWsIlostProc()
{
    return _WsIlostProc;
}

// Standard Setter
public void SetWsIlostProc(decimal value)
{
    _WsIlostProc = value;
}

// Get<>AsString()
public string GetWsIlostProcAsString()
{
    return _WsIlostProc.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlostProcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostProc = parsed;
}

// Standard Getter
public string GetFiller279()
{
    return _Filler279;
}

// Standard Setter
public void SetFiller279(string value)
{
    _Filler279 = value;
}

// Get<>AsString()
public string GetFiller279AsString()
{
    return _Filler279.PadRight(1);
}

// Set<>AsString()
public void SetFiller279AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler279 = value;
}

// Standard Getter
public decimal GetWsIlostGain()
{
    return _WsIlostGain;
}

// Standard Setter
public void SetWsIlostGain(decimal value)
{
    _WsIlostGain = value;
}

// Get<>AsString()
public string GetWsIlostGainAsString()
{
    return _WsIlostGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlostGainAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostGain = parsed;
}

// Standard Getter
public string GetFiller280()
{
    return _Filler280;
}

// Standard Setter
public void SetFiller280(string value)
{
    _Filler280 = value;
}

// Get<>AsString()
public string GetFiller280AsString()
{
    return _Filler280.PadRight(1);
}

// Set<>AsString()
public void SetFiller280AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler280 = value;
}

// Standard Getter
public decimal GetWsIlostIndx()
{
    return _WsIlostIndx;
}

// Standard Setter
public void SetWsIlostIndx(decimal value)
{
    _WsIlostIndx = value;
}

// Get<>AsString()
public string GetWsIlostIndxAsString()
{
    return _WsIlostIndx.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlostIndxAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostIndx = parsed;
}

// Standard Getter
public string GetFiller281()
{
    return _Filler281;
}

// Standard Setter
public void SetFiller281(string value)
{
    _Filler281 = value;
}

// Get<>AsString()
public string GetFiller281AsString()
{
    return _Filler281.PadRight(1);
}

// Set<>AsString()
public void SetFiller281AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler281 = value;
}

// Standard Getter
public decimal GetWsIlost()
{
    return _WsIlost;
}

// Standard Setter
public void SetWsIlost(decimal value)
{
    _WsIlost = value;
}

// Get<>AsString()
public string GetWsIlostAsString()
{
    return _WsIlost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlostAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlost = parsed;
}

// Standard Getter
public string GetFiller282()
{
    return _Filler282;
}

// Standard Setter
public void SetFiller282(string value)
{
    _Filler282 = value;
}

// Get<>AsString()
public string GetFiller282AsString()
{
    return _Filler282.PadRight(1);
}

// Set<>AsString()
public void SetFiller282AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler282 = value;
}

// Standard Getter
public decimal GetWsIlostTotIndxn()
{
    return _WsIlostTotIndxn;
}

// Standard Setter
public void SetWsIlostTotIndxn(decimal value)
{
    _WsIlostTotIndxn = value;
}

// Get<>AsString()
public string GetWsIlostTotIndxnAsString()
{
    return _WsIlostTotIndxn.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlostTotIndxnAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostTotIndxn = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetWsIlostTotLine(string value)
{
    _WsIlostTotLine.SetWsIlostTotLineAsString(value);
}
// Nested Class: WsIlostTotLine
public class WsIlostTotLine
{
    private static int _size = 141;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler283, is_external=, is_static_class=False, static_prefix=
    private string _Filler283 ="";
    
    
    
    
    // [DEBUG] Field: Filler284, is_external=, is_static_class=False, static_prefix=
    private string _Filler284 ="TOTAL ";
    
    
    
    
    // [DEBUG] Field: WsIlostHoldTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostHoldTot =0;
    
    
    
    
    // [DEBUG] Field: Filler285, is_external=, is_static_class=False, static_prefix=
    private string _Filler285 ="";
    
    
    
    
    // [DEBUG] Field: WsIlostCgtTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostCgtTot =0;
    
    
    
    
    // [DEBUG] Field: Filler286, is_external=, is_static_class=False, static_prefix=
    private string _Filler286 ="";
    
    
    
    
    // [DEBUG] Field: WsIlostProcTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostProcTot =0;
    
    
    
    
    // [DEBUG] Field: Filler287, is_external=, is_static_class=False, static_prefix=
    private string _Filler287 ="";
    
    
    
    
    // [DEBUG] Field: WsIlostGainTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostGainTot =0;
    
    
    
    
    // [DEBUG] Field: Filler288, is_external=, is_static_class=False, static_prefix=
    private string _Filler288 ="";
    
    
    
    
    // [DEBUG] Field: WsIlostIndxTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostIndxTot =0;
    
    
    
    
    // [DEBUG] Field: Filler289, is_external=, is_static_class=False, static_prefix=
    private string _Filler289 ="";
    
    
    
    
    // [DEBUG] Field: WsIlostTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostTot =0;
    
    
    
    
    // [DEBUG] Field: Filler290, is_external=, is_static_class=False, static_prefix=
    private string _Filler290 ="";
    
    
    
    
    // [DEBUG] Field: WsIlostTotIndxnTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsIlostTotIndxnTot =0;
    
    
    
    
public WsIlostTotLine() {}

public WsIlostTotLine(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller283(data.Substring(offset, 23).Trim());
    offset += 23;
    SetFiller284(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWsIlostHoldTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller285(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlostCgtTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller286(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlostProcTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller287(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlostGainTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller288(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlostIndxTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller289(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlostTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    SetFiller290(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsIlostTotIndxnTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 16)));
    offset += 16;
    
}

// Serialization methods
public string GetWsIlostTotLineAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler283.PadRight(23));
    result.Append(_Filler284.PadRight(0));
    result.Append(_WsIlostHoldTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler285.PadRight(1));
    result.Append(_WsIlostCgtTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler286.PadRight(1));
    result.Append(_WsIlostProcTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler287.PadRight(1));
    result.Append(_WsIlostGainTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler288.PadRight(1));
    result.Append(_WsIlostIndxTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler289.PadRight(1));
    result.Append(_WsIlostTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler290.PadRight(1));
    result.Append(_WsIlostTotIndxnTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetWsIlostTotLineAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 23 <= data.Length)
    {
        string extracted = data.Substring(offset, 23).Trim();
        SetFiller283(extracted);
    }
    offset += 23;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller284(extracted);
    }
    offset += 0;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlostHoldTot(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller285(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlostCgtTot(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller286(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlostProcTot(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller287(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlostGainTot(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller288(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlostIndxTot(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller289(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlostTot(parsedDec);
    }
    offset += 16;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller290(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsIlostTotIndxnTot(parsedDec);
    }
    offset += 16;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller283()
{
    return _Filler283;
}

// Standard Setter
public void SetFiller283(string value)
{
    _Filler283 = value;
}

// Get<>AsString()
public string GetFiller283AsString()
{
    return _Filler283.PadRight(23);
}

// Set<>AsString()
public void SetFiller283AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler283 = value;
}

// Standard Getter
public string GetFiller284()
{
    return _Filler284;
}

// Standard Setter
public void SetFiller284(string value)
{
    _Filler284 = value;
}

// Get<>AsString()
public string GetFiller284AsString()
{
    return _Filler284.PadRight(0);
}

// Set<>AsString()
public void SetFiller284AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler284 = value;
}

// Standard Getter
public decimal GetWsIlostHoldTot()
{
    return _WsIlostHoldTot;
}

// Standard Setter
public void SetWsIlostHoldTot(decimal value)
{
    _WsIlostHoldTot = value;
}

// Get<>AsString()
public string GetWsIlostHoldTotAsString()
{
    return _WsIlostHoldTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlostHoldTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostHoldTot = parsed;
}

// Standard Getter
public string GetFiller285()
{
    return _Filler285;
}

// Standard Setter
public void SetFiller285(string value)
{
    _Filler285 = value;
}

// Get<>AsString()
public string GetFiller285AsString()
{
    return _Filler285.PadRight(1);
}

// Set<>AsString()
public void SetFiller285AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler285 = value;
}

// Standard Getter
public decimal GetWsIlostCgtTot()
{
    return _WsIlostCgtTot;
}

// Standard Setter
public void SetWsIlostCgtTot(decimal value)
{
    _WsIlostCgtTot = value;
}

// Get<>AsString()
public string GetWsIlostCgtTotAsString()
{
    return _WsIlostCgtTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlostCgtTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostCgtTot = parsed;
}

// Standard Getter
public string GetFiller286()
{
    return _Filler286;
}

// Standard Setter
public void SetFiller286(string value)
{
    _Filler286 = value;
}

// Get<>AsString()
public string GetFiller286AsString()
{
    return _Filler286.PadRight(1);
}

// Set<>AsString()
public void SetFiller286AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler286 = value;
}

// Standard Getter
public decimal GetWsIlostProcTot()
{
    return _WsIlostProcTot;
}

// Standard Setter
public void SetWsIlostProcTot(decimal value)
{
    _WsIlostProcTot = value;
}

// Get<>AsString()
public string GetWsIlostProcTotAsString()
{
    return _WsIlostProcTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlostProcTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostProcTot = parsed;
}

// Standard Getter
public string GetFiller287()
{
    return _Filler287;
}

// Standard Setter
public void SetFiller287(string value)
{
    _Filler287 = value;
}

// Get<>AsString()
public string GetFiller287AsString()
{
    return _Filler287.PadRight(1);
}

// Set<>AsString()
public void SetFiller287AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler287 = value;
}

// Standard Getter
public decimal GetWsIlostGainTot()
{
    return _WsIlostGainTot;
}

// Standard Setter
public void SetWsIlostGainTot(decimal value)
{
    _WsIlostGainTot = value;
}

// Get<>AsString()
public string GetWsIlostGainTotAsString()
{
    return _WsIlostGainTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlostGainTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostGainTot = parsed;
}

// Standard Getter
public string GetFiller288()
{
    return _Filler288;
}

// Standard Setter
public void SetFiller288(string value)
{
    _Filler288 = value;
}

// Get<>AsString()
public string GetFiller288AsString()
{
    return _Filler288.PadRight(1);
}

// Set<>AsString()
public void SetFiller288AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler288 = value;
}

// Standard Getter
public decimal GetWsIlostIndxTot()
{
    return _WsIlostIndxTot;
}

// Standard Setter
public void SetWsIlostIndxTot(decimal value)
{
    _WsIlostIndxTot = value;
}

// Get<>AsString()
public string GetWsIlostIndxTotAsString()
{
    return _WsIlostIndxTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlostIndxTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostIndxTot = parsed;
}

// Standard Getter
public string GetFiller289()
{
    return _Filler289;
}

// Standard Setter
public void SetFiller289(string value)
{
    _Filler289 = value;
}

// Get<>AsString()
public string GetFiller289AsString()
{
    return _Filler289.PadRight(1);
}

// Set<>AsString()
public void SetFiller289AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler289 = value;
}

// Standard Getter
public decimal GetWsIlostTot()
{
    return _WsIlostTot;
}

// Standard Setter
public void SetWsIlostTot(decimal value)
{
    _WsIlostTot = value;
}

// Get<>AsString()
public string GetWsIlostTotAsString()
{
    return _WsIlostTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlostTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostTot = parsed;
}

// Standard Getter
public string GetFiller290()
{
    return _Filler290;
}

// Standard Setter
public void SetFiller290(string value)
{
    _Filler290 = value;
}

// Get<>AsString()
public string GetFiller290AsString()
{
    return _Filler290.PadRight(1);
}

// Set<>AsString()
public void SetFiller290AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler290 = value;
}

// Standard Getter
public decimal GetWsIlostTotIndxnTot()
{
    return _WsIlostTotIndxnTot;
}

// Standard Setter
public void SetWsIlostTotIndxnTot(decimal value)
{
    _WsIlostTotIndxnTot = value;
}

// Get<>AsString()
public string GetWsIlostTotIndxnTotAsString()
{
    return _WsIlostTotIndxnTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsIlostTotIndxnTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsIlostTotIndxnTot = parsed;
}



public static int GetSize()
{
    return _size;
}

}

}}