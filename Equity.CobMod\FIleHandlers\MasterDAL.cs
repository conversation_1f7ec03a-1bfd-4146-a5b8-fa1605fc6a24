﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using Legacy4.Equity.CobMod.Models;
using WK.UK.CCH.Equity.CobolDataTransformation;
using WK.UK.CCH.Equity.CommonTypes;
// Assuming the DAO classes are in this namespace or accessible
// using WK.UK.CCH.Equity.CobolDataTransformation;
// Using placeholder DAOs for compilation

namespace Legacy4.Equity.CobMod.FIleHandlers
{
    public class MasterDAL : IDisposable
    {
        // --- Dependencies (Injected or Instantiated) ---
        private UserInfoDA _userInfoDA;
        private UserFundsDA _userFundsDA;
        private HoldingDA _masterHoldingDA;
        private DisposalDA _masterDisposalDA;
        private AcquisitionDA _masterAcquisitionDA;
        private BalanceDA _masterBalanceDA;

        // --- State Variables (Mimicking Working-Storage) ---
        private int _userNo;
        private int _currentYear;
        private DateTime _priceDate;
        private int _processMode;
        private bool _firstTimeUserInfo = true;

        // Store the parsed object representation from DAOs
        private UserFundRecord _currentUserFund;
        private MasterHoldingDARecord _currentHolding;
        private MasterBalanceDARecord _currentBalance;
        private MasterAcquisitionDARecord _currentAcquisition;
        private MasterDisposalDARecord _currentDisposal;

        // Intermediate D13 record holder
        private D13RecordHolder _d13Record = new D13RecordHolder();

        private bool _startKeyCountryGroupSedol = false;

        private bool _balancesRequested;
        private bool _acquisitionsRequested;
        private bool _disposalsRequested;

        private bool _isInitialized = false;

        // Mimic Linkage Section parameters passed on each call
        private string _fileAction;
        private string _fileActionKey;
        private byte[] _recordAreaBuffer; // Reference to the output buffer
        private LinkageIDs _ids;          // Reference to the IDs struct

        // Static helper for encoding (EBCDIC might be needed for mainframe COBOL)
        // Using ASCII for now based on MarshalAs.Ansi in LinkageIDs
        private static readonly Encoding _cobolEncoding = Encoding.GetEncoding("iso-8859-1"); // Or ASCII, or EBCDIC code page like "IBM037"

        // Constants for D13 record formatting
        private const int D13HeaderSize = 358; // Approximate size based on D13RecordHolder fields up to D13_1_REAL_BOND_PROFIT_LOSS
        private const int D13CommonSize = 196; // Approximate size of D13-BAL-ACQ-DISP-COMMON
        private const int D13BalanceAcqExtraSize = 106; // Approximate size of D13-2-BAL-ACQ-EXTRA
        private const int D13CostElementSize = 67; // Approximate size of D13-2-COST-ELEMENT
        private const int D13DisposalExtraSize = 101; // Approximate size of D13-2-DISP-EXTRA

        // Calculate full sizes based on components
        private const int D13BalanceRecordSize = D13CommonSize + D13BalanceAcqExtraSize + (CobolConstants.MAX_NO_OF_COSTS * D13CostElementSize);
        private const int D13AcquisitionRecordSize = D13CommonSize + D13BalanceAcqExtraSize + (CobolConstants.MAX_NO_OF_COSTS * D13CostElementSize);
        private const int D13DisposalRecordSize = D13CommonSize + D13DisposalExtraSize;


        public MasterDALResult PerformAction(string fileAct, ref byte[] recordAreaBuffer, int userNumber, int year, ref LinkageIDs ids, LinkageRequests requests)
        {
            // ... (Parameter validation remains the same) ...
            _fileAction = fileAct.Substring(0, 2).TrimEnd();
            _fileActionKey = fileAct.Length >= 3 ? fileAct.Substring(2, 1) : " ";

            _recordAreaBuffer = recordAreaBuffer;
            _ids = ids;
            _userNo = userNumber; // Potentially updated on each call
            _currentYear = AdjustYear(year); // Potentially updated on each call

            _balancesRequested = requests.BalancesRequested;
            _acquisitionsRequested = requests.AcquisitionsRequested;
            _disposalsRequested = requests.DisposalsRequested;

            var result = new MasterDALResult { Success = true, IDs = _ids };

            try
            {
                switch (_fileAction)
                {
                    case CobolConstants.OPEN_INPUT:
                    case CobolConstants.OPEN_I_O:
                        Initialize(userNumber, year);
                        result.RecordData = _recordAreaBuffer;
                        result.IDs = _ids;
                        break;

                    case CobolConstants.READ_NEXT:
                        if (!_isInitialized)
                            return new MasterDALResult { Success = false, ErrorMessage = "DAL not initialized. Call with OPEN first." };
                        result = ReadNextRecord();
                        break;

                    // ... (Other cases like CLOSE, SET_TRANS_EXPORTED_FLAGS, START_NOT_LESS_THAN remain similar) ...
                    case CobolConstants.CLOSE_FILE:
                        _isInitialized = false;
                        result.RecordData = _recordAreaBuffer;
                        result.IDs = _ids;
                        break;

                    case CobolConstants.SET_TRANS_EXPORTED_FLAGS:
                        if (!_isInitialized)
                            return new MasterDALResult { Success = false, ErrorMessage = "DAL not initialized. Call with OPEN first." };
                        SetTransactionExportedFlags();
                        result.RecordData = _recordAreaBuffer;
                        result.IDs = _ids;
                        break;

                    case CobolConstants.START_NOT_LESS_THAN:
                        _startKeyCountryGroupSedol = (_fileActionKey == "4");
                        result.RecordData = _recordAreaBuffer;
                        result.IDs = _ids;
                        break;

                    default:
                        return new MasterDALResult { Success = false, ErrorMessage = $"Unsupported File Action: '{_fileAction}'" };
                }
            }
            catch (Exception ex)
            {
                // Log the exception ex
                Console.WriteLine($"ERROR in MasterDAL: {ex}"); // Basic logging
                result.Success = false;
                result.ErrorMessage = $"Error processing action '{_fileAction}': {ex.Message}";
                result.RecordData = _recordAreaBuffer;
                result.IDs = _ids;
            }

            recordAreaBuffer = _recordAreaBuffer;
            ids = _ids;

            return result;
        }

        private void Initialize(int userNumber, int year)
        {
            // ... (DAO instantiation remains the same) ...
            _userInfoDA = new UserInfoDA();
            _userFundsDA = new UserFundsDA(_userNo);
            _masterHoldingDA = new HoldingDA(_priceDate);
            /*            _masterDisposalDA = new DisposalDA(holdingID);
                        _masterAcquisitionDA = new AcquisitionDA(holdingID);
                        _masterBalanceDA = new BalanceDA();*/


            _userNo = userNumber;
            _currentYear = AdjustYear(year);

            _firstTimeUserInfo = true;
            _currentUserFund = null;
            _currentHolding = null;
            _currentBalance = null;
            _currentAcquisition = null;
            _currentDisposal = null;

            // ... (Pricing date logic remains the same) ...
            string userInfoString = _userInfoDA.GetUserInfo(_userNo);
            if (!string.IsNullOrEmpty(userInfoString))
            {
                try
                {
                    var userInfo = ParseUserInfoString(userInfoString);
                    if (userInfo != null && !string.IsNullOrEmpty(userInfo.D37_PRICING_DATE) && userInfo.D37_PRICING_DATE.Length == 6)
                    {
                        int priceDD = int.Parse(userInfo.D37_PRICING_DATE.Substring(4, 2));
                        int priceMM = int.Parse(userInfo.D37_PRICING_DATE.Substring(2, 2)); // Corrected index
                        int priceYY = int.Parse(userInfo.D37_PRICING_DATE.Substring(0, 2));
                        int priceCCYY = AdjustYearYY(priceYY);
                        _priceDate = new DateTime(priceCCYY, priceMM, priceDD);
                        _masterHoldingDA = new HoldingDA(_priceDate);
                    }
                    else { throw new InvalidOperationException("Could not parse pricing date from User Info."); }
                }
                catch (Exception ex) { throw new InvalidOperationException($"Failed to initialize pricing date: {ex.Message}", ex); }
            }
            else { throw new InvalidOperationException("Failed to read user info header."); }


            _isInitialized = true;
            ClearBuffer();
        }

        private MasterDALResult ReadNextRecord()
        {
            ClearBuffer();

            while (true)
            {
                // --- Step 1: Get Next Fund if needed ---
                if (_currentUserFund == null)
                {
                    string fundString = _userFundsDA.ReadNext();
                    if (!string.IsNullOrEmpty(fundString))
                    {
                        _currentUserFund = ParseUserFundString(fundString);
                        if (_currentUserFund == null || string.IsNullOrEmpty(_currentUserFund.D37_FUND_ID))
                        {
                            return new MasterDALResult { Success = false, ErrorMessage = "Failed to parse current user fund string." };
                        }

                        var reportOrder = _startKeyCountryGroupSedol ? ReportOrderType.COUNTRY_GROUP_SEDOL : ReportOrderType.SEDOL;
                        _masterHoldingDA.LoadHoldingsByFund(_currentUserFund.D37_FUND_ID, _currentYear, reportOrder);
                        _currentHolding = null; // Reset holding state
                    }
                    else
                    {
                        return new MasterDALResult { Success = true, IsEndOfData = true, RecordData = _recordAreaBuffer, IDs = _ids }; // End of data
                    }
                }

                // --- Step 2: Get Next Holding if needed ---
                if (_currentHolding == null)
                {
                    string holdingString = _masterHoldingDA.ReadNext();
                    if (!string.IsNullOrEmpty(holdingString))
                    {
                        _currentHolding = MasterHoldingDARecord.MapFromString(holdingString);
                        if (_currentHolding == null || string.IsNullOrEmpty(_currentHolding.MHDA_HoldingID))
                        {
                            return new MasterDALResult { Success = false, ErrorMessage = "Failed to parse current master holding string." };
                        }

                        // Initialize child DAOs
                        _masterDisposalDA = new DisposalDA(_currentHolding.MHDA_HoldingID);
                        _masterAcquisitionDA = new AcquisitionDA(_currentHolding.MHDA_HoldingID);
                        _masterBalanceDA = new BalanceDA(_currentHolding.MHDA_HoldingID);

                        // Reset child record state
                        _currentBalance = null;
                        _currentAcquisition = null;
                        _currentDisposal = null;

                        // Map Holding DAO record to D13 Header structure
                        MapHoldingToD13(_currentHolding, _currentUserFund, _d13Record); // Pass fund for context if needed

                        // Format D13 Header to bytes
                        byte[] d13Bytes = FormatD13HeaderToBytes(_d13Record);

                        // Write bytes to output buffer
                        WriteBytesToBuffer(d13Bytes, _recordAreaBuffer.Length);

                        // Update Linkage IDs
                        UpdateLinkageIDsForHolding(_currentHolding);

                        _processMode = CobolConstants.ProcessBalances; // Start with balances
                        return new MasterDALResult { Success = true, RecordData = _recordAreaBuffer, IDs = _ids };
                    }
                    else
                    {
                        // No more holdings for this fund
                        _currentUserFund = null; // Trigger reading next fund
                        continue; // Loop to fetch next fund
                    }
                }

                // --- Step 3: Process Balances, Acquisitions, Disposals ---
                if (_currentHolding != null)
                {
                    // Process Balances
                    if (_processMode == CobolConstants.ProcessBalances)
                    {
                        if (_balancesRequested)
                        {
                            if (_currentBalance == null) // Only read if not already holding a balance record
                            {
                                string balanceString = _masterBalanceDA.ReadNext();
                                if (!string.IsNullOrEmpty(balanceString))
                                {
                                    _currentBalance = MasterBalanceDARecord.MapFromString(balanceString);
                                }
                                else
                                {
                                    _currentBalance = null; // No more balances
                                }
                            }

                            if (_currentBalance != null)
                            {
                                MapBalanceToD13(_currentBalance, _currentHolding, _d13Record); // Map to D13 structure
                                byte[] d13Bytes = FormatD13BalanceAcqToBytes(_d13Record, isBalance: true); // Format
                                WriteBytesToBuffer(d13Bytes, _recordAreaBuffer.Length); // Write to buffer
                                UpdateLinkageIDsForBalance(_currentBalance); // Update IDs
                                _currentBalance = null; // Consume the record for this call
                                return new MasterDALResult { Success = true, RecordData = _recordAreaBuffer, IDs = _ids };
                            }
                        }
                        // No more balances or not requested
                        _processMode = CobolConstants.ProcessAcquisitions;
                        _currentBalance = null;
                    }

                    // Process Acquisitions
                    if (_processMode == CobolConstants.ProcessAcquisitions)
                    {
                        if (_acquisitionsRequested)
                        {
                            if (_currentAcquisition == null)
                            {
                                string acqString = _masterAcquisitionDA.ReadNext();
                                if (!string.IsNullOrEmpty(acqString))
                                {
                                    _currentAcquisition = MasterAcquisitionDARecord.MapFromString(acqString);
                                }
                                else
                                {
                                    _currentAcquisition = null; // No more acquisitions
                                }
                            }

                            if (_currentAcquisition != null)
                            {
                                MapAcquisitionToD13(_currentAcquisition, _currentHolding, _d13Record);
                                byte[] d13Bytes = FormatD13BalanceAcqToBytes(_d13Record, isBalance: false);
                                // Write potentially truncated data based on COBOL move
                                WriteBytesToBuffer(d13Bytes, D13AcquisitionRecordSize); // Use calculated size
                                UpdateLinkageIDsForAcquisition(_currentAcquisition);
                                _currentAcquisition = null; // Consume
                                return new MasterDALResult { Success = true, RecordData = _recordAreaBuffer, IDs = _ids };
                            }
                        }
                        // No more acquisitions or not requested
                        _processMode = CobolConstants.ProcessDisposals;
                        _currentAcquisition = null;
                    }

                    // Process Disposals
                    if (_processMode == CobolConstants.ProcessDisposals)
                    {
                        if (_disposalsRequested)
                        {
                            if (_currentDisposal == null)
                            {
                                string dispString = _masterDisposalDA.ReadNext();
                                if (!string.IsNullOrEmpty(dispString))
                                {
                                    _currentDisposal = MasterDisposalDARecord.MapFromString(dispString);
                                }
                                else
                                {
                                    _currentDisposal = null; // No more disposals
                                }
                            }

                            if (_currentDisposal != null)
                            {
                                MapDisposalToD13(_currentDisposal, _currentHolding, _d13Record);
                                byte[] d13Bytes = FormatD13DisposalToBytes(_d13Record);
                                // Write potentially truncated data based on COBOL move
                                WriteBytesToBuffer(d13Bytes, D13DisposalRecordSize); // Use calculated size
                                UpdateLinkageIDsForDisposal(_currentDisposal);
                                _currentDisposal = null; // Consume
                                return new MasterDALResult { Success = true, RecordData = _recordAreaBuffer, IDs = _ids };
                            }
                        }
                        // No more disposals or not requested. Finished with this holding.
                        _currentHolding = null; // Trigger reading next holding
                        _currentDisposal = null;
                        // Stay in the loop
                    }
                }
                // Should not be reached if logic is correct
                throw new InvalidOperationException("Invalid state reached in ReadNextRecord loop.");
            } // End while loop
        }

        // --- Mapping Methods (DAO Object -> D13RecordHolder) ---

        private void MapHoldingToD13(MasterHoldingDARecord holding, UserFundRecord fund, D13RecordHolder d13)
        {
            // This mimics the A1-SETUP-HEADER-COBOL-RECORD logic
            // Move relevant fields from 'holding' and 'fund' to 'd13'
            // Ensure data types match (e.g., string to string, decimal? to decimal?)
            // Handle nulls appropriately (map to spaces or zeros based on COBOL PIC)

            d13.D13_1_CO_AC_LK = fund?.D37_FUND_CODE ?? "    "; // Example: Get Fund Code
            d13.D13_1_SEDOL = holding.MHDA_SedolCode ?? "       ";
            d13.D13_1_CONTRACT_NO = "          "; // Not directly in holding? Set default/find source
            d13.D13_1_RECORD_CODE = CobolConstants.REC_CODE_HOLDING; // "01"
            d13.D13_1_TRANSACTION_CATEGORY = "  "; // Header doesn't have one
            d13.D13_1_DATE_TIME_STAMP = holding.MHDA_DateTimeStamp ?? "".PadRight(14);
            d13.D13_1_CALC_FLAG = holding.MHDA_HoldingStatusCode ?? "    ";
            d13.D13_1_R_CALC_PERIOD_END_DATE = holding.MHDA_RealisedCalculationDate ?? "      ";
            d13.D13_1_U_CALC_PERIOD_END_DATE = holding.MHDA_UnrealisedCalculationDate ?? "      ";
            d13.D13_1_SPARE_DATE = "      "; // Default
            d13.D13_1_SECURITY_SORT_CODE = holding.MHDA_SortCode ?? "".PadRight(15);
            d13.D13_1_COUNTRY_CODE = holding.MHDA_CountryCode ?? "   ";
            d13.D13_1_GROUP_PREFIX = (holding.MHDA_GroupCode != null && holding.MHDA_GroupCode.Length > 0) ? holding.MHDA_GroupCode.Substring(0, 1) : " ";
            d13.D13_1_GROUP_CODE = holding.MHDA_GroupCode ?? "  ";
            d13.D13_1_INDUSTRIAL_CLASS = null; // Find source if needed
            d13.D13_1_SECURITY_TYPE = holding.MHDA_SecurityType ?? " ";
            d13.D13_1_CURRENT_MARKET_PRICE = holding.MHDA_MarketPrice;
            d13.D13_1_PRICE_PCT_INDICATOR = holding.MHDA_PricePercentIndicator ?? " ";
            d13.D13_1_B_F_ACCOUNTING_VALUE = holding.MHDA_BookValueBF;
            d13.D13_1_ACCOUNTING_VALUE_YTD = holding.MHDA_BookValueYTD;
            d13.D13_1_PROFIT_LOSS = holding.MHDA_ProfitLoss;
            d13.D13_1_ISSUERS_NAME = holding.MHDA_IssuersName ?? "".PadRight(35);
            d13.D13_1_STOCK_DESCRIPTION = holding.MHDA_StockDescription ?? "".PadRight(40);
            d13.D13_1_ISSUED_CAPITAL = holding.MHDA_IssuedCapital;
            d13.D13_1_MOVEMENT_INDICATOR = (holding.MHDA_Sedolmovement == "Y" || holding.MHDA_Sedolmovement == "1") ? 1 : 0; // Example mapping
            d13.D13_1_B_F_2PCT_HOLDING_DATE_R = holding.MHDA_DateOf2PercentHoldingBF ?? "      ";
            d13.D13_1_2PCT_HOLDING_DATE_YTD_R = holding.MHDA_DateOf2PercentHoldingYTD ?? "      ";
            d13.D13_1_SECURITY_INDICATOR = holding.MHDA_SecurityInd ?? " ";
            d13.D13_1_TOTAL_UNITS_YTD = holding.MHDA_totalUnits; // Assuming YTD is the main total
            d13.D13_1_CAPITAL_GAIN_LOSS = holding.MHDA_CapitalGainLoss;
            d13.D13_1_UNREAL_GAIN_LOSS = holding.MHDA_UnrealisedGain;
            d13.D13_1_UNREAL_PROFIT_LOSS = holding.MHDA_UnrealisedProfit;
            d13.D13_1_OFFSHORE_CLASS = holding.MHDA_Offshore_Class ?? " ";
            d13.D13_1_OVER_RIDE_FLAG = holding.MHDA_OverrideYearEnd ?? " ";
            d13.D13_1_DATE_OF_ISSUE_R = holding.MHDA_DateOfIssue ?? "      ";
            d13.D13_1_INDEX_FROM_ISSUE = holding.MHDA_IndexFromIssue ?? " ";
            d13.D13_1_DEEMED_GAIN_LOSS = holding.MHDA_DeemedGainLoss;
            d13.D13_1_DEEMED_PROFIT_LOSS = holding.MHDA_DeemedProfitLoss;
            d13.D13_1_HOLDING_FLAG = holding.MHDA_HoldingFlag ?? " ";
            d13.D13_1_BOND_OVERRIDE = holding.MHDA_BondOverrideCode ?? " ";
            d13.D13_1_LR_BASIS = holding.MHDA_LRBasis ?? " ";
            d13.D13_1_BOND_MATURITY_DATE_R = holding.MHDA_BondMaturityDate ?? "        ";
            d13.D13_1_BOND_PAR_VALUE = holding.MHDA_BondParValue;
            d13.D13_1_BOND_GAIN_OVERRIDE = holding.MHDA_BondGainOverride ?? " ";
            d13.D13_1_UNREAL_BOND_GAIN_LOSS = holding.MHDA_UnrealisedBondGainLoss;
            d13.D13_1_UNREAL_BOND_PROFIT_LOSS = holding.MHDA_UnrealisedBondProfitLoss;
            d13.D13_1_ASSET_USAGE = holding.MHDA_AssetUsage ?? " ";
            d13.D13_1_REAL_BOND_GAIN_LOSS = holding.MHDA_RealisedBondGainLoss;
            d13.D13_1_REAL_BOND_PROFIT_LOSS = holding.MHDA_RealisedBondProfitLoss;

            // Clear fields specific to other record types
            ClearD13BalanceAcqDispFields(d13);
        }

        private void MapBalanceToD13(MasterBalanceDARecord balance, MasterHoldingDARecord holding, D13RecordHolder d13)
        {
            // Mimics A2-SETUP-BALANCE-COBOL-RECORD
            // 1. Set common header fields (Key, Record Code)
            d13.D13_1_CO_AC_LK = holding.MHDA_FundCode ?? "    ";
            d13.D13_1_SEDOL = holding.MHDA_SedolCode ?? "       ";
            d13.D13_1_CONTRACT_NO = balance.MBDA_ContractNumber ?? "".PadRight(10);
            d13.D13_1_RECORD_CODE = CobolConstants.REC_CODE_BALANCE_ACQUISITION; // "02"

            // 2. Set common Bal/Acq/Disp fields
            d13.D13_2_TRANSACTION_CATEGORY = balance.MBDA_BalanceTypeCode ?? "  ";
            d13.D13_2_DATE_TIME_STAMP = balance.MBDA_DateTimeStamp ?? "".PadRight(14);
            d13.D13_2_PARENT_CAL = holding.MHDA_FundCode ?? "    "; // Parent is the holding's fund
            d13.D13_2_PARENT_SEDOL_CODE = balance.MBDA_ParentSedolCode ?? "       ";
            d13.D13_2_PREVIOUS_CAL = "    "; // Find source if needed
            d13.D13_2_PREVIOUS_SEDOL_CODE = "       "; // Find source if needed
            d13.D13_2_ORIGINAL_BARGAIN_NO = "          "; // Balances don't usually have this
            d13.D13_2_BARGAIN_DATE = balance.MBDA_BargainDate ?? "      ";
            d13.D13_2_SETTLEMENT_DATE = "      "; // Balances don't usually have this
            d13.D13_2_CURRENCY_CODE = "   "; // Find source if needed
            d13.D13_2_NOTES_COMMENTS = balance.MBDA_NotesComments ?? "".PadRight(36);
            d13.D13_2_B_F_NI_PI_FLAG = balance.MBDA_NewIssuePrivFlagBF ?? " ";
            d13.D13_2_NI_PI_FLAG_YTD = balance.MBDA_NewIssuePrivFlagYTD ?? " ";
            d13.D13_2_TRANSACTION_EXPORTED = " "; // Balances aren't exported this way
            d13.D13_2_CT_LINK_FUND = "    "; // Not applicable to balance
            d13.D13_2_SUB_FUND = "    "; // Not applicable to balance
            d13.D13_2_TRANSACTION_OVERRIDE = " "; // Not applicable to balance
            d13.D13_2_PARENT_MARKET_PRICE = balance.MBDA_ParentPrice;
            d13.D13_2_TRANS_UNIT_PRICE = null; // Not applicable to balance
            d13.D13_2_PRICE_PCT_INDICATOR = " "; // Not applicable to balance
            d13.D13_2_LIABILITY_PER_SHARE = balance.MBDA_TotalLiabilityPrice;
            d13.D13_2_OUTSTANDING_LIABILITY = balance.MBDA_OutstandingLiabilityPrice;
            d13.D13_2_STOCK_EXCH_INDICATOR = balance.MBDA_StockExchangeIndicator ?? " ";

            // 3. Set Balance/Acquisition specific fields
            d13.D13_2_B_F_TRANCHE_TOTAL_UNITS = balance.MBDA_totalUnitsPerTrancheBF;
            d13.D13_2_TRANCHE_TOTAL_UNITS_YTD = balance.MBDA_totalUnitsPerTrancheYTD;
            d13.D13_2_B_F_DISP_UNITS_REAC = balance.MBDA_DisposalUnitsReacquiredBF;
            d13.D13_2_DISP_UNITS_REAC_YTD = balance.MBDA_DisposalUnitsReacquiredYTD;
            d13.D13_2_UNDERWRITING_COMMISSION = null; // Not applicable to balance
            d13.D13_2_BOOK_COST = balance.MBDA_BookCost;
            d13.D13_2_ISSUE_SAME_CLASS = " "; // Find source if needed
            d13.D13_2_B_F_DATE_PREV_OP_EVENT_R = balance.MBDA_PrevOpEventDateBF ?? "      ";
            d13.D13_2_DATE_PREV_OP_EVENT_YTD = balance.MBDA_PrevOpEventDateYTD ?? "      ";
            d13.D13_2_FIRST_DAY_DEALING_PRICE = balance.MBDA_FDDPrice;
            d13.D13_2_B_F_INDEXED_COST_BALANCE = balance.MBDA_IndexedCostBalanceBF;
            d13.D13_2_INDEXED_COST_BALANCE_YTD = balance.MBDA_IndexedCostBalanceYTD;
            d13.D13_2_UNINDEXABLE_FLAG = balance.MBDA_UnindexableHoldingsFlag ?? " ";
            d13.D13_2_GROUP_TRANSFER_FLAG = " "; // Find source if needed
            d13.D13_2_B_F_LINK_RECORD_INDIC = ParseIntSafe(balance.MBDA_LinkedRecordIndicatorBF);
            d13.D13_2_LINK_RECORD_INDIC_YTD = ParseIntSafe(balance.MBDA_LinkedRecordIndicatorYTD);
            d13.D13_2_B_F_INDEX85_COST_BALANCE = balance.MBDA_IndexedPoolBF; // Assuming mapping
            d13.D13_2_INDEX85_COST_BALANCE_YTD = balance.MBDA_IndexedPoolYTD; // Assuming mapping
            d13.D13_2_TRANCHE_FLAG = balance.MBDA_TrancheFlag ?? " ";
            d13.D13_2_RESET_TAPER_DATE = balance.MBDA_ResetTaperDateFlag ?? " ";
            d13.D13_2_NO_OF_INITIAL_BOOK_COSTS = balance.MBDA_NumberofInitialBookCosts;
            d13.D13_2_B_F_NO_OF_COSTS_HELD = balance.MBDA_NumberofCostsHeldBF;
            d13.D13_2_NO_OF_COSTS_HELD_YTD = balance.MBDA_NumberofCostsHeldYTD;
            d13.D13_2_CapitalGainLoss_AcqBal = null; // Not applicable to balance record itself
            d13.D13_2_ProfitLoss_AcqBal = null; // Not applicable to balance record itself
            d13.D13_2_ProceedsOfDisposal_AcqBal = null; // Not applicable to balance record itself

            // 4. Map Cost Table
            d13.D13_2_COST_TABLE.Clear();
            int costsToMap = Math.Min(balance.Master_Balance_Cost_DA_Record?.Count ?? 0, CobolConstants.MAX_NO_OF_COSTS);
            for (int i = 0; i < costsToMap; i++)
            {
                var costDao = balance.Master_Balance_Cost_DA_Record[i];
                var costD13 = new D13CostElement
                {
                    D13_2_B_F_BALANCE_UNITS = costDao.MCDA_BalanceUnitsBF,
                    D13_2_BALANCE_UNITS_YTD = costDao.MCDA_BalanceUnitsYTD,
                    D13_2_UNITS_PRESENT = (costDao.MCDA_BalanceUnitsYTD.HasValue && costDao.MCDA_BalanceUnitsYTD != 0) ? "Y" : "N", // Example logic
                    D13_2_B_F_UNIND_COST_BALANCE = costDao.MCDA_UnindexedCostBalanceBF,
                    D13_2_UNIND_COST_BALANCE_YTD = costDao.MCDA_UnindexedCostBalanceYTD,
                    D13_2_B_F_BUDGET_DAY_VALUE = null, // Find source if needed (MBDA_BudgetDayValue*) - Requires matching logic
                    D13_2_BUDGET_DAY_VALUE_YTD = null, // Find source if needed
                    D13_2_INDEX_DATE = costDao.MCDA_IndexDate ?? "      ",
                    D13_2_COST_DATE = costDao.MCDA_CostDate ?? "      ",
                    D13_2_NI_NP_PI_COSTS = costDao.MCDA_CostTypeCode ?? " ", // Assuming direct map
                    D13_2_TAPER_DATE = balance.MBDA_TaperDate ?? "      ", // Taper date is on parent balance
                    D13_2_B_F_TAPER_UNITS = (i == 0) ? balance.MBDA_TaperUnitsBF : null, // Example: Only on first cost? Check COBOL
                    D13_2_TAPER_UNITS_YTD = (i == 0) ? balance.MBDA_TaperUnitsYTD : null // Example: Only on first cost? Check COBOL
                };
                d13.D13_2_COST_TABLE.Add(costD13);
            }
            // Pad cost table if necessary (Format method should handle this)

            // 5. Clear Disposal specific fields
            ClearD13DisposalFields(d13);
        }

        private void MapAcquisitionToD13(MasterAcquisitionDARecord acq, MasterHoldingDARecord holding, D13RecordHolder d13)
        {
            // Mimics A3-SETUP-ACQUISITION-COBOL-RECORD
            // Similar structure to MapBalanceToD13, but using acq fields

            // 1. Set common header fields
            d13.D13_1_CO_AC_LK = holding.MHDA_FundCode ?? "    ";
            d13.D13_1_SEDOL = holding.MHDA_SedolCode ?? "       ";
            d13.D13_1_CONTRACT_NO = acq.MADA_ContractNumber ?? "".PadRight(10);
            d13.D13_1_RECORD_CODE = CobolConstants.REC_CODE_BALANCE_ACQUISITION; // "02"

            // 2. Set common Bal/Acq/Disp fields
            d13.D13_2_TRANSACTION_CATEGORY = acq.MADA_TransactionCategoryCode ?? "  ";
            d13.D13_2_DATE_TIME_STAMP = acq.MADA_DateTimeStamp ?? "".PadRight(14);
            d13.D13_2_PARENT_CAL = holding.MHDA_FundCode ?? "    ";
            d13.D13_2_PARENT_SEDOL_CODE = acq.MADA_ParentSedolCode ?? "       ";
            d13.D13_2_PREVIOUS_CAL = acq.MADA_PrevDispFundCode ?? "    "; // Previous Disposal Fund
            d13.D13_2_PREVIOUS_SEDOL_CODE = acq.MADA_PrevDispSedolCode ?? "       "; // Previous Disposal Sedol
            d13.D13_2_ORIGINAL_BARGAIN_NO = acq.MADA_BargainNumber ?? "".PadRight(10);
            d13.D13_2_BARGAIN_DATE = acq.MADA_BargainDate ?? "      ";
            d13.D13_2_SETTLEMENT_DATE = acq.MADA_SettlementDate ?? "      ";
            d13.D13_2_CURRENCY_CODE = "   "; // Find source if needed
            d13.D13_2_NOTES_COMMENTS = acq.MADA_NotesComments ?? "".PadRight(36);
            d13.D13_2_B_F_NI_PI_FLAG = " "; // Not directly applicable to Acq?
            d13.D13_2_NI_PI_FLAG_YTD = " "; // Not directly applicable to Acq?
            d13.D13_2_TRANSACTION_EXPORTED = acq.MADA_TransactionExported ?? " ";
            d13.D13_2_CT_LINK_FUND = acq.MADA_CTLinkFundCode ?? "    ";
            d13.D13_2_SUB_FUND = "    "; // Find source if needed
            d13.D13_2_TRANSACTION_OVERRIDE = " "; // Find source if needed
            d13.D13_2_PARENT_MARKET_PRICE = acq.MADA_ParentPrice;
            d13.D13_2_TRANS_UNIT_PRICE = acq.MADA_TransactionPrice;
            d13.D13_2_PRICE_PCT_INDICATOR = " "; // Find source if needed
            d13.D13_2_LIABILITY_PER_SHARE = acq.MADA_LiabilityPerShare;
            d13.D13_2_OUTSTANDING_LIABILITY = acq.MADA_OSLiabilityPerShare;
            d13.D13_2_STOCK_EXCH_INDICATOR = acq.MADA_StockExchangeIndicator ?? " ";

            // 3. Set Balance/Acquisition specific fields
            d13.D13_2_B_F_TRANCHE_TOTAL_UNITS = acq.MADA_NumberOfUnits_BF; // Assuming map
            d13.D13_2_TRANCHE_TOTAL_UNITS_YTD = acq.MADA_NumberOfUnits_YTD; // Assuming map
            d13.D13_2_B_F_DISP_UNITS_REAC = null; // Not applicable to Acq
            d13.D13_2_DISP_UNITS_REAC_YTD = null; // Not applicable to Acq
            d13.D13_2_UNDERWRITING_COMMISSION = null; // Find source if needed
            d13.D13_2_BOOK_COST = acq.MADA_Cost_YTD; // Assuming YTD cost is the book cost
            d13.D13_2_ISSUE_SAME_CLASS = " "; // Find source if needed
            d13.D13_2_B_F_DATE_PREV_OP_EVENT_R = "      "; // Find source if needed
            d13.D13_2_DATE_PREV_OP_EVENT_YTD = "      "; // Find source if needed
            d13.D13_2_FIRST_DAY_DEALING_PRICE = acq.MADA_FDDPrice;
            d13.D13_2_B_F_INDEXED_COST_BALANCE = null; // Acq doesn't have indexed cost itself
            d13.D13_2_INDEXED_COST_BALANCE_YTD = null; // Acq doesn't have indexed cost itself
            d13.D13_2_UNINDEXABLE_FLAG = " "; // Find source if needed
            d13.D13_2_GROUP_TRANSFER_FLAG = " "; // Find source if needed
            d13.D13_2_B_F_LINK_RECORD_INDIC = null; // Not applicable to Acq
            d13.D13_2_LINK_RECORD_INDIC_YTD = null; // Not applicable to Acq
            d13.D13_2_B_F_INDEX85_COST_BALANCE = null; // Not applicable to Acq
            d13.D13_2_INDEX85_COST_BALANCE_YTD = null; // Not applicable to Acq
            d13.D13_2_TRANCHE_FLAG = acq.MADA_TrancheFlag ?? " ";
            d13.D13_2_RESET_TAPER_DATE = acq.MADA_ResetTaperDateFlag ?? " ";
            d13.D13_2_NO_OF_INITIAL_BOOK_COSTS = null; // Not applicable to Acq
            d13.D13_2_B_F_NO_OF_COSTS_HELD = null; // Not applicable to Acq
            d13.D13_2_NO_OF_COSTS_HELD_YTD = null; // Not applicable to Acq
            d13.D13_2_CapitalGainLoss_AcqBal = acq.MADA_CapitalGainLoss; // From Acq record
            d13.D13_2_ProfitLoss_AcqBal = acq.MADA_ProfitLoss; // From Acq record
            d13.D13_2_ProceedsOfDisposal_AcqBal = acq.MADA_Proceeds; // From Acq record

            // 4. Clear Cost Table (Acquisitions don't have the same cost table structure in D13)
            d13.D13_2_COST_TABLE.Clear();

            // 5. Clear Disposal specific fields
            ClearD13DisposalFields(d13);
        }

        private void MapDisposalToD13(MasterDisposalDARecord disp, MasterHoldingDARecord holding, D13RecordHolder d13)
        {
            // Mimics A4-SETUP-DISPOSAL-COBOL-RECORD

            // 1. Set common header fields
            d13.D13_1_CO_AC_LK = holding.MHDA_FundCode ?? "    ";
            d13.D13_1_SEDOL = holding.MHDA_SedolCode ?? "       ";
            d13.D13_1_CONTRACT_NO = disp.MDDA_ContractNo ?? "".PadRight(10);
            d13.D13_1_RECORD_CODE = CobolConstants.REC_CODE_DISPOSAL; // "03"

            // 2. Set common Bal/Acq/Disp fields
            d13.D13_2_TRANSACTION_CATEGORY = disp.MDDA_TransactionCategory ?? "  ";
            d13.D13_2_DATE_TIME_STAMP = disp.MDDA_DateTimeStamp ?? "".PadRight(14);
            d13.D13_2_PARENT_CAL = holding.MHDA_FundCode ?? "    ";
            d13.D13_2_PARENT_SEDOL_CODE = disp.MDDA_ParentSedol ?? "       ";
            d13.D13_2_PREVIOUS_CAL = disp.MDDA_PrevDispFundCode ?? "    "; // Previous Disposal Fund
            d13.D13_2_PREVIOUS_SEDOL_CODE = disp.MDDA_PrevDispSedolCode ?? "       "; // Previous Disposal Sedol
            d13.D13_2_ORIGINAL_BARGAIN_NO = disp.MDDA_BargainNumber ?? "".PadRight(10);
            d13.D13_2_BARGAIN_DATE = disp.MDDA_BargainDate ?? "      ";
            d13.D13_2_SETTLEMENT_DATE = disp.MDDA_SettlementDate ?? "      ";
            d13.D13_2_CURRENCY_CODE = "   "; // Find source if needed
            d13.D13_2_NOTES_COMMENTS = disp.MDDA_NotesComments ?? "".PadRight(36);
            d13.D13_2_B_F_NI_PI_FLAG = " "; // Not applicable to Disp
            d13.D13_2_NI_PI_FLAG_YTD = " "; // Not applicable to Disp
            d13.D13_2_TRANSACTION_EXPORTED = disp.MDDA_TransactionExported ?? " ";
            d13.D13_2_CT_LINK_FUND = disp.MDDA_CTLinkFundCode ?? "    ";
            d13.D13_2_SUB_FUND = "    "; // Find source if needed
            d13.D13_2_TRANSACTION_OVERRIDE = " "; // Find source if needed
            d13.D13_2_PARENT_MARKET_PRICE = disp.MDDA_ParentPrice;
            d13.D13_2_TRANS_UNIT_PRICE = disp.MDDA_TransactionPrice;
            d13.D13_2_PRICE_PCT_INDICATOR = " "; // Find source if needed
            d13.D13_2_LIABILITY_PER_SHARE = disp.MDDA_LiabilityPerShare;
            d13.D13_2_OUTSTANDING_LIABILITY = disp.MDDA_OSLiabilityPerShare;
            d13.D13_2_STOCK_EXCH_INDICATOR = disp.MDDA_StockExchangeIndicator ?? " ";

            // 3. Clear Balance/Acquisition specific fields
            ClearD13BalanceAcqFields(d13);

            // 4. Set Disposal specific fields
            d13.D13_2_NUMBER_OF_UNITS = disp.MDDA_NumberOfUnits;
            d13.D13_2_PROCEEDS = disp.MDDA_Proceeds;
            d13.D13_2_CAPITAL_GAIN_LOSS_Disp = disp.MDDA_CapitalGainLoss;
            d13.D13_2_FORCE_2PCT_MATCH_FLAG = disp.MDDA_Force2PercentMatching ?? " ";
            d13.D13_2_CGT_COST = disp.MDDA_CgtCost;
            d13.D13_2_PROFIT_LOSS_Disp = disp.MDDA_ProfitLoss;
            d13.D13_2_INDEXATION_USED = disp.MDDA_IndexationUsed;
            d13.D13_2_BOND_DISPOSAL = disp.MDDA_BondDisposal ?? " ";
            d13.D13_2_CONSOLIDATED_FLAG = " "; // Find source if needed
            d13.D13_2_STORE_UNITS = null; // Find source if needed
            d13.D13_2_STORE_PROCEEDS = null; // Find source if needed
            d13.D13_2_CONSOLIDATE_KEY = "".PadRight(23); // Find source if needed
            d13.D13_2_FA_2003_EXEMPTION = disp.MDDA_FA2003Exemption ?? " ";
            d13.D13_2_GT_PRO_RATA_MATCH = disp.MDDA_GTMatchProRata ?? " ";
            d13.D13_2_GT_USE_ORIGINAL_DATES = disp.MDDA_GTUseOriginalDates ?? " ";
            d13.D13_2_NumberOfUnitsYTD_Disp = disp.MDDA_NumberOfUnitsYTD;
            d13.D13_2_ProceedsYTD_Disp = disp.MDDA_ProceedsYTD;
        }

        private void ClearD13BalanceAcqDispFields(D13RecordHolder d13)
        {
            ClearD13BalanceAcqFields(d13);
            ClearD13DisposalFields(d13);
        }

        private void ClearD13BalanceAcqFields(D13RecordHolder d13)
        {
            // Clear fields from D13-2-RECORD-02-FIELDS
            d13.D13_2_B_F_TRANCHE_TOTAL_UNITS = null;
            d13.D13_2_TRANCHE_TOTAL_UNITS_YTD = null;
            d13.D13_2_B_F_DISP_UNITS_REAC = null;
            // ... clear all other fields in this group ...
            d13.D13_2_ProceedsOfDisposal_AcqBal = null;
            d13.D13_2_COST_TABLE.Clear();
        }

        private void ClearD13DisposalFields(D13RecordHolder d13)
        {
            // Clear fields from D13-2-RECORD-03-FIELDS
            d13.D13_2_NUMBER_OF_UNITS = null;
            d13.D13_2_PROCEEDS = null;
            // ... clear all other fields in this group ...
            d13.D13_2_ProceedsYTD_Disp = null;
        }


        // --- Formatting Methods (D13RecordHolder -> byte[]) ---
        // !! CRITICAL IMPLEMENTATION NEEDED HERE !!
        // These need to precisely match the COBOL layout including COMP-3, signs etc.

        private byte[] FormatD13HeaderToBytes(D13RecordHolder d13)
        {
            // Allocate buffer for header part (adjust size if needed)
            byte[] buffer = new byte[D13HeaderSize]; // Use estimated or exact size
            int offset = 0;

            // Format each field into the buffer at the correct offset
            offset = FormatString(buffer, offset, d13.D13_1_CO_AC_LK, 4);
            offset = FormatString(buffer, offset, d13.D13_1_SEDOL, 7);
            offset = FormatString(buffer, offset, d13.D13_1_CONTRACT_NO, 10);
            offset = FormatString(buffer, offset, d13.D13_1_RECORD_CODE, 2); // PIC 99 as string
            offset = FormatString(buffer, offset, d13.D13_1_TRANSACTION_CATEGORY, 2);
            offset = FormatString(buffer, offset, d13.D13_1_DATE_TIME_STAMP, 14);
            offset = FormatString(buffer, offset, d13.D13_1_CALC_FLAG, 4);
            offset = FormatString(buffer, offset, d13.D13_1_R_CALC_PERIOD_END_DATE, 6);
            offset = FormatString(buffer, offset, d13.D13_1_U_CALC_PERIOD_END_DATE, 6);
            offset = FormatString(buffer, offset, d13.D13_1_SPARE_DATE, 6);
            offset = FormatString(buffer, offset, d13.D13_1_SECURITY_SORT_CODE, 15);
            offset = FormatString(buffer, offset, d13.D13_1_COUNTRY_CODE, 3);
            offset = FormatString(buffer, offset, d13.D13_1_GROUP_PREFIX, 1);
            offset = FormatString(buffer, offset, d13.D13_1_GROUP_CODE, 2);
            offset = FormatPic9(buffer, offset, d13.D13_1_INDUSTRIAL_CLASS, 2); // PIC 99
            offset = FormatString(buffer, offset, d13.D13_1_SECURITY_TYPE, 1);
            offset = FormatComp3(buffer, offset, d13.D13_1_CURRENT_MARKET_PRICE, 9, 6, false); // PIC 9(9)V9(6) COMP-3 -> 8 bytes
            offset = FormatString(buffer, offset, d13.D13_1_PRICE_PCT_INDICATOR, 1);
            offset = FormatComp3(buffer, offset, d13.D13_1_B_F_ACCOUNTING_VALUE, 15, 2, true); // PIC S9(15)V99 COMP-3 -> 9 bytes
            offset = FormatComp3(buffer, offset, d13.D13_1_ACCOUNTING_VALUE_YTD, 15, 2, true); // PIC S9(15)V99 COMP-3 -> 9 bytes
            offset = FormatComp3(buffer, offset, d13.D13_1_PROFIT_LOSS, 13, 2, true); // PIC S9(13)V99 COMP-3 -> 8 bytes
            offset = FormatString(buffer, offset, d13.D13_1_ISSUERS_NAME, 35);
            offset = FormatString(buffer, offset, d13.D13_1_STOCK_DESCRIPTION, 40);
            offset = FormatComp3(buffer, offset, d13.D13_1_ISSUED_CAPITAL, 9, 6, false); // PIC 9(9)V9(6) COMP-3 -> 8 bytes
            offset = FormatPic9(buffer, offset, d13.D13_1_MOVEMENT_INDICATOR, 1); // PIC 9
            offset = FormatString(buffer, offset, d13.D13_1_B_F_2PCT_HOLDING_DATE_R, 6);
            offset = FormatString(buffer, offset, d13.D13_1_2PCT_HOLDING_DATE_YTD_R, 6);
            offset = FormatString(buffer, offset, d13.D13_1_SECURITY_INDICATOR, 1);
            offset = FormatComp3(buffer, offset, d13.D13_1_TOTAL_UNITS_YTD, 11, 2, true); // PIC S9(11)V99 COMP-3 -> 7 bytes
            offset = FormatComp3(buffer, offset, d13.D13_1_CAPITAL_GAIN_LOSS, 13, 2, true); // PIC S9(13)V99 COMP-3 -> 8 bytes
            offset = FormatComp3(buffer, offset, d13.D13_1_UNREAL_GAIN_LOSS, 13, 2, true); // PIC S9(13)V99 COMP-3 -> 8 bytes
            offset = FormatComp3(buffer, offset, d13.D13_1_UNREAL_PROFIT_LOSS, 13, 2, true); // PIC S9(13)V99 COMP-3 -> 8 bytes
            offset = FormatString(buffer, offset, d13.D13_1_OFFSHORE_CLASS, 1);
            offset = FormatString(buffer, offset, d13.D13_1_OVER_RIDE_FLAG, 1);
            offset = FormatString(buffer, offset, d13.D13_1_DATE_OF_ISSUE_R, 6);
            offset = FormatString(buffer, offset, d13.D13_1_INDEX_FROM_ISSUE, 1);
            offset = FormatComp3(buffer, offset, d13.D13_1_DEEMED_GAIN_LOSS, 13, 2, true); // PIC S9(13)V99 COMP-3 -> 8 bytes
            offset = FormatComp3(buffer, offset, d13.D13_1_DEEMED_PROFIT_LOSS, 13, 2, true); // PIC S9(13)V99 COMP-3 -> 8 bytes
            offset = FormatString(buffer, offset, d13.D13_1_HOLDING_FLAG, 1);
            offset = FormatString(buffer, offset, d13.D13_1_BOND_OVERRIDE, 1);
            offset = FormatString(buffer, offset, d13.D13_1_LR_BASIS, 1);
            offset = FormatString(buffer, offset, d13.D13_1_BOND_MATURITY_DATE_R, 8);
            offset = FormatComp3(buffer, offset, d13.D13_1_BOND_PAR_VALUE, 5, 6, false); // PIC 9(5)V9(6) COMP-3 -> 6 bytes
            offset = FormatString(buffer, offset, d13.D13_1_BOND_GAIN_OVERRIDE, 1);
            offset = FormatComp3(buffer, offset, d13.D13_1_UNREAL_BOND_GAIN_LOSS, 13, 2, true); // PIC S9(13)V99 COMP-3 -> 8 bytes
            offset = FormatComp3(buffer, offset, d13.D13_1_UNREAL_BOND_PROFIT_LOSS, 13, 2, true); // PIC S9(13)V99 COMP-3 -> 8 bytes
            offset = FormatString(buffer, offset, d13.D13_1_ASSET_USAGE, 1);
            offset = FormatComp3(buffer, offset, d13.D13_1_REAL_BOND_GAIN_LOSS, 13, 2, true); // PIC S9(13)V99 COMP-3 -> 8 bytes
            offset = FormatComp3(buffer, offset, d13.D13_1_REAL_BOND_PROFIT_LOSS, 13, 2, true); // PIC S9(13)V99 COMP-3 -> 8 bytes

            // Ensure offset matches expected D13HeaderSize
            if (offset != D13HeaderSize)
            {
                Console.WriteLine($"Warning: Formatted D13 Header size ({offset}) does not match expected size ({D13HeaderSize}).");
                // Optionally throw an error or resize buffer if critical
            }

            return buffer;
        }

        private byte[] FormatD13BalanceAcqToBytes(D13RecordHolder d13, bool isBalance)
        {
            // Allocate buffer for the full Bal/Acq record size
            int recordSize = isBalance ? D13BalanceRecordSize : D13AcquisitionRecordSize;
            byte[] buffer = new byte[recordSize];
            int offset = 0;

            // --- Format D13-BAL-ACQ-DISP-COMMON ---
            offset = FormatString(buffer, offset, d13.D13_1_CO_AC_LK, 4); // Key part 1
            offset = FormatString(buffer, offset, d13.D13_1_SEDOL, 7);    // Key part 2
            offset = FormatString(buffer, offset, d13.D13_1_CONTRACT_NO, 10); // Key part 3
            offset = FormatString(buffer, offset, d13.D13_1_RECORD_CODE, 2); // "02"
            offset = FormatString(buffer, offset, d13.D13_2_TRANSACTION_CATEGORY, 2);
            offset = FormatString(buffer, offset, d13.D13_2_DATE_TIME_STAMP, 14);
            offset = FormatString(buffer, offset, d13.D13_2_PARENT_CAL, 4);
            offset = FormatString(buffer, offset, d13.D13_2_PARENT_SEDOL_CODE, 7);
            offset = FormatString(buffer, offset, d13.D13_2_PREVIOUS_CAL, 4);
            offset = FormatString(buffer, offset, d13.D13_2_PREVIOUS_SEDOL_CODE, 7);
            offset = FormatString(buffer, offset, d13.D13_2_ORIGINAL_BARGAIN_NO, 10);
            offset = FormatPic9(buffer, offset, d13.D13_2_BARGAIN_DATE, 6); // PIC 9(6) as string
            offset = FormatPic9(buffer, offset, d13.D13_2_SETTLEMENT_DATE, 6); // PIC 9(6) as string
            offset = FormatString(buffer, offset, d13.D13_2_CURRENCY_CODE, 3);
            offset = FormatString(buffer, offset, d13.D13_2_NOTES_COMMENTS, 36);
            offset = FormatString(buffer, offset, d13.D13_2_B_F_NI_PI_FLAG, 1);
            offset = FormatString(buffer, offset, d13.D13_2_NI_PI_FLAG_YTD, 1);
            offset = FormatString(buffer, offset, d13.D13_2_TRANSACTION_EXPORTED, 1);
            offset = FormatString(buffer, offset, d13.D13_2_CT_LINK_FUND, 4);
            offset = FormatString(buffer, offset, d13.D13_2_SUB_FUND, 4);
            offset = FormatString(buffer, offset, d13.D13_2_TRANSACTION_OVERRIDE, 1);
            offset = FormatComp3(buffer, offset, d13.D13_2_PARENT_MARKET_PRICE, 9, 6, false); // PIC 9(9)V9(6) -> 8 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_TRANS_UNIT_PRICE, 5, 6, false); // PIC 9(5)V9(6) -> 6 bytes
            offset = FormatString(buffer, offset, d13.D13_2_PRICE_PCT_INDICATOR, 1);
            offset = FormatComp3(buffer, offset, d13.D13_2_LIABILITY_PER_SHARE, 5, 6, false); // PIC 9(5)V9(6) -> 6 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_OUTSTANDING_LIABILITY, 5, 6, false); // PIC 9(5)V9(6) -> 6 bytes
            offset = FormatString(buffer, offset, d13.D13_2_STOCK_EXCH_INDICATOR, 1);
            // Ensure offset matches D13CommonSize
            if (offset != D13CommonSize) Console.WriteLine($"Warning: D13 Common size mismatch ({offset} vs {D13CommonSize})");


            // --- Format D13-2-BAL-ACQ-EXTRA ---
            int startBalAcqExtra = offset;
            offset = FormatComp3(buffer, offset, d13.D13_2_B_F_TRANCHE_TOTAL_UNITS, 11, 2, true); // S9(11)V99 -> 7 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_TRANCHE_TOTAL_UNITS_YTD, 11, 2, true); // S9(11)V99 -> 7 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_B_F_DISP_UNITS_REAC, 11, 2, false); // 9(11)V99 -> 7 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_DISP_UNITS_REAC_YTD, 11, 2, false); // 9(11)V99 -> 7 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_UNDERWRITING_COMMISSION, 11, 2, false); // 9(11)V99 -> 7 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_BOOK_COST, 9, 2, false); // 9(9)V99 -> 6 bytes
            offset = FormatString(buffer, offset, d13.D13_2_ISSUE_SAME_CLASS, 1);
            offset = FormatString(buffer, offset, d13.D13_2_B_F_DATE_PREV_OP_EVENT_R, 6);
            offset = FormatPic9(buffer, offset, d13.D13_2_DATE_PREV_OP_EVENT_YTD, 6); // PIC 9(6) as string
            offset = FormatComp3(buffer, offset, d13.D13_2_FIRST_DAY_DEALING_PRICE, 5, 6, false); // 9(5)V9(6) -> 6 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_B_F_INDEXED_COST_BALANCE, 15, 2, true); // S9(15)V99 -> 9 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_INDEXED_COST_BALANCE_YTD, 15, 2, true); // S9(15)V99 -> 9 bytes
            offset = FormatString(buffer, offset, d13.D13_2_UNINDEXABLE_FLAG, 1);
            offset = FormatString(buffer, offset, d13.D13_2_GROUP_TRANSFER_FLAG, 1);
            offset = FormatPic9(buffer, offset, d13.D13_2_B_F_LINK_RECORD_INDIC, 1); // PIC 9
            offset = FormatPic9(buffer, offset, d13.D13_2_LINK_RECORD_INDIC_YTD, 1); // PIC 9
            offset = FormatComp3(buffer, offset, d13.D13_2_B_F_INDEX85_COST_BALANCE, 15, 2, true); // S9(15)V99 -> 9 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_INDEX85_COST_BALANCE_YTD, 15, 2, true); // S9(15)V99 -> 9 bytes
            offset = FormatString(buffer, offset, d13.D13_2_TRANCHE_FLAG, 1);
            offset = FormatString(buffer, offset, d13.D13_2_RESET_TAPER_DATE, 1);
            offset = FormatComp3(buffer, offset, d13.D13_2_NO_OF_INITIAL_BOOK_COSTS, 2, 0, false); // PIC 99 COMP-3 -> 2 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_B_F_NO_OF_COSTS_HELD, 2, 0, false); // PIC 99 COMP-3 -> 2 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_NO_OF_COSTS_HELD_YTD, 2, 0, false); // PIC 99 COMP-3 -> 2 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_CapitalGainLoss_AcqBal, 13, 2, true); // S9(13)V99 -> 8 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_ProfitLoss_AcqBal, 13, 2, true); // S9(13)V99 -> 8 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_ProceedsOfDisposal_AcqBal, 13, 2, false); // 9(13)V99 -> 8 bytes
                                                                                                     // Ensure offset matches D13CommonSize + D13BalanceAcqExtraSize
            if (offset != startBalAcqExtra + D13BalanceAcqExtraSize) Console.WriteLine($"Warning: D13 BalAcq Extra size mismatch ({offset - startBalAcqExtra} vs {D13BalanceAcqExtraSize})");


            // --- Format D13-2-COST-TABLE ---
            int costsToFormat = Math.Min(d13.D13_2_COST_TABLE?.Count ?? 0, CobolConstants.MAX_NO_OF_COSTS);
            for (int i = 0; i < costsToFormat; i++)
            {
                var cost = d13.D13_2_COST_TABLE[i];
                int costStartOffset = offset;
                offset = FormatComp3(buffer, offset, cost.D13_2_B_F_BALANCE_UNITS, 11, 2, true); // S9(11)V99 -> 7 bytes
                offset = FormatComp3(buffer, offset, cost.D13_2_BALANCE_UNITS_YTD, 11, 2, true); // S9(11)V99 -> 7 bytes
                offset = FormatString(buffer, offset, cost.D13_2_UNITS_PRESENT, 1);
                offset = FormatComp3(buffer, offset, cost.D13_2_B_F_UNIND_COST_BALANCE, 15, 2, true); // S9(15)V99 -> 9 bytes
                offset = FormatComp3(buffer, offset, cost.D13_2_UNIND_COST_BALANCE_YTD, 15, 2, true); // S9(15)V99 -> 9 bytes
                offset = FormatComp3(buffer, offset, cost.D13_2_B_F_BUDGET_DAY_VALUE, 13, 2, true); // S9(13)V99 -> 8 bytes
                offset = FormatComp3(buffer, offset, cost.D13_2_BUDGET_DAY_VALUE_YTD, 13, 2, true); // S9(13)V99 -> 8 bytes
                offset = FormatPic9(buffer, offset, cost.D13_2_INDEX_DATE, 6); // PIC 9(6) as string
                offset = FormatPic9(buffer, offset, cost.D13_2_COST_DATE, 6); // PIC 9(6) as string
                offset = FormatString(buffer, offset, cost.D13_2_NI_NP_PI_COSTS, 1);
                offset = FormatString(buffer, offset, cost.D13_2_TAPER_DATE, 6);
                offset = FormatComp3(buffer, offset, cost.D13_2_B_F_TAPER_UNITS, 9, 2, true); // S9(09)V99 -> 6 bytes
                offset = FormatComp3(buffer, offset, cost.D13_2_TAPER_UNITS_YTD, 9, 2, true); // S9(09)V99 -> 6 bytes

                // Ensure cost element size matches
                if (offset - costStartOffset != D13CostElementSize) Console.WriteLine($"Warning: D13 Cost Element size mismatch ({offset - costStartOffset} vs {D13CostElementSize}) for index {i}");
            }
            // Pad remaining cost elements with spaces/zeros if needed (Format* methods handle nulls)
            for (int i = costsToFormat; i < CobolConstants.MAX_NO_OF_COSTS; i++)
            {
                // Call formatters with null values to fill space
                int costStartOffset = offset;
                offset = FormatComp3(buffer, offset, null, 11, 2, true);
                offset = FormatComp3(buffer, offset, null, 11, 2, true);
                offset = FormatString(buffer, offset, null, 1);
                offset = FormatComp3(buffer, offset, null, 15, 2, true);
                offset = FormatComp3(buffer, offset, null, 15, 2, true);
                offset = FormatComp3(buffer, offset, null, 13, 2, true);
                offset = FormatComp3(buffer, offset, null, 13, 2, true);
                offset = FormatPic9(buffer, offset, 0, 6);
                offset = FormatPic9(buffer, offset, 0, 6);
                offset = FormatString(buffer, offset, null, 1);
                offset = FormatString(buffer, offset, null, 6);
                offset = FormatComp3(buffer, offset, null, 9, 2, true);
                offset = FormatComp3(buffer, offset, null, 9, 2, true);
                if (offset - costStartOffset != D13CostElementSize) Console.WriteLine($"Warning: D13 Padded Cost Element size mismatch ({offset - costStartOffset} vs {D13CostElementSize}) for index {i}");
            }


            // Ensure final offset matches expected total size
            if (offset != recordSize)
            {
                Console.WriteLine($"Warning: Formatted D13 Bal/Acq size ({offset}) does not match expected size ({recordSize}).");
            }

            return buffer;
        }

        private byte[] FormatD13DisposalToBytes(D13RecordHolder d13)
        {
            // Allocate buffer for the full Disposal record size
            byte[] buffer = new byte[D13DisposalRecordSize];
            int offset = 0;

            // --- Format D13-BAL-ACQ-DISP-COMMON ---
            // (Same as FormatD13BalanceAcqToBytes - could be refactored)
            offset = FormatString(buffer, offset, d13.D13_1_CO_AC_LK, 4);
            offset = FormatString(buffer, offset, d13.D13_1_SEDOL, 7);
            offset = FormatString(buffer, offset, d13.D13_1_CONTRACT_NO, 10);
            offset = FormatString(buffer, offset, d13.D13_1_RECORD_CODE, 2); // "03"
            offset = FormatString(buffer, offset, d13.D13_2_TRANSACTION_CATEGORY, 2);
            offset = FormatString(buffer, offset, d13.D13_2_DATE_TIME_STAMP, 14);
            offset = FormatString(buffer, offset, d13.D13_2_PARENT_CAL, 4);
            offset = FormatString(buffer, offset, d13.D13_2_PARENT_SEDOL_CODE, 7);
            offset = FormatString(buffer, offset, d13.D13_2_PREVIOUS_CAL, 4);
            offset = FormatString(buffer, offset, d13.D13_2_PREVIOUS_SEDOL_CODE, 7);
            offset = FormatString(buffer, offset, d13.D13_2_ORIGINAL_BARGAIN_NO, 10);
            offset = FormatPic9(buffer, offset, d13.D13_2_BARGAIN_DATE, 6);
            offset = FormatPic9(buffer, offset, d13.D13_2_SETTLEMENT_DATE, 6);
            offset = FormatString(buffer, offset, d13.D13_2_CURRENCY_CODE, 3);
            offset = FormatString(buffer, offset, d13.D13_2_NOTES_COMMENTS, 36);
            offset = FormatString(buffer, offset, d13.D13_2_B_F_NI_PI_FLAG, 1);
            offset = FormatString(buffer, offset, d13.D13_2_NI_PI_FLAG_YTD, 1);
            offset = FormatString(buffer, offset, d13.D13_2_TRANSACTION_EXPORTED, 1);
            offset = FormatString(buffer, offset, d13.D13_2_CT_LINK_FUND, 4);
            offset = FormatString(buffer, offset, d13.D13_2_SUB_FUND, 4);
            offset = FormatString(buffer, offset, d13.D13_2_TRANSACTION_OVERRIDE, 1);
            offset = FormatComp3(buffer, offset, d13.D13_2_PARENT_MARKET_PRICE, 9, 6, false);
            offset = FormatComp3(buffer, offset, d13.D13_2_TRANS_UNIT_PRICE, 5, 6, false);
            offset = FormatString(buffer, offset, d13.D13_2_PRICE_PCT_INDICATOR, 1);
            offset = FormatComp3(buffer, offset, d13.D13_2_LIABILITY_PER_SHARE, 5, 6, false);
            offset = FormatComp3(buffer, offset, d13.D13_2_OUTSTANDING_LIABILITY, 5, 6, false);
            offset = FormatString(buffer, offset, d13.D13_2_STOCK_EXCH_INDICATOR, 1);
            if (offset != D13CommonSize) Console.WriteLine($"Warning: D13 Common size mismatch ({offset} vs {D13CommonSize})");


            // --- Format D13-2-DISP-EXTRA ---
            int startDispExtra = offset;
            offset = FormatComp3(buffer, offset, d13.D13_2_NUMBER_OF_UNITS, 11, 2, true); // S9(11)V99 -> 7 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_PROCEEDS, 13, 2, true); // S9(13)V99 -> 8 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_CAPITAL_GAIN_LOSS_Disp, 13, 2, true); // S9(13)V99 -> 8 bytes
            offset = FormatString(buffer, offset, d13.D13_2_FORCE_2PCT_MATCH_FLAG, 1);
            offset = FormatComp3(buffer, offset, d13.D13_2_CGT_COST, 13, 2, true); // S9(13)V99 -> 8 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_PROFIT_LOSS_Disp, 13, 2, true); // S9(13)V99 -> 8 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_INDEXATION_USED, 13, 2, true); // S9(13)V99 -> 8 bytes
            offset = FormatString(buffer, offset, d13.D13_2_BOND_DISPOSAL, 1);
            offset = FormatString(buffer, offset, d13.D13_2_CONSOLIDATED_FLAG, 1);
            offset = FormatComp3(buffer, offset, d13.D13_2_STORE_UNITS, 11, 2, true); // S9(11)V99 -> 7 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_STORE_PROCEEDS, 13, 2, true); // S9(13)V99 -> 8 bytes
            offset = FormatString(buffer, offset, d13.D13_2_CONSOLIDATE_KEY, 23);
            offset = FormatString(buffer, offset, d13.D13_2_FA_2003_EXEMPTION, 1);
            offset = FormatString(buffer, offset, d13.D13_2_GT_PRO_RATA_MATCH, 1);
            offset = FormatString(buffer, offset, d13.D13_2_GT_USE_ORIGINAL_DATES, 1);
            offset = FormatComp3(buffer, offset, d13.D13_2_NumberOfUnitsYTD_Disp, 14, 3, false); // 9(14)V999 -> 9 bytes
            offset = FormatComp3(buffer, offset, d13.D13_2_ProceedsYTD_Disp, 14, 2, false); // 9(14)V99 -> 8 bytes
                                                                                            // Ensure offset matches D13CommonSize + D13DisposalExtraSize
            if (offset != startDispExtra + D13DisposalExtraSize) Console.WriteLine($"Warning: D13 Disposal Extra size mismatch ({offset - startDispExtra} vs {D13DisposalExtraSize})");


            // Ensure final offset matches expected total size
            if (offset != D13DisposalRecordSize)
            {
                Console.WriteLine($"Warning: Formatted D13 Disposal size ({offset}) does not match expected size ({D13DisposalRecordSize}).");
            }

            return buffer;
        }


        // --- Formatting Helper Methods ---

        private int FormatString(byte[] buffer, int offset, string value, int length)
        {
            string paddedValue = (value ?? string.Empty).PadRight(length);
            if (paddedValue.Length > length)
                paddedValue = paddedValue.Substring(0, length);

            byte[] bytes = _cobolEncoding.GetBytes(paddedValue);
            Buffer.BlockCopy(bytes, 0, buffer, offset, length);
            return offset + length;
        }

        private int FormatPic9(byte[] buffer, int offset, int? value, int length)
        {
            string stringValue = value?.ToString($"D{length}") ?? new string('0', length);
            if (stringValue.Length > length)
                stringValue = stringValue.Substring(stringValue.Length - length); // Take rightmost digits
            else if (stringValue.Length < length)
                stringValue = stringValue.PadLeft(length, '0');

            byte[] bytes = _cobolEncoding.GetBytes(stringValue);
            Buffer.BlockCopy(bytes, 0, buffer, offset, length);
            return offset + length;
        }

        private int FormatPic9(byte[] buffer, int offset, string value, int length)
        {
            // Format string containing digits, padding with '0' if needed
            string stringValue = value ?? string.Empty;
            if (!string.IsNullOrEmpty(stringValue) && stringValue.All(char.IsDigit))
            {
                if (stringValue.Length > length)
                    stringValue = stringValue.Substring(stringValue.Length - length);
                else if (stringValue.Length < length)
                    stringValue = stringValue.PadLeft(length, '0');
            }
            else
            {
                stringValue = new string('0', length); // Default to zeros if invalid/null
            }

            byte[] bytes = _cobolEncoding.GetBytes(stringValue);
            Buffer.BlockCopy(bytes, 0, buffer, offset, length);
            return offset + length;
        }


        // Simplified COMP-3 / Packed Decimal formatting (Assumes Big-Endian if mainframe)
        // NOTE: This is a basic implementation. Thorough testing is required.
        //       Consider using a dedicated library for robust COBOL data type handling.
        private int FormatComp3(byte[] buffer, int offset, decimal? value, int totalDigits, int decimalPlaces, bool isSigned)
        {
            int packedLength = (totalDigits + 1) / 2 + (isSigned ? 1 : 0); // +1 for sign nibble if signed
            packedLength = (totalDigits + (isSigned ? 1 : 0) + 1) / 2; // Correct calculation: (digits + sign_nibble + 1)/2


            if (!value.HasValue)
            {
                // Fill with packed zeros (usually 0x00 bytes with a positive sign nibble 0x0C)
                Array.Clear(buffer, offset, packedLength);
                if (packedLength > 0)
                {
                    buffer[offset + packedLength - 1] = 0x0C; // Positive sign
                }
                return offset + packedLength;
            }

            decimal val = value.Value;
            bool negative = val < 0;
            if (negative) val = -val;

            // Scale the value by decimal places and convert to long
            long scaledValue;
            try
            {
                scaledValue = (long)Math.Round(val * (decimal)Math.Pow(10, decimalPlaces), MidpointRounding.AwayFromZero);
            }
            catch (OverflowException)
            {
                // Handle overflow - maybe write max packed value or throw?
                Console.WriteLine($"Warning: Overflow formatting COMP-3 for value {value}. TotalDigits={totalDigits}");
                // Fill with packed 9s?
                for (int i = 0; i < packedLength; i++) buffer[offset + i] = 0x99;
                buffer[offset + packedLength - 1] = (byte)((buffer[offset + packedLength - 1] & 0xF0) | 0x0C); // Ensure valid sign
                return offset + packedLength;
            }


            string digits = scaledValue.ToString($"D{totalDigits}");

            int bufferIndex = offset;
            int digitIndex = 0;

            // Handle leading nybble if totalDigits is even
            if ((totalDigits + (isSigned ? 1 : 0)) % 2 == 0)
            {
                buffer[bufferIndex++] = 0x00; // Leading zero nybble often implied or handled differently, check standard
            }


            // Pack digits in pairs
            while (digitIndex < totalDigits)
            {
                byte highNybble = (byte)(digits[digitIndex++] - '0');
                byte lowNybble;

                if (digitIndex < totalDigits)
                {
                    lowNybble = (byte)(digits[digitIndex++] - '0');
                }
                else
                {
                    // Last digit, combine with sign
                    lowNybble = isSigned ? (byte)(negative ? 0x0D : 0x0C) : (byte)0x0F; // C=Pos, D=Neg, F=Unsigned
                }

                buffer[bufferIndex++] = (byte)((highNybble << 4) | lowNybble);
            }

            // Add sign if totalDigits is even and signed
            if (isSigned && totalDigits % 2 == 0)
            {
                if (bufferIndex < offset + packedLength)
                {
                    buffer[bufferIndex - 1] = (byte)((buffer[bufferIndex - 1] & 0xF0) | (negative ? 0x0D : 0x0C));
                }
                else
                {
                    Console.WriteLine($"Warning: COMP-3 buffer overflow adding sign. Offset={offset}, PackedLength={packedLength}, BufferIndex={bufferIndex}");
                }
            }


            // Ensure the correct number of bytes were written
            if (bufferIndex != offset + packedLength)
            {
                Console.WriteLine($"Warning: COMP-3 packed length mismatch. Expected {packedLength}, wrote {bufferIndex - offset}. Value={value}, Digits={totalDigits}, Dec={decimalPlaces}, Signed={isSigned}");
                // Adjust bufferIndex or handle error
            }


            return offset + packedLength;
        }


        private void WriteBytesToBuffer(byte[] data, int maxLength)
        {
            if (_recordAreaBuffer == null) return;
            ClearBuffer(); // Clear before writing new data

            int bytesToCopy = Math.Min(data.Length, Math.Min(maxLength, _recordAreaBuffer.Length));
            Buffer.BlockCopy(data, 0, _recordAreaBuffer, 0, bytesToCopy);

            // No space padding needed here as we are writing raw bytes
        }

        private int? ParseIntSafe(string s)
        {
            if (int.TryParse(s, out int result))
            {
                return result;
            }
            return null;
        }

        // --- Other Helper Methods (AdjustYear, ClearBuffer, Parse*, UpdateLinkageIDs*, Dispose) remain largely the same ---
        private int AdjustYear(int year)
        {
            if (year <= CobolConstants.LAST_MASTER_YEAR_YY) return year + 2000;
            else if (year > 100 && year < CobolConstants.FIRST_MASTER_YEAR) return year;
            else if (year >= CobolConstants.FIRST_MASTER_YEAR_YY && year < 100) return year + 1900;
            else return year;
        }
        private int AdjustYearYY(int yy)
        {
            if (yy <= CobolConstants.LAST_MASTER_YEAR_YY) return yy + 2000;
            else return yy + 1900;
        }
        private void ClearBuffer()
        {
            if (_recordAreaBuffer != null) Array.Clear(_recordAreaBuffer, 0, _recordAreaBuffer.Length);
        }
        private UserInfoRecord ParseUserInfoString(string data)
        {
            if (string.IsNullOrEmpty(data) || data.Length < 68) return null; // Adjust length check
            try
            {
                return new UserInfoRecord
                {
                    UserFundID = data.Substring(0, 8).Trim(),
                    D37_GLOBAL_COMP_NAME = data.Substring(8, 50).Trim(),
                    D37_MASTER_FILE_YEAR = data.Substring(58, 2).Trim(),
                    D37_PRICING_DATE = data.Substring(60, 6).Trim()
                };
            }
            catch { return null; }
        }
        private UserFundRecord ParseUserFundString(string data)
        {
            if (string.IsNullOrEmpty(data)) return null;
            try
            { // Add basic try-catch for safety
              // Assuming fixed format matching UserFundsDARecord copybook approx
                return new UserFundRecord
                {
                    D37_FUND_ID = data.Length > 8 ? data.Substring(0, 8).Trim() : string.Empty,
                    D37_PERIOD_START_DATE = data.Length > 14 ? data.Substring(8, 6).Trim() : string.Empty,
                    D37_PERIOD_END_DATE = data.Length > 20 ? data.Substring(14, 6).Trim() : string.Empty,
                    D37_FUND_CODE = data.Length > 24 ? data.Substring(20, 4).Trim() : string.Empty,
                    D37_NAME = data.Length > 54 ? data.Substring(24, 30).Trim() : string.Empty,
                    // ... parse other fields similarly ...
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error parsing fund string: {ex.Message}. Data: {data}");
                return null;
            }
        }
        private void SetTransactionExportedFlags()
        {
            if (_recordAreaBuffer == null || _recordAreaBuffer.Length < 8) throw new InvalidOperationException("Record buffer too small or null for SetTransactionExportedFlags.");
            string fundIdStr = _cobolEncoding.GetString(_recordAreaBuffer, 0, 8).Trim();
            if (!string.IsNullOrEmpty(fundIdStr)) _userFundsDA.SetTransactionExportedFlagsTrue(fundIdStr, _currentYear);
            else throw new InvalidOperationException("Invalid FundID provided in buffer for SetTransactionExportedFlags.");
            ClearBuffer();
        }
        private void UpdateLinkageIDsForHolding(MasterHoldingDARecord holding) { if (holding == null) return; _ids.MHDA_HoldingID = PadRight(holding.MHDA_HoldingID, 8); _ids.MHDA_FundID = PadRight(holding.MHDA_FundID, 8); _ids.MHDA_StockID = PadRight(holding.MHDA_StockID, 8); _ids.MHDA_BondOverrideID = PadRight(holding.MHDA_BondOverrideID, 8); _ids.MHDA_AssetUsageOverrideID = PadRight(holding.MHDA_AssetUsageOverrideID, 8); _ids.MADA_PrevDispID = PadRight("", 8); _ids.MADA_PrevAcqnID = PadRight("", 8); _ids.MHDA_DB_Timestamp = PadRight(holding.MHDA_DB_Timestamp, 24); _ids.MHDA_ILGCondition = holding.MHDA_ILGCondition ?? 0; }
        private void UpdateLinkageIDsForBalance(MasterBalanceDARecord balance) { if (balance == null) return; _ids.MBDA_BalanceID = PadRight(balance.MBDA_BalanceID, 8); _ids.MBDA_HoldingID = PadRight(balance.MBDA_HoldingID, 8); _ids.MBDA_ParentStockID = PadRight(balance.MBDA_ParentStockID, 8); _ids.MADA_TransactionCategoryID = PadRight("", 8); _ids.MADA_CTLinkFundID = PadRight("", 8); _ids.MADA_PrevDispID = PadRight("", 8); _ids.MADA_PrevAcqnID = PadRight("", 8); _ids.MBDA_DB_Timestamp = PadRight(balance.MBDA_DB_Timestamp, 24); }
        private void UpdateLinkageIDsForAcquisition(MasterAcquisitionDARecord acq) { if (acq == null) return; _ids.MADA_AcquisitionID = PadRight(acq.MADA_AcquisitionID, 8); _ids.MADA_HoldingID = PadRight(acq.MADA_HoldingID, 8); _ids.MADA_ParentStockID = PadRight(acq.MADA_ParentStockID, 8); _ids.MADA_TransactionCategoryID = PadRight(acq.MADA_TransactionCategoryID, 8); _ids.MADA_CTLinkFundID = PadRight(acq.MADA_CTLinkFundID, 8); _ids.MADA_PrevDispID = PadRight(acq.MADA_PrevDispID, 8); _ids.MADA_PrevAcqnID = PadRight(acq.MADA_PrevAcqnID, 8); _ids.MADA_DB_Timestamp = PadRight(acq.MADA_DB_Timestamp, 24); }
        private void UpdateLinkageIDsForDisposal(MasterDisposalDARecord disp) { if (disp == null) return; _ids.MDDA_DisposalID = PadRight(disp.MDDA_DisposalID, 8); _ids.MDDA_HoldingID = PadRight(disp.MDDA_HoldingID, 8); _ids.MDDA_ParentStockID = PadRight(disp.MDDA_ParentStockID, 8); _ids.MDDA_TransactionCategoryID = PadRight(disp.MDDA_TransactionCategoryID, 8); _ids.MDDA_CTLinkFundID = PadRight(disp.MDDA_CTLinkFundID, 8); _ids.MDDA_PrevDispID = PadRight(disp.MDDA_PrevDispID, 8); _ids.MDDA_PrevAcqnID = PadRight(disp.MDDA_PrevAcqnID, 8); _ids.MDDA_DB_Timestamp = PadRight(disp.MDDA_DB_Timestamp, 24); }
        private string PadRight(string value, int length) { return (value ?? string.Empty).PadRight(length); }
        public void Dispose() { (_userInfoDA as IDisposable)?.Dispose(); (_userFundsDA as IDisposable)?.Dispose(); (_masterHoldingDA as IDisposable)?.Dispose(); (_masterDisposalDA as IDisposable)?.Dispose(); (_masterAcquisitionDA as IDisposable)?.Dispose(); (_masterBalanceDA as IDisposable)?.Dispose(); _isInitialized = false; GC.SuppressFinalize(this); }

    } // End MasterDAL Class

    // ... (MasterDALResult, ReportOrderType enum, Placeholder DAOs remain the same) ...

} // End Namespace