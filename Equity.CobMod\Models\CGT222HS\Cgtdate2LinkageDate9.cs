using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// DTO class representing Cgtdate2LinkageDate9 Data Structure

public class Cgtdate2LinkageDate9
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate9, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd9, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd9 =0;
    
    
    
    
    // [DEBUG] Field: Filler78, is_external=, is_static_class=False, static_prefix=
    private Filler78 _Filler78 = new Filler78();
    
    
    
    
    // [DEBUG] Field: Filler79, is_external=, is_static_class=False, static_prefix=
    private Filler79 _Filler79 = new Filler79();
    
    
    
    
    // [DEBUG] Field: Filler80, is_external=, is_static_class=False, static_prefix=
    private Filler80 _Filler80 = new Filler80();
    
    
    
    
    // [DEBUG] Field: Filler81, is_external=, is_static_class=False, static_prefix=
    private Filler81 _Filler81 = new Filler81();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate9AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd9.ToString().PadLeft(8, '0'));
        result.Append(_Filler78.GetFiller78AsString());
        result.Append(_Filler79.GetFiller79AsString());
        result.Append(_Filler80.GetFiller80AsString());
        result.Append(_Filler81.GetFiller81AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate9AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd9(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler78.SetFiller78AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler78.SetFiller78AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler79.SetFiller79AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler79.SetFiller79AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler80.SetFiller80AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler80.SetFiller80AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler81.SetFiller81AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler81.SetFiller81AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate9AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate9(string value)
    {
        SetCgtdate2LinkageDate9AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd9()
    {
        return _Cgtdate2Ccyymmdd9;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd9(int value)
    {
        _Cgtdate2Ccyymmdd9 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd9AsString()
    {
        return _Cgtdate2Ccyymmdd9.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd9 = parsed;
    }
    
    // Standard Getter
    public Filler78 GetFiller78()
    {
        return _Filler78;
    }
    
    // Standard Setter
    public void SetFiller78(Filler78 value)
    {
        _Filler78 = value;
    }
    
    // Get<>AsString()
    public string GetFiller78AsString()
    {
        return _Filler78 != null ? _Filler78.GetFiller78AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller78AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler78 == null)
        {
            _Filler78 = new Filler78();
        }
        _Filler78.SetFiller78AsString(value);
    }
    
    // Standard Getter
    public Filler79 GetFiller79()
    {
        return _Filler79;
    }
    
    // Standard Setter
    public void SetFiller79(Filler79 value)
    {
        _Filler79 = value;
    }
    
    // Get<>AsString()
    public string GetFiller79AsString()
    {
        return _Filler79 != null ? _Filler79.GetFiller79AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller79AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler79 == null)
        {
            _Filler79 = new Filler79();
        }
        _Filler79.SetFiller79AsString(value);
    }
    
    // Standard Getter
    public Filler80 GetFiller80()
    {
        return _Filler80;
    }
    
    // Standard Setter
    public void SetFiller80(Filler80 value)
    {
        _Filler80 = value;
    }
    
    // Get<>AsString()
    public string GetFiller80AsString()
    {
        return _Filler80 != null ? _Filler80.GetFiller80AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller80AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler80 == null)
        {
            _Filler80 = new Filler80();
        }
        _Filler80.SetFiller80AsString(value);
    }
    
    // Standard Getter
    public Filler81 GetFiller81()
    {
        return _Filler81;
    }
    
    // Standard Setter
    public void SetFiller81(Filler81 value)
    {
        _Filler81 = value;
    }
    
    // Get<>AsString()
    public string GetFiller81AsString()
    {
        return _Filler81 != null ? _Filler81.GetFiller81AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller81AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler81 == null)
        {
            _Filler81 = new Filler81();
        }
        _Filler81.SetFiller81AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller78(string value)
    {
        _Filler78.SetFiller78AsString(value);
    }
    // Nested Class: Filler78
    public class Filler78
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc9, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc9 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd9, is_external=, is_static_class=False, static_prefix=
        private Filler78.Cgtdate2Yymmdd9 _Cgtdate2Yymmdd9 = new Filler78.Cgtdate2Yymmdd9();
        
        
        
        
    public Filler78() {}
    
    public Filler78(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc9(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd9.SetCgtdate2Yymmdd9AsString(data.Substring(offset, Cgtdate2Yymmdd9.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller78AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc9.PadRight(2));
        result.Append(_Cgtdate2Yymmdd9.GetCgtdate2Yymmdd9AsString());
        
        return result.ToString();
    }
    
    public void SetFiller78AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc9(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd9.SetCgtdate2Yymmdd9AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd9.SetCgtdate2Yymmdd9AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc9()
    {
        return _Cgtdate2Cc9;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc9(string value)
    {
        _Cgtdate2Cc9 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc9AsString()
    {
        return _Cgtdate2Cc9.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc9 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd9 GetCgtdate2Yymmdd9()
    {
        return _Cgtdate2Yymmdd9;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd9(Cgtdate2Yymmdd9 value)
    {
        _Cgtdate2Yymmdd9 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd9AsString()
    {
        return _Cgtdate2Yymmdd9 != null ? _Cgtdate2Yymmdd9.GetCgtdate2Yymmdd9AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd9 == null)
        {
            _Cgtdate2Yymmdd9 = new Cgtdate2Yymmdd9();
        }
        _Cgtdate2Yymmdd9.SetCgtdate2Yymmdd9AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd9
    public class Cgtdate2Yymmdd9
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy9, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy9 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd9, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd9.Cgtdate2Mmdd9 _Cgtdate2Mmdd9 = new Cgtdate2Yymmdd9.Cgtdate2Mmdd9();
        
        
        
        
    public Cgtdate2Yymmdd9() {}
    
    public Cgtdate2Yymmdd9(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy9(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd9.SetCgtdate2Mmdd9AsString(data.Substring(offset, Cgtdate2Mmdd9.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd9AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy9.PadRight(2));
        result.Append(_Cgtdate2Mmdd9.GetCgtdate2Mmdd9AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd9AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy9(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd9.SetCgtdate2Mmdd9AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd9.SetCgtdate2Mmdd9AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy9()
    {
        return _Cgtdate2Yy9;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy9(string value)
    {
        _Cgtdate2Yy9 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy9AsString()
    {
        return _Cgtdate2Yy9.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy9 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd9 GetCgtdate2Mmdd9()
    {
        return _Cgtdate2Mmdd9;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd9(Cgtdate2Mmdd9 value)
    {
        _Cgtdate2Mmdd9 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd9AsString()
    {
        return _Cgtdate2Mmdd9 != null ? _Cgtdate2Mmdd9.GetCgtdate2Mmdd9AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd9 == null)
        {
            _Cgtdate2Mmdd9 = new Cgtdate2Mmdd9();
        }
        _Cgtdate2Mmdd9.SetCgtdate2Mmdd9AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd9
    public class Cgtdate2Mmdd9
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm9, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm9 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd9, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd9 ="";
        
        
        
        
    public Cgtdate2Mmdd9() {}
    
    public Cgtdate2Mmdd9(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm9(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd9(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd9AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm9.PadRight(2));
        result.Append(_Cgtdate2Dd9.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd9AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm9(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd9(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm9()
    {
        return _Cgtdate2Mm9;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm9(string value)
    {
        _Cgtdate2Mm9 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm9AsString()
    {
        return _Cgtdate2Mm9.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm9 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd9()
    {
        return _Cgtdate2Dd9;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd9(string value)
    {
        _Cgtdate2Dd9 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd9AsString()
    {
        return _Cgtdate2Dd9.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd9 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller79(string value)
{
    _Filler79.SetFiller79AsString(value);
}
// Nested Class: Filler79
public class Filler79
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy9, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy9 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd9, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd9 =0;
    
    
    
    
public Filler79() {}

public Filler79(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy9(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd9(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller79AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy9.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd9.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller79AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy9(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd9(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy9()
{
    return _Cgtdate2CCcyy9;
}

// Standard Setter
public void SetCgtdate2CCcyy9(int value)
{
    _Cgtdate2CCcyy9 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy9AsString()
{
    return _Cgtdate2CCcyy9.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy9 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd9()
{
    return _Cgtdate2CMmdd9;
}

// Standard Setter
public void SetCgtdate2CMmdd9(int value)
{
    _Cgtdate2CMmdd9 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd9AsString()
{
    return _Cgtdate2CMmdd9.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd9 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller80(string value)
{
    _Filler80.SetFiller80AsString(value);
}
// Nested Class: Filler80
public class Filler80
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc9, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc9 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy9, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy9 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm9, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm9 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd9, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd9 =0;
    
    
    
    
public Filler80() {}

public Filler80(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc9(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy9(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm9(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd9(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller80AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc9.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy9.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm9.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd9.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller80AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc9(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy9(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm9(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd9(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc9()
{
    return _Cgtdate2CCc9;
}

// Standard Setter
public void SetCgtdate2CCc9(int value)
{
    _Cgtdate2CCc9 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc9AsString()
{
    return _Cgtdate2CCc9.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc9 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy9()
{
    return _Cgtdate2CYy9;
}

// Standard Setter
public void SetCgtdate2CYy9(int value)
{
    _Cgtdate2CYy9 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy9AsString()
{
    return _Cgtdate2CYy9.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy9 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm9()
{
    return _Cgtdate2CMm9;
}

// Standard Setter
public void SetCgtdate2CMm9(int value)
{
    _Cgtdate2CMm9 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm9AsString()
{
    return _Cgtdate2CMm9.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm9 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd9()
{
    return _Cgtdate2CDd9;
}

// Standard Setter
public void SetCgtdate2CDd9(int value)
{
    _Cgtdate2CDd9 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd9AsString()
{
    return _Cgtdate2CDd9.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd9 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller81(string value)
{
    _Filler81.SetFiller81AsString(value);
}
// Nested Class: Filler81
public class Filler81
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm9, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm9 =0;
    
    
    
    
    // [DEBUG] Field: Filler82, is_external=, is_static_class=False, static_prefix=
    private string _Filler82 ="";
    
    
    
    
public Filler81() {}

public Filler81(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm9(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller82(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller81AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm9.ToString().PadLeft(6, '0'));
    result.Append(_Filler82.PadRight(2));
    
    return result.ToString();
}

public void SetFiller81AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm9(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller82(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm9()
{
    return _Cgtdate2CCcyymm9;
}

// Standard Setter
public void SetCgtdate2CCcyymm9(int value)
{
    _Cgtdate2CCcyymm9 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm9AsString()
{
    return _Cgtdate2CCcyymm9.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm9 = parsed;
}

// Standard Getter
public string GetFiller82()
{
    return _Filler82;
}

// Standard Setter
public void SetFiller82(string value)
{
    _Filler82 = value;
}

// Get<>AsString()
public string GetFiller82AsString()
{
    return _Filler82.PadRight(2);
}

// Set<>AsString()
public void SetFiller82AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler82 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}