using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtabortDTO
{// DTO class representing EquityErrorMessages Data Structure

public class EquityErrorMessages
{
    private static int _size = 500;
    // [DEBUG] Class: EquityErrorMessages, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler1, is_external=, is_static_class=False, static_prefix=
    private string _Filler1 ="000000";
    
    
    
    
    // [DEBUG] Field: Filler2, is_external=, is_static_class=False, static_prefix=
    private string _Filler2 ="Successful                                        ";
    
    
    
    
    // [DEBUG] Field: Filler3, is_external=, is_static_class=False, static_prefix=
    private string _Filler3 ="000001";
    
    
    
    
    // [DEBUG] Field: Filler4, is_external=, is_static_class=False, static_prefix=
    private string _Filler4 ="Logon successful but user crashed last session    ";
    
    
    
    
    // [DEBUG] Field: Filler5, is_external=, is_static_class=False, static_prefix=
    private string _Filler5 ="000002";
    
    
    
    
    // [DEBUG] Field: Filler6, is_external=, is_static_class=False, static_prefix=
    private string _Filler6 ="Userid is already in use                          ";
    
    
    
    
    // [DEBUG] Field: Filler7, is_external=, is_static_class=False, static_prefix=
    private string _Filler7 ="000003";
    
    
    
    
    // [DEBUG] Field: Filler8, is_external=, is_static_class=False, static_prefix=
    private string _Filler8 ="Userid does not exist                             ";
    
    
    
    
    // [DEBUG] Field: Filler9, is_external=, is_static_class=False, static_prefix=
    private string _Filler9 ="000004";
    
    
    
    
    // [DEBUG] Field: Filler10, is_external=, is_static_class=False, static_prefix=
    private string _Filler10 ="Invalid userid/pw combination                     ";
    
    
    
    
    // [DEBUG] Field: Filler11, is_external=, is_static_class=False, static_prefix=
    private string _Filler11 ="000005";
    
    
    
    
    // [DEBUG] Field: Filler12, is_external=, is_static_class=False, static_prefix=
    private string _Filler12 ="See error log file $uuuuERR.LOG in user directory ";
    
    
    
    
    // [DEBUG] Field: Filler13, is_external=, is_static_class=False, static_prefix=
    private string _Filler13 ="000006";
    
    
    
    
    // [DEBUG] Field: Filler14, is_external=, is_static_class=False, static_prefix=
    private string _Filler14 ="Load record(s) rejected                           ";
    
    
    
    
    // [DEBUG] Field: Filler15, is_external=, is_static_class=False, static_prefix=
    private string _Filler15 ="000007";
    
    
    
    
    // [DEBUG] Field: Filler16, is_external=, is_static_class=False, static_prefix=
    private string _Filler16 ="Run completed but warnings were reported          ";
    
    
    
    
    // [DEBUG] Field: Filler17, is_external=, is_static_class=False, static_prefix=
    private string _Filler17 ="000008";
    
    
    
    
    // [DEBUG] Field: Filler18, is_external=, is_static_class=False, static_prefix=
    private string _Filler18 ="Severe error detected see reports and run log file";
    
    
    
    
    // [DEBUG] Field: Filler19, is_external=, is_static_class=False, static_prefix=
    private string _Filler19 ="000009";
    
    
    
    
    // [DEBUG] Field: Filler20, is_external=, is_static_class=False, static_prefix=
    private string _Filler20 ="User not logged on                                ";
    
    
    
    
    
    // Serialization methods
    public string GetEquityErrorMessagesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler1.PadRight(0));
        result.Append(_Filler2.PadRight(50));
        result.Append(_Filler3.PadRight(0));
        result.Append(_Filler4.PadRight(50));
        result.Append(_Filler5.PadRight(0));
        result.Append(_Filler6.PadRight(50));
        result.Append(_Filler7.PadRight(0));
        result.Append(_Filler8.PadRight(50));
        result.Append(_Filler9.PadRight(0));
        result.Append(_Filler10.PadRight(50));
        result.Append(_Filler11.PadRight(0));
        result.Append(_Filler12.PadRight(50));
        result.Append(_Filler13.PadRight(0));
        result.Append(_Filler14.PadRight(50));
        result.Append(_Filler15.PadRight(0));
        result.Append(_Filler16.PadRight(50));
        result.Append(_Filler17.PadRight(0));
        result.Append(_Filler18.PadRight(50));
        result.Append(_Filler19.PadRight(0));
        result.Append(_Filler20.PadRight(50));
        
        return result.ToString();
    }
    
    public void SetEquityErrorMessagesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller1(extracted);
        }
        offset += 0;
        if (offset + 50 <= data.Length)
        {
            string extracted = data.Substring(offset, 50).Trim();
            SetFiller2(extracted);
        }
        offset += 50;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller3(extracted);
        }
        offset += 0;
        if (offset + 50 <= data.Length)
        {
            string extracted = data.Substring(offset, 50).Trim();
            SetFiller4(extracted);
        }
        offset += 50;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller5(extracted);
        }
        offset += 0;
        if (offset + 50 <= data.Length)
        {
            string extracted = data.Substring(offset, 50).Trim();
            SetFiller6(extracted);
        }
        offset += 50;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller7(extracted);
        }
        offset += 0;
        if (offset + 50 <= data.Length)
        {
            string extracted = data.Substring(offset, 50).Trim();
            SetFiller8(extracted);
        }
        offset += 50;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller9(extracted);
        }
        offset += 0;
        if (offset + 50 <= data.Length)
        {
            string extracted = data.Substring(offset, 50).Trim();
            SetFiller10(extracted);
        }
        offset += 50;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller11(extracted);
        }
        offset += 0;
        if (offset + 50 <= data.Length)
        {
            string extracted = data.Substring(offset, 50).Trim();
            SetFiller12(extracted);
        }
        offset += 50;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller13(extracted);
        }
        offset += 0;
        if (offset + 50 <= data.Length)
        {
            string extracted = data.Substring(offset, 50).Trim();
            SetFiller14(extracted);
        }
        offset += 50;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller15(extracted);
        }
        offset += 0;
        if (offset + 50 <= data.Length)
        {
            string extracted = data.Substring(offset, 50).Trim();
            SetFiller16(extracted);
        }
        offset += 50;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller17(extracted);
        }
        offset += 0;
        if (offset + 50 <= data.Length)
        {
            string extracted = data.Substring(offset, 50).Trim();
            SetFiller18(extracted);
        }
        offset += 50;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller19(extracted);
        }
        offset += 0;
        if (offset + 50 <= data.Length)
        {
            string extracted = data.Substring(offset, 50).Trim();
            SetFiller20(extracted);
        }
        offset += 50;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetEquityErrorMessagesAsString();
    }
    // Set<>String Override function
    public void SetEquityErrorMessages(string value)
    {
        SetEquityErrorMessagesAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller1()
    {
        return _Filler1;
    }
    
    // Standard Setter
    public void SetFiller1(string value)
    {
        _Filler1 = value;
    }
    
    // Get<>AsString()
    public string GetFiller1AsString()
    {
        return _Filler1.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler1 = value;
    }
    
    // Standard Getter
    public string GetFiller2()
    {
        return _Filler2;
    }
    
    // Standard Setter
    public void SetFiller2(string value)
    {
        _Filler2 = value;
    }
    
    // Get<>AsString()
    public string GetFiller2AsString()
    {
        return _Filler2.PadRight(50);
    }
    
    // Set<>AsString()
    public void SetFiller2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler2 = value;
    }
    
    // Standard Getter
    public string GetFiller3()
    {
        return _Filler3;
    }
    
    // Standard Setter
    public void SetFiller3(string value)
    {
        _Filler3 = value;
    }
    
    // Get<>AsString()
    public string GetFiller3AsString()
    {
        return _Filler3.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler3 = value;
    }
    
    // Standard Getter
    public string GetFiller4()
    {
        return _Filler4;
    }
    
    // Standard Setter
    public void SetFiller4(string value)
    {
        _Filler4 = value;
    }
    
    // Get<>AsString()
    public string GetFiller4AsString()
    {
        return _Filler4.PadRight(50);
    }
    
    // Set<>AsString()
    public void SetFiller4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler4 = value;
    }
    
    // Standard Getter
    public string GetFiller5()
    {
        return _Filler5;
    }
    
    // Standard Setter
    public void SetFiller5(string value)
    {
        _Filler5 = value;
    }
    
    // Get<>AsString()
    public string GetFiller5AsString()
    {
        return _Filler5.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler5 = value;
    }
    
    // Standard Getter
    public string GetFiller6()
    {
        return _Filler6;
    }
    
    // Standard Setter
    public void SetFiller6(string value)
    {
        _Filler6 = value;
    }
    
    // Get<>AsString()
    public string GetFiller6AsString()
    {
        return _Filler6.PadRight(50);
    }
    
    // Set<>AsString()
    public void SetFiller6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler6 = value;
    }
    
    // Standard Getter
    public string GetFiller7()
    {
        return _Filler7;
    }
    
    // Standard Setter
    public void SetFiller7(string value)
    {
        _Filler7 = value;
    }
    
    // Get<>AsString()
    public string GetFiller7AsString()
    {
        return _Filler7.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler7 = value;
    }
    
    // Standard Getter
    public string GetFiller8()
    {
        return _Filler8;
    }
    
    // Standard Setter
    public void SetFiller8(string value)
    {
        _Filler8 = value;
    }
    
    // Get<>AsString()
    public string GetFiller8AsString()
    {
        return _Filler8.PadRight(50);
    }
    
    // Set<>AsString()
    public void SetFiller8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler8 = value;
    }
    
    // Standard Getter
    public string GetFiller9()
    {
        return _Filler9;
    }
    
    // Standard Setter
    public void SetFiller9(string value)
    {
        _Filler9 = value;
    }
    
    // Get<>AsString()
    public string GetFiller9AsString()
    {
        return _Filler9.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler9 = value;
    }
    
    // Standard Getter
    public string GetFiller10()
    {
        return _Filler10;
    }
    
    // Standard Setter
    public void SetFiller10(string value)
    {
        _Filler10 = value;
    }
    
    // Get<>AsString()
    public string GetFiller10AsString()
    {
        return _Filler10.PadRight(50);
    }
    
    // Set<>AsString()
    public void SetFiller10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler10 = value;
    }
    
    // Standard Getter
    public string GetFiller11()
    {
        return _Filler11;
    }
    
    // Standard Setter
    public void SetFiller11(string value)
    {
        _Filler11 = value;
    }
    
    // Get<>AsString()
    public string GetFiller11AsString()
    {
        return _Filler11.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller11AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler11 = value;
    }
    
    // Standard Getter
    public string GetFiller12()
    {
        return _Filler12;
    }
    
    // Standard Setter
    public void SetFiller12(string value)
    {
        _Filler12 = value;
    }
    
    // Get<>AsString()
    public string GetFiller12AsString()
    {
        return _Filler12.PadRight(50);
    }
    
    // Set<>AsString()
    public void SetFiller12AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler12 = value;
    }
    
    // Standard Getter
    public string GetFiller13()
    {
        return _Filler13;
    }
    
    // Standard Setter
    public void SetFiller13(string value)
    {
        _Filler13 = value;
    }
    
    // Get<>AsString()
    public string GetFiller13AsString()
    {
        return _Filler13.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller13AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler13 = value;
    }
    
    // Standard Getter
    public string GetFiller14()
    {
        return _Filler14;
    }
    
    // Standard Setter
    public void SetFiller14(string value)
    {
        _Filler14 = value;
    }
    
    // Get<>AsString()
    public string GetFiller14AsString()
    {
        return _Filler14.PadRight(50);
    }
    
    // Set<>AsString()
    public void SetFiller14AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler14 = value;
    }
    
    // Standard Getter
    public string GetFiller15()
    {
        return _Filler15;
    }
    
    // Standard Setter
    public void SetFiller15(string value)
    {
        _Filler15 = value;
    }
    
    // Get<>AsString()
    public string GetFiller15AsString()
    {
        return _Filler15.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller15AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler15 = value;
    }
    
    // Standard Getter
    public string GetFiller16()
    {
        return _Filler16;
    }
    
    // Standard Setter
    public void SetFiller16(string value)
    {
        _Filler16 = value;
    }
    
    // Get<>AsString()
    public string GetFiller16AsString()
    {
        return _Filler16.PadRight(50);
    }
    
    // Set<>AsString()
    public void SetFiller16AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler16 = value;
    }
    
    // Standard Getter
    public string GetFiller17()
    {
        return _Filler17;
    }
    
    // Standard Setter
    public void SetFiller17(string value)
    {
        _Filler17 = value;
    }
    
    // Get<>AsString()
    public string GetFiller17AsString()
    {
        return _Filler17.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller17AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler17 = value;
    }
    
    // Standard Getter
    public string GetFiller18()
    {
        return _Filler18;
    }
    
    // Standard Setter
    public void SetFiller18(string value)
    {
        _Filler18 = value;
    }
    
    // Get<>AsString()
    public string GetFiller18AsString()
    {
        return _Filler18.PadRight(50);
    }
    
    // Set<>AsString()
    public void SetFiller18AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler18 = value;
    }
    
    // Standard Getter
    public string GetFiller19()
    {
        return _Filler19;
    }
    
    // Standard Setter
    public void SetFiller19(string value)
    {
        _Filler19 = value;
    }
    
    // Get<>AsString()
    public string GetFiller19AsString()
    {
        return _Filler19.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller19AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler19 = value;
    }
    
    // Standard Getter
    public string GetFiller20()
    {
        return _Filler20;
    }
    
    // Standard Setter
    public void SetFiller20(string value)
    {
        _Filler20 = value;
    }
    
    // Get<>AsString()
    public string GetFiller20AsString()
    {
        return _Filler20.PadRight(50);
    }
    
    // Set<>AsString()
    public void SetFiller20AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler20 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
