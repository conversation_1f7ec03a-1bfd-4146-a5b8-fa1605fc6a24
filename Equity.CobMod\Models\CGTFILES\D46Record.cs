using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D46Record Data Structure

public class D46Record
{
    private static int _size = 194;
    // [DEBUG] Class: D46Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D46Fund, is_external=, is_static_class=False, static_prefix=
    private string _D46Fund ="";
    
    
    
    
    // [DEBUG] Field: D46Sedol, is_external=, is_static_class=False, static_prefix=
    private string _D46Sedol ="";
    
    
    
    
    // [DEBUG] Field: D46ContractNo, is_external=, is_static_class=False, static_prefix=
    private string _D46ContractNo ="";
    
    
    
    
    // [DEBUG] Field: D46TransactionCategory, is_external=, is_static_class=False, static_prefix=
    private string _D46TransactionCategory ="";
    
    
    
    
    // [DEBUG] Field: D46BargainDate, is_external=, is_static_class=False, static_prefix=
    private D46BargainDate _D46BargainDate = new D46BargainDate();
    
    
    
    
    // [DEBUG] Field: D46BargainDateN, is_external=, is_static_class=False, static_prefix=
    private int _D46BargainDateN =0;
    
    
    
    
    // [DEBUG] Field: D46SettlementDate, is_external=, is_static_class=False, static_prefix=
    private D46SettlementDate _D46SettlementDate = new D46SettlementDate();
    
    
    
    
    // [DEBUG] Field: D46DeleteFlag, is_external=, is_static_class=False, static_prefix=
    private string _D46DeleteFlag ="";
    
    
    
    
    // [DEBUG] Field: D46Quantity, is_external=, is_static_class=False, static_prefix=
    private string _D46Quantity ="";
    
    
    
    
    // [DEBUG] Field: D46Value, is_external=, is_static_class=False, static_prefix=
    private string _D46Value ="";
    
    
    
    
    // [DEBUG] Field: D46TransactionPrice, is_external=, is_static_class=False, static_prefix=
    private string _D46TransactionPrice ="";
    
    
    
    
    // [DEBUG] Field: D46ParentSedol, is_external=, is_static_class=False, static_prefix=
    private string _D46ParentSedol ="";
    
    
    
    
    // [DEBUG] Field: D46ParentPrice, is_external=, is_static_class=False, static_prefix=
    private string _D46ParentPrice ="";
    
    
    
    
    // [DEBUG] Field: D46FddPrice, is_external=, is_static_class=False, static_prefix=
    private string _D46FddPrice ="";
    
    
    
    
    // [DEBUG] Field: D46OsLiability, is_external=, is_static_class=False, static_prefix=
    private string _D46OsLiability ="";
    
    
    
    
    // [DEBUG] Field: D46LiabilityPerShare, is_external=, is_static_class=False, static_prefix=
    private string _D46LiabilityPerShare ="";
    
    
    
    
    // [DEBUG] Field: D46Comments, is_external=, is_static_class=False, static_prefix=
    private string _D46Comments ="";
    
    
    
    
    // [DEBUG] Field: D46NumberOfAcq, is_external=, is_static_class=False, static_prefix=
    private string _D46NumberOfAcq ="";
    
    
    
    
    // [DEBUG] Field: D46HoldingFlag, is_external=, is_static_class=False, static_prefix=
    private string _D46HoldingFlag ="";
    
    
    
    
    // [DEBUG] Field: D46TrancheFlag, is_external=, is_static_class=False, static_prefix=
    private string _D46TrancheFlag ="";
    
    
    
    
    // [DEBUG] Field: D46ResetTaperDate, is_external=, is_static_class=False, static_prefix=
    private string _D46ResetTaperDate ="";
    
    
    
    
    // [DEBUG] Field: D46Fa2003Exemption, is_external=, is_static_class=False, static_prefix=
    private string _D46Fa2003Exemption ="";
    
    
    
    
    // [DEBUG] Field: D46GtProRataMatch, is_external=, is_static_class=False, static_prefix=
    private string _D46GtProRataMatch ="";
    
    
    
    
    // [DEBUG] Field: D46GtUseOriginalDates, is_external=, is_static_class=False, static_prefix=
    private string _D46GtUseOriginalDates ="";
    
    
    
    
    // [DEBUG] Field: D46Filler, is_external=, is_static_class=False, static_prefix=
    private string _D46Filler ="";
    
    
    
    
    
    // Serialization methods
    public string GetD46RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D46Fund.PadRight(4));
        result.Append(_D46Sedol.PadRight(7));
        result.Append(_D46ContractNo.PadRight(10));
        result.Append(_D46TransactionCategory.PadRight(2));
        result.Append(_D46BargainDate.GetD46BargainDateAsString());
        result.Append(_D46BargainDateN.ToString().PadLeft(6, '0'));
        result.Append(_D46SettlementDate.GetD46SettlementDateAsString());
        result.Append(_D46DeleteFlag.PadRight(1));
        result.Append(_D46Quantity.PadRight(11));
        result.Append(_D46Value.PadRight(11));
        result.Append(_D46TransactionPrice.PadRight(11));
        result.Append(_D46ParentSedol.PadRight(7));
        result.Append(_D46ParentPrice.PadRight(11));
        result.Append(_D46FddPrice.PadRight(11));
        result.Append(_D46OsLiability.PadRight(11));
        result.Append(_D46LiabilityPerShare.PadRight(11));
        result.Append(_D46Comments.PadRight(40));
        result.Append(_D46NumberOfAcq.PadRight(2));
        result.Append(_D46HoldingFlag.PadRight(0));
        result.Append(_D46TrancheFlag.PadRight(0));
        result.Append(_D46ResetTaperDate.PadRight(0));
        result.Append(_D46Fa2003Exemption.PadRight(0));
        result.Append(_D46GtProRataMatch.PadRight(0));
        result.Append(_D46GtUseOriginalDates.PadRight(0));
        result.Append(_D46Filler.PadRight(26));
        
        return result.ToString();
    }
    
    public void SetD46RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD46Fund(extracted);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetD46Sedol(extracted);
        }
        offset += 7;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetD46ContractNo(extracted);
        }
        offset += 10;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD46TransactionCategory(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _D46BargainDate.SetD46BargainDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _D46BargainDate.SetD46BargainDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD46BargainDateN(parsedInt);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            _D46SettlementDate.SetD46SettlementDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _D46SettlementDate.SetD46SettlementDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD46DeleteFlag(extracted);
        }
        offset += 1;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetD46Quantity(extracted);
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetD46Value(extracted);
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetD46TransactionPrice(extracted);
        }
        offset += 11;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetD46ParentSedol(extracted);
        }
        offset += 7;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetD46ParentPrice(extracted);
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetD46FddPrice(extracted);
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetD46OsLiability(extracted);
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetD46LiabilityPerShare(extracted);
        }
        offset += 11;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD46Comments(extracted);
        }
        offset += 40;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD46NumberOfAcq(extracted);
        }
        offset += 2;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD46HoldingFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD46TrancheFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD46ResetTaperDate(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD46Fa2003Exemption(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD46GtProRataMatch(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD46GtUseOriginalDates(extracted);
        }
        offset += 0;
        if (offset + 26 <= data.Length)
        {
            string extracted = data.Substring(offset, 26).Trim();
            SetD46Filler(extracted);
        }
        offset += 26;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD46RecordAsString();
    }
    // Set<>String Override function
    public void SetD46Record(string value)
    {
        SetD46RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD46Fund()
    {
        return _D46Fund;
    }
    
    // Standard Setter
    public void SetD46Fund(string value)
    {
        _D46Fund = value;
    }
    
    // Get<>AsString()
    public string GetD46FundAsString()
    {
        return _D46Fund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD46FundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46Fund = value;
    }
    
    // Standard Getter
    public string GetD46Sedol()
    {
        return _D46Sedol;
    }
    
    // Standard Setter
    public void SetD46Sedol(string value)
    {
        _D46Sedol = value;
    }
    
    // Get<>AsString()
    public string GetD46SedolAsString()
    {
        return _D46Sedol.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetD46SedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46Sedol = value;
    }
    
    // Standard Getter
    public string GetD46ContractNo()
    {
        return _D46ContractNo;
    }
    
    // Standard Setter
    public void SetD46ContractNo(string value)
    {
        _D46ContractNo = value;
    }
    
    // Get<>AsString()
    public string GetD46ContractNoAsString()
    {
        return _D46ContractNo.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetD46ContractNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46ContractNo = value;
    }
    
    // Standard Getter
    public string GetD46TransactionCategory()
    {
        return _D46TransactionCategory;
    }
    
    // Standard Setter
    public void SetD46TransactionCategory(string value)
    {
        _D46TransactionCategory = value;
    }
    
    // Get<>AsString()
    public string GetD46TransactionCategoryAsString()
    {
        return _D46TransactionCategory.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD46TransactionCategoryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46TransactionCategory = value;
    }
    
    // Standard Getter
    public D46BargainDate GetD46BargainDate()
    {
        return _D46BargainDate;
    }
    
    // Standard Setter
    public void SetD46BargainDate(D46BargainDate value)
    {
        _D46BargainDate = value;
    }
    
    // Get<>AsString()
    public string GetD46BargainDateAsString()
    {
        return _D46BargainDate != null ? _D46BargainDate.GetD46BargainDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD46BargainDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D46BargainDate == null)
        {
            _D46BargainDate = new D46BargainDate();
        }
        _D46BargainDate.SetD46BargainDateAsString(value);
    }
    
    // Standard Getter
    public int GetD46BargainDateN()
    {
        return _D46BargainDateN;
    }
    
    // Standard Setter
    public void SetD46BargainDateN(int value)
    {
        _D46BargainDateN = value;
    }
    
    // Get<>AsString()
    public string GetD46BargainDateNAsString()
    {
        return _D46BargainDateN.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD46BargainDateNAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D46BargainDateN = parsed;
    }
    
    // Standard Getter
    public D46SettlementDate GetD46SettlementDate()
    {
        return _D46SettlementDate;
    }
    
    // Standard Setter
    public void SetD46SettlementDate(D46SettlementDate value)
    {
        _D46SettlementDate = value;
    }
    
    // Get<>AsString()
    public string GetD46SettlementDateAsString()
    {
        return _D46SettlementDate != null ? _D46SettlementDate.GetD46SettlementDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD46SettlementDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D46SettlementDate == null)
        {
            _D46SettlementDate = new D46SettlementDate();
        }
        _D46SettlementDate.SetD46SettlementDateAsString(value);
    }
    
    // Standard Getter
    public string GetD46DeleteFlag()
    {
        return _D46DeleteFlag;
    }
    
    // Standard Setter
    public void SetD46DeleteFlag(string value)
    {
        _D46DeleteFlag = value;
    }
    
    // Get<>AsString()
    public string GetD46DeleteFlagAsString()
    {
        return _D46DeleteFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD46DeleteFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46DeleteFlag = value;
    }
    
    // Standard Getter
    public string GetD46Quantity()
    {
        return _D46Quantity;
    }
    
    // Standard Setter
    public void SetD46Quantity(string value)
    {
        _D46Quantity = value;
    }
    
    // Get<>AsString()
    public string GetD46QuantityAsString()
    {
        return _D46Quantity.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetD46QuantityAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46Quantity = value;
    }
    
    // Standard Getter
    public string GetD46Value()
    {
        return _D46Value;
    }
    
    // Standard Setter
    public void SetD46Value(string value)
    {
        _D46Value = value;
    }
    
    // Get<>AsString()
    public string GetD46ValueAsString()
    {
        return _D46Value.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetD46ValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46Value = value;
    }
    
    // Standard Getter
    public string GetD46TransactionPrice()
    {
        return _D46TransactionPrice;
    }
    
    // Standard Setter
    public void SetD46TransactionPrice(string value)
    {
        _D46TransactionPrice = value;
    }
    
    // Get<>AsString()
    public string GetD46TransactionPriceAsString()
    {
        return _D46TransactionPrice.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetD46TransactionPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46TransactionPrice = value;
    }
    
    // Standard Getter
    public string GetD46ParentSedol()
    {
        return _D46ParentSedol;
    }
    
    // Standard Setter
    public void SetD46ParentSedol(string value)
    {
        _D46ParentSedol = value;
    }
    
    // Get<>AsString()
    public string GetD46ParentSedolAsString()
    {
        return _D46ParentSedol.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetD46ParentSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46ParentSedol = value;
    }
    
    // Standard Getter
    public string GetD46ParentPrice()
    {
        return _D46ParentPrice;
    }
    
    // Standard Setter
    public void SetD46ParentPrice(string value)
    {
        _D46ParentPrice = value;
    }
    
    // Get<>AsString()
    public string GetD46ParentPriceAsString()
    {
        return _D46ParentPrice.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetD46ParentPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46ParentPrice = value;
    }
    
    // Standard Getter
    public string GetD46FddPrice()
    {
        return _D46FddPrice;
    }
    
    // Standard Setter
    public void SetD46FddPrice(string value)
    {
        _D46FddPrice = value;
    }
    
    // Get<>AsString()
    public string GetD46FddPriceAsString()
    {
        return _D46FddPrice.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetD46FddPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46FddPrice = value;
    }
    
    // Standard Getter
    public string GetD46OsLiability()
    {
        return _D46OsLiability;
    }
    
    // Standard Setter
    public void SetD46OsLiability(string value)
    {
        _D46OsLiability = value;
    }
    
    // Get<>AsString()
    public string GetD46OsLiabilityAsString()
    {
        return _D46OsLiability.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetD46OsLiabilityAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46OsLiability = value;
    }
    
    // Standard Getter
    public string GetD46LiabilityPerShare()
    {
        return _D46LiabilityPerShare;
    }
    
    // Standard Setter
    public void SetD46LiabilityPerShare(string value)
    {
        _D46LiabilityPerShare = value;
    }
    
    // Get<>AsString()
    public string GetD46LiabilityPerShareAsString()
    {
        return _D46LiabilityPerShare.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetD46LiabilityPerShareAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46LiabilityPerShare = value;
    }
    
    // Standard Getter
    public string GetD46Comments()
    {
        return _D46Comments;
    }
    
    // Standard Setter
    public void SetD46Comments(string value)
    {
        _D46Comments = value;
    }
    
    // Get<>AsString()
    public string GetD46CommentsAsString()
    {
        return _D46Comments.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD46CommentsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46Comments = value;
    }
    
    // Standard Getter
    public string GetD46NumberOfAcq()
    {
        return _D46NumberOfAcq;
    }
    
    // Standard Setter
    public void SetD46NumberOfAcq(string value)
    {
        _D46NumberOfAcq = value;
    }
    
    // Get<>AsString()
    public string GetD46NumberOfAcqAsString()
    {
        return _D46NumberOfAcq.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD46NumberOfAcqAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46NumberOfAcq = value;
    }
    
    // Standard Getter
    public string GetD46HoldingFlag()
    {
        return _D46HoldingFlag;
    }
    
    // Standard Setter
    public void SetD46HoldingFlag(string value)
    {
        _D46HoldingFlag = value;
    }
    
    // Get<>AsString()
    public string GetD46HoldingFlagAsString()
    {
        return _D46HoldingFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD46HoldingFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46HoldingFlag = value;
    }
    
    // Standard Getter
    public string GetD46TrancheFlag()
    {
        return _D46TrancheFlag;
    }
    
    // Standard Setter
    public void SetD46TrancheFlag(string value)
    {
        _D46TrancheFlag = value;
    }
    
    // Get<>AsString()
    public string GetD46TrancheFlagAsString()
    {
        return _D46TrancheFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD46TrancheFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46TrancheFlag = value;
    }
    
    // Standard Getter
    public string GetD46ResetTaperDate()
    {
        return _D46ResetTaperDate;
    }
    
    // Standard Setter
    public void SetD46ResetTaperDate(string value)
    {
        _D46ResetTaperDate = value;
    }
    
    // Get<>AsString()
    public string GetD46ResetTaperDateAsString()
    {
        return _D46ResetTaperDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD46ResetTaperDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46ResetTaperDate = value;
    }
    
    // Standard Getter
    public string GetD46Fa2003Exemption()
    {
        return _D46Fa2003Exemption;
    }
    
    // Standard Setter
    public void SetD46Fa2003Exemption(string value)
    {
        _D46Fa2003Exemption = value;
    }
    
    // Get<>AsString()
    public string GetD46Fa2003ExemptionAsString()
    {
        return _D46Fa2003Exemption.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD46Fa2003ExemptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46Fa2003Exemption = value;
    }
    
    // Standard Getter
    public string GetD46GtProRataMatch()
    {
        return _D46GtProRataMatch;
    }
    
    // Standard Setter
    public void SetD46GtProRataMatch(string value)
    {
        _D46GtProRataMatch = value;
    }
    
    // Get<>AsString()
    public string GetD46GtProRataMatchAsString()
    {
        return _D46GtProRataMatch.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD46GtProRataMatchAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46GtProRataMatch = value;
    }
    
    // Standard Getter
    public string GetD46GtUseOriginalDates()
    {
        return _D46GtUseOriginalDates;
    }
    
    // Standard Setter
    public void SetD46GtUseOriginalDates(string value)
    {
        _D46GtUseOriginalDates = value;
    }
    
    // Get<>AsString()
    public string GetD46GtUseOriginalDatesAsString()
    {
        return _D46GtUseOriginalDates.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD46GtUseOriginalDatesAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46GtUseOriginalDates = value;
    }
    
    // Standard Getter
    public string GetD46Filler()
    {
        return _D46Filler;
    }
    
    // Standard Setter
    public void SetD46Filler(string value)
    {
        _D46Filler = value;
    }
    
    // Get<>AsString()
    public string GetD46FillerAsString()
    {
        return _D46Filler.PadRight(26);
    }
    
    // Set<>AsString()
    public void SetD46FillerAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46Filler = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD46BargainDate(string value)
    {
        _D46BargainDate.SetD46BargainDateAsString(value);
    }
    // Nested Class: D46BargainDate
    public class D46BargainDate
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D46BargainDateYy, is_external=, is_static_class=False, static_prefix=
        private string _D46BargainDateYy ="";
        
        
        
        
        // [DEBUG] Field: D46BargainDateMm, is_external=, is_static_class=False, static_prefix=
        private string _D46BargainDateMm ="";
        
        
        
        
        // [DEBUG] Field: D46BargainDateDd, is_external=, is_static_class=False, static_prefix=
        private string _D46BargainDateDd ="";
        
        
        
        
    public D46BargainDate() {}
    
    public D46BargainDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD46BargainDateYy(data.Substring(offset, 2).Trim());
        offset += 2;
        SetD46BargainDateMm(data.Substring(offset, 2).Trim());
        offset += 2;
        SetD46BargainDateDd(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetD46BargainDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D46BargainDateYy.PadRight(2));
        result.Append(_D46BargainDateMm.PadRight(2));
        result.Append(_D46BargainDateDd.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetD46BargainDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD46BargainDateYy(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD46BargainDateMm(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD46BargainDateDd(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD46BargainDateYy()
    {
        return _D46BargainDateYy;
    }
    
    // Standard Setter
    public void SetD46BargainDateYy(string value)
    {
        _D46BargainDateYy = value;
    }
    
    // Get<>AsString()
    public string GetD46BargainDateYyAsString()
    {
        return _D46BargainDateYy.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD46BargainDateYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46BargainDateYy = value;
    }
    
    // Standard Getter
    public string GetD46BargainDateMm()
    {
        return _D46BargainDateMm;
    }
    
    // Standard Setter
    public void SetD46BargainDateMm(string value)
    {
        _D46BargainDateMm = value;
    }
    
    // Get<>AsString()
    public string GetD46BargainDateMmAsString()
    {
        return _D46BargainDateMm.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD46BargainDateMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46BargainDateMm = value;
    }
    
    // Standard Getter
    public string GetD46BargainDateDd()
    {
        return _D46BargainDateDd;
    }
    
    // Standard Setter
    public void SetD46BargainDateDd(string value)
    {
        _D46BargainDateDd = value;
    }
    
    // Get<>AsString()
    public string GetD46BargainDateDdAsString()
    {
        return _D46BargainDateDd.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD46BargainDateDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D46BargainDateDd = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetD46SettlementDate(string value)
{
    _D46SettlementDate.SetD46SettlementDateAsString(value);
}
// Nested Class: D46SettlementDate
public class D46SettlementDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D46SettlementDateYy, is_external=, is_static_class=False, static_prefix=
    private string _D46SettlementDateYy ="";
    
    
    
    
    // [DEBUG] Field: D46SettlementDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D46SettlementDateMm ="";
    
    
    
    
    // [DEBUG] Field: D46SettlementDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D46SettlementDateDd ="";
    
    
    
    
public D46SettlementDate() {}

public D46SettlementDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD46SettlementDateYy(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD46SettlementDateMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD46SettlementDateDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD46SettlementDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D46SettlementDateYy.PadRight(2));
    result.Append(_D46SettlementDateMm.PadRight(2));
    result.Append(_D46SettlementDateDd.PadRight(2));
    
    return result.ToString();
}

public void SetD46SettlementDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD46SettlementDateYy(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD46SettlementDateMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD46SettlementDateDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD46SettlementDateYy()
{
    return _D46SettlementDateYy;
}

// Standard Setter
public void SetD46SettlementDateYy(string value)
{
    _D46SettlementDateYy = value;
}

// Get<>AsString()
public string GetD46SettlementDateYyAsString()
{
    return _D46SettlementDateYy.PadRight(2);
}

// Set<>AsString()
public void SetD46SettlementDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D46SettlementDateYy = value;
}

// Standard Getter
public string GetD46SettlementDateMm()
{
    return _D46SettlementDateMm;
}

// Standard Setter
public void SetD46SettlementDateMm(string value)
{
    _D46SettlementDateMm = value;
}

// Get<>AsString()
public string GetD46SettlementDateMmAsString()
{
    return _D46SettlementDateMm.PadRight(2);
}

// Set<>AsString()
public void SetD46SettlementDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D46SettlementDateMm = value;
}

// Standard Getter
public string GetD46SettlementDateDd()
{
    return _D46SettlementDateDd;
}

// Standard Setter
public void SetD46SettlementDateDd(string value)
{
    _D46SettlementDateDd = value;
}

// Get<>AsString()
public string GetD46SettlementDateDdAsString()
{
    return _D46SettlementDateDd.PadRight(2);
}

// Set<>AsString()
public void SetD46SettlementDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D46SettlementDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}

}}