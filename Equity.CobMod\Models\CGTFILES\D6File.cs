using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D6File Data Structure

public class D6File
{
    private static int _size = 12;
    // [DEBUG] Class: D6File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler161, is_external=, is_static_class=False, static_prefix=
    private string _Filler161 ="$";
    
    
    
    
    // [DEBUG] Field: D6UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D6UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler162, is_external=, is_static_class=False, static_prefix=
    private string _Filler162 ="R05.REP";
    
    
    
    
    
    // Serialization methods
    public string GetD6FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler161.PadRight(1));
        result.Append(_D6UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler162.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD6FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller161(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD6UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller162(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD6FileAsString();
    }
    // Set<>String Override function
    public void SetD6File(string value)
    {
        SetD6FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller161()
    {
        return _Filler161;
    }
    
    // Standard Setter
    public void SetFiller161(string value)
    {
        _Filler161 = value;
    }
    
    // Get<>AsString()
    public string GetFiller161AsString()
    {
        return _Filler161.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller161AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler161 = value;
    }
    
    // Standard Getter
    public int GetD6UserNo()
    {
        return _D6UserNo;
    }
    
    // Standard Setter
    public void SetD6UserNo(int value)
    {
        _D6UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD6UserNoAsString()
    {
        return _D6UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD6UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D6UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller162()
    {
        return _Filler162;
    }
    
    // Standard Setter
    public void SetFiller162(string value)
    {
        _Filler162 = value;
    }
    
    // Get<>AsString()
    public string GetFiller162AsString()
    {
        return _Filler162.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller162AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler162 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}