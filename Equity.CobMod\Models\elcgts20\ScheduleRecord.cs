using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing ScheduleRecord Data Structure

public class ScheduleRecord
{
    private static int _size = 1064;
    // [DEBUG] Class: ScheduleRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: SSort, is_external=, is_static_class=False, static_prefix=
    private SSort _SSort = new SSort();
    
    
    
    
    // [DEBUG] Field: SRecordType, is_external=, is_static_class=False, static_prefix=
    private string _SRecordType ="";
    
    
    // 88-level condition checks for SRecordType
    public bool IsSSedolRecord()
    {
        if (this._SRecordType == "'1'") return true;
        return false;
    }
    public bool IsSDetailRecord()
    {
        if (this._SRecordType == "'2'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: SData, is_external=, is_static_class=False, static_prefix=
    private SData _SData = new SData();
    
    
    
    
    // [DEBUG] Field: SSedolData, is_external=, is_static_class=False, static_prefix=
    private SSedolData _SSedolData = new SSedolData();
    
    
    
    
    // [DEBUG] Field: STrancheDetailData, is_external=, is_static_class=False, static_prefix=
    private STrancheDetailData _STrancheDetailData = new STrancheDetailData();
    
    
    
    
    
    // Serialization methods
    public string GetScheduleRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_SSort.GetSSortAsString());
        result.Append(_SRecordType.PadRight(1));
        result.Append(_SData.GetSDataAsString());
        result.Append(_SSedolData.GetSSedolDataAsString());
        result.Append(_STrancheDetailData.GetSTrancheDetailDataAsString());
        
        return result.ToString();
    }
    
    public void SetScheduleRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 34 <= data.Length)
        {
            _SSort.SetSSortAsString(data.Substring(offset, 34));
        }
        else
        {
            _SSort.SetSSortAsString(data.Substring(offset));
        }
        offset += 34;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetSRecordType(extracted);
        }
        offset += 1;
        if (offset + 130 <= data.Length)
        {
            _SData.SetSDataAsString(data.Substring(offset, 130));
        }
        else
        {
            _SData.SetSDataAsString(data.Substring(offset));
        }
        offset += 130;
        if (offset + 154 <= data.Length)
        {
            _SSedolData.SetSSedolDataAsString(data.Substring(offset, 154));
        }
        else
        {
            _SSedolData.SetSSedolDataAsString(data.Substring(offset));
        }
        offset += 154;
        if (offset + 745 <= data.Length)
        {
            _STrancheDetailData.SetSTrancheDetailDataAsString(data.Substring(offset, 745));
        }
        else
        {
            _STrancheDetailData.SetSTrancheDetailDataAsString(data.Substring(offset));
        }
        offset += 745;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetScheduleRecordAsString();
    }
    // Set<>String Override function
    public void SetScheduleRecord(string value)
    {
        SetScheduleRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public SSort GetSSort()
    {
        return _SSort;
    }
    
    // Standard Setter
    public void SetSSort(SSort value)
    {
        _SSort = value;
    }
    
    // Get<>AsString()
    public string GetSSortAsString()
    {
        return _SSort != null ? _SSort.GetSSortAsString() : "";
    }
    
    // Set<>AsString()
    public void SetSSortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_SSort == null)
        {
            _SSort = new SSort();
        }
        _SSort.SetSSortAsString(value);
    }
    
    // Standard Getter
    public string GetSRecordType()
    {
        return _SRecordType;
    }
    
    // Standard Setter
    public void SetSRecordType(string value)
    {
        _SRecordType = value;
    }
    
    // Get<>AsString()
    public string GetSRecordTypeAsString()
    {
        return _SRecordType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetSRecordTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SRecordType = value;
    }
    
    // Standard Getter
    public SData GetSData()
    {
        return _SData;
    }
    
    // Standard Setter
    public void SetSData(SData value)
    {
        _SData = value;
    }
    
    // Get<>AsString()
    public string GetSDataAsString()
    {
        return _SData != null ? _SData.GetSDataAsString() : "";
    }
    
    // Set<>AsString()
    public void SetSDataAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_SData == null)
        {
            _SData = new SData();
        }
        _SData.SetSDataAsString(value);
    }
    
    // Standard Getter
    public SSedolData GetSSedolData()
    {
        return _SSedolData;
    }
    
    // Standard Setter
    public void SetSSedolData(SSedolData value)
    {
        _SSedolData = value;
    }
    
    // Get<>AsString()
    public string GetSSedolDataAsString()
    {
        return _SSedolData != null ? _SSedolData.GetSSedolDataAsString() : "";
    }
    
    // Set<>AsString()
    public void SetSSedolDataAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_SSedolData == null)
        {
            _SSedolData = new SSedolData();
        }
        _SSedolData.SetSSedolDataAsString(value);
    }
    
    // Standard Getter
    public STrancheDetailData GetSTrancheDetailData()
    {
        return _STrancheDetailData;
    }
    
    // Standard Setter
    public void SetSTrancheDetailData(STrancheDetailData value)
    {
        _STrancheDetailData = value;
    }
    
    // Get<>AsString()
    public string GetSTrancheDetailDataAsString()
    {
        return _STrancheDetailData != null ? _STrancheDetailData.GetSTrancheDetailDataAsString() : "";
    }
    
    // Set<>AsString()
    public void SetSTrancheDetailDataAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_STrancheDetailData == null)
        {
            _STrancheDetailData = new STrancheDetailData();
        }
        _STrancheDetailData.SetSTrancheDetailDataAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetSSort(string value)
    {
        _SSort.SetSSortAsString(value);
    }
    // Nested Class: SSort
    public class SSort
    {
        private static int _size = 34;
        
        // Fields in the class
        
        
        // [DEBUG] Field: SCurrencySort, is_external=, is_static_class=False, static_prefix=
        private int _SCurrencySort =0;
        
        
        // 88-level condition checks for SCurrencySort
        public bool IsSCurrencySterling()
        {
            if (this._SCurrencySort == 0) return true;
            return false;
        }
        public bool IsSCurrencyEuro()
        {
            if (this._SCurrencySort == 1) return true;
            return false;
        }
        
        
        // [DEBUG] Field: SCoAcLk, is_external=, is_static_class=False, static_prefix=
        private string _SCoAcLk ="";
        
        
        
        
        // [DEBUG] Field: SCountryCode, is_external=, is_static_class=False, static_prefix=
        private string _SCountryCode ="";
        
        
        
        
        // [DEBUG] Field: SMainGroup, is_external=, is_static_class=False, static_prefix=
        private string _SMainGroup ="";
        
        
        
        
        // [DEBUG] Field: SSecurityType, is_external=, is_static_class=False, static_prefix=
        private string _SSecurityType ="";
        
        
        // 88-level condition checks for SSecurityType
        public bool IsSShortFuture()
        {
            if (this._SSecurityType == "'G'") return true;
            return false;
        }
        public bool IsSLongFuture()
        {
            if (this._SSecurityType == "'H'") return true;
            return false;
        }
        public bool IsSWrittenCall()
        {
            if (this._SSecurityType == "'K'") return true;
            return false;
        }
        public bool IsSWrittenPut()
        {
            if (this._SSecurityType == "'L'") return true;
            return false;
        }
        public bool IsSShortWrittenDerivative()
        {
            if (this._SSecurityType == "'G'") return true;
            if (this._SSecurityType == "'K'") return true;
            if (this._SSecurityType == "'L'") return true;
            return false;
        }
        public bool IsSFutureWrittenDerivative()
        {
            if (this._SSecurityType == "'G'") return true;
            if (this._SSecurityType == "'H'") return true;
            if (this._SSecurityType == "'K'") return true;
            if (this._SSecurityType == "'L'") return true;
            return false;
        }
        public bool IsSPurchasedOption()
        {
            if (this._SSecurityType == "'I'") return true;
            if (this._SSecurityType == "'J'") return true;
            return false;
        }
        public bool IsSWrittenOption()
        {
            if (this._SSecurityType == "'K'") return true;
            if (this._SSecurityType == "'L'") return true;
            return false;
        }
        public bool IsSDerivative()
        {
            if (!string.IsNullOrEmpty(this.GetSSecurityType()) && this.GetSSecurityType().Length == 1)
            {
                var ch = this.GetSSecurityType()[0];
                if (ch >= 'G' && ch <= 'L') return true;
            }
            return false;
        }
        
        
        // [DEBUG] Field: SSecuritySortCode, is_external=, is_static_class=False, static_prefix=
        private string _SSecuritySortCode ="";
        
        
        
        
        // [DEBUG] Field: SSedolSort, is_external=, is_static_class=False, static_prefix=
        private string _SSedolSort ="";
        
        
        
        
    public SSort() {}
    
    public SSort(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetSCurrencySort(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetSCoAcLk(data.Substring(offset, 4).Trim());
        offset += 4;
        SetSCountryCode(data.Substring(offset, 3).Trim());
        offset += 3;
        SetSMainGroup(data.Substring(offset, 3).Trim());
        offset += 3;
        SetSSecurityType(data.Substring(offset, 1).Trim());
        offset += 1;
        SetSSecuritySortCode(data.Substring(offset, 15).Trim());
        offset += 15;
        SetSSedolSort(data.Substring(offset, 7).Trim());
        offset += 7;
        
    }
    
    // Serialization methods
    public string GetSSortAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_SCurrencySort.ToString().PadLeft(1, '0'));
        result.Append(_SCoAcLk.PadRight(4));
        result.Append(_SCountryCode.PadRight(3));
        result.Append(_SMainGroup.PadRight(3));
        result.Append(_SSecurityType.PadRight(1));
        result.Append(_SSecuritySortCode.PadRight(15));
        result.Append(_SSedolSort.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetSSortAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetSCurrencySort(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetSCoAcLk(extracted);
        }
        offset += 4;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetSCountryCode(extracted);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetSMainGroup(extracted);
        }
        offset += 3;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetSSecurityType(extracted);
        }
        offset += 1;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            SetSSecuritySortCode(extracted);
        }
        offset += 15;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetSSedolSort(extracted);
        }
        offset += 7;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetSCurrencySort()
    {
        return _SCurrencySort;
    }
    
    // Standard Setter
    public void SetSCurrencySort(int value)
    {
        _SCurrencySort = value;
    }
    
    // Get<>AsString()
    public string GetSCurrencySortAsString()
    {
        return _SCurrencySort.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetSCurrencySortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _SCurrencySort = parsed;
    }
    
    // Standard Getter
    public string GetSCoAcLk()
    {
        return _SCoAcLk;
    }
    
    // Standard Setter
    public void SetSCoAcLk(string value)
    {
        _SCoAcLk = value;
    }
    
    // Get<>AsString()
    public string GetSCoAcLkAsString()
    {
        return _SCoAcLk.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetSCoAcLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SCoAcLk = value;
    }
    
    // Standard Getter
    public string GetSCountryCode()
    {
        return _SCountryCode;
    }
    
    // Standard Setter
    public void SetSCountryCode(string value)
    {
        _SCountryCode = value;
    }
    
    // Get<>AsString()
    public string GetSCountryCodeAsString()
    {
        return _SCountryCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetSCountryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SCountryCode = value;
    }
    
    // Standard Getter
    public string GetSMainGroup()
    {
        return _SMainGroup;
    }
    
    // Standard Setter
    public void SetSMainGroup(string value)
    {
        _SMainGroup = value;
    }
    
    // Get<>AsString()
    public string GetSMainGroupAsString()
    {
        return _SMainGroup.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetSMainGroupAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SMainGroup = value;
    }
    
    // Standard Getter
    public string GetSSecurityType()
    {
        return _SSecurityType;
    }
    
    // Standard Setter
    public void SetSSecurityType(string value)
    {
        _SSecurityType = value;
    }
    
    // Get<>AsString()
    public string GetSSecurityTypeAsString()
    {
        return _SSecurityType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetSSecurityTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SSecurityType = value;
    }
    
    // Standard Getter
    public string GetSSecuritySortCode()
    {
        return _SSecuritySortCode;
    }
    
    // Standard Setter
    public void SetSSecuritySortCode(string value)
    {
        _SSecuritySortCode = value;
    }
    
    // Get<>AsString()
    public string GetSSecuritySortCodeAsString()
    {
        return _SSecuritySortCode.PadRight(15);
    }
    
    // Set<>AsString()
    public void SetSSecuritySortCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SSecuritySortCode = value;
    }
    
    // Standard Getter
    public string GetSSedolSort()
    {
        return _SSedolSort;
    }
    
    // Standard Setter
    public void SetSSedolSort(string value)
    {
        _SSedolSort = value;
    }
    
    // Get<>AsString()
    public string GetSSedolSortAsString()
    {
        return _SSedolSort.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetSSedolSortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _SSedolSort = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetSData(string value)
{
    _SData.SetSDataAsString(value);
}
// Nested Class: SData
public class SData
{
    private static int _size = 130;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SCompanyName, is_external=, is_static_class=False, static_prefix=
    private string _SCompanyName ="";
    
    
    
    
    // [DEBUG] Field: STitle, is_external=, is_static_class=False, static_prefix=
    private string _STitle ="";
    
    
    
    
    // [DEBUG] Field: Filler117, is_external=, is_static_class=False, static_prefix=
    private string _Filler117 ="";
    
    
    
    
    // [DEBUG] Field: Filler118, is_external=, is_static_class=False, static_prefix=
    private string _Filler118 ="";
    
    
    
    
    // [DEBUG] Field: Filler119, is_external=, is_static_class=False, static_prefix=
    private string _Filler119 ="";
    
    
    
    
    // [DEBUG] Field: Filler120, is_external=, is_static_class=False, static_prefix=
    private string _Filler120 ="";
    
    
    
    
    // [DEBUG] Field: Filler121, is_external=, is_static_class=False, static_prefix=
    private string _Filler121 ="";
    
    
    
    
    // [DEBUG] Field: Filler122, is_external=, is_static_class=False, static_prefix=
    private string _Filler122 ="";
    
    
    
    
    // [DEBUG] Field: Filler123, is_external=, is_static_class=False, static_prefix=
    private string _Filler123 ="";
    
    
    
    
    // [DEBUG] Field: Filler124, is_external=, is_static_class=False, static_prefix=
    private string _Filler124 ="";
    
    
    
    
    // [DEBUG] Field: Filler125, is_external=, is_static_class=False, static_prefix=
    private string _Filler125 ="";
    
    
    
    
    // [DEBUG] Field: Filler126, is_external=, is_static_class=False, static_prefix=
    private string _Filler126 ="";
    
    
    
    
    // [DEBUG] Field: Filler127, is_external=, is_static_class=False, static_prefix=
    private string _Filler127 ="";
    
    
    
    
    // [DEBUG] Field: Filler128, is_external=, is_static_class=False, static_prefix=
    private string _Filler128 ="";
    
    
    
    
    // [DEBUG] Field: Filler129, is_external=, is_static_class=False, static_prefix=
    private string _Filler129 ="";
    
    
    
    
    // [DEBUG] Field: Filler130, is_external=, is_static_class=False, static_prefix=
    private string _Filler130 ="";
    
    
    
    
public SData() {}

public SData(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSCompanyName(data.Substring(offset, 51).Trim());
    offset += 51;
    SetSTitle(data.Substring(offset, 60).Trim());
    offset += 60;
    SetFiller117(data.Substring(offset, 19).Trim());
    offset += 19;
    SetFiller118(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller119(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller120(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller121(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller122(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller123(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller124(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller125(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller126(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller127(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller128(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller129(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller130(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetSDataAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SCompanyName.PadRight(51));
    result.Append(_STitle.PadRight(60));
    result.Append(_Filler117.PadRight(19));
    result.Append(_Filler118.PadRight(0));
    result.Append(_Filler119.PadRight(0));
    result.Append(_Filler120.PadRight(0));
    result.Append(_Filler121.PadRight(0));
    result.Append(_Filler122.PadRight(0));
    result.Append(_Filler123.PadRight(0));
    result.Append(_Filler124.PadRight(0));
    result.Append(_Filler125.PadRight(0));
    result.Append(_Filler126.PadRight(0));
    result.Append(_Filler127.PadRight(0));
    result.Append(_Filler128.PadRight(0));
    result.Append(_Filler129.PadRight(0));
    result.Append(_Filler130.PadRight(0));
    
    return result.ToString();
}

public void SetSDataAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 51 <= data.Length)
    {
        string extracted = data.Substring(offset, 51).Trim();
        SetSCompanyName(extracted);
    }
    offset += 51;
    if (offset + 60 <= data.Length)
    {
        string extracted = data.Substring(offset, 60).Trim();
        SetSTitle(extracted);
    }
    offset += 60;
    if (offset + 19 <= data.Length)
    {
        string extracted = data.Substring(offset, 19).Trim();
        SetFiller117(extracted);
    }
    offset += 19;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller118(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller119(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller120(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller121(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller122(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller123(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller124(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller125(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller126(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller127(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller128(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller129(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller130(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetSCompanyName()
{
    return _SCompanyName;
}

// Standard Setter
public void SetSCompanyName(string value)
{
    _SCompanyName = value;
}

// Get<>AsString()
public string GetSCompanyNameAsString()
{
    return _SCompanyName.PadRight(51);
}

// Set<>AsString()
public void SetSCompanyNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SCompanyName = value;
}

// Standard Getter
public string GetSTitle()
{
    return _STitle;
}

// Standard Setter
public void SetSTitle(string value)
{
    _STitle = value;
}

// Get<>AsString()
public string GetSTitleAsString()
{
    return _STitle.PadRight(60);
}

// Set<>AsString()
public void SetSTitleAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STitle = value;
}

// Standard Getter
public string GetFiller117()
{
    return _Filler117;
}

// Standard Setter
public void SetFiller117(string value)
{
    _Filler117 = value;
}

// Get<>AsString()
public string GetFiller117AsString()
{
    return _Filler117.PadRight(19);
}

// Set<>AsString()
public void SetFiller117AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler117 = value;
}

// Standard Getter
public string GetFiller118()
{
    return _Filler118;
}

// Standard Setter
public void SetFiller118(string value)
{
    _Filler118 = value;
}

// Get<>AsString()
public string GetFiller118AsString()
{
    return _Filler118.PadRight(0);
}

// Set<>AsString()
public void SetFiller118AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler118 = value;
}

// Standard Getter
public string GetFiller119()
{
    return _Filler119;
}

// Standard Setter
public void SetFiller119(string value)
{
    _Filler119 = value;
}

// Get<>AsString()
public string GetFiller119AsString()
{
    return _Filler119.PadRight(0);
}

// Set<>AsString()
public void SetFiller119AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler119 = value;
}

// Standard Getter
public string GetFiller120()
{
    return _Filler120;
}

// Standard Setter
public void SetFiller120(string value)
{
    _Filler120 = value;
}

// Get<>AsString()
public string GetFiller120AsString()
{
    return _Filler120.PadRight(0);
}

// Set<>AsString()
public void SetFiller120AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler120 = value;
}

// Standard Getter
public string GetFiller121()
{
    return _Filler121;
}

// Standard Setter
public void SetFiller121(string value)
{
    _Filler121 = value;
}

// Get<>AsString()
public string GetFiller121AsString()
{
    return _Filler121.PadRight(0);
}

// Set<>AsString()
public void SetFiller121AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler121 = value;
}

// Standard Getter
public string GetFiller122()
{
    return _Filler122;
}

// Standard Setter
public void SetFiller122(string value)
{
    _Filler122 = value;
}

// Get<>AsString()
public string GetFiller122AsString()
{
    return _Filler122.PadRight(0);
}

// Set<>AsString()
public void SetFiller122AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler122 = value;
}

// Standard Getter
public string GetFiller123()
{
    return _Filler123;
}

// Standard Setter
public void SetFiller123(string value)
{
    _Filler123 = value;
}

// Get<>AsString()
public string GetFiller123AsString()
{
    return _Filler123.PadRight(0);
}

// Set<>AsString()
public void SetFiller123AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler123 = value;
}

// Standard Getter
public string GetFiller124()
{
    return _Filler124;
}

// Standard Setter
public void SetFiller124(string value)
{
    _Filler124 = value;
}

// Get<>AsString()
public string GetFiller124AsString()
{
    return _Filler124.PadRight(0);
}

// Set<>AsString()
public void SetFiller124AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler124 = value;
}

// Standard Getter
public string GetFiller125()
{
    return _Filler125;
}

// Standard Setter
public void SetFiller125(string value)
{
    _Filler125 = value;
}

// Get<>AsString()
public string GetFiller125AsString()
{
    return _Filler125.PadRight(0);
}

// Set<>AsString()
public void SetFiller125AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler125 = value;
}

// Standard Getter
public string GetFiller126()
{
    return _Filler126;
}

// Standard Setter
public void SetFiller126(string value)
{
    _Filler126 = value;
}

// Get<>AsString()
public string GetFiller126AsString()
{
    return _Filler126.PadRight(0);
}

// Set<>AsString()
public void SetFiller126AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler126 = value;
}

// Standard Getter
public string GetFiller127()
{
    return _Filler127;
}

// Standard Setter
public void SetFiller127(string value)
{
    _Filler127 = value;
}

// Get<>AsString()
public string GetFiller127AsString()
{
    return _Filler127.PadRight(0);
}

// Set<>AsString()
public void SetFiller127AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler127 = value;
}

// Standard Getter
public string GetFiller128()
{
    return _Filler128;
}

// Standard Setter
public void SetFiller128(string value)
{
    _Filler128 = value;
}

// Get<>AsString()
public string GetFiller128AsString()
{
    return _Filler128.PadRight(0);
}

// Set<>AsString()
public void SetFiller128AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler128 = value;
}

// Standard Getter
public string GetFiller129()
{
    return _Filler129;
}

// Standard Setter
public void SetFiller129(string value)
{
    _Filler129 = value;
}

// Get<>AsString()
public string GetFiller129AsString()
{
    return _Filler129.PadRight(0);
}

// Set<>AsString()
public void SetFiller129AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler129 = value;
}

// Standard Getter
public string GetFiller130()
{
    return _Filler130;
}

// Standard Setter
public void SetFiller130(string value)
{
    _Filler130 = value;
}

// Get<>AsString()
public string GetFiller130AsString()
{
    return _Filler130.PadRight(0);
}

// Set<>AsString()
public void SetFiller130AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler130 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetSSedolData(string value)
{
    _SSedolData.SetSSedolDataAsString(value);
}
// Nested Class: SSedolData
public class SSedolData
{
    private static int _size = 154;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler131, is_external=, is_static_class=False, static_prefix=
    private string _Filler131 ="";
    
    
    
    
    // [DEBUG] Field: SSedolNumber, is_external=, is_static_class=False, static_prefix=
    private string _SSedolNumber ="";
    
    
    
    
    // [DEBUG] Field: SIssuersName, is_external=, is_static_class=False, static_prefix=
    private SSedolData.SIssuersName _SIssuersName = new SSedolData.SIssuersName();
    
    
    
    
    // [DEBUG] Field: SStockDescription, is_external=, is_static_class=False, static_prefix=
    private SSedolData.SStockDescription _SStockDescription = new SSedolData.SStockDescription();
    
    
    
    
    // [DEBUG] Field: SLastTrancheContractNo, is_external=, is_static_class=False, static_prefix=
    private string _SLastTrancheContractNo ="";
    
    
    
    
    // [DEBUG] Field: SProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _SProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: SSedolRecordCount, is_external=, is_static_class=False, static_prefix=
    private int _SSedolRecordCount =0;
    
    
    
    
    // [DEBUG] Field: STrancheCount, is_external=, is_static_class=False, static_prefix=
    private int _STrancheCount =0;
    
    
    
    
    // [DEBUG] Field: SPrintFlag, is_external=, is_static_class=False, static_prefix=
    private string _SPrintFlag ="";
    
    
    
    
    // [DEBUG] Field: SHoldingFlag, is_external=, is_static_class=False, static_prefix=
    private string _SHoldingFlag ="";
    
    
    
    
    // [DEBUG] Field: Filler132, is_external=, is_static_class=False, static_prefix=
    private string _Filler132 ="";
    
    
    
    
    // [DEBUG] Field: Filler133, is_external=, is_static_class=False, static_prefix=
    private string _Filler133 ="";
    
    
    
    
    // [DEBUG] Field: SReitSecurityType, is_external=, is_static_class=False, static_prefix=
    private string _SReitSecurityType ="";
    
    
    
    
    // [DEBUG] Field: SQuotedIndicator, is_external=, is_static_class=False, static_prefix=
    private string _SQuotedIndicator ="";
    
    
    
    
    // [DEBUG] Field: Filler134, is_external=, is_static_class=False, static_prefix=
    private string _Filler134 ="";
    
    
    
    
    // [DEBUG] Field: SLostIndexation, is_external=, is_static_class=False, static_prefix=
    private decimal _SLostIndexation =0;
    
    
    
    
    // [DEBUG] Field: SOffshoreIncomeGain, is_external=, is_static_class=False, static_prefix=
    private decimal _SOffshoreIncomeGain =0;
    
    
    
    
    // [DEBUG] Field: Filler135, is_external=, is_static_class=False, static_prefix=
    private string _Filler135 ="";
    
    
    
    
    // [DEBUG] Field: Filler136, is_external=, is_static_class=False, static_prefix=
    private string _Filler136 ="";
    
    
    
    
    // [DEBUG] Field: Filler137, is_external=, is_static_class=False, static_prefix=
    private string _Filler137 ="";
    
    
    
    
    // [DEBUG] Field: Filler138, is_external=, is_static_class=False, static_prefix=
    private string _Filler138 ="";
    
    
    
    
    // [DEBUG] Field: Filler139, is_external=, is_static_class=False, static_prefix=
    private string _Filler139 ="";
    
    
    
    
    // [DEBUG] Field: Filler140, is_external=, is_static_class=False, static_prefix=
    private string _Filler140 ="";
    
    
    
    
    // [DEBUG] Field: Filler141, is_external=, is_static_class=False, static_prefix=
    private string _Filler141 ="";
    
    
    
    
public SSedolData() {}

public SSedolData(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller131(data.Substring(offset, 19).Trim());
    offset += 19;
    SetSSedolNumber(data.Substring(offset, 7).Trim());
    offset += 7;
    _SIssuersName.SetSIssuersNameAsString(data.Substring(offset, SIssuersName.GetSize()));
    offset += 35;
    _SStockDescription.SetSStockDescriptionAsString(data.Substring(offset, SStockDescription.GetSize()));
    offset += 40;
    SetSLastTrancheContractNo(data.Substring(offset, 10).Trim());
    offset += 10;
    SetSProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
    offset += 11;
    SetSSedolRecordCount(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetSTrancheCount(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetSPrintFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetSHoldingFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetFiller132(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller133(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSReitSecurityType(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSQuotedIndicator(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller134(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSLostIndexation(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSOffshoreIncomeGain(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetFiller135(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller136(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller137(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller138(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller139(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller140(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller141(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetSSedolDataAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler131.PadRight(19));
    result.Append(_SSedolNumber.PadRight(7));
    result.Append(_SIssuersName.GetSIssuersNameAsString());
    result.Append(_SStockDescription.GetSStockDescriptionAsString());
    result.Append(_SLastTrancheContractNo.PadRight(10));
    result.Append(_SProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SSedolRecordCount.ToString().PadLeft(3, '0'));
    result.Append(_STrancheCount.ToString().PadLeft(3, '0'));
    result.Append(_SPrintFlag.PadRight(1));
    result.Append(_SHoldingFlag.PadRight(1));
    result.Append(_Filler132.PadRight(0));
    result.Append(_Filler133.PadRight(0));
    result.Append(_SReitSecurityType.PadRight(0));
    result.Append(_SQuotedIndicator.PadRight(0));
    result.Append(_Filler134.PadRight(0));
    result.Append(_SLostIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SOffshoreIncomeGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler135.PadRight(0));
    result.Append(_Filler136.PadRight(0));
    result.Append(_Filler137.PadRight(0));
    result.Append(_Filler138.PadRight(0));
    result.Append(_Filler139.PadRight(0));
    result.Append(_Filler140.PadRight(0));
    result.Append(_Filler141.PadRight(0));
    
    return result.ToString();
}

public void SetSSedolDataAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 19 <= data.Length)
    {
        string extracted = data.Substring(offset, 19).Trim();
        SetFiller131(extracted);
    }
    offset += 19;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetSSedolNumber(extracted);
    }
    offset += 7;
    if (offset + 35 <= data.Length)
    {
        _SIssuersName.SetSIssuersNameAsString(data.Substring(offset, 35));
    }
    else
    {
        _SIssuersName.SetSIssuersNameAsString(data.Substring(offset));
    }
    offset += 35;
    if (offset + 40 <= data.Length)
    {
        _SStockDescription.SetSStockDescriptionAsString(data.Substring(offset, 40));
    }
    else
    {
        _SStockDescription.SetSStockDescriptionAsString(data.Substring(offset));
    }
    offset += 40;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetSLastTrancheContractNo(extracted);
    }
    offset += 10;
    if (offset + 11 <= data.Length)
    {
        string extracted = data.Substring(offset, 11).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSProfitLoss(parsedDec);
    }
    offset += 11;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSSedolRecordCount(parsedInt);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSTrancheCount(parsedInt);
    }
    offset += 3;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetSPrintFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetSHoldingFlag(extracted);
    }
    offset += 1;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller132(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller133(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSReitSecurityType(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSQuotedIndicator(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller134(extracted);
    }
    offset += 0;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSLostIndexation(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSOffshoreIncomeGain(parsedDec);
    }
    offset += 12;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller135(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller136(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller137(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller138(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller139(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller140(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller141(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller131()
{
    return _Filler131;
}

// Standard Setter
public void SetFiller131(string value)
{
    _Filler131 = value;
}

// Get<>AsString()
public string GetFiller131AsString()
{
    return _Filler131.PadRight(19);
}

// Set<>AsString()
public void SetFiller131AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler131 = value;
}

// Standard Getter
public string GetSSedolNumber()
{
    return _SSedolNumber;
}

// Standard Setter
public void SetSSedolNumber(string value)
{
    _SSedolNumber = value;
}

// Get<>AsString()
public string GetSSedolNumberAsString()
{
    return _SSedolNumber.PadRight(7);
}

// Set<>AsString()
public void SetSSedolNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SSedolNumber = value;
}

// Standard Getter
public SIssuersName GetSIssuersName()
{
    return _SIssuersName;
}

// Standard Setter
public void SetSIssuersName(SIssuersName value)
{
    _SIssuersName = value;
}

// Get<>AsString()
public string GetSIssuersNameAsString()
{
    return _SIssuersName != null ? _SIssuersName.GetSIssuersNameAsString() : "";
}

// Set<>AsString()
public void SetSIssuersNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_SIssuersName == null)
    {
        _SIssuersName = new SIssuersName();
    }
    _SIssuersName.SetSIssuersNameAsString(value);
}

// Standard Getter
public SStockDescription GetSStockDescription()
{
    return _SStockDescription;
}

// Standard Setter
public void SetSStockDescription(SStockDescription value)
{
    _SStockDescription = value;
}

// Get<>AsString()
public string GetSStockDescriptionAsString()
{
    return _SStockDescription != null ? _SStockDescription.GetSStockDescriptionAsString() : "";
}

// Set<>AsString()
public void SetSStockDescriptionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_SStockDescription == null)
    {
        _SStockDescription = new SStockDescription();
    }
    _SStockDescription.SetSStockDescriptionAsString(value);
}

// Standard Getter
public string GetSLastTrancheContractNo()
{
    return _SLastTrancheContractNo;
}

// Standard Setter
public void SetSLastTrancheContractNo(string value)
{
    _SLastTrancheContractNo = value;
}

// Get<>AsString()
public string GetSLastTrancheContractNoAsString()
{
    return _SLastTrancheContractNo.PadRight(10);
}

// Set<>AsString()
public void SetSLastTrancheContractNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SLastTrancheContractNo = value;
}

// Standard Getter
public decimal GetSProfitLoss()
{
    return _SProfitLoss;
}

// Standard Setter
public void SetSProfitLoss(decimal value)
{
    _SProfitLoss = value;
}

// Get<>AsString()
public string GetSProfitLossAsString()
{
    return _SProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSProfitLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SProfitLoss = parsed;
}

// Standard Getter
public int GetSSedolRecordCount()
{
    return _SSedolRecordCount;
}

// Standard Setter
public void SetSSedolRecordCount(int value)
{
    _SSedolRecordCount = value;
}

// Get<>AsString()
public string GetSSedolRecordCountAsString()
{
    return _SSedolRecordCount.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetSSedolRecordCountAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SSedolRecordCount = parsed;
}

// Standard Getter
public int GetSTrancheCount()
{
    return _STrancheCount;
}

// Standard Setter
public void SetSTrancheCount(int value)
{
    _STrancheCount = value;
}

// Get<>AsString()
public string GetSTrancheCountAsString()
{
    return _STrancheCount.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetSTrancheCountAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _STrancheCount = parsed;
}

// Standard Getter
public string GetSPrintFlag()
{
    return _SPrintFlag;
}

// Standard Setter
public void SetSPrintFlag(string value)
{
    _SPrintFlag = value;
}

// Get<>AsString()
public string GetSPrintFlagAsString()
{
    return _SPrintFlag.PadRight(1);
}

// Set<>AsString()
public void SetSPrintFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SPrintFlag = value;
}

// Standard Getter
public string GetSHoldingFlag()
{
    return _SHoldingFlag;
}

// Standard Setter
public void SetSHoldingFlag(string value)
{
    _SHoldingFlag = value;
}

// Get<>AsString()
public string GetSHoldingFlagAsString()
{
    return _SHoldingFlag.PadRight(1);
}

// Set<>AsString()
public void SetSHoldingFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SHoldingFlag = value;
}

// Standard Getter
public string GetFiller132()
{
    return _Filler132;
}

// Standard Setter
public void SetFiller132(string value)
{
    _Filler132 = value;
}

// Get<>AsString()
public string GetFiller132AsString()
{
    return _Filler132.PadRight(0);
}

// Set<>AsString()
public void SetFiller132AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler132 = value;
}

// Standard Getter
public string GetFiller133()
{
    return _Filler133;
}

// Standard Setter
public void SetFiller133(string value)
{
    _Filler133 = value;
}

// Get<>AsString()
public string GetFiller133AsString()
{
    return _Filler133.PadRight(0);
}

// Set<>AsString()
public void SetFiller133AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler133 = value;
}

// Standard Getter
public string GetSReitSecurityType()
{
    return _SReitSecurityType;
}

// Standard Setter
public void SetSReitSecurityType(string value)
{
    _SReitSecurityType = value;
}

// Get<>AsString()
public string GetSReitSecurityTypeAsString()
{
    return _SReitSecurityType.PadRight(0);
}

// Set<>AsString()
public void SetSReitSecurityTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SReitSecurityType = value;
}

// Standard Getter
public string GetSQuotedIndicator()
{
    return _SQuotedIndicator;
}

// Standard Setter
public void SetSQuotedIndicator(string value)
{
    _SQuotedIndicator = value;
}

// Get<>AsString()
public string GetSQuotedIndicatorAsString()
{
    return _SQuotedIndicator.PadRight(0);
}

// Set<>AsString()
public void SetSQuotedIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SQuotedIndicator = value;
}

// Standard Getter
public string GetFiller134()
{
    return _Filler134;
}

// Standard Setter
public void SetFiller134(string value)
{
    _Filler134 = value;
}

// Get<>AsString()
public string GetFiller134AsString()
{
    return _Filler134.PadRight(0);
}

// Set<>AsString()
public void SetFiller134AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler134 = value;
}

// Standard Getter
public decimal GetSLostIndexation()
{
    return _SLostIndexation;
}

// Standard Setter
public void SetSLostIndexation(decimal value)
{
    _SLostIndexation = value;
}

// Get<>AsString()
public string GetSLostIndexationAsString()
{
    return _SLostIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSLostIndexationAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SLostIndexation = parsed;
}

// Standard Getter
public decimal GetSOffshoreIncomeGain()
{
    return _SOffshoreIncomeGain;
}

// Standard Setter
public void SetSOffshoreIncomeGain(decimal value)
{
    _SOffshoreIncomeGain = value;
}

// Get<>AsString()
public string GetSOffshoreIncomeGainAsString()
{
    return _SOffshoreIncomeGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSOffshoreIncomeGainAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SOffshoreIncomeGain = parsed;
}

// Standard Getter
public string GetFiller135()
{
    return _Filler135;
}

// Standard Setter
public void SetFiller135(string value)
{
    _Filler135 = value;
}

// Get<>AsString()
public string GetFiller135AsString()
{
    return _Filler135.PadRight(0);
}

// Set<>AsString()
public void SetFiller135AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler135 = value;
}

// Standard Getter
public string GetFiller136()
{
    return _Filler136;
}

// Standard Setter
public void SetFiller136(string value)
{
    _Filler136 = value;
}

// Get<>AsString()
public string GetFiller136AsString()
{
    return _Filler136.PadRight(0);
}

// Set<>AsString()
public void SetFiller136AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler136 = value;
}

// Standard Getter
public string GetFiller137()
{
    return _Filler137;
}

// Standard Setter
public void SetFiller137(string value)
{
    _Filler137 = value;
}

// Get<>AsString()
public string GetFiller137AsString()
{
    return _Filler137.PadRight(0);
}

// Set<>AsString()
public void SetFiller137AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler137 = value;
}

// Standard Getter
public string GetFiller138()
{
    return _Filler138;
}

// Standard Setter
public void SetFiller138(string value)
{
    _Filler138 = value;
}

// Get<>AsString()
public string GetFiller138AsString()
{
    return _Filler138.PadRight(0);
}

// Set<>AsString()
public void SetFiller138AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler138 = value;
}

// Standard Getter
public string GetFiller139()
{
    return _Filler139;
}

// Standard Setter
public void SetFiller139(string value)
{
    _Filler139 = value;
}

// Get<>AsString()
public string GetFiller139AsString()
{
    return _Filler139.PadRight(0);
}

// Set<>AsString()
public void SetFiller139AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler139 = value;
}

// Standard Getter
public string GetFiller140()
{
    return _Filler140;
}

// Standard Setter
public void SetFiller140(string value)
{
    _Filler140 = value;
}

// Get<>AsString()
public string GetFiller140AsString()
{
    return _Filler140.PadRight(0);
}

// Set<>AsString()
public void SetFiller140AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler140 = value;
}

// Standard Getter
public string GetFiller141()
{
    return _Filler141;
}

// Standard Setter
public void SetFiller141(string value)
{
    _Filler141 = value;
}

// Get<>AsString()
public string GetFiller141AsString()
{
    return _Filler141.PadRight(0);
}

// Set<>AsString()
public void SetFiller141AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler141 = value;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: SIssuersName
public class SIssuersName
{
    private static int _size = 35;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SIssuersName1, is_external=, is_static_class=False, static_prefix=
    private string _SIssuersName1 ="";
    
    
    
    
    // [DEBUG] Field: SIssuersName2, is_external=, is_static_class=False, static_prefix=
    private string _SIssuersName2 ="";
    
    
    
    
public SIssuersName() {}

public SIssuersName(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSIssuersName1(data.Substring(offset, 20).Trim());
    offset += 20;
    SetSIssuersName2(data.Substring(offset, 15).Trim());
    offset += 15;
    
}

// Serialization methods
public string GetSIssuersNameAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SIssuersName1.PadRight(20));
    result.Append(_SIssuersName2.PadRight(15));
    
    return result.ToString();
}

public void SetSIssuersNameAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetSIssuersName1(extracted);
    }
    offset += 20;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetSIssuersName2(extracted);
    }
    offset += 15;
}

// Getter and Setter methods

// Standard Getter
public string GetSIssuersName1()
{
    return _SIssuersName1;
}

// Standard Setter
public void SetSIssuersName1(string value)
{
    _SIssuersName1 = value;
}

// Get<>AsString()
public string GetSIssuersName1AsString()
{
    return _SIssuersName1.PadRight(20);
}

// Set<>AsString()
public void SetSIssuersName1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SIssuersName1 = value;
}

// Standard Getter
public string GetSIssuersName2()
{
    return _SIssuersName2;
}

// Standard Setter
public void SetSIssuersName2(string value)
{
    _SIssuersName2 = value;
}

// Get<>AsString()
public string GetSIssuersName2AsString()
{
    return _SIssuersName2.PadRight(15);
}

// Set<>AsString()
public void SetSIssuersName2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SIssuersName2 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: SStockDescription
public class SStockDescription
{
    private static int _size = 40;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SStockDescription1, is_external=, is_static_class=False, static_prefix=
    private string _SStockDescription1 ="";
    
    
    
    
    // [DEBUG] Field: SStockDescription2, is_external=, is_static_class=False, static_prefix=
    private string _SStockDescription2 ="";
    
    
    
    
public SStockDescription() {}

public SStockDescription(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSStockDescription1(data.Substring(offset, 20).Trim());
    offset += 20;
    SetSStockDescription2(data.Substring(offset, 20).Trim());
    offset += 20;
    
}

// Serialization methods
public string GetSStockDescriptionAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SStockDescription1.PadRight(20));
    result.Append(_SStockDescription2.PadRight(20));
    
    return result.ToString();
}

public void SetSStockDescriptionAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetSStockDescription1(extracted);
    }
    offset += 20;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetSStockDescription2(extracted);
    }
    offset += 20;
}

// Getter and Setter methods

// Standard Getter
public string GetSStockDescription1()
{
    return _SStockDescription1;
}

// Standard Setter
public void SetSStockDescription1(string value)
{
    _SStockDescription1 = value;
}

// Get<>AsString()
public string GetSStockDescription1AsString()
{
    return _SStockDescription1.PadRight(20);
}

// Set<>AsString()
public void SetSStockDescription1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SStockDescription1 = value;
}

// Standard Getter
public string GetSStockDescription2()
{
    return _SStockDescription2;
}

// Standard Setter
public void SetSStockDescription2(string value)
{
    _SStockDescription2 = value;
}

// Get<>AsString()
public string GetSStockDescription2AsString()
{
    return _SStockDescription2.PadRight(20);
}

// Set<>AsString()
public void SetSStockDescription2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SStockDescription2 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetSTrancheDetailData(string value)
{
    _STrancheDetailData.SetSTrancheDetailDataAsString(value);
}
// Nested Class: STrancheDetailData
public class STrancheDetailData
{
    private static int _size = 745;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SAcquisitionDateX, is_external=, is_static_class=False, static_prefix=
    private string _SAcquisitionDateX ="";
    
    
    
    
    // [DEBUG] Field: SAcquisitionDate9, is_external=, is_static_class=False, static_prefix=
    private STrancheDetailData.SAcquisitionDate9 _SAcquisitionDate9 = new STrancheDetailData.SAcquisitionDate9();
    
    
    
    
    // [DEBUG] Field: STrancheContractNumber, is_external=, is_static_class=False, static_prefix=
    private string _STrancheContractNumber ="";
    
    
    
    
    // [DEBUG] Field: SLineNumber, is_external=, is_static_class=False, static_prefix=
    private int _SLineNumber =0;
    
    
    // 88-level condition checks for SLineNumber
    public bool IsSTrancheHeader()
    {
        if (this._SLineNumber == 00000) return true;
        return false;
    }
    
    
    // [DEBUG] Field: STrancheData, is_external=, is_static_class=False, static_prefix=
    private string _STrancheData ="";
    
    
    
    
    // [DEBUG] Field: STrancheHeaderData, is_external=, is_static_class=False, static_prefix=
    private STrancheDetailData.STrancheHeaderData _STrancheHeaderData = new STrancheDetailData.STrancheHeaderData();
    
    
    
    
    // [DEBUG] Field: STrancheLine, is_external=, is_static_class=False, static_prefix=
    private STrancheDetailData.STrancheLine _STrancheLine = new STrancheDetailData.STrancheLine();
    
    
    
    
public STrancheDetailData() {}

public STrancheDetailData(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSAcquisitionDateX(data.Substring(offset, 6).Trim());
    offset += 6;
    _SAcquisitionDate9.SetSAcquisitionDate9AsString(data.Substring(offset, SAcquisitionDate9.GetSize()));
    offset += 6;
    SetSTrancheContractNumber(data.Substring(offset, 10).Trim());
    offset += 10;
    SetSLineNumber(int.Parse(data.Substring(offset, 5).Trim()));
    offset += 5;
    SetSTrancheData(data.Substring(offset, 233).Trim());
    offset += 233;
    _STrancheHeaderData.SetSTrancheHeaderDataAsString(data.Substring(offset, STrancheHeaderData.GetSize()));
    offset += 110;
    _STrancheLine.SetSTrancheLineAsString(data.Substring(offset, STrancheLine.GetSize()));
    offset += 375;
    
}

// Serialization methods
public string GetSTrancheDetailDataAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SAcquisitionDateX.PadRight(6));
    result.Append(_SAcquisitionDate9.GetSAcquisitionDate9AsString());
    result.Append(_STrancheContractNumber.PadRight(10));
    result.Append(_SLineNumber.ToString().PadLeft(5, '0'));
    result.Append(_STrancheData.PadRight(233));
    result.Append(_STrancheHeaderData.GetSTrancheHeaderDataAsString());
    result.Append(_STrancheLine.GetSTrancheLineAsString());
    
    return result.ToString();
}

public void SetSTrancheDetailDataAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetSAcquisitionDateX(extracted);
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        _SAcquisitionDate9.SetSAcquisitionDate9AsString(data.Substring(offset, 6));
    }
    else
    {
        _SAcquisitionDate9.SetSAcquisitionDate9AsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetSTrancheContractNumber(extracted);
    }
    offset += 10;
    if (offset + 5 <= data.Length)
    {
        string extracted = data.Substring(offset, 5).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSLineNumber(parsedInt);
    }
    offset += 5;
    if (offset + 233 <= data.Length)
    {
        string extracted = data.Substring(offset, 233).Trim();
        SetSTrancheData(extracted);
    }
    offset += 233;
    if (offset + 110 <= data.Length)
    {
        _STrancheHeaderData.SetSTrancheHeaderDataAsString(data.Substring(offset, 110));
    }
    else
    {
        _STrancheHeaderData.SetSTrancheHeaderDataAsString(data.Substring(offset));
    }
    offset += 110;
    if (offset + 375 <= data.Length)
    {
        _STrancheLine.SetSTrancheLineAsString(data.Substring(offset, 375));
    }
    else
    {
        _STrancheLine.SetSTrancheLineAsString(data.Substring(offset));
    }
    offset += 375;
}

// Getter and Setter methods

// Standard Getter
public string GetSAcquisitionDateX()
{
    return _SAcquisitionDateX;
}

// Standard Setter
public void SetSAcquisitionDateX(string value)
{
    _SAcquisitionDateX = value;
}

// Get<>AsString()
public string GetSAcquisitionDateXAsString()
{
    return _SAcquisitionDateX.PadRight(6);
}

// Set<>AsString()
public void SetSAcquisitionDateXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SAcquisitionDateX = value;
}

// Standard Getter
public SAcquisitionDate9 GetSAcquisitionDate9()
{
    return _SAcquisitionDate9;
}

// Standard Setter
public void SetSAcquisitionDate9(SAcquisitionDate9 value)
{
    _SAcquisitionDate9 = value;
}

// Get<>AsString()
public string GetSAcquisitionDate9AsString()
{
    return _SAcquisitionDate9 != null ? _SAcquisitionDate9.GetSAcquisitionDate9AsString() : "";
}

// Set<>AsString()
public void SetSAcquisitionDate9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_SAcquisitionDate9 == null)
    {
        _SAcquisitionDate9 = new SAcquisitionDate9();
    }
    _SAcquisitionDate9.SetSAcquisitionDate9AsString(value);
}

// Standard Getter
public string GetSTrancheContractNumber()
{
    return _STrancheContractNumber;
}

// Standard Setter
public void SetSTrancheContractNumber(string value)
{
    _STrancheContractNumber = value;
}

// Get<>AsString()
public string GetSTrancheContractNumberAsString()
{
    return _STrancheContractNumber.PadRight(10);
}

// Set<>AsString()
public void SetSTrancheContractNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STrancheContractNumber = value;
}

// Standard Getter
public int GetSLineNumber()
{
    return _SLineNumber;
}

// Standard Setter
public void SetSLineNumber(int value)
{
    _SLineNumber = value;
}

// Get<>AsString()
public string GetSLineNumberAsString()
{
    return _SLineNumber.ToString().PadLeft(5, '0');
}

// Set<>AsString()
public void SetSLineNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SLineNumber = parsed;
}

// Standard Getter
public string GetSTrancheData()
{
    return _STrancheData;
}

// Standard Setter
public void SetSTrancheData(string value)
{
    _STrancheData = value;
}

// Get<>AsString()
public string GetSTrancheDataAsString()
{
    return _STrancheData.PadRight(233);
}

// Set<>AsString()
public void SetSTrancheDataAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STrancheData = value;
}

// Standard Getter
public STrancheHeaderData GetSTrancheHeaderData()
{
    return _STrancheHeaderData;
}

// Standard Setter
public void SetSTrancheHeaderData(STrancheHeaderData value)
{
    _STrancheHeaderData = value;
}

// Get<>AsString()
public string GetSTrancheHeaderDataAsString()
{
    return _STrancheHeaderData != null ? _STrancheHeaderData.GetSTrancheHeaderDataAsString() : "";
}

// Set<>AsString()
public void SetSTrancheHeaderDataAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_STrancheHeaderData == null)
    {
        _STrancheHeaderData = new STrancheHeaderData();
    }
    _STrancheHeaderData.SetSTrancheHeaderDataAsString(value);
}

// Standard Getter
public STrancheLine GetSTrancheLine()
{
    return _STrancheLine;
}

// Standard Setter
public void SetSTrancheLine(STrancheLine value)
{
    _STrancheLine = value;
}

// Get<>AsString()
public string GetSTrancheLineAsString()
{
    return _STrancheLine != null ? _STrancheLine.GetSTrancheLineAsString() : "";
}

// Set<>AsString()
public void SetSTrancheLineAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_STrancheLine == null)
    {
        _STrancheLine = new STrancheLine();
    }
    _STrancheLine.SetSTrancheLineAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: SAcquisitionDate9
public class SAcquisitionDate9
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SAcquisitionDateYy, is_external=, is_static_class=False, static_prefix=
    private int _SAcquisitionDateYy =0;
    
    
    
    
    // [DEBUG] Field: SAcquisitionDateMm, is_external=, is_static_class=False, static_prefix=
    private int _SAcquisitionDateMm =0;
    
    
    
    
    // [DEBUG] Field: SAcquisitionDateDd, is_external=, is_static_class=False, static_prefix=
    private int _SAcquisitionDateDd =0;
    
    
    
    
public SAcquisitionDate9() {}

public SAcquisitionDate9(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSAcquisitionDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetSAcquisitionDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetSAcquisitionDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetSAcquisitionDate9AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SAcquisitionDateYy.ToString().PadLeft(2, '0'));
    result.Append(_SAcquisitionDateMm.ToString().PadLeft(2, '0'));
    result.Append(_SAcquisitionDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetSAcquisitionDate9AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSAcquisitionDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSAcquisitionDateMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSAcquisitionDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetSAcquisitionDateYy()
{
    return _SAcquisitionDateYy;
}

// Standard Setter
public void SetSAcquisitionDateYy(int value)
{
    _SAcquisitionDateYy = value;
}

// Get<>AsString()
public string GetSAcquisitionDateYyAsString()
{
    return _SAcquisitionDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetSAcquisitionDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SAcquisitionDateYy = parsed;
}

// Standard Getter
public int GetSAcquisitionDateMm()
{
    return _SAcquisitionDateMm;
}

// Standard Setter
public void SetSAcquisitionDateMm(int value)
{
    _SAcquisitionDateMm = value;
}

// Get<>AsString()
public string GetSAcquisitionDateMmAsString()
{
    return _SAcquisitionDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetSAcquisitionDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SAcquisitionDateMm = parsed;
}

// Standard Getter
public int GetSAcquisitionDateDd()
{
    return _SAcquisitionDateDd;
}

// Standard Setter
public void SetSAcquisitionDateDd(int value)
{
    _SAcquisitionDateDd = value;
}

// Get<>AsString()
public string GetSAcquisitionDateDdAsString()
{
    return _SAcquisitionDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetSAcquisitionDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SAcquisitionDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: STrancheHeaderData
public class STrancheHeaderData
{
    private static int _size = 110;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler142, is_external=, is_static_class=False, static_prefix=
    private string _Filler142 ="";
    
    
    
    
    // [DEBUG] Field: STrancheRecordCount, is_external=, is_static_class=False, static_prefix=
    private int _STrancheRecordCount =0;
    
    
    
    
    // [DEBUG] Field: Filler143, is_external=, is_static_class=False, static_prefix=
    private string _Filler143 ="";
    
    
    
    
    // [DEBUG] Field: Filler144, is_external=, is_static_class=False, static_prefix=
    private string _Filler144 ="";
    
    
    
    
    // [DEBUG] Field: Filler145, is_external=, is_static_class=False, static_prefix=
    private string _Filler145 ="";
    
    
    
    
    // [DEBUG] Field: Filler146, is_external=, is_static_class=False, static_prefix=
    private string _Filler146 ="";
    
    
    
    
    // [DEBUG] Field: Filler147, is_external=, is_static_class=False, static_prefix=
    private string _Filler147 ="";
    
    
    
    
    // [DEBUG] Field: Filler148, is_external=, is_static_class=False, static_prefix=
    private string _Filler148 ="";
    
    
    
    
    // [DEBUG] Field: Filler149, is_external=, is_static_class=False, static_prefix=
    private string _Filler149 ="";
    
    
    
    
    // [DEBUG] Field: Filler150, is_external=, is_static_class=False, static_prefix=
    private string _Filler150 ="";
    
    
    
    
    // [DEBUG] Field: Filler151, is_external=, is_static_class=False, static_prefix=
    private string _Filler151 ="";
    
    
    
    
    // [DEBUG] Field: Filler152, is_external=, is_static_class=False, static_prefix=
    private string _Filler152 ="";
    
    
    
    
    // [DEBUG] Field: Filler153, is_external=, is_static_class=False, static_prefix=
    private string _Filler153 ="";
    
    
    
    
    // [DEBUG] Field: Filler154, is_external=, is_static_class=False, static_prefix=
    private string _Filler154 ="";
    
    
    
    
    // [DEBUG] Field: Filler155, is_external=, is_static_class=False, static_prefix=
    private string _Filler155 ="";
    
    
    
    
    // [DEBUG] Field: Filler156, is_external=, is_static_class=False, static_prefix=
    private string _Filler156 ="";
    
    
    
    
    // [DEBUG] Field: Filler157, is_external=, is_static_class=False, static_prefix=
    private string _Filler157 ="";
    
    
    
    
public STrancheHeaderData() {}

public STrancheHeaderData(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller142(data.Substring(offset, 2).Trim());
    offset += 2;
    SetSTrancheRecordCount(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetFiller143(data.Substring(offset, 105).Trim());
    offset += 105;
    SetFiller144(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller145(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller146(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller147(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller148(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller149(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller150(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller151(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller152(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller153(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller154(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller155(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller156(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller157(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetSTrancheHeaderDataAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler142.PadRight(2));
    result.Append(_STrancheRecordCount.ToString().PadLeft(3, '0'));
    result.Append(_Filler143.PadRight(105));
    result.Append(_Filler144.PadRight(0));
    result.Append(_Filler145.PadRight(0));
    result.Append(_Filler146.PadRight(0));
    result.Append(_Filler147.PadRight(0));
    result.Append(_Filler148.PadRight(0));
    result.Append(_Filler149.PadRight(0));
    result.Append(_Filler150.PadRight(0));
    result.Append(_Filler151.PadRight(0));
    result.Append(_Filler152.PadRight(0));
    result.Append(_Filler153.PadRight(0));
    result.Append(_Filler154.PadRight(0));
    result.Append(_Filler155.PadRight(0));
    result.Append(_Filler156.PadRight(0));
    result.Append(_Filler157.PadRight(0));
    
    return result.ToString();
}

public void SetSTrancheHeaderDataAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller142(extracted);
    }
    offset += 2;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSTrancheRecordCount(parsedInt);
    }
    offset += 3;
    if (offset + 105 <= data.Length)
    {
        string extracted = data.Substring(offset, 105).Trim();
        SetFiller143(extracted);
    }
    offset += 105;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller144(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller145(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller146(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller147(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller148(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller149(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller150(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller151(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller152(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller153(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller154(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller155(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller156(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller157(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller142()
{
    return _Filler142;
}

// Standard Setter
public void SetFiller142(string value)
{
    _Filler142 = value;
}

// Get<>AsString()
public string GetFiller142AsString()
{
    return _Filler142.PadRight(2);
}

// Set<>AsString()
public void SetFiller142AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler142 = value;
}

// Standard Getter
public int GetSTrancheRecordCount()
{
    return _STrancheRecordCount;
}

// Standard Setter
public void SetSTrancheRecordCount(int value)
{
    _STrancheRecordCount = value;
}

// Get<>AsString()
public string GetSTrancheRecordCountAsString()
{
    return _STrancheRecordCount.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetSTrancheRecordCountAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _STrancheRecordCount = parsed;
}

// Standard Getter
public string GetFiller143()
{
    return _Filler143;
}

// Standard Setter
public void SetFiller143(string value)
{
    _Filler143 = value;
}

// Get<>AsString()
public string GetFiller143AsString()
{
    return _Filler143.PadRight(105);
}

// Set<>AsString()
public void SetFiller143AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler143 = value;
}

// Standard Getter
public string GetFiller144()
{
    return _Filler144;
}

// Standard Setter
public void SetFiller144(string value)
{
    _Filler144 = value;
}

// Get<>AsString()
public string GetFiller144AsString()
{
    return _Filler144.PadRight(0);
}

// Set<>AsString()
public void SetFiller144AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler144 = value;
}

// Standard Getter
public string GetFiller145()
{
    return _Filler145;
}

// Standard Setter
public void SetFiller145(string value)
{
    _Filler145 = value;
}

// Get<>AsString()
public string GetFiller145AsString()
{
    return _Filler145.PadRight(0);
}

// Set<>AsString()
public void SetFiller145AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler145 = value;
}

// Standard Getter
public string GetFiller146()
{
    return _Filler146;
}

// Standard Setter
public void SetFiller146(string value)
{
    _Filler146 = value;
}

// Get<>AsString()
public string GetFiller146AsString()
{
    return _Filler146.PadRight(0);
}

// Set<>AsString()
public void SetFiller146AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler146 = value;
}

// Standard Getter
public string GetFiller147()
{
    return _Filler147;
}

// Standard Setter
public void SetFiller147(string value)
{
    _Filler147 = value;
}

// Get<>AsString()
public string GetFiller147AsString()
{
    return _Filler147.PadRight(0);
}

// Set<>AsString()
public void SetFiller147AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler147 = value;
}

// Standard Getter
public string GetFiller148()
{
    return _Filler148;
}

// Standard Setter
public void SetFiller148(string value)
{
    _Filler148 = value;
}

// Get<>AsString()
public string GetFiller148AsString()
{
    return _Filler148.PadRight(0);
}

// Set<>AsString()
public void SetFiller148AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler148 = value;
}

// Standard Getter
public string GetFiller149()
{
    return _Filler149;
}

// Standard Setter
public void SetFiller149(string value)
{
    _Filler149 = value;
}

// Get<>AsString()
public string GetFiller149AsString()
{
    return _Filler149.PadRight(0);
}

// Set<>AsString()
public void SetFiller149AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler149 = value;
}

// Standard Getter
public string GetFiller150()
{
    return _Filler150;
}

// Standard Setter
public void SetFiller150(string value)
{
    _Filler150 = value;
}

// Get<>AsString()
public string GetFiller150AsString()
{
    return _Filler150.PadRight(0);
}

// Set<>AsString()
public void SetFiller150AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler150 = value;
}

// Standard Getter
public string GetFiller151()
{
    return _Filler151;
}

// Standard Setter
public void SetFiller151(string value)
{
    _Filler151 = value;
}

// Get<>AsString()
public string GetFiller151AsString()
{
    return _Filler151.PadRight(0);
}

// Set<>AsString()
public void SetFiller151AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler151 = value;
}

// Standard Getter
public string GetFiller152()
{
    return _Filler152;
}

// Standard Setter
public void SetFiller152(string value)
{
    _Filler152 = value;
}

// Get<>AsString()
public string GetFiller152AsString()
{
    return _Filler152.PadRight(0);
}

// Set<>AsString()
public void SetFiller152AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler152 = value;
}

// Standard Getter
public string GetFiller153()
{
    return _Filler153;
}

// Standard Setter
public void SetFiller153(string value)
{
    _Filler153 = value;
}

// Get<>AsString()
public string GetFiller153AsString()
{
    return _Filler153.PadRight(0);
}

// Set<>AsString()
public void SetFiller153AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler153 = value;
}

// Standard Getter
public string GetFiller154()
{
    return _Filler154;
}

// Standard Setter
public void SetFiller154(string value)
{
    _Filler154 = value;
}

// Get<>AsString()
public string GetFiller154AsString()
{
    return _Filler154.PadRight(0);
}

// Set<>AsString()
public void SetFiller154AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler154 = value;
}

// Standard Getter
public string GetFiller155()
{
    return _Filler155;
}

// Standard Setter
public void SetFiller155(string value)
{
    _Filler155 = value;
}

// Get<>AsString()
public string GetFiller155AsString()
{
    return _Filler155.PadRight(0);
}

// Set<>AsString()
public void SetFiller155AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler155 = value;
}

// Standard Getter
public string GetFiller156()
{
    return _Filler156;
}

// Standard Setter
public void SetFiller156(string value)
{
    _Filler156 = value;
}

// Get<>AsString()
public string GetFiller156AsString()
{
    return _Filler156.PadRight(0);
}

// Set<>AsString()
public void SetFiller156AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler156 = value;
}

// Standard Getter
public string GetFiller157()
{
    return _Filler157;
}

// Standard Setter
public void SetFiller157(string value)
{
    _Filler157 = value;
}

// Get<>AsString()
public string GetFiller157AsString()
{
    return _Filler157.PadRight(0);
}

// Set<>AsString()
public void SetFiller157AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler157 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: STrancheLine
public class STrancheLine
{
    private static int _size = 375;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SBdvLineIndicator, is_external=, is_static_class=False, static_prefix=
    private string _SBdvLineIndicator ="";
    
    
    
    
    // [DEBUG] Field: STrancheType, is_external=, is_static_class=False, static_prefix=
    private string _STrancheType ="";
    
    
    // 88-level condition checks for STrancheType
    public bool IsSPoolTranche()
    {
        if (this._STrancheType == "'A'") return true;
        return false;
    }
    public bool IsSParallelPoolTranche()
    {
        if (this._STrancheType == "'B'") return true;
        return false;
    }
    public bool IsSIndexPoolTranche()
    {
        if (this._STrancheType == "'C'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: SMovementDateX, is_external=, is_static_class=False, static_prefix=
    private string _SMovementDateX ="";
    
    
    
    
    // [DEBUG] Field: SMovementDate9, is_external=, is_static_class=False, static_prefix=
    private STrancheLine.SMovementDate9 _SMovementDate9 = new STrancheLine.SMovementDate9();
    
    
    
    
    // [DEBUG] Field: SHatchIndicator, is_external=, is_static_class=False, static_prefix=
    private string _SHatchIndicator ="";
    
    
    
    
    // [DEBUG] Field: SMovementDescription, is_external=, is_static_class=False, static_prefix=
    private string _SMovementDescription ="";
    
    
    // 88-level condition checks for SMovementDescription
    public bool IsSale()
    {
        if (this._SMovementDescription == "'DISPOSAL'") return true;
        if (this._SMovementDescription == "'STOCK SALE'") return true;
        if (this._SMovementDescription == "'RIGHTS SALE'") return true;
        if (this._SMovementDescription == "'RIGHTS LAPSE'") return true;
        if (this._SMovementDescription == "'REDEMPTION'") return true;
        if (this._SMovementDescription == "'LIQUIDATION'") return true;
        if (this._SMovementDescription == "'DEEMED DISPOSAL'") return true;
        if (this._SMovementDescription == "'RIGHTS FRACTION'") return true;
        if (this._SMovementDescription == "'EQUALISATION'") return true;
        return false;
    }
    public bool IsCdSale()
    {
        if (this._SMovementDescription == "'CAPITAL DISTBTN'") return true;
        return false;
    }
    public bool IsRevaluationSale()
    {
        if (this._SMovementDescription == "'REVALUATION'") return true;
        return false;
    }
    public bool IsAcquisition()
    {
        if (this._SMovementDescription == "'SHARE EXCHANGE'") return true;
        if (this._SMovementDescription == "'ACCUMULATION'") return true;
        if (this._SMovementDescription == "'PRIVATISATION'") return true;
        if (this._SMovementDescription == "'NEW ISSUE SALE'") return true;
        if (this._SMovementDescription == "'NEW ISSUE PURCH'") return true;
        if (this._SMovementDescription == "'ACQUISITION'") return true;
        if (this._SMovementDescription == "'STOCK PURCHASE'") return true;
        if (this._SMovementDescription == "'BONUS'") return true;
        if (this._SMovementDescription == "'NEW ISSUE'") return true;
        if (this._SMovementDescription == "'RIGHTS'") return true;
        if (this._SMovementDescription == "'RIGHTS PURCHASE'") return true;
        if (this._SMovementDescription == "'U/W PURCHASE'") return true;
        return false;
    }
    public bool IsBalanceBf()
    {
        if (this._SMovementDescription == "'BALANCE B/F'") return true;
        return false;
    }
    public bool IsBalance()
    {
        if (this._SMovementDescription == "'        BALANCE'") return true;
        return false;
    }
    public bool IsSchedDescIrishNdlCost()
    {
        if (this._SMovementDescription == "'NDL'") return true;
        return false;
    }
    public bool IsSchedDescIrishDdHistCost()
    {
        if (this._SMovementDescription == "'HISTORIC COST'") return true;
        return false;
    }
    public bool IsSchedDescDeferredLoss()
    {
        if (this._SMovementDescription == "'DEFERRED LOSS'") return true;
        return false;
    }
    public bool IsOptionLapse()
    {
        if (this._SMovementDescription == "'OPTION LAPSE'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: Filler158, is_external=, is_static_class=False, static_prefix=
    private STrancheLine.Filler158 _Filler158 = new STrancheLine.Filler158();
    
    
    
    
    // [DEBUG] Field: Filler161, is_external=, is_static_class=False, static_prefix=
    private STrancheLine.Filler161 _Filler161 = new STrancheLine.Filler161();
    
    
    
    
    // [DEBUG] Field: Filler165, is_external=, is_static_class=False, static_prefix=
    private STrancheLine.Filler165 _Filler165 = new STrancheLine.Filler165();
    
    
    
    
    // [DEBUG] Field: Filler168, is_external=, is_static_class=False, static_prefix=
    private STrancheLine.Filler168 _Filler168 = new STrancheLine.Filler168();
    
    
    
    
    // [DEBUG] Field: SHoldingUnitsX, is_external=, is_static_class=False, static_prefix=
    private string _SHoldingUnitsX ="";
    
    
    
    
    // [DEBUG] Field: SHoldingUnits9, is_external=, is_static_class=False, static_prefix=
    private decimal _SHoldingUnits9 =0;
    
    
    
    
    // [DEBUG] Field: SHoldingCostX, is_external=, is_static_class=False, static_prefix=
    private string _SHoldingCostX ="";
    
    
    
    
    // [DEBUG] Field: SHoldingCost9, is_external=, is_static_class=False, static_prefix=
    private decimal _SHoldingCost9 =0;
    
    
    
    
    // [DEBUG] Field: SHoldingCostUnsigned, is_external=, is_static_class=False, static_prefix=
    private decimal _SHoldingCostUnsigned =0;
    
    
    
    
    // [DEBUG] Field: SIndexDateX, is_external=, is_static_class=False, static_prefix=
    private string _SIndexDateX ="";
    
    
    
    
    // [DEBUG] Field: SIndexDate9, is_external=, is_static_class=False, static_prefix=
    private int _SIndexDate9 =0;
    
    
    
    
    // [DEBUG] Field: SIndexFactorX, is_external=, is_static_class=False, static_prefix=
    private string _SIndexFactorX ="";
    
    
    
    
    // [DEBUG] Field: SIndexFactor9, is_external=, is_static_class=False, static_prefix=
    private int _SIndexFactor9 =0;
    
    
    
    
    // [DEBUG] Field: SIndexationLimit, is_external=, is_static_class=False, static_prefix=
    private string _SIndexationLimit ="";
    
    
    // 88-level condition checks for SIndexationLimit
    public bool IsSchedSaleGivesRiseToNdl()
    {
        if (this._SIndexationLimit == "'F'") return true;
        return false;
    }
    public bool IsSchedLossRestrToOrigCost()
    {
        if (this._SIndexationLimit == "'G'") return true;
        return false;
    }
    public bool IsSchedDdlossRestrOrigCost()
    {
        if (this._SIndexationLimit == "'H'") return true;
        return false;
    }
    public bool IsSchedGainRelievedByNdl()
    {
        if (this._SIndexationLimit == "'J'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: SIndexationCostX, is_external=, is_static_class=False, static_prefix=
    private string _SIndexationCostX ="";
    
    
    
    
    // [DEBUG] Field: SIndexationCost9, is_external=, is_static_class=False, static_prefix=
    private decimal _SIndexationCost9 =0;
    
    
    
    
    // [DEBUG] Field: SIndexationCostUnsigned, is_external=, is_static_class=False, static_prefix=
    private decimal _SIndexationCostUnsigned =0;
    
    
    
    
    // [DEBUG] Field: SDisposalProceedsX, is_external=, is_static_class=False, static_prefix=
    private string _SDisposalProceedsX ="";
    
    
    
    
    // [DEBUG] Field: SDisposalProceeds9, is_external=, is_static_class=False, static_prefix=
    private decimal _SDisposalProceeds9 =0;
    
    
    
    
    // [DEBUG] Field: SDisposalProceedsUnsigned, is_external=, is_static_class=False, static_prefix=
    private decimal _SDisposalProceedsUnsigned =0;
    
    
    
    
    // [DEBUG] Field: SLongTermIndicator, is_external=, is_static_class=False, static_prefix=
    private string _SLongTermIndicator ="";
    
    
    
    
    // [DEBUG] Field: SCapitalGainOrLossX, is_external=, is_static_class=False, static_prefix=
    private string _SCapitalGainOrLossX ="";
    
    
    
    
    // [DEBUG] Field: SCapitalGainOrLoss9, is_external=, is_static_class=False, static_prefix=
    private decimal _SCapitalGainOrLoss9 =0;
    
    
    
    
    // [DEBUG] Field: SBookCostX, is_external=, is_static_class=False, static_prefix=
    private string _SBookCostX ="";
    
    
    
    
    // [DEBUG] Field: SBookCost9, is_external=, is_static_class=False, static_prefix=
    private decimal _SBookCost9 =0;
    
    
    
    
    // [DEBUG] Field: Filler170, is_external=, is_static_class=False, static_prefix=
    private string _Filler170 ="";
    
    
    
    
    // [DEBUG] Field: SSequenceNoX, is_external=, is_static_class=False, static_prefix=
    private string _SSequenceNoX ="";
    
    
    
    
    // [DEBUG] Field: SSequenceNo9, is_external=, is_static_class=False, static_prefix=
    private int _SSequenceNo9 =0;
    
    
    
    
    // [DEBUG] Field: SYear, is_external=, is_static_class=False, static_prefix=
    private string _SYear ="";
    
    
    
    
    // [DEBUG] Field: SPercentBusinessUseX, is_external=, is_static_class=False, static_prefix=
    private string _SPercentBusinessUseX ="";
    
    
    
    
    // [DEBUG] Field: SPercentBusinessUse9, is_external=, is_static_class=False, static_prefix=
    private int _SPercentBusinessUse9 =0;
    
    
    
    
    // [DEBUG] Field: STrancheFlag, is_external=, is_static_class=False, static_prefix=
    private string _STrancheFlag ="";
    
    
    
    
    // [DEBUG] Field: SLostIndexationX, is_external=, is_static_class=False, static_prefix=
    private string _SLostIndexationX ="";
    
    
    
    
    // [DEBUG] Field: SLostIndexation9, is_external=, is_static_class=False, static_prefix=
    private decimal _SLostIndexation9 =0;
    
    
    
    
    // [DEBUG] Field: SOffshoreIncomeGainX, is_external=, is_static_class=False, static_prefix=
    private string _SOffshoreIncomeGainX ="";
    
    
    
    
    // [DEBUG] Field: SOffshoreIncomeGain9, is_external=, is_static_class=False, static_prefix=
    private decimal _SOffshoreIncomeGain9 =0;
    
    
    
    
    // [DEBUG] Field: SDisposalContractNo, is_external=, is_static_class=False, static_prefix=
    private string _SDisposalContractNo ="";
    
    
    
    
    // [DEBUG] Field: SFullTaperDate, is_external=, is_static_class=False, static_prefix=
    private STrancheLine.SFullTaperDate _SFullTaperDate = new STrancheLine.SFullTaperDate();
    
    
    
    
    // [DEBUG] Field: STaperUnitsX, is_external=, is_static_class=False, static_prefix=
    private string _STaperUnitsX ="";
    
    
    
    
    // [DEBUG] Field: STaperUnits9, is_external=, is_static_class=False, static_prefix=
    private decimal _STaperUnits9 =0;
    
    
    
    
    // [DEBUG] Field: STaperGainX, is_external=, is_static_class=False, static_prefix=
    private string _STaperGainX ="";
    
    
    
    
    // [DEBUG] Field: STaperGain9, is_external=, is_static_class=False, static_prefix=
    private decimal _STaperGain9 =0;
    
    
    
    
    // [DEBUG] Field: STaperProcX, is_external=, is_static_class=False, static_prefix=
    private string _STaperProcX ="";
    
    
    
    
    // [DEBUG] Field: STaperProc9, is_external=, is_static_class=False, static_prefix=
    private decimal _STaperProc9 =0;
    
    
    
    
    // [DEBUG] Field: SSourceCategory, is_external=, is_static_class=False, static_prefix=
    private string _SSourceCategory ="";
    
    
    // 88-level condition checks for SSourceCategory
    public bool IsSStockPurchase()
    {
        if (this._SSourceCategory == "'SP'") return true;
        return false;
    }
    public bool IsSStockSale()
    {
        if (this._SSourceCategory == "'SS'") return true;
        return false;
    }
    public bool IsSEpWcExercise()
    {
        if (this._SSourceCategory == "'EP'") return true;
        if (this._SSourceCategory == "'WC'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: SDisplayCategory, is_external=, is_static_class=False, static_prefix=
    private string _SDisplayCategory ="";
    
    
    
    
    // [DEBUG] Field: SCategoryId, is_external=, is_static_class=False, static_prefix=
    private string _SCategoryId ="";
    
    
    
    
    // [DEBUG] Field: STransactionCategory, is_external=, is_static_class=False, static_prefix=
    private string _STransactionCategory ="";
    
    
    
    
public STrancheLine() {}

public STrancheLine(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSBdvLineIndicator(data.Substring(offset, 1).Trim());
    offset += 1;
    SetSTrancheType(data.Substring(offset, 1).Trim());
    offset += 1;
    SetSMovementDateX(data.Substring(offset, 6).Trim());
    offset += 6;
    _SMovementDate9.SetSMovementDate9AsString(data.Substring(offset, SMovementDate9.GetSize()));
    offset += 6;
    SetSHatchIndicator(data.Substring(offset, 1).Trim());
    offset += 1;
    SetSMovementDescription(data.Substring(offset, 16).Trim());
    offset += 16;
    _Filler158.SetFiller158AsString(data.Substring(offset, Filler158.GetSize()));
    offset += 0;
    _Filler161.SetFiller161AsString(data.Substring(offset, Filler161.GetSize()));
    offset += 0;
    _Filler165.SetFiller165AsString(data.Substring(offset, Filler165.GetSize()));
    offset += 0;
    _Filler168.SetFiller168AsString(data.Substring(offset, Filler168.GetSize()));
    offset += 2;
    SetSHoldingUnitsX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSHoldingUnits9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSHoldingCostX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSHoldingCost9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSHoldingCostUnsigned(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSIndexDateX(data.Substring(offset, 4).Trim());
    offset += 4;
    SetSIndexDate9(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetSIndexFactorX(data.Substring(offset, 5).Trim());
    offset += 5;
    SetSIndexFactor9(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetSIndexationLimit(data.Substring(offset, 1).Trim());
    offset += 1;
    SetSIndexationCostX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSIndexationCost9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSIndexationCostUnsigned(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSDisposalProceedsX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSDisposalProceeds9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSDisposalProceedsUnsigned(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSLongTermIndicator(data.Substring(offset, 1).Trim());
    offset += 1;
    SetSCapitalGainOrLossX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSCapitalGainOrLoss9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSBookCostX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSBookCost9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetFiller170(data.Substring(offset, 3).Trim());
    offset += 3;
    SetSSequenceNoX(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSSequenceNo9(int.Parse(data.Substring(offset, 9).Trim()));
    offset += 9;
    SetSYear(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSPercentBusinessUseX(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSPercentBusinessUse9(int.Parse(data.Substring(offset, 3).Trim()));
    offset += 3;
    SetSTrancheFlag(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSLostIndexationX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSLostIndexation9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSOffshoreIncomeGainX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSOffshoreIncomeGain9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSDisposalContractNo(data.Substring(offset, 10).Trim());
    offset += 10;
    _SFullTaperDate.SetSFullTaperDateAsString(data.Substring(offset, SFullTaperDate.GetSize()));
    offset += 0;
    SetSTaperUnitsX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSTaperUnits9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSTaperGainX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSTaperGain9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSTaperProcX(data.Substring(offset, 12).Trim());
    offset += 12;
    SetSTaperProc9(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetSSourceCategory(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSDisplayCategory(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSCategoryId(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSTransactionCategory(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetSTrancheLineAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SBdvLineIndicator.PadRight(1));
    result.Append(_STrancheType.PadRight(1));
    result.Append(_SMovementDateX.PadRight(6));
    result.Append(_SMovementDate9.GetSMovementDate9AsString());
    result.Append(_SHatchIndicator.PadRight(1));
    result.Append(_SMovementDescription.PadRight(16));
    result.Append(_Filler158.GetFiller158AsString());
    result.Append(_Filler161.GetFiller161AsString());
    result.Append(_Filler165.GetFiller165AsString());
    result.Append(_Filler168.GetFiller168AsString());
    result.Append(_SHoldingUnitsX.PadRight(12));
    result.Append(_SHoldingUnits9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SHoldingCostX.PadRight(12));
    result.Append(_SHoldingCost9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SHoldingCostUnsigned.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SIndexDateX.PadRight(4));
    result.Append(_SIndexDate9.ToString().PadLeft(4, '0'));
    result.Append(_SIndexFactorX.PadRight(5));
    result.Append(_SIndexFactor9.ToString().PadLeft(2, '0'));
    result.Append(_SIndexationLimit.PadRight(1));
    result.Append(_SIndexationCostX.PadRight(12));
    result.Append(_SIndexationCost9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SIndexationCostUnsigned.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SDisposalProceedsX.PadRight(12));
    result.Append(_SDisposalProceeds9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SDisposalProceedsUnsigned.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SLongTermIndicator.PadRight(1));
    result.Append(_SCapitalGainOrLossX.PadRight(12));
    result.Append(_SCapitalGainOrLoss9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SBookCostX.PadRight(12));
    result.Append(_SBookCost9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler170.PadRight(3));
    result.Append(_SSequenceNoX.PadRight(0));
    result.Append(_SSequenceNo9.ToString().PadLeft(9, '0'));
    result.Append(_SYear.PadRight(0));
    result.Append(_SPercentBusinessUseX.PadRight(0));
    result.Append(_SPercentBusinessUse9.ToString().PadLeft(3, '0'));
    result.Append(_STrancheFlag.PadRight(0));
    result.Append(_SLostIndexationX.PadRight(12));
    result.Append(_SLostIndexation9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SOffshoreIncomeGainX.PadRight(12));
    result.Append(_SOffshoreIncomeGain9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SDisposalContractNo.PadRight(10));
    result.Append(_SFullTaperDate.GetSFullTaperDateAsString());
    result.Append(_STaperUnitsX.PadRight(12));
    result.Append(_STaperUnits9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_STaperGainX.PadRight(12));
    result.Append(_STaperGain9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_STaperProcX.PadRight(12));
    result.Append(_STaperProc9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_SSourceCategory.PadRight(0));
    result.Append(_SDisplayCategory.PadRight(0));
    result.Append(_SCategoryId.PadRight(0));
    result.Append(_STransactionCategory.PadRight(0));
    
    return result.ToString();
}

public void SetSTrancheLineAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetSBdvLineIndicator(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetSTrancheType(extracted);
    }
    offset += 1;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetSMovementDateX(extracted);
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        _SMovementDate9.SetSMovementDate9AsString(data.Substring(offset, 6));
    }
    else
    {
        _SMovementDate9.SetSMovementDate9AsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetSHatchIndicator(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        string extracted = data.Substring(offset, 16).Trim();
        SetSMovementDescription(extracted);
    }
    offset += 16;
    if (offset + 0 <= data.Length)
    {
        _Filler158.SetFiller158AsString(data.Substring(offset, 0));
    }
    else
    {
        _Filler158.SetFiller158AsString(data.Substring(offset));
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        _Filler161.SetFiller161AsString(data.Substring(offset, 0));
    }
    else
    {
        _Filler161.SetFiller161AsString(data.Substring(offset));
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        _Filler165.SetFiller165AsString(data.Substring(offset, 0));
    }
    else
    {
        _Filler165.SetFiller165AsString(data.Substring(offset));
    }
    offset += 0;
    if (offset + 2 <= data.Length)
    {
        _Filler168.SetFiller168AsString(data.Substring(offset, 2));
    }
    else
    {
        _Filler168.SetFiller168AsString(data.Substring(offset));
    }
    offset += 2;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSHoldingUnitsX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSHoldingUnits9(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSHoldingCostX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSHoldingCost9(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSHoldingCostUnsigned(parsedDec);
    }
    offset += 12;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetSIndexDateX(extracted);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSIndexDate9(parsedInt);
    }
    offset += 4;
    if (offset + 5 <= data.Length)
    {
        string extracted = data.Substring(offset, 5).Trim();
        SetSIndexFactorX(extracted);
    }
    offset += 5;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSIndexFactor9(parsedInt);
    }
    offset += 2;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetSIndexationLimit(extracted);
    }
    offset += 1;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSIndexationCostX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSIndexationCost9(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSIndexationCostUnsigned(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSDisposalProceedsX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSDisposalProceeds9(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSDisposalProceedsUnsigned(parsedDec);
    }
    offset += 12;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetSLongTermIndicator(extracted);
    }
    offset += 1;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSCapitalGainOrLossX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSCapitalGainOrLoss9(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSBookCostX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSBookCost9(parsedDec);
    }
    offset += 12;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller170(extracted);
    }
    offset += 3;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSSequenceNoX(extracted);
    }
    offset += 0;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSSequenceNo9(parsedInt);
    }
    offset += 9;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSYear(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSPercentBusinessUseX(extracted);
    }
    offset += 0;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSPercentBusinessUse9(parsedInt);
    }
    offset += 3;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSTrancheFlag(extracted);
    }
    offset += 0;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSLostIndexationX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSLostIndexation9(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSOffshoreIncomeGainX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSOffshoreIncomeGain9(parsedDec);
    }
    offset += 12;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetSDisposalContractNo(extracted);
    }
    offset += 10;
    if (offset + 0 <= data.Length)
    {
        _SFullTaperDate.SetSFullTaperDateAsString(data.Substring(offset, 0));
    }
    else
    {
        _SFullTaperDate.SetSFullTaperDateAsString(data.Substring(offset));
    }
    offset += 0;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSTaperUnitsX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSTaperUnits9(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSTaperGainX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSTaperGain9(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetSTaperProcX(extracted);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetSTaperProc9(parsedDec);
    }
    offset += 12;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSSourceCategory(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSDisplayCategory(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSCategoryId(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSTransactionCategory(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetSBdvLineIndicator()
{
    return _SBdvLineIndicator;
}

// Standard Setter
public void SetSBdvLineIndicator(string value)
{
    _SBdvLineIndicator = value;
}

// Get<>AsString()
public string GetSBdvLineIndicatorAsString()
{
    return _SBdvLineIndicator.PadRight(1);
}

// Set<>AsString()
public void SetSBdvLineIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SBdvLineIndicator = value;
}

// Standard Getter
public string GetSTrancheType()
{
    return _STrancheType;
}

// Standard Setter
public void SetSTrancheType(string value)
{
    _STrancheType = value;
}

// Get<>AsString()
public string GetSTrancheTypeAsString()
{
    return _STrancheType.PadRight(1);
}

// Set<>AsString()
public void SetSTrancheTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STrancheType = value;
}

// Standard Getter
public string GetSMovementDateX()
{
    return _SMovementDateX;
}

// Standard Setter
public void SetSMovementDateX(string value)
{
    _SMovementDateX = value;
}

// Get<>AsString()
public string GetSMovementDateXAsString()
{
    return _SMovementDateX.PadRight(6);
}

// Set<>AsString()
public void SetSMovementDateXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SMovementDateX = value;
}

// Standard Getter
public SMovementDate9 GetSMovementDate9()
{
    return _SMovementDate9;
}

// Standard Setter
public void SetSMovementDate9(SMovementDate9 value)
{
    _SMovementDate9 = value;
}

// Get<>AsString()
public string GetSMovementDate9AsString()
{
    return _SMovementDate9 != null ? _SMovementDate9.GetSMovementDate9AsString() : "";
}

// Set<>AsString()
public void SetSMovementDate9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_SMovementDate9 == null)
    {
        _SMovementDate9 = new SMovementDate9();
    }
    _SMovementDate9.SetSMovementDate9AsString(value);
}

// Standard Getter
public string GetSHatchIndicator()
{
    return _SHatchIndicator;
}

// Standard Setter
public void SetSHatchIndicator(string value)
{
    _SHatchIndicator = value;
}

// Get<>AsString()
public string GetSHatchIndicatorAsString()
{
    return _SHatchIndicator.PadRight(1);
}

// Set<>AsString()
public void SetSHatchIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SHatchIndicator = value;
}

// Standard Getter
public string GetSMovementDescription()
{
    return _SMovementDescription;
}

// Standard Setter
public void SetSMovementDescription(string value)
{
    _SMovementDescription = value;
}

// Get<>AsString()
public string GetSMovementDescriptionAsString()
{
    return _SMovementDescription.PadRight(16);
}

// Set<>AsString()
public void SetSMovementDescriptionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SMovementDescription = value;
}

// Standard Getter
public Filler158 GetFiller158()
{
    return _Filler158;
}

// Standard Setter
public void SetFiller158(Filler158 value)
{
    _Filler158 = value;
}

// Get<>AsString()
public string GetFiller158AsString()
{
    return _Filler158 != null ? _Filler158.GetFiller158AsString() : "";
}

// Set<>AsString()
public void SetFiller158AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler158 == null)
    {
        _Filler158 = new Filler158();
    }
    _Filler158.SetFiller158AsString(value);
}

// Standard Getter
public Filler161 GetFiller161()
{
    return _Filler161;
}

// Standard Setter
public void SetFiller161(Filler161 value)
{
    _Filler161 = value;
}

// Get<>AsString()
public string GetFiller161AsString()
{
    return _Filler161 != null ? _Filler161.GetFiller161AsString() : "";
}

// Set<>AsString()
public void SetFiller161AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler161 == null)
    {
        _Filler161 = new Filler161();
    }
    _Filler161.SetFiller161AsString(value);
}

// Standard Getter
public Filler165 GetFiller165()
{
    return _Filler165;
}

// Standard Setter
public void SetFiller165(Filler165 value)
{
    _Filler165 = value;
}

// Get<>AsString()
public string GetFiller165AsString()
{
    return _Filler165 != null ? _Filler165.GetFiller165AsString() : "";
}

// Set<>AsString()
public void SetFiller165AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler165 == null)
    {
        _Filler165 = new Filler165();
    }
    _Filler165.SetFiller165AsString(value);
}

// Standard Getter
public Filler168 GetFiller168()
{
    return _Filler168;
}

// Standard Setter
public void SetFiller168(Filler168 value)
{
    _Filler168 = value;
}

// Get<>AsString()
public string GetFiller168AsString()
{
    return _Filler168 != null ? _Filler168.GetFiller168AsString() : "";
}

// Set<>AsString()
public void SetFiller168AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler168 == null)
    {
        _Filler168 = new Filler168();
    }
    _Filler168.SetFiller168AsString(value);
}

// Standard Getter
public string GetSHoldingUnitsX()
{
    return _SHoldingUnitsX;
}

// Standard Setter
public void SetSHoldingUnitsX(string value)
{
    _SHoldingUnitsX = value;
}

// Get<>AsString()
public string GetSHoldingUnitsXAsString()
{
    return _SHoldingUnitsX.PadRight(12);
}

// Set<>AsString()
public void SetSHoldingUnitsXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SHoldingUnitsX = value;
}

// Standard Getter
public decimal GetSHoldingUnits9()
{
    return _SHoldingUnits9;
}

// Standard Setter
public void SetSHoldingUnits9(decimal value)
{
    _SHoldingUnits9 = value;
}

// Get<>AsString()
public string GetSHoldingUnits9AsString()
{
    return _SHoldingUnits9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSHoldingUnits9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SHoldingUnits9 = parsed;
}

// Standard Getter
public string GetSHoldingCostX()
{
    return _SHoldingCostX;
}

// Standard Setter
public void SetSHoldingCostX(string value)
{
    _SHoldingCostX = value;
}

// Get<>AsString()
public string GetSHoldingCostXAsString()
{
    return _SHoldingCostX.PadRight(12);
}

// Set<>AsString()
public void SetSHoldingCostXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SHoldingCostX = value;
}

// Standard Getter
public decimal GetSHoldingCost9()
{
    return _SHoldingCost9;
}

// Standard Setter
public void SetSHoldingCost9(decimal value)
{
    _SHoldingCost9 = value;
}

// Get<>AsString()
public string GetSHoldingCost9AsString()
{
    return _SHoldingCost9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSHoldingCost9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SHoldingCost9 = parsed;
}

// Standard Getter
public decimal GetSHoldingCostUnsigned()
{
    return _SHoldingCostUnsigned;
}

// Standard Setter
public void SetSHoldingCostUnsigned(decimal value)
{
    _SHoldingCostUnsigned = value;
}

// Get<>AsString()
public string GetSHoldingCostUnsignedAsString()
{
    return _SHoldingCostUnsigned.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSHoldingCostUnsignedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SHoldingCostUnsigned = parsed;
}

// Standard Getter
public string GetSIndexDateX()
{
    return _SIndexDateX;
}

// Standard Setter
public void SetSIndexDateX(string value)
{
    _SIndexDateX = value;
}

// Get<>AsString()
public string GetSIndexDateXAsString()
{
    return _SIndexDateX.PadRight(4);
}

// Set<>AsString()
public void SetSIndexDateXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SIndexDateX = value;
}

// Standard Getter
public int GetSIndexDate9()
{
    return _SIndexDate9;
}

// Standard Setter
public void SetSIndexDate9(int value)
{
    _SIndexDate9 = value;
}

// Get<>AsString()
public string GetSIndexDate9AsString()
{
    return _SIndexDate9.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetSIndexDate9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SIndexDate9 = parsed;
}

// Standard Getter
public string GetSIndexFactorX()
{
    return _SIndexFactorX;
}

// Standard Setter
public void SetSIndexFactorX(string value)
{
    _SIndexFactorX = value;
}

// Get<>AsString()
public string GetSIndexFactorXAsString()
{
    return _SIndexFactorX.PadRight(5);
}

// Set<>AsString()
public void SetSIndexFactorXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SIndexFactorX = value;
}

// Standard Getter
public int GetSIndexFactor9()
{
    return _SIndexFactor9;
}

// Standard Setter
public void SetSIndexFactor9(int value)
{
    _SIndexFactor9 = value;
}

// Get<>AsString()
public string GetSIndexFactor9AsString()
{
    return _SIndexFactor9.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetSIndexFactor9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SIndexFactor9 = parsed;
}

// Standard Getter
public string GetSIndexationLimit()
{
    return _SIndexationLimit;
}

// Standard Setter
public void SetSIndexationLimit(string value)
{
    _SIndexationLimit = value;
}

// Get<>AsString()
public string GetSIndexationLimitAsString()
{
    return _SIndexationLimit.PadRight(1);
}

// Set<>AsString()
public void SetSIndexationLimitAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SIndexationLimit = value;
}

// Standard Getter
public string GetSIndexationCostX()
{
    return _SIndexationCostX;
}

// Standard Setter
public void SetSIndexationCostX(string value)
{
    _SIndexationCostX = value;
}

// Get<>AsString()
public string GetSIndexationCostXAsString()
{
    return _SIndexationCostX.PadRight(12);
}

// Set<>AsString()
public void SetSIndexationCostXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SIndexationCostX = value;
}

// Standard Getter
public decimal GetSIndexationCost9()
{
    return _SIndexationCost9;
}

// Standard Setter
public void SetSIndexationCost9(decimal value)
{
    _SIndexationCost9 = value;
}

// Get<>AsString()
public string GetSIndexationCost9AsString()
{
    return _SIndexationCost9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSIndexationCost9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SIndexationCost9 = parsed;
}

// Standard Getter
public decimal GetSIndexationCostUnsigned()
{
    return _SIndexationCostUnsigned;
}

// Standard Setter
public void SetSIndexationCostUnsigned(decimal value)
{
    _SIndexationCostUnsigned = value;
}

// Get<>AsString()
public string GetSIndexationCostUnsignedAsString()
{
    return _SIndexationCostUnsigned.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSIndexationCostUnsignedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SIndexationCostUnsigned = parsed;
}

// Standard Getter
public string GetSDisposalProceedsX()
{
    return _SDisposalProceedsX;
}

// Standard Setter
public void SetSDisposalProceedsX(string value)
{
    _SDisposalProceedsX = value;
}

// Get<>AsString()
public string GetSDisposalProceedsXAsString()
{
    return _SDisposalProceedsX.PadRight(12);
}

// Set<>AsString()
public void SetSDisposalProceedsXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SDisposalProceedsX = value;
}

// Standard Getter
public decimal GetSDisposalProceeds9()
{
    return _SDisposalProceeds9;
}

// Standard Setter
public void SetSDisposalProceeds9(decimal value)
{
    _SDisposalProceeds9 = value;
}

// Get<>AsString()
public string GetSDisposalProceeds9AsString()
{
    return _SDisposalProceeds9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSDisposalProceeds9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SDisposalProceeds9 = parsed;
}

// Standard Getter
public decimal GetSDisposalProceedsUnsigned()
{
    return _SDisposalProceedsUnsigned;
}

// Standard Setter
public void SetSDisposalProceedsUnsigned(decimal value)
{
    _SDisposalProceedsUnsigned = value;
}

// Get<>AsString()
public string GetSDisposalProceedsUnsignedAsString()
{
    return _SDisposalProceedsUnsigned.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSDisposalProceedsUnsignedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SDisposalProceedsUnsigned = parsed;
}

// Standard Getter
public string GetSLongTermIndicator()
{
    return _SLongTermIndicator;
}

// Standard Setter
public void SetSLongTermIndicator(string value)
{
    _SLongTermIndicator = value;
}

// Get<>AsString()
public string GetSLongTermIndicatorAsString()
{
    return _SLongTermIndicator.PadRight(1);
}

// Set<>AsString()
public void SetSLongTermIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SLongTermIndicator = value;
}

// Standard Getter
public string GetSCapitalGainOrLossX()
{
    return _SCapitalGainOrLossX;
}

// Standard Setter
public void SetSCapitalGainOrLossX(string value)
{
    _SCapitalGainOrLossX = value;
}

// Get<>AsString()
public string GetSCapitalGainOrLossXAsString()
{
    return _SCapitalGainOrLossX.PadRight(12);
}

// Set<>AsString()
public void SetSCapitalGainOrLossXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SCapitalGainOrLossX = value;
}

// Standard Getter
public decimal GetSCapitalGainOrLoss9()
{
    return _SCapitalGainOrLoss9;
}

// Standard Setter
public void SetSCapitalGainOrLoss9(decimal value)
{
    _SCapitalGainOrLoss9 = value;
}

// Get<>AsString()
public string GetSCapitalGainOrLoss9AsString()
{
    return _SCapitalGainOrLoss9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSCapitalGainOrLoss9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SCapitalGainOrLoss9 = parsed;
}

// Standard Getter
public string GetSBookCostX()
{
    return _SBookCostX;
}

// Standard Setter
public void SetSBookCostX(string value)
{
    _SBookCostX = value;
}

// Get<>AsString()
public string GetSBookCostXAsString()
{
    return _SBookCostX.PadRight(12);
}

// Set<>AsString()
public void SetSBookCostXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SBookCostX = value;
}

// Standard Getter
public decimal GetSBookCost9()
{
    return _SBookCost9;
}

// Standard Setter
public void SetSBookCost9(decimal value)
{
    _SBookCost9 = value;
}

// Get<>AsString()
public string GetSBookCost9AsString()
{
    return _SBookCost9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSBookCost9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SBookCost9 = parsed;
}

// Standard Getter
public string GetFiller170()
{
    return _Filler170;
}

// Standard Setter
public void SetFiller170(string value)
{
    _Filler170 = value;
}

// Get<>AsString()
public string GetFiller170AsString()
{
    return _Filler170.PadRight(3);
}

// Set<>AsString()
public void SetFiller170AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler170 = value;
}

// Standard Getter
public string GetSSequenceNoX()
{
    return _SSequenceNoX;
}

// Standard Setter
public void SetSSequenceNoX(string value)
{
    _SSequenceNoX = value;
}

// Get<>AsString()
public string GetSSequenceNoXAsString()
{
    return _SSequenceNoX.PadRight(0);
}

// Set<>AsString()
public void SetSSequenceNoXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SSequenceNoX = value;
}

// Standard Getter
public int GetSSequenceNo9()
{
    return _SSequenceNo9;
}

// Standard Setter
public void SetSSequenceNo9(int value)
{
    _SSequenceNo9 = value;
}

// Get<>AsString()
public string GetSSequenceNo9AsString()
{
    return _SSequenceNo9.ToString().PadLeft(9, '0');
}

// Set<>AsString()
public void SetSSequenceNo9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SSequenceNo9 = parsed;
}

// Standard Getter
public string GetSYear()
{
    return _SYear;
}

// Standard Setter
public void SetSYear(string value)
{
    _SYear = value;
}

// Get<>AsString()
public string GetSYearAsString()
{
    return _SYear.PadRight(0);
}

// Set<>AsString()
public void SetSYearAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SYear = value;
}

// Standard Getter
public string GetSPercentBusinessUseX()
{
    return _SPercentBusinessUseX;
}

// Standard Setter
public void SetSPercentBusinessUseX(string value)
{
    _SPercentBusinessUseX = value;
}

// Get<>AsString()
public string GetSPercentBusinessUseXAsString()
{
    return _SPercentBusinessUseX.PadRight(0);
}

// Set<>AsString()
public void SetSPercentBusinessUseXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SPercentBusinessUseX = value;
}

// Standard Getter
public int GetSPercentBusinessUse9()
{
    return _SPercentBusinessUse9;
}

// Standard Setter
public void SetSPercentBusinessUse9(int value)
{
    _SPercentBusinessUse9 = value;
}

// Get<>AsString()
public string GetSPercentBusinessUse9AsString()
{
    return _SPercentBusinessUse9.ToString().PadLeft(3, '0');
}

// Set<>AsString()
public void SetSPercentBusinessUse9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SPercentBusinessUse9 = parsed;
}

// Standard Getter
public string GetSTrancheFlag()
{
    return _STrancheFlag;
}

// Standard Setter
public void SetSTrancheFlag(string value)
{
    _STrancheFlag = value;
}

// Get<>AsString()
public string GetSTrancheFlagAsString()
{
    return _STrancheFlag.PadRight(0);
}

// Set<>AsString()
public void SetSTrancheFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STrancheFlag = value;
}

// Standard Getter
public string GetSLostIndexationX()
{
    return _SLostIndexationX;
}

// Standard Setter
public void SetSLostIndexationX(string value)
{
    _SLostIndexationX = value;
}

// Get<>AsString()
public string GetSLostIndexationXAsString()
{
    return _SLostIndexationX.PadRight(12);
}

// Set<>AsString()
public void SetSLostIndexationXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SLostIndexationX = value;
}

// Standard Getter
public decimal GetSLostIndexation9()
{
    return _SLostIndexation9;
}

// Standard Setter
public void SetSLostIndexation9(decimal value)
{
    _SLostIndexation9 = value;
}

// Get<>AsString()
public string GetSLostIndexation9AsString()
{
    return _SLostIndexation9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSLostIndexation9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SLostIndexation9 = parsed;
}

// Standard Getter
public string GetSOffshoreIncomeGainX()
{
    return _SOffshoreIncomeGainX;
}

// Standard Setter
public void SetSOffshoreIncomeGainX(string value)
{
    _SOffshoreIncomeGainX = value;
}

// Get<>AsString()
public string GetSOffshoreIncomeGainXAsString()
{
    return _SOffshoreIncomeGainX.PadRight(12);
}

// Set<>AsString()
public void SetSOffshoreIncomeGainXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SOffshoreIncomeGainX = value;
}

// Standard Getter
public decimal GetSOffshoreIncomeGain9()
{
    return _SOffshoreIncomeGain9;
}

// Standard Setter
public void SetSOffshoreIncomeGain9(decimal value)
{
    _SOffshoreIncomeGain9 = value;
}

// Get<>AsString()
public string GetSOffshoreIncomeGain9AsString()
{
    return _SOffshoreIncomeGain9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSOffshoreIncomeGain9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _SOffshoreIncomeGain9 = parsed;
}

// Standard Getter
public string GetSDisposalContractNo()
{
    return _SDisposalContractNo;
}

// Standard Setter
public void SetSDisposalContractNo(string value)
{
    _SDisposalContractNo = value;
}

// Get<>AsString()
public string GetSDisposalContractNoAsString()
{
    return _SDisposalContractNo.PadRight(10);
}

// Set<>AsString()
public void SetSDisposalContractNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SDisposalContractNo = value;
}

// Standard Getter
public SFullTaperDate GetSFullTaperDate()
{
    return _SFullTaperDate;
}

// Standard Setter
public void SetSFullTaperDate(SFullTaperDate value)
{
    _SFullTaperDate = value;
}

// Get<>AsString()
public string GetSFullTaperDateAsString()
{
    return _SFullTaperDate != null ? _SFullTaperDate.GetSFullTaperDateAsString() : "";
}

// Set<>AsString()
public void SetSFullTaperDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_SFullTaperDate == null)
    {
        _SFullTaperDate = new SFullTaperDate();
    }
    _SFullTaperDate.SetSFullTaperDateAsString(value);
}

// Standard Getter
public string GetSTaperUnitsX()
{
    return _STaperUnitsX;
}

// Standard Setter
public void SetSTaperUnitsX(string value)
{
    _STaperUnitsX = value;
}

// Get<>AsString()
public string GetSTaperUnitsXAsString()
{
    return _STaperUnitsX.PadRight(12);
}

// Set<>AsString()
public void SetSTaperUnitsXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STaperUnitsX = value;
}

// Standard Getter
public decimal GetSTaperUnits9()
{
    return _STaperUnits9;
}

// Standard Setter
public void SetSTaperUnits9(decimal value)
{
    _STaperUnits9 = value;
}

// Get<>AsString()
public string GetSTaperUnits9AsString()
{
    return _STaperUnits9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSTaperUnits9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _STaperUnits9 = parsed;
}

// Standard Getter
public string GetSTaperGainX()
{
    return _STaperGainX;
}

// Standard Setter
public void SetSTaperGainX(string value)
{
    _STaperGainX = value;
}

// Get<>AsString()
public string GetSTaperGainXAsString()
{
    return _STaperGainX.PadRight(12);
}

// Set<>AsString()
public void SetSTaperGainXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STaperGainX = value;
}

// Standard Getter
public decimal GetSTaperGain9()
{
    return _STaperGain9;
}

// Standard Setter
public void SetSTaperGain9(decimal value)
{
    _STaperGain9 = value;
}

// Get<>AsString()
public string GetSTaperGain9AsString()
{
    return _STaperGain9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSTaperGain9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _STaperGain9 = parsed;
}

// Standard Getter
public string GetSTaperProcX()
{
    return _STaperProcX;
}

// Standard Setter
public void SetSTaperProcX(string value)
{
    _STaperProcX = value;
}

// Get<>AsString()
public string GetSTaperProcXAsString()
{
    return _STaperProcX.PadRight(12);
}

// Set<>AsString()
public void SetSTaperProcXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STaperProcX = value;
}

// Standard Getter
public decimal GetSTaperProc9()
{
    return _STaperProc9;
}

// Standard Setter
public void SetSTaperProc9(decimal value)
{
    _STaperProc9 = value;
}

// Get<>AsString()
public string GetSTaperProc9AsString()
{
    return _STaperProc9.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetSTaperProc9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _STaperProc9 = parsed;
}

// Standard Getter
public string GetSSourceCategory()
{
    return _SSourceCategory;
}

// Standard Setter
public void SetSSourceCategory(string value)
{
    _SSourceCategory = value;
}

// Get<>AsString()
public string GetSSourceCategoryAsString()
{
    return _SSourceCategory.PadRight(0);
}

// Set<>AsString()
public void SetSSourceCategoryAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SSourceCategory = value;
}

// Standard Getter
public string GetSDisplayCategory()
{
    return _SDisplayCategory;
}

// Standard Setter
public void SetSDisplayCategory(string value)
{
    _SDisplayCategory = value;
}

// Get<>AsString()
public string GetSDisplayCategoryAsString()
{
    return _SDisplayCategory.PadRight(0);
}

// Set<>AsString()
public void SetSDisplayCategoryAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SDisplayCategory = value;
}

// Standard Getter
public string GetSCategoryId()
{
    return _SCategoryId;
}

// Standard Setter
public void SetSCategoryId(string value)
{
    _SCategoryId = value;
}

// Get<>AsString()
public string GetSCategoryIdAsString()
{
    return _SCategoryId.PadRight(0);
}

// Set<>AsString()
public void SetSCategoryIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _SCategoryId = value;
}

// Standard Getter
public string GetSTransactionCategory()
{
    return _STransactionCategory;
}

// Standard Setter
public void SetSTransactionCategory(string value)
{
    _STransactionCategory = value;
}

// Get<>AsString()
public string GetSTransactionCategoryAsString()
{
    return _STransactionCategory.PadRight(0);
}

// Set<>AsString()
public void SetSTransactionCategoryAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STransactionCategory = value;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: SMovementDate9
public class SMovementDate9
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: SMovementDateYy, is_external=, is_static_class=False, static_prefix=
    private int _SMovementDateYy =0;
    
    
    
    
    // [DEBUG] Field: SMovementDateMm, is_external=, is_static_class=False, static_prefix=
    private int _SMovementDateMm =0;
    
    
    
    
    // [DEBUG] Field: SMovementDateDd, is_external=, is_static_class=False, static_prefix=
    private int _SMovementDateDd =0;
    
    
    
    
public SMovementDate9() {}

public SMovementDate9(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSMovementDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetSMovementDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetSMovementDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetSMovementDate9AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_SMovementDateYy.ToString().PadLeft(2, '0'));
    result.Append(_SMovementDateMm.ToString().PadLeft(2, '0'));
    result.Append(_SMovementDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetSMovementDate9AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSMovementDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSMovementDateMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetSMovementDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetSMovementDateYy()
{
    return _SMovementDateYy;
}

// Standard Setter
public void SetSMovementDateYy(int value)
{
    _SMovementDateYy = value;
}

// Get<>AsString()
public string GetSMovementDateYyAsString()
{
    return _SMovementDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetSMovementDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SMovementDateYy = parsed;
}

// Standard Getter
public int GetSMovementDateMm()
{
    return _SMovementDateMm;
}

// Standard Setter
public void SetSMovementDateMm(int value)
{
    _SMovementDateMm = value;
}

// Get<>AsString()
public string GetSMovementDateMmAsString()
{
    return _SMovementDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetSMovementDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SMovementDateMm = parsed;
}

// Standard Getter
public int GetSMovementDateDd()
{
    return _SMovementDateDd;
}

// Standard Setter
public void SetSMovementDateDd(int value)
{
    _SMovementDateDd = value;
}

// Get<>AsString()
public string GetSMovementDateDdAsString()
{
    return _SMovementDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetSMovementDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _SMovementDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler158
public class Filler158
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler159, is_external=, is_static_class=False, static_prefix=
    private string _Filler159 ="";
    
    
    // 88-level condition checks for Filler159
    public bool IsTransferSale()
    {
        if (this._Filler159 == "'RG TO'") return true;
        if (this._Filler159 == "'TR TO'") return true;
        if (this._Filler159 == "'TD TO'") return true;
        if (this._Filler159 == "'CN TO'") return true;
        if (this._Filler159 == "'RO TO'") return true;
        if (this._Filler159 == "'TS TO'") return true;
        if (this._Filler159 == "'CS TO'") return true;
        if (this._Filler159 == "'GS TO'") return true;
        if (this._Filler159 == "'CL TO'") return true;
        if (this._Filler159 == "'RC TO'") return true;
        if (this._Filler159 == "'RR TO'") return true;
        return false;
    }
    public bool IsTransferAcquisition()
    {
        if (this._Filler159 == "'RG FM'") return true;
        if (this._Filler159 == "'TR FM'") return true;
        if (this._Filler159 == "'TD FM'") return true;
        if (this._Filler159 == "'CN FM'") return true;
        if (this._Filler159 == "'RO FM'") return true;
        if (this._Filler159 == "'TP FM'") return true;
        if (this._Filler159 == "'CP FM'") return true;
        if (this._Filler159 == "'GP FM'") return true;
        if (this._Filler159 == "'CL FM'") return true;
        if (this._Filler159 == "'RC FM'") return true;
        if (this._Filler159 == "'RR FM'") return true;
        return false;
    }
    public bool IsStockExerciseFm()
    {
        if (this._Filler159 == "'EX FM'") return true;
        return false;
    }
    public bool IsOptionExerciseTo()
    {
        if (this._Filler159 == "'EX TO'") return true;
        return false;
    }
    public bool IsChgTransferSale()
    {
        if (this._Filler159 == "'CT TO'") return true;
        return false;
    }
    public bool IsChgTransferAcquisition()
    {
        if (this._Filler159 == "'CT FM'") return true;
        return false;
    }
    public bool IsRcAcquisition()
    {
        if (this._Filler159 == "'RC FM'") return true;
        return false;
    }
    public bool IsRrAcquisition()
    {
        if (this._Filler159 == "'RR FM'") return true;
        return false;
    }
    public bool IsRcDisposal()
    {
        if (this._Filler159 == "'RC TO'") return true;
        return false;
    }
    public bool IsRrDisposal()
    {
        if (this._Filler159 == "'RR TO'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: Filler160, is_external=, is_static_class=False, static_prefix=
    private string _Filler160 ="";
    
    
    
    
public Filler158() {}

public Filler158(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller159(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller160(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetFiller158AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler159.PadRight(0));
    result.Append(_Filler160.PadRight(0));
    
    return result.ToString();
}

public void SetFiller158AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller159(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller160(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller159()
{
    return _Filler159;
}

// Standard Setter
public void SetFiller159(string value)
{
    _Filler159 = value;
}

// Get<>AsString()
public string GetFiller159AsString()
{
    return _Filler159.PadRight(0);
}

// Set<>AsString()
public void SetFiller159AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler159 = value;
}

// Standard Getter
public string GetFiller160()
{
    return _Filler160;
}

// Standard Setter
public void SetFiller160(string value)
{
    _Filler160 = value;
}

// Get<>AsString()
public string GetFiller160AsString()
{
    return _Filler160.PadRight(0);
}

// Set<>AsString()
public void SetFiller160AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler160 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler161
public class Filler161
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler162, is_external=, is_static_class=False, static_prefix=
    private string _Filler162 ="";
    
    
    // 88-level condition checks for Filler162
    public bool IsExerciseTransaction()
    {
        if (this._Filler162 == "'EX'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: Filler163, is_external=, is_static_class=False, static_prefix=
    private string _Filler163 ="";
    
    
    // 88-level condition checks for Filler163
    public bool IsHoldingToHoldingMovement()
    {
        if (this._Filler163 == "' TO '") return true;
        if (this._Filler163 == "' FM '") return true;
        return false;
    }
    
    
    // [DEBUG] Field: Filler164, is_external=, is_static_class=False, static_prefix=
    private string _Filler164 ="";
    
    
    
    
public Filler161() {}

public Filler161(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller162(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller163(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller164(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetFiller161AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler162.PadRight(0));
    result.Append(_Filler163.PadRight(0));
    result.Append(_Filler164.PadRight(0));
    
    return result.ToString();
}

public void SetFiller161AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller162(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller163(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller164(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller162()
{
    return _Filler162;
}

// Standard Setter
public void SetFiller162(string value)
{
    _Filler162 = value;
}

// Get<>AsString()
public string GetFiller162AsString()
{
    return _Filler162.PadRight(0);
}

// Set<>AsString()
public void SetFiller162AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler162 = value;
}

// Standard Getter
public string GetFiller163()
{
    return _Filler163;
}

// Standard Setter
public void SetFiller163(string value)
{
    _Filler163 = value;
}

// Get<>AsString()
public string GetFiller163AsString()
{
    return _Filler163.PadRight(0);
}

// Set<>AsString()
public void SetFiller163AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler163 = value;
}

// Standard Getter
public string GetFiller164()
{
    return _Filler164;
}

// Standard Setter
public void SetFiller164(string value)
{
    _Filler164 = value;
}

// Get<>AsString()
public string GetFiller164AsString()
{
    return _Filler164.PadRight(0);
}

// Set<>AsString()
public void SetFiller164AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler164 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler165
public class Filler165
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler166, is_external=, is_static_class=False, static_prefix=
    private string _Filler166 ="";
    
    
    // 88-level condition checks for Filler166
    public bool IsRightsAllotment()
    {
        if (this._Filler166 == "'RIGHTS'") return true;
        return false;
    }
    public bool IsBonusAllotment()
    {
        if (this._Filler166 == "'BONUS '") return true;
        return false;
    }
    public bool IsCompAdjustment()
    {
        if (this._Filler166 == "'ADJT. '") return true;
        return false;
    }
    
    
    // [DEBUG] Field: Filler167, is_external=, is_static_class=False, static_prefix=
    private string _Filler167 ="";
    
    
    
    
public Filler165() {}

public Filler165(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller166(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller167(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetFiller165AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler166.PadRight(0));
    result.Append(_Filler167.PadRight(0));
    
    return result.ToString();
}

public void SetFiller165AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller166(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller167(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller166()
{
    return _Filler166;
}

// Standard Setter
public void SetFiller166(string value)
{
    _Filler166 = value;
}

// Get<>AsString()
public string GetFiller166AsString()
{
    return _Filler166.PadRight(0);
}

// Set<>AsString()
public void SetFiller166AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler166 = value;
}

// Standard Getter
public string GetFiller167()
{
    return _Filler167;
}

// Standard Setter
public void SetFiller167(string value)
{
    _Filler167 = value;
}

// Get<>AsString()
public string GetFiller167AsString()
{
    return _Filler167.PadRight(0);
}

// Set<>AsString()
public void SetFiller167AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler167 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler168
public class Filler168
{
    private static int _size = 2;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CustomTranCategoryCode, is_external=, is_static_class=False, static_prefix=
    private string _CustomTranCategoryCode ="";
    
    
    
    
    // [DEBUG] Field: Filler169, is_external=, is_static_class=False, static_prefix=
    private string _Filler169 ="";
    
    
    
    
public Filler168() {}

public Filler168(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCustomTranCategoryCode(data.Substring(offset, 2).Trim());
    offset += 2;
    SetFiller169(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetFiller168AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CustomTranCategoryCode.PadRight(2));
    result.Append(_Filler169.PadRight(0));
    
    return result.ToString();
}

public void SetFiller168AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetCustomTranCategoryCode(extracted);
    }
    offset += 2;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller169(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetCustomTranCategoryCode()
{
    return _CustomTranCategoryCode;
}

// Standard Setter
public void SetCustomTranCategoryCode(string value)
{
    _CustomTranCategoryCode = value;
}

// Get<>AsString()
public string GetCustomTranCategoryCodeAsString()
{
    return _CustomTranCategoryCode.PadRight(2);
}

// Set<>AsString()
public void SetCustomTranCategoryCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CustomTranCategoryCode = value;
}

// Standard Getter
public string GetFiller169()
{
    return _Filler169;
}

// Standard Setter
public void SetFiller169(string value)
{
    _Filler169 = value;
}

// Get<>AsString()
public string GetFiller169AsString()
{
    return _Filler169.PadRight(0);
}

// Set<>AsString()
public void SetFiller169AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler169 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: SFullTaperDate
public class SFullTaperDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: STaperDateCc, is_external=, is_static_class=False, static_prefix=
    private string _STaperDateCc ="";
    
    
    
    
    // [DEBUG] Field: STaperDate, is_external=, is_static_class=False, static_prefix=
    private string _STaperDate ="";
    
    
    
    
public SFullTaperDate() {}

public SFullTaperDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetSTaperDateCc(data.Substring(offset, 0).Trim());
    offset += 0;
    SetSTaperDate(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetSFullTaperDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_STaperDateCc.PadRight(0));
    result.Append(_STaperDate.PadRight(0));
    
    return result.ToString();
}

public void SetSFullTaperDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSTaperDateCc(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetSTaperDate(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetSTaperDateCc()
{
    return _STaperDateCc;
}

// Standard Setter
public void SetSTaperDateCc(string value)
{
    _STaperDateCc = value;
}

// Get<>AsString()
public string GetSTaperDateCcAsString()
{
    return _STaperDateCc.PadRight(0);
}

// Set<>AsString()
public void SetSTaperDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STaperDateCc = value;
}

// Standard Getter
public string GetSTaperDate()
{
    return _STaperDate;
}

// Standard Setter
public void SetSTaperDate(string value)
{
    _STaperDate = value;
}

// Get<>AsString()
public string GetSTaperDateAsString()
{
    return _STaperDate.PadRight(0);
}

// Set<>AsString()
public void SetSTaperDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _STaperDate = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}

}}
