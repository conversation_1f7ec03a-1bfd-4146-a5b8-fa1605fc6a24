using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D79File Data Structure

public class D79File
{
    private static int _size = 12;
    // [DEBUG] Class: D79File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler247, is_external=, is_static_class=False, static_prefix=
    private string _Filler247 ="$";
    
    
    
    
    // [DEBUG] Field: D79UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D79UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler248, is_external=, is_static_class=False, static_prefix=
    private string _Filler248 ="ACQ.DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD79FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler247.PadRight(1));
        result.Append(_D79UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler248.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD79FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller247(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD79UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller248(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD79FileAsString();
    }
    // Set<>String Override function
    public void SetD79File(string value)
    {
        SetD79FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller247()
    {
        return _Filler247;
    }
    
    // Standard Setter
    public void SetFiller247(string value)
    {
        _Filler247 = value;
    }
    
    // Get<>AsString()
    public string GetFiller247AsString()
    {
        return _Filler247.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller247AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler247 = value;
    }
    
    // Standard Getter
    public int GetD79UserNo()
    {
        return _D79UserNo;
    }
    
    // Standard Setter
    public void SetD79UserNo(int value)
    {
        _D79UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD79UserNoAsString()
    {
        return _D79UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD79UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D79UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller248()
    {
        return _Filler248;
    }
    
    // Standard Setter
    public void SetFiller248(string value)
    {
        _Filler248 = value;
    }
    
    // Get<>AsString()
    public string GetFiller248AsString()
    {
        return _Filler248.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller248AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler248 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}