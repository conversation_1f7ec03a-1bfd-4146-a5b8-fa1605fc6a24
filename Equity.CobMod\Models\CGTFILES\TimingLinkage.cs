using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing TimingLinkage Data Structure

public class TimingLinkage
{
    private static int _size = 380;
    // [DEBUG] Class: TimingLinkage, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: TimingAction, is_external=, is_static_class=False, static_prefix=
    private int _TimingAction =0;
    
    
    // 88-level condition checks for TimingAction
    public bool IsTimingBeginSection()
    {
        if (this._TimingAction == 1) return true;
        return false;
    }
    public bool IsTimingEndSection()
    {
        if (this._TimingAction == 2) return true;
        return false;
    }
    
    
    // [DEBUG] Field: TimingSection, is_external=, is_static_class=False, static_prefix=
    private int _TimingSection =0;
    
    
    // 88-level condition checks for TimingSection
    public bool IsTimingSectionJobRunner()
    {
        if (this._TimingSection == 1) return true;
        return false;
    }
    public bool IsTimingSectionCobolRouter()
    {
        if (this._TimingSection == 2) return true;
        return false;
    }
    public bool IsTimingSectionCobolComp()
    {
        if (this._TimingSection == 3) return true;
        return false;
    }
    public bool IsTimingSectionCobolScheduleIo()
    {
        if (this._TimingSection == 4) return true;
        return false;
    }
    public bool IsTimingSectionCobolErrorDataIo()
    {
        if (this._TimingSection == 5) return true;
        return false;
    }
    public bool IsTimingSectionCobolLogIo()
    {
        if (this._TimingSection == 6) return true;
        return false;
    }
    public bool IsTimingSectionDataTrans()
    {
        if (this._TimingSection == 7) return true;
        return false;
    }
    public bool IsTimingSectionCobolStrings()
    {
        if (this._TimingSection == 8) return true;
        return false;
    }
    public bool IsTimingSectionCobolDiskTempIo()
    {
        if (this._TimingSection == 9) return true;
        return false;
    }
    public bool IsTimingSectionCsharpTempIo()
    {
        if (this._TimingSection == 10) return true;
        return false;
    }
    public bool IsTimingSectionCobolMemoryTempIo()
    {
        if (this._TimingSection == 11) return true;
        return false;
    }
    public bool IsTimingSectionCobolOtherIo()
    {
        if (this._TimingSection == 12) return true;
        return false;
    }
    
    
    // [DEBUG] Field: TimingNoOfSections, is_external=, is_static_class=False, static_prefix=
    private int _TimingNoOfSections =12;
    
    
    
    
    // [DEBUG] Field: TimingDescriptions, is_external=, is_static_class=False, static_prefix=
    private TimingDescriptions _TimingDescriptions = new TimingDescriptions();
    
    
    
    
    // [DEBUG] Field: Filler420, is_external=, is_static_class=False, static_prefix=
    private Filler420 _Filler420 = new Filler420();
    
    
    
    
    // [DEBUG] Field: TimingMessageSuffix, is_external=, is_static_class=False, static_prefix=
    private string _TimingMessageSuffix ="";
    
    
    
    
    
    // Serialization methods
    public string GetTimingLinkageAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_TimingAction.ToString().PadLeft(1, '0'));
        result.Append(_TimingSection.ToString().PadLeft(2, '0'));
        result.Append(_TimingNoOfSections.ToString().PadLeft(2, '0'));
        result.Append(_TimingDescriptions.GetTimingDescriptionsAsString());
        result.Append(_Filler420.GetFiller420AsString());
        result.Append(_TimingMessageSuffix.PadRight(50));
        
        return result.ToString();
    }
    
    public void SetTimingLinkageAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetTimingAction(parsedInt);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetTimingSection(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetTimingNoOfSections(parsedInt);
        }
        offset += 2;
        if (offset + 300 <= data.Length)
        {
            _TimingDescriptions.SetTimingDescriptionsAsString(data.Substring(offset, 300));
        }
        else
        {
            _TimingDescriptions.SetTimingDescriptionsAsString(data.Substring(offset));
        }
        offset += 300;
        if (offset + 25 <= data.Length)
        {
            _Filler420.SetFiller420AsString(data.Substring(offset, 25));
        }
        else
        {
            _Filler420.SetFiller420AsString(data.Substring(offset));
        }
        offset += 25;
        if (offset + 50 <= data.Length)
        {
            string extracted = data.Substring(offset, 50).Trim();
            SetTimingMessageSuffix(extracted);
        }
        offset += 50;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetTimingLinkageAsString();
    }
    // Set<>String Override function
    public void SetTimingLinkage(string value)
    {
        SetTimingLinkageAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetTimingAction()
    {
        return _TimingAction;
    }
    
    // Standard Setter
    public void SetTimingAction(int value)
    {
        _TimingAction = value;
    }
    
    // Get<>AsString()
    public string GetTimingActionAsString()
    {
        return _TimingAction.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetTimingActionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _TimingAction = parsed;
    }
    
    // Standard Getter
    public int GetTimingSection()
    {
        return _TimingSection;
    }
    
    // Standard Setter
    public void SetTimingSection(int value)
    {
        _TimingSection = value;
    }
    
    // Get<>AsString()
    public string GetTimingSectionAsString()
    {
        return _TimingSection.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetTimingSectionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _TimingSection = parsed;
    }
    
    // Standard Getter
    public int GetTimingNoOfSections()
    {
        return _TimingNoOfSections;
    }
    
    // Standard Setter
    public void SetTimingNoOfSections(int value)
    {
        _TimingNoOfSections = value;
    }
    
    // Get<>AsString()
    public string GetTimingNoOfSectionsAsString()
    {
        return _TimingNoOfSections.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetTimingNoOfSectionsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _TimingNoOfSections = parsed;
    }
    
    // Standard Getter
    public TimingDescriptions GetTimingDescriptions()
    {
        return _TimingDescriptions;
    }
    
    // Standard Setter
    public void SetTimingDescriptions(TimingDescriptions value)
    {
        _TimingDescriptions = value;
    }
    
    // Get<>AsString()
    public string GetTimingDescriptionsAsString()
    {
        return _TimingDescriptions != null ? _TimingDescriptions.GetTimingDescriptionsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetTimingDescriptionsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_TimingDescriptions == null)
        {
            _TimingDescriptions = new TimingDescriptions();
        }
        _TimingDescriptions.SetTimingDescriptionsAsString(value);
    }
    
    // Standard Getter
    public Filler420 GetFiller420()
    {
        return _Filler420;
    }
    
    // Standard Setter
    public void SetFiller420(Filler420 value)
    {
        _Filler420 = value;
    }
    
    // Get<>AsString()
    public string GetFiller420AsString()
    {
        return _Filler420 != null ? _Filler420.GetFiller420AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller420AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler420 == null)
        {
            _Filler420 = new Filler420();
        }
        _Filler420.SetFiller420AsString(value);
    }
    
    // Standard Getter
    public string GetTimingMessageSuffix()
    {
        return _TimingMessageSuffix;
    }
    
    // Standard Setter
    public void SetTimingMessageSuffix(string value)
    {
        _TimingMessageSuffix = value;
    }
    
    // Get<>AsString()
    public string GetTimingMessageSuffixAsString()
    {
        return _TimingMessageSuffix.PadRight(50);
    }
    
    // Set<>AsString()
    public void SetTimingMessageSuffixAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _TimingMessageSuffix = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetTimingDescriptions(string value)
    {
        _TimingDescriptions.SetTimingDescriptionsAsString(value);
    }
    // Nested Class: TimingDescriptions
    public class TimingDescriptions
    {
        private static int _size = 300;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler408, is_external=, is_static_class=False, static_prefix=
        private string _Filler408 ="Job Runner";
        
        
        
        
        // [DEBUG] Field: Filler409, is_external=, is_static_class=False, static_prefix=
        private string _Filler409 ="Cobol Router";
        
        
        
        
        // [DEBUG] Field: Filler410, is_external=, is_static_class=False, static_prefix=
        private string _Filler410 ="Cobol Comp";
        
        
        
        
        // [DEBUG] Field: Filler411, is_external=, is_static_class=False, static_prefix=
        private string _Filler411 ="Cobol Schedule IO";
        
        
        
        
        // [DEBUG] Field: Filler412, is_external=, is_static_class=False, static_prefix=
        private string _Filler412 ="Cobol Error IO";
        
        
        
        
        // [DEBUG] Field: Filler413, is_external=, is_static_class=False, static_prefix=
        private string _Filler413 ="Cobol Log IO";
        
        
        
        
        // [DEBUG] Field: Filler414, is_external=, is_static_class=False, static_prefix=
        private string _Filler414 ="Data Transformation";
        
        
        
        
        // [DEBUG] Field: Filler415, is_external=, is_static_class=False, static_prefix=
        private string _Filler415 ="Cobol String Handling";
        
        
        
        
        // [DEBUG] Field: Filler416, is_external=, is_static_class=False, static_prefix=
        private string _Filler416 ="Cobol TempFile Disk IO";
        
        
        
        
        // [DEBUG] Field: Filler417, is_external=, is_static_class=False, static_prefix=
        private string _Filler417 ="C# TempFile Handling";
        
        
        
        
        // [DEBUG] Field: Filler418, is_external=, is_static_class=False, static_prefix=
        private string _Filler418 ="Cobol TempFile Handling";
        
        
        
        
        // [DEBUG] Field: Filler419, is_external=, is_static_class=False, static_prefix=
        private string _Filler419 ="Cobol Other IO";
        
        
        
        
    public TimingDescriptions() {}
    
    public TimingDescriptions(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller408(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller409(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller410(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller411(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller412(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller413(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller414(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller415(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller416(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller417(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller418(data.Substring(offset, 25).Trim());
        offset += 25;
        SetFiller419(data.Substring(offset, 25).Trim());
        offset += 25;
        
    }
    
    // Serialization methods
    public string GetTimingDescriptionsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler408.PadRight(25));
        result.Append(_Filler409.PadRight(25));
        result.Append(_Filler410.PadRight(25));
        result.Append(_Filler411.PadRight(25));
        result.Append(_Filler412.PadRight(25));
        result.Append(_Filler413.PadRight(25));
        result.Append(_Filler414.PadRight(25));
        result.Append(_Filler415.PadRight(25));
        result.Append(_Filler416.PadRight(25));
        result.Append(_Filler417.PadRight(25));
        result.Append(_Filler418.PadRight(25));
        result.Append(_Filler419.PadRight(25));
        
        return result.ToString();
    }
    
    public void SetTimingDescriptionsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller408(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller409(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller410(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller411(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller412(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller413(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller414(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller415(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller416(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller417(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller418(extracted);
        }
        offset += 25;
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller419(extracted);
        }
        offset += 25;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller408()
    {
        return _Filler408;
    }
    
    // Standard Setter
    public void SetFiller408(string value)
    {
        _Filler408 = value;
    }
    
    // Get<>AsString()
    public string GetFiller408AsString()
    {
        return _Filler408.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller408AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler408 = value;
    }
    
    // Standard Getter
    public string GetFiller409()
    {
        return _Filler409;
    }
    
    // Standard Setter
    public void SetFiller409(string value)
    {
        _Filler409 = value;
    }
    
    // Get<>AsString()
    public string GetFiller409AsString()
    {
        return _Filler409.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller409AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler409 = value;
    }
    
    // Standard Getter
    public string GetFiller410()
    {
        return _Filler410;
    }
    
    // Standard Setter
    public void SetFiller410(string value)
    {
        _Filler410 = value;
    }
    
    // Get<>AsString()
    public string GetFiller410AsString()
    {
        return _Filler410.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller410AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler410 = value;
    }
    
    // Standard Getter
    public string GetFiller411()
    {
        return _Filler411;
    }
    
    // Standard Setter
    public void SetFiller411(string value)
    {
        _Filler411 = value;
    }
    
    // Get<>AsString()
    public string GetFiller411AsString()
    {
        return _Filler411.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller411AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler411 = value;
    }
    
    // Standard Getter
    public string GetFiller412()
    {
        return _Filler412;
    }
    
    // Standard Setter
    public void SetFiller412(string value)
    {
        _Filler412 = value;
    }
    
    // Get<>AsString()
    public string GetFiller412AsString()
    {
        return _Filler412.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller412AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler412 = value;
    }
    
    // Standard Getter
    public string GetFiller413()
    {
        return _Filler413;
    }
    
    // Standard Setter
    public void SetFiller413(string value)
    {
        _Filler413 = value;
    }
    
    // Get<>AsString()
    public string GetFiller413AsString()
    {
        return _Filler413.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller413AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler413 = value;
    }
    
    // Standard Getter
    public string GetFiller414()
    {
        return _Filler414;
    }
    
    // Standard Setter
    public void SetFiller414(string value)
    {
        _Filler414 = value;
    }
    
    // Get<>AsString()
    public string GetFiller414AsString()
    {
        return _Filler414.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller414AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler414 = value;
    }
    
    // Standard Getter
    public string GetFiller415()
    {
        return _Filler415;
    }
    
    // Standard Setter
    public void SetFiller415(string value)
    {
        _Filler415 = value;
    }
    
    // Get<>AsString()
    public string GetFiller415AsString()
    {
        return _Filler415.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller415AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler415 = value;
    }
    
    // Standard Getter
    public string GetFiller416()
    {
        return _Filler416;
    }
    
    // Standard Setter
    public void SetFiller416(string value)
    {
        _Filler416 = value;
    }
    
    // Get<>AsString()
    public string GetFiller416AsString()
    {
        return _Filler416.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller416AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler416 = value;
    }
    
    // Standard Getter
    public string GetFiller417()
    {
        return _Filler417;
    }
    
    // Standard Setter
    public void SetFiller417(string value)
    {
        _Filler417 = value;
    }
    
    // Get<>AsString()
    public string GetFiller417AsString()
    {
        return _Filler417.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller417AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler417 = value;
    }
    
    // Standard Getter
    public string GetFiller418()
    {
        return _Filler418;
    }
    
    // Standard Setter
    public void SetFiller418(string value)
    {
        _Filler418 = value;
    }
    
    // Get<>AsString()
    public string GetFiller418AsString()
    {
        return _Filler418.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller418AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler418 = value;
    }
    
    // Standard Getter
    public string GetFiller419()
    {
        return _Filler419;
    }
    
    // Standard Setter
    public void SetFiller419(string value)
    {
        _Filler419 = value;
    }
    
    // Get<>AsString()
    public string GetFiller419AsString()
    {
        return _Filler419.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller419AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler419 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetFiller420(string value)
{
    _Filler420.SetFiller420AsString(value);
}
// Nested Class: Filler420
public class Filler420
{
    private static int _size = 25;
    
    // Fields in the class
    
    
    // [DEBUG] Field: TimingElement, is_external=, is_static_class=False, static_prefix=
    private string[] _TimingElement = new string[12];
    
    
    
    
public Filler420() {}

public Filler420(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    for (int i = 0; i < 12; i++)
    {
        string value = data.Substring(offset, 25);
        _TimingElement[i] = value.Trim();
        offset += 25;
    }
    
}

// Serialization methods
public string GetFiller420AsString()
{
    StringBuilder result = new StringBuilder();
    
    for (int i = 0; i < 12; i++)
    {
        result.Append(_TimingElement[i].PadRight(25));
    }
    
    return result.ToString();
}

public void SetFiller420AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    for (int i = 0; i < 12; i++)
    {
        if (offset + 25 > data.Length) break;
        string val = data.Substring(offset, 25);
        
        _TimingElement[i] = val.Trim();
        offset += 25;
    }
}

// Getter and Setter methods

// Array Accessors for TimingElement
public string GetTimingElementAt(int index)
{
    return _TimingElement[index];
}

public void SetTimingElementAt(int index, string value)
{
    _TimingElement[index] = value;
}

public string GetTimingElementAsStringAt(int index)
{
    return _TimingElement[index].PadRight(25);
}

public void SetTimingElementAsStringAt(int index, string value)
{
    if (string.IsNullOrEmpty(value)) return;
    _TimingElement[index] = value;
}

// Flattened accessors (index 0)
public string GetTimingElement()
{
    return _TimingElement != null && _TimingElement.Length > 0
    ? _TimingElement[0]
    : default(string);
}

public void SetTimingElement(string value)
{
    if (_TimingElement == null || _TimingElement.Length == 0)
    _TimingElement = new string[1];
    _TimingElement[0] = value;
}

public string GetTimingElementAsString()
{
    return _TimingElement != null && _TimingElement.Length > 0
    ? _TimingElement[0].ToString()
    : string.Empty;
}

public void SetTimingElementAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    if (_TimingElement == null || _TimingElement.Length == 0)
    _TimingElement = new string[1];
    
    _TimingElement[0] = value;
}




public static int GetSize()
{
    return _size;
}

}

}}