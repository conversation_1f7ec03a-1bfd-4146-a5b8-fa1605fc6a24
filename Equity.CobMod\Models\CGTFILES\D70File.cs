using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D70File Data Structure

public class D70File
{
    private static int _size = 128;
    // [DEBUG] Class: D70File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler223, is_external=, is_static_class=False, static_prefix=
    private string _Filler223 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD70FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler223.PadRight(128));
        
        return result.ToString();
    }
    
    public void SetD70FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 128 <= data.Length)
        {
            string extracted = data.Substring(offset, 128).Trim();
            SetFiller223(extracted);
        }
        offset += 128;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD70FileAsString();
    }
    // Set<>String Override function
    public void SetD70File(string value)
    {
        SetD70FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller223()
    {
        return _Filler223;
    }
    
    // Standard Setter
    public void SetFiller223(string value)
    {
        _Filler223 = value;
    }
    
    // Get<>AsString()
    public string GetFiller223AsString()
    {
        return _Filler223.PadRight(128);
    }
    
    // Set<>AsString()
    public void SetFiller223AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler223 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}