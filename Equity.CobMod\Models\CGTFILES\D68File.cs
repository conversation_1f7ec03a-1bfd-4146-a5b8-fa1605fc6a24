using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D68File Data Structure

public class D68File
{
    private static int _size = 128;
    // [DEBUG] Class: D68File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler220, is_external=, is_static_class=False, static_prefix=
    private string _Filler220 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD68FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler220.PadRight(128));
        
        return result.ToString();
    }
    
    public void SetD68FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 128 <= data.Length)
        {
            string extracted = data.Substring(offset, 128).Trim();
            SetFiller220(extracted);
        }
        offset += 128;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD68FileAsString();
    }
    // Set<>String Override function
    public void SetD68File(string value)
    {
        SetD68FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller220()
    {
        return _Filler220;
    }
    
    // Standard Setter
    public void SetFiller220(string value)
    {
        _Filler220 = value;
    }
    
    // Get<>AsString()
    public string GetFiller220AsString()
    {
        return _Filler220.PadRight(128);
    }
    
    // Set<>AsString()
    public void SetFiller220AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler220 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}