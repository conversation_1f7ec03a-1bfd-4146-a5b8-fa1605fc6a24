using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D78File Data Structure

public class D78File
{
    private static int _size = 12;
    // [DEBUG] Class: D78File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler244, is_external=, is_static_class=False, static_prefix=
    private string _Filler244 ="$";
    
    
    
    
    // [DEBUG] Field: D78UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D78UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler245, is_external=, is_static_class=False, static_prefix=
    private string _Filler245 ="RI";
    
    
    
    
    // [DEBUG] Field: D78ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D78ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler246, is_external=, is_static_class=False, static_prefix=
    private string _Filler246 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD78FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler244.PadRight(1));
        result.Append(_D78UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler245.PadRight(2));
        result.Append(_D78ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler246.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD78FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller244(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD78UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller245(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD78ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller246(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD78FileAsString();
    }
    // Set<>String Override function
    public void SetD78File(string value)
    {
        SetD78FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller244()
    {
        return _Filler244;
    }
    
    // Standard Setter
    public void SetFiller244(string value)
    {
        _Filler244 = value;
    }
    
    // Get<>AsString()
    public string GetFiller244AsString()
    {
        return _Filler244.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller244AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler244 = value;
    }
    
    // Standard Getter
    public int GetD78UserNo()
    {
        return _D78UserNo;
    }
    
    // Standard Setter
    public void SetD78UserNo(int value)
    {
        _D78UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD78UserNoAsString()
    {
        return _D78UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD78UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D78UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller245()
    {
        return _Filler245;
    }
    
    // Standard Setter
    public void SetFiller245(string value)
    {
        _Filler245 = value;
    }
    
    // Get<>AsString()
    public string GetFiller245AsString()
    {
        return _Filler245.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller245AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler245 = value;
    }
    
    // Standard Getter
    public int GetD78ReportNo()
    {
        return _D78ReportNo;
    }
    
    // Standard Setter
    public void SetD78ReportNo(int value)
    {
        _D78ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD78ReportNoAsString()
    {
        return _D78ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD78ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D78ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller246()
    {
        return _Filler246;
    }
    
    // Standard Setter
    public void SetFiller246(string value)
    {
        _Filler246 = value;
    }
    
    // Get<>AsString()
    public string GetFiller246AsString()
    {
        return _Filler246.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller246AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler246 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}