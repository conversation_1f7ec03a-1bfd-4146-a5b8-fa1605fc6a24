using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// DTO class representing WTodaysDate Data Structure

public class WTodaysDate
{
    private static int _size = 0;
    // [DEBUG] Class: WTodaysDate, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WTodaysYy, is_external=, is_static_class=False, static_prefix=
    private string _WTodaysYy ="";
    
    
    
    
    // [DEBUG] Field: WTodaysMm, is_external=, is_static_class=False, static_prefix=
    private string _WTodaysMm ="";
    
    
    
    
    // [DEBUG] Field: WTodaysDd, is_external=, is_static_class=False, static_prefix=
    private string _WTodaysDd ="";
    
    
    
    
    
    // Serialization methods
    public string GetWTodaysDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WTodaysYy.PadRight(0));
        result.Append(_WTodaysMm.PadRight(0));
        result.Append(_WTodaysDd.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetWTodaysDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWTodaysYy(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWTodaysMm(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWTodaysDd(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWTodaysDateAsString();
    }
    // Set<>String Override function
    public void SetWTodaysDate(string value)
    {
        SetWTodaysDateAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWTodaysYy()
    {
        return _WTodaysYy;
    }
    
    // Standard Setter
    public void SetWTodaysYy(string value)
    {
        _WTodaysYy = value;
    }
    
    // Get<>AsString()
    public string GetWTodaysYyAsString()
    {
        return _WTodaysYy.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWTodaysYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WTodaysYy = value;
    }
    
    // Standard Getter
    public string GetWTodaysMm()
    {
        return _WTodaysMm;
    }
    
    // Standard Setter
    public void SetWTodaysMm(string value)
    {
        _WTodaysMm = value;
    }
    
    // Get<>AsString()
    public string GetWTodaysMmAsString()
    {
        return _WTodaysMm.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWTodaysMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WTodaysMm = value;
    }
    
    // Standard Getter
    public string GetWTodaysDd()
    {
        return _WTodaysDd;
    }
    
    // Standard Setter
    public void SetWTodaysDd(string value)
    {
        _WTodaysDd = value;
    }
    
    // Get<>AsString()
    public string GetWTodaysDdAsString()
    {
        return _WTodaysDd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWTodaysDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WTodaysDd = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}