using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing CgtMasterRecord01 Data Structure

public class CgtMasterRecord01
{
    private static int _size = 356;
    // [DEBUG] Class: CgtMasterRecord01, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: CgtRecordKey, is_external=, is_static_class=False, static_prefix=
    private CgtRecordKey _CgtRecordKey = new CgtRecordKey();
    
    
    
    
    // [DEBUG] Field: CgtTransactionCategory, is_external=, is_static_class=False, static_prefix=
    private string _CgtTransactionCategory ="";
    
    
    // 88-level condition checks for CgtTransactionCategory
    public bool IsDualOrMultipleTransaction()
    {
        if (this._CgtTransactionCategory == "'CL'") return true;
        if (this._CgtTransactionCategory == "'CN'") return true;
        if (this._CgtTransactionCategory == "'CP'") return true;
        if (this._CgtTransactionCategory == "'CS'") return true;
        if (this._CgtTransactionCategory == "'CT'") return true;
        if (this._CgtTransactionCategory == "'GP'") return true;
        if (this._CgtTransactionCategory == "'GS'") return true;
        if (this._CgtTransactionCategory == "'RC'") return true;
        if (this._CgtTransactionCategory == "'RG'") return true;
        if (this._CgtTransactionCategory == "'RO'") return true;
        if (this._CgtTransactionCategory == "'RR'") return true;
        if (this._CgtTransactionCategory == "'TD'") return true;
        if (this._CgtTransactionCategory == "'TP'") return true;
        if (this._CgtTransactionCategory == "'TR'") return true;
        if (this._CgtTransactionCategory == "'TS'") return true;
        if (this._CgtTransactionCategory == "'EC'") return true;
        if (this._CgtTransactionCategory == "'EP'") return true;
        if (this._CgtTransactionCategory == "'WC'") return true;
        if (this._CgtTransactionCategory == "'WP'") return true;
        return false;
    }
    public bool IsApportionedTransfer()
    {
        if (this._CgtTransactionCategory == "'RG'") return true;
        if (this._CgtTransactionCategory == "'CN'") return true;
        if (this._CgtTransactionCategory == "'TR'") return true;
        if (this._CgtTransactionCategory == "'TS'") return true;
        if (this._CgtTransactionCategory == "'CS'") return true;
        if (this._CgtTransactionCategory == "'TD'") return true;
        if (this._CgtTransactionCategory == "'TP'") return true;
        if (this._CgtTransactionCategory == "'CP'") return true;
        if (this._CgtTransactionCategory == "'RO'") return true;
        return false;
    }
    public bool IsGroupTransfer()
    {
        if (this._CgtTransactionCategory == "'GS'") return true;
        if (this._CgtTransactionCategory == "'GP'") return true;
        return false;
    }
    public bool IsSingleAcquisitionTransaction()
    {
        if (this._CgtTransactionCategory == "'SP'") return true;
        if (this._CgtTransactionCategory == "'PI'") return true;
        if (this._CgtTransactionCategory == "'NP'") return true;
        if (this._CgtTransactionCategory == "'NI'") return true;
        if (this._CgtTransactionCategory == "'RP'") return true;
        if (this._CgtTransactionCategory == "'UL'") return true;
        if (this._CgtTransactionCategory == "'SX'") return true;
        return false;
    }
    public bool IsOptionExercise()
    {
        if (this._CgtTransactionCategory == "'EC'") return true;
        if (this._CgtTransactionCategory == "'EP'") return true;
        if (this._CgtTransactionCategory == "'WC'") return true;
        if (this._CgtTransactionCategory == "'WP'") return true;
        return false;
    }
    public bool IsBalanceRecord()
    {
        if (this._CgtTransactionCategory == "'00'") return true;
        if (this._CgtTransactionCategory == "'PP'") return true;
        if (this._CgtTransactionCategory == "'TB'") return true;
        if (this._CgtTransactionCategory == "'01'") return true;
        return false;
    }
    public bool IsBonusAllotmentTransaction()
    {
        if (this._CgtTransactionCategory == "'BA'") return true;
        return false;
    }
    public bool IsWrittenOption()
    {
        if (this._CgtTransactionCategory == "'WP'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: CgtDateTimeStamp, is_external=, is_static_class=False, static_prefix=
    private CgtDateTimeStamp _CgtDateTimeStamp = new CgtDateTimeStamp();
    
    
    
    
    // [DEBUG] Field: CgtParentKey, is_external=, is_static_class=False, static_prefix=
    private CgtParentKey _CgtParentKey = new CgtParentKey();
    
    
    
    
    // [DEBUG] Field: Filler174, is_external=, is_static_class=False, static_prefix=
    private Filler174 _Filler174 = new Filler174();
    
    
    
    
    // [DEBUG] Field: CgtPreviousKey, is_external=, is_static_class=False, static_prefix=
    private CgtPreviousKey _CgtPreviousKey = new CgtPreviousKey();
    
    
    
    
    // [DEBUG] Field: CgtBargainKey, is_external=, is_static_class=False, static_prefix=
    private CgtBargainKey _CgtBargainKey = new CgtBargainKey();
    
    
    
    
    // [DEBUG] Field: CgtBargainDate, is_external=, is_static_class=False, static_prefix=
    private string _CgtBargainDate ="";
    
    
    
    
    // [DEBUG] Field: CgtSettlementDate, is_external=, is_static_class=False, static_prefix=
    private string _CgtSettlementDate ="";
    
    
    
    
    // [DEBUG] Field: Filler177, is_external=, is_static_class=False, static_prefix=
    private string _Filler177 ="";
    
    
    
    
    // [DEBUG] Field: CgtSecurityType, is_external=, is_static_class=False, static_prefix=
    private string _CgtSecurityType ="";
    
    
    
    
    // [DEBUG] Field: Filler178, is_external=, is_static_class=False, static_prefix=
    private string _Filler178 ="";
    
    
    
    
    // [DEBUG] Field: CgtHoldingFlag, is_external=, is_static_class=False, static_prefix=
    private string _CgtHoldingFlag ="";
    
    
    
    
    // [DEBUG] Field: Filler179, is_external=, is_static_class=False, static_prefix=
    private string _Filler179 ="";
    
    
    
    
    
    // Serialization methods
    public string GetCgtMasterRecord01AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_CgtRecordKey.GetCgtRecordKeyAsString());
        result.Append(_CgtTransactionCategory.PadRight(2));
        result.Append(_CgtDateTimeStamp.GetCgtDateTimeStampAsString());
        result.Append(_CgtParentKey.GetCgtParentKeyAsString());
        result.Append(_Filler174.GetFiller174AsString());
        result.Append(_CgtPreviousKey.GetCgtPreviousKeyAsString());
        result.Append(_CgtBargainKey.GetCgtBargainKeyAsString());
        result.Append(_CgtBargainDate.PadRight(6));
        result.Append(_CgtSettlementDate.PadRight(6));
        result.Append(_Filler177.PadRight(1));
        result.Append(_CgtSecurityType.PadRight(1));
        result.Append(_Filler178.PadRight(188));
        result.Append(_CgtHoldingFlag.PadRight(1));
        result.Append(_Filler179.PadRight(61));
        
        return result.ToString();
    }
    
    public void SetCgtMasterRecord01AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 33 <= data.Length)
        {
            _CgtRecordKey.SetCgtRecordKeyAsString(data.Substring(offset, 33));
        }
        else
        {
            _CgtRecordKey.SetCgtRecordKeyAsString(data.Substring(offset));
        }
        offset += 33;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtTransactionCategory(extracted);
        }
        offset += 2;
        if (offset + 14 <= data.Length)
        {
            _CgtDateTimeStamp.SetCgtDateTimeStampAsString(data.Substring(offset, 14));
        }
        else
        {
            _CgtDateTimeStamp.SetCgtDateTimeStampAsString(data.Substring(offset));
        }
        offset += 14;
        if (offset + 11 <= data.Length)
        {
            _CgtParentKey.SetCgtParentKeyAsString(data.Substring(offset, 11));
        }
        else
        {
            _CgtParentKey.SetCgtParentKeyAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            _Filler174.SetFiller174AsString(data.Substring(offset, 11));
        }
        else
        {
            _Filler174.SetFiller174AsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            _CgtPreviousKey.SetCgtPreviousKeyAsString(data.Substring(offset, 11));
        }
        else
        {
            _CgtPreviousKey.SetCgtPreviousKeyAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 10 <= data.Length)
        {
            _CgtBargainKey.SetCgtBargainKeyAsString(data.Substring(offset, 10));
        }
        else
        {
            _CgtBargainKey.SetCgtBargainKeyAsString(data.Substring(offset));
        }
        offset += 10;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetCgtBargainDate(extracted);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetCgtSettlementDate(extracted);
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller177(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetCgtSecurityType(extracted);
        }
        offset += 1;
        if (offset + 188 <= data.Length)
        {
            string extracted = data.Substring(offset, 188).Trim();
            SetFiller178(extracted);
        }
        offset += 188;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetCgtHoldingFlag(extracted);
        }
        offset += 1;
        if (offset + 61 <= data.Length)
        {
            string extracted = data.Substring(offset, 61).Trim();
            SetFiller179(extracted);
        }
        offset += 61;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtMasterRecord01AsString();
    }
    // Set<>String Override function
    public void SetCgtMasterRecord01(string value)
    {
        SetCgtMasterRecord01AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public CgtRecordKey GetCgtRecordKey()
    {
        return _CgtRecordKey;
    }
    
    // Standard Setter
    public void SetCgtRecordKey(CgtRecordKey value)
    {
        _CgtRecordKey = value;
    }
    
    // Get<>AsString()
    public string GetCgtRecordKeyAsString()
    {
        return _CgtRecordKey != null ? _CgtRecordKey.GetCgtRecordKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtRecordKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CgtRecordKey == null)
        {
            _CgtRecordKey = new CgtRecordKey();
        }
        _CgtRecordKey.SetCgtRecordKeyAsString(value);
    }
    
    // Standard Getter
    public string GetCgtTransactionCategory()
    {
        return _CgtTransactionCategory;
    }
    
    // Standard Setter
    public void SetCgtTransactionCategory(string value)
    {
        _CgtTransactionCategory = value;
    }
    
    // Get<>AsString()
    public string GetCgtTransactionCategoryAsString()
    {
        return _CgtTransactionCategory.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtTransactionCategoryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtTransactionCategory = value;
    }
    
    // Standard Getter
    public CgtDateTimeStamp GetCgtDateTimeStamp()
    {
        return _CgtDateTimeStamp;
    }
    
    // Standard Setter
    public void SetCgtDateTimeStamp(CgtDateTimeStamp value)
    {
        _CgtDateTimeStamp = value;
    }
    
    // Get<>AsString()
    public string GetCgtDateTimeStampAsString()
    {
        return _CgtDateTimeStamp != null ? _CgtDateTimeStamp.GetCgtDateTimeStampAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtDateTimeStampAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CgtDateTimeStamp == null)
        {
            _CgtDateTimeStamp = new CgtDateTimeStamp();
        }
        _CgtDateTimeStamp.SetCgtDateTimeStampAsString(value);
    }
    
    // Standard Getter
    public CgtParentKey GetCgtParentKey()
    {
        return _CgtParentKey;
    }
    
    // Standard Setter
    public void SetCgtParentKey(CgtParentKey value)
    {
        _CgtParentKey = value;
    }
    
    // Get<>AsString()
    public string GetCgtParentKeyAsString()
    {
        return _CgtParentKey != null ? _CgtParentKey.GetCgtParentKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtParentKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CgtParentKey == null)
        {
            _CgtParentKey = new CgtParentKey();
        }
        _CgtParentKey.SetCgtParentKeyAsString(value);
    }
    
    // Standard Getter
    public Filler174 GetFiller174()
    {
        return _Filler174;
    }
    
    // Standard Setter
    public void SetFiller174(Filler174 value)
    {
        _Filler174 = value;
    }
    
    // Get<>AsString()
    public string GetFiller174AsString()
    {
        return _Filler174 != null ? _Filler174.GetFiller174AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller174AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler174 == null)
        {
            _Filler174 = new Filler174();
        }
        _Filler174.SetFiller174AsString(value);
    }
    
    // Standard Getter
    public CgtPreviousKey GetCgtPreviousKey()
    {
        return _CgtPreviousKey;
    }
    
    // Standard Setter
    public void SetCgtPreviousKey(CgtPreviousKey value)
    {
        _CgtPreviousKey = value;
    }
    
    // Get<>AsString()
    public string GetCgtPreviousKeyAsString()
    {
        return _CgtPreviousKey != null ? _CgtPreviousKey.GetCgtPreviousKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtPreviousKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CgtPreviousKey == null)
        {
            _CgtPreviousKey = new CgtPreviousKey();
        }
        _CgtPreviousKey.SetCgtPreviousKeyAsString(value);
    }
    
    // Standard Getter
    public CgtBargainKey GetCgtBargainKey()
    {
        return _CgtBargainKey;
    }
    
    // Standard Setter
    public void SetCgtBargainKey(CgtBargainKey value)
    {
        _CgtBargainKey = value;
    }
    
    // Get<>AsString()
    public string GetCgtBargainKeyAsString()
    {
        return _CgtBargainKey != null ? _CgtBargainKey.GetCgtBargainKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtBargainKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CgtBargainKey == null)
        {
            _CgtBargainKey = new CgtBargainKey();
        }
        _CgtBargainKey.SetCgtBargainKeyAsString(value);
    }
    
    // Standard Getter
    public string GetCgtBargainDate()
    {
        return _CgtBargainDate;
    }
    
    // Standard Setter
    public void SetCgtBargainDate(string value)
    {
        _CgtBargainDate = value;
    }
    
    // Get<>AsString()
    public string GetCgtBargainDateAsString()
    {
        return _CgtBargainDate.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetCgtBargainDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtBargainDate = value;
    }
    
    // Standard Getter
    public string GetCgtSettlementDate()
    {
        return _CgtSettlementDate;
    }
    
    // Standard Setter
    public void SetCgtSettlementDate(string value)
    {
        _CgtSettlementDate = value;
    }
    
    // Get<>AsString()
    public string GetCgtSettlementDateAsString()
    {
        return _CgtSettlementDate.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetCgtSettlementDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtSettlementDate = value;
    }
    
    // Standard Getter
    public string GetFiller177()
    {
        return _Filler177;
    }
    
    // Standard Setter
    public void SetFiller177(string value)
    {
        _Filler177 = value;
    }
    
    // Get<>AsString()
    public string GetFiller177AsString()
    {
        return _Filler177.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller177AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler177 = value;
    }
    
    // Standard Getter
    public string GetCgtSecurityType()
    {
        return _CgtSecurityType;
    }
    
    // Standard Setter
    public void SetCgtSecurityType(string value)
    {
        _CgtSecurityType = value;
    }
    
    // Get<>AsString()
    public string GetCgtSecurityTypeAsString()
    {
        return _CgtSecurityType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetCgtSecurityTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtSecurityType = value;
    }
    
    // Standard Getter
    public string GetFiller178()
    {
        return _Filler178;
    }
    
    // Standard Setter
    public void SetFiller178(string value)
    {
        _Filler178 = value;
    }
    
    // Get<>AsString()
    public string GetFiller178AsString()
    {
        return _Filler178.PadRight(188);
    }
    
    // Set<>AsString()
    public void SetFiller178AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler178 = value;
    }
    
    // Standard Getter
    public string GetCgtHoldingFlag()
    {
        return _CgtHoldingFlag;
    }
    
    // Standard Setter
    public void SetCgtHoldingFlag(string value)
    {
        _CgtHoldingFlag = value;
    }
    
    // Get<>AsString()
    public string GetCgtHoldingFlagAsString()
    {
        return _CgtHoldingFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetCgtHoldingFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtHoldingFlag = value;
    }
    
    // Standard Getter
    public string GetFiller179()
    {
        return _Filler179;
    }
    
    // Standard Setter
    public void SetFiller179(string value)
    {
        _Filler179 = value;
    }
    
    // Get<>AsString()
    public string GetFiller179AsString()
    {
        return _Filler179.PadRight(61);
    }
    
    // Set<>AsString()
    public void SetFiller179AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler179 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetCgtRecordKey(string value)
    {
        _CgtRecordKey.SetCgtRecordKeyAsString(value);
    }
    // Nested Class: CgtRecordKey
    public class CgtRecordKey
    {
        private static int _size = 33;
        
        // Fields in the class
        
        
        // [DEBUG] Field: CgtCalSedol, is_external=, is_static_class=False, static_prefix=
        private CgtRecordKey.CgtCalSedol _CgtCalSedol = new CgtRecordKey.CgtCalSedol();
        
        
        
        
        // [DEBUG] Field: CgtContractNo, is_external=, is_static_class=False, static_prefix=
        private CgtRecordKey.CgtContractNo _CgtContractNo = new CgtRecordKey.CgtContractNo();
        
        
        
        
        // [DEBUG] Field: CgtRecordCode, is_external=, is_static_class=False, static_prefix=
        private string _CgtRecordCode ="";
        
        
        
        
    public CgtRecordKey() {}
    
    public CgtRecordKey(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _CgtCalSedol.SetCgtCalSedolAsString(data.Substring(offset, CgtCalSedol.GetSize()));
        offset += 11;
        _CgtContractNo.SetCgtContractNoAsString(data.Substring(offset, CgtContractNo.GetSize()));
        offset += 20;
        SetCgtRecordCode(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtRecordKeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_CgtCalSedol.GetCgtCalSedolAsString());
        result.Append(_CgtContractNo.GetCgtContractNoAsString());
        result.Append(_CgtRecordCode.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtRecordKeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 11 <= data.Length)
        {
            _CgtCalSedol.SetCgtCalSedolAsString(data.Substring(offset, 11));
        }
        else
        {
            _CgtCalSedol.SetCgtCalSedolAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 20 <= data.Length)
        {
            _CgtContractNo.SetCgtContractNoAsString(data.Substring(offset, 20));
        }
        else
        {
            _CgtContractNo.SetCgtContractNoAsString(data.Substring(offset));
        }
        offset += 20;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtRecordCode(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public CgtCalSedol GetCgtCalSedol()
    {
        return _CgtCalSedol;
    }
    
    // Standard Setter
    public void SetCgtCalSedol(CgtCalSedol value)
    {
        _CgtCalSedol = value;
    }
    
    // Get<>AsString()
    public string GetCgtCalSedolAsString()
    {
        return _CgtCalSedol != null ? _CgtCalSedol.GetCgtCalSedolAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtCalSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CgtCalSedol == null)
        {
            _CgtCalSedol = new CgtCalSedol();
        }
        _CgtCalSedol.SetCgtCalSedolAsString(value);
    }
    
    // Standard Getter
    public CgtContractNo GetCgtContractNo()
    {
        return _CgtContractNo;
    }
    
    // Standard Setter
    public void SetCgtContractNo(CgtContractNo value)
    {
        _CgtContractNo = value;
    }
    
    // Get<>AsString()
    public string GetCgtContractNoAsString()
    {
        return _CgtContractNo != null ? _CgtContractNo.GetCgtContractNoAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtContractNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CgtContractNo == null)
        {
            _CgtContractNo = new CgtContractNo();
        }
        _CgtContractNo.SetCgtContractNoAsString(value);
    }
    
    // Standard Getter
    public string GetCgtRecordCode()
    {
        return _CgtRecordCode;
    }
    
    // Standard Setter
    public void SetCgtRecordCode(string value)
    {
        _CgtRecordCode = value;
    }
    
    // Get<>AsString()
    public string GetCgtRecordCodeAsString()
    {
        return _CgtRecordCode.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtRecordCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtRecordCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: CgtCalSedol
    public class CgtCalSedol
    {
        private static int _size = 11;
        
        // Fields in the class
        
        
        // [DEBUG] Field: CgtCoAcLk, is_external=, is_static_class=False, static_prefix=
        private string _CgtCoAcLk ="";
        
        
        
        
        // [DEBUG] Field: CgtSedol, is_external=, is_static_class=False, static_prefix=
        private string _CgtSedol ="";
        
        
        
        
    public CgtCalSedol() {}
    
    public CgtCalSedol(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtCoAcLk(data.Substring(offset, 4).Trim());
        offset += 4;
        SetCgtSedol(data.Substring(offset, 7).Trim());
        offset += 7;
        
    }
    
    // Serialization methods
    public string GetCgtCalSedolAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_CgtCoAcLk.PadRight(4));
        result.Append(_CgtSedol.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetCgtCalSedolAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetCgtCoAcLk(extracted);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetCgtSedol(extracted);
        }
        offset += 7;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtCoAcLk()
    {
        return _CgtCoAcLk;
    }
    
    // Standard Setter
    public void SetCgtCoAcLk(string value)
    {
        _CgtCoAcLk = value;
    }
    
    // Get<>AsString()
    public string GetCgtCoAcLkAsString()
    {
        return _CgtCoAcLk.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetCgtCoAcLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtCoAcLk = value;
    }
    
    // Standard Getter
    public string GetCgtSedol()
    {
        return _CgtSedol;
    }
    
    // Standard Setter
    public void SetCgtSedol(string value)
    {
        _CgtSedol = value;
    }
    
    // Get<>AsString()
    public string GetCgtSedolAsString()
    {
        return _CgtSedol.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetCgtSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtSedol = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: CgtContractNo
public class CgtContractNo
{
    private static int _size = 20;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtContract, is_external=, is_static_class=False, static_prefix=
    private CgtContractNo.CgtContract _CgtContract = new CgtContractNo.CgtContract();
    
    
    
    
    // [DEBUG] Field: Filler172, is_external=, is_static_class=False, static_prefix=
    private CgtContractNo.Filler172 _Filler172 = new CgtContractNo.Filler172();
    
    
    
    
public CgtContractNo() {}

public CgtContractNo(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _CgtContract.SetCgtContractAsString(data.Substring(offset, CgtContract.GetSize()));
    offset += 10;
    _Filler172.SetFiller172AsString(data.Substring(offset, Filler172.GetSize()));
    offset += 10;
    
}

// Serialization methods
public string GetCgtContractNoAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtContract.GetCgtContractAsString());
    result.Append(_Filler172.GetFiller172AsString());
    
    return result.ToString();
}

public void SetCgtContractNoAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 10 <= data.Length)
    {
        _CgtContract.SetCgtContractAsString(data.Substring(offset, 10));
    }
    else
    {
        _CgtContract.SetCgtContractAsString(data.Substring(offset));
    }
    offset += 10;
    if (offset + 10 <= data.Length)
    {
        _Filler172.SetFiller172AsString(data.Substring(offset, 10));
    }
    else
    {
        _Filler172.SetFiller172AsString(data.Substring(offset));
    }
    offset += 10;
}

// Getter and Setter methods

// Standard Getter
public CgtContract GetCgtContract()
{
    return _CgtContract;
}

// Standard Setter
public void SetCgtContract(CgtContract value)
{
    _CgtContract = value;
}

// Get<>AsString()
public string GetCgtContractAsString()
{
    return _CgtContract != null ? _CgtContract.GetCgtContractAsString() : "";
}

// Set<>AsString()
public void SetCgtContractAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtContract == null)
    {
        _CgtContract = new CgtContract();
    }
    _CgtContract.SetCgtContractAsString(value);
}

// Standard Getter
public Filler172 GetFiller172()
{
    return _Filler172;
}

// Standard Setter
public void SetFiller172(Filler172 value)
{
    _Filler172 = value;
}

// Get<>AsString()
public string GetFiller172AsString()
{
    return _Filler172 != null ? _Filler172.GetFiller172AsString() : "";
}

// Set<>AsString()
public void SetFiller172AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler172 == null)
    {
        _Filler172 = new Filler172();
    }
    _Filler172.SetFiller172AsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: CgtContract
public class CgtContract
{
    private static int _size = 10;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtContractPrefix, is_external=, is_static_class=False, static_prefix=
    private string _CgtContractPrefix ="";
    
    
    
    
    // [DEBUG] Field: CgtContractSuffix, is_external=, is_static_class=False, static_prefix=
    private string _CgtContractSuffix ="";
    
    
    
    
public CgtContract() {}

public CgtContract(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtContractPrefix(data.Substring(offset, 7).Trim());
    offset += 7;
    SetCgtContractSuffix(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetCgtContractAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtContractPrefix.PadRight(7));
    result.Append(_CgtContractSuffix.PadRight(3));
    
    return result.ToString();
}

public void SetCgtContractAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetCgtContractPrefix(extracted);
    }
    offset += 7;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetCgtContractSuffix(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetCgtContractPrefix()
{
    return _CgtContractPrefix;
}

// Standard Setter
public void SetCgtContractPrefix(string value)
{
    _CgtContractPrefix = value;
}

// Get<>AsString()
public string GetCgtContractPrefixAsString()
{
    return _CgtContractPrefix.PadRight(7);
}

// Set<>AsString()
public void SetCgtContractPrefixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtContractPrefix = value;
}

// Standard Getter
public string GetCgtContractSuffix()
{
    return _CgtContractSuffix;
}

// Standard Setter
public void SetCgtContractSuffix(string value)
{
    _CgtContractSuffix = value;
}

// Get<>AsString()
public string GetCgtContractSuffixAsString()
{
    return _CgtContractSuffix.PadRight(3);
}

// Set<>AsString()
public void SetCgtContractSuffixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtContractSuffix = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler172
public class Filler172
{
    private static int _size = 10;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtContract12, is_external=, is_static_class=False, static_prefix=
    private string _CgtContract12 ="";
    
    
    
    
    // [DEBUG] Field: Filler173, is_external=, is_static_class=False, static_prefix=
    private string _Filler173 ="";
    
    
    
    
public Filler172() {}

public Filler172(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtContract12(data.Substring(offset, 2).Trim());
    offset += 2;
    SetFiller173(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetFiller172AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtContract12.PadRight(2));
    result.Append(_Filler173.PadRight(8));
    
    return result.ToString();
}

public void SetFiller172AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetCgtContract12(extracted);
    }
    offset += 2;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller173(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetCgtContract12()
{
    return _CgtContract12;
}

// Standard Setter
public void SetCgtContract12(string value)
{
    _CgtContract12 = value;
}

// Get<>AsString()
public string GetCgtContract12AsString()
{
    return _CgtContract12.PadRight(2);
}

// Set<>AsString()
public void SetCgtContract12AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtContract12 = value;
}

// Standard Getter
public string GetFiller173()
{
    return _Filler173;
}

// Standard Setter
public void SetFiller173(string value)
{
    _Filler173 = value;
}

// Get<>AsString()
public string GetFiller173AsString()
{
    return _Filler173.PadRight(8);
}

// Set<>AsString()
public void SetFiller173AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler173 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}
// Set<>String Override function (Nested)
public void SetCgtDateTimeStamp(string value)
{
    _CgtDateTimeStamp.SetCgtDateTimeStampAsString(value);
}
// Nested Class: CgtDateTimeStamp
public class CgtDateTimeStamp
{
    private static int _size = 14;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtStamp, is_external=, is_static_class=False, static_prefix=
    private string _CgtStamp ="";
    
    
    
    
    // [DEBUG] Field: CgtPartly, is_external=, is_static_class=False, static_prefix=
    private string _CgtPartly ="";
    
    
    
    
public CgtDateTimeStamp() {}

public CgtDateTimeStamp(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtStamp(data.Substring(offset, 13).Trim());
    offset += 13;
    SetCgtPartly(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetCgtDateTimeStampAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtStamp.PadRight(13));
    result.Append(_CgtPartly.PadRight(1));
    
    return result.ToString();
}

public void SetCgtDateTimeStampAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetCgtStamp(extracted);
    }
    offset += 13;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetCgtPartly(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetCgtStamp()
{
    return _CgtStamp;
}

// Standard Setter
public void SetCgtStamp(string value)
{
    _CgtStamp = value;
}

// Get<>AsString()
public string GetCgtStampAsString()
{
    return _CgtStamp.PadRight(13);
}

// Set<>AsString()
public void SetCgtStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtStamp = value;
}

// Standard Getter
public string GetCgtPartly()
{
    return _CgtPartly;
}

// Standard Setter
public void SetCgtPartly(string value)
{
    _CgtPartly = value;
}

// Get<>AsString()
public string GetCgtPartlyAsString()
{
    return _CgtPartly.PadRight(1);
}

// Set<>AsString()
public void SetCgtPartlyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtPartly = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetCgtParentKey(string value)
{
    _CgtParentKey.SetCgtParentKeyAsString(value);
}
// Nested Class: CgtParentKey
public class CgtParentKey
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtParentCalSedol, is_external=, is_static_class=False, static_prefix=
    private CgtParentKey.CgtParentCalSedol _CgtParentCalSedol = new CgtParentKey.CgtParentCalSedol();
    
    
    
    
public CgtParentKey() {}

public CgtParentKey(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _CgtParentCalSedol.SetCgtParentCalSedolAsString(data.Substring(offset, CgtParentCalSedol.GetSize()));
    offset += 11;
    
}

// Serialization methods
public string GetCgtParentKeyAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtParentCalSedol.GetCgtParentCalSedolAsString());
    
    return result.ToString();
}

public void SetCgtParentKeyAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 11 <= data.Length)
    {
        _CgtParentCalSedol.SetCgtParentCalSedolAsString(data.Substring(offset, 11));
    }
    else
    {
        _CgtParentCalSedol.SetCgtParentCalSedolAsString(data.Substring(offset));
    }
    offset += 11;
}

// Getter and Setter methods

// Standard Getter
public CgtParentCalSedol GetCgtParentCalSedol()
{
    return _CgtParentCalSedol;
}

// Standard Setter
public void SetCgtParentCalSedol(CgtParentCalSedol value)
{
    _CgtParentCalSedol = value;
}

// Get<>AsString()
public string GetCgtParentCalSedolAsString()
{
    return _CgtParentCalSedol != null ? _CgtParentCalSedol.GetCgtParentCalSedolAsString() : "";
}

// Set<>AsString()
public void SetCgtParentCalSedolAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtParentCalSedol == null)
    {
        _CgtParentCalSedol = new CgtParentCalSedol();
    }
    _CgtParentCalSedol.SetCgtParentCalSedolAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: CgtParentCalSedol
public class CgtParentCalSedol
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtParentCal, is_external=, is_static_class=False, static_prefix=
    private string _CgtParentCal ="";
    
    
    
    
    // [DEBUG] Field: CgtParentSedol, is_external=, is_static_class=False, static_prefix=
    private string _CgtParentSedol ="";
    
    
    
    
public CgtParentCalSedol() {}

public CgtParentCalSedol(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtParentCal(data.Substring(offset, 4).Trim());
    offset += 4;
    SetCgtParentSedol(data.Substring(offset, 7).Trim());
    offset += 7;
    
}

// Serialization methods
public string GetCgtParentCalSedolAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtParentCal.PadRight(4));
    result.Append(_CgtParentSedol.PadRight(7));
    
    return result.ToString();
}

public void SetCgtParentCalSedolAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetCgtParentCal(extracted);
    }
    offset += 4;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetCgtParentSedol(extracted);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetCgtParentCal()
{
    return _CgtParentCal;
}

// Standard Setter
public void SetCgtParentCal(string value)
{
    _CgtParentCal = value;
}

// Get<>AsString()
public string GetCgtParentCalAsString()
{
    return _CgtParentCal.PadRight(4);
}

// Set<>AsString()
public void SetCgtParentCalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtParentCal = value;
}

// Standard Getter
public string GetCgtParentSedol()
{
    return _CgtParentSedol;
}

// Standard Setter
public void SetCgtParentSedol(string value)
{
    _CgtParentSedol = value;
}

// Get<>AsString()
public string GetCgtParentSedolAsString()
{
    return _CgtParentSedol.PadRight(7);
}

// Set<>AsString()
public void SetCgtParentSedolAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtParentSedol = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetFiller174(string value)
{
    _Filler174.SetFiller174AsString(value);
}
// Nested Class: Filler174
public class Filler174
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtCalcFlag, is_external=, is_static_class=False, static_prefix=
    private string _CgtCalcFlag ="";
    
    
    
    
    // [DEBUG] Field: Filler175, is_external=, is_static_class=False, static_prefix=
    private string _Filler175 ="";
    
    
    
    
public Filler174() {}

public Filler174(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtCalcFlag(data.Substring(offset, 4).Trim());
    offset += 4;
    SetFiller175(data.Substring(offset, 7).Trim());
    offset += 7;
    
}

// Serialization methods
public string GetFiller174AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtCalcFlag.PadRight(4));
    result.Append(_Filler175.PadRight(7));
    
    return result.ToString();
}

public void SetFiller174AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetCgtCalcFlag(extracted);
    }
    offset += 4;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetFiller175(extracted);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetCgtCalcFlag()
{
    return _CgtCalcFlag;
}

// Standard Setter
public void SetCgtCalcFlag(string value)
{
    _CgtCalcFlag = value;
}

// Get<>AsString()
public string GetCgtCalcFlagAsString()
{
    return _CgtCalcFlag.PadRight(4);
}

// Set<>AsString()
public void SetCgtCalcFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtCalcFlag = value;
}

// Standard Getter
public string GetFiller175()
{
    return _Filler175;
}

// Standard Setter
public void SetFiller175(string value)
{
    _Filler175 = value;
}

// Get<>AsString()
public string GetFiller175AsString()
{
    return _Filler175.PadRight(7);
}

// Set<>AsString()
public void SetFiller175AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler175 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetCgtPreviousKey(string value)
{
    _CgtPreviousKey.SetCgtPreviousKeyAsString(value);
}
// Nested Class: CgtPreviousKey
public class CgtPreviousKey
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtPreviousCalSedol, is_external=, is_static_class=False, static_prefix=
    private CgtPreviousKey.CgtPreviousCalSedol _CgtPreviousCalSedol = new CgtPreviousKey.CgtPreviousCalSedol();
    
    
    
    
public CgtPreviousKey() {}

public CgtPreviousKey(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _CgtPreviousCalSedol.SetCgtPreviousCalSedolAsString(data.Substring(offset, CgtPreviousCalSedol.GetSize()));
    offset += 11;
    
}

// Serialization methods
public string GetCgtPreviousKeyAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtPreviousCalSedol.GetCgtPreviousCalSedolAsString());
    
    return result.ToString();
}

public void SetCgtPreviousKeyAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 11 <= data.Length)
    {
        _CgtPreviousCalSedol.SetCgtPreviousCalSedolAsString(data.Substring(offset, 11));
    }
    else
    {
        _CgtPreviousCalSedol.SetCgtPreviousCalSedolAsString(data.Substring(offset));
    }
    offset += 11;
}

// Getter and Setter methods

// Standard Getter
public CgtPreviousCalSedol GetCgtPreviousCalSedol()
{
    return _CgtPreviousCalSedol;
}

// Standard Setter
public void SetCgtPreviousCalSedol(CgtPreviousCalSedol value)
{
    _CgtPreviousCalSedol = value;
}

// Get<>AsString()
public string GetCgtPreviousCalSedolAsString()
{
    return _CgtPreviousCalSedol != null ? _CgtPreviousCalSedol.GetCgtPreviousCalSedolAsString() : "";
}

// Set<>AsString()
public void SetCgtPreviousCalSedolAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtPreviousCalSedol == null)
    {
        _CgtPreviousCalSedol = new CgtPreviousCalSedol();
    }
    _CgtPreviousCalSedol.SetCgtPreviousCalSedolAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: CgtPreviousCalSedol
public class CgtPreviousCalSedol
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtPreviousCal, is_external=, is_static_class=False, static_prefix=
    private string _CgtPreviousCal ="";
    
    
    
    
    // [DEBUG] Field: CgtPreviousSedol, is_external=, is_static_class=False, static_prefix=
    private string _CgtPreviousSedol ="";
    
    
    
    
public CgtPreviousCalSedol() {}

public CgtPreviousCalSedol(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtPreviousCal(data.Substring(offset, 4).Trim());
    offset += 4;
    SetCgtPreviousSedol(data.Substring(offset, 7).Trim());
    offset += 7;
    
}

// Serialization methods
public string GetCgtPreviousCalSedolAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtPreviousCal.PadRight(4));
    result.Append(_CgtPreviousSedol.PadRight(7));
    
    return result.ToString();
}

public void SetCgtPreviousCalSedolAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetCgtPreviousCal(extracted);
    }
    offset += 4;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetCgtPreviousSedol(extracted);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetCgtPreviousCal()
{
    return _CgtPreviousCal;
}

// Standard Setter
public void SetCgtPreviousCal(string value)
{
    _CgtPreviousCal = value;
}

// Get<>AsString()
public string GetCgtPreviousCalAsString()
{
    return _CgtPreviousCal.PadRight(4);
}

// Set<>AsString()
public void SetCgtPreviousCalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtPreviousCal = value;
}

// Standard Getter
public string GetCgtPreviousSedol()
{
    return _CgtPreviousSedol;
}

// Standard Setter
public void SetCgtPreviousSedol(string value)
{
    _CgtPreviousSedol = value;
}

// Get<>AsString()
public string GetCgtPreviousSedolAsString()
{
    return _CgtPreviousSedol.PadRight(7);
}

// Set<>AsString()
public void SetCgtPreviousSedolAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtPreviousSedol = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetCgtBargainKey(string value)
{
    _CgtBargainKey.SetCgtBargainKeyAsString(value);
}
// Nested Class: CgtBargainKey
public class CgtBargainKey
{
    private static int _size = 10;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtBargainPrefix, is_external=, is_static_class=False, static_prefix=
    private string _CgtBargainPrefix ="";
    
    
    
    
    // [DEBUG] Field: Filler176, is_external=, is_static_class=False, static_prefix=
    private string _Filler176 ="";
    
    
    
    
public CgtBargainKey() {}

public CgtBargainKey(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtBargainPrefix(data.Substring(offset, 7).Trim());
    offset += 7;
    SetFiller176(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetCgtBargainKeyAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtBargainPrefix.PadRight(7));
    result.Append(_Filler176.PadRight(3));
    
    return result.ToString();
}

public void SetCgtBargainKeyAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetCgtBargainPrefix(extracted);
    }
    offset += 7;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller176(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetCgtBargainPrefix()
{
    return _CgtBargainPrefix;
}

// Standard Setter
public void SetCgtBargainPrefix(string value)
{
    _CgtBargainPrefix = value;
}

// Get<>AsString()
public string GetCgtBargainPrefixAsString()
{
    return _CgtBargainPrefix.PadRight(7);
}

// Set<>AsString()
public void SetCgtBargainPrefixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtBargainPrefix = value;
}

// Standard Getter
public string GetFiller176()
{
    return _Filler176;
}

// Standard Setter
public void SetFiller176(string value)
{
    _Filler176 = value;
}

// Get<>AsString()
public string GetFiller176AsString()
{
    return _Filler176.PadRight(3);
}

// Set<>AsString()
public void SetFiller176AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler176 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}
