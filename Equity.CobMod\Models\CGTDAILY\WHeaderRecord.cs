using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdailyDTO
{// DTO class representing WHeaderRecord Data Structure

public class WHeaderRecord
{
    private static int _size = 58;
    // [DEBUG] Class: WHeaderRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler13, is_external=, is_static_class=False, static_prefix=
    private string _Filler13 ="";
    
    
    
    
    // [DEBUG] Field: Filler14, is_external=, is_static_class=False, static_prefix=
    private string _Filler14 ="Fund";
    
    
    
    
    // [DEBUG] Field: Filler15, is_external=, is_static_class=False, static_prefix=
    private string _Filler15 =",";
    
    
    
    
    // [DEBUG] Field: Filler16, is_external=, is_static_class=False, static_prefix=
    private string _Filler16 ="Sedol";
    
    
    
    
    // [DEBUG] Field: Filler17, is_external=, is_static_class=False, static_prefix=
    private string _Filler17 ="\",";
    
    
    
    
    // [DEBUG] Field: Filler18, is_external=, is_static_class=False, static_prefix=
    private string _Filler18 ="Quantity";
    
    
    
    
    // [DEBUG] Field: Filler19, is_external=, is_static_class=False, static_prefix=
    private string _Filler19 =",\"";
    
    
    
    
    // [DEBUG] Field: Filler20, is_external=, is_static_class=False, static_prefix=
    private string _Filler20 ="BargainDate";
    
    
    
    
    // [DEBUG] Field: Filler21, is_external=, is_static_class=False, static_prefix=
    private string _Filler21 =",";
    
    
    
    
    // [DEBUG] Field: Filler22, is_external=, is_static_class=False, static_prefix=
    private string _Filler22 ="SettlementDate";
    
    
    
    
    // [DEBUG] Field: Filler23, is_external=, is_static_class=False, static_prefix=
    private string _Filler23 =",";
    
    
    
    
    // [DEBUG] Field: Filler24, is_external=, is_static_class=False, static_prefix=
    private string _Filler24 ="TransactionCategory";
    
    
    
    
    // [DEBUG] Field: Filler25, is_external=, is_static_class=False, static_prefix=
    private string _Filler25 ="\",";
    
    
    
    
    // [DEBUG] Field: Filler26, is_external=, is_static_class=False, static_prefix=
    private string _Filler26 ="Value";
    
    
    
    
    // [DEBUG] Field: Filler27, is_external=, is_static_class=False, static_prefix=
    private string _Filler27 =",\"";
    
    
    
    
    // [DEBUG] Field: Filler28, is_external=, is_static_class=False, static_prefix=
    private string _Filler28 ="ContractNumber";
    
    
    
    
    // [DEBUG] Field: Filler29, is_external=, is_static_class=False, static_prefix=
    private string _Filler29 ="";
    
    
    
    
    
    // Serialization methods
    public string GetWHeaderRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler13.PadRight(0));
        result.Append(_Filler14.PadRight(0));
        result.Append(_Filler15.PadRight(0));
        result.Append(_Filler16.PadRight(0));
        result.Append(_Filler17.PadRight(0));
        result.Append(_Filler18.PadRight(0));
        result.Append(_Filler19.PadRight(0));
        result.Append(_Filler20.PadRight(11));
        result.Append(_Filler21.PadRight(0));
        result.Append(_Filler22.PadRight(14));
        result.Append(_Filler23.PadRight(0));
        result.Append(_Filler24.PadRight(19));
        result.Append(_Filler25.PadRight(0));
        result.Append(_Filler26.PadRight(0));
        result.Append(_Filler27.PadRight(0));
        result.Append(_Filler28.PadRight(14));
        result.Append(_Filler29.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetWHeaderRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller13(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller14(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller15(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller16(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller17(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller18(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller19(extracted);
        }
        offset += 0;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetFiller20(extracted);
        }
        offset += 11;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller21(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            SetFiller22(extracted);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller23(extracted);
        }
        offset += 0;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller24(extracted);
        }
        offset += 19;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller25(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller26(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller27(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            SetFiller28(extracted);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller29(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWHeaderRecordAsString();
    }
    // Set<>String Override function
    public void SetWHeaderRecord(string value)
    {
        SetWHeaderRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller13()
    {
        return _Filler13;
    }
    
    // Standard Setter
    public void SetFiller13(string value)
    {
        _Filler13 = value;
    }
    
    // Get<>AsString()
    public string GetFiller13AsString()
    {
        return _Filler13.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller13AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler13 = value;
    }
    
    // Standard Getter
    public string GetFiller14()
    {
        return _Filler14;
    }
    
    // Standard Setter
    public void SetFiller14(string value)
    {
        _Filler14 = value;
    }
    
    // Get<>AsString()
    public string GetFiller14AsString()
    {
        return _Filler14.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller14AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler14 = value;
    }
    
    // Standard Getter
    public string GetFiller15()
    {
        return _Filler15;
    }
    
    // Standard Setter
    public void SetFiller15(string value)
    {
        _Filler15 = value;
    }
    
    // Get<>AsString()
    public string GetFiller15AsString()
    {
        return _Filler15.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller15AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler15 = value;
    }
    
    // Standard Getter
    public string GetFiller16()
    {
        return _Filler16;
    }
    
    // Standard Setter
    public void SetFiller16(string value)
    {
        _Filler16 = value;
    }
    
    // Get<>AsString()
    public string GetFiller16AsString()
    {
        return _Filler16.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller16AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler16 = value;
    }
    
    // Standard Getter
    public string GetFiller17()
    {
        return _Filler17;
    }
    
    // Standard Setter
    public void SetFiller17(string value)
    {
        _Filler17 = value;
    }
    
    // Get<>AsString()
    public string GetFiller17AsString()
    {
        return _Filler17.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller17AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler17 = value;
    }
    
    // Standard Getter
    public string GetFiller18()
    {
        return _Filler18;
    }
    
    // Standard Setter
    public void SetFiller18(string value)
    {
        _Filler18 = value;
    }
    
    // Get<>AsString()
    public string GetFiller18AsString()
    {
        return _Filler18.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller18AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler18 = value;
    }
    
    // Standard Getter
    public string GetFiller19()
    {
        return _Filler19;
    }
    
    // Standard Setter
    public void SetFiller19(string value)
    {
        _Filler19 = value;
    }
    
    // Get<>AsString()
    public string GetFiller19AsString()
    {
        return _Filler19.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller19AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler19 = value;
    }
    
    // Standard Getter
    public string GetFiller20()
    {
        return _Filler20;
    }
    
    // Standard Setter
    public void SetFiller20(string value)
    {
        _Filler20 = value;
    }
    
    // Get<>AsString()
    public string GetFiller20AsString()
    {
        return _Filler20.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetFiller20AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler20 = value;
    }
    
    // Standard Getter
    public string GetFiller21()
    {
        return _Filler21;
    }
    
    // Standard Setter
    public void SetFiller21(string value)
    {
        _Filler21 = value;
    }
    
    // Get<>AsString()
    public string GetFiller21AsString()
    {
        return _Filler21.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller21AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler21 = value;
    }
    
    // Standard Getter
    public string GetFiller22()
    {
        return _Filler22;
    }
    
    // Standard Setter
    public void SetFiller22(string value)
    {
        _Filler22 = value;
    }
    
    // Get<>AsString()
    public string GetFiller22AsString()
    {
        return _Filler22.PadRight(14);
    }
    
    // Set<>AsString()
    public void SetFiller22AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler22 = value;
    }
    
    // Standard Getter
    public string GetFiller23()
    {
        return _Filler23;
    }
    
    // Standard Setter
    public void SetFiller23(string value)
    {
        _Filler23 = value;
    }
    
    // Get<>AsString()
    public string GetFiller23AsString()
    {
        return _Filler23.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller23AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler23 = value;
    }
    
    // Standard Getter
    public string GetFiller24()
    {
        return _Filler24;
    }
    
    // Standard Setter
    public void SetFiller24(string value)
    {
        _Filler24 = value;
    }
    
    // Get<>AsString()
    public string GetFiller24AsString()
    {
        return _Filler24.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller24AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler24 = value;
    }
    
    // Standard Getter
    public string GetFiller25()
    {
        return _Filler25;
    }
    
    // Standard Setter
    public void SetFiller25(string value)
    {
        _Filler25 = value;
    }
    
    // Get<>AsString()
    public string GetFiller25AsString()
    {
        return _Filler25.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller25AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler25 = value;
    }
    
    // Standard Getter
    public string GetFiller26()
    {
        return _Filler26;
    }
    
    // Standard Setter
    public void SetFiller26(string value)
    {
        _Filler26 = value;
    }
    
    // Get<>AsString()
    public string GetFiller26AsString()
    {
        return _Filler26.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller26AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler26 = value;
    }
    
    // Standard Getter
    public string GetFiller27()
    {
        return _Filler27;
    }
    
    // Standard Setter
    public void SetFiller27(string value)
    {
        _Filler27 = value;
    }
    
    // Get<>AsString()
    public string GetFiller27AsString()
    {
        return _Filler27.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller27AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler27 = value;
    }
    
    // Standard Getter
    public string GetFiller28()
    {
        return _Filler28;
    }
    
    // Standard Setter
    public void SetFiller28(string value)
    {
        _Filler28 = value;
    }
    
    // Get<>AsString()
    public string GetFiller28AsString()
    {
        return _Filler28.PadRight(14);
    }
    
    // Set<>AsString()
    public void SetFiller28AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler28 = value;
    }
    
    // Standard Getter
    public string GetFiller29()
    {
        return _Filler29;
    }
    
    // Standard Setter
    public void SetFiller29(string value)
    {
        _Filler29 = value;
    }
    
    // Get<>AsString()
    public string GetFiller29AsString()
    {
        return _Filler29.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller29AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler29 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}