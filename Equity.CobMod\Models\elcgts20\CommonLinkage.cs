using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing CommonLinkage Data Structure

public class CommonLinkage
{
    private static int _size = 110;
    // [DEBUG] Class: CommonLinkage, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: LUserid, is_external=, is_static_class=False, static_prefix=
    private string _LUserid ="";
    
    
    
    
    // [DEBUG] Field: LUserName, is_external=, is_static_class=False, static_prefix=
    private string _LUserName ="";
    
    
    
    
    // [DEBUG] Field: LUserNo, is_external=, is_static_class=False, static_prefix=
    private int _LUserNo =0;
    
    
    
    
    // [DEBUG] Field: LSignonDateTime, is_external=, is_static_class=False, static_prefix=
    private int _LSignonDateTime =0;
    
    
    
    
    // [DEBUG] Field: LMasterFileYear, is_external=, is_static_class=False, static_prefix=
    private int _LMasterFileYear =0;
    
    
    
    
    // [DEBUG] Field: LDefaultFund, is_external=, is_static_class=False, static_prefix=
    private string _LDefaultFund ="";
    
    
    
    
    // [DEBUG] Field: LDefaultFundName, is_external=, is_static_class=False, static_prefix=
    private string _LDefaultFundName ="";
    
    
    
    
    // [DEBUG] Field: LDefaultPrinter, is_external=, is_static_class=False, static_prefix=
    private string _LDefaultPrinter ="";
    
    
    
    
    // [DEBUG] Field: LSegregate, is_external=, is_static_class=False, static_prefix=
    private string _LSegregate ="";
    
    
    
    
    
    // Serialization methods
    public string GetCommonLinkageAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LUserid.PadRight(8));
        result.Append(_LUserName.PadRight(40));
        result.Append(_LUserNo.ToString().PadLeft(4, '0'));
        result.Append(_LSignonDateTime.ToString().PadLeft(14, '0'));
        result.Append(_LMasterFileYear.ToString().PadLeft(2, '0'));
        result.Append(_LDefaultFund.PadRight(4));
        result.Append(_LDefaultFundName.PadRight(30));
        result.Append(_LDefaultPrinter.PadRight(7));
        result.Append(_LSegregate.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetCommonLinkageAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetLUserid(extracted);
        }
        offset += 8;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetLUserName(extracted);
        }
        offset += 40;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetLUserNo(parsedInt);
        }
        offset += 4;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetLSignonDateTime(parsedInt);
        }
        offset += 14;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetLMasterFileYear(parsedInt);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetLDefaultFund(extracted);
        }
        offset += 4;
        if (offset + 30 <= data.Length)
        {
            string extracted = data.Substring(offset, 30).Trim();
            SetLDefaultFundName(extracted);
        }
        offset += 30;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetLDefaultPrinter(extracted);
        }
        offset += 7;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetLSegregate(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCommonLinkageAsString();
    }
    // Set<>String Override function
    public void SetCommonLinkage(string value)
    {
        SetCommonLinkageAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetLUserid()
    {
        return _LUserid;
    }
    
    // Standard Setter
    public void SetLUserid(string value)
    {
        _LUserid = value;
    }
    
    // Get<>AsString()
    public string GetLUseridAsString()
    {
        return _LUserid.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetLUseridAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LUserid = value;
    }
    
    // Standard Getter
    public string GetLUserName()
    {
        return _LUserName;
    }
    
    // Standard Setter
    public void SetLUserName(string value)
    {
        _LUserName = value;
    }
    
    // Get<>AsString()
    public string GetLUserNameAsString()
    {
        return _LUserName.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetLUserNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LUserName = value;
    }
    
    // Standard Getter
    public int GetLUserNo()
    {
        return _LUserNo;
    }
    
    // Standard Setter
    public void SetLUserNo(int value)
    {
        _LUserNo = value;
    }
    
    // Get<>AsString()
    public string GetLUserNoAsString()
    {
        return _LUserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetLUserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _LUserNo = parsed;
    }
    
    // Standard Getter
    public int GetLSignonDateTime()
    {
        return _LSignonDateTime;
    }
    
    // Standard Setter
    public void SetLSignonDateTime(int value)
    {
        _LSignonDateTime = value;
    }
    
    // Get<>AsString()
    public string GetLSignonDateTimeAsString()
    {
        return _LSignonDateTime.ToString().PadLeft(14, '0');
    }
    
    // Set<>AsString()
    public void SetLSignonDateTimeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _LSignonDateTime = parsed;
    }
    
    // Standard Getter
    public int GetLMasterFileYear()
    {
        return _LMasterFileYear;
    }
    
    // Standard Setter
    public void SetLMasterFileYear(int value)
    {
        _LMasterFileYear = value;
    }
    
    // Get<>AsString()
    public string GetLMasterFileYearAsString()
    {
        return _LMasterFileYear.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetLMasterFileYearAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _LMasterFileYear = parsed;
    }
    
    // Standard Getter
    public string GetLDefaultFund()
    {
        return _LDefaultFund;
    }
    
    // Standard Setter
    public void SetLDefaultFund(string value)
    {
        _LDefaultFund = value;
    }
    
    // Get<>AsString()
    public string GetLDefaultFundAsString()
    {
        return _LDefaultFund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetLDefaultFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LDefaultFund = value;
    }
    
    // Standard Getter
    public string GetLDefaultFundName()
    {
        return _LDefaultFundName;
    }
    
    // Standard Setter
    public void SetLDefaultFundName(string value)
    {
        _LDefaultFundName = value;
    }
    
    // Get<>AsString()
    public string GetLDefaultFundNameAsString()
    {
        return _LDefaultFundName.PadRight(30);
    }
    
    // Set<>AsString()
    public void SetLDefaultFundNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LDefaultFundName = value;
    }
    
    // Standard Getter
    public string GetLDefaultPrinter()
    {
        return _LDefaultPrinter;
    }
    
    // Standard Setter
    public void SetLDefaultPrinter(string value)
    {
        _LDefaultPrinter = value;
    }
    
    // Get<>AsString()
    public string GetLDefaultPrinterAsString()
    {
        return _LDefaultPrinter.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetLDefaultPrinterAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LDefaultPrinter = value;
    }
    
    // Standard Getter
    public string GetLSegregate()
    {
        return _LSegregate;
    }
    
    // Standard Setter
    public void SetLSegregate(string value)
    {
        _LSegregate = value;
    }
    
    // Get<>AsString()
    public string GetLSegregateAsString()
    {
        return _LSegregate.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetLSegregateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LSegregate = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
