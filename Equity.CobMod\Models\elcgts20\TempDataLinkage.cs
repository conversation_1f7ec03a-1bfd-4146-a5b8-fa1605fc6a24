using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing TempDataLinkage Data Structure

public class TempDataLinkage
{
    private static int _size = 28;
    // [DEBUG] Class: TempDataLinkage, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: TempDataAction, is_external=, is_static_class=False, static_prefix=
    private int _TempDataAction =0;
    
    
    // 88-level condition checks for TempDataAction
    public bool IsWriteTempData()
    {
        if (this._TempDataAction == 1) return true;
        return false;
    }
    public bool IsReadTempData()
    {
        if (this._TempDataAction == 2) return true;
        return false;
    }
    public bool IsConstructor()
    {
        if (this._TempDataAction == 3) return true;
        return false;
    }
    
    
    // [DEBUG] Field: TempDataWtsSize, is_external=, is_static_class=False, static_prefix=
    private int _TempDataWtsSize =0;
    
    
    
    
    // [DEBUG] Field: TempDataWttSize, is_external=, is_static_class=False, static_prefix=
    private int _TempDataWttSize =0;
    
    
    
    
    // [DEBUG] Field: TempDataWtcSize, is_external=, is_static_class=False, static_prefix=
    private int _TempDataWtcSize =0;
    
    
    
    
    
    // Serialization methods
    public string GetTempDataLinkageAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_TempDataAction.ToString().PadLeft(1, '0'));
        result.Append(_TempDataWtsSize.ToString().PadLeft(9, '0'));
        result.Append(_TempDataWttSize.ToString().PadLeft(9, '0'));
        result.Append(_TempDataWtcSize.ToString().PadLeft(9, '0'));
        
        return result.ToString();
    }
    
    public void SetTempDataLinkageAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetTempDataAction(parsedInt);
        }
        offset += 1;
        if (offset + 9 <= data.Length)
        {
            string extracted = data.Substring(offset, 9).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetTempDataWtsSize(parsedInt);
        }
        offset += 9;
        if (offset + 9 <= data.Length)
        {
            string extracted = data.Substring(offset, 9).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetTempDataWttSize(parsedInt);
        }
        offset += 9;
        if (offset + 9 <= data.Length)
        {
            string extracted = data.Substring(offset, 9).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetTempDataWtcSize(parsedInt);
        }
        offset += 9;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetTempDataLinkageAsString();
    }
    // Set<>String Override function
    public void SetTempDataLinkage(string value)
    {
        SetTempDataLinkageAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetTempDataAction()
    {
        return _TempDataAction;
    }
    
    // Standard Setter
    public void SetTempDataAction(int value)
    {
        _TempDataAction = value;
    }
    
    // Get<>AsString()
    public string GetTempDataActionAsString()
    {
        return _TempDataAction.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetTempDataActionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _TempDataAction = parsed;
    }
    
    // Standard Getter
    public int GetTempDataWtsSize()
    {
        return _TempDataWtsSize;
    }
    
    // Standard Setter
    public void SetTempDataWtsSize(int value)
    {
        _TempDataWtsSize = value;
    }
    
    // Get<>AsString()
    public string GetTempDataWtsSizeAsString()
    {
        return _TempDataWtsSize.ToString().PadLeft(9, '0');
    }
    
    // Set<>AsString()
    public void SetTempDataWtsSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _TempDataWtsSize = parsed;
    }
    
    // Standard Getter
    public int GetTempDataWttSize()
    {
        return _TempDataWttSize;
    }
    
    // Standard Setter
    public void SetTempDataWttSize(int value)
    {
        _TempDataWttSize = value;
    }
    
    // Get<>AsString()
    public string GetTempDataWttSizeAsString()
    {
        return _TempDataWttSize.ToString().PadLeft(9, '0');
    }
    
    // Set<>AsString()
    public void SetTempDataWttSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _TempDataWttSize = parsed;
    }
    
    // Standard Getter
    public int GetTempDataWtcSize()
    {
        return _TempDataWtcSize;
    }
    
    // Standard Setter
    public void SetTempDataWtcSize(int value)
    {
        _TempDataWtcSize = value;
    }
    
    // Get<>AsString()
    public string GetTempDataWtcSizeAsString()
    {
        return _TempDataWtcSize.ToString().PadLeft(9, '0');
    }
    
    // Set<>AsString()
    public void SetTempDataWtcSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _TempDataWtcSize = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
