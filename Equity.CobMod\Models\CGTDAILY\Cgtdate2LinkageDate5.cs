using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdailyDTO
{// DTO class representing Cgtdate2LinkageDate5 Data Structure

public class Cgtdate2LinkageDate5
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate5, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd5, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd5 =0;
    
    
    
    
    // [DEBUG] Field: Filler70, is_external=, is_static_class=False, static_prefix=
    private Filler70 _Filler70 = new Filler70();
    
    
    
    
    // [DEBUG] Field: Filler71, is_external=, is_static_class=False, static_prefix=
    private Filler71 _Filler71 = new Filler71();
    
    
    
    
    // [DEBUG] Field: Filler72, is_external=, is_static_class=False, static_prefix=
    private Filler72 _Filler72 = new Filler72();
    
    
    
    
    // [DEBUG] Field: Filler73, is_external=, is_static_class=False, static_prefix=
    private Filler73 _Filler73 = new Filler73();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate5AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd5.ToString().PadLeft(8, '0'));
        result.Append(_Filler70.GetFiller70AsString());
        result.Append(_Filler71.GetFiller71AsString());
        result.Append(_Filler72.GetFiller72AsString());
        result.Append(_Filler73.GetFiller73AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate5AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd5(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler70.SetFiller70AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler70.SetFiller70AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler71.SetFiller71AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler71.SetFiller71AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler72.SetFiller72AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler72.SetFiller72AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler73.SetFiller73AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler73.SetFiller73AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate5AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate5(string value)
    {
        SetCgtdate2LinkageDate5AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd5()
    {
        return _Cgtdate2Ccyymmdd5;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd5(int value)
    {
        _Cgtdate2Ccyymmdd5 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd5AsString()
    {
        return _Cgtdate2Ccyymmdd5.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd5 = parsed;
    }
    
    // Standard Getter
    public Filler70 GetFiller70()
    {
        return _Filler70;
    }
    
    // Standard Setter
    public void SetFiller70(Filler70 value)
    {
        _Filler70 = value;
    }
    
    // Get<>AsString()
    public string GetFiller70AsString()
    {
        return _Filler70 != null ? _Filler70.GetFiller70AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller70AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler70 == null)
        {
            _Filler70 = new Filler70();
        }
        _Filler70.SetFiller70AsString(value);
    }
    
    // Standard Getter
    public Filler71 GetFiller71()
    {
        return _Filler71;
    }
    
    // Standard Setter
    public void SetFiller71(Filler71 value)
    {
        _Filler71 = value;
    }
    
    // Get<>AsString()
    public string GetFiller71AsString()
    {
        return _Filler71 != null ? _Filler71.GetFiller71AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller71AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler71 == null)
        {
            _Filler71 = new Filler71();
        }
        _Filler71.SetFiller71AsString(value);
    }
    
    // Standard Getter
    public Filler72 GetFiller72()
    {
        return _Filler72;
    }
    
    // Standard Setter
    public void SetFiller72(Filler72 value)
    {
        _Filler72 = value;
    }
    
    // Get<>AsString()
    public string GetFiller72AsString()
    {
        return _Filler72 != null ? _Filler72.GetFiller72AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller72AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler72 == null)
        {
            _Filler72 = new Filler72();
        }
        _Filler72.SetFiller72AsString(value);
    }
    
    // Standard Getter
    public Filler73 GetFiller73()
    {
        return _Filler73;
    }
    
    // Standard Setter
    public void SetFiller73(Filler73 value)
    {
        _Filler73 = value;
    }
    
    // Get<>AsString()
    public string GetFiller73AsString()
    {
        return _Filler73 != null ? _Filler73.GetFiller73AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller73AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler73 == null)
        {
            _Filler73 = new Filler73();
        }
        _Filler73.SetFiller73AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller70(string value)
    {
        _Filler70.SetFiller70AsString(value);
    }
    // Nested Class: Filler70
    public class Filler70
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc5, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc5 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd5, is_external=, is_static_class=False, static_prefix=
        private Filler70.Cgtdate2Yymmdd5 _Cgtdate2Yymmdd5 = new Filler70.Cgtdate2Yymmdd5();
        
        
        
        
    public Filler70() {}
    
    public Filler70(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc5(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd5.SetCgtdate2Yymmdd5AsString(data.Substring(offset, Cgtdate2Yymmdd5.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller70AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc5.PadRight(2));
        result.Append(_Cgtdate2Yymmdd5.GetCgtdate2Yymmdd5AsString());
        
        return result.ToString();
    }
    
    public void SetFiller70AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc5(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd5.SetCgtdate2Yymmdd5AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd5.SetCgtdate2Yymmdd5AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc5()
    {
        return _Cgtdate2Cc5;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc5(string value)
    {
        _Cgtdate2Cc5 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc5AsString()
    {
        return _Cgtdate2Cc5.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc5 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd5 GetCgtdate2Yymmdd5()
    {
        return _Cgtdate2Yymmdd5;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd5(Cgtdate2Yymmdd5 value)
    {
        _Cgtdate2Yymmdd5 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd5AsString()
    {
        return _Cgtdate2Yymmdd5 != null ? _Cgtdate2Yymmdd5.GetCgtdate2Yymmdd5AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd5 == null)
        {
            _Cgtdate2Yymmdd5 = new Cgtdate2Yymmdd5();
        }
        _Cgtdate2Yymmdd5.SetCgtdate2Yymmdd5AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd5
    public class Cgtdate2Yymmdd5
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy5, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy5 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd5, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd5.Cgtdate2Mmdd5 _Cgtdate2Mmdd5 = new Cgtdate2Yymmdd5.Cgtdate2Mmdd5();
        
        
        
        
    public Cgtdate2Yymmdd5() {}
    
    public Cgtdate2Yymmdd5(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy5(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd5.SetCgtdate2Mmdd5AsString(data.Substring(offset, Cgtdate2Mmdd5.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd5AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy5.PadRight(2));
        result.Append(_Cgtdate2Mmdd5.GetCgtdate2Mmdd5AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd5AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy5(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd5.SetCgtdate2Mmdd5AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd5.SetCgtdate2Mmdd5AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy5()
    {
        return _Cgtdate2Yy5;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy5(string value)
    {
        _Cgtdate2Yy5 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy5AsString()
    {
        return _Cgtdate2Yy5.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy5 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd5 GetCgtdate2Mmdd5()
    {
        return _Cgtdate2Mmdd5;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd5(Cgtdate2Mmdd5 value)
    {
        _Cgtdate2Mmdd5 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd5AsString()
    {
        return _Cgtdate2Mmdd5 != null ? _Cgtdate2Mmdd5.GetCgtdate2Mmdd5AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd5 == null)
        {
            _Cgtdate2Mmdd5 = new Cgtdate2Mmdd5();
        }
        _Cgtdate2Mmdd5.SetCgtdate2Mmdd5AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd5
    public class Cgtdate2Mmdd5
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm5, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm5 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd5, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd5 ="";
        
        
        
        
    public Cgtdate2Mmdd5() {}
    
    public Cgtdate2Mmdd5(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm5(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd5(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd5AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm5.PadRight(2));
        result.Append(_Cgtdate2Dd5.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd5AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm5(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd5(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm5()
    {
        return _Cgtdate2Mm5;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm5(string value)
    {
        _Cgtdate2Mm5 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm5AsString()
    {
        return _Cgtdate2Mm5.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm5 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd5()
    {
        return _Cgtdate2Dd5;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd5(string value)
    {
        _Cgtdate2Dd5 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd5AsString()
    {
        return _Cgtdate2Dd5.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd5 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller71(string value)
{
    _Filler71.SetFiller71AsString(value);
}
// Nested Class: Filler71
public class Filler71
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy5, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy5 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd5, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd5 =0;
    
    
    
    
public Filler71() {}

public Filler71(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy5(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd5(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller71AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy5.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd5.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller71AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy5(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd5(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy5()
{
    return _Cgtdate2CCcyy5;
}

// Standard Setter
public void SetCgtdate2CCcyy5(int value)
{
    _Cgtdate2CCcyy5 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy5AsString()
{
    return _Cgtdate2CCcyy5.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy5AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy5 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd5()
{
    return _Cgtdate2CMmdd5;
}

// Standard Setter
public void SetCgtdate2CMmdd5(int value)
{
    _Cgtdate2CMmdd5 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd5AsString()
{
    return _Cgtdate2CMmdd5.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd5AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd5 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller72(string value)
{
    _Filler72.SetFiller72AsString(value);
}
// Nested Class: Filler72
public class Filler72
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc5, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc5 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy5, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy5 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm5, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm5 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd5, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd5 =0;
    
    
    
    
public Filler72() {}

public Filler72(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc5(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy5(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm5(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd5(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller72AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc5.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy5.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm5.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd5.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller72AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc5(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy5(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm5(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd5(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc5()
{
    return _Cgtdate2CCc5;
}

// Standard Setter
public void SetCgtdate2CCc5(int value)
{
    _Cgtdate2CCc5 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc5AsString()
{
    return _Cgtdate2CCc5.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc5AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc5 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy5()
{
    return _Cgtdate2CYy5;
}

// Standard Setter
public void SetCgtdate2CYy5(int value)
{
    _Cgtdate2CYy5 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy5AsString()
{
    return _Cgtdate2CYy5.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy5AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy5 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm5()
{
    return _Cgtdate2CMm5;
}

// Standard Setter
public void SetCgtdate2CMm5(int value)
{
    _Cgtdate2CMm5 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm5AsString()
{
    return _Cgtdate2CMm5.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm5AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm5 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd5()
{
    return _Cgtdate2CDd5;
}

// Standard Setter
public void SetCgtdate2CDd5(int value)
{
    _Cgtdate2CDd5 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd5AsString()
{
    return _Cgtdate2CDd5.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd5AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd5 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller73(string value)
{
    _Filler73.SetFiller73AsString(value);
}
// Nested Class: Filler73
public class Filler73
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm5, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm5 =0;
    
    
    
    
    // [DEBUG] Field: Filler74, is_external=, is_static_class=False, static_prefix=
    private string _Filler74 ="";
    
    
    
    
public Filler73() {}

public Filler73(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm5(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller74(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller73AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm5.ToString().PadLeft(6, '0'));
    result.Append(_Filler74.PadRight(2));
    
    return result.ToString();
}

public void SetFiller73AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm5(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller74(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm5()
{
    return _Cgtdate2CCcyymm5;
}

// Standard Setter
public void SetCgtdate2CCcyymm5(int value)
{
    _Cgtdate2CCcyymm5 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm5AsString()
{
    return _Cgtdate2CCcyymm5.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm5AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm5 = parsed;
}

// Standard Getter
public string GetFiller74()
{
    return _Filler74;
}

// Standard Setter
public void SetFiller74(string value)
{
    _Filler74 = value;
}

// Get<>AsString()
public string GetFiller74AsString()
{
    return _Filler74.PadRight(2);
}

// Set<>AsString()
public void SetFiller74AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler74 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}