using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WsFileFlags Data Structure

public class WsFileFlags
{
    private static int _size = 8;
    // [DEBUG] Class: WsFileFlags, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsDrOpen, is_external=, is_static_class=False, static_prefix=
    private string _WsDrOpen ="N";
    
    
    // 88-level condition checks for WsDrOpen
    public bool IsScheduleFileOpen()
    {
        if (this._WsDrOpen == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsDpOpen, is_external=, is_static_class=False, static_prefix=
    private string _WsDpOpen ="X";
    
    
    // 88-level condition checks for WsDpOpen
    public bool IsPoolRptFileOpen()
    {
        if (this._WsDpOpen == "'Y'") return true;
        return false;
    }
    public bool IsPoolRptNotNeeded()
    {
        if (this._WsDpOpen == "'X'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsDuOpen, is_external=, is_static_class=False, static_prefix=
    private string _WsDuOpen ="N";
    
    
    
    
    // [DEBUG] Field: WsMfOpen, is_external=, is_static_class=False, static_prefix=
    private string _WsMfOpen ="N";
    
    
    
    
    // [DEBUG] Field: WsMsgOpen, is_external=, is_static_class=False, static_prefix=
    private string _WsMsgOpen ="N";
    
    
    
    
    // [DEBUG] Field: WsFundOpen, is_external=, is_static_class=False, static_prefix=
    private string _WsFundOpen ="N";
    
    
    
    
    // [DEBUG] Field: WsErrOpen, is_external=, is_static_class=False, static_prefix=
    private string _WsErrOpen ="N";
    
    
    
    
    // [DEBUG] Field: WsRpiOpen, is_external=, is_static_class=False, static_prefix=
    private string _WsRpiOpen ="N";
    
    
    
    
    
    // Serialization methods
    public string GetWsFileFlagsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsDrOpen.PadRight(1));
        result.Append(_WsDpOpen.PadRight(1));
        result.Append(_WsDuOpen.PadRight(1));
        result.Append(_WsMfOpen.PadRight(1));
        result.Append(_WsMsgOpen.PadRight(1));
        result.Append(_WsFundOpen.PadRight(1));
        result.Append(_WsErrOpen.PadRight(1));
        result.Append(_WsRpiOpen.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWsFileFlagsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsDrOpen(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsDpOpen(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsDuOpen(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsMfOpen(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsMsgOpen(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsFundOpen(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsErrOpen(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsRpiOpen(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsFileFlagsAsString();
    }
    // Set<>String Override function
    public void SetWsFileFlags(string value)
    {
        SetWsFileFlagsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsDrOpen()
    {
        return _WsDrOpen;
    }
    
    // Standard Setter
    public void SetWsDrOpen(string value)
    {
        _WsDrOpen = value;
    }
    
    // Get<>AsString()
    public string GetWsDrOpenAsString()
    {
        return _WsDrOpen.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsDrOpenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsDrOpen = value;
    }
    
    // Standard Getter
    public string GetWsDpOpen()
    {
        return _WsDpOpen;
    }
    
    // Standard Setter
    public void SetWsDpOpen(string value)
    {
        _WsDpOpen = value;
    }
    
    // Get<>AsString()
    public string GetWsDpOpenAsString()
    {
        return _WsDpOpen.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsDpOpenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsDpOpen = value;
    }
    
    // Standard Getter
    public string GetWsDuOpen()
    {
        return _WsDuOpen;
    }
    
    // Standard Setter
    public void SetWsDuOpen(string value)
    {
        _WsDuOpen = value;
    }
    
    // Get<>AsString()
    public string GetWsDuOpenAsString()
    {
        return _WsDuOpen.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsDuOpenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsDuOpen = value;
    }
    
    // Standard Getter
    public string GetWsMfOpen()
    {
        return _WsMfOpen;
    }
    
    // Standard Setter
    public void SetWsMfOpen(string value)
    {
        _WsMfOpen = value;
    }
    
    // Get<>AsString()
    public string GetWsMfOpenAsString()
    {
        return _WsMfOpen.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsMfOpenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsMfOpen = value;
    }
    
    // Standard Getter
    public string GetWsMsgOpen()
    {
        return _WsMsgOpen;
    }
    
    // Standard Setter
    public void SetWsMsgOpen(string value)
    {
        _WsMsgOpen = value;
    }
    
    // Get<>AsString()
    public string GetWsMsgOpenAsString()
    {
        return _WsMsgOpen.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsMsgOpenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsMsgOpen = value;
    }
    
    // Standard Getter
    public string GetWsFundOpen()
    {
        return _WsFundOpen;
    }
    
    // Standard Setter
    public void SetWsFundOpen(string value)
    {
        _WsFundOpen = value;
    }
    
    // Get<>AsString()
    public string GetWsFundOpenAsString()
    {
        return _WsFundOpen.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsFundOpenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsFundOpen = value;
    }
    
    // Standard Getter
    public string GetWsErrOpen()
    {
        return _WsErrOpen;
    }
    
    // Standard Setter
    public void SetWsErrOpen(string value)
    {
        _WsErrOpen = value;
    }
    
    // Get<>AsString()
    public string GetWsErrOpenAsString()
    {
        return _WsErrOpen.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsErrOpenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsErrOpen = value;
    }
    
    // Standard Getter
    public string GetWsRpiOpen()
    {
        return _WsRpiOpen;
    }
    
    // Standard Setter
    public void SetWsRpiOpen(string value)
    {
        _WsRpiOpen = value;
    }
    
    // Get<>AsString()
    public string GetWsRpiOpenAsString()
    {
        return _WsRpiOpen.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsRpiOpenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsRpiOpen = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}