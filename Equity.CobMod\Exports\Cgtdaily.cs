using System;
using System.Text;
using EquityProject.CommonDTO;
using EquityProject.CgtdailyDTO;
using EquityProject.CgtabortPGM;
using EquityProject.EqtpathPGM;
using EquityProject.EquityGlobalParmsDTO;
namespace EquityProject.CgtdailyPGM
{
    // Cgtdaily Class Definition

    //Cgtdaily Class Constructor
    public class Cgtdaily
    {
        // Declare Cgtdaily Class private variables
        private Fvar _fvar = new Fvar();
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();
        private EquityGlobalParms equityGlobalParms = new EquityGlobalParms();
        // Declare Cgtdaily Class getters setters
        public Fvar GetFvar() { return _fvar; }
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }



        // Run() method
        public void Run(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Main entry point - calls AControl paragraph
            AControl(fvar, gvar, ivar);
        }

        // CallSub() method
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
        }

        /// <summary>
        /// Controls the program flow based on CGTSKAN-ACTION
        /// COBOL paragraph name: A-CONTROL
        /// </summary>
        public void AControl(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            string action = ivar.GetCgtskanLinkage().GetCgtskanAction();

            switch (action)
            {
                case "I":
                    IInitialise(fvar, gvar, ivar);
                    break;
                case "R":
                    RReportDtl(fvar, gvar, ivar);
                    break;
                case "S":
                    SSedolTotal(fvar, gvar, ivar);
                    break;
                case "F":
                    FFundTotal(fvar, gvar, ivar);
                    break;
                case "G":
                    GFinalTotal(fvar, gvar, ivar);
                    break;
                case "Q":
                    QQuitReport(fvar, gvar, ivar);
                    break;
            }
        }

        /// <summary>
        /// Implementation of the iInitialise paragraph
        /// </summary>
        public void IInitialise(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // COBOL: MOVE   L-USER-NO                      TO   REPORT-USER-NO
            gvar.GetReportFile().SetReportUserNo(ivar.GetCommonLinkage().GetLUserNoAsString());

            // COBOL: MOVE   CGTSKAN-REPORT-GENERATION-NO   TO   REPORT-GEN-NO
            gvar.GetReportFile().SetReportGenNo(ivar.GetCgtskanLinkage().GetCgtskanReportGenerationNo());

            // COBOL: MOVE   USER-DATA-PATH    TO   EQTPATH-PATH-ENV-VARIABLE
            gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);

            // COBOL: MOVE   REPORT-FILE       TO   EQTPATH-FILE-NAME
            gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetReportFileAsString());

            // COBOL: PERFORM   X-CALL-EQTPATH
            XCallEqtpath(fvar, gvar, ivar);

            // COBOL: MOVE   EQTPATH-PATH-FILE-NAME   TO   EXPORT-FILE-NAME
            gvar.SetExportFileName(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

            // COBOL: OPEN   OUTPUT   DAILY-TRANS-EXPORT
            // File opening is handled by the runtime system

            // COBOL: IF    W-FILE-RETURN-CODE   NOT   =   ZERO
            if (gvar.GetWFileReturnCode() != "00")
            {
                // COBOL: PERFORM   X2-CGTABORT
                X2Cgtabort(fvar, gvar, ivar);
            }

            // COBOL: MOVE   W-HEADER-RECORD   TO   D171-RECORD
            fvar.GetD171Record().SetD171RecordAsString(gvar.GetWHeaderRecord().GetWHeaderRecordAsString());

            // COBOL: PERFORM   X1-WRITE-EXPORT
            X1WriteExport(fvar, gvar, ivar);

            // COBOL: MOVE   'I'   TO   W-LAST-CALL
            gvar.SetWLastCall("I");
        }

        /// <summary>
        /// This paragraph moves data from the CGT SKAN master record to the banking acquisition and disposition record.
        /// COBOL paragraph 'R-REPORT-DTL'.
        /// </summary>
        public void RReportDtl(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            /// <Move CGTSKAN-MASTER-RECORD to D13-BAL-ACQ-DISP-RECORD>
            gvar.GetD13BalAcqDispRecord().SetD13BalAcqDispRecordAsString(ivar.GetCgtskanLinkage().GetCgtskanMasterRecordAsString());

            // IF D13-2-TRANSACTION-EXPORTED <> 'Y'
            if (gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().GetD132TransactionExported() != "Y")
            {
                /// <MOVE 'Y' TO D13-2-TRANSACTION-EXPORTED>
                gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().SetD132TransactionExported("Y");

                /// <CALL X3-EXPORT-AND-REWRITE>
                X3ExportAndRewrite(fvar, gvar, ivar);
            }

            // <ADD 1 TO W-EXPORT-COUNT>
            gvar.SetWExportCount(gvar.GetWExportCount() + 1);

            // <MOVE 'R' TO W-LAST-CALL>
            gvar.SetWLastCall("R");
        }

        /// <summary>
        /// This method sets up the system for processing Sedol totals.
        /// </summary>
        public void SSedolTotal(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // The original COBOL code had a MOVE operation which equates to a C# setter method call.
            gvar.SetWLastCall("S");
        }

        /// <summary>
        /// This method corresponds to the COBOL paragraph 'FFUNDTOTAL'.
        /// </summary>
        public void FFundTotal(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move 'F' to WLASTCALL
            // COBOL logic: MOVE   'F' TO W-LAST-CALL.
            gvar.SetWLastCall("F");
        }

        /// <summary>
        /// CLOSE DAILY-TRANS-EXPORT
        /// MOVE 'G' TO W-LAST-CALL.
        /// This method provides transitions for daily transaction maintenance
        /// </summary>
        public void GFinalTotal(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Perform the close operation of DAILY-TRANS-EXPORT
            // File closing is handled by the runtime system

            // MOVE 'G' TO W-LAST-CALL.
            gvar.SetWLastCall("G");
        }

        /// <summary>
        /// The QQuitReport method performs actions related to closing a file and handling
        /// file return codes, including calling another method if the file return code is not zero
        /// and then setting a specific flag to 'Q'.
        /// </summary>
        public void QQuitReport(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // CLOSE DAILY-TRANS-EXPORT
            // The C# equivalent would typically be handled by the file management library in use
            // For the purpose of this conversion, we will assume the file is closed properly
            // and no specific C# code is provided here for the close operation

            // Check if W-FILE-RETURN-CODE is NOT equal to ZERO
            if (gvar.GetWFileReturnCode() != "00")
            {
                // Execute the logic in X2Cgtabort()
                X2Cgtabort(fvar, gvar, ivar);
            }

            // Move 'Q' to W-LAST-CALL
            gvar.SetWLastCall("Q");
        }

        /// <summary>
        /// Write the D171-RECORD data to an output location.
        /// </summary>
        public void X1WriteExport(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Write the D171-RECORD
            // File write operations are handled by the runtime system
            // The D171-RECORD is written to the export file

            // Check if the file return code is not equal to zero
            if (gvar.GetWFileReturnCode() != "00")
            {
                // Perform the X2Cgtabort method if the file return code is not zero
                X2Cgtabort(fvar, gvar, ivar);
            }
        }

        /// <summary>
        /// This method is converted from the COBOL paragraph x2Cgtabort.
        /// </summary>
        public void X2Cgtabort(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move W-PROGRAM-NAME to L-ABORT-PROGRAM-NAME
            gvar.GetCgtabortLinkage().SetLAbortProgramName(gvar.GetWProgramName());

            // Move W-FILE-RETURN-CODE to L-ABORT-FILE-STATUS
            gvar.GetCgtabortLinkage().SetLAbortFileStatus(gvar.GetWFileReturnCode());

            // Move REPORT-FILE to L-ABORT-FILE-NAME
            gvar.GetCgtabortLinkage().SetLAbortFileName(gvar.GetReportFileAsString());

            // Create a new instance of the external program CGTABORT
            var cgtabort = new Cgtabort();
            // Call the Run method with the COMMON-LINKAGE and CGTABORT-LINKAGE parameters
            cgtabort.GetIvar().SetCgtabortLinkageAsString(gvar.GetCgtabortLinkageAsString());
            cgtabort.GetIvar().SetCommonLinkageAsString(ivar.GetCommonLinkageAsString());
            cgtabort.Run(cgtabort.GetGvar(),cgtabort.GetIvar());
        }

        /// <summary>
        /// Performs the logic specified in the original COBOL paragraph X3-EXPORT-AND-REWRITE.
        /// This method handles the processing and reformatting of data from the input record to the output record.
        /// </summary>
        public void X3ExportAndRewrite(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // MOVE W-INITIALISED-RECORD TO D171-RECORD
            fvar.GetD171Record().SetD171RecordAsString(gvar.GetWInitialisedRecord().GetWInitialisedRecordAsString());

            // Move values from input record to output record
            var d13BalAcqDispRecord = gvar.GetD13BalAcqDispRecord();

            // MOVE D13-2-CO-AC-LK TO D171-FUND-CODE
            fvar.GetD171Record().SetD171FundCode(d13BalAcqDispRecord.GetD13BalAcqDispCommon().GetD132Key().GetD132CalSedol().GetD132CoAcLk());

            // MOVE D13-2-SEDOL TO D171-SEDOL-CODE
            fvar.GetD171Record().SetD171SedolCode(d13BalAcqDispRecord.GetD13BalAcqDispCommon().GetD132Key().GetD132CalSedol().GetD132Sedol());

            // MOVE D13-2-CONTRACT-NO TO D171-CONTRACT-NUMBER
            fvar.GetD171Record().SetD171ContractNumber(d13BalAcqDispRecord.GetD13BalAcqDispCommon().GetD132Key().GetD132ContractNoAsString());

            // MOVE D13-2-BARGAIN-DATE TO CGTDATE2-YYMMDD1
            gvar.GetCgtdate2LinkageDate1().SetCgtdate2Ccyymmdd1AsString(d13BalAcqDispRecord.GetD13BalAcqDispCommon().GetD132BargainDateAsString());

            // CALL "CGTDATE2" USING CGTDATE2-LINKAGE-DATE1

            /* must uncomment
            var cgtdate2 = new Cgtdate2();
            cgtdate2.Run(gvar.GetCgtdate2LinkageDate1());*/

            // STRING CGTDATE2-C-DD1 '/' CGTDATE2-C-MM1 '/' CGTDATE2-C-CCYY1 DELIMITED BY SIZE INTO D171-BARGAIN-DATE
            /*must uncomment
            string bargainDateString = $"{gvar.GetCgtdate2LinkageDate1().GetCgtdate2CDd1()}/{gvar.GetCgtdate2LinkageDate1().GetCgtdate2CMm1()}/{gvar.GetCgtdate2LinkageDate1().GetCgtdate2CCcyy1()}";
            fvar.GetD171Record().SetD171BargainDate(bargainDateString);*/

            // IF D13-2-SETTLEMENT-DATE <> SPACES
            if (!string.IsNullOrEmpty(d13BalAcqDispRecord.GetD13BalAcqDispCommon().GetD132SettlementDateAsString()))
            {
                // MOVE D13-2-SETTLEMENT-DATE TO CGTDATE2-YYMMDD1
                gvar.GetCgtdate2LinkageDate1().SetCgtdate2Ccyymmdd1AsString(d13BalAcqDispRecord.GetD13BalAcqDispCommon().GetD132SettlementDateAsString());

                // CALL "CGTDATE2" USING CGTDATE2-LINKAGE-DATE1
                /* must uncomment
                cgtdate2.Run(gvar.GetCgtdate2LinkageDate1());*/

                // STRING CGTDATE2-C-DD1 '/' CGTDATE2-C-MM1 '/' CGTDATE2-C-CCYY1 DELIMITED BY SIZE INTO D171-SETTLEMENT-DATE
                /*must uncomment
                string settlementDateString = $"{gvar.GetCgtdate2LinkageDate1().GetCgtdate2CDd1()}/{gvar.GetCgtdate2LinkageDate1().GetCgtdate2CMm1()}/{gvar.GetCgtdate2LinkageDate1().GetCgtdate2CCcyy1()}";
                fvar.GetD171Record().SetD171SettlementDate(settlementDateString);*/
            }

            // MOVE D13-2-TRANSACTION-CATEGORY TO D171-TRANSACTION-CATEGORY
            fvar.GetD171Record().SetD171TransactionCategory(d13BalAcqDispRecord.GetD13BalAcqDispCommon().GetD132TransactionCategory());

            // IF D13-2-RECORD-CODE = '02'
            if (d13BalAcqDispRecord.GetD13BalAcqDispCommon().GetD132Key().GetD132RecordCode() == 2)
            {
                // MOVE D13-2-B-F-TRANCHE-TOTAL-UNITS TO D171-QUANTITY
                fvar.GetD171Record().SetD171Quantity(d13BalAcqDispRecord.GetFiller42().GetD132Record02Fields().GetD132BalAcqExtra().GetD132BfTrancheTotalUnits());

                // MOVE D13-2-B-F-UNIND-COST-BALANCE(1) TO D171-VALUE
                fvar.GetD171Record().SetD171Value(d13BalAcqDispRecord.GetFiller42().GetD132Record02Fields().GetD132BalCostListAt(0).GetD132BalCost());
            }
            else
            {
                // SUBTRACT D13-2-NUMBER-OF-UNITS FROM ZERO GIVING D171-QUANTITY
                fvar.GetD171Record().SetD171Quantity(-d13BalAcqDispRecord.GetFiller42().GetD132Record03Fields().GetD132DispExtra().GetD132NumberOfUnits());

                // SUBTRACT D13-2-PROCEEDS FROM ZERO GIVING D171-VALUE
                fvar.GetD171Record().SetD171Value(-d13BalAcqDispRecord.GetFiller42().GetD132Record03Fields().GetD132DispExtra().GetD132Proceeds());
            }

            // PERFORM X1-WRITE-EXPORT
            X1WriteExport(fvar, gvar, ivar);

            // ADD 1 TO W-EXPORT-COUNT
            gvar.SetWExportCount(gvar.GetWExportCount() + 1);
        }

        /// <summary>
        /// This method simulates the COBOL CALL operation that invokes the 'EQTPATH' program,
        /// passing the EQTPATH-LINKAGE data structure. This is a direct translation of the
        /// 'xCallEqtpath' COBOL paragraph.
        /// </summary>
        public void XCallEqtpath(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // COBOL: CALL   'EQTPATH'   USING   EQTPATH-LINKAGE.
            // C# equivalent:
            var eqtpath = new Eqtpath();
            eqtpath.GetIvar().SetEqtpathLinkageAsString(gvar.GetEqtpathLinkageAsString());
            eqtpath.Run(eqtpath.GetGvar(), eqtpath.GetIvar(), equityGlobalParms);
        }
    }
}