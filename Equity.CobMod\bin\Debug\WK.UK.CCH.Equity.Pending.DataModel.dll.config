<?xml version="1.0"?>
<configuration>
  <configSections>
  </configSections>
  <connectionStrings>
    <add name="WK.UK.CCH.Equity.DataModel.Properties.Settings.EquityNetConnectionString1" connectionString="Data Source=EQDEVSQL;Initial Catalog=EquityNet;Integrated Security=True" providerName="System.Data.SqlClient"/>
    <add name="WK.UK.CCH.Equity.DataModel.Properties.Settings.EquityNetConnectionString" connectionString="Data Source=WOKINGTESTER;Initial Catalog=EquityNet;Integrated Security=True" providerName="System.Data.SqlClient"/>
    <add name="WK.UK.CCH.Equity.DataModel.Properties.Settings.EquityNetConnectionString2" connectionString="Data Source=EQDEVSQL;Initial Catalog=EquityNet;User ID=sa" providerName="System.Data.SqlClient"/>
    <add name="WK.UK.CCH.Equity.Pending.DataModel.Properties.Settings.EquityNetConnectionString" connectionString="Data Source=EQDEVSQL2K5;Initial Catalog=Equity-e;Integrated Security=True" providerName="System.Data.SqlClient"/>
  </connectionStrings>
<startup><supportedRuntime version="v2.0.50727"/></startup></configuration>
