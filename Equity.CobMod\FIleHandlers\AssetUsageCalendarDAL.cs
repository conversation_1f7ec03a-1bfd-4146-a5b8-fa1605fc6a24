﻿using Legacy4.Equity.CobMod.Models;
using WK.UK.CCH.Equity.CobolDataTransformation;

namespace Equity.CobMod.FIleHandlers
{
    /// <summary>
    /// Provides the data access logic for the AssetUsageCalendar.
    /// </summary>
    public class AssetUsageCalendarDAL
    {
        // Simulated external classes (the repository and string wrapper)
        private AssetUsageCalendarDA _assetUsageCalendarDA;

        // We store the composed key extracted in the START-NOT-LESS-THAN branch.
        private ComposedKey _currentComposedKey;


        // A simple parameters structure (matching ParametersDARecord in COBOL)
        public class ParametersRecord
        {
            public string PDAFromDate { get; set; }       // PIC X(08)
            public string PDABusinessAsset { get; set; }    // PIC X(01)
        }

        // A simple composed key structure (matching ComposedKey in COBOL)
        public class ComposedKey
        {
            public string FundCode { get; set; }    // PIC X(04)
            public string SedolCode { get; set; }   // PIC X(07)
        }

        /// <summary>
        /// Processes the file action and updates the file record area.
        /// </summary>
        /// <param name="fileAction">Corresponds to L-FILE-ACTION (PIC X(02)).</param>
        /// <param name="fileRecordArea">
        /// Corresponds to L-FILE-RECORD-AREA (PIC X(1870)). Passed by reference so that its value can be updated.
        /// </param>
        /// <returns>The updated file record area.</returns>
        public string Process(string fileAction, string fileRecordArea)
        {
            // Evaluate fileAction.
            if (fileAction == CommonLinkage.START_NOT_LESS_THAN)
            {
                // In COBOL: "move L-FILE-RECORD-AREA to ComposedKey"
                // We assume that fileRecordArea begins with the composed key:
                // first 4 characters: fundCode, next 7 characters: sedolCode.
                _currentComposedKey = new ComposedKey
                {
                    FundCode = fileRecordArea.Substring(0, 4),
                    SedolCode = fileRecordArea.Substring(4, 7)
                };

                // Invoke AssetUsageCalendarDA "New" using fundCode and sedolCode.
                _assetUsageCalendarDA = new AssetUsageCalendarDA(_currentComposedKey.FundCode, _currentComposedKey.SedolCode);

                // (In this branch nothing is written back to fileRecordArea.)
            }
            else if (fileAction == CommonLinkage.READ_NEXT)
            {

                // Invoke oAssetUsageCalendarDA "ReadNext" returning sRecordArea.
                // (Assume that ReadNext returns a string from which parameters are extracted.)
                string sRecordArea = _assetUsageCalendarDA.ReadNext();

                if (!string.IsNullOrEmpty(sRecordArea))
                {
                    // In COBOL: "move sRecordArea to ParametersDARecord"
                    // For this example we assume that sRecordArea contains:
                    // - Positions 0-7: PDA-FromDate (8 characters)
                    // - Position 8: PDA-BussinesAsset (1 character)
                    var parameters = new ParametersRecord
                    {
                        PDAFromDate = sRecordArea.Substring(0, 8),
                        PDABusinessAsset = sRecordArea.Substring(8, 1)
                    };

                    // Build the D133-RECORD from the composed key and parameters.
                    // The business logic moves:
                    //   fundCode          -> D133-FUND
                    //   sedolCode         -> D133-SEDOL
                    //   PDA-FromDate      -> D133-FROM-DATE (split into CCYY, MM, DD)
                    //   PDA-BussinesAsset -> D133-BUSINESS-ASSET
                    // (Other fields such as UpdateCount and InverseKey are not set in the COBOL code.)
                    var d133Record = new D133Record
                    {
                        Key = new D133Key
                        {
                            Fund = _currentComposedKey.FundCode,
                            Sedol = _currentComposedKey.SedolCode,
                            FromDate = new D133FromDate
                            {
                                // Assuming PDAFromDate is in the format "CCYYMMDD"
                                CCYY = new D133CCYY
                                {
                                    CC = parameters.PDAFromDate.Substring(0, 2),
                                    YY = parameters.PDAFromDate.Substring(2, 2)
                                },
                                MM = parameters.PDAFromDate.Substring(4, 2),
                                DD = parameters.PDAFromDate.Substring(6, 2)
                            }
                        },
                        BusinessAsset = parameters.PDABusinessAsset,
                        // UpdateCount and InverseKey are not updated by this program.
                    };

                    // In COBOL: "move D133-RECORD to L-FILE-RECORD-AREA"
                    // Here we convert the record to a string.
                    fileRecordArea = ConvertD133RecordToString(d133Record);
                }
                else
                {
                    // If sRecordArea is empty, then move it to fileRecordArea.
                    fileRecordArea = sRecordArea;
                }
            }

            // End evaluate.
            return fileRecordArea;
        }

        /// <summary>
        /// Converts a D133Record instance into its string representation.
        /// This method simulates the COBOL move of the structured record back to the file area.
        /// </summary>
        /// <param name="record">The D133Record instance.</param>
        /// <returns>A string representation of the record.</returns>
        private string ConvertD133RecordToString(D133Record record)
        {
            // For demonstration purposes, we simply concatenate the fields.
            // In a real application you might need to ensure proper field lengths, padding, etc.
            string ccyy = record.Key.FromDate.CCYY.CC + record.Key.FromDate.CCYY.YY;
            string datePart = ccyy + record.Key.FromDate.MM + record.Key.FromDate.DD;
            // Concatenate the key fields and business asset.
            string result = record.Key.Fund.PadRight(4) +
                            record.Key.Sedol.PadRight(7) +
                            datePart.PadRight(8) +
                            record.BusinessAsset.PadRight(1);
            // (Additional fields, if any, would be concatenated to reach the expected record length.)
            return result;
        }
    }
}
