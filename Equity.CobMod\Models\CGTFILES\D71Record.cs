using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D71Record Data Structure

public class D71Record
{
    private static int _size = 181;
    // [DEBUG] Class: D71Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D71PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D71PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D71ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D71ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD71RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D71PrintControl.PadRight(1));
        result.Append(_D71ReportLine.PadRight(180));
        
        return result.ToString();
    }
    
    public void SetD71RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD71PrintControl(extracted);
        }
        offset += 1;
        if (offset + 180 <= data.Length)
        {
            string extracted = data.Substring(offset, 180).Trim();
            SetD71ReportLine(extracted);
        }
        offset += 180;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD71RecordAsString();
    }
    // Set<>String Override function
    public void SetD71Record(string value)
    {
        SetD71RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD71PrintControl()
    {
        return _D71PrintControl;
    }
    
    // Standard Setter
    public void SetD71PrintControl(string value)
    {
        _D71PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD71PrintControlAsString()
    {
        return _D71PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD71PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D71PrintControl = value;
    }
    
    // Standard Getter
    public string GetD71ReportLine()
    {
        return _D71ReportLine;
    }
    
    // Standard Setter
    public void SetD71ReportLine(string value)
    {
        _D71ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD71ReportLineAsString()
    {
        return _D71ReportLine.PadRight(180);
    }
    
    // Set<>AsString()
    public void SetD71ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D71ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}