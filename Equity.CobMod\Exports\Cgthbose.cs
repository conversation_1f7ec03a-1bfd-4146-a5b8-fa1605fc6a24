using System;
using System.Text;
using EquityProject.CommonDTO;
using EquityProject.CgthboseDTO;
using EquityProject.EqtpathPGM;
using EquityProject.EquityGlobalParmsDTO;
//using EquityProject.CgttempPGM;
using EquityProject.CgtabortPGM;
namespace EquityProject.CgthbosePGM
{
    // Cgthbose Class Definition

    //Cgthbose Class Constructor
    public class Cgthbose
    {
        // Declare Cgthbose Class private variables
        private Fvar _fvar = new Fvar();
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();
        private EquityGlobalParms equityGlobalParms =  new EquityGlobalParms();

        // Declare Cgthbose Class getters setters
        public Fvar GetFvar() { return _fvar; }
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }



        // Run() method
        public void Run(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Main entry point - calls Mainline paragraph
            Mainline(fvar, gvar, ivar);
        }

        // CallSub() method
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
        }

        /// <summary>
        /// Mainline actions to be performed based on the value of CGTSKAN-ACTION.
        /// Converts COBOL Evaluation to C# switch statement, COBOL EVALUATE to C# switch, and implements paragraph calls as method calls.
        /// </summary>
        public void Mainline(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Get the current action from the CgtskanLinkage
            string cgtskanAction = ivar.GetCgtskanLinkage().GetCgtskanAction();

            // Evaluate the CGTSKAN-ACTION to determine which method to call
            switch (cgtskanAction)
            {
                case "I":
                    // Perform initialisation logic as detailed in COBOL paragraph I-INITIALISE
                    IInitialise(fvar, gvar, ivar);
                    break;
                case "R":
                    // Perform Report DTL logic as detailed in paragraph R-REPORT-DTL
                    RReportDtl(fvar, gvar, ivar);
                    break;
                case "S":
                    // Perform SEDOL Total logic as detailed in paragraph S-SEDOL-TOTAL
                    SSedolTotal(fvar, gvar, ivar);
                    break;
                case "F":
                    // Perform SEDOL Total and Fund Total logic as detailed in paragraphs
                    // S-SEDOL-TOTAL and F-FUND-TOTAL
                    SSedolTotal(fvar, gvar, ivar);
                    FFundTotal(fvar, gvar, ivar);
                    break;
                case "G":
                    // Perform Final Total logic as detailed in paragraph G-FINAL-TOTAL
                    GFinalTotal(fvar, gvar, ivar);
                    break;
                case "Q":
                    //Perform Exit logic as detailed in paragraph Q-QUIT-REPORT
                    QQuitReport(fvar, gvar, ivar);
                    break;
                default:
                    // Handle undefined action, should never occur
                    break;
            }
        }

        /// <summary>
        /// This method replicates the functionality of the COBOL paragraph "I-INITIALISE"
        /// </summary>
        public void IInitialise(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move L-USER-NO to REPORT-USER-NO
            gvar.GetReportFile().SetReportUserNo(ivar.GetCommonLinkage().GetLUserNoAsString());

            // Move CGTSKAN-REPORT-GENERATION-NO to REPORT-GEN-NO
            gvar.GetReportFile().SetReportGenNo(ivar.GetCgtskanLinkage().GetCgtskanReportGenerationNo());

            // Move USER-DATA-PATH to EQTPATH-PATH-ENV-VARIABLE
            gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);

            // Move REPORT-FILE to EQTPATH-FILE-NAME
            gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetReportFileAsString());

            // Perform X-CALL-EQTPATH
            XCallEqtpath(fvar, gvar, ivar);

            // Move EQTPATH-PATH-FILE-NAME to REPORT-FILE-NAME
            gvar.SetReportFileName(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

            // Open EXPORT-FILE for output (file operations handled by runtime)
            // File opening is handled by the runtime system

            // Check if file open was successful
            if (gvar.IsOpenSuccessful())
            {
                // Move CGTTEMP-DELETE-FILE to CGTTEMP-ACTION
                gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_DELETE_FILE);

                // Perform X4-CALL-CGTTEMP
                X4CallCgttemp(fvar, gvar, ivar);

                // Move CGTTEMP-OPEN-IO to CGTTEMP-ACTION
                gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_OPEN_IO);

                // Perform X4-CALL-CGTTEMP
                X4CallCgttemp(fvar, gvar, ivar);

                // Move INTER-CONNECTED-FUNDS-FILE to L-FILE-NAME
                gvar.GetCgtfilesLinkage().SetLFileName("INTER-CONNECTED-FUNDS-FILE");

                // Move OPEN-I-O to L-FILE-ACTION
                gvar.GetCgtfilesLinkage().SetLFileAction("OPEN-I-O");

                // Perform X-CALL-CGTFILES
                XCallCgtfiles(fvar, gvar, ivar);

                // Initialize D164-RECORD
                fvar.GetD164Record().SetD164RecordAsString(gvar.GetWInitialisedRecord().GetWInitialisedRecordAsString());

                // Initialize W-WORK-FIELDS
                gvar.GetWWorkFields().SetWWorkFieldsAsString("");
            }
            else
            {
                // Perform X2-CGTABORT
                X2Cgtabort(fvar, gvar, ivar);
            }
        }

        /// <summary>
        /// This method corresponds to the COBOL paragraph 'R-REPORT-DTL'.
        /// </summary>
        public void RReportDtl(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // MOVE CGTSKAN-MASTER-RECORD TO D13-BAL-ACQ-DISP-RECORD and D13-SEDOL-HEADER-RECORD
            gvar.GetD13BalAcqDispRecord().SetD13BalAcqDispRecordAsString(ivar.GetCgtskanLinkage().GetCgtskanMasterRecordAsString());
            gvar.GetD13SedolHeaderRecord().SetD13SedolHeaderRecordAsString(ivar.GetCgtskanLinkage().GetCgtskanMasterRecordAsString());

            // EVALUATE D13-2-RECORD-CODE
            int recordCode = gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().GetD132Key().GetD132RecordCode();

            switch (recordCode)
            {
                case 1: // WHEN '01'
                        // INITIALIZE W-TEMP-RECORD
                    gvar.SetWTempRecord(new WTempRecord());

                    // MOVE D13-2-CO-AC-LK TO W-TEMP-CODE
                    gvar.GetWTempRecord().GetWTempKey().SetWTempCode(gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().GetD132Key().GetD132CalSedol().GetD132CoAcLk());

                    // MOVE D13-2-SEDOL TO W-TEMP-SEDOL-CODE
                    gvar.GetWTempRecord().GetWTempKey().SetWTempSedolCode(gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().GetD132Key().GetD132CalSedol().GetD132Sedol());

                    // MOVE CGTTEMP-WRITE TO CGTTEMP-ACTION
                    gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_WRITE);

                    // MOVE INTER-CONNECTED-FUNDS-FILE TO L-FILE-NAME
                    gvar.GetCgtfilesLinkage().SetLFileName("INTER-CONNECTED-FUNDS-FILE");

                    // MOVE D13-2-CO-AC-LK TO D163-Non-OLAB-Fund-Code
                    gvar.GetD163Record().GetD163Key().SetD163NonOlabFundCode(gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().GetD132Key().GetD132CalSedol().GetD132CoAcLk());

                    // MOVE SPACES TO D163-OLAB-Fund-Code
                    gvar.GetD163Record().GetD163Key().SetD163OlabFundCode("");

                    // MOVE START-NOT-LESS-THAN TO L-FILE-ACTION
                    gvar.GetCgtfilesLinkage().SetLFileAction("START-NOT-LESS-THAN");

                    // PERFORM R1-READ-INTERCONNECTED-FUNDS
                    R1ReadInterconnectedFunds(fvar, gvar, ivar);

                    // IF SUCCESSFUL AND D163-Non-OLAB-Fund-Code = D13-2-CO-AC-LK
                    if (gvar.GetCgtfilesLinkage().GetLFileReturnCode() == "00" &&
                        gvar.GetD163Record().GetD163Key().GetD163NonOlabFundCode() == gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().GetD132Key().GetD132CalSedol().GetD132CoAcLk())
                    {
                        // MOVE D163-Reconciliation-Code TO W-TEMP-CODE
                        gvar.GetWTempRecord().GetWTempKey().SetWTempCode(gvar.GetD163Record().GetD163ReconciliationCode());

                        // PERFORM R2-READ-TEMP-FILE
                        R2ReadTempFile(fvar, gvar, ivar);
                    }
                    else
                    {
                        // MOVE SPACES TO D163-Non-OLAB-Fund-Code
                        gvar.GetD163Record().GetD163Key().SetD163NonOlabFundCode("");

                        // MOVE D13-2-CO-AC-LK TO D163-OLAB-Fund-Code
                        gvar.GetD163Record().GetD163Key().SetD163OlabFundCode(gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().GetD132Key().GetD132CalSedol().GetD132CoAcLk());

                        // MOVE START-NOT-LESS-THAN-KEY2 TO L-FILE-ACTION
                        gvar.GetCgtfilesLinkage().SetLFileAction("START-NOT-LESS-THAN-KEY2");

                        // PERFORM R1-READ-INTERCONNECTED-FUNDS
                        R1ReadInterconnectedFunds(fvar, gvar, ivar);

                        // IF SUCCESSFUL AND D163-OLAB-Fund-Code = D13-2-CO-AC-LK
                        if (gvar.GetCgtfilesLinkage().GetLFileReturnCode() == "00" &&
                            gvar.GetD163Record().GetD163Key().GetD163OlabFundCode() == gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().GetD132Key().GetD132CalSedol().GetD132CoAcLk())
                        {
                            // MOVE D163-Reconciliation-Code TO W-TEMP-CODE
                            gvar.GetWTempRecord().GetWTempKey().SetWTempCode(gvar.GetD163Record().GetD163ReconciliationCode());

                            // PERFORM R2-READ-TEMP-FILE
                            R2ReadTempFile(fvar, gvar, ivar);
                        }
                    }

                    // MOVE D13-1-CURRENT-MARKET-PRICE TO W-TEMP-PRICE
                    gvar.GetWTempRecord().GetWTempDetails().SetWTempPrice(gvar.GetD13SedolHeaderRecord().GetD131CurrentMarketPrice());

                    // IF D13-1-PRICE-PCT-INDICATOR = '1'
                    if (gvar.GetD13SedolHeaderRecord().GetD131PricePctIndicator() == "1")
                    {
                        // DIVIDE 100 INTO W-TEMP-PRICE ROUNDED
                        decimal tempPrice = gvar.GetWTempRecord().GetWTempDetails().GetWTempPrice() / 100;
                        gvar.GetWTempRecord().GetWTempDetails().SetWTempPrice(Math.Round(tempPrice, 2));
                    }

                    // ADD D13-1-TOTAL-UNITS-YTD TO W-TEMP-HOLDING
                    gvar.GetWTempRecord().GetWTempDetails().SetWTempHolding(gvar.GetWTempRecord().GetWTempDetails().GetWTempHolding() + gvar.GetD13SedolHeaderRecord().GetD131TotalUnitsYtd());
                    break;

                case 2: // WHEN '02'
                        // PERFORM VARYING W-SUB FROM 1 BY 1 UNTIL W-SUB > D13-2-NO-OF-COSTS-HELD-YTD
                    int noOfCosts = gvar.GetD13BalAcqDispRecord().GetFiller19().GetD132Record02Fields().GetD132BalAcqExtra().GetD132BfNoOfCostsHeld();
                    for (int wSub = 1; wSub <= noOfCosts; wSub++)
                    {
                        gvar.GetWWorkFields().SetWSub(wSub);

                        // IF D13-2-REAL-COST(W-SUB) OR D13-2-OS-LIABILITY(W-SUB)
                        /* must uncomment
                        var balCost = gvar.GetD13BalAcqDispRecord().GetFiller19().GetD132Record02Fields().GetD132BalCostListAt(wSub - 1);
                        if (balCost.GetD132BalCost().GetD132UnitsPresent() == "REAL-COST" || balCost.GetD132UnitsPresent() == "OS-LIABILITY")
                        {
                            // ADD D13-2-B-F-UNIND-COST-BALANCE(W-SUB) TO W-TEMP-ACQUISITION-COSTS
                            decimal currentCosts = gvar.GetWTempRecord().GetWTempDetails().GetWTempAcquisitionCosts();
                            decimal additionalCosts = balCost.GetD132BfIndexedCostBalance();
                            gvar.GetWTempRecord().GetWTempDetails().SetWTempAcquisitionCosts(currentCosts + additionalCosts);
                        }*/
                    }
                    break;

                case 3: // WHEN '03'
                        // ADD D13-2-PROCEEDS TO W-TEMP-DISPOSAL-PROCEEDS
                    decimal currentProceeds = gvar.GetWTempRecord().GetWTempDetails().GetWTempDisposalProceeds();
                    decimal additionalProceeds = gvar.GetD13BalAcqDispRecord().GetFiller19().GetD132Record03Fields().GetD132DispExtra().GetD132Proceeds();
                    gvar.GetWTempRecord().GetWTempDetails().SetWTempDisposalProceeds(currentProceeds + additionalProceeds);
                    break;
            }

            // MOVE 'R' TO W-LAST-CALL
            gvar.GetWWorkFields().SetWLastCall("R");
        }

        /// <summary>
        /// This method sets up the system for processing Sedol totals.
        /// </summary>
        public void SSedolTotal(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // MOVE 'S' TO W-LAST-CALL
            gvar.GetWWorkFields().SetWLastCall("S");
        }

        /// <summary>
        /// This method corresponds to the COBOL paragraph 'F-FUND-TOTAL'.
        /// </summary>
        public void FFundTotal(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // MOVE 'F' TO W-LAST-CALL
            gvar.GetWWorkFields().SetWLastCall("F");
        }

        /// <summary>
        /// This method provides transitions for daily transaction maintenance
        /// </summary>
        public void GFinalTotal(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // PERFORM X3-WRITE-EXPORT
            X3WriteExport(fvar, gvar, ivar);

            // MOVE 'G' TO W-LAST-CALL
            gvar.GetWWorkFields().SetWLastCall("G");
        }

        /// <summary>
        /// The QQuitReport method performs actions related to closing files and cleanup
        /// </summary>
        public void QQuitReport(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // MOVE CGTTEMP-CLOSE TO CGTTEMP-ACTION
            gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_CLOSE);

            // PERFORM X4-CALL-CGTTEMP
            X4CallCgttemp(fvar, gvar, ivar);

            // MOVE CGTTEMP-DELETE-FILE TO CGTTEMP-ACTION
            gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_DELETE_FILE);

            // PERFORM X4-CALL-CGTTEMP
            X4CallCgttemp(fvar, gvar, ivar);

            // Close EXPORT-FILE (file operations handled by runtime)
            // File closing is handled by the runtime system

            // Check if file return code is not equal to zero
            if (gvar.GetWFileReturnCode() != "00")
            {
                // PERFORM X2-CGTABORT
                X2Cgtabort(fvar, gvar, ivar);
            }

            // MOVE 'Q' TO W-LAST-CALL
            gvar.GetWWorkFields().SetWLastCall("Q");
        }

        /// <summary>
        /// COBOL paragraph name: X3-WRITE-EXPORT
        /// </summary>
        public void X3WriteExport(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // MOVE SPACES TO CGTTEMP-KEY
            gvar.GetCgttempLinkage().GetCgttempRecord().SetCgttempKey("");

            // MOVE CGTTEMP-START-NOT-LESS-THAN TO CGTTEMP-ACTION
            gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_START_NOT_LESS_THAN);

            // PERFORM X4-CALL-CGTTEMP
            X4CallCgttemp(fvar, gvar, ivar);

            // MOVE CGTTEMP-READ TO CGTTEMP-ACTION
            gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_READ);

            // PERFORM X4-CALL-CGTTEMP
            X4CallCgttemp(fvar, gvar, ivar);

            // PERFORM UNTIL CGTTEMP-STATUS NOT = '00'
            while (gvar.GetCgttempLinkage().GetCgttempStatus() == "00")
            {
                // MOVE CGTTEMP-KEY TO W-TEMP-KEY
                gvar.GetWTempRecord().SetWTempKey(gvar.GetCgttempLinkage().GetCgttempRecord().GetCgttempKey());

                // MOVE CGTTEMP-DETAILS TO W-TEMP-DETAILS
                gvar.GetWTempRecord().SetWTempDetails(gvar.GetCgttempLinkage().GetCgttempRecord().GetCgttempDetails());

                // MOVE W-INITIALISED-RECORD TO D164-RECORD
                fvar.GetD164Record().SetD164RecordAsString(gvar.GetWInitialisedRecord().GetWInitialisedRecordAsString());

                // MOVE W-TEMP-CODE TO D164-CODE
                fvar.GetD164Record().SetD164Code(gvar.GetWTempRecord().GetWTempKey().GetWTempCode());

                // MOVE W-TEMP-SEDOL-CODE TO D164-SEDOL-CODE
                fvar.GetD164Record().SetD164SedolCode(gvar.GetWTempRecord().GetWTempKey().GetWTempSedolCode());

                // MOVE W-TEMP-HOLDING TO D164-HOLDING
                fvar.GetD164Record().SetD164Holding(gvar.GetWTempRecord().GetWTempDetails().GetWTempHolding());

                // COMPUTE D164-MARKET-VALUE = W-TEMP-HOLDING * W-TEMP-PRICE
                decimal holding = gvar.GetWTempRecord().GetWTempDetails().GetWTempHolding();
                decimal price = gvar.GetWTempRecord().GetWTempDetails().GetWTempPrice();
                decimal marketValue = holding * price;
                fvar.GetD164Record().SetD164MarketValue(marketValue);

                // MOVE W-TEMP-ACQUISITION-COSTS TO D164-ACQUISITION-COSTS
                fvar.GetD164Record().SetD164AcquisitionCosts(gvar.GetWTempRecord().GetWTempDetails().GetWTempAcquisitionCosts());

                // MOVE W-TEMP-DISPOSAL-PROCEEDS TO D164-DISPOSAL-PROCEEDS
                fvar.GetD164Record().SetD164DisposalProceeds(gvar.GetWTempRecord().GetWTempDetails().GetWTempDisposalProceeds());

                // WRITE D164-RECORD
                // File write operations are handled by the runtime system

                // MOVE CGTTEMP-READ TO CGTTEMP-ACTION
                gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_READ);

                // PERFORM X4-CALL-CGTTEMP
                X4CallCgttemp(fvar, gvar, ivar);
            }
        }

        /// <summary>
        /// This method simulates the COBOL CALL operation that invokes the 'EQTPATH' program
        /// </summary>
        public void XCallEqtpath(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // CALL 'EQTPATH' USING EQTPATH-LINKAGE
            var eqtpath = new Eqtpath();
            eqtpath.GetIvar().SetEqtpathLinkageAsString(gvar.GetEqtpathLinkageAsString());
            eqtpath.Run(eqtpath.GetGvar(), eqtpath.GetIvar(), equityGlobalParms);
        }

        /// <summary>
        /// This method simulates the COBOL CALL operation that invokes the 'CGTFILES' program
        /// </summary>
        public void XCallCgtfiles(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // CALL 'CGTFILES' USING COMMON-LINKAGE, CGTFILES-LINKAGE
            /* must uncomment
            var cgtfiles = new Cgtfiles();
            cgtfiles.Run(ivar.GetCommonLinkage(), gvar.GetCgtfilesLinkage());*/
        }

        /// <summary>
        /// This method simulates the COBOL CALL operation that invokes the 'CGTTEMP' program
        /// </summary>
        public void X4CallCgttemp(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // CALL 'CGTTEMP' USING COMMON-LINKAGE, CGTTEMP-LINKAGE
            //must uncomment
           /* var cgttemp = new Cgttmp();
            cgttemp.GetIvar().SetCgttempLinkageAsString(gvar.GetCgttempLinkageAsString());
            cgttemp.Run(cgttemp.GetFvar(), cgttemp.GetGvar(), cgttemp.GetIvar());*/
        }

        /// <summary>
        /// This method is converted from the COBOL paragraph X2-CGTABORT
        /// </summary>
        public void X2Cgtabort(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // MOVE PROGRAM-NAME TO L-ABORT-PROGRAM-NAME
            gvar.GetCgtabortLinkage().SetLAbortProgramName(gvar.GetProgramName());

            // MOVE W-FILE-RETURN-CODE TO L-ABORT-FILE-STATUS
            gvar.GetCgtabortLinkage().SetLAbortFileStatus(gvar.GetWFileReturnCode());

            // MOVE REPORT-FILE-NAME TO L-ABORT-FILE-NAME
            gvar.GetCgtabortLinkage().SetLAbortFileName(gvar.GetReportFileName());

            // CALL 'CGTABORT' USING COMMON-LINKAGE, CGTABORT-LINKAGE
            var cgtabort = new Cgtabort();
            cgtabort.GetIvar().SetCgtabortLinkageAsString(gvar.GetCgtabortLinkageAsString());
            cgtabort.Run(cgtabort.GetGvar(), cgtabort.GetIvar());
        }

        /// <summary>
        /// This method reads interconnected funds file
        /// </summary>
        public void R1ReadInterconnectedFunds(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // PERFORM X-CALL-CGTFILES
            XCallCgtfiles(fvar, gvar, ivar);
        }

        /// <summary>
        /// This method reads temporary file
        /// </summary>
        public void R2ReadTempFile(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // PERFORM X4-CALL-CGTTEMP
            X4CallCgttemp(fvar, gvar, ivar);
        }
    }
}