using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D92File Data Structure

public class D92File
{
    private static int _size = 12;
    // [DEBUG] Class: D92File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler270, is_external=, is_static_class=False, static_prefix=
    private string _Filler270 ="$";
    
    
    
    
    // [DEBUG] Field: D92UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D92UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler271, is_external=, is_static_class=False, static_prefix=
    private string _Filler271 ="RLD.REP";
    
    
    
    
    
    // Serialization methods
    public string GetD92FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler270.PadRight(1));
        result.Append(_D92UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler271.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD92FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller270(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD92UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller271(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD92FileAsString();
    }
    // Set<>String Override function
    public void SetD92File(string value)
    {
        SetD92FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller270()
    {
        return _Filler270;
    }
    
    // Standard Setter
    public void SetFiller270(string value)
    {
        _Filler270 = value;
    }
    
    // Get<>AsString()
    public string GetFiller270AsString()
    {
        return _Filler270.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller270AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler270 = value;
    }
    
    // Standard Getter
    public int GetD92UserNo()
    {
        return _D92UserNo;
    }
    
    // Standard Setter
    public void SetD92UserNo(int value)
    {
        _D92UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD92UserNoAsString()
    {
        return _D92UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD92UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D92UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller271()
    {
        return _Filler271;
    }
    
    // Standard Setter
    public void SetFiller271(string value)
    {
        _Filler271 = value;
    }
    
    // Get<>AsString()
    public string GetFiller271AsString()
    {
        return _Filler271.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller271AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler271 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}