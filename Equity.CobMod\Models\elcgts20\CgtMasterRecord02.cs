using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing CgtMasterRecord02 Data Structure

public class CgtMasterRecord02
{
    private static int _size = 21997;
    // [DEBUG] Class: CgtMasterRecord02, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: CgtMasterRecord02Fixed, is_external=, is_static_class=False, static_prefix=
    private CgtMasterRecord02Fixed _CgtMasterRecord02Fixed = new CgtMasterRecord02Fixed();
    
    
    
    
    // [DEBUG] Field: Filler182, is_external=, is_static_class=False, static_prefix=
    private string _Filler182 ="";
    
    
    
    
    // [DEBUG] Field: Filler183, is_external=, is_static_class=False, static_prefix=
    private Filler183[] _Filler183 = new Filler183[200];
    
    public void InitializeFiller183Array()
    {
        for (int i = 0; i < 200; i++)
        {
            _Filler183[i] = new Filler183();
        }
    }
    
    
    
    
    // Serialization methods
    public string GetCgtMasterRecord02AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_CgtMasterRecord02Fixed.GetCgtMasterRecord02FixedAsString());
        result.Append(_Filler182.PadRight(0));
        for (int i = 0; i < 200; i++)
        {
            result.Append(_Filler183[i].GetFiller183AsString());
        }
        
        return result.ToString();
    }
    
    public void SetCgtMasterRecord02AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 197 <= data.Length)
        {
            _CgtMasterRecord02Fixed.SetCgtMasterRecord02FixedAsString(data.Substring(offset, 197));
        }
        else
        {
            _CgtMasterRecord02Fixed.SetCgtMasterRecord02FixedAsString(data.Substring(offset));
        }
        offset += 197;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller182(extracted);
        }
        offset += 0;
        for (int i = 0; i < 200; i++)
        {
            if (offset + 109 > data.Length) break;
            string val = data.Substring(offset, 109);
            
            _Filler183[i].SetFiller183AsString(val);
            offset += 109;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtMasterRecord02AsString();
    }
    // Set<>String Override function
    public void SetCgtMasterRecord02(string value)
    {
        SetCgtMasterRecord02AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public CgtMasterRecord02Fixed GetCgtMasterRecord02Fixed()
    {
        return _CgtMasterRecord02Fixed;
    }
    
    // Standard Setter
    public void SetCgtMasterRecord02Fixed(CgtMasterRecord02Fixed value)
    {
        _CgtMasterRecord02Fixed = value;
    }
    
    // Get<>AsString()
    public string GetCgtMasterRecord02FixedAsString()
    {
        return _CgtMasterRecord02Fixed != null ? _CgtMasterRecord02Fixed.GetCgtMasterRecord02FixedAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtMasterRecord02FixedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CgtMasterRecord02Fixed == null)
        {
            _CgtMasterRecord02Fixed = new CgtMasterRecord02Fixed();
        }
        _CgtMasterRecord02Fixed.SetCgtMasterRecord02FixedAsString(value);
    }
    
    // Standard Getter
    public string GetFiller182()
    {
        return _Filler182;
    }
    
    // Standard Setter
    public void SetFiller182(string value)
    {
        _Filler182 = value;
    }
    
    // Get<>AsString()
    public string GetFiller182AsString()
    {
        return _Filler182.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller182AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler182 = value;
    }
    
    // Array Accessors for Filler183
    public Filler183 GetFiller183At(int index)
    {
        return _Filler183[index];
    }
    
    public void SetFiller183At(int index, Filler183 value)
    {
        _Filler183[index] = value;
    }
    
    // Flattened accessors (index 0)
    public Filler183 GetFiller183()
    {
        return _Filler183 != null && _Filler183.Length > 0
        ? _Filler183[0]
        : new Filler183();
    }
    
    public void SetFiller183(Filler183 value)
    {
        if (_Filler183 == null || _Filler183.Length == 0)
        _Filler183 = new Filler183[1];
        _Filler183[0] = value;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetCgtMasterRecord02Fixed(string value)
    {
        _CgtMasterRecord02Fixed.SetCgtMasterRecord02FixedAsString(value);
    }
    // Nested Class: CgtMasterRecord02Fixed
    public class CgtMasterRecord02Fixed
    {
        private static int _size = 197;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler180, is_external=, is_static_class=False, static_prefix=
        private string _Filler180 ="";
        
        
        
        
        // [DEBUG] Field: CgtStockExchIndicator, is_external=, is_static_class=False, static_prefix=
        private string _CgtStockExchIndicator ="";
        
        
        
        
        // [DEBUG] Field: CgtBfTrancheTotalUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _CgtBfTrancheTotalUnits =0;
        
        
        
        
        // [DEBUG] Field: CgtTrancheTotalUnitsYtd, is_external=, is_static_class=False, static_prefix=
        private decimal _CgtTrancheTotalUnitsYtd =0;
        
        
        
        
        // [DEBUG] Field: Filler181, is_external=, is_static_class=False, static_prefix=
        private string _Filler181 ="";
        
        
        
        
        // [DEBUG] Field: CgtNoOfInitialBookCosts, is_external=, is_static_class=False, static_prefix=
        private int _CgtNoOfInitialBookCosts =0;
        
        
        
        
        // [DEBUG] Field: CgtBfNoOfOccurs, is_external=, is_static_class=False, static_prefix=
        private int _CgtBfNoOfOccurs =0;
        
        
        
        
        // [DEBUG] Field: CgtNoOfOccurs, is_external=, is_static_class=False, static_prefix=
        private int _CgtNoOfOccurs =0;
        
        
        
        
    public CgtMasterRecord02Fixed() {}
    
    public CgtMasterRecord02Fixed(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller180(data.Substring(offset, 161).Trim());
        offset += 161;
        SetCgtStockExchIndicator(data.Substring(offset, 1).Trim());
        offset += 1;
        SetCgtBfTrancheTotalUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetCgtTrancheTotalUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetFiller181(data.Substring(offset, 0).Trim());
        offset += 0;
        SetCgtNoOfInitialBookCosts(int.Parse(data.Substring(offset, 3).Trim()));
        offset += 3;
        SetCgtBfNoOfOccurs(int.Parse(data.Substring(offset, 3).Trim()));
        offset += 3;
        SetCgtNoOfOccurs(int.Parse(data.Substring(offset, 3).Trim()));
        offset += 3;
        
    }
    
    // Serialization methods
    public string GetCgtMasterRecord02FixedAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler180.PadRight(161));
        result.Append(_CgtStockExchIndicator.PadRight(1));
        result.Append(_CgtBfTrancheTotalUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_CgtTrancheTotalUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler181.PadRight(0));
        result.Append(_CgtNoOfInitialBookCosts.ToString().PadLeft(3, '0'));
        result.Append(_CgtBfNoOfOccurs.ToString().PadLeft(3, '0'));
        result.Append(_CgtNoOfOccurs.ToString().PadLeft(3, '0'));
        
        return result.ToString();
    }
    
    public void SetCgtMasterRecord02FixedAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 161 <= data.Length)
        {
            string extracted = data.Substring(offset, 161).Trim();
            SetFiller180(extracted);
        }
        offset += 161;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetCgtStockExchIndicator(extracted);
        }
        offset += 1;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetCgtBfTrancheTotalUnits(parsedDec);
        }
        offset += 13;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetCgtTrancheTotalUnitsYtd(parsedDec);
        }
        offset += 13;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller181(extracted);
        }
        offset += 0;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtNoOfInitialBookCosts(parsedInt);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtBfNoOfOccurs(parsedInt);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtNoOfOccurs(parsedInt);
        }
        offset += 3;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller180()
    {
        return _Filler180;
    }
    
    // Standard Setter
    public void SetFiller180(string value)
    {
        _Filler180 = value;
    }
    
    // Get<>AsString()
    public string GetFiller180AsString()
    {
        return _Filler180.PadRight(161);
    }
    
    // Set<>AsString()
    public void SetFiller180AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler180 = value;
    }
    
    // Standard Getter
    public string GetCgtStockExchIndicator()
    {
        return _CgtStockExchIndicator;
    }
    
    // Standard Setter
    public void SetCgtStockExchIndicator(string value)
    {
        _CgtStockExchIndicator = value;
    }
    
    // Get<>AsString()
    public string GetCgtStockExchIndicatorAsString()
    {
        return _CgtStockExchIndicator.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetCgtStockExchIndicatorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtStockExchIndicator = value;
    }
    
    // Standard Getter
    public decimal GetCgtBfTrancheTotalUnits()
    {
        return _CgtBfTrancheTotalUnits;
    }
    
    // Standard Setter
    public void SetCgtBfTrancheTotalUnits(decimal value)
    {
        _CgtBfTrancheTotalUnits = value;
    }
    
    // Get<>AsString()
    public string GetCgtBfTrancheTotalUnitsAsString()
    {
        return _CgtBfTrancheTotalUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetCgtBfTrancheTotalUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _CgtBfTrancheTotalUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetCgtTrancheTotalUnitsYtd()
    {
        return _CgtTrancheTotalUnitsYtd;
    }
    
    // Standard Setter
    public void SetCgtTrancheTotalUnitsYtd(decimal value)
    {
        _CgtTrancheTotalUnitsYtd = value;
    }
    
    // Get<>AsString()
    public string GetCgtTrancheTotalUnitsYtdAsString()
    {
        return _CgtTrancheTotalUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetCgtTrancheTotalUnitsYtdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _CgtTrancheTotalUnitsYtd = parsed;
    }
    
    // Standard Getter
    public string GetFiller181()
    {
        return _Filler181;
    }
    
    // Standard Setter
    public void SetFiller181(string value)
    {
        _Filler181 = value;
    }
    
    // Get<>AsString()
    public string GetFiller181AsString()
    {
        return _Filler181.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller181AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler181 = value;
    }
    
    // Standard Getter
    public int GetCgtNoOfInitialBookCosts()
    {
        return _CgtNoOfInitialBookCosts;
    }
    
    // Standard Setter
    public void SetCgtNoOfInitialBookCosts(int value)
    {
        _CgtNoOfInitialBookCosts = value;
    }
    
    // Get<>AsString()
    public string GetCgtNoOfInitialBookCostsAsString()
    {
        return _CgtNoOfInitialBookCosts.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetCgtNoOfInitialBookCostsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _CgtNoOfInitialBookCosts = parsed;
    }
    
    // Standard Getter
    public int GetCgtBfNoOfOccurs()
    {
        return _CgtBfNoOfOccurs;
    }
    
    // Standard Setter
    public void SetCgtBfNoOfOccurs(int value)
    {
        _CgtBfNoOfOccurs = value;
    }
    
    // Get<>AsString()
    public string GetCgtBfNoOfOccursAsString()
    {
        return _CgtBfNoOfOccurs.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetCgtBfNoOfOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _CgtBfNoOfOccurs = parsed;
    }
    
    // Standard Getter
    public int GetCgtNoOfOccurs()
    {
        return _CgtNoOfOccurs;
    }
    
    // Standard Setter
    public void SetCgtNoOfOccurs(int value)
    {
        _CgtNoOfOccurs = value;
    }
    
    // Get<>AsString()
    public string GetCgtNoOfOccursAsString()
    {
        return _CgtNoOfOccurs.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetCgtNoOfOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _CgtNoOfOccurs = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetFiller183(string value)
{
    if (!string.IsNullOrEmpty(value) && value.Length < Filler183.GetSize() * _Filler183.Length)
    {
        value = value.PadRight(Filler183.GetSize() * _Filler183.Length);
    }
    
    int offset = 0;
    for (int i = 0; i < _Filler183.Length; i++)
    {
        if (offset + Filler183.GetSize() > value.Length) break;
        string chunk = value.Substring(offset, Filler183.GetSize());
        _Filler183[i].SetFiller183AsString(chunk);
        offset += Filler183.GetSize();
    }
}
// Nested Class: Filler183
public class Filler183
{
    private static int _size = 109;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtBfBalanceUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _CgtBfBalanceUnits =0;
    
    
    
    
    // [DEBUG] Field: CgtBalanceUnitsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _CgtBalanceUnitsYtd =0;
    
    
    
    
    // [DEBUG] Field: CgtUnitsPresent, is_external=, is_static_class=False, static_prefix=
    private string _CgtUnitsPresent ="";
    
    
    // 88-level condition checks for CgtUnitsPresent
    public bool IsCgtRealCost()
    {
        if (this._CgtUnitsPresent == "'0'") return true;
        if (this._CgtUnitsPresent == "'1'") return true;
        if (this._CgtUnitsPresent == "'I'") return true;
        if (this._CgtUnitsPresent == "'4'") return true;
        return false;
    }
    public bool IsCgtCostWithUnits()
    {
        if (this._CgtUnitsPresent == "'1'") return true;
        if (this._CgtUnitsPresent == "'I'") return true;
        return false;
    }
    public bool IsCgtIndexed1982Cost()
    {
        if (this._CgtUnitsPresent == "'I'") return true;
        return false;
    }
    public bool IsCgtGiltLoss()
    {
        if (this._CgtUnitsPresent == "'2'") return true;
        return false;
    }
    public bool IsCgtBfGain()
    {
        if (this._CgtUnitsPresent == "'3'") return true;
        return false;
    }
    public bool IsCgtSmallDistribution()
    {
        if (this._CgtUnitsPresent == "'4'") return true;
        return false;
    }
    public bool IsCgtBfIndxn()
    {
        if (this._CgtUnitsPresent == "'5'") return true;
        return false;
    }
    public bool IsCgtOsLiability()
    {
        if (this._CgtUnitsPresent == "'6'") return true;
        return false;
    }
    public bool IsCgt1982Bdv()
    {
        if (this._CgtUnitsPresent == "'7'") return true;
        return false;
    }
    public bool IsCgtIrishDdCost()
    {
        if (this._CgtUnitsPresent == "'D'") return true;
        return false;
    }
    public bool IsCgtIrishNdlCost()
    {
        if (this._CgtUnitsPresent == "'N'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: CgtBfUnindexedCostBalance, is_external=, is_static_class=False, static_prefix=
    private decimal _CgtBfUnindexedCostBalance =0;
    
    
    
    
    // [DEBUG] Field: CgtUnindexedCostBalanceYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _CgtUnindexedCostBalanceYtd =0;
    
    
    
    
    // [DEBUG] Field: CgtBfBudgetDayValue, is_external=, is_static_class=False, static_prefix=
    private decimal _CgtBfBudgetDayValue =0;
    
    
    
    
    // [DEBUG] Field: CgtBudgetDayValueYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _CgtBudgetDayValueYtd =0;
    
    
    
    
    // [DEBUG] Field: CgtDates, is_external=, is_static_class=False, static_prefix=
    private Filler183.CgtDates _CgtDates = new Filler183.CgtDates();
    
    
    
    
public Filler183() {}

public Filler183(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtBfBalanceUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetCgtBalanceUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetCgtUnitsPresent(data.Substring(offset, 1).Trim());
    offset += 1;
    SetCgtBfUnindexedCostBalance(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetCgtUnindexedCostBalanceYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetCgtBfBudgetDayValue(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetCgtBudgetDayValueYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    _CgtDates.SetCgtDatesAsString(data.Substring(offset, CgtDates.GetSize()));
    offset += 18;
    
}

// Serialization methods
public string GetFiller183AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtBfBalanceUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_CgtBalanceUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_CgtUnitsPresent.PadRight(1));
    result.Append(_CgtBfUnindexedCostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_CgtUnindexedCostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_CgtBfBudgetDayValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_CgtBudgetDayValueYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_CgtDates.GetCgtDatesAsString());
    
    return result.ToString();
}

public void SetFiller183AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetCgtBfBalanceUnits(parsedDec);
    }
    offset += 13;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetCgtBalanceUnitsYtd(parsedDec);
    }
    offset += 13;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetCgtUnitsPresent(extracted);
    }
    offset += 1;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetCgtBfUnindexedCostBalance(parsedDec);
    }
    offset += 17;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetCgtUnindexedCostBalanceYtd(parsedDec);
    }
    offset += 17;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetCgtBfBudgetDayValue(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetCgtBudgetDayValueYtd(parsedDec);
    }
    offset += 15;
    if (offset + 18 <= data.Length)
    {
        _CgtDates.SetCgtDatesAsString(data.Substring(offset, 18));
    }
    else
    {
        _CgtDates.SetCgtDatesAsString(data.Substring(offset));
    }
    offset += 18;
}

// Getter and Setter methods

// Standard Getter
public decimal GetCgtBfBalanceUnits()
{
    return _CgtBfBalanceUnits;
}

// Standard Setter
public void SetCgtBfBalanceUnits(decimal value)
{
    _CgtBfBalanceUnits = value;
}

// Get<>AsString()
public string GetCgtBfBalanceUnitsAsString()
{
    return _CgtBfBalanceUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetCgtBfBalanceUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _CgtBfBalanceUnits = parsed;
}

// Standard Getter
public decimal GetCgtBalanceUnitsYtd()
{
    return _CgtBalanceUnitsYtd;
}

// Standard Setter
public void SetCgtBalanceUnitsYtd(decimal value)
{
    _CgtBalanceUnitsYtd = value;
}

// Get<>AsString()
public string GetCgtBalanceUnitsYtdAsString()
{
    return _CgtBalanceUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetCgtBalanceUnitsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _CgtBalanceUnitsYtd = parsed;
}

// Standard Getter
public string GetCgtUnitsPresent()
{
    return _CgtUnitsPresent;
}

// Standard Setter
public void SetCgtUnitsPresent(string value)
{
    _CgtUnitsPresent = value;
}

// Get<>AsString()
public string GetCgtUnitsPresentAsString()
{
    return _CgtUnitsPresent.PadRight(1);
}

// Set<>AsString()
public void SetCgtUnitsPresentAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtUnitsPresent = value;
}

// Standard Getter
public decimal GetCgtBfUnindexedCostBalance()
{
    return _CgtBfUnindexedCostBalance;
}

// Standard Setter
public void SetCgtBfUnindexedCostBalance(decimal value)
{
    _CgtBfUnindexedCostBalance = value;
}

// Get<>AsString()
public string GetCgtBfUnindexedCostBalanceAsString()
{
    return _CgtBfUnindexedCostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetCgtBfUnindexedCostBalanceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _CgtBfUnindexedCostBalance = parsed;
}

// Standard Getter
public decimal GetCgtUnindexedCostBalanceYtd()
{
    return _CgtUnindexedCostBalanceYtd;
}

// Standard Setter
public void SetCgtUnindexedCostBalanceYtd(decimal value)
{
    _CgtUnindexedCostBalanceYtd = value;
}

// Get<>AsString()
public string GetCgtUnindexedCostBalanceYtdAsString()
{
    return _CgtUnindexedCostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetCgtUnindexedCostBalanceYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _CgtUnindexedCostBalanceYtd = parsed;
}

// Standard Getter
public decimal GetCgtBfBudgetDayValue()
{
    return _CgtBfBudgetDayValue;
}

// Standard Setter
public void SetCgtBfBudgetDayValue(decimal value)
{
    _CgtBfBudgetDayValue = value;
}

// Get<>AsString()
public string GetCgtBfBudgetDayValueAsString()
{
    return _CgtBfBudgetDayValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetCgtBfBudgetDayValueAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _CgtBfBudgetDayValue = parsed;
}

// Standard Getter
public decimal GetCgtBudgetDayValueYtd()
{
    return _CgtBudgetDayValueYtd;
}

// Standard Setter
public void SetCgtBudgetDayValueYtd(decimal value)
{
    _CgtBudgetDayValueYtd = value;
}

// Get<>AsString()
public string GetCgtBudgetDayValueYtdAsString()
{
    return _CgtBudgetDayValueYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetCgtBudgetDayValueYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _CgtBudgetDayValueYtd = parsed;
}

// Standard Getter
public CgtDates GetCgtDates()
{
    return _CgtDates;
}

// Standard Setter
public void SetCgtDates(CgtDates value)
{
    _CgtDates = value;
}

// Get<>AsString()
public string GetCgtDatesAsString()
{
    return _CgtDates != null ? _CgtDates.GetCgtDatesAsString() : "";
}

// Set<>AsString()
public void SetCgtDatesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtDates == null)
    {
        _CgtDates = new CgtDates();
    }
    _CgtDates.SetCgtDatesAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: CgtDates
public class CgtDates
{
    private static int _size = 18;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtIndexDate, is_external=, is_static_class=False, static_prefix=
    private CgtDates.CgtIndexDate _CgtIndexDate = new CgtDates.CgtIndexDate();
    
    
    
    
    // [DEBUG] Field: CgtCostDate, is_external=, is_static_class=False, static_prefix=
    private CgtDates.CgtCostDate _CgtCostDate = new CgtDates.CgtCostDate();
    
    
    
    
    // [DEBUG] Field: Filler184, is_external=, is_static_class=False, static_prefix=
    private CgtDates.Filler184 _Filler184 = new CgtDates.Filler184();
    
    
    
    
public CgtDates() {}

public CgtDates(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _CgtIndexDate.SetCgtIndexDateAsString(data.Substring(offset, CgtIndexDate.GetSize()));
    offset += 6;
    _CgtCostDate.SetCgtCostDateAsString(data.Substring(offset, CgtCostDate.GetSize()));
    offset += 6;
    _Filler184.SetFiller184AsString(data.Substring(offset, Filler184.GetSize()));
    offset += 6;
    
}

// Serialization methods
public string GetCgtDatesAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtIndexDate.GetCgtIndexDateAsString());
    result.Append(_CgtCostDate.GetCgtCostDateAsString());
    result.Append(_Filler184.GetFiller184AsString());
    
    return result.ToString();
}

public void SetCgtDatesAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        _CgtIndexDate.SetCgtIndexDateAsString(data.Substring(offset, 6));
    }
    else
    {
        _CgtIndexDate.SetCgtIndexDateAsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        _CgtCostDate.SetCgtCostDateAsString(data.Substring(offset, 6));
    }
    else
    {
        _CgtCostDate.SetCgtCostDateAsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        _Filler184.SetFiller184AsString(data.Substring(offset, 6));
    }
    else
    {
        _Filler184.SetFiller184AsString(data.Substring(offset));
    }
    offset += 6;
}

// Getter and Setter methods

// Standard Getter
public CgtIndexDate GetCgtIndexDate()
{
    return _CgtIndexDate;
}

// Standard Setter
public void SetCgtIndexDate(CgtIndexDate value)
{
    _CgtIndexDate = value;
}

// Get<>AsString()
public string GetCgtIndexDateAsString()
{
    return _CgtIndexDate != null ? _CgtIndexDate.GetCgtIndexDateAsString() : "";
}

// Set<>AsString()
public void SetCgtIndexDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtIndexDate == null)
    {
        _CgtIndexDate = new CgtIndexDate();
    }
    _CgtIndexDate.SetCgtIndexDateAsString(value);
}

// Standard Getter
public CgtCostDate GetCgtCostDate()
{
    return _CgtCostDate;
}

// Standard Setter
public void SetCgtCostDate(CgtCostDate value)
{
    _CgtCostDate = value;
}

// Get<>AsString()
public string GetCgtCostDateAsString()
{
    return _CgtCostDate != null ? _CgtCostDate.GetCgtCostDateAsString() : "";
}

// Set<>AsString()
public void SetCgtCostDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtCostDate == null)
    {
        _CgtCostDate = new CgtCostDate();
    }
    _CgtCostDate.SetCgtCostDateAsString(value);
}

// Standard Getter
public Filler184 GetFiller184()
{
    return _Filler184;
}

// Standard Setter
public void SetFiller184(Filler184 value)
{
    _Filler184 = value;
}

// Get<>AsString()
public string GetFiller184AsString()
{
    return _Filler184 != null ? _Filler184.GetFiller184AsString() : "";
}

// Set<>AsString()
public void SetFiller184AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler184 == null)
    {
        _Filler184 = new Filler184();
    }
    _Filler184.SetFiller184AsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: CgtIndexDate
public class CgtIndexDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtIndexDateYymm, is_external=, is_static_class=False, static_prefix=
    private CgtIndexDate.CgtIndexDateYymm _CgtIndexDateYymm = new CgtIndexDate.CgtIndexDateYymm();
    
    
    
    
    // [DEBUG] Field: CgtIndexDateDd, is_external=, is_static_class=False, static_prefix=
    private int _CgtIndexDateDd =0;
    
    
    
    
public CgtIndexDate() {}

public CgtIndexDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _CgtIndexDateYymm.SetCgtIndexDateYymmAsString(data.Substring(offset, CgtIndexDateYymm.GetSize()));
    offset += 4;
    SetCgtIndexDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetCgtIndexDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtIndexDateYymm.GetCgtIndexDateYymmAsString());
    result.Append(_CgtIndexDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetCgtIndexDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        _CgtIndexDateYymm.SetCgtIndexDateYymmAsString(data.Substring(offset, 4));
    }
    else
    {
        _CgtIndexDateYymm.SetCgtIndexDateYymmAsString(data.Substring(offset));
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtIndexDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public CgtIndexDateYymm GetCgtIndexDateYymm()
{
    return _CgtIndexDateYymm;
}

// Standard Setter
public void SetCgtIndexDateYymm(CgtIndexDateYymm value)
{
    _CgtIndexDateYymm = value;
}

// Get<>AsString()
public string GetCgtIndexDateYymmAsString()
{
    return _CgtIndexDateYymm != null ? _CgtIndexDateYymm.GetCgtIndexDateYymmAsString() : "";
}

// Set<>AsString()
public void SetCgtIndexDateYymmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtIndexDateYymm == null)
    {
        _CgtIndexDateYymm = new CgtIndexDateYymm();
    }
    _CgtIndexDateYymm.SetCgtIndexDateYymmAsString(value);
}

// Standard Getter
public int GetCgtIndexDateDd()
{
    return _CgtIndexDateDd;
}

// Standard Setter
public void SetCgtIndexDateDd(int value)
{
    _CgtIndexDateDd = value;
}

// Get<>AsString()
public string GetCgtIndexDateDdAsString()
{
    return _CgtIndexDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtIndexDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _CgtIndexDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: CgtIndexDateYymm
public class CgtIndexDateYymm
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtIndexDateYy, is_external=, is_static_class=False, static_prefix=
    private int _CgtIndexDateYy =0;
    
    
    
    
    // [DEBUG] Field: CgtIndexDateMm, is_external=, is_static_class=False, static_prefix=
    private int _CgtIndexDateMm =0;
    
    
    
    
public CgtIndexDateYymm() {}

public CgtIndexDateYymm(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtIndexDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtIndexDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetCgtIndexDateYymmAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtIndexDateYy.ToString().PadLeft(2, '0'));
    result.Append(_CgtIndexDateMm.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetCgtIndexDateYymmAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtIndexDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtIndexDateMm(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtIndexDateYy()
{
    return _CgtIndexDateYy;
}

// Standard Setter
public void SetCgtIndexDateYy(int value)
{
    _CgtIndexDateYy = value;
}

// Get<>AsString()
public string GetCgtIndexDateYyAsString()
{
    return _CgtIndexDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtIndexDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _CgtIndexDateYy = parsed;
}

// Standard Getter
public int GetCgtIndexDateMm()
{
    return _CgtIndexDateMm;
}

// Standard Setter
public void SetCgtIndexDateMm(int value)
{
    _CgtIndexDateMm = value;
}

// Get<>AsString()
public string GetCgtIndexDateMmAsString()
{
    return _CgtIndexDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtIndexDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _CgtIndexDateMm = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: CgtCostDate
public class CgtCostDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtCostDateYymm, is_external=, is_static_class=False, static_prefix=
    private CgtCostDate.CgtCostDateYymm _CgtCostDateYymm = new CgtCostDate.CgtCostDateYymm();
    
    
    
    
    // [DEBUG] Field: CgtCostDateDd, is_external=, is_static_class=False, static_prefix=
    private int _CgtCostDateDd =0;
    
    
    
    
public CgtCostDate() {}

public CgtCostDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _CgtCostDateYymm.SetCgtCostDateYymmAsString(data.Substring(offset, CgtCostDateYymm.GetSize()));
    offset += 4;
    SetCgtCostDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetCgtCostDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtCostDateYymm.GetCgtCostDateYymmAsString());
    result.Append(_CgtCostDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetCgtCostDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        _CgtCostDateYymm.SetCgtCostDateYymmAsString(data.Substring(offset, 4));
    }
    else
    {
        _CgtCostDateYymm.SetCgtCostDateYymmAsString(data.Substring(offset));
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtCostDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public CgtCostDateYymm GetCgtCostDateYymm()
{
    return _CgtCostDateYymm;
}

// Standard Setter
public void SetCgtCostDateYymm(CgtCostDateYymm value)
{
    _CgtCostDateYymm = value;
}

// Get<>AsString()
public string GetCgtCostDateYymmAsString()
{
    return _CgtCostDateYymm != null ? _CgtCostDateYymm.GetCgtCostDateYymmAsString() : "";
}

// Set<>AsString()
public void SetCgtCostDateYymmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtCostDateYymm == null)
    {
        _CgtCostDateYymm = new CgtCostDateYymm();
    }
    _CgtCostDateYymm.SetCgtCostDateYymmAsString(value);
}

// Standard Getter
public int GetCgtCostDateDd()
{
    return _CgtCostDateDd;
}

// Standard Setter
public void SetCgtCostDateDd(int value)
{
    _CgtCostDateDd = value;
}

// Get<>AsString()
public string GetCgtCostDateDdAsString()
{
    return _CgtCostDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtCostDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _CgtCostDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: CgtCostDateYymm
public class CgtCostDateYymm
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtCostDateYy, is_external=, is_static_class=False, static_prefix=
    private int _CgtCostDateYy =0;
    
    
    
    
    // [DEBUG] Field: CgtCostDateMm, is_external=, is_static_class=False, static_prefix=
    private int _CgtCostDateMm =0;
    
    
    
    
public CgtCostDateYymm() {}

public CgtCostDateYymm(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtCostDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtCostDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetCgtCostDateYymmAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtCostDateYy.ToString().PadLeft(2, '0'));
    result.Append(_CgtCostDateMm.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetCgtCostDateYymmAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtCostDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtCostDateMm(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtCostDateYy()
{
    return _CgtCostDateYy;
}

// Standard Setter
public void SetCgtCostDateYy(int value)
{
    _CgtCostDateYy = value;
}

// Get<>AsString()
public string GetCgtCostDateYyAsString()
{
    return _CgtCostDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtCostDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _CgtCostDateYy = parsed;
}

// Standard Getter
public int GetCgtCostDateMm()
{
    return _CgtCostDateMm;
}

// Standard Setter
public void SetCgtCostDateMm(int value)
{
    _CgtCostDateMm = value;
}

// Get<>AsString()
public string GetCgtCostDateMmAsString()
{
    return _CgtCostDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtCostDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _CgtCostDateMm = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: Filler184
public class Filler184
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtNiNpPiCosts, is_external=, is_static_class=False, static_prefix=
    private string _CgtNiNpPiCosts ="";
    
    
    
    
    // [DEBUG] Field: CgtTaperDate, is_external=, is_static_class=False, static_prefix=
    private Filler184.CgtTaperDate _CgtTaperDate = new Filler184.CgtTaperDate();
    
    
    
    
    // [DEBUG] Field: CgtBfTaperUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _CgtBfTaperUnits =0;
    
    
    
    
    // [DEBUG] Field: CgtBfTaperUnitsX, is_external=, is_static_class=False, static_prefix=
    private string _CgtBfTaperUnitsX ="";
    
    
    
    
    // [DEBUG] Field: CgtTaperUnitsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _CgtTaperUnitsYtd =0;
    
    
    
    
public Filler184() {}

public Filler184(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtNiNpPiCosts(data.Substring(offset, 0).Trim());
    offset += 0;
    _CgtTaperDate.SetCgtTaperDateAsString(data.Substring(offset, CgtTaperDate.GetSize()));
    offset += 0;
    SetCgtBfTaperUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 0)));
    offset += 0;
    SetCgtBfTaperUnitsX(data.Substring(offset, 6).Trim());
    offset += 6;
    SetCgtTaperUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 0)));
    offset += 0;
    
}

// Serialization methods
public string GetFiller184AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtNiNpPiCosts.PadRight(0));
    result.Append(_CgtTaperDate.GetCgtTaperDateAsString());
    result.Append(_CgtBfTaperUnits.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_CgtBfTaperUnitsX.PadRight(6));
    result.Append(_CgtTaperUnitsYtd.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetFiller184AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetCgtNiNpPiCosts(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        _CgtTaperDate.SetCgtTaperDateAsString(data.Substring(offset, 0));
    }
    else
    {
        _CgtTaperDate.SetCgtTaperDateAsString(data.Substring(offset));
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetCgtBfTaperUnits(parsedDec);
    }
    offset += 0;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetCgtBfTaperUnitsX(extracted);
    }
    offset += 6;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetCgtTaperUnitsYtd(parsedDec);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetCgtNiNpPiCosts()
{
    return _CgtNiNpPiCosts;
}

// Standard Setter
public void SetCgtNiNpPiCosts(string value)
{
    _CgtNiNpPiCosts = value;
}

// Get<>AsString()
public string GetCgtNiNpPiCostsAsString()
{
    return _CgtNiNpPiCosts.PadRight(0);
}

// Set<>AsString()
public void SetCgtNiNpPiCostsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtNiNpPiCosts = value;
}

// Standard Getter
public CgtTaperDate GetCgtTaperDate()
{
    return _CgtTaperDate;
}

// Standard Setter
public void SetCgtTaperDate(CgtTaperDate value)
{
    _CgtTaperDate = value;
}

// Get<>AsString()
public string GetCgtTaperDateAsString()
{
    return _CgtTaperDate != null ? _CgtTaperDate.GetCgtTaperDateAsString() : "";
}

// Set<>AsString()
public void SetCgtTaperDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_CgtTaperDate == null)
    {
        _CgtTaperDate = new CgtTaperDate();
    }
    _CgtTaperDate.SetCgtTaperDateAsString(value);
}

// Standard Getter
public decimal GetCgtBfTaperUnits()
{
    return _CgtBfTaperUnits;
}

// Standard Setter
public void SetCgtBfTaperUnits(decimal value)
{
    _CgtBfTaperUnits = value;
}

// Get<>AsString()
public string GetCgtBfTaperUnitsAsString()
{
    return _CgtBfTaperUnits.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetCgtBfTaperUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _CgtBfTaperUnits = parsed;
}

// Standard Getter
public string GetCgtBfTaperUnitsX()
{
    return _CgtBfTaperUnitsX;
}

// Standard Setter
public void SetCgtBfTaperUnitsX(string value)
{
    _CgtBfTaperUnitsX = value;
}

// Get<>AsString()
public string GetCgtBfTaperUnitsXAsString()
{
    return _CgtBfTaperUnitsX.PadRight(6);
}

// Set<>AsString()
public void SetCgtBfTaperUnitsXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtBfTaperUnitsX = value;
}

// Standard Getter
public decimal GetCgtTaperUnitsYtd()
{
    return _CgtTaperUnitsYtd;
}

// Standard Setter
public void SetCgtTaperUnitsYtd(decimal value)
{
    _CgtTaperUnitsYtd = value;
}

// Get<>AsString()
public string GetCgtTaperUnitsYtdAsString()
{
    return _CgtTaperUnitsYtd.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetCgtTaperUnitsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _CgtTaperUnitsYtd = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: CgtTaperDate
public class CgtTaperDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CgtTaperDateYy, is_external=, is_static_class=False, static_prefix=
    private string _CgtTaperDateYy ="";
    
    
    
    
    // [DEBUG] Field: CgtTaperDateMm, is_external=, is_static_class=False, static_prefix=
    private string _CgtTaperDateMm ="";
    
    
    
    
    // [DEBUG] Field: CgtTaperDateDd, is_external=, is_static_class=False, static_prefix=
    private string _CgtTaperDateDd ="";
    
    
    
    
public CgtTaperDate() {}

public CgtTaperDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtTaperDateYy(data.Substring(offset, 0).Trim());
    offset += 0;
    SetCgtTaperDateMm(data.Substring(offset, 0).Trim());
    offset += 0;
    SetCgtTaperDateDd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetCgtTaperDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CgtTaperDateYy.PadRight(0));
    result.Append(_CgtTaperDateMm.PadRight(0));
    result.Append(_CgtTaperDateDd.PadRight(0));
    
    return result.ToString();
}

public void SetCgtTaperDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetCgtTaperDateYy(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetCgtTaperDateMm(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetCgtTaperDateDd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetCgtTaperDateYy()
{
    return _CgtTaperDateYy;
}

// Standard Setter
public void SetCgtTaperDateYy(string value)
{
    _CgtTaperDateYy = value;
}

// Get<>AsString()
public string GetCgtTaperDateYyAsString()
{
    return _CgtTaperDateYy.PadRight(0);
}

// Set<>AsString()
public void SetCgtTaperDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtTaperDateYy = value;
}

// Standard Getter
public string GetCgtTaperDateMm()
{
    return _CgtTaperDateMm;
}

// Standard Setter
public void SetCgtTaperDateMm(string value)
{
    _CgtTaperDateMm = value;
}

// Get<>AsString()
public string GetCgtTaperDateMmAsString()
{
    return _CgtTaperDateMm.PadRight(0);
}

// Set<>AsString()
public void SetCgtTaperDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtTaperDateMm = value;
}

// Standard Getter
public string GetCgtTaperDateDd()
{
    return _CgtTaperDateDd;
}

// Standard Setter
public void SetCgtTaperDateDd(string value)
{
    _CgtTaperDateDd = value;
}

// Get<>AsString()
public string GetCgtTaperDateDdAsString()
{
    return _CgtTaperDateDd.PadRight(0);
}

// Set<>AsString()
public void SetCgtTaperDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CgtTaperDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}
}

}}
