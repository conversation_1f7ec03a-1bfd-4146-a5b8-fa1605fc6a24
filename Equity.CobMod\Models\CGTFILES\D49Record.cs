using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D49Record Data Structure

public class D49Record
{
    private static int _size = 151;
    // [DEBUG] Class: D49Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D49PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D49PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D49ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D49ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD49RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D49PrintControl.PadRight(1));
        result.Append(_D49ReportLine.PadRight(150));
        
        return result.ToString();
    }
    
    public void SetD49RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD49PrintControl(extracted);
        }
        offset += 1;
        if (offset + 150 <= data.Length)
        {
            string extracted = data.Substring(offset, 150).Trim();
            SetD49ReportLine(extracted);
        }
        offset += 150;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD49RecordAsString();
    }
    // Set<>String Override function
    public void SetD49Record(string value)
    {
        SetD49RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD49PrintControl()
    {
        return _D49PrintControl;
    }
    
    // Standard Setter
    public void SetD49PrintControl(string value)
    {
        _D49PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD49PrintControlAsString()
    {
        return _D49PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD49PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D49PrintControl = value;
    }
    
    // Standard Getter
    public string GetD49ReportLine()
    {
        return _D49ReportLine;
    }
    
    // Standard Setter
    public void SetD49ReportLine(string value)
    {
        _D49ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD49ReportLineAsString()
    {
        return _D49ReportLine.PadRight(150);
    }
    
    // Set<>AsString()
    public void SetD49ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D49ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}