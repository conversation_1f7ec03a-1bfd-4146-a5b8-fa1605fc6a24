using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WWarningFields Data Structure

public class WWarningFields
{
    private static int _size = 1004;
    // [DEBUG] Class: WWarningFields, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WWarningRecord, is_external=, is_static_class=False, static_prefix=
    private string _WWarningRecord ="";
    
    
    
    
    // [DEBUG] Field: WWarningError, is_external=, is_static_class=False, static_prefix=
    private int _WWarningError =0;
    
    
    
    
    
    // Serialization methods
    public string GetWWarningFieldsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WWarningRecord.PadRight(1000));
        result.Append(_WWarningError.ToString().PadLeft(4, '0'));
        
        return result.ToString();
    }
    
    public void SetWWarningFieldsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1000 <= data.Length)
        {
            string extracted = data.Substring(offset, 1000).Trim();
            SetWWarningRecord(extracted);
        }
        offset += 1000;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWWarningError(parsedInt);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWWarningFieldsAsString();
    }
    // Set<>String Override function
    public void SetWWarningFields(string value)
    {
        SetWWarningFieldsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWWarningRecord()
    {
        return _WWarningRecord;
    }
    
    // Standard Setter
    public void SetWWarningRecord(string value)
    {
        _WWarningRecord = value;
    }
    
    // Get<>AsString()
    public string GetWWarningRecordAsString()
    {
        return _WWarningRecord.PadRight(1000);
    }
    
    // Set<>AsString()
    public void SetWWarningRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WWarningRecord = value;
    }
    
    // Standard Getter
    public int GetWWarningError()
    {
        return _WWarningError;
    }
    
    // Standard Setter
    public void SetWWarningError(int value)
    {
        _WWarningError = value;
    }
    
    // Get<>AsString()
    public string GetWWarningErrorAsString()
    {
        return _WWarningError.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWWarningErrorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WWarningError = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
