using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtTables Data Structure

public class WtTables
{
    private static int _size = 16;
    // [DEBUG] Class: WtTables, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler373, is_external=, is_static_class=False, static_prefix=
    private string _Filler373 ="TABLES==========";
    
    
    
    
    
    // Serialization methods
    public string GetWtTablesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler373.PadRight(16));
        
        return result.ToString();
    }
    
    public void SetWtTablesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller373(extracted);
        }
        offset += 16;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtTablesAsString();
    }
    // Set<>String Override function
    public void SetWtTables(string value)
    {
        SetWtTablesAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller373()
    {
        return _Filler373;
    }
    
    // Standard Setter
    public void SetFiller373(string value)
    {
        _Filler373 = value;
    }
    
    // Get<>AsString()
    public string GetFiller373AsString()
    {
        return _Filler373.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller373AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler373 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
