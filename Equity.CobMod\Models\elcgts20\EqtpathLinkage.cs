using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing EqtpathLinkage Data Structure

public class EqtpathLinkage
{
    private static int _size = 513;
    // [DEBUG] Class: EqtpathLinkage, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: EqtpathPathEnvVariable, is_external=, is_static_class=False, static_prefix=
    private string _EqtpathPathEnvVariable ="";
    
    
    
    
    // [DEBUG] Field: EqtpathFileName, is_external=, is_static_class=False, static_prefix=
    private string _EqtpathFileName ="";
    
    
    
    
    // [DEBUG] Field: EqtpathPathFileName, is_external=, is_static_class=False, static_prefix=
    private string _EqtpathPathFileName ="";
    
    
    
    
    // [DEBUG] Field: EqtpathReturnCode, is_external=, is_static_class=False, static_prefix=
    private int _EqtpathReturnCode =0;
    
    
    
    
    
    // Serialization methods
    public string GetEqtpathLinkageAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_EqtpathPathEnvVariable.PadRight(0));
        result.Append(_EqtpathFileName.PadRight(256));
        result.Append(_EqtpathPathFileName.PadRight(256));
        result.Append(_EqtpathReturnCode.ToString().PadLeft(1, '0'));
        
        return result.ToString();
    }
    
    public void SetEqtpathLinkageAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetEqtpathPathEnvVariable(extracted);
        }
        offset += 0;
        if (offset + 256 <= data.Length)
        {
            string extracted = data.Substring(offset, 256).Trim();
            SetEqtpathFileName(extracted);
        }
        offset += 256;
        if (offset + 256 <= data.Length)
        {
            string extracted = data.Substring(offset, 256).Trim();
            SetEqtpathPathFileName(extracted);
        }
        offset += 256;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetEqtpathReturnCode(parsedInt);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetEqtpathLinkageAsString();
    }
    // Set<>String Override function
    public void SetEqtpathLinkage(string value)
    {
        SetEqtpathLinkageAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetEqtpathPathEnvVariable()
    {
        return _EqtpathPathEnvVariable;
    }
    
    // Standard Setter
    public void SetEqtpathPathEnvVariable(string value)
    {
        _EqtpathPathEnvVariable = value;
    }
    
    // Get<>AsString()
    public string GetEqtpathPathEnvVariableAsString()
    {
        return _EqtpathPathEnvVariable.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetEqtpathPathEnvVariableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EqtpathPathEnvVariable = value;
    }
    
    // Standard Getter
    public string GetEqtpathFileName()
    {
        return _EqtpathFileName;
    }
    
    // Standard Setter
    public void SetEqtpathFileName(string value)
    {
        _EqtpathFileName = value;
    }
    
    // Get<>AsString()
    public string GetEqtpathFileNameAsString()
    {
        return _EqtpathFileName.PadRight(256);
    }
    
    // Set<>AsString()
    public void SetEqtpathFileNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EqtpathFileName = value;
    }
    
    // Standard Getter
    public string GetEqtpathPathFileName()
    {
        return _EqtpathPathFileName;
    }
    
    // Standard Setter
    public void SetEqtpathPathFileName(string value)
    {
        _EqtpathPathFileName = value;
    }
    
    // Get<>AsString()
    public string GetEqtpathPathFileNameAsString()
    {
        return _EqtpathPathFileName.PadRight(256);
    }
    
    // Set<>AsString()
    public void SetEqtpathPathFileNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _EqtpathPathFileName = value;
    }
    
    // Standard Getter
    public int GetEqtpathReturnCode()
    {
        return _EqtpathReturnCode;
    }
    
    // Standard Setter
    public void SetEqtpathReturnCode(int value)
    {
        _EqtpathReturnCode = value;
    }
    
    // Get<>AsString()
    public string GetEqtpathReturnCodeAsString()
    {
        return _EqtpathReturnCode.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetEqtpathReturnCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _EqtpathReturnCode = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}