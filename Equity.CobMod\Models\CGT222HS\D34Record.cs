using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// DTO class representing D34Record Data Structure

public class D34Record
{
    private static int _size = 82;
    // [DEBUG] Class: D34Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D34Key, is_external=, is_static_class=False, static_prefix=
    private D34Key _D34Key = new D34Key();
    
    
    
    
    // [DEBUG] Field: D34DescriptionI, is_external=, is_static_class=False, static_prefix=
    private string _D34DescriptionI ="";
    
    
    
    
    // [DEBUG] Field: D34DescriptionC, is_external=, is_static_class=False, static_prefix=
    private string _D34DescriptionC ="";
    
    
    
    
    
    // Serialization methods
    public string GetD34RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D34Key.GetD34KeyAsString());
        result.Append(_D34DescriptionI.PadRight(40));
        result.Append(_D34DescriptionC.PadRight(40));
        
        return result.ToString();
    }
    
    public void SetD34RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            _D34Key.SetD34KeyAsString(data.Substring(offset, 2));
        }
        else
        {
            _D34Key.SetD34KeyAsString(data.Substring(offset));
        }
        offset += 2;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD34DescriptionI(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD34DescriptionC(extracted);
        }
        offset += 40;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD34RecordAsString();
    }
    // Set<>String Override function
    public void SetD34Record(string value)
    {
        SetD34RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D34Key GetD34Key()
    {
        return _D34Key;
    }
    
    // Standard Setter
    public void SetD34Key(D34Key value)
    {
        _D34Key = value;
    }
    
    // Get<>AsString()
    public string GetD34KeyAsString()
    {
        return _D34Key != null ? _D34Key.GetD34KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD34KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D34Key == null)
        {
            _D34Key = new D34Key();
        }
        _D34Key.SetD34KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD34DescriptionI()
    {
        return _D34DescriptionI;
    }
    
    // Standard Setter
    public void SetD34DescriptionI(string value)
    {
        _D34DescriptionI = value;
    }
    
    // Get<>AsString()
    public string GetD34DescriptionIAsString()
    {
        return _D34DescriptionI.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD34DescriptionIAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D34DescriptionI = value;
    }
    
    // Standard Getter
    public string GetD34DescriptionC()
    {
        return _D34DescriptionC;
    }
    
    // Standard Setter
    public void SetD34DescriptionC(string value)
    {
        _D34DescriptionC = value;
    }
    
    // Get<>AsString()
    public string GetD34DescriptionCAsString()
    {
        return _D34DescriptionC.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD34DescriptionCAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D34DescriptionC = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD34Key(string value)
    {
        _D34Key.SetD34KeyAsString(value);
    }
    // Nested Class: D34Key
    public class D34Key
    {
        private static int _size = 2;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D34TransactionCode, is_external=, is_static_class=False, static_prefix=
        private string _D34TransactionCode ="";
        
        
        
        
    public D34Key() {}
    
    public D34Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD34TransactionCode(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetD34KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D34TransactionCode.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetD34KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD34TransactionCode(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD34TransactionCode()
    {
        return _D34TransactionCode;
    }
    
    // Standard Setter
    public void SetD34TransactionCode(string value)
    {
        _D34TransactionCode = value;
    }
    
    // Get<>AsString()
    public string GetD34TransactionCodeAsString()
    {
        return _D34TransactionCode.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD34TransactionCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D34TransactionCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}