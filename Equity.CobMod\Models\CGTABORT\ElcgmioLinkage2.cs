using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtabortDTO
{// DTO class representing ElcgmioLinkage2 Data Structure

public class ElcgmioLinkage2
{
    private static int _size = 548;
    // [DEBUG] Class: ElcgmioLinkage2, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: LRecordArea, is_external=, is_static_class=False, static_prefix=
    private LRecordArea _LRecordArea = new LRecordArea();
    
    
    
    
    // [DEBUG] Field: Filler27, is_external=, is_static_class=False, static_prefix=
    private Filler27 _Filler27 = new Filler27();
    
    
    
    
    
    // Serialization methods
    public string GetElcgmioLinkage2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LRecordArea.GetLRecordAreaAsString());
        result.Append(_Filler27.GetFiller27AsString());
        
        return result.ToString();
    }
    
    public void SetElcgmioLinkage2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 270 <= data.Length)
        {
            _LRecordArea.SetLRecordAreaAsString(data.Substring(offset, 270));
        }
        else
        {
            _LRecordArea.SetLRecordAreaAsString(data.Substring(offset));
        }
        offset += 270;
        if (offset + 278 <= data.Length)
        {
            _Filler27.SetFiller27AsString(data.Substring(offset, 278));
        }
        else
        {
            _Filler27.SetFiller27AsString(data.Substring(offset));
        }
        offset += 278;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetElcgmioLinkage2AsString();
    }
    // Set<>String Override function
    public void SetElcgmioLinkage2(string value)
    {
        SetElcgmioLinkage2AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public LRecordArea GetLRecordArea()
    {
        return _LRecordArea;
    }
    
    // Standard Setter
    public void SetLRecordArea(LRecordArea value)
    {
        _LRecordArea = value;
    }
    
    // Get<>AsString()
    public string GetLRecordAreaAsString()
    {
        return _LRecordArea != null ? _LRecordArea.GetLRecordAreaAsString() : "";
    }
    
    // Set<>AsString()
    public void SetLRecordAreaAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_LRecordArea == null)
        {
            _LRecordArea = new LRecordArea();
        }
        _LRecordArea.SetLRecordAreaAsString(value);
    }
    
    // Standard Getter
    public Filler27 GetFiller27()
    {
        return _Filler27;
    }
    
    // Standard Setter
    public void SetFiller27(Filler27 value)
    {
        _Filler27 = value;
    }
    
    // Get<>AsString()
    public string GetFiller27AsString()
    {
        return _Filler27 != null ? _Filler27.GetFiller27AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller27AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler27 == null)
        {
            _Filler27 = new Filler27();
        }
        _Filler27.SetFiller27AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetLRecordArea(string value)
    {
        _LRecordArea.SetLRecordAreaAsString(value);
    }
    // Nested Class: LRecordArea
    public class LRecordArea
    {
        private static int _size = 270;
        
        // Fields in the class
        
        
        // [DEBUG] Field: FixedPortion, is_external=, is_static_class=False, static_prefix=
        private string _FixedPortion ="";
        
        
        
        
        // [DEBUG] Field: Filler26, is_external=, is_static_class=False, static_prefix=
        private string _Filler26 ="";
        
        
        
        
        // [DEBUG] Field: BalanceCosts, is_external=, is_static_class=False, static_prefix=
        private string[] _BalanceCosts = new string[200];
        
        
        
        
    public LRecordArea() {}
    
    public LRecordArea(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFixedPortion(data.Substring(offset, 270).Trim());
        offset += 270;
        SetFiller26(data.Substring(offset, 0).Trim());
        offset += 0;
        for (int i = 0; i < 200; i++)
        {
            string value = data.Substring(offset, 0);
            _BalanceCosts[i] = value.Trim();
            offset += 0;
        }
        
    }
    
    // Serialization methods
    public string GetLRecordAreaAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_FixedPortion.PadRight(270));
        result.Append(_Filler26.PadRight(0));
        for (int i = 0; i < 200; i++)
        {
            result.Append(_BalanceCosts[i].PadRight(0));
        }
        
        return result.ToString();
    }
    
    public void SetLRecordAreaAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 270 <= data.Length)
        {
            string extracted = data.Substring(offset, 270).Trim();
            SetFixedPortion(extracted);
        }
        offset += 270;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller26(extracted);
        }
        offset += 0;
        for (int i = 0; i < 200; i++)
        {
            if (offset + 0 > data.Length) break;
            string val = data.Substring(offset, 0);
            
            _BalanceCosts[i] = val.Trim();
            offset += 0;
        }
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFixedPortion()
    {
        return _FixedPortion;
    }
    
    // Standard Setter
    public void SetFixedPortion(string value)
    {
        _FixedPortion = value;
    }
    
    // Get<>AsString()
    public string GetFixedPortionAsString()
    {
        return _FixedPortion.PadRight(270);
    }
    
    // Set<>AsString()
    public void SetFixedPortionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _FixedPortion = value;
    }
    
    // Standard Getter
    public string GetFiller26()
    {
        return _Filler26;
    }
    
    // Standard Setter
    public void SetFiller26(string value)
    {
        _Filler26 = value;
    }
    
    // Get<>AsString()
    public string GetFiller26AsString()
    {
        return _Filler26.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller26AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler26 = value;
    }
    
    // Array Accessors for BalanceCosts
    public string GetBalanceCostsAt(int index)
    {
        return _BalanceCosts[index];
    }
    
    public void SetBalanceCostsAt(int index, string value)
    {
        _BalanceCosts[index] = value;
    }
    
    public string GetBalanceCostsAsStringAt(int index)
    {
        return _BalanceCosts[index].PadRight(0);
    }
    
    public void SetBalanceCostsAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _BalanceCosts[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetBalanceCosts()
    {
        return _BalanceCosts != null && _BalanceCosts.Length > 0
        ? _BalanceCosts[0]
        : default(string);
    }
    
    public void SetBalanceCosts(string value)
    {
        if (_BalanceCosts == null || _BalanceCosts.Length == 0)
        _BalanceCosts = new string[1];
        _BalanceCosts[0] = value;
    }
    
    public string GetBalanceCostsAsString()
    {
        return _BalanceCosts != null && _BalanceCosts.Length > 0
        ? _BalanceCosts[0].ToString()
        : string.Empty;
    }
    
    public void SetBalanceCostsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_BalanceCosts == null || _BalanceCosts.Length == 0)
        _BalanceCosts = new string[1];
        
        _BalanceCosts[0] = value;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetFiller27(string value)
{
    _Filler27.SetFiller27AsString(value);
}
// Nested Class: Filler27
public class Filler27
{
    private static int _size = 278;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler28, is_external=, is_static_class=False, static_prefix=
    private string _Filler28 ="";
    
    
    
    
    // [DEBUG] Field: LFnUserNo, is_external=, is_static_class=False, static_prefix=
    private string _LFnUserNo ="";
    
    
    
    
    // [DEBUG] Field: LFnGenerationNo, is_external=, is_static_class=False, static_prefix=
    private string _LFnGenerationNo ="";
    
    
    
    
    // [DEBUG] Field: LFnYy, is_external=, is_static_class=False, static_prefix=
    private int _LFnYy =0;
    
    
    
    
    // [DEBUG] Field: Filler29, is_external=, is_static_class=False, static_prefix=
    private Filler27.Filler29 _Filler29 = new Filler27.Filler29();
    
    
    
    
public Filler27() {}

public Filler27(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller28(data.Substring(offset, 1).Trim());
    offset += 1;
    SetLFnUserNo(data.Substring(offset, 4).Trim());
    offset += 4;
    SetLFnGenerationNo(data.Substring(offset, 1).Trim());
    offset += 1;
    SetLFnYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    _Filler29.SetFiller29AsString(data.Substring(offset, Filler29.GetSize()));
    offset += 270;
    
}

// Serialization methods
public string GetFiller27AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler28.PadRight(1));
    result.Append(_LFnUserNo.PadRight(4));
    result.Append(_LFnGenerationNo.PadRight(1));
    result.Append(_LFnYy.ToString().PadLeft(2, '0'));
    result.Append(_Filler29.GetFiller29AsString());
    
    return result.ToString();
}

public void SetFiller27AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller28(extracted);
    }
    offset += 1;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetLFnUserNo(extracted);
    }
    offset += 4;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetLFnGenerationNo(extracted);
    }
    offset += 1;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetLFnYy(parsedInt);
    }
    offset += 2;
    if (offset + 270 <= data.Length)
    {
        _Filler29.SetFiller29AsString(data.Substring(offset, 270));
    }
    else
    {
        _Filler29.SetFiller29AsString(data.Substring(offset));
    }
    offset += 270;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller28()
{
    return _Filler28;
}

// Standard Setter
public void SetFiller28(string value)
{
    _Filler28 = value;
}

// Get<>AsString()
public string GetFiller28AsString()
{
    return _Filler28.PadRight(1);
}

// Set<>AsString()
public void SetFiller28AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler28 = value;
}

// Standard Getter
public string GetLFnUserNo()
{
    return _LFnUserNo;
}

// Standard Setter
public void SetLFnUserNo(string value)
{
    _LFnUserNo = value;
}

// Get<>AsString()
public string GetLFnUserNoAsString()
{
    return _LFnUserNo.PadRight(4);
}

// Set<>AsString()
public void SetLFnUserNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LFnUserNo = value;
}

// Standard Getter
public string GetLFnGenerationNo()
{
    return _LFnGenerationNo;
}

// Standard Setter
public void SetLFnGenerationNo(string value)
{
    _LFnGenerationNo = value;
}

// Get<>AsString()
public string GetLFnGenerationNoAsString()
{
    return _LFnGenerationNo.PadRight(1);
}

// Set<>AsString()
public void SetLFnGenerationNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LFnGenerationNo = value;
}

// Standard Getter
public int GetLFnYy()
{
    return _LFnYy;
}

// Standard Setter
public void SetLFnYy(int value)
{
    _LFnYy = value;
}

// Get<>AsString()
public string GetLFnYyAsString()
{
    return _LFnYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetLFnYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _LFnYy = parsed;
}

// Standard Getter
public Filler29 GetFiller29()
{
    return _Filler29;
}

// Standard Setter
public void SetFiller29(Filler29 value)
{
    _Filler29 = value;
}

// Get<>AsString()
public string GetFiller29AsString()
{
    return _Filler29 != null ? _Filler29.GetFiller29AsString() : "";
}

// Set<>AsString()
public void SetFiller29AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler29 == null)
    {
        _Filler29 = new Filler29();
    }
    _Filler29.SetFiller29AsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: Filler29
public class Filler29
{
    private static int _size = 270;
    
    // Fields in the class
    
    
    // [DEBUG] Field: FixedPortion, is_external=, is_static_class=False, static_prefix=
    private string _FixedPortion ="";
    
    
    
    
    // [DEBUG] Field: Filler30, is_external=, is_static_class=False, static_prefix=
    private string _Filler30 ="";
    
    
    
    
    // [DEBUG] Field: BalanceCosts, is_external=, is_static_class=False, static_prefix=
    private string[] _BalanceCosts = new string[200];
    
    
    
    
public Filler29() {}

public Filler29(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFixedPortion(data.Substring(offset, 270).Trim());
    offset += 270;
    SetFiller30(data.Substring(offset, 0).Trim());
    offset += 0;
    for (int i = 0; i < 200; i++)
    {
        string value = data.Substring(offset, 0);
        _BalanceCosts[i] = value.Trim();
        offset += 0;
    }
    
}

// Serialization methods
public string GetFiller29AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_FixedPortion.PadRight(270));
    result.Append(_Filler30.PadRight(0));
    for (int i = 0; i < 200; i++)
    {
        result.Append(_BalanceCosts[i].PadRight(0));
    }
    
    return result.ToString();
}

public void SetFiller29AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 270 <= data.Length)
    {
        string extracted = data.Substring(offset, 270).Trim();
        SetFixedPortion(extracted);
    }
    offset += 270;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller30(extracted);
    }
    offset += 0;
    for (int i = 0; i < 200; i++)
    {
        if (offset + 0 > data.Length) break;
        string val = data.Substring(offset, 0);
        
        _BalanceCosts[i] = val.Trim();
        offset += 0;
    }
}

// Getter and Setter methods

// Standard Getter
public string GetFixedPortion()
{
    return _FixedPortion;
}

// Standard Setter
public void SetFixedPortion(string value)
{
    _FixedPortion = value;
}

// Get<>AsString()
public string GetFixedPortionAsString()
{
    return _FixedPortion.PadRight(270);
}

// Set<>AsString()
public void SetFixedPortionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _FixedPortion = value;
}

// Standard Getter
public string GetFiller30()
{
    return _Filler30;
}

// Standard Setter
public void SetFiller30(string value)
{
    _Filler30 = value;
}

// Get<>AsString()
public string GetFiller30AsString()
{
    return _Filler30.PadRight(0);
}

// Set<>AsString()
public void SetFiller30AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler30 = value;
}

// Array Accessors for BalanceCosts
public string GetBalanceCostsAt(int index)
{
    return _BalanceCosts[index];
}

public void SetBalanceCostsAt(int index, string value)
{
    _BalanceCosts[index] = value;
}

public string GetBalanceCostsAsStringAt(int index)
{
    return _BalanceCosts[index].PadRight(0);
}

public void SetBalanceCostsAsStringAt(int index, string value)
{
    if (string.IsNullOrEmpty(value)) return;
    _BalanceCosts[index] = value;
}

// Flattened accessors (index 0)
public string GetBalanceCosts()
{
    return _BalanceCosts != null && _BalanceCosts.Length > 0
    ? _BalanceCosts[0]
    : default(string);
}

public void SetBalanceCosts(string value)
{
    if (_BalanceCosts == null || _BalanceCosts.Length == 0)
    _BalanceCosts = new string[1];
    _BalanceCosts[0] = value;
}

public string GetBalanceCostsAsString()
{
    return _BalanceCosts != null && _BalanceCosts.Length > 0
    ? _BalanceCosts[0].ToString()
    : string.Empty;
}

public void SetBalanceCostsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    if (_BalanceCosts == null || _BalanceCosts.Length == 0)
    _BalanceCosts = new string[1];
    
    _BalanceCosts[0] = value;
}




public static int GetSize()
{
    return _size;
}

}
}

}}
