﻿using System;
using System.Collections.Generic;
using System.Text;
using Legacy4.Equity.CobMod.Models;
using WK.UK.CCH.Equity.CobolDataTransformation;

namespace Legacy4.Equity.CobMod.FIleHandlers
{
    /// <summary>
    /// This class mimics the COBOL main program (PriceDAL).
    /// It processes the linkage structure and performs one of three actions:
    /// - "I": Initialise the PriceDA object.
    /// - "P": Retrieve the price using PriceDA and extract output values.
    /// - "T": Terminate (no action is performed).
    /// </summary>
    public class PriceDAL
    {
        // Holds the persistent instance of PriceDA (like the COBOL working storage pointer oPriceDA)
        private static PriceDA _priceDA;

        /// <summary>
        /// Process the linkage parameters according to the action code.
        /// </summary>
        /// <param name="linkage">The PriceLinkage structure containing input and output values.</param>
        public static void Process(CgtPriceLinkage linkage)
        {
            if (linkage == null)
                throw new ArgumentNullException(nameof(linkage));

            // Evaluate the action (similar to the COBOL EVALUATE statement)
            switch (linkage.ParametersIn.Action)
            {
                case "I": // CGTPRICE-ACTION-INITIALISE
                    // Create a new PriceDA object
                    _priceDA = new PriceDA();
                    break;

                case "P": // CGTPRICE-ACTION-GET-PRICE
                    if (_priceDA == null)
                    {
                        // If not initialised, create a new PriceDA instance (mimicking COBOL behaviour)
                        _priceDA = new PriceDA();
                    }

                    // Call the method that converts string parameters to the proper types,
                    // applies business logic and returns a concatenated result.
                    string priceString = _priceDA.GetPriceFromCobol(
                        linkage.ParametersIn.StockId,
                        linkage.ParametersIn.PriceTypeId,
                        linkage.ParametersIn.PriceDate,
                        linkage.ParametersIn.UseEarlierPrice);

                    // The COBOL code takes the first 15 characters as the market price
                    // and the 16th character as the return code.
                    if (priceString.Length >= 16)
                    {
                        linkage.ParametersOut.SetMarketPrice(priceString.Substring(0,15));
                        linkage.ParametersOut.ReturnCode = priceString.Substring(15, 1);
                    }
                    else
                    {
                        // In case the returned string is shorter than expected,
                        // assign the full string to MarketPrice and leave ReturnCode empty.
                        linkage.ParametersOut.MarketPrice = decimal.Parse(priceString);
                        linkage.ParametersOut.ReturnCode = string.Empty;
                    }
                    break;

                case "T": // CGTPRICE-ACTION-TERMINATE
                    // Termination: no specific action required (mimics the COBOL "continue")
                    break;

                default:
                    throw new ArgumentException("Invalid action code in linkage.", nameof(linkage.ParametersIn.Action));
            }
        }
    }
}
