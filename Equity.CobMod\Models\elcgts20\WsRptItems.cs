using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WsRptItems Data Structure

public class WsRptItems
{
    private static int _size = 1787;
    // [DEBUG] Class: WsRptItems, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsOffshHd1, is_external=, is_static_class=False, static_prefix=
    private WsOffshHd1 _WsOffshHd1 = new WsOffshHd1();
    
    
    
    
    // [DEBUG] Field: WsOffshHd1Dd, is_external=, is_static_class=False, static_prefix=
    private WsOffshHd1Dd _WsOffshHd1Dd = new WsOffshHd1Dd();
    
    
    
    
    // [DEBUG] Field: WsOffshHd2, is_external=, is_static_class=False, static_prefix=
    private WsOffshHd2 _WsOffshHd2 = new WsOffshHd2();
    
    
    
    
    // [DEBUG] Field: WsOffshHd3, is_external=, is_static_class=False, static_prefix=
    private WsOffshHd3 _WsOffshHd3 = new WsOffshHd3();
    
    
    
    
    // [DEBUG] Field: WsOffshHd4, is_external=, is_static_class=False, static_prefix=
    private WsOffshHd4 _WsOffshHd4 = new WsOffshHd4();
    
    
    
    
    // [DEBUG] Field: WsOffshHd5, is_external=, is_static_class=False, static_prefix=
    private WsOffshHd5 _WsOffshHd5 = new WsOffshHd5();
    
    
    
    
    // [DEBUG] Field: WsOffshDtl, is_external=, is_static_class=False, static_prefix=
    private WsOffshDtl _WsOffshDtl = new WsOffshDtl();
    
    
    
    
    // [DEBUG] Field: WsOffshTotLine, is_external=, is_static_class=False, static_prefix=
    private WsOffshTotLine _WsOffshTotLine = new WsOffshTotLine();
    
    
    
    
    // [DEBUG] Field: WsGrandTotText, is_external=, is_static_class=False, static_prefix=
    private string _WsGrandTotText ="SCHEDULE D CASE VI. INCOME";
    
    
    
    
    // [DEBUG] Field: WsFundTotText, is_external=, is_static_class=False, static_prefix=
    private string _WsFundTotText ="FUND TOTAL                ";
    
    
    
    
    // [DEBUG] Field: WsOldFund, is_external=, is_static_class=False, static_prefix=
    private string _WsOldFund ="";
    
    
    
    
    // [DEBUG] Field: WsOldStock, is_external=, is_static_class=False, static_prefix=
    private string _WsOldStock ="";
    
    
    
    
    // [DEBUG] Field: WsOldCompFund, is_external=, is_static_class=False, static_prefix=
    private string _WsOldCompFund ="";
    
    
    
    
    // [DEBUG] Field: WsOldFromDate, is_external=, is_static_class=False, static_prefix=
    private int _WsOldFromDate =0;
    
    
    
    
    // [DEBUG] Field: WsOldToDate, is_external=, is_static_class=False, static_prefix=
    private int _WsOldToDate =0;
    
    
    
    
    // [DEBUG] Field: WsLineCount, is_external=, is_static_class=False, static_prefix=
    private int _WsLineCount =0;
    
    
    
    
    // [DEBUG] Field: WsPageCount, is_external=, is_static_class=False, static_prefix=
    private int _WsPageCount =0;
    
    
    
    
    // [DEBUG] Field: WsNegOffshHoldings, is_external=, is_static_class=False, static_prefix=
    private decimal _WsNegOffshHoldings =0;
    
    
    
    
    // [DEBUG] Field: WsNegOffshCgt, is_external=, is_static_class=False, static_prefix=
    private decimal _WsNegOffshCgt =0;
    
    
    
    
    // [DEBUG] Field: WsFundCgtTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsFundCgtTot =0;
    
    
    
    
    // [DEBUG] Field: WsFundProceedsTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsFundProceedsTot =0;
    
    
    
    
    // [DEBUG] Field: WsFundSchedDTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsFundSchedDTot =0;
    
    
    
    
    // [DEBUG] Field: WsGrandCgtTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsGrandCgtTot =0;
    
    
    
    
    // [DEBUG] Field: WsGrandProceedsTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsGrandProceedsTot =0;
    
    
    
    
    // [DEBUG] Field: WsGrandSchedDTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsGrandSchedDTot =0;
    
    
    
    
    // [DEBUG] Field: WsFirstTime, is_external=, is_static_class=False, static_prefix=
    private string _WsFirstTime ="Y";
    
    
    // 88-level condition checks for WsFirstTime
    public bool IsFirstTime()
    {
        if (this._WsFirstTime == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsRecordWritten, is_external=, is_static_class=False, static_prefix=
    private string _WsRecordWritten ="Y";
    
    
    // 88-level condition checks for WsRecordWritten
    public bool IsRecordWritten()
    {
        if (this._WsRecordWritten == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsOldFundDd, is_external=, is_static_class=False, static_prefix=
    private string _WsOldFundDd ="";
    
    
    
    
    // [DEBUG] Field: WsOldStockDd, is_external=, is_static_class=False, static_prefix=
    private string _WsOldStockDd ="";
    
    
    
    
    // [DEBUG] Field: WsOldCompFundDd, is_external=, is_static_class=False, static_prefix=
    private string _WsOldCompFundDd ="";
    
    
    
    
    // [DEBUG] Field: WsOldFromDateDd, is_external=, is_static_class=False, static_prefix=
    private int _WsOldFromDateDd =0;
    
    
    
    
    // [DEBUG] Field: WsOldToDateDd, is_external=, is_static_class=False, static_prefix=
    private int _WsOldToDateDd =0;
    
    
    
    
    // [DEBUG] Field: WsLineCountDd, is_external=, is_static_class=False, static_prefix=
    private int _WsLineCountDd =0;
    
    
    
    
    // [DEBUG] Field: WsPageCountDd, is_external=, is_static_class=False, static_prefix=
    private int _WsPageCountDd =0;
    
    
    
    
    // [DEBUG] Field: WsNegOffshHoldingsDd, is_external=, is_static_class=False, static_prefix=
    private decimal _WsNegOffshHoldingsDd =0;
    
    
    
    
    // [DEBUG] Field: WsNegOffshCgtDd, is_external=, is_static_class=False, static_prefix=
    private decimal _WsNegOffshCgtDd =0;
    
    
    
    
    // [DEBUG] Field: WsFundCgtTotDd, is_external=, is_static_class=False, static_prefix=
    private decimal _WsFundCgtTotDd =0;
    
    
    
    
    // [DEBUG] Field: WsFundProceedsTotDd, is_external=, is_static_class=False, static_prefix=
    private decimal _WsFundProceedsTotDd =0;
    
    
    
    
    // [DEBUG] Field: WsFundSchedDTotDd, is_external=, is_static_class=False, static_prefix=
    private decimal _WsFundSchedDTotDd =0;
    
    
    
    
    // [DEBUG] Field: WsGrandCgtTotDd, is_external=, is_static_class=False, static_prefix=
    private decimal _WsGrandCgtTotDd =0;
    
    
    
    
    // [DEBUG] Field: WsGrandProceedsTotDd, is_external=, is_static_class=False, static_prefix=
    private decimal _WsGrandProceedsTotDd =0;
    
    
    
    
    // [DEBUG] Field: WsGrandSchedDTotDd, is_external=, is_static_class=False, static_prefix=
    private decimal _WsGrandSchedDTotDd =0;
    
    
    
    
    // [DEBUG] Field: WsFirstTimeDd, is_external=, is_static_class=False, static_prefix=
    private string _WsFirstTimeDd ="Y";
    
    
    // 88-level condition checks for WsFirstTimeDd
    public bool IsFirstTimeDd()
    {
        if (this._WsFirstTimeDd == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsRecordWrittenDd, is_external=, is_static_class=False, static_prefix=
    private string _WsRecordWrittenDd ="Y";
    
    
    // 88-level condition checks for WsRecordWrittenDd
    public bool IsRecordWrittenDd()
    {
        if (this._WsRecordWrittenDd == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsConvSedol, is_external=, is_static_class=False, static_prefix=
    private WsConvSedol _WsConvSedol = new WsConvSedol();
    
    
    
    
    // [DEBUG] Field: WsDateConv, is_external=, is_static_class=False, static_prefix=
    private WsDateConv _WsDateConv = new WsDateConv();
    
    
    
    
    // [DEBUG] Field: WsDateOut, is_external=, is_static_class=False, static_prefix=
    private WsDateOut _WsDateOut = new WsDateOut();
    
    
    
    
    // [DEBUG] Field: MonthTable, is_external=, is_static_class=False, static_prefix=
    private MonthTable _MonthTable = new MonthTable();
    
    
    
    
    // [DEBUG] Field: MonthTab, is_external=, is_static_class=False, static_prefix=
    private MonthTab _MonthTab = new MonthTab();
    
    
    
    
    
    // Serialization methods
    public string GetWsRptItemsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsOffshHd1.GetWsOffshHd1AsString());
        result.Append(_WsOffshHd1Dd.GetWsOffshHd1DdAsString());
        result.Append(_WsOffshHd2.GetWsOffshHd2AsString());
        result.Append(_WsOffshHd3.GetWsOffshHd3AsString());
        result.Append(_WsOffshHd4.GetWsOffshHd4AsString());
        result.Append(_WsOffshHd5.GetWsOffshHd5AsString());
        result.Append(_WsOffshDtl.GetWsOffshDtlAsString());
        result.Append(_WsOffshTotLine.GetWsOffshTotLineAsString());
        result.Append(_WsGrandTotText.PadRight(26));
        result.Append(_WsFundTotText.PadRight(26));
        result.Append(_WsOldFund.PadRight(4));
        result.Append(_WsOldStock.PadRight(7));
        result.Append(_WsOldCompFund.PadRight(60));
        result.Append(_WsOldFromDate.ToString().PadLeft(6, '0'));
        result.Append(_WsOldToDate.ToString().PadLeft(6, '0'));
        result.Append(_WsLineCount.ToString().PadLeft(4, '0'));
        result.Append(_WsPageCount.ToString().PadLeft(4, '0'));
        result.Append(_WsNegOffshHoldings.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsNegOffshCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsFundCgtTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsFundProceedsTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsFundSchedDTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsGrandCgtTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsGrandProceedsTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsGrandSchedDTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsFirstTime.PadRight(1));
        result.Append(_WsRecordWritten.PadRight(1));
        result.Append(_WsOldFundDd.PadRight(4));
        result.Append(_WsOldStockDd.PadRight(7));
        result.Append(_WsOldCompFundDd.PadRight(60));
        result.Append(_WsOldFromDateDd.ToString().PadLeft(6, '0'));
        result.Append(_WsOldToDateDd.ToString().PadLeft(6, '0'));
        result.Append(_WsLineCountDd.ToString().PadLeft(4, '0'));
        result.Append(_WsPageCountDd.ToString().PadLeft(4, '0'));
        result.Append(_WsNegOffshHoldingsDd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsNegOffshCgtDd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsFundCgtTotDd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsFundProceedsTotDd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsFundSchedDTotDd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsGrandCgtTotDd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsGrandProceedsTotDd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsGrandSchedDTotDd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsFirstTimeDd.PadRight(1));
        result.Append(_WsRecordWrittenDd.PadRight(1));
        result.Append(_WsConvSedol.GetWsConvSedolAsString());
        result.Append(_WsDateConv.GetWsDateConvAsString());
        result.Append(_WsDateOut.GetWsDateOutAsString());
        result.Append(_MonthTable.GetMonthTableAsString());
        result.Append(_MonthTab.GetMonthTabAsString());
        
        return result.ToString();
    }
    
    public void SetWsRptItemsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 160 <= data.Length)
        {
            _WsOffshHd1.SetWsOffshHd1AsString(data.Substring(offset, 160));
        }
        else
        {
            _WsOffshHd1.SetWsOffshHd1AsString(data.Substring(offset));
        }
        offset += 160;
        if (offset + 160 <= data.Length)
        {
            _WsOffshHd1Dd.SetWsOffshHd1DdAsString(data.Substring(offset, 160));
        }
        else
        {
            _WsOffshHd1Dd.SetWsOffshHd1DdAsString(data.Substring(offset));
        }
        offset += 160;
        if (offset + 163 <= data.Length)
        {
            _WsOffshHd2.SetWsOffshHd2AsString(data.Substring(offset, 163));
        }
        else
        {
            _WsOffshHd2.SetWsOffshHd2AsString(data.Substring(offset));
        }
        offset += 163;
        if (offset + 163 <= data.Length)
        {
            _WsOffshHd3.SetWsOffshHd3AsString(data.Substring(offset, 163));
        }
        else
        {
            _WsOffshHd3.SetWsOffshHd3AsString(data.Substring(offset));
        }
        offset += 163;
        if (offset + 163 <= data.Length)
        {
            _WsOffshHd4.SetWsOffshHd4AsString(data.Substring(offset, 163));
        }
        else
        {
            _WsOffshHd4.SetWsOffshHd4AsString(data.Substring(offset));
        }
        offset += 163;
        if (offset + 163 <= data.Length)
        {
            _WsOffshHd5.SetWsOffshHd5AsString(data.Substring(offset, 163));
        }
        else
        {
            _WsOffshHd5.SetWsOffshHd5AsString(data.Substring(offset));
        }
        offset += 163;
        if (offset + 147 <= data.Length)
        {
            _WsOffshDtl.SetWsOffshDtlAsString(data.Substring(offset, 147));
        }
        else
        {
            _WsOffshDtl.SetWsOffshDtlAsString(data.Substring(offset));
        }
        offset += 147;
        if (offset + 151 <= data.Length)
        {
            _WsOffshTotLine.SetWsOffshTotLineAsString(data.Substring(offset, 151));
        }
        else
        {
            _WsOffshTotLine.SetWsOffshTotLineAsString(data.Substring(offset));
        }
        offset += 151;
        if (offset + 26 <= data.Length)
        {
            string extracted = data.Substring(offset, 26).Trim();
            SetWsGrandTotText(extracted);
        }
        offset += 26;
        if (offset + 26 <= data.Length)
        {
            string extracted = data.Substring(offset, 26).Trim();
            SetWsFundTotText(extracted);
        }
        offset += 26;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWsOldFund(extracted);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetWsOldStock(extracted);
        }
        offset += 7;
        if (offset + 60 <= data.Length)
        {
            string extracted = data.Substring(offset, 60).Trim();
            SetWsOldCompFund(extracted);
        }
        offset += 60;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsOldFromDate(parsedInt);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsOldToDate(parsedInt);
        }
        offset += 6;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsLineCount(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsPageCount(parsedInt);
        }
        offset += 4;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsNegOffshHoldings(parsedDec);
        }
        offset += 13;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsNegOffshCgt(parsedDec);
        }
        offset += 13;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsFundCgtTot(parsedDec);
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsFundProceedsTot(parsedDec);
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsFundSchedDTot(parsedDec);
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsGrandCgtTot(parsedDec);
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsGrandProceedsTot(parsedDec);
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsGrandSchedDTot(parsedDec);
        }
        offset += 14;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsFirstTime(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsRecordWritten(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWsOldFundDd(extracted);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetWsOldStockDd(extracted);
        }
        offset += 7;
        if (offset + 60 <= data.Length)
        {
            string extracted = data.Substring(offset, 60).Trim();
            SetWsOldCompFundDd(extracted);
        }
        offset += 60;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsOldFromDateDd(parsedInt);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsOldToDateDd(parsedInt);
        }
        offset += 6;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsLineCountDd(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsPageCountDd(parsedInt);
        }
        offset += 4;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsNegOffshHoldingsDd(parsedDec);
        }
        offset += 13;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsNegOffshCgtDd(parsedDec);
        }
        offset += 13;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsFundCgtTotDd(parsedDec);
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsFundProceedsTotDd(parsedDec);
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsFundSchedDTotDd(parsedDec);
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsGrandCgtTotDd(parsedDec);
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsGrandProceedsTotDd(parsedDec);
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsGrandSchedDTotDd(parsedDec);
        }
        offset += 14;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsFirstTimeDd(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsRecordWrittenDd(extracted);
        }
        offset += 1;
        if (offset + 7 <= data.Length)
        {
            _WsConvSedol.SetWsConvSedolAsString(data.Substring(offset, 7));
        }
        else
        {
            _WsConvSedol.SetWsConvSedolAsString(data.Substring(offset));
        }
        offset += 7;
        if (offset + 6 <= data.Length)
        {
            _WsDateConv.SetWsDateConvAsString(data.Substring(offset, 6));
        }
        else
        {
            _WsDateConv.SetWsDateConvAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 7 <= data.Length)
        {
            _WsDateOut.SetWsDateOutAsString(data.Substring(offset, 7));
        }
        else
        {
            _WsDateOut.SetWsDateOutAsString(data.Substring(offset));
        }
        offset += 7;
        if (offset + 36 <= data.Length)
        {
            _MonthTable.SetMonthTableAsString(data.Substring(offset, 36));
        }
        else
        {
            _MonthTable.SetMonthTableAsString(data.Substring(offset));
        }
        offset += 36;
        if (offset + 3 <= data.Length)
        {
            _MonthTab.SetMonthTabAsString(data.Substring(offset, 3));
        }
        else
        {
            _MonthTab.SetMonthTabAsString(data.Substring(offset));
        }
        offset += 3;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsRptItemsAsString();
    }
    // Set<>String Override function
    public void SetWsRptItems(string value)
    {
        SetWsRptItemsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public WsOffshHd1 GetWsOffshHd1()
    {
        return _WsOffshHd1;
    }
    
    // Standard Setter
    public void SetWsOffshHd1(WsOffshHd1 value)
    {
        _WsOffshHd1 = value;
    }
    
    // Get<>AsString()
    public string GetWsOffshHd1AsString()
    {
        return _WsOffshHd1 != null ? _WsOffshHd1.GetWsOffshHd1AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsOffshHd1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsOffshHd1 == null)
        {
            _WsOffshHd1 = new WsOffshHd1();
        }
        _WsOffshHd1.SetWsOffshHd1AsString(value);
    }
    
    // Standard Getter
    public WsOffshHd1Dd GetWsOffshHd1Dd()
    {
        return _WsOffshHd1Dd;
    }
    
    // Standard Setter
    public void SetWsOffshHd1Dd(WsOffshHd1Dd value)
    {
        _WsOffshHd1Dd = value;
    }
    
    // Get<>AsString()
    public string GetWsOffshHd1DdAsString()
    {
        return _WsOffshHd1Dd != null ? _WsOffshHd1Dd.GetWsOffshHd1DdAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsOffshHd1DdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsOffshHd1Dd == null)
        {
            _WsOffshHd1Dd = new WsOffshHd1Dd();
        }
        _WsOffshHd1Dd.SetWsOffshHd1DdAsString(value);
    }
    
    // Standard Getter
    public WsOffshHd2 GetWsOffshHd2()
    {
        return _WsOffshHd2;
    }
    
    // Standard Setter
    public void SetWsOffshHd2(WsOffshHd2 value)
    {
        _WsOffshHd2 = value;
    }
    
    // Get<>AsString()
    public string GetWsOffshHd2AsString()
    {
        return _WsOffshHd2 != null ? _WsOffshHd2.GetWsOffshHd2AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsOffshHd2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsOffshHd2 == null)
        {
            _WsOffshHd2 = new WsOffshHd2();
        }
        _WsOffshHd2.SetWsOffshHd2AsString(value);
    }
    
    // Standard Getter
    public WsOffshHd3 GetWsOffshHd3()
    {
        return _WsOffshHd3;
    }
    
    // Standard Setter
    public void SetWsOffshHd3(WsOffshHd3 value)
    {
        _WsOffshHd3 = value;
    }
    
    // Get<>AsString()
    public string GetWsOffshHd3AsString()
    {
        return _WsOffshHd3 != null ? _WsOffshHd3.GetWsOffshHd3AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsOffshHd3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsOffshHd3 == null)
        {
            _WsOffshHd3 = new WsOffshHd3();
        }
        _WsOffshHd3.SetWsOffshHd3AsString(value);
    }
    
    // Standard Getter
    public WsOffshHd4 GetWsOffshHd4()
    {
        return _WsOffshHd4;
    }
    
    // Standard Setter
    public void SetWsOffshHd4(WsOffshHd4 value)
    {
        _WsOffshHd4 = value;
    }
    
    // Get<>AsString()
    public string GetWsOffshHd4AsString()
    {
        return _WsOffshHd4 != null ? _WsOffshHd4.GetWsOffshHd4AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsOffshHd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsOffshHd4 == null)
        {
            _WsOffshHd4 = new WsOffshHd4();
        }
        _WsOffshHd4.SetWsOffshHd4AsString(value);
    }
    
    // Standard Getter
    public WsOffshHd5 GetWsOffshHd5()
    {
        return _WsOffshHd5;
    }
    
    // Standard Setter
    public void SetWsOffshHd5(WsOffshHd5 value)
    {
        _WsOffshHd5 = value;
    }
    
    // Get<>AsString()
    public string GetWsOffshHd5AsString()
    {
        return _WsOffshHd5 != null ? _WsOffshHd5.GetWsOffshHd5AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsOffshHd5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsOffshHd5 == null)
        {
            _WsOffshHd5 = new WsOffshHd5();
        }
        _WsOffshHd5.SetWsOffshHd5AsString(value);
    }
    
    // Standard Getter
    public WsOffshDtl GetWsOffshDtl()
    {
        return _WsOffshDtl;
    }
    
    // Standard Setter
    public void SetWsOffshDtl(WsOffshDtl value)
    {
        _WsOffshDtl = value;
    }
    
    // Get<>AsString()
    public string GetWsOffshDtlAsString()
    {
        return _WsOffshDtl != null ? _WsOffshDtl.GetWsOffshDtlAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsOffshDtlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsOffshDtl == null)
        {
            _WsOffshDtl = new WsOffshDtl();
        }
        _WsOffshDtl.SetWsOffshDtlAsString(value);
    }
    
    // Standard Getter
    public WsOffshTotLine GetWsOffshTotLine()
    {
        return _WsOffshTotLine;
    }
    
    // Standard Setter
    public void SetWsOffshTotLine(WsOffshTotLine value)
    {
        _WsOffshTotLine = value;
    }
    
    // Get<>AsString()
    public string GetWsOffshTotLineAsString()
    {
        return _WsOffshTotLine != null ? _WsOffshTotLine.GetWsOffshTotLineAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsOffshTotLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsOffshTotLine == null)
        {
            _WsOffshTotLine = new WsOffshTotLine();
        }
        _WsOffshTotLine.SetWsOffshTotLineAsString(value);
    }
    
    // Standard Getter
    public string GetWsGrandTotText()
    {
        return _WsGrandTotText;
    }
    
    // Standard Setter
    public void SetWsGrandTotText(string value)
    {
        _WsGrandTotText = value;
    }
    
    // Get<>AsString()
    public string GetWsGrandTotTextAsString()
    {
        return _WsGrandTotText.PadRight(26);
    }
    
    // Set<>AsString()
    public void SetWsGrandTotTextAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsGrandTotText = value;
    }
    
    // Standard Getter
    public string GetWsFundTotText()
    {
        return _WsFundTotText;
    }
    
    // Standard Setter
    public void SetWsFundTotText(string value)
    {
        _WsFundTotText = value;
    }
    
    // Get<>AsString()
    public string GetWsFundTotTextAsString()
    {
        return _WsFundTotText.PadRight(26);
    }
    
    // Set<>AsString()
    public void SetWsFundTotTextAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsFundTotText = value;
    }
    
    // Standard Getter
    public string GetWsOldFund()
    {
        return _WsOldFund;
    }
    
    // Standard Setter
    public void SetWsOldFund(string value)
    {
        _WsOldFund = value;
    }
    
    // Get<>AsString()
    public string GetWsOldFundAsString()
    {
        return _WsOldFund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWsOldFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsOldFund = value;
    }
    
    // Standard Getter
    public string GetWsOldStock()
    {
        return _WsOldStock;
    }
    
    // Standard Setter
    public void SetWsOldStock(string value)
    {
        _WsOldStock = value;
    }
    
    // Get<>AsString()
    public string GetWsOldStockAsString()
    {
        return _WsOldStock.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetWsOldStockAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsOldStock = value;
    }
    
    // Standard Getter
    public string GetWsOldCompFund()
    {
        return _WsOldCompFund;
    }
    
    // Standard Setter
    public void SetWsOldCompFund(string value)
    {
        _WsOldCompFund = value;
    }
    
    // Get<>AsString()
    public string GetWsOldCompFundAsString()
    {
        return _WsOldCompFund.PadRight(60);
    }
    
    // Set<>AsString()
    public void SetWsOldCompFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsOldCompFund = value;
    }
    
    // Standard Getter
    public int GetWsOldFromDate()
    {
        return _WsOldFromDate;
    }
    
    // Standard Setter
    public void SetWsOldFromDate(int value)
    {
        _WsOldFromDate = value;
    }
    
    // Get<>AsString()
    public string GetWsOldFromDateAsString()
    {
        return _WsOldFromDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetWsOldFromDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsOldFromDate = parsed;
    }
    
    // Standard Getter
    public int GetWsOldToDate()
    {
        return _WsOldToDate;
    }
    
    // Standard Setter
    public void SetWsOldToDate(int value)
    {
        _WsOldToDate = value;
    }
    
    // Get<>AsString()
    public string GetWsOldToDateAsString()
    {
        return _WsOldToDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetWsOldToDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsOldToDate = parsed;
    }
    
    // Standard Getter
    public int GetWsLineCount()
    {
        return _WsLineCount;
    }
    
    // Standard Setter
    public void SetWsLineCount(int value)
    {
        _WsLineCount = value;
    }
    
    // Get<>AsString()
    public string GetWsLineCountAsString()
    {
        return _WsLineCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsLineCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsLineCount = parsed;
    }
    
    // Standard Getter
    public int GetWsPageCount()
    {
        return _WsPageCount;
    }
    
    // Standard Setter
    public void SetWsPageCount(int value)
    {
        _WsPageCount = value;
    }
    
    // Get<>AsString()
    public string GetWsPageCountAsString()
    {
        return _WsPageCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsPageCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsPageCount = parsed;
    }
    
    // Standard Getter
    public decimal GetWsNegOffshHoldings()
    {
        return _WsNegOffshHoldings;
    }
    
    // Standard Setter
    public void SetWsNegOffshHoldings(decimal value)
    {
        _WsNegOffshHoldings = value;
    }
    
    // Get<>AsString()
    public string GetWsNegOffshHoldingsAsString()
    {
        return _WsNegOffshHoldings.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsNegOffshHoldingsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsNegOffshHoldings = parsed;
    }
    
    // Standard Getter
    public decimal GetWsNegOffshCgt()
    {
        return _WsNegOffshCgt;
    }
    
    // Standard Setter
    public void SetWsNegOffshCgt(decimal value)
    {
        _WsNegOffshCgt = value;
    }
    
    // Get<>AsString()
    public string GetWsNegOffshCgtAsString()
    {
        return _WsNegOffshCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsNegOffshCgtAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsNegOffshCgt = parsed;
    }
    
    // Standard Getter
    public decimal GetWsFundCgtTot()
    {
        return _WsFundCgtTot;
    }
    
    // Standard Setter
    public void SetWsFundCgtTot(decimal value)
    {
        _WsFundCgtTot = value;
    }
    
    // Get<>AsString()
    public string GetWsFundCgtTotAsString()
    {
        return _WsFundCgtTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsFundCgtTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsFundCgtTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsFundProceedsTot()
    {
        return _WsFundProceedsTot;
    }
    
    // Standard Setter
    public void SetWsFundProceedsTot(decimal value)
    {
        _WsFundProceedsTot = value;
    }
    
    // Get<>AsString()
    public string GetWsFundProceedsTotAsString()
    {
        return _WsFundProceedsTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsFundProceedsTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsFundProceedsTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsFundSchedDTot()
    {
        return _WsFundSchedDTot;
    }
    
    // Standard Setter
    public void SetWsFundSchedDTot(decimal value)
    {
        _WsFundSchedDTot = value;
    }
    
    // Get<>AsString()
    public string GetWsFundSchedDTotAsString()
    {
        return _WsFundSchedDTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsFundSchedDTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsFundSchedDTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsGrandCgtTot()
    {
        return _WsGrandCgtTot;
    }
    
    // Standard Setter
    public void SetWsGrandCgtTot(decimal value)
    {
        _WsGrandCgtTot = value;
    }
    
    // Get<>AsString()
    public string GetWsGrandCgtTotAsString()
    {
        return _WsGrandCgtTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsGrandCgtTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsGrandCgtTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsGrandProceedsTot()
    {
        return _WsGrandProceedsTot;
    }
    
    // Standard Setter
    public void SetWsGrandProceedsTot(decimal value)
    {
        _WsGrandProceedsTot = value;
    }
    
    // Get<>AsString()
    public string GetWsGrandProceedsTotAsString()
    {
        return _WsGrandProceedsTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsGrandProceedsTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsGrandProceedsTot = parsed;
    }
    
    // Standard Getter
    public decimal GetWsGrandSchedDTot()
    {
        return _WsGrandSchedDTot;
    }
    
    // Standard Setter
    public void SetWsGrandSchedDTot(decimal value)
    {
        _WsGrandSchedDTot = value;
    }
    
    // Get<>AsString()
    public string GetWsGrandSchedDTotAsString()
    {
        return _WsGrandSchedDTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsGrandSchedDTotAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsGrandSchedDTot = parsed;
    }
    
    // Standard Getter
    public string GetWsFirstTime()
    {
        return _WsFirstTime;
    }
    
    // Standard Setter
    public void SetWsFirstTime(string value)
    {
        _WsFirstTime = value;
    }
    
    // Get<>AsString()
    public string GetWsFirstTimeAsString()
    {
        return _WsFirstTime.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsFirstTimeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsFirstTime = value;
    }
    
    // Standard Getter
    public string GetWsRecordWritten()
    {
        return _WsRecordWritten;
    }
    
    // Standard Setter
    public void SetWsRecordWritten(string value)
    {
        _WsRecordWritten = value;
    }
    
    // Get<>AsString()
    public string GetWsRecordWrittenAsString()
    {
        return _WsRecordWritten.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsRecordWrittenAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsRecordWritten = value;
    }
    
    // Standard Getter
    public string GetWsOldFundDd()
    {
        return _WsOldFundDd;
    }
    
    // Standard Setter
    public void SetWsOldFundDd(string value)
    {
        _WsOldFundDd = value;
    }
    
    // Get<>AsString()
    public string GetWsOldFundDdAsString()
    {
        return _WsOldFundDd.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWsOldFundDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsOldFundDd = value;
    }
    
    // Standard Getter
    public string GetWsOldStockDd()
    {
        return _WsOldStockDd;
    }
    
    // Standard Setter
    public void SetWsOldStockDd(string value)
    {
        _WsOldStockDd = value;
    }
    
    // Get<>AsString()
    public string GetWsOldStockDdAsString()
    {
        return _WsOldStockDd.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetWsOldStockDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsOldStockDd = value;
    }
    
    // Standard Getter
    public string GetWsOldCompFundDd()
    {
        return _WsOldCompFundDd;
    }
    
    // Standard Setter
    public void SetWsOldCompFundDd(string value)
    {
        _WsOldCompFundDd = value;
    }
    
    // Get<>AsString()
    public string GetWsOldCompFundDdAsString()
    {
        return _WsOldCompFundDd.PadRight(60);
    }
    
    // Set<>AsString()
    public void SetWsOldCompFundDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsOldCompFundDd = value;
    }
    
    // Standard Getter
    public int GetWsOldFromDateDd()
    {
        return _WsOldFromDateDd;
    }
    
    // Standard Setter
    public void SetWsOldFromDateDd(int value)
    {
        _WsOldFromDateDd = value;
    }
    
    // Get<>AsString()
    public string GetWsOldFromDateDdAsString()
    {
        return _WsOldFromDateDd.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetWsOldFromDateDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsOldFromDateDd = parsed;
    }
    
    // Standard Getter
    public int GetWsOldToDateDd()
    {
        return _WsOldToDateDd;
    }
    
    // Standard Setter
    public void SetWsOldToDateDd(int value)
    {
        _WsOldToDateDd = value;
    }
    
    // Get<>AsString()
    public string GetWsOldToDateDdAsString()
    {
        return _WsOldToDateDd.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetWsOldToDateDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsOldToDateDd = parsed;
    }
    
    // Standard Getter
    public int GetWsLineCountDd()
    {
        return _WsLineCountDd;
    }
    
    // Standard Setter
    public void SetWsLineCountDd(int value)
    {
        _WsLineCountDd = value;
    }
    
    // Get<>AsString()
    public string GetWsLineCountDdAsString()
    {
        return _WsLineCountDd.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsLineCountDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsLineCountDd = parsed;
    }
    
    // Standard Getter
    public int GetWsPageCountDd()
    {
        return _WsPageCountDd;
    }
    
    // Standard Setter
    public void SetWsPageCountDd(int value)
    {
        _WsPageCountDd = value;
    }
    
    // Get<>AsString()
    public string GetWsPageCountDdAsString()
    {
        return _WsPageCountDd.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWsPageCountDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsPageCountDd = parsed;
    }
    
    // Standard Getter
    public decimal GetWsNegOffshHoldingsDd()
    {
        return _WsNegOffshHoldingsDd;
    }
    
    // Standard Setter
    public void SetWsNegOffshHoldingsDd(decimal value)
    {
        _WsNegOffshHoldingsDd = value;
    }
    
    // Get<>AsString()
    public string GetWsNegOffshHoldingsDdAsString()
    {
        return _WsNegOffshHoldingsDd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsNegOffshHoldingsDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsNegOffshHoldingsDd = parsed;
    }
    
    // Standard Getter
    public decimal GetWsNegOffshCgtDd()
    {
        return _WsNegOffshCgtDd;
    }
    
    // Standard Setter
    public void SetWsNegOffshCgtDd(decimal value)
    {
        _WsNegOffshCgtDd = value;
    }
    
    // Get<>AsString()
    public string GetWsNegOffshCgtDdAsString()
    {
        return _WsNegOffshCgtDd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsNegOffshCgtDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsNegOffshCgtDd = parsed;
    }
    
    // Standard Getter
    public decimal GetWsFundCgtTotDd()
    {
        return _WsFundCgtTotDd;
    }
    
    // Standard Setter
    public void SetWsFundCgtTotDd(decimal value)
    {
        _WsFundCgtTotDd = value;
    }
    
    // Get<>AsString()
    public string GetWsFundCgtTotDdAsString()
    {
        return _WsFundCgtTotDd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsFundCgtTotDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsFundCgtTotDd = parsed;
    }
    
    // Standard Getter
    public decimal GetWsFundProceedsTotDd()
    {
        return _WsFundProceedsTotDd;
    }
    
    // Standard Setter
    public void SetWsFundProceedsTotDd(decimal value)
    {
        _WsFundProceedsTotDd = value;
    }
    
    // Get<>AsString()
    public string GetWsFundProceedsTotDdAsString()
    {
        return _WsFundProceedsTotDd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsFundProceedsTotDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsFundProceedsTotDd = parsed;
    }
    
    // Standard Getter
    public decimal GetWsFundSchedDTotDd()
    {
        return _WsFundSchedDTotDd;
    }
    
    // Standard Setter
    public void SetWsFundSchedDTotDd(decimal value)
    {
        _WsFundSchedDTotDd = value;
    }
    
    // Get<>AsString()
    public string GetWsFundSchedDTotDdAsString()
    {
        return _WsFundSchedDTotDd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsFundSchedDTotDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsFundSchedDTotDd = parsed;
    }
    
    // Standard Getter
    public decimal GetWsGrandCgtTotDd()
    {
        return _WsGrandCgtTotDd;
    }
    
    // Standard Setter
    public void SetWsGrandCgtTotDd(decimal value)
    {
        _WsGrandCgtTotDd = value;
    }
    
    // Get<>AsString()
    public string GetWsGrandCgtTotDdAsString()
    {
        return _WsGrandCgtTotDd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsGrandCgtTotDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsGrandCgtTotDd = parsed;
    }
    
    // Standard Getter
    public decimal GetWsGrandProceedsTotDd()
    {
        return _WsGrandProceedsTotDd;
    }
    
    // Standard Setter
    public void SetWsGrandProceedsTotDd(decimal value)
    {
        _WsGrandProceedsTotDd = value;
    }
    
    // Get<>AsString()
    public string GetWsGrandProceedsTotDdAsString()
    {
        return _WsGrandProceedsTotDd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsGrandProceedsTotDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsGrandProceedsTotDd = parsed;
    }
    
    // Standard Getter
    public decimal GetWsGrandSchedDTotDd()
    {
        return _WsGrandSchedDTotDd;
    }
    
    // Standard Setter
    public void SetWsGrandSchedDTotDd(decimal value)
    {
        _WsGrandSchedDTotDd = value;
    }
    
    // Get<>AsString()
    public string GetWsGrandSchedDTotDdAsString()
    {
        return _WsGrandSchedDTotDd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsGrandSchedDTotDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsGrandSchedDTotDd = parsed;
    }
    
    // Standard Getter
    public string GetWsFirstTimeDd()
    {
        return _WsFirstTimeDd;
    }
    
    // Standard Setter
    public void SetWsFirstTimeDd(string value)
    {
        _WsFirstTimeDd = value;
    }
    
    // Get<>AsString()
    public string GetWsFirstTimeDdAsString()
    {
        return _WsFirstTimeDd.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsFirstTimeDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsFirstTimeDd = value;
    }
    
    // Standard Getter
    public string GetWsRecordWrittenDd()
    {
        return _WsRecordWrittenDd;
    }
    
    // Standard Setter
    public void SetWsRecordWrittenDd(string value)
    {
        _WsRecordWrittenDd = value;
    }
    
    // Get<>AsString()
    public string GetWsRecordWrittenDdAsString()
    {
        return _WsRecordWrittenDd.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsRecordWrittenDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsRecordWrittenDd = value;
    }
    
    // Standard Getter
    public WsConvSedol GetWsConvSedol()
    {
        return _WsConvSedol;
    }
    
    // Standard Setter
    public void SetWsConvSedol(WsConvSedol value)
    {
        _WsConvSedol = value;
    }
    
    // Get<>AsString()
    public string GetWsConvSedolAsString()
    {
        return _WsConvSedol != null ? _WsConvSedol.GetWsConvSedolAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsConvSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsConvSedol == null)
        {
            _WsConvSedol = new WsConvSedol();
        }
        _WsConvSedol.SetWsConvSedolAsString(value);
    }
    
    // Standard Getter
    public WsDateConv GetWsDateConv()
    {
        return _WsDateConv;
    }
    
    // Standard Setter
    public void SetWsDateConv(WsDateConv value)
    {
        _WsDateConv = value;
    }
    
    // Get<>AsString()
    public string GetWsDateConvAsString()
    {
        return _WsDateConv != null ? _WsDateConv.GetWsDateConvAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsDateConvAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsDateConv == null)
        {
            _WsDateConv = new WsDateConv();
        }
        _WsDateConv.SetWsDateConvAsString(value);
    }
    
    // Standard Getter
    public WsDateOut GetWsDateOut()
    {
        return _WsDateOut;
    }
    
    // Standard Setter
    public void SetWsDateOut(WsDateOut value)
    {
        _WsDateOut = value;
    }
    
    // Get<>AsString()
    public string GetWsDateOutAsString()
    {
        return _WsDateOut != null ? _WsDateOut.GetWsDateOutAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsDateOutAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsDateOut == null)
        {
            _WsDateOut = new WsDateOut();
        }
        _WsDateOut.SetWsDateOutAsString(value);
    }
    
    // Standard Getter
    public MonthTable GetMonthTable()
    {
        return _MonthTable;
    }
    
    // Standard Setter
    public void SetMonthTable(MonthTable value)
    {
        _MonthTable = value;
    }
    
    // Get<>AsString()
    public string GetMonthTableAsString()
    {
        return _MonthTable != null ? _MonthTable.GetMonthTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetMonthTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_MonthTable == null)
        {
            _MonthTable = new MonthTable();
        }
        _MonthTable.SetMonthTableAsString(value);
    }
    
    // Standard Getter
    public MonthTab GetMonthTab()
    {
        return _MonthTab;
    }
    
    // Standard Setter
    public void SetMonthTab(MonthTab value)
    {
        _MonthTab = value;
    }
    
    // Get<>AsString()
    public string GetMonthTabAsString()
    {
        return _MonthTab != null ? _MonthTab.GetMonthTabAsString() : "";
    }
    
    // Set<>AsString()
    public void SetMonthTabAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_MonthTab == null)
        {
            _MonthTab = new MonthTab();
        }
        _MonthTab.SetMonthTabAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWsOffshHd1(string value)
    {
        _WsOffshHd1.SetWsOffshHd1AsString(value);
    }
    // Nested Class: WsOffshHd1
    public class WsOffshHd1
    {
        private static int _size = 160;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WsOffshCompName, is_external=, is_static_class=False, static_prefix=
        private string _WsOffshCompName ="";
        
        
        
        
        // [DEBUG] Field: Filler193, is_external=, is_static_class=False, static_prefix=
        private string _Filler193 ="";
        
        
        
        
        // [DEBUG] Field: Filler194, is_external=, is_static_class=False, static_prefix=
        private string _Filler194 ="SCHEDULE OF OFFSHORE GAINS TREATED AS ";
        
        
        
        
        // [DEBUG] Field: Filler195, is_external=, is_static_class=False, static_prefix=
        private string _Filler195 ="SCHEDULE D CASE IV. INCOME";
        
        
        
        
    public WsOffshHd1() {}
    
    public WsOffshHd1(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWsOffshCompName(data.Substring(offset, 60).Trim());
        offset += 60;
        SetFiller193(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller194(data.Substring(offset, 38).Trim());
        offset += 38;
        SetFiller195(data.Substring(offset, 62).Trim());
        offset += 62;
        
    }
    
    // Serialization methods
    public string GetWsOffshHd1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsOffshCompName.PadRight(60));
        result.Append(_Filler193.PadRight(0));
        result.Append(_Filler194.PadRight(38));
        result.Append(_Filler195.PadRight(62));
        
        return result.ToString();
    }
    
    public void SetWsOffshHd1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 60 <= data.Length)
        {
            string extracted = data.Substring(offset, 60).Trim();
            SetWsOffshCompName(extracted);
        }
        offset += 60;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller193(extracted);
        }
        offset += 0;
        if (offset + 38 <= data.Length)
        {
            string extracted = data.Substring(offset, 38).Trim();
            SetFiller194(extracted);
        }
        offset += 38;
        if (offset + 62 <= data.Length)
        {
            string extracted = data.Substring(offset, 62).Trim();
            SetFiller195(extracted);
        }
        offset += 62;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsOffshCompName()
    {
        return _WsOffshCompName;
    }
    
    // Standard Setter
    public void SetWsOffshCompName(string value)
    {
        _WsOffshCompName = value;
    }
    
    // Get<>AsString()
    public string GetWsOffshCompNameAsString()
    {
        return _WsOffshCompName.PadRight(60);
    }
    
    // Set<>AsString()
    public void SetWsOffshCompNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsOffshCompName = value;
    }
    
    // Standard Getter
    public string GetFiller193()
    {
        return _Filler193;
    }
    
    // Standard Setter
    public void SetFiller193(string value)
    {
        _Filler193 = value;
    }
    
    // Get<>AsString()
    public string GetFiller193AsString()
    {
        return _Filler193.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller193AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler193 = value;
    }
    
    // Standard Getter
    public string GetFiller194()
    {
        return _Filler194;
    }
    
    // Standard Setter
    public void SetFiller194(string value)
    {
        _Filler194 = value;
    }
    
    // Get<>AsString()
    public string GetFiller194AsString()
    {
        return _Filler194.PadRight(38);
    }
    
    // Set<>AsString()
    public void SetFiller194AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler194 = value;
    }
    
    // Standard Getter
    public string GetFiller195()
    {
        return _Filler195;
    }
    
    // Standard Setter
    public void SetFiller195(string value)
    {
        _Filler195 = value;
    }
    
    // Get<>AsString()
    public string GetFiller195AsString()
    {
        return _Filler195.PadRight(62);
    }
    
    // Set<>AsString()
    public void SetFiller195AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler195 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetWsOffshHd1Dd(string value)
{
    _WsOffshHd1Dd.SetWsOffshHd1DdAsString(value);
}
// Nested Class: WsOffshHd1Dd
public class WsOffshHd1Dd
{
    private static int _size = 160;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsOffshCompNameDd, is_external=, is_static_class=False, static_prefix=
    private string _WsOffshCompNameDd ="";
    
    
    
    
    // [DEBUG] Field: Filler196, is_external=, is_static_class=False, static_prefix=
    private string _Filler196 ="";
    
    
    
    
    // [DEBUG] Field: Filler197, is_external=, is_static_class=False, static_prefix=
    private string _Filler197 ="SCHEDULE OF DEEMED OFFSHORE GAINS TREATED AS ";
    
    
    
    
    // [DEBUG] Field: Filler198, is_external=, is_static_class=False, static_prefix=
    private string _Filler198 ="SCHEDULE D CASE IV. INCOME";
    
    
    
    
public WsOffshHd1Dd() {}

public WsOffshHd1Dd(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsOffshCompNameDd(data.Substring(offset, 60).Trim());
    offset += 60;
    SetFiller196(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller197(data.Substring(offset, 45).Trim());
    offset += 45;
    SetFiller198(data.Substring(offset, 55).Trim());
    offset += 55;
    
}

// Serialization methods
public string GetWsOffshHd1DdAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsOffshCompNameDd.PadRight(60));
    result.Append(_Filler196.PadRight(0));
    result.Append(_Filler197.PadRight(45));
    result.Append(_Filler198.PadRight(55));
    
    return result.ToString();
}

public void SetWsOffshHd1DdAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 60 <= data.Length)
    {
        string extracted = data.Substring(offset, 60).Trim();
        SetWsOffshCompNameDd(extracted);
    }
    offset += 60;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller196(extracted);
    }
    offset += 0;
    if (offset + 45 <= data.Length)
    {
        string extracted = data.Substring(offset, 45).Trim();
        SetFiller197(extracted);
    }
    offset += 45;
    if (offset + 55 <= data.Length)
    {
        string extracted = data.Substring(offset, 55).Trim();
        SetFiller198(extracted);
    }
    offset += 55;
}

// Getter and Setter methods

// Standard Getter
public string GetWsOffshCompNameDd()
{
    return _WsOffshCompNameDd;
}

// Standard Setter
public void SetWsOffshCompNameDd(string value)
{
    _WsOffshCompNameDd = value;
}

// Get<>AsString()
public string GetWsOffshCompNameDdAsString()
{
    return _WsOffshCompNameDd.PadRight(60);
}

// Set<>AsString()
public void SetWsOffshCompNameDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsOffshCompNameDd = value;
}

// Standard Getter
public string GetFiller196()
{
    return _Filler196;
}

// Standard Setter
public void SetFiller196(string value)
{
    _Filler196 = value;
}

// Get<>AsString()
public string GetFiller196AsString()
{
    return _Filler196.PadRight(0);
}

// Set<>AsString()
public void SetFiller196AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler196 = value;
}

// Standard Getter
public string GetFiller197()
{
    return _Filler197;
}

// Standard Setter
public void SetFiller197(string value)
{
    _Filler197 = value;
}

// Get<>AsString()
public string GetFiller197AsString()
{
    return _Filler197.PadRight(45);
}

// Set<>AsString()
public void SetFiller197AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler197 = value;
}

// Standard Getter
public string GetFiller198()
{
    return _Filler198;
}

// Standard Setter
public void SetFiller198(string value)
{
    _Filler198 = value;
}

// Get<>AsString()
public string GetFiller198AsString()
{
    return _Filler198.PadRight(55);
}

// Set<>AsString()
public void SetFiller198AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler198 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsOffshHd2(string value)
{
    _WsOffshHd2.SetWsOffshHd2AsString(value);
}
// Nested Class: WsOffshHd2
public class WsOffshHd2
{
    private static int _size = 163;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler199, is_external=, is_static_class=False, static_prefix=
    private string _Filler199 ="";
    
    
    
    
    // [DEBUG] Field: Filler200, is_external=, is_static_class=False, static_prefix=
    private string _Filler200 ="Fund";
    
    
    
    
    // [DEBUG] Field: Filler201, is_external=, is_static_class=False, static_prefix=
    private string _Filler201 ="Period";
    
    
    
    
    // [DEBUG] Field: Filler202, is_external=, is_static_class=False, static_prefix=
    private string _Filler202 ="Page";
    
    
    
    
public WsOffshHd2() {}

public WsOffshHd2(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller199(data.Substring(offset, 129).Trim());
    offset += 129;
    SetFiller200(data.Substring(offset, 15).Trim());
    offset += 15;
    SetFiller201(data.Substring(offset, 15).Trim());
    offset += 15;
    SetFiller202(data.Substring(offset, 4).Trim());
    offset += 4;
    
}

// Serialization methods
public string GetWsOffshHd2AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler199.PadRight(129));
    result.Append(_Filler200.PadRight(15));
    result.Append(_Filler201.PadRight(15));
    result.Append(_Filler202.PadRight(4));
    
    return result.ToString();
}

public void SetWsOffshHd2AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 129 <= data.Length)
    {
        string extracted = data.Substring(offset, 129).Trim();
        SetFiller199(extracted);
    }
    offset += 129;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetFiller200(extracted);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetFiller201(extracted);
    }
    offset += 15;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetFiller202(extracted);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller199()
{
    return _Filler199;
}

// Standard Setter
public void SetFiller199(string value)
{
    _Filler199 = value;
}

// Get<>AsString()
public string GetFiller199AsString()
{
    return _Filler199.PadRight(129);
}

// Set<>AsString()
public void SetFiller199AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler199 = value;
}

// Standard Getter
public string GetFiller200()
{
    return _Filler200;
}

// Standard Setter
public void SetFiller200(string value)
{
    _Filler200 = value;
}

// Get<>AsString()
public string GetFiller200AsString()
{
    return _Filler200.PadRight(15);
}

// Set<>AsString()
public void SetFiller200AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler200 = value;
}

// Standard Getter
public string GetFiller201()
{
    return _Filler201;
}

// Standard Setter
public void SetFiller201(string value)
{
    _Filler201 = value;
}

// Get<>AsString()
public string GetFiller201AsString()
{
    return _Filler201.PadRight(15);
}

// Set<>AsString()
public void SetFiller201AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler201 = value;
}

// Standard Getter
public string GetFiller202()
{
    return _Filler202;
}

// Standard Setter
public void SetFiller202(string value)
{
    _Filler202 = value;
}

// Get<>AsString()
public string GetFiller202AsString()
{
    return _Filler202.PadRight(4);
}

// Set<>AsString()
public void SetFiller202AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler202 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsOffshHd3(string value)
{
    _WsOffshHd3.SetWsOffshHd3AsString(value);
}
// Nested Class: WsOffshHd3
public class WsOffshHd3
{
    private static int _size = 163;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler203, is_external=, is_static_class=False, static_prefix=
    private string _Filler203 ="";
    
    
    
    
    // [DEBUG] Field: WsOffshCompFund, is_external=, is_static_class=False, static_prefix=
    private string _WsOffshCompFund ="";
    
    
    
    
    // [DEBUG] Field: Filler204, is_external=, is_static_class=False, static_prefix=
    private string _Filler204 =" ";
    
    
    
    
    // [DEBUG] Field: WsOffshFund, is_external=, is_static_class=False, static_prefix=
    private string _WsOffshFund ="";
    
    
    
    
    // [DEBUG] Field: Filler205, is_external=, is_static_class=False, static_prefix=
    private string _Filler205 =" ";
    
    
    
    
    // [DEBUG] Field: WsOffshPeriod, is_external=, is_static_class=False, static_prefix=
    private WsOffshHd3.WsOffshPeriod _WsOffshPeriod = new WsOffshHd3.WsOffshPeriod();
    
    
    
    
    // [DEBUG] Field: Filler207, is_external=, is_static_class=False, static_prefix=
    private string _Filler207 =" ";
    
    
    
    
    // [DEBUG] Field: WsOffshPageNo, is_external=, is_static_class=False, static_prefix=
    private decimal _WsOffshPageNo =0;
    
    
    
    
public WsOffshHd3() {}

public WsOffshHd3(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller203(data.Substring(offset, 54).Trim());
    offset += 54;
    SetWsOffshCompFund(data.Substring(offset, 60).Trim());
    offset += 60;
    SetFiller204(data.Substring(offset, 15).Trim());
    offset += 15;
    SetWsOffshFund(data.Substring(offset, 4).Trim());
    offset += 4;
    SetFiller205(data.Substring(offset, 7).Trim());
    offset += 7;
    _WsOffshPeriod.SetWsOffshPeriodAsString(data.Substring(offset, WsOffshPeriod.GetSize()));
    offset += 15;
    SetFiller207(data.Substring(offset, 4).Trim());
    offset += 4;
    SetWsOffshPageNo(PackedDecimalConverter.ToDecimal(data.Substring(offset, 4)));
    offset += 4;
    
}

// Serialization methods
public string GetWsOffshHd3AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler203.PadRight(54));
    result.Append(_WsOffshCompFund.PadRight(60));
    result.Append(_Filler204.PadRight(15));
    result.Append(_WsOffshFund.PadRight(4));
    result.Append(_Filler205.PadRight(7));
    result.Append(_WsOffshPeriod.GetWsOffshPeriodAsString());
    result.Append(_Filler207.PadRight(4));
    result.Append(_WsOffshPageNo.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetWsOffshHd3AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 54 <= data.Length)
    {
        string extracted = data.Substring(offset, 54).Trim();
        SetFiller203(extracted);
    }
    offset += 54;
    if (offset + 60 <= data.Length)
    {
        string extracted = data.Substring(offset, 60).Trim();
        SetWsOffshCompFund(extracted);
    }
    offset += 60;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetFiller204(extracted);
    }
    offset += 15;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWsOffshFund(extracted);
    }
    offset += 4;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetFiller205(extracted);
    }
    offset += 7;
    if (offset + 15 <= data.Length)
    {
        _WsOffshPeriod.SetWsOffshPeriodAsString(data.Substring(offset, 15));
    }
    else
    {
        _WsOffshPeriod.SetWsOffshPeriodAsString(data.Substring(offset));
    }
    offset += 15;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetFiller207(extracted);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsOffshPageNo(parsedDec);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller203()
{
    return _Filler203;
}

// Standard Setter
public void SetFiller203(string value)
{
    _Filler203 = value;
}

// Get<>AsString()
public string GetFiller203AsString()
{
    return _Filler203.PadRight(54);
}

// Set<>AsString()
public void SetFiller203AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler203 = value;
}

// Standard Getter
public string GetWsOffshCompFund()
{
    return _WsOffshCompFund;
}

// Standard Setter
public void SetWsOffshCompFund(string value)
{
    _WsOffshCompFund = value;
}

// Get<>AsString()
public string GetWsOffshCompFundAsString()
{
    return _WsOffshCompFund.PadRight(60);
}

// Set<>AsString()
public void SetWsOffshCompFundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsOffshCompFund = value;
}

// Standard Getter
public string GetFiller204()
{
    return _Filler204;
}

// Standard Setter
public void SetFiller204(string value)
{
    _Filler204 = value;
}

// Get<>AsString()
public string GetFiller204AsString()
{
    return _Filler204.PadRight(15);
}

// Set<>AsString()
public void SetFiller204AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler204 = value;
}

// Standard Getter
public string GetWsOffshFund()
{
    return _WsOffshFund;
}

// Standard Setter
public void SetWsOffshFund(string value)
{
    _WsOffshFund = value;
}

// Get<>AsString()
public string GetWsOffshFundAsString()
{
    return _WsOffshFund.PadRight(4);
}

// Set<>AsString()
public void SetWsOffshFundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsOffshFund = value;
}

// Standard Getter
public string GetFiller205()
{
    return _Filler205;
}

// Standard Setter
public void SetFiller205(string value)
{
    _Filler205 = value;
}

// Get<>AsString()
public string GetFiller205AsString()
{
    return _Filler205.PadRight(7);
}

// Set<>AsString()
public void SetFiller205AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler205 = value;
}

// Standard Getter
public WsOffshPeriod GetWsOffshPeriod()
{
    return _WsOffshPeriod;
}

// Standard Setter
public void SetWsOffshPeriod(WsOffshPeriod value)
{
    _WsOffshPeriod = value;
}

// Get<>AsString()
public string GetWsOffshPeriodAsString()
{
    return _WsOffshPeriod != null ? _WsOffshPeriod.GetWsOffshPeriodAsString() : "";
}

// Set<>AsString()
public void SetWsOffshPeriodAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsOffshPeriod == null)
    {
        _WsOffshPeriod = new WsOffshPeriod();
    }
    _WsOffshPeriod.SetWsOffshPeriodAsString(value);
}

// Standard Getter
public string GetFiller207()
{
    return _Filler207;
}

// Standard Setter
public void SetFiller207(string value)
{
    _Filler207 = value;
}

// Get<>AsString()
public string GetFiller207AsString()
{
    return _Filler207.PadRight(4);
}

// Set<>AsString()
public void SetFiller207AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler207 = value;
}

// Standard Getter
public decimal GetWsOffshPageNo()
{
    return _WsOffshPageNo;
}

// Standard Setter
public void SetWsOffshPageNo(decimal value)
{
    _WsOffshPageNo = value;
}

// Get<>AsString()
public string GetWsOffshPageNoAsString()
{
    return _WsOffshPageNo.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsOffshPageNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsOffshPageNo = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WsOffshPeriod
public class WsOffshPeriod
{
    private static int _size = 15;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsFromDate, is_external=, is_static_class=False, static_prefix=
    private string _WsFromDate ="";
    
    
    
    
    // [DEBUG] Field: Filler206, is_external=, is_static_class=False, static_prefix=
    private string _Filler206 ="-";
    
    
    
    
    // [DEBUG] Field: WsToDate, is_external=, is_static_class=False, static_prefix=
    private string _WsToDate ="";
    
    
    
    
public WsOffshPeriod() {}

public WsOffshPeriod(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsFromDate(data.Substring(offset, 7).Trim());
    offset += 7;
    SetFiller206(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsToDate(data.Substring(offset, 7).Trim());
    offset += 7;
    
}

// Serialization methods
public string GetWsOffshPeriodAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsFromDate.PadRight(7));
    result.Append(_Filler206.PadRight(1));
    result.Append(_WsToDate.PadRight(7));
    
    return result.ToString();
}

public void SetWsOffshPeriodAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetWsFromDate(extracted);
    }
    offset += 7;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller206(extracted);
    }
    offset += 1;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetWsToDate(extracted);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetWsFromDate()
{
    return _WsFromDate;
}

// Standard Setter
public void SetWsFromDate(string value)
{
    _WsFromDate = value;
}

// Get<>AsString()
public string GetWsFromDateAsString()
{
    return _WsFromDate.PadRight(7);
}

// Set<>AsString()
public void SetWsFromDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsFromDate = value;
}

// Standard Getter
public string GetFiller206()
{
    return _Filler206;
}

// Standard Setter
public void SetFiller206(string value)
{
    _Filler206 = value;
}

// Get<>AsString()
public string GetFiller206AsString()
{
    return _Filler206.PadRight(1);
}

// Set<>AsString()
public void SetFiller206AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler206 = value;
}

// Standard Getter
public string GetWsToDate()
{
    return _WsToDate;
}

// Standard Setter
public void SetWsToDate(string value)
{
    _WsToDate = value;
}

// Get<>AsString()
public string GetWsToDateAsString()
{
    return _WsToDate.PadRight(7);
}

// Set<>AsString()
public void SetWsToDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsToDate = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetWsOffshHd4(string value)
{
    _WsOffshHd4.SetWsOffshHd4AsString(value);
}
// Nested Class: WsOffshHd4
public class WsOffshHd4
{
    private static int _size = 163;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler208, is_external=, is_static_class=False, static_prefix=
    private string _Filler208 ="Security Code";
    
    
    
    
    // [DEBUG] Field: Filler209, is_external=, is_static_class=False, static_prefix=
    private string _Filler209 ="Name of Security";
    
    
    
    
    // [DEBUG] Field: Filler210, is_external=, is_static_class=False, static_prefix=
    private string _Filler210 ="Type ";
    
    
    
    
    // [DEBUG] Field: Filler211, is_external=, is_static_class=False, static_prefix=
    private string _Filler211 ="Bargain  ";
    
    
    
    
    // [DEBUG] Field: Filler212, is_external=, is_static_class=False, static_prefix=
    private string _Filler212 ="  Holdings";
    
    
    
    
    // [DEBUG] Field: Filler213, is_external=, is_static_class=False, static_prefix=
    private string _Filler213 ="CGT";
    
    
    
    
    // [DEBUG] Field: Filler214, is_external=, is_static_class=False, static_prefix=
    private string _Filler214 ="Proceeds of";
    
    
    
    
    // [DEBUG] Field: Filler215, is_external=, is_static_class=False, static_prefix=
    private string _Filler215 ="Sched D";
    
    
    
    
public WsOffshHd4() {}

public WsOffshHd4(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller208(data.Substring(offset, 15).Trim());
    offset += 15;
    SetFiller209(data.Substring(offset, 73).Trim());
    offset += 73;
    SetFiller210(data.Substring(offset, 5).Trim());
    offset += 5;
    SetFiller211(data.Substring(offset, 9).Trim());
    offset += 9;
    SetFiller212(data.Substring(offset, 18).Trim());
    offset += 18;
    SetFiller213(data.Substring(offset, 14).Trim());
    offset += 14;
    SetFiller214(data.Substring(offset, 19).Trim());
    offset += 19;
    SetFiller215(data.Substring(offset, 10).Trim());
    offset += 10;
    
}

// Serialization methods
public string GetWsOffshHd4AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler208.PadRight(15));
    result.Append(_Filler209.PadRight(73));
    result.Append(_Filler210.PadRight(5));
    result.Append(_Filler211.PadRight(9));
    result.Append(_Filler212.PadRight(18));
    result.Append(_Filler213.PadRight(14));
    result.Append(_Filler214.PadRight(19));
    result.Append(_Filler215.PadRight(10));
    
    return result.ToString();
}

public void SetWsOffshHd4AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetFiller208(extracted);
    }
    offset += 15;
    if (offset + 73 <= data.Length)
    {
        string extracted = data.Substring(offset, 73).Trim();
        SetFiller209(extracted);
    }
    offset += 73;
    if (offset + 5 <= data.Length)
    {
        string extracted = data.Substring(offset, 5).Trim();
        SetFiller210(extracted);
    }
    offset += 5;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        SetFiller211(extracted);
    }
    offset += 9;
    if (offset + 18 <= data.Length)
    {
        string extracted = data.Substring(offset, 18).Trim();
        SetFiller212(extracted);
    }
    offset += 18;
    if (offset + 14 <= data.Length)
    {
        string extracted = data.Substring(offset, 14).Trim();
        SetFiller213(extracted);
    }
    offset += 14;
    if (offset + 19 <= data.Length)
    {
        string extracted = data.Substring(offset, 19).Trim();
        SetFiller214(extracted);
    }
    offset += 19;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetFiller215(extracted);
    }
    offset += 10;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller208()
{
    return _Filler208;
}

// Standard Setter
public void SetFiller208(string value)
{
    _Filler208 = value;
}

// Get<>AsString()
public string GetFiller208AsString()
{
    return _Filler208.PadRight(15);
}

// Set<>AsString()
public void SetFiller208AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler208 = value;
}

// Standard Getter
public string GetFiller209()
{
    return _Filler209;
}

// Standard Setter
public void SetFiller209(string value)
{
    _Filler209 = value;
}

// Get<>AsString()
public string GetFiller209AsString()
{
    return _Filler209.PadRight(73);
}

// Set<>AsString()
public void SetFiller209AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler209 = value;
}

// Standard Getter
public string GetFiller210()
{
    return _Filler210;
}

// Standard Setter
public void SetFiller210(string value)
{
    _Filler210 = value;
}

// Get<>AsString()
public string GetFiller210AsString()
{
    return _Filler210.PadRight(5);
}

// Set<>AsString()
public void SetFiller210AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler210 = value;
}

// Standard Getter
public string GetFiller211()
{
    return _Filler211;
}

// Standard Setter
public void SetFiller211(string value)
{
    _Filler211 = value;
}

// Get<>AsString()
public string GetFiller211AsString()
{
    return _Filler211.PadRight(9);
}

// Set<>AsString()
public void SetFiller211AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler211 = value;
}

// Standard Getter
public string GetFiller212()
{
    return _Filler212;
}

// Standard Setter
public void SetFiller212(string value)
{
    _Filler212 = value;
}

// Get<>AsString()
public string GetFiller212AsString()
{
    return _Filler212.PadRight(18);
}

// Set<>AsString()
public void SetFiller212AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler212 = value;
}

// Standard Getter
public string GetFiller213()
{
    return _Filler213;
}

// Standard Setter
public void SetFiller213(string value)
{
    _Filler213 = value;
}

// Get<>AsString()
public string GetFiller213AsString()
{
    return _Filler213.PadRight(14);
}

// Set<>AsString()
public void SetFiller213AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler213 = value;
}

// Standard Getter
public string GetFiller214()
{
    return _Filler214;
}

// Standard Setter
public void SetFiller214(string value)
{
    _Filler214 = value;
}

// Get<>AsString()
public string GetFiller214AsString()
{
    return _Filler214.PadRight(19);
}

// Set<>AsString()
public void SetFiller214AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler214 = value;
}

// Standard Getter
public string GetFiller215()
{
    return _Filler215;
}

// Standard Setter
public void SetFiller215(string value)
{
    _Filler215 = value;
}

// Get<>AsString()
public string GetFiller215AsString()
{
    return _Filler215.PadRight(10);
}

// Set<>AsString()
public void SetFiller215AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler215 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsOffshHd5(string value)
{
    _WsOffshHd5.SetWsOffshHd5AsString(value);
}
// Nested Class: WsOffshHd5
public class WsOffshHd5
{
    private static int _size = 163;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler216, is_external=, is_static_class=False, static_prefix=
    private string _Filler216 =" ";
    
    
    
    
    // [DEBUG] Field: Filler217, is_external=, is_static_class=False, static_prefix=
    private string _Filler217 =" Date  ";
    
    
    
    
    // [DEBUG] Field: Filler218, is_external=, is_static_class=False, static_prefix=
    private string _Filler218 ="  Disposed";
    
    
    
    
    // [DEBUG] Field: Filler219, is_external=, is_static_class=False, static_prefix=
    private string _Filler219 ="Costs";
    
    
    
    
    // [DEBUG] Field: Filler220, is_external=, is_static_class=False, static_prefix=
    private string _Filler220 ="  Disposal ";
    
    
    
    
    // [DEBUG] Field: Filler221, is_external=, is_static_class=False, static_prefix=
    private string _Filler221 ="Case VI.";
    
    
    
    
public WsOffshHd5() {}

public WsOffshHd5(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller216(data.Substring(offset, 93).Trim());
    offset += 93;
    SetFiller217(data.Substring(offset, 9).Trim());
    offset += 9;
    SetFiller218(data.Substring(offset, 18).Trim());
    offset += 18;
    SetFiller219(data.Substring(offset, 14).Trim());
    offset += 14;
    SetFiller220(data.Substring(offset, 19).Trim());
    offset += 19;
    SetFiller221(data.Substring(offset, 10).Trim());
    offset += 10;
    
}

// Serialization methods
public string GetWsOffshHd5AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler216.PadRight(93));
    result.Append(_Filler217.PadRight(9));
    result.Append(_Filler218.PadRight(18));
    result.Append(_Filler219.PadRight(14));
    result.Append(_Filler220.PadRight(19));
    result.Append(_Filler221.PadRight(10));
    
    return result.ToString();
}

public void SetWsOffshHd5AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 93 <= data.Length)
    {
        string extracted = data.Substring(offset, 93).Trim();
        SetFiller216(extracted);
    }
    offset += 93;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        SetFiller217(extracted);
    }
    offset += 9;
    if (offset + 18 <= data.Length)
    {
        string extracted = data.Substring(offset, 18).Trim();
        SetFiller218(extracted);
    }
    offset += 18;
    if (offset + 14 <= data.Length)
    {
        string extracted = data.Substring(offset, 14).Trim();
        SetFiller219(extracted);
    }
    offset += 14;
    if (offset + 19 <= data.Length)
    {
        string extracted = data.Substring(offset, 19).Trim();
        SetFiller220(extracted);
    }
    offset += 19;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetFiller221(extracted);
    }
    offset += 10;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller216()
{
    return _Filler216;
}

// Standard Setter
public void SetFiller216(string value)
{
    _Filler216 = value;
}

// Get<>AsString()
public string GetFiller216AsString()
{
    return _Filler216.PadRight(93);
}

// Set<>AsString()
public void SetFiller216AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler216 = value;
}

// Standard Getter
public string GetFiller217()
{
    return _Filler217;
}

// Standard Setter
public void SetFiller217(string value)
{
    _Filler217 = value;
}

// Get<>AsString()
public string GetFiller217AsString()
{
    return _Filler217.PadRight(9);
}

// Set<>AsString()
public void SetFiller217AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler217 = value;
}

// Standard Getter
public string GetFiller218()
{
    return _Filler218;
}

// Standard Setter
public void SetFiller218(string value)
{
    _Filler218 = value;
}

// Get<>AsString()
public string GetFiller218AsString()
{
    return _Filler218.PadRight(18);
}

// Set<>AsString()
public void SetFiller218AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler218 = value;
}

// Standard Getter
public string GetFiller219()
{
    return _Filler219;
}

// Standard Setter
public void SetFiller219(string value)
{
    _Filler219 = value;
}

// Get<>AsString()
public string GetFiller219AsString()
{
    return _Filler219.PadRight(14);
}

// Set<>AsString()
public void SetFiller219AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler219 = value;
}

// Standard Getter
public string GetFiller220()
{
    return _Filler220;
}

// Standard Setter
public void SetFiller220(string value)
{
    _Filler220 = value;
}

// Get<>AsString()
public string GetFiller220AsString()
{
    return _Filler220.PadRight(19);
}

// Set<>AsString()
public void SetFiller220AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler220 = value;
}

// Standard Getter
public string GetFiller221()
{
    return _Filler221;
}

// Standard Setter
public void SetFiller221(string value)
{
    _Filler221 = value;
}

// Get<>AsString()
public string GetFiller221AsString()
{
    return _Filler221.PadRight(10);
}

// Set<>AsString()
public void SetFiller221AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler221 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsOffshDtl(string value)
{
    _WsOffshDtl.SetWsOffshDtlAsString(value);
}
// Nested Class: WsOffshDtl
public class WsOffshDtl
{
    private static int _size = 147;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsOffshSedol, is_external=, is_static_class=False, static_prefix=
    private string _WsOffshSedol ="";
    
    
    
    
    // [DEBUG] Field: Filler222, is_external=, is_static_class=False, static_prefix=
    private string _Filler222 ="";
    
    
    
    
    // [DEBUG] Field: WsOffshSecurity, is_external=, is_static_class=False, static_prefix=
    private string _WsOffshSecurity ="";
    
    
    
    
    // [DEBUG] Field: Filler223, is_external=, is_static_class=False, static_prefix=
    private string _Filler223 ="";
    
    
    
    
    // [DEBUG] Field: WsOffshSecName, is_external=, is_static_class=False, static_prefix=
    private WsOffshDtl.WsOffshSecName _WsOffshSecName = new WsOffshDtl.WsOffshSecName();
    
    
    
    
    // [DEBUG] Field: Filler225, is_external=, is_static_class=False, static_prefix=
    private string _Filler225 ="";
    
    
    
    
    // [DEBUG] Field: WsOffshType, is_external=, is_static_class=False, static_prefix=
    private string _WsOffshType ="";
    
    
    
    
    // [DEBUG] Field: Filler226, is_external=, is_static_class=False, static_prefix=
    private string _Filler226 ="";
    
    
    
    
    // [DEBUG] Field: WsOffshBargDate, is_external=, is_static_class=False, static_prefix=
    private string _WsOffshBargDate ="";
    
    
    
    
    // [DEBUG] Field: Filler227, is_external=, is_static_class=False, static_prefix=
    private string _Filler227 ="";
    
    
    
    
    // [DEBUG] Field: WsOffshHoldings, is_external=, is_static_class=False, static_prefix=
    private decimal _WsOffshHoldings =0;
    
    
    
    
    // [DEBUG] Field: Filler228, is_external=, is_static_class=False, static_prefix=
    private string _Filler228 ="";
    
    
    
    
    // [DEBUG] Field: WsOffshCgt, is_external=, is_static_class=False, static_prefix=
    private decimal _WsOffshCgt =0;
    
    
    
    
    // [DEBUG] Field: Filler229, is_external=, is_static_class=False, static_prefix=
    private string _Filler229 ="";
    
    
    
    
    // [DEBUG] Field: WsOffshProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _WsOffshProceeds =0;
    
    
    
    
    // [DEBUG] Field: Filler230, is_external=, is_static_class=False, static_prefix=
    private string _Filler230 ="";
    
    
    
    
    // [DEBUG] Field: WsOffshSchedD, is_external=, is_static_class=False, static_prefix=
    private decimal _WsOffshSchedD =0;
    
    
    
    
public WsOffshDtl() {}

public WsOffshDtl(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsOffshSedol(data.Substring(offset, 9).Trim());
    offset += 9;
    SetFiller222(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsOffshSecurity(data.Substring(offset, 1).Trim());
    offset += 1;
    SetFiller223(data.Substring(offset, 1).Trim());
    offset += 1;
    _WsOffshSecName.SetWsOffshSecNameAsString(data.Substring(offset, WsOffshSecName.GetSize()));
    offset += 76;
    SetFiller225(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsOffshType(data.Substring(offset, 2).Trim());
    offset += 2;
    SetFiller226(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsOffshBargDate(data.Substring(offset, 7).Trim());
    offset += 7;
    SetFiller227(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsOffshHoldings(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
    offset += 11;
    SetFiller228(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsOffshCgt(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
    offset += 11;
    SetFiller229(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsOffshProceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
    offset += 11;
    SetFiller230(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsOffshSchedD(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
    offset += 11;
    
}

// Serialization methods
public string GetWsOffshDtlAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsOffshSedol.PadRight(9));
    result.Append(_Filler222.PadRight(1));
    result.Append(_WsOffshSecurity.PadRight(1));
    result.Append(_Filler223.PadRight(1));
    result.Append(_WsOffshSecName.GetWsOffshSecNameAsString());
    result.Append(_Filler225.PadRight(1));
    result.Append(_WsOffshType.PadRight(2));
    result.Append(_Filler226.PadRight(1));
    result.Append(_WsOffshBargDate.PadRight(7));
    result.Append(_Filler227.PadRight(1));
    result.Append(_WsOffshHoldings.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler228.PadRight(1));
    result.Append(_WsOffshCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler229.PadRight(1));
    result.Append(_WsOffshProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler230.PadRight(1));
    result.Append(_WsOffshSchedD.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetWsOffshDtlAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        SetWsOffshSedol(extracted);
    }
    offset += 9;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller222(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWsOffshSecurity(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller223(extracted);
    }
    offset += 1;
    if (offset + 76 <= data.Length)
    {
        _WsOffshSecName.SetWsOffshSecNameAsString(data.Substring(offset, 76));
    }
    else
    {
        _WsOffshSecName.SetWsOffshSecNameAsString(data.Substring(offset));
    }
    offset += 76;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller225(extracted);
    }
    offset += 1;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWsOffshType(extracted);
    }
    offset += 2;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller226(extracted);
    }
    offset += 1;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetWsOffshBargDate(extracted);
    }
    offset += 7;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller227(extracted);
    }
    offset += 1;
    if (offset + 11 <= data.Length)
    {
        string extracted = data.Substring(offset, 11).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsOffshHoldings(parsedDec);
    }
    offset += 11;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller228(extracted);
    }
    offset += 1;
    if (offset + 11 <= data.Length)
    {
        string extracted = data.Substring(offset, 11).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsOffshCgt(parsedDec);
    }
    offset += 11;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller229(extracted);
    }
    offset += 1;
    if (offset + 11 <= data.Length)
    {
        string extracted = data.Substring(offset, 11).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsOffshProceeds(parsedDec);
    }
    offset += 11;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller230(extracted);
    }
    offset += 1;
    if (offset + 11 <= data.Length)
    {
        string extracted = data.Substring(offset, 11).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsOffshSchedD(parsedDec);
    }
    offset += 11;
}

// Getter and Setter methods

// Standard Getter
public string GetWsOffshSedol()
{
    return _WsOffshSedol;
}

// Standard Setter
public void SetWsOffshSedol(string value)
{
    _WsOffshSedol = value;
}

// Get<>AsString()
public string GetWsOffshSedolAsString()
{
    return _WsOffshSedol.PadRight(9);
}

// Set<>AsString()
public void SetWsOffshSedolAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsOffshSedol = value;
}

// Standard Getter
public string GetFiller222()
{
    return _Filler222;
}

// Standard Setter
public void SetFiller222(string value)
{
    _Filler222 = value;
}

// Get<>AsString()
public string GetFiller222AsString()
{
    return _Filler222.PadRight(1);
}

// Set<>AsString()
public void SetFiller222AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler222 = value;
}

// Standard Getter
public string GetWsOffshSecurity()
{
    return _WsOffshSecurity;
}

// Standard Setter
public void SetWsOffshSecurity(string value)
{
    _WsOffshSecurity = value;
}

// Get<>AsString()
public string GetWsOffshSecurityAsString()
{
    return _WsOffshSecurity.PadRight(1);
}

// Set<>AsString()
public void SetWsOffshSecurityAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsOffshSecurity = value;
}

// Standard Getter
public string GetFiller223()
{
    return _Filler223;
}

// Standard Setter
public void SetFiller223(string value)
{
    _Filler223 = value;
}

// Get<>AsString()
public string GetFiller223AsString()
{
    return _Filler223.PadRight(1);
}

// Set<>AsString()
public void SetFiller223AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler223 = value;
}

// Standard Getter
public WsOffshSecName GetWsOffshSecName()
{
    return _WsOffshSecName;
}

// Standard Setter
public void SetWsOffshSecName(WsOffshSecName value)
{
    _WsOffshSecName = value;
}

// Get<>AsString()
public string GetWsOffshSecNameAsString()
{
    return _WsOffshSecName != null ? _WsOffshSecName.GetWsOffshSecNameAsString() : "";
}

// Set<>AsString()
public void SetWsOffshSecNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WsOffshSecName == null)
    {
        _WsOffshSecName = new WsOffshSecName();
    }
    _WsOffshSecName.SetWsOffshSecNameAsString(value);
}

// Standard Getter
public string GetFiller225()
{
    return _Filler225;
}

// Standard Setter
public void SetFiller225(string value)
{
    _Filler225 = value;
}

// Get<>AsString()
public string GetFiller225AsString()
{
    return _Filler225.PadRight(1);
}

// Set<>AsString()
public void SetFiller225AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler225 = value;
}

// Standard Getter
public string GetWsOffshType()
{
    return _WsOffshType;
}

// Standard Setter
public void SetWsOffshType(string value)
{
    _WsOffshType = value;
}

// Get<>AsString()
public string GetWsOffshTypeAsString()
{
    return _WsOffshType.PadRight(2);
}

// Set<>AsString()
public void SetWsOffshTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsOffshType = value;
}

// Standard Getter
public string GetFiller226()
{
    return _Filler226;
}

// Standard Setter
public void SetFiller226(string value)
{
    _Filler226 = value;
}

// Get<>AsString()
public string GetFiller226AsString()
{
    return _Filler226.PadRight(1);
}

// Set<>AsString()
public void SetFiller226AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler226 = value;
}

// Standard Getter
public string GetWsOffshBargDate()
{
    return _WsOffshBargDate;
}

// Standard Setter
public void SetWsOffshBargDate(string value)
{
    _WsOffshBargDate = value;
}

// Get<>AsString()
public string GetWsOffshBargDateAsString()
{
    return _WsOffshBargDate.PadRight(7);
}

// Set<>AsString()
public void SetWsOffshBargDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsOffshBargDate = value;
}

// Standard Getter
public string GetFiller227()
{
    return _Filler227;
}

// Standard Setter
public void SetFiller227(string value)
{
    _Filler227 = value;
}

// Get<>AsString()
public string GetFiller227AsString()
{
    return _Filler227.PadRight(1);
}

// Set<>AsString()
public void SetFiller227AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler227 = value;
}

// Standard Getter
public decimal GetWsOffshHoldings()
{
    return _WsOffshHoldings;
}

// Standard Setter
public void SetWsOffshHoldings(decimal value)
{
    _WsOffshHoldings = value;
}

// Get<>AsString()
public string GetWsOffshHoldingsAsString()
{
    return _WsOffshHoldings.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsOffshHoldingsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsOffshHoldings = parsed;
}

// Standard Getter
public string GetFiller228()
{
    return _Filler228;
}

// Standard Setter
public void SetFiller228(string value)
{
    _Filler228 = value;
}

// Get<>AsString()
public string GetFiller228AsString()
{
    return _Filler228.PadRight(1);
}

// Set<>AsString()
public void SetFiller228AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler228 = value;
}

// Standard Getter
public decimal GetWsOffshCgt()
{
    return _WsOffshCgt;
}

// Standard Setter
public void SetWsOffshCgt(decimal value)
{
    _WsOffshCgt = value;
}

// Get<>AsString()
public string GetWsOffshCgtAsString()
{
    return _WsOffshCgt.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsOffshCgtAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsOffshCgt = parsed;
}

// Standard Getter
public string GetFiller229()
{
    return _Filler229;
}

// Standard Setter
public void SetFiller229(string value)
{
    _Filler229 = value;
}

// Get<>AsString()
public string GetFiller229AsString()
{
    return _Filler229.PadRight(1);
}

// Set<>AsString()
public void SetFiller229AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler229 = value;
}

// Standard Getter
public decimal GetWsOffshProceeds()
{
    return _WsOffshProceeds;
}

// Standard Setter
public void SetWsOffshProceeds(decimal value)
{
    _WsOffshProceeds = value;
}

// Get<>AsString()
public string GetWsOffshProceedsAsString()
{
    return _WsOffshProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsOffshProceedsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsOffshProceeds = parsed;
}

// Standard Getter
public string GetFiller230()
{
    return _Filler230;
}

// Standard Setter
public void SetFiller230(string value)
{
    _Filler230 = value;
}

// Get<>AsString()
public string GetFiller230AsString()
{
    return _Filler230.PadRight(1);
}

// Set<>AsString()
public void SetFiller230AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler230 = value;
}

// Standard Getter
public decimal GetWsOffshSchedD()
{
    return _WsOffshSchedD;
}

// Standard Setter
public void SetWsOffshSchedD(decimal value)
{
    _WsOffshSchedD = value;
}

// Get<>AsString()
public string GetWsOffshSchedDAsString()
{
    return _WsOffshSchedD.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsOffshSchedDAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsOffshSchedD = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WsOffshSecName
public class WsOffshSecName
{
    private static int _size = 76;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsOffshIssuer, is_external=, is_static_class=False, static_prefix=
    private string _WsOffshIssuer ="";
    
    
    
    
    // [DEBUG] Field: Filler224, is_external=, is_static_class=False, static_prefix=
    private string _Filler224 ="";
    
    
    
    
    // [DEBUG] Field: WsOffshStDesc, is_external=, is_static_class=False, static_prefix=
    private string _WsOffshStDesc ="";
    
    
    
    
public WsOffshSecName() {}

public WsOffshSecName(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsOffshIssuer(data.Substring(offset, 35).Trim());
    offset += 35;
    SetFiller224(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsOffshStDesc(data.Substring(offset, 40).Trim());
    offset += 40;
    
}

// Serialization methods
public string GetWsOffshSecNameAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsOffshIssuer.PadRight(35));
    result.Append(_Filler224.PadRight(1));
    result.Append(_WsOffshStDesc.PadRight(40));
    
    return result.ToString();
}

public void SetWsOffshSecNameAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 35 <= data.Length)
    {
        string extracted = data.Substring(offset, 35).Trim();
        SetWsOffshIssuer(extracted);
    }
    offset += 35;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller224(extracted);
    }
    offset += 1;
    if (offset + 40 <= data.Length)
    {
        string extracted = data.Substring(offset, 40).Trim();
        SetWsOffshStDesc(extracted);
    }
    offset += 40;
}

// Getter and Setter methods

// Standard Getter
public string GetWsOffshIssuer()
{
    return _WsOffshIssuer;
}

// Standard Setter
public void SetWsOffshIssuer(string value)
{
    _WsOffshIssuer = value;
}

// Get<>AsString()
public string GetWsOffshIssuerAsString()
{
    return _WsOffshIssuer.PadRight(35);
}

// Set<>AsString()
public void SetWsOffshIssuerAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsOffshIssuer = value;
}

// Standard Getter
public string GetFiller224()
{
    return _Filler224;
}

// Standard Setter
public void SetFiller224(string value)
{
    _Filler224 = value;
}

// Get<>AsString()
public string GetFiller224AsString()
{
    return _Filler224.PadRight(1);
}

// Set<>AsString()
public void SetFiller224AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler224 = value;
}

// Standard Getter
public string GetWsOffshStDesc()
{
    return _WsOffshStDesc;
}

// Standard Setter
public void SetWsOffshStDesc(string value)
{
    _WsOffshStDesc = value;
}

// Get<>AsString()
public string GetWsOffshStDescAsString()
{
    return _WsOffshStDesc.PadRight(40);
}

// Set<>AsString()
public void SetWsOffshStDescAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsOffshStDesc = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetWsOffshTotLine(string value)
{
    _WsOffshTotLine.SetWsOffshTotLineAsString(value);
}
// Nested Class: WsOffshTotLine
public class WsOffshTotLine
{
    private static int _size = 151;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler231, is_external=, is_static_class=False, static_prefix=
    private string _Filler231 ="";
    
    
    
    
    // [DEBUG] Field: WsOffshTotText, is_external=, is_static_class=False, static_prefix=
    private string _WsOffshTotText ="";
    
    
    
    
    // [DEBUG] Field: Filler232, is_external=, is_static_class=False, static_prefix=
    private string _Filler232 ="";
    
    
    
    
    // [DEBUG] Field: WsOffshCgtTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsOffshCgtTot =0;
    
    
    
    
    // [DEBUG] Field: WsOffshProceedsTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsOffshProceedsTot =0;
    
    
    
    
    // [DEBUG] Field: WsOffshSchedDTot, is_external=, is_static_class=False, static_prefix=
    private decimal _WsOffshSchedDTot =0;
    
    
    
    
public WsOffshTotLine() {}

public WsOffshTotLine(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller231(data.Substring(offset, 69).Trim());
    offset += 69;
    SetWsOffshTotText(data.Substring(offset, 26).Trim());
    offset += 26;
    SetFiller232(data.Substring(offset, 20).Trim());
    offset += 20;
    SetWsOffshCgtTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetWsOffshProceedsTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    SetWsOffshSchedDTot(PackedDecimalConverter.ToDecimal(data.Substring(offset, 12)));
    offset += 12;
    
}

// Serialization methods
public string GetWsOffshTotLineAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler231.PadRight(69));
    result.Append(_WsOffshTotText.PadRight(26));
    result.Append(_Filler232.PadRight(20));
    result.Append(_WsOffshCgtTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WsOffshProceedsTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WsOffshSchedDTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetWsOffshTotLineAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 69 <= data.Length)
    {
        string extracted = data.Substring(offset, 69).Trim();
        SetFiller231(extracted);
    }
    offset += 69;
    if (offset + 26 <= data.Length)
    {
        string extracted = data.Substring(offset, 26).Trim();
        SetWsOffshTotText(extracted);
    }
    offset += 26;
    if (offset + 20 <= data.Length)
    {
        string extracted = data.Substring(offset, 20).Trim();
        SetFiller232(extracted);
    }
    offset += 20;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsOffshCgtTot(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsOffshProceedsTot(parsedDec);
    }
    offset += 12;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWsOffshSchedDTot(parsedDec);
    }
    offset += 12;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller231()
{
    return _Filler231;
}

// Standard Setter
public void SetFiller231(string value)
{
    _Filler231 = value;
}

// Get<>AsString()
public string GetFiller231AsString()
{
    return _Filler231.PadRight(69);
}

// Set<>AsString()
public void SetFiller231AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler231 = value;
}

// Standard Getter
public string GetWsOffshTotText()
{
    return _WsOffshTotText;
}

// Standard Setter
public void SetWsOffshTotText(string value)
{
    _WsOffshTotText = value;
}

// Get<>AsString()
public string GetWsOffshTotTextAsString()
{
    return _WsOffshTotText.PadRight(26);
}

// Set<>AsString()
public void SetWsOffshTotTextAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsOffshTotText = value;
}

// Standard Getter
public string GetFiller232()
{
    return _Filler232;
}

// Standard Setter
public void SetFiller232(string value)
{
    _Filler232 = value;
}

// Get<>AsString()
public string GetFiller232AsString()
{
    return _Filler232.PadRight(20);
}

// Set<>AsString()
public void SetFiller232AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler232 = value;
}

// Standard Getter
public decimal GetWsOffshCgtTot()
{
    return _WsOffshCgtTot;
}

// Standard Setter
public void SetWsOffshCgtTot(decimal value)
{
    _WsOffshCgtTot = value;
}

// Get<>AsString()
public string GetWsOffshCgtTotAsString()
{
    return _WsOffshCgtTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsOffshCgtTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsOffshCgtTot = parsed;
}

// Standard Getter
public decimal GetWsOffshProceedsTot()
{
    return _WsOffshProceedsTot;
}

// Standard Setter
public void SetWsOffshProceedsTot(decimal value)
{
    _WsOffshProceedsTot = value;
}

// Get<>AsString()
public string GetWsOffshProceedsTotAsString()
{
    return _WsOffshProceedsTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsOffshProceedsTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsOffshProceedsTot = parsed;
}

// Standard Getter
public decimal GetWsOffshSchedDTot()
{
    return _WsOffshSchedDTot;
}

// Standard Setter
public void SetWsOffshSchedDTot(decimal value)
{
    _WsOffshSchedDTot = value;
}

// Get<>AsString()
public string GetWsOffshSchedDTotAsString()
{
    return _WsOffshSchedDTot.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWsOffshSchedDTotAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsOffshSchedDTot = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsConvSedol(string value)
{
    _WsConvSedol.SetWsConvSedolAsString(value);
}
// Nested Class: WsConvSedol
public class WsConvSedol
{
    private static int _size = 7;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsCSedol1, is_external=, is_static_class=False, static_prefix=
    private string _WsCSedol1 ="";
    
    
    
    
    // [DEBUG] Field: WsCSedol24, is_external=, is_static_class=False, static_prefix=
    private string _WsCSedol24 ="";
    
    
    
    
    // [DEBUG] Field: WsCSedol57, is_external=, is_static_class=False, static_prefix=
    private string _WsCSedol57 ="";
    
    
    
    
public WsConvSedol() {}

public WsConvSedol(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsCSedol1(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWsCSedol24(data.Substring(offset, 3).Trim());
    offset += 3;
    SetWsCSedol57(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetWsConvSedolAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsCSedol1.PadRight(1));
    result.Append(_WsCSedol24.PadRight(3));
    result.Append(_WsCSedol57.PadRight(3));
    
    return result.ToString();
}

public void SetWsConvSedolAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWsCSedol1(extracted);
    }
    offset += 1;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetWsCSedol24(extracted);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetWsCSedol57(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetWsCSedol1()
{
    return _WsCSedol1;
}

// Standard Setter
public void SetWsCSedol1(string value)
{
    _WsCSedol1 = value;
}

// Get<>AsString()
public string GetWsCSedol1AsString()
{
    return _WsCSedol1.PadRight(1);
}

// Set<>AsString()
public void SetWsCSedol1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsCSedol1 = value;
}

// Standard Getter
public string GetWsCSedol24()
{
    return _WsCSedol24;
}

// Standard Setter
public void SetWsCSedol24(string value)
{
    _WsCSedol24 = value;
}

// Get<>AsString()
public string GetWsCSedol24AsString()
{
    return _WsCSedol24.PadRight(3);
}

// Set<>AsString()
public void SetWsCSedol24AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsCSedol24 = value;
}

// Standard Getter
public string GetWsCSedol57()
{
    return _WsCSedol57;
}

// Standard Setter
public void SetWsCSedol57(string value)
{
    _WsCSedol57 = value;
}

// Get<>AsString()
public string GetWsCSedol57AsString()
{
    return _WsCSedol57.PadRight(3);
}

// Set<>AsString()
public void SetWsCSedol57AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsCSedol57 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsDateConv(string value)
{
    _WsDateConv.SetWsDateConvAsString(value);
}
// Nested Class: WsDateConv
public class WsDateConv
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsYy, is_external=, is_static_class=False, static_prefix=
    private int _WsYy =0;
    
    
    
    
    // [DEBUG] Field: WsMm, is_external=, is_static_class=False, static_prefix=
    private int _WsMm =0;
    
    
    
    
    // [DEBUG] Field: WsDd, is_external=, is_static_class=False, static_prefix=
    private int _WsDd =0;
    
    
    
    
public WsDateConv() {}

public WsDateConv(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWsMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWsDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWsDateConvAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsYy.ToString().PadLeft(2, '0'));
    result.Append(_WsMm.ToString().PadLeft(2, '0'));
    result.Append(_WsDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWsDateConvAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWsYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWsMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWsDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWsYy()
{
    return _WsYy;
}

// Standard Setter
public void SetWsYy(int value)
{
    _WsYy = value;
}

// Get<>AsString()
public string GetWsYyAsString()
{
    return _WsYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWsYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WsYy = parsed;
}

// Standard Getter
public int GetWsMm()
{
    return _WsMm;
}

// Standard Setter
public void SetWsMm(int value)
{
    _WsMm = value;
}

// Get<>AsString()
public string GetWsMmAsString()
{
    return _WsMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWsMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WsMm = parsed;
}

// Standard Getter
public int GetWsDd()
{
    return _WsDd;
}

// Standard Setter
public void SetWsDd(int value)
{
    _WsDd = value;
}

// Get<>AsString()
public string GetWsDdAsString()
{
    return _WsDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWsDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WsDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetWsDateOut(string value)
{
    _WsDateOut.SetWsDateOutAsString(value);
}
// Nested Class: WsDateOut
public class WsDateOut
{
    private static int _size = 7;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WsDay, is_external=, is_static_class=False, static_prefix=
    private int _WsDay =0;
    
    
    
    
    // [DEBUG] Field: WsMonth, is_external=, is_static_class=False, static_prefix=
    private string _WsMonth ="";
    
    
    
    
    // [DEBUG] Field: WsYear, is_external=, is_static_class=False, static_prefix=
    private int _WsYear =0;
    
    
    
    
public WsDateOut() {}

public WsDateOut(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWsDay(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWsMonth(data.Substring(offset, 3).Trim());
    offset += 3;
    SetWsYear(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWsDateOutAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WsDay.ToString().PadLeft(2, '0'));
    result.Append(_WsMonth.PadRight(3));
    result.Append(_WsYear.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWsDateOutAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWsDay(parsedInt);
    }
    offset += 2;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetWsMonth(extracted);
    }
    offset += 3;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWsYear(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWsDay()
{
    return _WsDay;
}

// Standard Setter
public void SetWsDay(int value)
{
    _WsDay = value;
}

// Get<>AsString()
public string GetWsDayAsString()
{
    return _WsDay.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWsDayAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WsDay = parsed;
}

// Standard Getter
public string GetWsMonth()
{
    return _WsMonth;
}

// Standard Setter
public void SetWsMonth(string value)
{
    _WsMonth = value;
}

// Get<>AsString()
public string GetWsMonthAsString()
{
    return _WsMonth.PadRight(3);
}

// Set<>AsString()
public void SetWsMonthAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WsMonth = value;
}

// Standard Getter
public int GetWsYear()
{
    return _WsYear;
}

// Standard Setter
public void SetWsYear(int value)
{
    _WsYear = value;
}

// Get<>AsString()
public string GetWsYearAsString()
{
    return _WsYear.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWsYearAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WsYear = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetMonthTable(string value)
{
    _MonthTable.SetMonthTableAsString(value);
}
// Nested Class: MonthTable
public class MonthTable
{
    private static int _size = 36;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler233, is_external=, is_static_class=False, static_prefix=
    private string _Filler233 ="JAN";
    
    
    
    
    // [DEBUG] Field: Filler234, is_external=, is_static_class=False, static_prefix=
    private string _Filler234 ="FEB";
    
    
    
    
    // [DEBUG] Field: Filler235, is_external=, is_static_class=False, static_prefix=
    private string _Filler235 ="MAR";
    
    
    
    
    // [DEBUG] Field: Filler236, is_external=, is_static_class=False, static_prefix=
    private string _Filler236 ="APR";
    
    
    
    
    // [DEBUG] Field: Filler237, is_external=, is_static_class=False, static_prefix=
    private string _Filler237 ="MAY";
    
    
    
    
    // [DEBUG] Field: Filler238, is_external=, is_static_class=False, static_prefix=
    private string _Filler238 ="JUN";
    
    
    
    
    // [DEBUG] Field: Filler239, is_external=, is_static_class=False, static_prefix=
    private string _Filler239 ="JUL";
    
    
    
    
    // [DEBUG] Field: Filler240, is_external=, is_static_class=False, static_prefix=
    private string _Filler240 ="AUG";
    
    
    
    
    // [DEBUG] Field: Filler241, is_external=, is_static_class=False, static_prefix=
    private string _Filler241 ="SEP";
    
    
    
    
    // [DEBUG] Field: Filler242, is_external=, is_static_class=False, static_prefix=
    private string _Filler242 ="OCT";
    
    
    
    
    // [DEBUG] Field: Filler243, is_external=, is_static_class=False, static_prefix=
    private string _Filler243 ="NOV";
    
    
    
    
    // [DEBUG] Field: Filler244, is_external=, is_static_class=False, static_prefix=
    private string _Filler244 ="DEC";
    
    
    
    
public MonthTable() {}

public MonthTable(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller233(data.Substring(offset, 3).Trim());
    offset += 3;
    SetFiller234(data.Substring(offset, 3).Trim());
    offset += 3;
    SetFiller235(data.Substring(offset, 3).Trim());
    offset += 3;
    SetFiller236(data.Substring(offset, 3).Trim());
    offset += 3;
    SetFiller237(data.Substring(offset, 3).Trim());
    offset += 3;
    SetFiller238(data.Substring(offset, 3).Trim());
    offset += 3;
    SetFiller239(data.Substring(offset, 3).Trim());
    offset += 3;
    SetFiller240(data.Substring(offset, 3).Trim());
    offset += 3;
    SetFiller241(data.Substring(offset, 3).Trim());
    offset += 3;
    SetFiller242(data.Substring(offset, 3).Trim());
    offset += 3;
    SetFiller243(data.Substring(offset, 3).Trim());
    offset += 3;
    SetFiller244(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetMonthTableAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler233.PadRight(3));
    result.Append(_Filler234.PadRight(3));
    result.Append(_Filler235.PadRight(3));
    result.Append(_Filler236.PadRight(3));
    result.Append(_Filler237.PadRight(3));
    result.Append(_Filler238.PadRight(3));
    result.Append(_Filler239.PadRight(3));
    result.Append(_Filler240.PadRight(3));
    result.Append(_Filler241.PadRight(3));
    result.Append(_Filler242.PadRight(3));
    result.Append(_Filler243.PadRight(3));
    result.Append(_Filler244.PadRight(3));
    
    return result.ToString();
}

public void SetMonthTableAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller233(extracted);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller234(extracted);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller235(extracted);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller236(extracted);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller237(extracted);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller238(extracted);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller239(extracted);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller240(extracted);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller241(extracted);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller242(extracted);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller243(extracted);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller244(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller233()
{
    return _Filler233;
}

// Standard Setter
public void SetFiller233(string value)
{
    _Filler233 = value;
}

// Get<>AsString()
public string GetFiller233AsString()
{
    return _Filler233.PadRight(3);
}

// Set<>AsString()
public void SetFiller233AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler233 = value;
}

// Standard Getter
public string GetFiller234()
{
    return _Filler234;
}

// Standard Setter
public void SetFiller234(string value)
{
    _Filler234 = value;
}

// Get<>AsString()
public string GetFiller234AsString()
{
    return _Filler234.PadRight(3);
}

// Set<>AsString()
public void SetFiller234AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler234 = value;
}

// Standard Getter
public string GetFiller235()
{
    return _Filler235;
}

// Standard Setter
public void SetFiller235(string value)
{
    _Filler235 = value;
}

// Get<>AsString()
public string GetFiller235AsString()
{
    return _Filler235.PadRight(3);
}

// Set<>AsString()
public void SetFiller235AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler235 = value;
}

// Standard Getter
public string GetFiller236()
{
    return _Filler236;
}

// Standard Setter
public void SetFiller236(string value)
{
    _Filler236 = value;
}

// Get<>AsString()
public string GetFiller236AsString()
{
    return _Filler236.PadRight(3);
}

// Set<>AsString()
public void SetFiller236AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler236 = value;
}

// Standard Getter
public string GetFiller237()
{
    return _Filler237;
}

// Standard Setter
public void SetFiller237(string value)
{
    _Filler237 = value;
}

// Get<>AsString()
public string GetFiller237AsString()
{
    return _Filler237.PadRight(3);
}

// Set<>AsString()
public void SetFiller237AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler237 = value;
}

// Standard Getter
public string GetFiller238()
{
    return _Filler238;
}

// Standard Setter
public void SetFiller238(string value)
{
    _Filler238 = value;
}

// Get<>AsString()
public string GetFiller238AsString()
{
    return _Filler238.PadRight(3);
}

// Set<>AsString()
public void SetFiller238AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler238 = value;
}

// Standard Getter
public string GetFiller239()
{
    return _Filler239;
}

// Standard Setter
public void SetFiller239(string value)
{
    _Filler239 = value;
}

// Get<>AsString()
public string GetFiller239AsString()
{
    return _Filler239.PadRight(3);
}

// Set<>AsString()
public void SetFiller239AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler239 = value;
}

// Standard Getter
public string GetFiller240()
{
    return _Filler240;
}

// Standard Setter
public void SetFiller240(string value)
{
    _Filler240 = value;
}

// Get<>AsString()
public string GetFiller240AsString()
{
    return _Filler240.PadRight(3);
}

// Set<>AsString()
public void SetFiller240AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler240 = value;
}

// Standard Getter
public string GetFiller241()
{
    return _Filler241;
}

// Standard Setter
public void SetFiller241(string value)
{
    _Filler241 = value;
}

// Get<>AsString()
public string GetFiller241AsString()
{
    return _Filler241.PadRight(3);
}

// Set<>AsString()
public void SetFiller241AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler241 = value;
}

// Standard Getter
public string GetFiller242()
{
    return _Filler242;
}

// Standard Setter
public void SetFiller242(string value)
{
    _Filler242 = value;
}

// Get<>AsString()
public string GetFiller242AsString()
{
    return _Filler242.PadRight(3);
}

// Set<>AsString()
public void SetFiller242AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler242 = value;
}

// Standard Getter
public string GetFiller243()
{
    return _Filler243;
}

// Standard Setter
public void SetFiller243(string value)
{
    _Filler243 = value;
}

// Get<>AsString()
public string GetFiller243AsString()
{
    return _Filler243.PadRight(3);
}

// Set<>AsString()
public void SetFiller243AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler243 = value;
}

// Standard Getter
public string GetFiller244()
{
    return _Filler244;
}

// Standard Setter
public void SetFiller244(string value)
{
    _Filler244 = value;
}

// Get<>AsString()
public string GetFiller244AsString()
{
    return _Filler244.PadRight(3);
}

// Set<>AsString()
public void SetFiller244AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler244 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetMonthTab(string value)
{
    _MonthTab.SetMonthTabAsString(value);
}
// Nested Class: MonthTab
public class MonthTab
{
    private static int _size = 3;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Month, is_external=, is_static_class=False, static_prefix=
    private string[] _Month = new string[12];
    
    
    
    
public MonthTab() {}

public MonthTab(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    for (int i = 0; i < 12; i++)
    {
        string value = data.Substring(offset, 3);
        _Month[i] = value.Trim();
        offset += 3;
    }
    
}

// Serialization methods
public string GetMonthTabAsString()
{
    StringBuilder result = new StringBuilder();
    
    for (int i = 0; i < 12; i++)
    {
        result.Append(_Month[i].PadRight(3));
    }
    
    return result.ToString();
}

public void SetMonthTabAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    for (int i = 0; i < 12; i++)
    {
        if (offset + 3 > data.Length) break;
        string val = data.Substring(offset, 3);
        
        _Month[i] = val.Trim();
        offset += 3;
    }
}

// Getter and Setter methods

// Array Accessors for Month
public string GetMonthAt(int index)
{
    return _Month[index];
}

public void SetMonthAt(int index, string value)
{
    _Month[index] = value;
}

public string GetMonthAsStringAt(int index)
{
    return _Month[index].PadRight(3);
}

public void SetMonthAsStringAt(int index, string value)
{
    if (string.IsNullOrEmpty(value)) return;
    _Month[index] = value;
}

// Flattened accessors (index 0)
public string GetMonth()
{
    return _Month != null && _Month.Length > 0
    ? _Month[0]
    : default(string);
}

public void SetMonth(string value)
{
    if (_Month == null || _Month.Length == 0)
    _Month = new string[1];
    _Month[0] = value;
}

public string GetMonthAsString()
{
    return _Month != null && _Month.Length > 0
    ? _Month[0].ToString()
    : string.Empty;
}

public void SetMonthAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    if (_Month == null || _Month.Length == 0)
    _Month = new string[1];
    
    _Month[0] = value;
}




public static int GetSize()
{
    return _size;
}

}

}}