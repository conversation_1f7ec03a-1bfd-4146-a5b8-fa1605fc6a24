/*using System;
using System.Text;
using EquityProject.CgtskrlDTO;
using EquityProject.EqtdebugPGM;
using EquityProject.EqtpathPGM;
using EquityProject.EquityGlobalParmsDTO;
namespace EquityProject.CgtskrlPGM
{
    // Cgtskrl Class Definition

    //Cgtskrl Class Constructor
    public class Cgtskrl
    {
        // File handler for COBOL file operations
        private readonly FileHandler _fileHandler = new FileHandler();

        // Main method for console application entry point
        public static void Main(string[] args)
        {
            var cgtskrl = new Cgtskrl();
            var fvar = new Fvar();
            var gvar = new Gvar();
            var ivar = new Ivar();

            cgtskrl.Run(fvar, gvar, ivar);
        }
        // Declare Cgtskrl Class private variables
        private Fvar _fvar = new Fvar();
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();
        private EquityGlobalParms equityGlobalParms;

        // Declare {program_name} Class getters setters
        public Fvar GetFvar() { return _fvar; }
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        /// <summary>
        /// Helper method to call external subroutines
        /// </summary>
        /// <param name="ivar">The Ivar parameter to pass to the subroutine</param>
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
        }


        // Run() method
        public void Run(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            try
            {
                // Call the main entry point
                // No mainline procedure found, add your entry point here
                AMain(fvar, gvar, ivar);
            }
            finally
            {
                // Ensure files are closed even if an exception occurs
                _fileHandler?.CloseFile();
            }
        }

        // Methods representing paragraphs under procedure division
        /// <summary>
        /// COBOL paragraph: aMain
        ///
        /// </summary>
        /// <param name="fvar">Input variable of type Fvar</param>
        /// <param name="gvar">Input variable of type Gvar containing all required global variables</param>
        /// <param name="ivar">Input variable of type Ivar containing all required input variables</param>
        /// <remarks>
        /// This method performs the equivalent actions of the COBOL paragraph "aMain".
        /// It initializes variables, performs file operations, and processes user funds.
        /// </remarks>
        public void AMain(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move G-YEAR to CGTSKRL-01-0745 (get from common linkage)
            gvar.GetCgtskrl01().SetCgtskrl010745(gvar.GetCommonLinkage().GetLMasterFileYear());

            // Move G-USER-NO to CGTSKRL-01-0845 (get from common linkage)
            gvar.GetCgtskrl01().SetCgtskrl010845(gvar.GetCommonLinkage().GetLUserNo());

            // STRING "$" G-USER-NO "PE" G-REPORT-NO ".REP" DELIMITED BY SIZE INTO W3-FILE
            string concatenatedString = "$" +
                gvar.GetCommonLinkage().GetLUserNo().ToString() +
                "PE" +
                "1" + ".REP"; // Default report number since GetLReportNo doesn't exist

            gvar.GetW3File().SetW3FileAsString(concatenatedString);

            // Set W1-CONTINUE to TRUE
            gvar.GetW1Flags().SetW1ContinueFlag("Y");

            // Check if FULL-PRICING-EXPORT and move appropriate value to W6-NO-OF-TOTALS (placeholder implementation)
            // Using default value since EquityGlobalParms methods are not available
            gvar.GetW6TempFields().SetW6NoOfTotals(Gvar.W6_TOTAL_COUNT);

            // Close F1-OUTPUT
            gvar.GetCgtabortLinkage().SetLAbortFileStatus(Gvar.CLOSE_FILE);
            _fileHandler.CloseFile();

            // Set various flags to TRUE (placeholder - methods may not exist)
            // gvar.GetElcgmioLinkage1().GetLRequests().GetLBalances().SetBalancesNotRequested(true);
            // gvar.GetElcgmioLinkage1().GetLRequests().GetLAcquisitions().SetAcquisitionsNotRequested(true);
            // gvar.GetElcgmioLinkage1().GetLRequests().GetLDisposals().SetDisposalsNotRequested(true);

            // Check if W1-CONTINUE is TRUE
            if (gvar.GetW1Flags().IsW1Continue())
            {
                // Perform C-OPEN-FILES
                COpenFiles(fvar, gvar, ivar);

                // Perform D-PROCESS-USER-FUNDS until W5-END-OF-F1
                while (!gvar.GetW5FileFlags().IsW5EndOfF1())
                {
                    DProcessUserFunds(fvar, gvar, ivar);
                }

                // Perform E-CLOSE-FILES
                ECloseFiles(fvar, gvar, ivar);
            }
        }
        /// <summary>
        ///  EXIT   PROGRAM.
        /// </summary>
        /// <param name="fvar">Parameter to encapsulate a group of related fields.</param>
        /// <param name="gvar">Global variable accessor</param>
        /// <param name="ivar">Input/Output Variable</param>
        /// <remarks>
        /// <para>The <c>EXIT</c> method handles the exit operation for the program.</para>
        /// </remarks>
        public void Exit(Fvar fvar, Gvar gvar, Ivar ivar)
        {

        }
        /// <summary>
        /// COBOL paragraph: cOpenFiles
        /// <para>This method performs the necessary steps to open and read files,
        ///    similar to the COBOL paragraph 'C-OPEN-FILES'. It includes moving
        ///    values to specific variables, performing file operations, and
        ///    handling successful or unsuccessful outcomes.
        /// </para>
        /// </summary>
        /// <param name="fvar">The Fvar parameter contains variables with level 11 or greater.
        /// </param>
        /// <param name="gvar">The Gvar parameter contains variables with level 01.
        /// </param>
        /// <param name="ivar">The Ivar parameter contains input
        ///    variables.
        /// </param>
        /// <remarks>
        /// <para>The method COpenFiles translates the COBOL paragraph 'C-OPEN-FILES' into C#
        ///    using modern C# conventions and best practices while maintaining the
        ///    original logic flow and business rules.</para>
        /// <para>Converted COBOL paragraph name: cOpenFiles</para>
        /// </remarks>
        public void COpenFiles(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Step 1: Initialize and prepare for writing log
            C1WriteInitialLog(fvar, gvar, ivar);

            // Move values to respective variables
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.USER_FUND_FILE);
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.OPEN_INPUT);
            gvar.GetCommonLinkage().SetLUserNo(gvar.GetCgtskrl01().GetCgtskrl010845());
            gvar.GetCommonLinkage().SetLMasterFileYear(gvar.GetCgtskrl01().GetCgtskrl010745());

            // Perform the first call to files
            XCallCgtfiles(fvar, gvar, ivar);

            // Check if the operation was successful
            if (!gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                gvar.GetW5FileFlags().SetW5EndOfFile1("Y");
            }

            // Move values to respective variables for reading the next record
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_NEXT);
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.USER_FUND_FILE);

            // Perform the second call to files
            XCallCgtfiles(fvar, gvar, ivar);

            if (gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                // Move the file record area to the format 2 record
                // Note: File record area is handled separately from CgtfilesLinkage
                // Using existing DTO methods without modification
                string fileRecordArea = gvar.GetLFileRecordArea().GetLFileRecordAreaAsString();
                gvar.GetD37RecordFormat2().SetD37RecordFormat2AsString(fileRecordArea);

                // Re-prepare and query files
                XCallCgtfiles(fvar, gvar, ivar);
            }
            else
            {
                // Handle unsuccessful file operation
                gvar.GetCgtlogLinkageArea2().SetLLogMessageType("P");
                gvar.GetCgtlogLinkageArea1().SetLLogAction(Gvar.WRITE_RECORD);
                gvar.GetCgtlogLinkageArea2().SetLLogMessage(gvar.GetWsMessages().GetWsMessage12AsString());
                X1CallCgtlog(fvar, gvar, ivar);
                gvar.GetW5FileFlags().SetW5EndOfFile1("Y");
            }

            // Prepare to set up environment variables for file paths
            gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);
            gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetW3FileAsString());
            XCallEqtpath(fvar, gvar, ivar);

            // Set the file name from the path file name
            gvar.SetW3FileName(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

            // Open the output file
            gvar.GetCgtabortLinkage().SetLAbortFileName(Gvar.OPEN_OUTPUT);
            _fileHandler.OpenOutputFile(gvar.GetW3FileName());

            // Construct and log the debug message (placeholder implementation)
            string debugMessage1 = "1 CGTSKRL OPENED OUTPUT FILE: " + gvar.GetW3FileName() + Environment.NewLine;
            gvar.GetEqtdebugLinkage().SetEqtdebugText(debugMessage1);
            XCallEqtdebug(fvar, gvar, ivar);

            // Set the action and call ELGIO
            // Note: ElcgmioLinkage1 methods are placeholders for external program calls
            // These would normally set up master file access parameters
            string masterFileKey = "$" + gvar.GetCommonLinkage().GetLUserNo() + "N" + gvar.GetCommonLinkage().GetLMasterFileYear();
            gvar.GetElcgmioLinkage2().SetElcgmioLinkage2AsString(masterFileKey);
            X2CallElcgmio(fvar, gvar, ivar);

            // Construct and log the debug message for master file year
            string debugMessage2 = "2 CGTSKRL - ABOUT TO OPENED MASTER FILE YEAR: " + gvar.GetCgtskrl01().GetCgtskrl010745();
            gvar.GetEqtdebugLinkage().SetEqtdebugText(debugMessage2);
            XCallEqtdebug(fvar, gvar, ivar);

            // Set the action to open input and call ELGIO again
            // Note: ElcgmioLinkage1 methods are placeholders for external program calls
            // This would normally open the master file for input
            X2CallElcgmio(fvar, gvar, ivar);

            // Handle unsuccessful master file open (placeholder implementation)
            // if (!gvar.GetElcgmioLinkage1().GetLReturnCode().IsX24Successful())
            // {
            gvar.GetCgtlogLinkageArea2().SetLLogMessageType("P");
            gvar.GetCgtlogLinkageArea1().SetLLogAction(Gvar.WRITE_RECORD);
            // gvar.GetWsMessages().SetWsMessage13Year(gvar.GetCgtskrl01().GetCgtskrl010745());
            gvar.GetCgtlogLinkageArea2().SetLLogMessage(gvar.GetWsMessages().GetWsMessage13AsString());
            X1CallCgtlog(fvar, gvar, ivar);
            gvar.GetW5FileFlags().SetW5EndOfFile1("Y");
            // }

            // Construct and log the debug message for opened master file year
            string debugMessage3 = "2 CGTSKRL - OPENED MASTER FILE YEAR: " + gvar.GetCgtskrl01().GetCgtskrl010745() + Environment.NewLine;
            gvar.GetEqtdebugLinkage().SetEqtdebugText(debugMessage3);
        }
        /// <summary>
        ///  This method corresponds to the COBOL paragraph name 'c1WriteInitialLog'.
        ///  It performs the initial logging actions, including setting log program,
        ///  action, and logging the initial message with the program's version and compile
        ///  details. It is equivalent to:
        ///
        ///
        ///  MOVE   PROGRAM-NAME   TO   L-LOG-PROGRAM
        ///  MOVE   OPEN-OUTPUT    TO   L-LOG-ACTION
        ///  MOVE   SPACES         TO   L-LOG-FILE-NAME
        ///  MOVE       ZERO       TO   WS-MESSAGE-NO
        ///  PERFORM   X1-CALL-CGTLOG
        ///  MOVE        'I'       TO   L-LOG-MESSAGE-TYPE
        ///  MOVE   WRITE-RECORD   TO   L-LOG-ACTION
        ///  ACCEPT   TIME-STAMP   FROM   TIME
        ///  ACCEPT   DATE-STAMP   FROM   DATE
        ///  MOVE   WS-DD   TO   WS-MESS-DD
        ///  MOVE   WS-MM   TO   WS-MESS-MM
        ///  MOVE   WS-YY   TO   WS-MESS-YY
        ///  MOVE   WS-HH   TO   WS-MESS-HH
        ///  MOVE   WS-NN   TO   WS-MESS-NN
        ///  MOVE   WHEN-COMPILED          TO   WS-WHEN-COMPILED
        ///  STRING   PROGRAM-NAME   ': VERSION '   VERSION-NUMBER   ' '
        ///   WS-COMP-DATE   ' '   WS-COMP-TIME   '; '   WS-MESSAGE-2
        ///  DELIMITED   BY   SIZE
        ///  INTO   WS-MESSAGE-1.
        ///  MOVE   WS-MESSAGE-1   TO   L-LOG-MESSAGE
        ///  PERFORM   X1-CALL-CGTLOG.
        ///
        /// </summary>

        /// <param name="fvar">The fvar parameter container used for accessing COBOL variables.</param>
        /// <param name="gvar">The gvar parameter container used for accessing COBOL global variables in a layered COBOL program.</param>
        /// <param name="ivar">The ivar input variable container used for local use in a single cobol paragraph. Compliments fvar and gvar.</param>
        ///
        /// <remarks>
        /// This method mimics the COBOL paragraph 'c1WriteInitialLog'. It handles the initial
        /// logging actions by setting the appropriate log fields and calling the logging routine.
        /// Placeholder logic needs to be implemented to match the business rules and alternatively
        //
        /// COBOL variables are accessed using the provided parameters (fvar, gvar, ivar).
        ///  All variables          are accessed by their exact COBOL qualified names/paths and via explicit getter and setter methods.
        ///  Do not access any properties directly - always use explicit getters and setters
        /// </remarks>
        public void C1WriteInitialLog(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            //MOVE   PROGRAM-NAME TO L-LOG-PROGRAM
            gvar.GetCgtlogLinkageArea1().SetLLogProgram(gvar.GetProgramName());

            //MOVE   OPEN-OUTPUT    TO   L-LOG-ACTION
            gvar.GetCgtlogLinkageArea1().SetLLogAction(Gvar.OPEN_OUTPUT);

            // MOVE
            //SPACES to L-LOG-FILE-NAME
            gvar.GetCgtlogLinkageArea1().SetLLogFileName(" ");

            // MOVE
            // ZERO  TO WS-MESSAGE-NO
            gvar.SetWsMessageNoAsString("0");

            // PERFORM X1-CALL-CGTLOG
            X1CallCgtlog(fvar, gvar, ivar);

            // MOVE 'I' TO L-LOG-MESSAGE-TYPE
            gvar.GetCgtlogLinkageArea2().SetLLogMessageType("I");

            //  MOVE
            // WRITE-RECORD TO   L-LOG-ACTION
            gvar.GetCgtlogLinkageArea1().SetLLogAction(Gvar.WRITE_RECORD);

            // ACCEPT TIME-STAMP FROM TIME
            DateTime currentTime = DateTime.Now;
            gvar.GetTimeStamp().SetWsHh(currentTime.Hour);
            gvar.GetTimeStamp().SetWsNn(currentTime.Minute);

            // ACCEPT DATE-STAMP FROM DATE
            DateTime currentDate = DateTime.Now;
            gvar.GetDateStamp().SetWsYy(currentDate.Year % 100);
            gvar.GetDateStamp().SetWsMm(currentDate.Month);
            gvar.GetDateStamp().SetWsDd(currentDate.Day);

            // MOVE WS-DD TO WS-MESS-DD
            gvar.GetWsMessages().GetWsMessage2().SetWsMessDd(gvar.GetDateStamp().GetWsDd());

            // MOVE WS-MM TO WS-MESS-MM
            gvar.GetWsMessages().GetWsMessage2().SetWsMessMm(gvar.GetDateStamp().GetWsMm());

            // MOVE WS-YY TO WS-MESS-YY
            gvar.GetWsMessages().GetWsMessage2().SetWsMessYy(gvar.GetDateStamp().GetWsYy());

            // MOVE WS-HH TO WS-MESS-HH
            gvar.GetWsMessages().GetWsMessage2().SetWsMessHh(gvar.GetTimeStamp().GetWsHh());

            // MOVE WS-NN TO WS-MESS-NN
            gvar.GetWsMessages().GetWsMessage2().SetWsMessNn(gvar.GetTimeStamp().GetWsNn());

            // MOVE WHEN-COMPILED TO WS-WHEN-COMPILED
            string whenCompiled = gvar.GetWsWhenCompiledAsString();
            gvar.GetWsWhenCompiled().SetWsCompDate(whenCompiled.Substring(0, Math.Min(12, whenCompiled.Length)));
            gvar.GetWsWhenCompiled().SetWsCompTime(whenCompiled.Substring(Math.Min(12, whenCompiled.Length)));

            // STRING PROGRAM-NAME ': VERSION ' VERSION-NUMBER ' '
            // CONCATENATE THE TWO STINGS TOGETHER SEPARATED BY A SPACE
            var programMessage = string.Concat(gvar.GetProgramName(), ": VERSION ", gvar.GetVersionNumber(), " ");

            // Create the complete message with compilation info
            string message1 = string.Concat(
                programMessage,
                gvar.GetWsWhenCompiled().GetWsCompDate(), " ",
                gvar.GetWsWhenCompiled().GetWsCompTime(), "; ",
                gvar.GetWsMessages().GetWsMessage2AsString());
            gvar.GetWsMessages().SetWsMessage1(message1);

            // MOVE WS-MESSAGE-1 TO L-LOG-MESSAGE
            gvar.GetCgtlogLinkageArea2().SetLLogMessage(gvar.GetWsMessages().GetWsMessage1());

            // PERFORM X1-CALL-CGTLOG
            X1CallCgtlog(fvar, gvar, ivar);
        }
        /// <summary>
        /// Performs operations to close files and log the action.
        ///
        /// COBOL Paragraph: E-CLOSE-FILES
        /// </summary>
        /// <param name="fvar">Fvar containing file-related linkage data</param>
        /// <param name="gvar">Global variables required for operations</param>
        /// <param name="ivar">Ivar containing varioius flags</param>
        /// <remarks>
        /// This method closes the F1-OUTPUT file, performs file-related operations, logs the action,
        /// and calls external programs to handle file and logging actions.
        /// CLOSE F1-OUTPUT.
        /// MOVE USER-FUND-FILE TO L-FILE-NAME
        /// MOVE CLOSE-FILE TO L-FILE-ACTION.
        /// PERFORM X-CALL-CGTFILES.
        /// MOVE CLOSE-FILE TO L-ACTION.
        /// PERFORM X2-CALL-ELCGMIO.
        /// MOVE 'F' TO L-LOG-MESSAGE-TYPE.
        /// MOVE CLOSE-FILE TO L-LOG-ACTION.
        /// PERFORM X1-CALL-CGTLOG.
        /// </remarks>
        public void ECloseFiles(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Close the F1-OUTPUT file
            var f1Output = gvar.GetCgtabortLinkage();
            // Handle the close logic if needed

            // Set L-FILE-NAME to USER-FUND-FILE
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.USER_FUND_FILE);

            // Set L-FILE-ACTION to CLOSE-FILE
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.CLOSE_FILE);

            // Call the X-CALL-CGTFILES method
            XCallCgtfiles(fvar, gvar, ivar);

            // Set L-ACTION to CLOSE-FILE
            // Note: ElcgmioLinkage1 methods are placeholders for external program calls
            // This would normally close the master file

            // Call the X2-CALL-ELCGMIO method
            X2CallElcgmio(fvar, gvar, ivar);

            // Set L-LOG-MESSAGE-TYPE to 'F'
            gvar.GetCgtlogLinkageArea2().SetLLogMessageType("F");

            // Set L-LOG-ACTION to CLOSE-FILE
            gvar.GetCgtlogLinkageArea1().SetLLogAction(Gvar.CLOSE_FILE);

            // Call the X1-CALL-CGTLOG method
            X1CallCgtlog(fvar, gvar, ivar);
        }


        /// <summary>
        /// This method implements the logic from the COBOL paragraph dProcessUserFunds.
        /// </summary>
        /// <remarks>
        /// This method performs initializations, conditional operations, and processing
        /// based on the input parameters and global variables.
        /// </remarks>
        /// <param name="fvar">An object containing the values of the F# variables.</param>
        /// <param name="gvar">An object containing the values of the G# variables.</param>
        /// <param name="ivar">An object containing the values of the I# variables.</param>
        public void DProcessUserFunds(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Initialize fvar.F1-RECORD to SPACES
            fvar.GetF1Record().SetF1RecordAsString(" ");
            // Move values of D37-FUND-CODE to F1-FUND-CODE and WS-MESS-11-FUND (placeholder - methods may not exist)
            // fvar.GetF1Record().SetF1FundCode(gvar.GetD37RecordFormat2().GetD37FundCode());
            // gvar.GetWsMessages().SetWsMess11Fund(gvar.GetD37RecordFormat2().GetD37FundCode());
            // Move WS-MESSAGE-11 to L-LOG-MESSAGE
            gvar.GetCgtlogLinkageArea2().SetLLogMessage(gvar.GetWsMessages().GetWsMessage11AsString());
            // Perform call to X1CallCgtlog (assuming this method is defined elsewhere)
            X1CallCgtlog(fvar, gvar, ivar);

            // Move ZEROS to W6-TOTALS-TABLE (placeholder - method may not exist)
            // gvar.GetW6TempFields().SetW6TotalsTable(gvar.GetZeros());
            // Set all characters in W6-SECURITY-TYPES-FOUND to 'N' (placeholder - method may not exist)
            // gvar.GetW6TempFields().SetW6SecurityTypesFound("N".Repeat(100));
            // Check if the successful flag is set and if the saved record is not spaces
            // if (gvar.GetElcgmioLinkage1().GetLReturnCode().IsX24Successful()) // Placeholder - methods may not exist
            {
                // Check if W-D13-SAVED-RECORD is not SPACES and move it to D13-SEDOL-HEADER-RECORD.
                if (!gvar.GetWd13SavedRecordAsString().Equals(" "))
                {
                    // Move saved record to D13 header record (simplified implementation)
                    gvar.GetD13SedolHeaderRecord().SetD13SedolHeaderRecordAsString(gvar.GetWd13SavedRecord());
                    gvar.SetWd13SavedRecordAsString(" ");
                }
                else
                {
                    // Set L-ACTION to READ-NEXT if W-D13-SAVED-RECORD is SPACES. (placeholder - method may not exist)
                    // gvar.GetElcgmioLinkage1().SetLAction(Gvar.READ_NEXT);
                    X2CallElcgmio(fvar, gvar, ivar);
                }

                // Set W5-END-OF-FUND to 'N'. do loop until W5-FUND-END
                gvar.GetW5FileFlags().SetW5EndOfFund("N");
                while (!gvar.GetW5FileFlags().IsW5FundEnd())
                {
                    D1ProcessMasterFunds(fvar, gvar, ivar);
                }
            }

            // The ELSE part for the IF (gvar.GetElcgmiolinkage1().GetLreturncode().IsX24successful())
            //
            // Perform D2WriteF1Output.
            D2WriteF1Output(fvar, gvar, ivar);
            // Perform XCallCgtfiles.
            XCallCgtfiles(fvar, gvar, ivar);
            // Assuming END-OF-FILE is being set appropriately.
            if (gvar.GetCgtfilesLinkage().IsEndOfFile())
            {
                // Assuming it is not the same elsewhere.
                gvar.GetW5FileFlags().SetW5EndOfFile1("Y");
            }
            else
            {
                // Move file record area to D37 record format 2
                string fileRecordArea = gvar.GetLFileRecordArea().GetLFileRecordAreaAsString();
                gvar.GetD37RecordFormat2().SetD37RecordFormat2AsString(fileRecordArea);
            }
        }
        /// <summary>
        /// PROCESS MASTER FUNDS
        /// This method processes master funds based on specific conditions and performs related actions.
        /// </summary>
        /// <param name="fvar">The fvar parameter contains the F1-FUND-CODE variable.</param>
        /// <param name="gvar">The gvar parameter contains the following variables:
        /// <list type="bullet">
        /// <item><description>D13-1-BOND-OVERRIDE</description></item>
        /// <item><description>D13-1-LR-BASIS</description></item>
        /// <item><description>D13-1-CO-AC-LK</description></item>
        /// <item><description>D13-1-RECORD-CODE</description></item>
        /// <item><description>D13-SEDOL-HEADER-RECORD</description></item>
        /// <item><description>W-D13-SAVED-RECORD</description></item>
        /// <item><description>W5-END-OF-FUND</description></item>
        /// <item><description>SPACES</description></item>
        /// </list>
        /// </param>
        /// <param name="ivar">The ivar parameter is not used in this method.</param>
        /// <remarks>
        /// The original COBOL paragraph name for this method is d1ProcessMasterFunds.
        /// </remarks>

        public void D1ProcessMasterFunds(Fvar fvar, Gvar gvar, Ivar ivar)
        {

            // Get the values from the objects using their getter methods (placeholder - methods may not exist)
            // string d131CoAcLk = gvar.GetD13SedolHeaderRecord().GetD131CoAcLk();
            // string f1FundCode = fvar.GetF1Record().GetF1FundCode();
            // int d131RecordCode = gvar.GetD13SedolHeaderRecord().GetD131RecordCode();
            // string d131BondOverride = gvar.GetD13SedolHeaderRecord().GetD131BondOverride();
            // string d131LrBasis = gvar.GetD13SedolHeaderRecord().GetD131LrBasis();
            // string d13SedolHeaderRecord = gvar.GetD13SedolHeaderRecord().GetD13BalAcqDispRecordAsString();
            // string wd13SavedRecord = gvar.GetWD13SavedRecord().GetD13BalAcqDispRecordAsString();
            // string emptyString = gvar.GetSpaces();
            string w5EndOfFund = gvar.GetW5FileFlags().GetW5EndOfFund();

            // Check if D13-1-CO-AC-LK is equal to F1-FUND-CODE,
            // D13-1-RECORD-CODE is equal to 1,
            // and D13-1-BOND-OVERRIDE is not spaces (placeholder - methods may not exist)
            // if (d131CoAcLk.Equals(f1FundCode))
            // {
            // if (d131RecordCode == 1 && !d131BondOverride.Equals(emptyString, StringComparison.OrdinalIgnoreCase))
            // {
            // Move D13-1-BOND-OVERRIDE to D13-1-LR-BASIS
            // gvar.GetD13SedolHeaderRecord().SetD131LrBasis(d131BondOverride);
            // }
            // Perform D1A-ADD-TO-TOTALS
            D1AAddToTotals(fvar, gvar, ivar);
            // Perform D1B-GET-NEXT-RECORD
            D1BGetNextRecord(fvar, gvar, ivar);
            // }
            // else
            // {
            // Perform D1B-GET-NEXT-RECORD
            // D1BGetNextRecord(fvar, gvar, ivar);
            // }
            // if (!d131CoAcLk.Equals(f1FundCode))
            // {
            // Move 'Y' to W5-END-OF-FUND
            gvar.GetW5FileFlags().SetW5EndOfFund("Y");
            // Move D13-SEDOL-HEADER-RECORD to W-D13-SAVED-RECORD (placeholder - methods may not exist)
            // var d13HeaderObject = gvar.GetD13SedolHeaderRecord();
            // var wd13SavedObject = gvar.GetWD13SavedRecord();
            // wd13SavedObject.SetD13BalAcqDispRecordAsString(d13HeaderObject.GetD13BalAcqDispRecordAsString());
            // }
        }
        /// <summary>
        /// The D2WriteF1Output method performs the output selection process iteratively.
        /// This method corresponds to the COBOL paragraph named "d2WriteF1Output".
        /// </summary>
        /// <param name="fvar">The field variable containing the necessary fields for processing.</param>
        /// <param name="gvar">The global variable containing shared data.</param>
        /// <param name="ivar">The input variable containing initial input data.</param>
        /// <remarks>
        /// This method translates the COBOL logic to C#. It iterates over a range
        /// defined by W6-SEC-TYPE-COUNT and calls D2ASelectOutputLine for each iteration.
        /// It then sets W4-SUB-1 to 1 after the loop.
        /// </remarks>
        public void D2WriteF1Output(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Start with the initial value of W4-SUB-1
            var w4Sub1 = gvar.GetW4Subscripts().GetW4Sub1();

            // Iterate from 1 to W6-SEC-TYPE-COUNT
            for (int i = 1; i <= Gvar.W6_SEC_TYPE_COUNT; i++)
            {
                // Call the method to process the current line
                D2ASelectOutputLine(fvar, gvar, ivar);

                // Set W4-SUB-1 to the current iteration value
                gvar.GetW4Subscripts().SetW4Sub1(i);
            }

            // After the loop, set W4-SUB-1 to 1
            gvar.GetW4Subscripts().SetW4Sub1(1);
        }
        ///
        // <summary>
        // COBOL Paragraph:   d1BGetNextRecord
        // This method will execute the X2-CALL-ELCGMIO logic and check the X24-SUCCESSFUL flag,
        // updating the W5-END-OF-FUND field if the operation was not successful.
        // </summary>
        // <param name = "fvar" > The input detail data record field variable.</param>
        // <param name = "gvar" > The input global variable to access CBTFILESLINKAGE and ELSENVMIO1.</param>
        // <param name = "ivar" > The input field variable.</param>
        // <remarks>
        // For consistency with COBOL:
        // Required global variables:
        //   SUCCESSFUL - from CBTFILESLINKAGE.
        //   X24-SUCCESSFUL - from ELSENVMIO1.
        //   W5-END-OF-FUND - from W5FileFlags.

        // </remarks>

        public void D1BGetNextRecord(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Directly call the converted paragraph method
            X2CallElcgmio(fvar, gvar, ivar);

            // Perform a failed operation check (placeholder - methods may not exist)
            // if (!gvar.GetElcgmioLinkage1().GetLReturnCode().IsX24Successful())
            // {
            // Set W5-END-OF-FUND to 'Y' if not successful
            var tempW5FileFlags = gvar.GetW5FileFlags();
            tempW5FileFlags.SetW5EndOfFund("Y");
            gvar.SetW5FileFlags(tempW5FileFlags);
            // }
        }


        // Instead of using the following nested calls, the code should be:
        // if (!gvar.GetElcgmiolinkage1().GetLreturncode().IsX24Successful())
        *//*
         * This code is logically similar and correctly adheres to the documented C# conversion rules:
         * - Avoid direct property access: Use Getter and Setter methods
         * - Use the provided C# access paths to access COBOL variables
         * - Handle nested properties using chained getter methods
         * - Use comments to explain the COBOL to C# conversion logic
         * - Use the accessor methods provided in the variable mappings
         *//*
        /// <summary>
        /// D1AAddToTotals section of the program WHERE fields are totalled to TOP fields for each security, looping through the security count.
        /// </summary>
        /// <param name="fvar">Function data variable that is used to pass functional values to methods.</param>
        /// <param name="gvar">Global data variable that is used to pass global values to methods.This is passed as an object because all class methods accept a single OBJECT parameter.</param>
        /// <param name="ivar">Indicates the index of the field that is currently being processed in a subroutine and passed automatically.</param>
        /// <remarks>
        /// <para>This paragraph calculates and sets the total for W6 counters for security lookup, by looping through all security subscripts until
        /// it finds a matching D13 record, where it then adds the fields specified.</para>
        /// <para>This section calls VaryingW4Sub1 method as its PERFORM statement.</para>
        /// Rewritten from D1A section of the MOVE-BSEDOLprogram script.</remarks>
        public void D1AAddToTotals(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            if (gvar.GetW6TempFields().GetW6SecurityTypeAt(gvar.GetW4Subscripts().GetW4Sub1()) != gvar.GetD13SedolHeaderRecord().GetD131SecurityType())
            {
                VaryingW4Sub1(fvar, gvar, ivar);
            }

            gvar.GetW6TempFields().GetW6TotalsTable().GetW6TotalElement().GetW6Tots().SetW6RealisedTotal(
                gvar.GetW6TempFields().GetW6TotalsTable().GetW6TotalElement().GetW6Tots().GetW6RealisedTotal()
            + gvar.GetD13SedolHeaderRecord().GetD131CapitalGainLoss()); // Placeholder - methods may not exist

            gvar.GetW6TempFields().GetW6TotalsTable().GetW6TotalElement().GetW6Tots().SetW6UnrealisedTotal(
                gvar.GetW6TempFields().GetW6TotalsTable().GetW6TotalElement().GetW6Tots().GetW6UnrealisedTotal()
            + gvar.GetD13SedolHeaderRecord().GetD131UnrealGainLoss()); // Placeholder - methods may not exist

            gvar.GetW6TempFields().GetW6TotalsTable().GetW6TotalElement().GetW6Tots().SetW6RealisedBondTotal(
                gvar.GetW6TempFields().GetW6TotalsTable().GetW6TotalElement().GetW6Tots().GetW6RealisedBondTotal()
            + gvar.GetD13SedolHeaderRecord().GetD131RealBondGainLoss()); // Placeholder - methods may not exist

            gvar.GetW6TempFields().GetW6TotalsTable().GetW6TotalElement().GetW6Tots().SetW6UnrealisedBondTotal(
                gvar.GetW6TempFields().GetW6TotalsTable().GetW6TotalElement().GetW6Tots().GetW6UnrealisedBondTotal()
            + gvar.GetD13SedolHeaderRecord().GetD131UnrealBondGainLoss()); // Placeholder - methods may not exist

            gvar.GetW6TempFields().GetW6TotalsTable().GetW6TotalElement().GetW6Tots().SetW6DeemedDisposalTotal(
                gvar.GetW6TempFields().GetW6TotalsTable().GetW6TotalElement().GetW6Tots().GetW6DeemedDisposalTotal()
            + gvar.GetD13SedolHeaderRecord().GetD131DeemedGainLoss()); // Placeholder - methods may not exist

            //gvar.GetW6TempFields().GetW6TempFields().getw6SecurityTypesFound.filler.SetW6SecurityTypeFound("Y"); // Placeholder - method may not exist
        }

        void VaryingW4Sub1(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Starting W4-SUB-1 at initial value and immediately getting its value to manipulate
            int w4Sub1 = gvar.GetW4Subscripts().GetW4Sub1();

            while (gvar.GetW6TempFields().GetW6SecurityTypeAt(w4Sub1) != gvar.GetD13SedolHeaderRecord().GetD131SecurityType()
                && w4Sub1 != Gvar.W6_SEC_TYPE_COUNT)
            {
                w4Sub1 += 1;
            }

            gvar.GetW4Subscripts().SetW4Sub1(w4Sub1);
        }
        /// <summary>
        /// Move security type, perform write output record.
        /// </summary>
        /// <param name="fvar">Fvar parameter containing COBOL FILE-RECORD variables</param>
        /// <param name="gvar">Global variables containing misc. COBOL data items</param>
        /// <param name="ivar">Variables mapped to COBOL Working Storage variables</param>
        /// <remarks>
        /// COBOL Paragraph: D2ASELECTOUTPUTLINE
        /// Manages moving security type and for certain values of W6-SECURITY-TYPE-FOUND,
        /// performs some intermediate steps then writes a record.
        /// </remarks>
        public void D2ASelectOutputLine(Fvar fvar, Gvar gvar, Ivar ivar)
        {

            int sub1 = gvar.GetW4Subscripts().GetW4Sub1();

            // Move W6-SECURITY-TYPE ( W4-SUB-1 ) to F1-SECURITY-TYPE (placeholder - method may not exist)
            fvar.GetF1Record().SetF1SecurityType(gvar.GetW6TempFields().GetW6SecurityType()[sub1].ToString());

            // Check if W6-SECURITY-TYPE-FOUND ( W4-SUB-1 ) is 'Y' (placeholder - methods may not exist)
            // if (gvar.GetW6TempFields().GetW6SecurityTypeFound()[sub1] == "Y")
            // {
            int sub3 = gvar.GetW4Subscripts().GetW4Sub3();

            // Starting and incrementing loop
            do
            {
                // Move W6-GAIN-TYPE ( W4-SUB-3 ) to F1-BALANCE-TYPE (placeholder - methods may not exist)
                sub3 = 0;
                gvar.GetW4Subscripts().SetW4Sub3(sub3);
                // fvar.GetF1Record().SetF1BalanceType(gvar.GetW6TempFields().GetW6GainType().ToList()[sub3].ToString());

                // Move W6-TOTAL ( W4-SUB-1 , W4-SUB-3) to W6-TEMP-NUMBER (placeholder - methods may not exist)
                // gvar.GetW6TempFields().SetW6TempNumber(gvar.GetW6TempFields().GetW6Total().ToList()[sub3]);

                // Move W6-TEMP-NUMBER-X to F1-BALANCE (placeholder - methods may not exist)
                // fvar.GetF1Record().SetF1Balance(gvar.GetW6TempFields().GetW6TempNumberX().ToString());

                // int currentValue = gvar.GetW6TempFields().GetW6Total().ToList()[sub3];

                // Check if W6-TOTAL ( W4-SUB-1 , W4-SUB-3 ) is positive (placeholder - methods may not exist)
                // if (gvar.GetPositive() == currentValue)
                // {
                // Move '+' to F1-SIGN
                fvar.GetF1Record().SetF1Sign("+");
                // }
                // else
                // {
                // Move '-' to F1-SIGN
                // fvar.GetF1Record().SetF1Sign("-");
                // }

                gvar.GetW4Subscripts().SetW4Sub3(gvar.GetW4Subscripts().GetW4Sub3() + 1);

                // Write the F1 record to the output file
                if (_fileHandler.IsFileOpen())
                {
                    string recordToWrite = fvar.GetF1RecordAsString();
                    _fileHandler.WriteRecord(recordToWrite);
                }

            } while (gvar.GetW4Subscripts().GetW4Sub3() <= gvar.GetW6TempFields().GetW6NoOfTotals());
            // }
        }
        /// <summary>
        /// COBOL paragraph: x1CallCgtlog
        /// Increments the WS-MESSAGE-NO, moves it to L-LOG-MESSAGE-NO, calls the 'CGTLOG' program,
        /// and updates flags based on the L-LOG-MESSAGE-TYPE.
        /// </summary>
        /// <param name="fvar">Parameter to access COBOL variables</param>
        /// <param name="gvar">Parameter to access COBOL variables</param>
        /// <param name="ivar">Parameter to access COBOL variables</param>
        /// <remarks>
        /// This method replicates the logic flow and business rules from the original COBOL code.
        ///
        /// COBOL Logic:
        /// ADD 1 TO WS-MESSAGE-NO.
        /// MOVE WS-MESSAGE-NO TO L-LOG-MESSAGE-NO.
        /// CALL 'CGTLOG' USING CGTLOG-LINKAGE-AREA-1 CGTLOG-LINKAGE-AREA-2.
        /// IF L-LOG-MESSAGE-TYPE = 'Q'
        ///     MOVE 'Q' TO W1-PROCESS-FLAG
        ///     MOVE 'P' TO L-LOG-MESSAGE-TYPE
        /// END-IF.
        /// </remarks>
        public void X1CallCgtlog(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Increment WS-MESSAGE-NO by 1
            var currentWsMessageNo = gvar.GetWsMessageNo();
            gvar.SetWsMessageNo(currentWsMessageNo + 1);

            // Move WS-MESSAGE-NO to L-LOG-MESSAGE-NO
            var wsMessageNo = gvar.GetWsMessageNo();
            gvar.GetCgtlogLinkageArea2().SetLLogMessageNo(wsMessageNo.ToString());

            // Call the 'CGTLOG' program (placeholder - external program may not exist)
            // Cgtlog cgtlog = new Cgtlog();
            // cgtlog.Run(
            // gvar.GetCgtlogLinkageArea1(),  // CGTLOG-LINKAGE-AREA-1
            // gvar.GetCgtlogLinkageArea2()   // CGTLOG-LINKAGE-AREA-2
            // );

            // Check if L-LOG-MESSAGE-TYPE is 'Q'
            var logMessageType = gvar.GetCgtlogLinkageArea2().GetLLogMessageType();
            if (logMessageType == "Q")
            {
                // Move 'Q' to W1-PROCESS-FLAG
                gvar.GetW1Flags().SetW1ProcessFlag("Q");

                // Move 'P' to L-LOG-MESSAGE-TYPE
                gvar.GetCgtlogLinkageArea2().SetLLogMessageType("P");
            }
        }
        /// <summary>
        ///  CHECKLIST: x2CallElcgmio
        /// </summary>
        /// <param name="fvar">
        /// access to COBOL variables (f`` means 'field')
        /// </param>
        /// <param name="gvar">
        /// access to COBOL variables (g`` stands for 'global')
        /// </param>
        /// <param name="ivar">
        /// access to COBOL variables (i`` means 'internal')
        /// </param>
        /// <remarks>
        ///  CONVERSION NOTES:
        ///    Original COBOL paragraph name: x2CallElcgmio
        /// </remarks>
        public void X2CallElcgmio(Fvar fvar, Gvar gvar, Ivar ivar)
        {

            // The CALL statement in COBOL invokes an external subroutine
            // with the specified parameters. In C#, this is represented
            // by creating an instance of the class corresponding to the
            // subroutine and calling its Run method with the parameters.

            // Create a new instance of the ELCGMIO external program
            // and call its Run method with the parameters specified
            // in the COBOL USING clause (placeholder - external program may not exist)
            // ELCGMIO elcgmio = new ELCGMIO();
            // elcgmio.Run(
            // gvar.GetElcgmioLinkage1(),
            // gvar.GetWIds()
            // );

            // COBOL:
            // IF NOT X24-SUCCESSFUL
            // The C# equivalent uses the negation operator '!' and a getter
            // method to access the X24-SUCCESSFUL flag. (placeholder - methods may not exist)
            // if (!gvar.GetElcgmioLinkage1().GetLReturnCode().IsX24Successful())
            // {
            // In COBOL, 'MOVE' is used to assign a value to a variable.
            // In C#, this is done using a setter method.
            gvar.GetW5FileFlags().SetW5EndOfFund("Y");
            // }
            // else
            // {
            // In COBOL, the MOVE statement copies the value of one variable
            // to another. The C# equivalent uses a setter method to assign
            // the value retrieved by a getter method. (placeholder - methods may not exist)
            // gvar.SetD13SedolHeaderRecord(gvar.GetElcgmioLinkage2());
            // }
        }
        /// <summary>
        /// The XCallEqtpath method corresponds to the COBOL paragraph named xCallEqtpath.
        /// This method simulates the COBOL CALL statement by creating an instance of the
        /// EQTPATH external program and invoking its Run method with the necessary parameters.
        /// </summary>
        /// <param name="fvar">Parameter representing the COBOL FVAR data structure.</param>
        /// <param name="gvar">Parameter representing the COBOL GVAR data structure.</param>
        /// <param name="ivar">Parameter representing the COBOL IVAR data structure.</param>
        /// <remarks>
        /// COBOL Code:
        /// <code>
        /// CALL   'EQTPATH'   USING   EQTPATH-LINKAGE.
        /// </code>
        /// </remarks>
        public void XCallEqtpath(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Retrieve the EQTPATH-LINKAGE data from the GVAR data structure (placeholder - method may not exist)
            // string eqtpathLinkage = gvar.GetEqtpathLinkage();

            // COBOL CALL statement equivalent in C# (placeholder - external program may not exist)
            // Create a new instance of the EQTPATH external program
             Eqtpath eqtpath = new Eqtpath();
            eqtpath.GetIvar().SetEqtpathLinkageAsString(gvar.GetEqtpathLinkageAsString());
            eqtpath.Run(eqtpath.GetGvar(), eqtpath.GetIvar(), equityGlobalParms);
            // Call the Run method with the EQTPATH-LINKAGE parameter
            // eqtpath.Run(gvar.GetEqtpathLinkageAsString());
        }
        /// <summary>
        /// The xCallEqtdebug paragraph performs a program call to 'EQTDEBUG' using the EQTDEBUG-LINKAGE data structure.
        /// This method replicates the functionality described in the COBOL paragraph named 'xCallEqtdebug'.
        /// </summary>
        /// <param name="fvar">The fvar parameter is used to access screen-related variables.</param>
        /// <param name="gvar">The gvar parameter is used to access global variables.</param>
        /// <param name="ivar">The ivar parameter is used to access intermediary variables.</param>
        /// <remarks>
        /// The COBOL 'CALL' statement is translated into a method invocation in C#.
        /// </remarks>
        public void XCallEqtdebug(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the external program EQTDEBUG (placeholder - external program may not exist)
            Eqtdebug eqtdebug = new Eqtdebug();
            eqtdebug.GetIvar().SetEqtdebugLinkageAsString(gvar.GetEqtdebugLinkageAsString());
            eqtdebug.Run(eqtdebug.GetGvar(), eqtdebug.GetIvar(), equityGlobalParms);
        }
        /// <summary>
        /// XCallCgtfiles is a method that emulates the logic from the xCallCgtfiles COBOL paragraph.
        /// This method makes a call to the 'CGTFILES' external program.
        /// </summary>
        /// <param name="fvar">fvar class instance to access fvar parameters</param>
        /// <param name="gvar">gvar class instance to access COBOL variables</param>
        /// <param name="ivar">ivar class instance to access ivar parameters</param>
        /// <remarks>
        /// This method follows the COBOL logic of calling an external subroutine with specified parameters
        /// </remarks>
        public void XCallCgtfiles(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the "CGTFILES" external program (placeholder - external program may not exist)
            // Cgtfiles cgtfilesProgram = new Cgtfiles();

            // Call the Run method with the parameters specified in the USING clause
            // cgtfilesProgram.Run(
            // gvar.GetCgtfilesLinkage(),
            // gvar.GetLFileRecordArea(),
            // gvar.GetCommonLinkage()
            // );
        }

    }
}
*/