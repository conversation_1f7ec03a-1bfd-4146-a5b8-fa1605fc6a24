using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// <Section> Class for Fvar
public class Fvar
{
public Fvar() {}

// Fields in the class


// [DEBUG] Field: D105Record, is_external=, is_static_class=False, static_prefix=
private D105Record _D105Record = new D105Record();




// Getter and Setter methods

// Standard Getter
public D105Record GetD105Record()
{
    return _D105Record;
}

// Standard Setter
public void SetD105Record(D105Record value)
{
    _D105Record = value;
}

// Get<>AsString()
public string GetD105RecordAsString()
{
    return _D105Record != null ? _D105Record.GetD105RecordAsString() : "";
}

// Set<>AsString()
public void SetD105RecordAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D105Record == null)
    {
        _D105Record = new D105Record();
    }
    _D105Record.SetD105RecordAsString(value);
}


}}