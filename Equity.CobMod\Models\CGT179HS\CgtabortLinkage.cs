using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt179HsDTO
{// DTO class representing CgtabortLinkage Data Structure

public class CgtabortLinkage
{
    private static int _size = 18;
    // [DEBUG] Class: CgtabortLinkage, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: LAbortFileStatus, is_external=, is_static_class=False, static_prefix=
    private string _LAbortFileStatus ="";
    
    
    
    
    // [DEBUG] Field: LAbortProgramName, is_external=, is_static_class=False, static_prefix=
    private string _LAbortProgramName ="";
    
    
    
    
    // [DEBUG] Field: LAbortFileName, is_external=, is_static_class=False, static_prefix=
    private string _LAbortFileName ="";
    
    
    
    
    
    // Serialization methods
    public string GetCgtabortLinkageAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LAbortFileStatus.PadRight(2));
        result.Append(_LAbortProgramName.PadRight(8));
        result.Append(_LAbortFileName.PadRight(8));
        
        return result.ToString();
    }
    
    public void SetCgtabortLinkageAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetLAbortFileStatus(extracted);
        }
        offset += 2;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetLAbortProgramName(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetLAbortFileName(extracted);
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtabortLinkageAsString();
    }
    // Set<>String Override function
    public void SetCgtabortLinkage(string value)
    {
        SetCgtabortLinkageAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetLAbortFileStatus()
    {
        return _LAbortFileStatus;
    }
    
    // Standard Setter
    public void SetLAbortFileStatus(string value)
    {
        _LAbortFileStatus = value;
    }
    
    // Get<>AsString()
    public string GetLAbortFileStatusAsString()
    {
        return _LAbortFileStatus.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetLAbortFileStatusAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LAbortFileStatus = value;
    }
    
    // Standard Getter
    public string GetLAbortProgramName()
    {
        return _LAbortProgramName;
    }
    
    // Standard Setter
    public void SetLAbortProgramName(string value)
    {
        _LAbortProgramName = value;
    }
    
    // Get<>AsString()
    public string GetLAbortProgramNameAsString()
    {
        return _LAbortProgramName.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetLAbortProgramNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LAbortProgramName = value;
    }
    
    // Standard Getter
    public string GetLAbortFileName()
    {
        return _LAbortFileName;
    }
    
    // Standard Setter
    public void SetLAbortFileName(string value)
    {
        _LAbortFileName = value;
    }
    
    // Get<>AsString()
    public string GetLAbortFileNameAsString()
    {
        return _LAbortFileName.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetLAbortFileNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LAbortFileName = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}