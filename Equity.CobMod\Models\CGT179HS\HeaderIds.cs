using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt179HsDTO
{// DTO class representing HeaderIds Data Structure

public class HeaderIds
{
    private static int _size = 81;
    // [DEBUG] Class: HeaderIds, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: MhdaHoldingId, is_external=, is_static_class=False, static_prefix=
    private string _MhdaHoldingId ="";
    
    
    
    
    // [DEBUG] Field: MhdaFundId, is_external=, is_static_class=False, static_prefix=
    private string _MhdaFundId ="";
    
    
    
    
    // [DEBUG] Field: MhdaStockId, is_external=, is_static_class=False, static_prefix=
    private string _MhdaStockId ="";
    
    
    
    
    // [DEBUG] Field: MhdaBondOverrideId, is_external=, is_static_class=False, static_prefix=
    private string _MhdaBondOverrideId ="";
    
    
    
    
    // [DEBUG] Field: MhdaAssetUsageOverrideId, is_external=, is_static_class=False, static_prefix=
    private string _MhdaAssetUsageOverrideId ="";
    
    
    
    
    // [DEBUG] Field: Filler103, is_external=, is_static_class=False, static_prefix=
    private string _Filler103 ="";
    
    
    
    
    // [DEBUG] Field: Filler104, is_external=, is_static_class=False, static_prefix=
    private string _Filler104 ="";
    
    
    
    
    // [DEBUG] Field: MhdaDbTimestamp, is_external=, is_static_class=False, static_prefix=
    private string _MhdaDbTimestamp ="";
    
    
    
    
    // [DEBUG] Field: MhdaIlgCondition, is_external=, is_static_class=False, static_prefix=
    private int _MhdaIlgCondition =0;
    
    
    // 88-level condition checks for MhdaIlgCondition
    public bool IsNotIlgHolding()
    {
        if (this._MhdaIlgCondition == 0) return true;
        return false;
    }
    public bool IsIlgWithIndexFirstPeriod()
    {
        if (this._MhdaIlgCondition == 1) return true;
        return false;
    }
    public bool IsIlgWithIndexOtherPeriod()
    {
        if (this._MhdaIlgCondition == 2) return true;
        return false;
    }
    public bool IsIlgNoIndexation()
    {
        if (this._MhdaIlgCondition == 3) return true;
        return false;
    }
    
    
    
    // Serialization methods
    public string GetHeaderIdsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_MhdaHoldingId.PadRight(8));
        result.Append(_MhdaFundId.PadRight(8));
        result.Append(_MhdaStockId.PadRight(8));
        result.Append(_MhdaBondOverrideId.PadRight(8));
        result.Append(_MhdaAssetUsageOverrideId.PadRight(8));
        result.Append(_Filler103.PadRight(8));
        result.Append(_Filler104.PadRight(8));
        result.Append(_MhdaDbTimestamp.PadRight(24));
        result.Append(_MhdaIlgCondition.ToString().PadLeft(1, '0'));
        
        return result.ToString();
    }
    
    public void SetHeaderIdsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMhdaHoldingId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMhdaFundId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMhdaStockId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMhdaBondOverrideId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetMhdaAssetUsageOverrideId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller103(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller104(extracted);
        }
        offset += 8;
        if (offset + 24 <= data.Length)
        {
            string extracted = data.Substring(offset, 24).Trim();
            SetMhdaDbTimestamp(extracted);
        }
        offset += 24;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetMhdaIlgCondition(parsedInt);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetHeaderIdsAsString();
    }
    // Set<>String Override function
    public void SetHeaderIds(string value)
    {
        SetHeaderIdsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetMhdaHoldingId()
    {
        return _MhdaHoldingId;
    }
    
    // Standard Setter
    public void SetMhdaHoldingId(string value)
    {
        _MhdaHoldingId = value;
    }
    
    // Get<>AsString()
    public string GetMhdaHoldingIdAsString()
    {
        return _MhdaHoldingId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMhdaHoldingIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MhdaHoldingId = value;
    }
    
    // Standard Getter
    public string GetMhdaFundId()
    {
        return _MhdaFundId;
    }
    
    // Standard Setter
    public void SetMhdaFundId(string value)
    {
        _MhdaFundId = value;
    }
    
    // Get<>AsString()
    public string GetMhdaFundIdAsString()
    {
        return _MhdaFundId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMhdaFundIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MhdaFundId = value;
    }
    
    // Standard Getter
    public string GetMhdaStockId()
    {
        return _MhdaStockId;
    }
    
    // Standard Setter
    public void SetMhdaStockId(string value)
    {
        _MhdaStockId = value;
    }
    
    // Get<>AsString()
    public string GetMhdaStockIdAsString()
    {
        return _MhdaStockId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMhdaStockIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MhdaStockId = value;
    }
    
    // Standard Getter
    public string GetMhdaBondOverrideId()
    {
        return _MhdaBondOverrideId;
    }
    
    // Standard Setter
    public void SetMhdaBondOverrideId(string value)
    {
        _MhdaBondOverrideId = value;
    }
    
    // Get<>AsString()
    public string GetMhdaBondOverrideIdAsString()
    {
        return _MhdaBondOverrideId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMhdaBondOverrideIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MhdaBondOverrideId = value;
    }
    
    // Standard Getter
    public string GetMhdaAssetUsageOverrideId()
    {
        return _MhdaAssetUsageOverrideId;
    }
    
    // Standard Setter
    public void SetMhdaAssetUsageOverrideId(string value)
    {
        _MhdaAssetUsageOverrideId = value;
    }
    
    // Get<>AsString()
    public string GetMhdaAssetUsageOverrideIdAsString()
    {
        return _MhdaAssetUsageOverrideId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetMhdaAssetUsageOverrideIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MhdaAssetUsageOverrideId = value;
    }
    
    // Standard Getter
    public string GetFiller103()
    {
        return _Filler103;
    }
    
    // Standard Setter
    public void SetFiller103(string value)
    {
        _Filler103 = value;
    }
    
    // Get<>AsString()
    public string GetFiller103AsString()
    {
        return _Filler103.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller103AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler103 = value;
    }
    
    // Standard Getter
    public string GetFiller104()
    {
        return _Filler104;
    }
    
    // Standard Setter
    public void SetFiller104(string value)
    {
        _Filler104 = value;
    }
    
    // Get<>AsString()
    public string GetFiller104AsString()
    {
        return _Filler104.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller104AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler104 = value;
    }
    
    // Standard Getter
    public string GetMhdaDbTimestamp()
    {
        return _MhdaDbTimestamp;
    }
    
    // Standard Setter
    public void SetMhdaDbTimestamp(string value)
    {
        _MhdaDbTimestamp = value;
    }
    
    // Get<>AsString()
    public string GetMhdaDbTimestampAsString()
    {
        return _MhdaDbTimestamp.PadRight(24);
    }
    
    // Set<>AsString()
    public void SetMhdaDbTimestampAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _MhdaDbTimestamp = value;
    }
    
    // Standard Getter
    public int GetMhdaIlgCondition()
    {
        return _MhdaIlgCondition;
    }
    
    // Standard Setter
    public void SetMhdaIlgCondition(int value)
    {
        _MhdaIlgCondition = value;
    }
    
    // Get<>AsString()
    public string GetMhdaIlgConditionAsString()
    {
        return _MhdaIlgCondition.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetMhdaIlgConditionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _MhdaIlgCondition = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}