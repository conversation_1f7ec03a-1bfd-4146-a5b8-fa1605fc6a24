using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D99Record Data Structure

public class D99Record
{
    private static int _size = 1;
    // [DEBUG] Class: D99Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D99RecordByte, is_external=, is_static_class=False, static_prefix=
    private string _D99RecordByte ="";
    
    
    
    
    
    // Serialization methods
    public string GetD99RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D99RecordByte.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetD99RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD99RecordByte(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD99RecordAsString();
    }
    // Set<>String Override function
    public void SetD99Record(string value)
    {
        SetD99RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD99RecordByte()
    {
        return _D99RecordByte;
    }
    
    // Standard Setter
    public void SetD99RecordByte(string value)
    {
        _D99RecordByte = value;
    }
    
    // Get<>AsString()
    public string GetD99RecordByteAsString()
    {
        return _D99RecordByte.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD99RecordByteAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D99RecordByte = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}