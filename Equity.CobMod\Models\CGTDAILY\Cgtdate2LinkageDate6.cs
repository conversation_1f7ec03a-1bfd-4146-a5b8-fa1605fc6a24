using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdailyDTO
{// DTO class representing Cgtdate2LinkageDate6 Data Structure

public class Cgtdate2LinkageDate6
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate6, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd6 =0;
    
    
    
    
    // [DEBUG] Field: Filler75, is_external=, is_static_class=False, static_prefix=
    private Filler75 _Filler75 = new Filler75();
    
    
    
    
    // [DEBUG] Field: Filler76, is_external=, is_static_class=False, static_prefix=
    private Filler76 _Filler76 = new Filler76();
    
    
    
    
    // [DEBUG] Field: Filler77, is_external=, is_static_class=False, static_prefix=
    private Filler77 _Filler77 = new Filler77();
    
    
    
    
    // [DEBUG] Field: Filler78, is_external=, is_static_class=False, static_prefix=
    private Filler78 _Filler78 = new Filler78();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate6AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd6.ToString().PadLeft(8, '0'));
        result.Append(_Filler75.GetFiller75AsString());
        result.Append(_Filler76.GetFiller76AsString());
        result.Append(_Filler77.GetFiller77AsString());
        result.Append(_Filler78.GetFiller78AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate6AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd6(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler75.SetFiller75AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler75.SetFiller75AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler76.SetFiller76AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler76.SetFiller76AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler77.SetFiller77AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler77.SetFiller77AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler78.SetFiller78AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler78.SetFiller78AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate6AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate6(string value)
    {
        SetCgtdate2LinkageDate6AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd6()
    {
        return _Cgtdate2Ccyymmdd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd6(int value)
    {
        _Cgtdate2Ccyymmdd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd6AsString()
    {
        return _Cgtdate2Ccyymmdd6.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd6 = parsed;
    }
    
    // Standard Getter
    public Filler75 GetFiller75()
    {
        return _Filler75;
    }
    
    // Standard Setter
    public void SetFiller75(Filler75 value)
    {
        _Filler75 = value;
    }
    
    // Get<>AsString()
    public string GetFiller75AsString()
    {
        return _Filler75 != null ? _Filler75.GetFiller75AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller75AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler75 == null)
        {
            _Filler75 = new Filler75();
        }
        _Filler75.SetFiller75AsString(value);
    }
    
    // Standard Getter
    public Filler76 GetFiller76()
    {
        return _Filler76;
    }
    
    // Standard Setter
    public void SetFiller76(Filler76 value)
    {
        _Filler76 = value;
    }
    
    // Get<>AsString()
    public string GetFiller76AsString()
    {
        return _Filler76 != null ? _Filler76.GetFiller76AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller76AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler76 == null)
        {
            _Filler76 = new Filler76();
        }
        _Filler76.SetFiller76AsString(value);
    }
    
    // Standard Getter
    public Filler77 GetFiller77()
    {
        return _Filler77;
    }
    
    // Standard Setter
    public void SetFiller77(Filler77 value)
    {
        _Filler77 = value;
    }
    
    // Get<>AsString()
    public string GetFiller77AsString()
    {
        return _Filler77 != null ? _Filler77.GetFiller77AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller77AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler77 == null)
        {
            _Filler77 = new Filler77();
        }
        _Filler77.SetFiller77AsString(value);
    }
    
    // Standard Getter
    public Filler78 GetFiller78()
    {
        return _Filler78;
    }
    
    // Standard Setter
    public void SetFiller78(Filler78 value)
    {
        _Filler78 = value;
    }
    
    // Get<>AsString()
    public string GetFiller78AsString()
    {
        return _Filler78 != null ? _Filler78.GetFiller78AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller78AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler78 == null)
        {
            _Filler78 = new Filler78();
        }
        _Filler78.SetFiller78AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller75(string value)
    {
        _Filler75.SetFiller75AsString(value);
    }
    // Nested Class: Filler75
    public class Filler75
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc6 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd6, is_external=, is_static_class=False, static_prefix=
        private Filler75.Cgtdate2Yymmdd6 _Cgtdate2Yymmdd6 = new Filler75.Cgtdate2Yymmdd6();
        
        
        
        
    public Filler75() {}
    
    public Filler75(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc6(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(data.Substring(offset, Cgtdate2Yymmdd6.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller75AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc6.PadRight(2));
        result.Append(_Cgtdate2Yymmdd6.GetCgtdate2Yymmdd6AsString());
        
        return result.ToString();
    }
    
    public void SetFiller75AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc6(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc6()
    {
        return _Cgtdate2Cc6;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc6(string value)
    {
        _Cgtdate2Cc6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc6AsString()
    {
        return _Cgtdate2Cc6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc6 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd6 GetCgtdate2Yymmdd6()
    {
        return _Cgtdate2Yymmdd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd6(Cgtdate2Yymmdd6 value)
    {
        _Cgtdate2Yymmdd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd6AsString()
    {
        return _Cgtdate2Yymmdd6 != null ? _Cgtdate2Yymmdd6.GetCgtdate2Yymmdd6AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd6 == null)
        {
            _Cgtdate2Yymmdd6 = new Cgtdate2Yymmdd6();
        }
        _Cgtdate2Yymmdd6.SetCgtdate2Yymmdd6AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd6
    public class Cgtdate2Yymmdd6
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy6 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd6, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd6.Cgtdate2Mmdd6 _Cgtdate2Mmdd6 = new Cgtdate2Yymmdd6.Cgtdate2Mmdd6();
        
        
        
        
    public Cgtdate2Yymmdd6() {}
    
    public Cgtdate2Yymmdd6(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy6(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(data.Substring(offset, Cgtdate2Mmdd6.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd6AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy6.PadRight(2));
        result.Append(_Cgtdate2Mmdd6.GetCgtdate2Mmdd6AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd6AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy6(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy6()
    {
        return _Cgtdate2Yy6;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy6(string value)
    {
        _Cgtdate2Yy6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy6AsString()
    {
        return _Cgtdate2Yy6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy6 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd6 GetCgtdate2Mmdd6()
    {
        return _Cgtdate2Mmdd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd6(Cgtdate2Mmdd6 value)
    {
        _Cgtdate2Mmdd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd6AsString()
    {
        return _Cgtdate2Mmdd6 != null ? _Cgtdate2Mmdd6.GetCgtdate2Mmdd6AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd6 == null)
        {
            _Cgtdate2Mmdd6 = new Cgtdate2Mmdd6();
        }
        _Cgtdate2Mmdd6.SetCgtdate2Mmdd6AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd6
    public class Cgtdate2Mmdd6
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm6 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd6, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd6 ="";
        
        
        
        
    public Cgtdate2Mmdd6() {}
    
    public Cgtdate2Mmdd6(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm6(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd6(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd6AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm6.PadRight(2));
        result.Append(_Cgtdate2Dd6.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd6AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm6(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd6(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm6()
    {
        return _Cgtdate2Mm6;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm6(string value)
    {
        _Cgtdate2Mm6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm6AsString()
    {
        return _Cgtdate2Mm6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm6 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd6()
    {
        return _Cgtdate2Dd6;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd6(string value)
    {
        _Cgtdate2Dd6 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd6AsString()
    {
        return _Cgtdate2Dd6.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd6 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller76(string value)
{
    _Filler76.SetFiller76AsString(value);
}
// Nested Class: Filler76
public class Filler76
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd6 =0;
    
    
    
    
public Filler76() {}

public Filler76(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy6(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd6(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller76AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy6.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd6.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller76AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy6(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd6(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy6()
{
    return _Cgtdate2CCcyy6;
}

// Standard Setter
public void SetCgtdate2CCcyy6(int value)
{
    _Cgtdate2CCcyy6 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy6AsString()
{
    return _Cgtdate2CCcyy6.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy6 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd6()
{
    return _Cgtdate2CMmdd6;
}

// Standard Setter
public void SetCgtdate2CMmdd6(int value)
{
    _Cgtdate2CMmdd6 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd6AsString()
{
    return _Cgtdate2CMmdd6.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd6 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller77(string value)
{
    _Filler77.SetFiller77AsString(value);
}
// Nested Class: Filler77
public class Filler77
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm6 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd6 =0;
    
    
    
    
public Filler77() {}

public Filler77(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd6(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller77AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc6.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy6.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm6.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd6.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller77AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc6(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy6(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm6(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd6(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc6()
{
    return _Cgtdate2CCc6;
}

// Standard Setter
public void SetCgtdate2CCc6(int value)
{
    _Cgtdate2CCc6 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc6AsString()
{
    return _Cgtdate2CCc6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc6 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy6()
{
    return _Cgtdate2CYy6;
}

// Standard Setter
public void SetCgtdate2CYy6(int value)
{
    _Cgtdate2CYy6 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy6AsString()
{
    return _Cgtdate2CYy6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy6 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm6()
{
    return _Cgtdate2CMm6;
}

// Standard Setter
public void SetCgtdate2CMm6(int value)
{
    _Cgtdate2CMm6 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm6AsString()
{
    return _Cgtdate2CMm6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm6 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd6()
{
    return _Cgtdate2CDd6;
}

// Standard Setter
public void SetCgtdate2CDd6(int value)
{
    _Cgtdate2CDd6 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd6AsString()
{
    return _Cgtdate2CDd6.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd6 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller78(string value)
{
    _Filler78.SetFiller78AsString(value);
}
// Nested Class: Filler78
public class Filler78
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm6, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm6 =0;
    
    
    
    
    // [DEBUG] Field: Filler79, is_external=, is_static_class=False, static_prefix=
    private string _Filler79 ="";
    
    
    
    
public Filler78() {}

public Filler78(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm6(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller79(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller78AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm6.ToString().PadLeft(6, '0'));
    result.Append(_Filler79.PadRight(2));
    
    return result.ToString();
}

public void SetFiller78AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm6(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller79(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm6()
{
    return _Cgtdate2CCcyymm6;
}

// Standard Setter
public void SetCgtdate2CCcyymm6(int value)
{
    _Cgtdate2CCcyymm6 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm6AsString()
{
    return _Cgtdate2CCcyymm6.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm6AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm6 = parsed;
}

// Standard Getter
public string GetFiller79()
{
    return _Filler79;
}

// Standard Setter
public void SetFiller79(string value)
{
    _Filler79 = value;
}

// Get<>AsString()
public string GetFiller79AsString()
{
    return _Filler79.PadRight(2);
}

// Set<>AsString()
public void SetFiller79AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler79 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}