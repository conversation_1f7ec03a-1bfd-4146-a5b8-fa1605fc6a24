using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtGtMatchedBargainDates Data Structure

public class WtGtMatchedBargainDates
{
    private static int _size = 73;
    // [DEBUG] Class: WtGtMatchedBargainDates, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler447, is_external=, is_static_class=False, static_prefix=
    private string _Filler447 ="GT-MATCH-BARG-DT";
    
    
    
    
    // [DEBUG] Field: WtmbdMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtmbdMaxTableSize =4000;
    
    
    
    
    // [DEBUG] Field: WtmbdOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtmbdOccurs =4000;
    
    
    
    
    // [DEBUG] Field: WtmbdTable, is_external=, is_static_class=False, static_prefix=
    private WtmbdTable _WtmbdTable = new WtmbdTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtGtMatchedBargainDatesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler447.PadRight(16));
        result.Append(_WtmbdMaxTableSize.ToString().PadLeft(4, '0'));
        result.Append(_WtmbdOccurs.ToString().PadLeft(4, '0'));
        result.Append(_WtmbdTable.GetWtmbdTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtGtMatchedBargainDatesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller447(extracted);
        }
        offset += 16;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtmbdMaxTableSize(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtmbdOccurs(parsedInt);
        }
        offset += 4;
        if (offset + 49 <= data.Length)
        {
            _WtmbdTable.SetWtmbdTableAsString(data.Substring(offset, 49));
        }
        else
        {
            _WtmbdTable.SetWtmbdTableAsString(data.Substring(offset));
        }
        offset += 49;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtGtMatchedBargainDatesAsString();
    }
    // Set<>String Override function
    public void SetWtGtMatchedBargainDates(string value)
    {
        SetWtGtMatchedBargainDatesAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller447()
    {
        return _Filler447;
    }
    
    // Standard Setter
    public void SetFiller447(string value)
    {
        _Filler447 = value;
    }
    
    // Get<>AsString()
    public string GetFiller447AsString()
    {
        return _Filler447.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller447AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler447 = value;
    }
    
    // Standard Getter
    public int GetWtmbdMaxTableSize()
    {
        return _WtmbdMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtmbdMaxTableSize(int value)
    {
        _WtmbdMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtmbdMaxTableSizeAsString()
    {
        return _WtmbdMaxTableSize.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtmbdMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtmbdMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtmbdOccurs()
    {
        return _WtmbdOccurs;
    }
    
    // Standard Setter
    public void SetWtmbdOccurs(int value)
    {
        _WtmbdOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtmbdOccursAsString()
    {
        return _WtmbdOccurs.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtmbdOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtmbdOccurs = parsed;
    }
    
    // Standard Getter
    public WtmbdTable GetWtmbdTable()
    {
        return _WtmbdTable;
    }
    
    // Standard Setter
    public void SetWtmbdTable(WtmbdTable value)
    {
        _WtmbdTable = value;
    }
    
    // Get<>AsString()
    public string GetWtmbdTableAsString()
    {
        return _WtmbdTable != null ? _WtmbdTable.GetWtmbdTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtmbdTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtmbdTable == null)
        {
            _WtmbdTable = new WtmbdTable();
        }
        _WtmbdTable.SetWtmbdTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtmbdTable(string value)
    {
        _WtmbdTable.SetWtmbdTableAsString(value);
    }
    // Nested Class: WtmbdTable
    public class WtmbdTable
    {
        private static int _size = 49;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtmbdElement, is_external=, is_static_class=False, static_prefix=
        private WtmbdTable.WtmbdElement[] _WtmbdElement = new WtmbdTable.WtmbdElement[4000];
        
        public void InitializeWtmbdElementArray()
        {
            for (int i = 0; i < 4000; i++)
            {
                _WtmbdElement[i] = new WtmbdTable.WtmbdElement();
            }
        }
        
        
        
    public WtmbdTable() {}
    
    public WtmbdTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWtmbdElementArray();
        for (int i = 0; i < 4000; i++)
        {
            _WtmbdElement[i].SetWtmbdElementAsString(data.Substring(offset, 49));
            offset += 49;
        }
        
    }
    
    // Serialization methods
    public string GetWtmbdTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 4000; i++)
        {
            result.Append(_WtmbdElement[i].GetWtmbdElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtmbdTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 4000; i++)
        {
            if (offset + 49 > data.Length) break;
            string val = data.Substring(offset, 49);
            
            _WtmbdElement[i].SetWtmbdElementAsString(val);
            offset += 49;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtmbdElement
    public WtmbdElement GetWtmbdElementAt(int index)
    {
        return _WtmbdElement[index];
    }
    
    public void SetWtmbdElementAt(int index, WtmbdElement value)
    {
        _WtmbdElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtmbdElement GetWtmbdElement()
    {
        return _WtmbdElement != null && _WtmbdElement.Length > 0
        ? _WtmbdElement[0]
        : new WtmbdElement();
    }
    
    public void SetWtmbdElement(WtmbdElement value)
    {
        if (_WtmbdElement == null || _WtmbdElement.Length == 0)
        _WtmbdElement = new WtmbdElement[1];
        _WtmbdElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtmbdElement
    public class WtmbdElement
    {
        private static int _size = 49;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtmbdDispIndex, is_external=, is_static_class=False, static_prefix=
        private string _WtmbdDispIndex ="";
        
        
        
        
        // [DEBUG] Field: WtmbdBargainDate, is_external=, is_static_class=False, static_prefix=
        private string _WtmbdBargainDate ="";
        
        
        
        
        // [DEBUG] Field: WtmbdTaperUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WtmbdTaperUnits =0;
        
        
        
        
        // [DEBUG] Field: WtmbdTaperGain, is_external=, is_static_class=False, static_prefix=
        private decimal _WtmbdTaperGain =0;
        
        
        
        
        // [DEBUG] Field: WtmbdTaperProceeds, is_external=, is_static_class=False, static_prefix=
        private decimal _WtmbdTaperProceeds =0;
        
        
        
        
    public WtmbdElement() {}
    
    public WtmbdElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtmbdDispIndex(data.Substring(offset, 4).Trim());
        offset += 4;
        SetWtmbdBargainDate(data.Substring(offset, 6).Trim());
        offset += 6;
        SetWtmbdTaperUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetWtmbdTaperGain(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetWtmbdTaperProceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        
    }
    
    // Serialization methods
    public string GetWtmbdElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtmbdDispIndex.PadRight(4));
        result.Append(_WtmbdBargainDate.PadRight(6));
        result.Append(_WtmbdTaperUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtmbdTaperGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtmbdTaperProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        
        return result.ToString();
    }
    
    public void SetWtmbdElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtmbdDispIndex(extracted);
        }
        offset += 4;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetWtmbdBargainDate(extracted);
        }
        offset += 6;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtmbdTaperUnits(parsedDec);
        }
        offset += 13;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtmbdTaperGain(parsedDec);
        }
        offset += 13;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtmbdTaperProceeds(parsedDec);
        }
        offset += 13;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtmbdDispIndex()
    {
        return _WtmbdDispIndex;
    }
    
    // Standard Setter
    public void SetWtmbdDispIndex(string value)
    {
        _WtmbdDispIndex = value;
    }
    
    // Get<>AsString()
    public string GetWtmbdDispIndexAsString()
    {
        return _WtmbdDispIndex.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtmbdDispIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtmbdDispIndex = value;
    }
    
    // Standard Getter
    public string GetWtmbdBargainDate()
    {
        return _WtmbdBargainDate;
    }
    
    // Standard Setter
    public void SetWtmbdBargainDate(string value)
    {
        _WtmbdBargainDate = value;
    }
    
    // Get<>AsString()
    public string GetWtmbdBargainDateAsString()
    {
        return _WtmbdBargainDate.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetWtmbdBargainDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtmbdBargainDate = value;
    }
    
    // Standard Getter
    public decimal GetWtmbdTaperUnits()
    {
        return _WtmbdTaperUnits;
    }
    
    // Standard Setter
    public void SetWtmbdTaperUnits(decimal value)
    {
        _WtmbdTaperUnits = value;
    }
    
    // Get<>AsString()
    public string GetWtmbdTaperUnitsAsString()
    {
        return _WtmbdTaperUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtmbdTaperUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtmbdTaperUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetWtmbdTaperGain()
    {
        return _WtmbdTaperGain;
    }
    
    // Standard Setter
    public void SetWtmbdTaperGain(decimal value)
    {
        _WtmbdTaperGain = value;
    }
    
    // Get<>AsString()
    public string GetWtmbdTaperGainAsString()
    {
        return _WtmbdTaperGain.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtmbdTaperGainAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtmbdTaperGain = parsed;
    }
    
    // Standard Getter
    public decimal GetWtmbdTaperProceeds()
    {
        return _WtmbdTaperProceeds;
    }
    
    // Standard Setter
    public void SetWtmbdTaperProceeds(decimal value)
    {
        _WtmbdTaperProceeds = value;
    }
    
    // Get<>AsString()
    public string GetWtmbdTaperProceedsAsString()
    {
        return _WtmbdTaperProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtmbdTaperProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtmbdTaperProceeds = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}

}}