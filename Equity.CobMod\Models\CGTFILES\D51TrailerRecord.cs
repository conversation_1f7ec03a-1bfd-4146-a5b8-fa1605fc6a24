using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D51TrailerRecord Data Structure

public class D51TrailerRecord
{
    private static int _size = 82;
    // [DEBUG] Class: D51TrailerRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler76, is_external=, is_static_class=False, static_prefix=
    private string _Filler76 ="";
    
    
    // 88-level condition checks for Filler76
    public bool IsTrailerFound()
    {
        if (this._Filler76 == "'9999999MICRO CGT STOCK TRAILER'") return true;
        return false;
    }
    
    
    
    // Serialization methods
    public string GetD51TrailerRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler76.PadRight(82));
        
        return result.ToString();
    }
    
    public void SetD51TrailerRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 82 <= data.Length)
        {
            string extracted = data.Substring(offset, 82).Trim();
            SetFiller76(extracted);
        }
        offset += 82;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD51TrailerRecordAsString();
    }
    // Set<>String Override function
    public void SetD51TrailerRecord(string value)
    {
        SetD51TrailerRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller76()
    {
        return _Filler76;
    }
    
    // Standard Setter
    public void SetFiller76(string value)
    {
        _Filler76 = value;
    }
    
    // Get<>AsString()
    public string GetFiller76AsString()
    {
        return _Filler76.PadRight(82);
    }
    
    // Set<>AsString()
    public void SetFiller76AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler76 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}