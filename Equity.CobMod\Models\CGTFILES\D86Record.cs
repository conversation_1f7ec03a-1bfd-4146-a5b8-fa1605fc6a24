using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D86Record Data Structure

public class D86Record
{
    private static int _size = 7;
    // [DEBUG] Class: D86Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D86Key, is_external=, is_static_class=False, static_prefix=
    private D86Key _D86Key = new D86Key();
    
    
    
    
    // [DEBUG] Field: D86RcfFraction, is_external=, is_static_class=False, static_prefix=
    private int _D86RcfFraction =0;
    
    
    
    
    
    // Serialization methods
    public string GetD86RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D86Key.GetD86KeyAsString());
        result.Append(_D86RcfFraction.ToString().PadLeft(1, '0'));
        
        return result.ToString();
    }
    
    public void SetD86RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 6 <= data.Length)
        {
            _D86Key.SetD86KeyAsString(data.Substring(offset, 6));
        }
        else
        {
            _D86Key.SetD86KeyAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD86RcfFraction(parsedInt);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD86RecordAsString();
    }
    // Set<>String Override function
    public void SetD86Record(string value)
    {
        SetD86RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D86Key GetD86Key()
    {
        return _D86Key;
    }
    
    // Standard Setter
    public void SetD86Key(D86Key value)
    {
        _D86Key = value;
    }
    
    // Get<>AsString()
    public string GetD86KeyAsString()
    {
        return _D86Key != null ? _D86Key.GetD86KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD86KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D86Key == null)
        {
            _D86Key = new D86Key();
        }
        _D86Key.SetD86KeyAsString(value);
    }
    
    // Standard Getter
    public int GetD86RcfFraction()
    {
        return _D86RcfFraction;
    }
    
    // Standard Setter
    public void SetD86RcfFraction(int value)
    {
        _D86RcfFraction = value;
    }
    
    // Get<>AsString()
    public string GetD86RcfFractionAsString()
    {
        return _D86RcfFraction.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD86RcfFractionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D86RcfFraction = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD86Key(string value)
    {
        _D86Key.SetD86KeyAsString(value);
    }
    // Nested Class: D86Key
    public class D86Key
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D86RcfYear, is_external=, is_static_class=False, static_prefix=
        private string _D86RcfYear ="";
        
        
        
        
        // [DEBUG] Field: D86RcfFund, is_external=, is_static_class=False, static_prefix=
        private string _D86RcfFund ="";
        
        
        
        
    public D86Key() {}
    
    public D86Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD86RcfYear(data.Substring(offset, 2).Trim());
        offset += 2;
        SetD86RcfFund(data.Substring(offset, 4).Trim());
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetD86KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D86RcfYear.PadRight(2));
        result.Append(_D86RcfFund.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD86KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD86RcfYear(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD86RcfFund(extracted);
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD86RcfYear()
    {
        return _D86RcfYear;
    }
    
    // Standard Setter
    public void SetD86RcfYear(string value)
    {
        _D86RcfYear = value;
    }
    
    // Get<>AsString()
    public string GetD86RcfYearAsString()
    {
        return _D86RcfYear.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD86RcfYearAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D86RcfYear = value;
    }
    
    // Standard Getter
    public string GetD86RcfFund()
    {
        return _D86RcfFund;
    }
    
    // Standard Setter
    public void SetD86RcfFund(string value)
    {
        _D86RcfFund = value;
    }
    
    // Get<>AsString()
    public string GetD86RcfFundAsString()
    {
        return _D86RcfFund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD86RcfFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D86RcfFund = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}