using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D46RecordN Data Structure

public class D46RecordN
{
    private static int _size = 159;
    // [DEBUG] Class: D46RecordN, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler60, is_external=, is_static_class=False, static_prefix=
    private string _Filler60 ="";
    
    
    
    
    // [DEBUG] Field: D46QuantityN, is_external=, is_static_class=False, static_prefix=
    private decimal _D46QuantityN =0;
    
    
    
    
    // [DEBUG] Field: D46ValueN, is_external=, is_static_class=False, static_prefix=
    private decimal _D46ValueN =0;
    
    
    
    
    // [DEBUG] Field: D46TransactionPriceN, is_external=, is_static_class=False, static_prefix=
    private decimal _D46TransactionPriceN =0;
    
    
    
    
    // [DEBUG] Field: Filler61, is_external=, is_static_class=False, static_prefix=
    private string _Filler61 ="";
    
    
    
    
    // [DEBUG] Field: D46ParentPriceN, is_external=, is_static_class=False, static_prefix=
    private decimal _D46ParentPriceN =0;
    
    
    
    
    // [DEBUG] Field: D46FddPriceN, is_external=, is_static_class=False, static_prefix=
    private decimal _D46FddPriceN =0;
    
    
    
    
    // [DEBUG] Field: D46OsLiabilityN, is_external=, is_static_class=False, static_prefix=
    private decimal _D46OsLiabilityN =0;
    
    
    
    
    // [DEBUG] Field: D46LiabilityPerShareN, is_external=, is_static_class=False, static_prefix=
    private decimal _D46LiabilityPerShareN =0;
    
    
    
    
    // [DEBUG] Field: Filler62, is_external=, is_static_class=False, static_prefix=
    private string _Filler62 ="";
    
    
    
    
    // [DEBUG] Field: D46NumberOfAcqN, is_external=, is_static_class=False, static_prefix=
    private int _D46NumberOfAcqN =0;
    
    
    
    
    // [DEBUG] Field: Filler63, is_external=, is_static_class=False, static_prefix=
    private string _Filler63 ="";
    
    
    
    
    // [DEBUG] Field: Filler64, is_external=, is_static_class=False, static_prefix=
    private string _Filler64 ="";
    
    
    
    
    // [DEBUG] Field: Filler65, is_external=, is_static_class=False, static_prefix=
    private string _Filler65 ="";
    
    
    
    
    // [DEBUG] Field: Filler66, is_external=, is_static_class=False, static_prefix=
    private string _Filler66 ="";
    
    
    
    
    // [DEBUG] Field: Filler67, is_external=, is_static_class=False, static_prefix=
    private string _Filler67 ="";
    
    
    
    
    // [DEBUG] Field: Filler68, is_external=, is_static_class=False, static_prefix=
    private string _Filler68 ="";
    
    
    
    
    // [DEBUG] Field: Filler69, is_external=, is_static_class=False, static_prefix=
    private string _Filler69 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD46RecordNAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler60.PadRight(36));
        result.Append(_D46QuantityN.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D46ValueN.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D46TransactionPriceN.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler61.PadRight(7));
        result.Append(_D46ParentPriceN.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D46FddPriceN.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D46OsLiabilityN.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D46LiabilityPerShareN.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler62.PadRight(40));
        result.Append(_D46NumberOfAcqN.ToString().PadLeft(2, '0'));
        result.Append(_Filler63.PadRight(0));
        result.Append(_Filler64.PadRight(0));
        result.Append(_Filler65.PadRight(0));
        result.Append(_Filler66.PadRight(0));
        result.Append(_Filler67.PadRight(0));
        result.Append(_Filler68.PadRight(0));
        result.Append(_Filler69.PadRight(24));
        
        return result.ToString();
    }
    
    public void SetD46RecordNAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 36 <= data.Length)
        {
            string extracted = data.Substring(offset, 36).Trim();
            SetFiller60(extracted);
        }
        offset += 36;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD46QuantityN(parsedDec);
        }
        offset += 10;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD46ValueN(parsedDec);
        }
        offset += 10;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD46TransactionPriceN(parsedDec);
        }
        offset += 6;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller61(extracted);
        }
        offset += 7;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD46ParentPriceN(parsedDec);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD46FddPriceN(parsedDec);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD46OsLiabilityN(parsedDec);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD46LiabilityPerShareN(parsedDec);
        }
        offset += 6;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller62(extracted);
        }
        offset += 40;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD46NumberOfAcqN(parsedInt);
        }
        offset += 2;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller63(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller64(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller65(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller66(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller67(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller68(extracted);
        }
        offset += 0;
        if (offset + 24 <= data.Length)
        {
            string extracted = data.Substring(offset, 24).Trim();
            SetFiller69(extracted);
        }
        offset += 24;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD46RecordNAsString();
    }
    // Set<>String Override function
    public void SetD46RecordN(string value)
    {
        SetD46RecordNAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller60()
    {
        return _Filler60;
    }
    
    // Standard Setter
    public void SetFiller60(string value)
    {
        _Filler60 = value;
    }
    
    // Get<>AsString()
    public string GetFiller60AsString()
    {
        return _Filler60.PadRight(36);
    }
    
    // Set<>AsString()
    public void SetFiller60AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler60 = value;
    }
    
    // Standard Getter
    public decimal GetD46QuantityN()
    {
        return _D46QuantityN;
    }
    
    // Standard Setter
    public void SetD46QuantityN(decimal value)
    {
        _D46QuantityN = value;
    }
    
    // Get<>AsString()
    public string GetD46QuantityNAsString()
    {
        return _D46QuantityN.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD46QuantityNAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D46QuantityN = parsed;
    }
    
    // Standard Getter
    public decimal GetD46ValueN()
    {
        return _D46ValueN;
    }
    
    // Standard Setter
    public void SetD46ValueN(decimal value)
    {
        _D46ValueN = value;
    }
    
    // Get<>AsString()
    public string GetD46ValueNAsString()
    {
        return _D46ValueN.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD46ValueNAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D46ValueN = parsed;
    }
    
    // Standard Getter
    public decimal GetD46TransactionPriceN()
    {
        return _D46TransactionPriceN;
    }
    
    // Standard Setter
    public void SetD46TransactionPriceN(decimal value)
    {
        _D46TransactionPriceN = value;
    }
    
    // Get<>AsString()
    public string GetD46TransactionPriceNAsString()
    {
        return _D46TransactionPriceN.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD46TransactionPriceNAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D46TransactionPriceN = parsed;
    }
    
    // Standard Getter
    public string GetFiller61()
    {
        return _Filler61;
    }
    
    // Standard Setter
    public void SetFiller61(string value)
    {
        _Filler61 = value;
    }
    
    // Get<>AsString()
    public string GetFiller61AsString()
    {
        return _Filler61.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller61AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler61 = value;
    }
    
    // Standard Getter
    public decimal GetD46ParentPriceN()
    {
        return _D46ParentPriceN;
    }
    
    // Standard Setter
    public void SetD46ParentPriceN(decimal value)
    {
        _D46ParentPriceN = value;
    }
    
    // Get<>AsString()
    public string GetD46ParentPriceNAsString()
    {
        return _D46ParentPriceN.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD46ParentPriceNAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D46ParentPriceN = parsed;
    }
    
    // Standard Getter
    public decimal GetD46FddPriceN()
    {
        return _D46FddPriceN;
    }
    
    // Standard Setter
    public void SetD46FddPriceN(decimal value)
    {
        _D46FddPriceN = value;
    }
    
    // Get<>AsString()
    public string GetD46FddPriceNAsString()
    {
        return _D46FddPriceN.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD46FddPriceNAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D46FddPriceN = parsed;
    }
    
    // Standard Getter
    public decimal GetD46OsLiabilityN()
    {
        return _D46OsLiabilityN;
    }
    
    // Standard Setter
    public void SetD46OsLiabilityN(decimal value)
    {
        _D46OsLiabilityN = value;
    }
    
    // Get<>AsString()
    public string GetD46OsLiabilityNAsString()
    {
        return _D46OsLiabilityN.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD46OsLiabilityNAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D46OsLiabilityN = parsed;
    }
    
    // Standard Getter
    public decimal GetD46LiabilityPerShareN()
    {
        return _D46LiabilityPerShareN;
    }
    
    // Standard Setter
    public void SetD46LiabilityPerShareN(decimal value)
    {
        _D46LiabilityPerShareN = value;
    }
    
    // Get<>AsString()
    public string GetD46LiabilityPerShareNAsString()
    {
        return _D46LiabilityPerShareN.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD46LiabilityPerShareNAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D46LiabilityPerShareN = parsed;
    }
    
    // Standard Getter
    public string GetFiller62()
    {
        return _Filler62;
    }
    
    // Standard Setter
    public void SetFiller62(string value)
    {
        _Filler62 = value;
    }
    
    // Get<>AsString()
    public string GetFiller62AsString()
    {
        return _Filler62.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller62AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler62 = value;
    }
    
    // Standard Getter
    public int GetD46NumberOfAcqN()
    {
        return _D46NumberOfAcqN;
    }
    
    // Standard Setter
    public void SetD46NumberOfAcqN(int value)
    {
        _D46NumberOfAcqN = value;
    }
    
    // Get<>AsString()
    public string GetD46NumberOfAcqNAsString()
    {
        return _D46NumberOfAcqN.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD46NumberOfAcqNAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D46NumberOfAcqN = parsed;
    }
    
    // Standard Getter
    public string GetFiller63()
    {
        return _Filler63;
    }
    
    // Standard Setter
    public void SetFiller63(string value)
    {
        _Filler63 = value;
    }
    
    // Get<>AsString()
    public string GetFiller63AsString()
    {
        return _Filler63.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller63AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler63 = value;
    }
    
    // Standard Getter
    public string GetFiller64()
    {
        return _Filler64;
    }
    
    // Standard Setter
    public void SetFiller64(string value)
    {
        _Filler64 = value;
    }
    
    // Get<>AsString()
    public string GetFiller64AsString()
    {
        return _Filler64.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller64AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler64 = value;
    }
    
    // Standard Getter
    public string GetFiller65()
    {
        return _Filler65;
    }
    
    // Standard Setter
    public void SetFiller65(string value)
    {
        _Filler65 = value;
    }
    
    // Get<>AsString()
    public string GetFiller65AsString()
    {
        return _Filler65.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller65AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler65 = value;
    }
    
    // Standard Getter
    public string GetFiller66()
    {
        return _Filler66;
    }
    
    // Standard Setter
    public void SetFiller66(string value)
    {
        _Filler66 = value;
    }
    
    // Get<>AsString()
    public string GetFiller66AsString()
    {
        return _Filler66.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller66AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler66 = value;
    }
    
    // Standard Getter
    public string GetFiller67()
    {
        return _Filler67;
    }
    
    // Standard Setter
    public void SetFiller67(string value)
    {
        _Filler67 = value;
    }
    
    // Get<>AsString()
    public string GetFiller67AsString()
    {
        return _Filler67.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller67AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler67 = value;
    }
    
    // Standard Getter
    public string GetFiller68()
    {
        return _Filler68;
    }
    
    // Standard Setter
    public void SetFiller68(string value)
    {
        _Filler68 = value;
    }
    
    // Get<>AsString()
    public string GetFiller68AsString()
    {
        return _Filler68.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller68AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler68 = value;
    }
    
    // Standard Getter
    public string GetFiller69()
    {
        return _Filler69;
    }
    
    // Standard Setter
    public void SetFiller69(string value)
    {
        _Filler69 = value;
    }
    
    // Get<>AsString()
    public string GetFiller69AsString()
    {
        return _Filler69.PadRight(24);
    }
    
    // Set<>AsString()
    public void SetFiller69AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler69 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}