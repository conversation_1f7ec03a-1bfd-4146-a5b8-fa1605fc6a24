﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WK.UK.CCH.Equity.CobolDataTransformation;
using Legacy4.Equity.CobMod.Models;

namespace Legacy4.Equity.CobMod.Helper
{
    /// <summary>
    /// C# equivalent of the COBOL TempData program
    /// </summary>
    public class TempData
    {
        // Static instance to persist between calls
        private static TemporaryData _oCompTempData;

        /// <summary>
        /// The main entry point for the TempData functionality
        /// </summary>
        /// <param name="tempDataLinkage">The linkage parameters</param>
        /// <param name="wtSedolHeaders">The SEDOL headers data</param>
        /// <param name="wtTransactions">The transactions data</param>
        /// <param name="wttcCosts">The costs data</param>
        public static void Process(TempDataLinkage tempDataLinkage, WTSedolHeaders wtSedolHeaders,
                                   WTTransactions wtTransactions, WTTCCosts wttcCosts)
        {
            switch (tempDataLinkage.TempDataAction)
            {
                case 1: // WriteTempData
                        // Make sure we have a valid instance
                    if (_oCompTempData == null)
                    {
                        _oCompTempData = new TemporaryData();
                    }

                    byte[] wtsBytes = wtSedolHeaders.ToByteArray(tempDataLinkage.TempDataWtsSize);
                    _oCompTempData.AppendData(wtsBytes);

                    byte[] wttBytes = wtTransactions.ToByteArray(tempDataLinkage.TempDataWttSize);
                    _oCompTempData.AppendData(wttBytes);

                    byte[] wtcBytes = wttcCosts.ToByteArray(tempDataLinkage.TempDataWtcSize);
                    _oCompTempData.AppendData(wtcBytes);
                    break;

                case 2: // ReadTempData
                        // Make sure we have a valid instance
                    if (_oCompTempData == null)
                    {
                        _oCompTempData = new TemporaryData();
                    }

                    _oCompTempData.Rewind();

                    byte[] wtsData = _oCompTempData.ReadNext();
                    wtSedolHeaders.FromByteArray(wtsData, tempDataLinkage.TempDataWtsSize);

                    byte[] wttData = _oCompTempData.ReadNext();
                    wtTransactions.FromByteArray(wttData, tempDataLinkage.TempDataWttSize);

                    byte[] wtcData = _oCompTempData.ReadNext();
                    wttcCosts.FromByteArray(wtcData, tempDataLinkage.TempDataWtcSize);

                    _oCompTempData.Clear();
                    break;

                case 3: // Constructor
                    _oCompTempData = new TemporaryData();
                    break;
            }
        }
    }

    /// <summary>
    /// C# equivalent of the COBOL TempData-linkage structure
    /// </summary>
    public class TempDataLinkage
    {
        public int TempDataAction { get; set; }
        public decimal TempDataWtsSize { get; set; }
        public decimal TempDataWttSize { get; set; }
        public decimal TempDataWtcSize { get; set; }

        // Constants for the TempDataAction field
        public const int WriteTempData = 1;
        public const int ReadTempData = 2;
        public const int Constructor = 3;

        /// <summary>
        /// Convert the object to a byte array
        /// </summary>
        public byte[] ToByteArray()
        {
            // Assuming a simple implementation for now
            byte[] result = new byte[16]; // Size of the structure
            result[0] = (byte)TempDataAction;
            // For the decimal values, we'd need a proper COMP-3 conversion
            // This is a placeholder
            return result;
        }

        /// <summary>
        /// Convert a byte array to this object
        /// </summary>
        public void FromByteArray(byte[] bytes)
        {
            // Assuming a simple implementation for now
            if (bytes.Length >= 16)
            {
                TempDataAction = bytes[0];
                // For the decimal values, we'd need a proper COMP-3 conversion
                // This is a placeholder
            }
        }
    }
}