using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// DTO class representing Cgtdate2LinkageDate1 Data Structure

public class Cgtdate2LinkageDate1
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate1, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd1, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd1 =0;
    
    
    
    
    // [DEBUG] Field: Filler38, is_external=, is_static_class=False, static_prefix=
    private Filler38 _Filler38 = new Filler38();
    
    
    
    
    // [DEBUG] Field: Filler39, is_external=, is_static_class=False, static_prefix=
    private Filler39 _Filler39 = new Filler39();
    
    
    
    
    // [DEBUG] Field: Filler40, is_external=, is_static_class=False, static_prefix=
    private Filler40 _Filler40 = new Filler40();
    
    
    
    
    // [DEBUG] Field: Filler41, is_external=, is_static_class=False, static_prefix=
    private Filler41 _Filler41 = new Filler41();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd1.ToString().PadLeft(8, '0'));
        result.Append(_Filler38.GetFiller38AsString());
        result.Append(_Filler39.GetFiller39AsString());
        result.Append(_Filler40.GetFiller40AsString());
        result.Append(_Filler41.GetFiller41AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd1(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler38.SetFiller38AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler38.SetFiller38AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler39.SetFiller39AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler39.SetFiller39AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler40.SetFiller40AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler40.SetFiller40AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler41.SetFiller41AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler41.SetFiller41AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate1AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate1(string value)
    {
        SetCgtdate2LinkageDate1AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd1()
    {
        return _Cgtdate2Ccyymmdd1;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd1(int value)
    {
        _Cgtdate2Ccyymmdd1 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd1AsString()
    {
        return _Cgtdate2Ccyymmdd1.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd1 = parsed;
    }
    
    // Standard Getter
    public Filler38 GetFiller38()
    {
        return _Filler38;
    }
    
    // Standard Setter
    public void SetFiller38(Filler38 value)
    {
        _Filler38 = value;
    }
    
    // Get<>AsString()
    public string GetFiller38AsString()
    {
        return _Filler38 != null ? _Filler38.GetFiller38AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller38AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler38 == null)
        {
            _Filler38 = new Filler38();
        }
        _Filler38.SetFiller38AsString(value);
    }
    
    // Standard Getter
    public Filler39 GetFiller39()
    {
        return _Filler39;
    }
    
    // Standard Setter
    public void SetFiller39(Filler39 value)
    {
        _Filler39 = value;
    }
    
    // Get<>AsString()
    public string GetFiller39AsString()
    {
        return _Filler39 != null ? _Filler39.GetFiller39AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller39AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler39 == null)
        {
            _Filler39 = new Filler39();
        }
        _Filler39.SetFiller39AsString(value);
    }
    
    // Standard Getter
    public Filler40 GetFiller40()
    {
        return _Filler40;
    }
    
    // Standard Setter
    public void SetFiller40(Filler40 value)
    {
        _Filler40 = value;
    }
    
    // Get<>AsString()
    public string GetFiller40AsString()
    {
        return _Filler40 != null ? _Filler40.GetFiller40AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller40AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler40 == null)
        {
            _Filler40 = new Filler40();
        }
        _Filler40.SetFiller40AsString(value);
    }
    
    // Standard Getter
    public Filler41 GetFiller41()
    {
        return _Filler41;
    }
    
    // Standard Setter
    public void SetFiller41(Filler41 value)
    {
        _Filler41 = value;
    }
    
    // Get<>AsString()
    public string GetFiller41AsString()
    {
        return _Filler41 != null ? _Filler41.GetFiller41AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller41AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler41 == null)
        {
            _Filler41 = new Filler41();
        }
        _Filler41.SetFiller41AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller38(string value)
    {
        _Filler38.SetFiller38AsString(value);
    }
    // Nested Class: Filler38
    public class Filler38
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc1, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc1 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd1, is_external=, is_static_class=False, static_prefix=
        private Filler38.Cgtdate2Yymmdd1 _Cgtdate2Yymmdd1 = new Filler38.Cgtdate2Yymmdd1();
        
        
        
        
    public Filler38() {}
    
    public Filler38(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc1(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd1.SetCgtdate2Yymmdd1AsString(data.Substring(offset, Cgtdate2Yymmdd1.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller38AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc1.PadRight(2));
        result.Append(_Cgtdate2Yymmdd1.GetCgtdate2Yymmdd1AsString());
        
        return result.ToString();
    }
    
    public void SetFiller38AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc1(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd1.SetCgtdate2Yymmdd1AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd1.SetCgtdate2Yymmdd1AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc1()
    {
        return _Cgtdate2Cc1;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc1(string value)
    {
        _Cgtdate2Cc1 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc1AsString()
    {
        return _Cgtdate2Cc1.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc1 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd1 GetCgtdate2Yymmdd1()
    {
        return _Cgtdate2Yymmdd1;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd1(Cgtdate2Yymmdd1 value)
    {
        _Cgtdate2Yymmdd1 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd1AsString()
    {
        return _Cgtdate2Yymmdd1 != null ? _Cgtdate2Yymmdd1.GetCgtdate2Yymmdd1AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd1 == null)
        {
            _Cgtdate2Yymmdd1 = new Cgtdate2Yymmdd1();
        }
        _Cgtdate2Yymmdd1.SetCgtdate2Yymmdd1AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd1
    public class Cgtdate2Yymmdd1
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy1, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy1 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd1, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd1.Cgtdate2Mmdd1 _Cgtdate2Mmdd1 = new Cgtdate2Yymmdd1.Cgtdate2Mmdd1();
        
        
        
        
    public Cgtdate2Yymmdd1() {}
    
    public Cgtdate2Yymmdd1(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy1(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd1.SetCgtdate2Mmdd1AsString(data.Substring(offset, Cgtdate2Mmdd1.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy1.PadRight(2));
        result.Append(_Cgtdate2Mmdd1.GetCgtdate2Mmdd1AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy1(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd1.SetCgtdate2Mmdd1AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd1.SetCgtdate2Mmdd1AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy1()
    {
        return _Cgtdate2Yy1;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy1(string value)
    {
        _Cgtdate2Yy1 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy1AsString()
    {
        return _Cgtdate2Yy1.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy1 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd1 GetCgtdate2Mmdd1()
    {
        return _Cgtdate2Mmdd1;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd1(Cgtdate2Mmdd1 value)
    {
        _Cgtdate2Mmdd1 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd1AsString()
    {
        return _Cgtdate2Mmdd1 != null ? _Cgtdate2Mmdd1.GetCgtdate2Mmdd1AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd1 == null)
        {
            _Cgtdate2Mmdd1 = new Cgtdate2Mmdd1();
        }
        _Cgtdate2Mmdd1.SetCgtdate2Mmdd1AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd1
    public class Cgtdate2Mmdd1
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm1, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm1 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd1, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd1 ="";
        
        
        
        
    public Cgtdate2Mmdd1() {}
    
    public Cgtdate2Mmdd1(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm1(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd1(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd1AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm1.PadRight(2));
        result.Append(_Cgtdate2Dd1.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd1AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm1(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd1(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm1()
    {
        return _Cgtdate2Mm1;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm1(string value)
    {
        _Cgtdate2Mm1 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm1AsString()
    {
        return _Cgtdate2Mm1.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm1 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd1()
    {
        return _Cgtdate2Dd1;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd1(string value)
    {
        _Cgtdate2Dd1 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd1AsString()
    {
        return _Cgtdate2Dd1.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd1 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller39(string value)
{
    _Filler39.SetFiller39AsString(value);
}
// Nested Class: Filler39
public class Filler39
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy1, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy1 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd1, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd1 =0;
    
    
    
    
public Filler39() {}

public Filler39(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy1(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd1(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller39AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy1.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd1.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller39AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy1(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd1(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy1()
{
    return _Cgtdate2CCcyy1;
}

// Standard Setter
public void SetCgtdate2CCcyy1(int value)
{
    _Cgtdate2CCcyy1 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy1AsString()
{
    return _Cgtdate2CCcyy1.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy1 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd1()
{
    return _Cgtdate2CMmdd1;
}

// Standard Setter
public void SetCgtdate2CMmdd1(int value)
{
    _Cgtdate2CMmdd1 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd1AsString()
{
    return _Cgtdate2CMmdd1.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd1 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller40(string value)
{
    _Filler40.SetFiller40AsString(value);
}
// Nested Class: Filler40
public class Filler40
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc1, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc1 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy1, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy1 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm1, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm1 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd1, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd1 =0;
    
    
    
    
public Filler40() {}

public Filler40(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc1(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy1(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm1(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd1(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller40AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc1.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy1.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm1.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd1.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller40AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc1(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy1(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm1(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd1(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc1()
{
    return _Cgtdate2CCc1;
}

// Standard Setter
public void SetCgtdate2CCc1(int value)
{
    _Cgtdate2CCc1 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc1AsString()
{
    return _Cgtdate2CCc1.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc1 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy1()
{
    return _Cgtdate2CYy1;
}

// Standard Setter
public void SetCgtdate2CYy1(int value)
{
    _Cgtdate2CYy1 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy1AsString()
{
    return _Cgtdate2CYy1.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy1 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm1()
{
    return _Cgtdate2CMm1;
}

// Standard Setter
public void SetCgtdate2CMm1(int value)
{
    _Cgtdate2CMm1 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm1AsString()
{
    return _Cgtdate2CMm1.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm1 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd1()
{
    return _Cgtdate2CDd1;
}

// Standard Setter
public void SetCgtdate2CDd1(int value)
{
    _Cgtdate2CDd1 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd1AsString()
{
    return _Cgtdate2CDd1.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd1 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller41(string value)
{
    _Filler41.SetFiller41AsString(value);
}
// Nested Class: Filler41
public class Filler41
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm1, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm1 =0;
    
    
    
    
    // [DEBUG] Field: Filler42, is_external=, is_static_class=False, static_prefix=
    private string _Filler42 ="";
    
    
    
    
public Filler41() {}

public Filler41(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm1(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller42(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller41AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm1.ToString().PadLeft(6, '0'));
    result.Append(_Filler42.PadRight(2));
    
    return result.ToString();
}

public void SetFiller41AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm1(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller42(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm1()
{
    return _Cgtdate2CCcyymm1;
}

// Standard Setter
public void SetCgtdate2CCcyymm1(int value)
{
    _Cgtdate2CCcyymm1 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm1AsString()
{
    return _Cgtdate2CCcyymm1.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm1 = parsed;
}

// Standard Getter
public string GetFiller42()
{
    return _Filler42;
}

// Standard Setter
public void SetFiller42(string value)
{
    _Filler42 = value;
}

// Get<>AsString()
public string GetFiller42AsString()
{
    return _Filler42.PadRight(2);
}

// Set<>AsString()
public void SetFiller42AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler42 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}