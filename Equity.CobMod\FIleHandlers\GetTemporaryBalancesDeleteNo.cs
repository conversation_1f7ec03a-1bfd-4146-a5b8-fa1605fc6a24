using System;
using Equity.CobMod.Models;
using WK.UK.CCH.Equity.Utilities;
namespace Legacy4.Equity.CobMod.Helper
{
    public class GetTemporaryBalancesDeleteNo{
    // Declare object reference for TransactionCategoryDA
    private RunLogManager _runLogManager;

    // Fields for handling condition names (88 Values in COBOL)
    private int _userNumber;

    // Fields for handling condition names (88 Values in COBOL)
    public EnvironmentSettings EnvSettings { get; set; }

    // Fields for handling condition names (88 Values in COBOL)
    public PathSettings PathSettings { get; set; }

    // Fields for handling condition names (88 Values in COBOL)   
 

    // Fields for handling condition names (88 Values in COBOL)   
    public GeneralParameters GeneralParams { get; set; }

    // Fields for handling condition names (88 Values in COBOL)   
    public ExporterSettings ExporterParams { get; set; }

    // Fields for handling condition names (88 Values in COBOL)   
    public LinkageArea Linkage { get; set; }

    // Constructor
    public GetTemporaryBalancesDeleteNo()
    {  
         EnvSettings = new EnvironmentSettings();
        PathSettings = new PathSettings();
        GeneralParams = new GeneralParameters();
        ExporterParams = new ExporterSettings();

        Linkage = new LinkageArea();
    }

    // Retrieves the number of deleted temporary balances8yy
    public int Execute(int userNumber){
        _userNumber = userNumber;
        _runLogManager = RunLogManager.GetRunLog(_userNumber);
        Linkage.Value = _runLogManager.TemporaryBalancesDeleteNo;
       return applyPadding(Linkage.Value,5);
    }

        public int applyPadding(int value,int paddingLentgh) {
            int length = value.ToString().Length;
            int padding = paddingLentgh - length;
            Console.WriteLine("Value: " + value.ToString().PadLeft(padding, '0'));
             return Int32.Parse( value.ToString().PadLeft(padding, '0')); 
        }
   }
}