using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D89TrailerRecord Data Structure

public class D89TrailerRecord
{
    private static int _size = 42;
    // [DEBUG] Class: D89TrailerRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler125, is_external=, is_static_class=False, static_prefix=
    private string _Filler125 ="";
    
    
    // 88-level condition checks for Filler125
    public bool IsD89TrailerFound()
    {
        if (this._Filler125 == "'zzEQUITY GROUP TRAILER'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D89TrailerCount, is_external=, is_static_class=False, static_prefix=
    private int _D89TrailerCount =0;
    
    
    
    
    // [DEBUG] Field: Filler126, is_external=, is_static_class=False, static_prefix=
    private string _Filler126 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD89TrailerRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler125.PadRight(22));
        result.Append(_D89TrailerCount.ToString().PadLeft(6, '0'));
        result.Append(_Filler126.PadRight(14));
        
        return result.ToString();
    }
    
    public void SetD89TrailerRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 22 <= data.Length)
        {
            string extracted = data.Substring(offset, 22).Trim();
            SetFiller125(extracted);
        }
        offset += 22;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD89TrailerCount(parsedInt);
        }
        offset += 6;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            SetFiller126(extracted);
        }
        offset += 14;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD89TrailerRecordAsString();
    }
    // Set<>String Override function
    public void SetD89TrailerRecord(string value)
    {
        SetD89TrailerRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller125()
    {
        return _Filler125;
    }
    
    // Standard Setter
    public void SetFiller125(string value)
    {
        _Filler125 = value;
    }
    
    // Get<>AsString()
    public string GetFiller125AsString()
    {
        return _Filler125.PadRight(22);
    }
    
    // Set<>AsString()
    public void SetFiller125AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler125 = value;
    }
    
    // Standard Getter
    public int GetD89TrailerCount()
    {
        return _D89TrailerCount;
    }
    
    // Standard Setter
    public void SetD89TrailerCount(int value)
    {
        _D89TrailerCount = value;
    }
    
    // Get<>AsString()
    public string GetD89TrailerCountAsString()
    {
        return _D89TrailerCount.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD89TrailerCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D89TrailerCount = parsed;
    }
    
    // Standard Getter
    public string GetFiller126()
    {
        return _Filler126;
    }
    
    // Standard Setter
    public void SetFiller126(string value)
    {
        _Filler126 = value;
    }
    
    // Get<>AsString()
    public string GetFiller126AsString()
    {
        return _Filler126.PadRight(14);
    }
    
    // Set<>AsString()
    public void SetFiller126AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler126 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}