using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtProcessedFundRecords Data Structure

public class WtProcessedFundRecords
{
    private static int _size = 56;
    // [DEBUG] Class: WtProcessedFundRecords, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler483, is_external=, is_static_class=False, static_prefix=
    private string _Filler483 ="PROCESSED FUND RECORD TABLE===============";
    
    
    
    
    // [DEBUG] Field: WtpfMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtpfMaxTableSize =50000;
    
    
    
    
    // [DEBUG] Field: WtpfOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtpfOccurs =0;
    
    
    
    
    // [DEBUG] Field: WtpfFundRecordTable, is_external=, is_static_class=False, static_prefix=
    private WtpfFundRecordTable _WtpfFundRecordTable = new WtpfFundRecordTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtProcessedFundRecordsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler483.PadRight(42));
        result.Append(_WtpfMaxTableSize.ToString().PadLeft(5, '0'));
        result.Append(_WtpfOccurs.ToString().PadLeft(5, '0'));
        result.Append(_WtpfFundRecordTable.GetWtpfFundRecordTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtProcessedFundRecordsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 42 <= data.Length)
        {
            string extracted = data.Substring(offset, 42).Trim();
            SetFiller483(extracted);
        }
        offset += 42;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtpfMaxTableSize(parsedInt);
        }
        offset += 5;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtpfOccurs(parsedInt);
        }
        offset += 5;
        if (offset + 4 <= data.Length)
        {
            _WtpfFundRecordTable.SetWtpfFundRecordTableAsString(data.Substring(offset, 4));
        }
        else
        {
            _WtpfFundRecordTable.SetWtpfFundRecordTableAsString(data.Substring(offset));
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtProcessedFundRecordsAsString();
    }
    // Set<>String Override function
    public void SetWtProcessedFundRecords(string value)
    {
        SetWtProcessedFundRecordsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller483()
    {
        return _Filler483;
    }
    
    // Standard Setter
    public void SetFiller483(string value)
    {
        _Filler483 = value;
    }
    
    // Get<>AsString()
    public string GetFiller483AsString()
    {
        return _Filler483.PadRight(42);
    }
    
    // Set<>AsString()
    public void SetFiller483AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler483 = value;
    }
    
    // Standard Getter
    public int GetWtpfMaxTableSize()
    {
        return _WtpfMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtpfMaxTableSize(int value)
    {
        _WtpfMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtpfMaxTableSizeAsString()
    {
        return _WtpfMaxTableSize.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWtpfMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtpfMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtpfOccurs()
    {
        return _WtpfOccurs;
    }
    
    // Standard Setter
    public void SetWtpfOccurs(int value)
    {
        _WtpfOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtpfOccursAsString()
    {
        return _WtpfOccurs.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWtpfOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtpfOccurs = parsed;
    }
    
    // Standard Getter
    public WtpfFundRecordTable GetWtpfFundRecordTable()
    {
        return _WtpfFundRecordTable;
    }
    
    // Standard Setter
    public void SetWtpfFundRecordTable(WtpfFundRecordTable value)
    {
        _WtpfFundRecordTable = value;
    }
    
    // Get<>AsString()
    public string GetWtpfFundRecordTableAsString()
    {
        return _WtpfFundRecordTable != null ? _WtpfFundRecordTable.GetWtpfFundRecordTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtpfFundRecordTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtpfFundRecordTable == null)
        {
            _WtpfFundRecordTable = new WtpfFundRecordTable();
        }
        _WtpfFundRecordTable.SetWtpfFundRecordTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtpfFundRecordTable(string value)
    {
        _WtpfFundRecordTable.SetWtpfFundRecordTableAsString(value);
    }
    // Nested Class: WtpfFundRecordTable
    public class WtpfFundRecordTable
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtpfElement, is_external=, is_static_class=False, static_prefix=
        private WtpfFundRecordTable.WtpfElement[] _WtpfElement = new WtpfFundRecordTable.WtpfElement[50000];
        
        public void InitializeWtpfElementArray()
        {
            for (int i = 0; i < 50000; i++)
            {
                _WtpfElement[i] = new WtpfFundRecordTable.WtpfElement();
            }
        }
        
        
        
    public WtpfFundRecordTable() {}
    
    public WtpfFundRecordTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWtpfElementArray();
        for (int i = 0; i < 50000; i++)
        {
            _WtpfElement[i].SetWtpfElementAsString(data.Substring(offset, 4));
            offset += 4;
        }
        
    }
    
    // Serialization methods
    public string GetWtpfFundRecordTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 50000; i++)
        {
            result.Append(_WtpfElement[i].GetWtpfElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtpfFundRecordTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 50000; i++)
        {
            if (offset + 4 > data.Length) break;
            string val = data.Substring(offset, 4);
            
            _WtpfElement[i].SetWtpfElementAsString(val);
            offset += 4;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtpfElement
    public WtpfElement GetWtpfElementAt(int index)
    {
        return _WtpfElement[index];
    }
    
    public void SetWtpfElementAt(int index, WtpfElement value)
    {
        _WtpfElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtpfElement GetWtpfElement()
    {
        return _WtpfElement != null && _WtpfElement.Length > 0
        ? _WtpfElement[0]
        : new WtpfElement();
    }
    
    public void SetWtpfElement(WtpfElement value)
    {
        if (_WtpfElement == null || _WtpfElement.Length == 0)
        _WtpfElement = new WtpfElement[1];
        _WtpfElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtpfElement
    public class WtpfElement
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtpfFundCode, is_external=, is_static_class=False, static_prefix=
        private string _WtpfFundCode ="";
        
        
        
        
    public WtpfElement() {}
    
    public WtpfElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtpfFundCode(data.Substring(offset, 4).Trim());
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetWtpfElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtpfFundCode.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetWtpfElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtpfFundCode(extracted);
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtpfFundCode()
    {
        return _WtpfFundCode;
    }
    
    // Standard Setter
    public void SetWtpfFundCode(string value)
    {
        _WtpfFundCode = value;
    }
    
    // Get<>AsString()
    public string GetWtpfFundCodeAsString()
    {
        return _WtpfFundCode.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtpfFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtpfFundCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}

}}
