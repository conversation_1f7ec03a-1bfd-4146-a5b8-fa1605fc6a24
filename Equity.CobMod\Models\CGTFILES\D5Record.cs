using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D5Record Data Structure

public class D5Record
{
    private static int _size = 133;
    // [DEBUG] Class: D5Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D5PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D5PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D5ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D5ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD5RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D5PrintControl.PadRight(1));
        result.Append(_D5ReportLine.PadRight(132));
        
        return result.ToString();
    }
    
    public void SetD5RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD5PrintControl(extracted);
        }
        offset += 1;
        if (offset + 132 <= data.Length)
        {
            string extracted = data.Substring(offset, 132).Trim();
            SetD5ReportLine(extracted);
        }
        offset += 132;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD5RecordAsString();
    }
    // Set<>String Override function
    public void SetD5Record(string value)
    {
        SetD5RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD5PrintControl()
    {
        return _D5PrintControl;
    }
    
    // Standard Setter
    public void SetD5PrintControl(string value)
    {
        _D5PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD5PrintControlAsString()
    {
        return _D5PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD5PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D5PrintControl = value;
    }
    
    // Standard Getter
    public string GetD5ReportLine()
    {
        return _D5ReportLine;
    }
    
    // Standard Setter
    public void SetD5ReportLine(string value)
    {
        _D5ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD5ReportLineAsString()
    {
        return _D5ReportLine.PadRight(132);
    }
    
    // Set<>AsString()
    public void SetD5ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D5ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}