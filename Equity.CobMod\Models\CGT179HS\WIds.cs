using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt179HsDTO
{// DTO class representing WIds Data Structure

public class WIds
{
    private static int _size = 81;
    // [DEBUG] Class: WIds, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler94, is_external=, is_static_class=False, static_prefix=
    private string _Filler94 ="";
    
    
    
    
    // [DEBUG] Field: Filler95, is_external=, is_static_class=False, static_prefix=
    private string _Filler95 ="";
    
    
    
    
    // [DEBUG] Field: Filler96, is_external=, is_static_class=False, static_prefix=
    private string _Filler96 ="";
    
    
    
    
    // [DEBUG] Field: Filler97, is_external=, is_static_class=False, static_prefix=
    private string _Filler97 ="";
    
    
    
    
    // [DEBUG] Field: Filler98, is_external=, is_static_class=False, static_prefix=
    private string _Filler98 ="";
    
    
    
    
    // [DEBUG] Field: Filler99, is_external=, is_static_class=False, static_prefix=
    private string _Filler99 ="";
    
    
    
    
    // [DEBUG] Field: Filler100, is_external=, is_static_class=False, static_prefix=
    private string _Filler100 ="";
    
    
    
    
    // [DEBUG] Field: Filler101, is_external=, is_static_class=False, static_prefix=
    private string _Filler101 ="";
    
    
    
    
    // [DEBUG] Field: Filler102, is_external=, is_static_class=False, static_prefix=
    private string _Filler102 ="";
    
    
    
    
    
    // Serialization methods
    public string GetWIdsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler94.PadRight(8));
        result.Append(_Filler95.PadRight(8));
        result.Append(_Filler96.PadRight(8));
        result.Append(_Filler97.PadRight(8));
        result.Append(_Filler98.PadRight(8));
        result.Append(_Filler99.PadRight(8));
        result.Append(_Filler100.PadRight(8));
        result.Append(_Filler101.PadRight(24));
        result.Append(_Filler102.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWIdsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller94(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller95(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller96(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller97(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller98(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller99(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller100(extracted);
        }
        offset += 8;
        if (offset + 24 <= data.Length)
        {
            string extracted = data.Substring(offset, 24).Trim();
            SetFiller101(extracted);
        }
        offset += 24;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller102(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWIdsAsString();
    }
    // Set<>String Override function
    public void SetWIds(string value)
    {
        SetWIdsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller94()
    {
        return _Filler94;
    }
    
    // Standard Setter
    public void SetFiller94(string value)
    {
        _Filler94 = value;
    }
    
    // Get<>AsString()
    public string GetFiller94AsString()
    {
        return _Filler94.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller94AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler94 = value;
    }
    
    // Standard Getter
    public string GetFiller95()
    {
        return _Filler95;
    }
    
    // Standard Setter
    public void SetFiller95(string value)
    {
        _Filler95 = value;
    }
    
    // Get<>AsString()
    public string GetFiller95AsString()
    {
        return _Filler95.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller95AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler95 = value;
    }
    
    // Standard Getter
    public string GetFiller96()
    {
        return _Filler96;
    }
    
    // Standard Setter
    public void SetFiller96(string value)
    {
        _Filler96 = value;
    }
    
    // Get<>AsString()
    public string GetFiller96AsString()
    {
        return _Filler96.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller96AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler96 = value;
    }
    
    // Standard Getter
    public string GetFiller97()
    {
        return _Filler97;
    }
    
    // Standard Setter
    public void SetFiller97(string value)
    {
        _Filler97 = value;
    }
    
    // Get<>AsString()
    public string GetFiller97AsString()
    {
        return _Filler97.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller97AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler97 = value;
    }
    
    // Standard Getter
    public string GetFiller98()
    {
        return _Filler98;
    }
    
    // Standard Setter
    public void SetFiller98(string value)
    {
        _Filler98 = value;
    }
    
    // Get<>AsString()
    public string GetFiller98AsString()
    {
        return _Filler98.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller98AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler98 = value;
    }
    
    // Standard Getter
    public string GetFiller99()
    {
        return _Filler99;
    }
    
    // Standard Setter
    public void SetFiller99(string value)
    {
        _Filler99 = value;
    }
    
    // Get<>AsString()
    public string GetFiller99AsString()
    {
        return _Filler99.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller99AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler99 = value;
    }
    
    // Standard Getter
    public string GetFiller100()
    {
        return _Filler100;
    }
    
    // Standard Setter
    public void SetFiller100(string value)
    {
        _Filler100 = value;
    }
    
    // Get<>AsString()
    public string GetFiller100AsString()
    {
        return _Filler100.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller100AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler100 = value;
    }
    
    // Standard Getter
    public string GetFiller101()
    {
        return _Filler101;
    }
    
    // Standard Setter
    public void SetFiller101(string value)
    {
        _Filler101 = value;
    }
    
    // Get<>AsString()
    public string GetFiller101AsString()
    {
        return _Filler101.PadRight(24);
    }
    
    // Set<>AsString()
    public void SetFiller101AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler101 = value;
    }
    
    // Standard Getter
    public string GetFiller102()
    {
        return _Filler102;
    }
    
    // Standard Setter
    public void SetFiller102(string value)
    {
        _Filler102 = value;
    }
    
    // Get<>AsString()
    public string GetFiller102AsString()
    {
        return _Filler102.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller102AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler102 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}