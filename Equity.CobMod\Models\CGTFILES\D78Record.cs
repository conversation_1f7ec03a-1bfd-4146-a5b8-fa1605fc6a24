using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D78Record Data Structure

public class D78Record
{
    private static int _size = 165;
    // [DEBUG] Class: D78Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D78PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D78PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D78ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D78ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD78RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D78PrintControl.PadRight(1));
        result.Append(_D78ReportLine.PadRight(164));
        
        return result.ToString();
    }
    
    public void SetD78RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD78PrintControl(extracted);
        }
        offset += 1;
        if (offset + 164 <= data.Length)
        {
            string extracted = data.Substring(offset, 164).Trim();
            SetD78ReportLine(extracted);
        }
        offset += 164;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD78RecordAsString();
    }
    // Set<>String Override function
    public void SetD78Record(string value)
    {
        SetD78RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD78PrintControl()
    {
        return _D78PrintControl;
    }
    
    // Standard Setter
    public void SetD78PrintControl(string value)
    {
        _D78PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD78PrintControlAsString()
    {
        return _D78PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD78PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D78PrintControl = value;
    }
    
    // Standard Getter
    public string GetD78ReportLine()
    {
        return _D78ReportLine;
    }
    
    // Standard Setter
    public void SetD78ReportLine(string value)
    {
        _D78ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD78ReportLineAsString()
    {
        return _D78ReportLine.PadRight(164);
    }
    
    // Set<>AsString()
    public void SetD78ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D78ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}