using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D50Record Data Structure

public class D50Record
{
    private static int _size = 167;
    // [DEBUG] Class: D50Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D50PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D50PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D50ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D50ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD50RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D50PrintControl.PadRight(1));
        result.Append(_D50ReportLine.PadRight(166));
        
        return result.ToString();
    }
    
    public void SetD50RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD50PrintControl(extracted);
        }
        offset += 1;
        if (offset + 166 <= data.Length)
        {
            string extracted = data.Substring(offset, 166).Trim();
            SetD50ReportLine(extracted);
        }
        offset += 166;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD50RecordAsString();
    }
    // Set<>String Override function
    public void SetD50Record(string value)
    {
        SetD50RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD50PrintControl()
    {
        return _D50PrintControl;
    }
    
    // Standard Setter
    public void SetD50PrintControl(string value)
    {
        _D50PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD50PrintControlAsString()
    {
        return _D50PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD50PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D50PrintControl = value;
    }
    
    // Standard Getter
    public string GetD50ReportLine()
    {
        return _D50ReportLine;
    }
    
    // Standard Setter
    public void SetD50ReportLine(string value)
    {
        _D50ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD50ReportLineAsString()
    {
        return _D50ReportLine.PadRight(166);
    }
    
    // Set<>AsString()
    public void SetD50ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D50ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}