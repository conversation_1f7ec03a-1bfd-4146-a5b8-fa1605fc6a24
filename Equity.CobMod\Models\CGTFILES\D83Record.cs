using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D83Record Data Structure

public class D83Record
{
    private static int _size = 127;
    // [DEBUG] Class: D83Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D83FundCode, is_external=, is_static_class=False, static_prefix=
    private string _D83FundCode ="";
    
    
    
    
    // [DEBUG] Field: D83SedolCode, is_external=, is_static_class=False, static_prefix=
    private string _D83SedolCode ="";
    
    
    
    
    // [DEBUG] Field: D83ContractNo, is_external=, is_static_class=False, static_prefix=
    private string _D83ContractNo ="";
    
    
    
    
    // [DEBUG] Field: D83BalanceType, is_external=, is_static_class=False, static_prefix=
    private string _D83BalanceType ="";
    
    
    
    
    // [DEBUG] Field: D83Quantity, is_external=, is_static_class=False, static_prefix=
    private decimal _D83Quantity =0;
    
    
    
    
    // [DEBUG] Field: D83AcqCost, is_external=, is_static_class=False, static_prefix=
    private decimal _D83AcqCost =0;
    
    
    
    
    // [DEBUG] Field: D83AcqDate, is_external=, is_static_class=False, static_prefix=
    private string _D83AcqDate ="";
    
    
    
    
    // [DEBUG] Field: D83IndexCost, is_external=, is_static_class=False, static_prefix=
    private decimal _D83IndexCost =0;
    
    
    
    
    // [DEBUG] Field: D83IndexDate, is_external=, is_static_class=False, static_prefix=
    private string _D83IndexDate ="";
    
    
    
    
    // [DEBUG] Field: D831965Value, is_external=, is_static_class=False, static_prefix=
    private decimal _D831965Value =0;
    
    
    
    
    // [DEBUG] Field: D831982Value, is_external=, is_static_class=False, static_prefix=
    private decimal _D831982Value =0;
    
    
    
    
    // [DEBUG] Field: D83BookValue, is_external=, is_static_class=False, static_prefix=
    private decimal _D83BookValue =0;
    
    
    
    
    // [DEBUG] Field: D83NoOfExtraCosts, is_external=, is_static_class=False, static_prefix=
    private int _D83NoOfExtraCosts =0;
    
    
    
    
    // [DEBUG] Field: D83OverrideFlag, is_external=, is_static_class=False, static_prefix=
    private string _D83OverrideFlag ="";
    
    
    
    
    // [DEBUG] Field: D83TrancheFlag, is_external=, is_static_class=False, static_prefix=
    private string _D83TrancheFlag ="";
    
    
    
    
    // [DEBUG] Field: D83TaperDate, is_external=, is_static_class=False, static_prefix=
    private string _D83TaperDate ="";
    
    
    
    
    // [DEBUG] Field: D83TaperUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _D83TaperUnits =0;
    
    
    
    
    
    // Serialization methods
    public string GetD83RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D83FundCode.PadRight(4));
        result.Append(_D83SedolCode.PadRight(7));
        result.Append(_D83ContractNo.PadRight(10));
        result.Append(_D83BalanceType.PadRight(0));
        result.Append(_D83Quantity.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D83AcqCost.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D83AcqDate.PadRight(6));
        result.Append(_D83IndexCost.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D83IndexDate.PadRight(6));
        result.Append(_D831965Value.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D831982Value.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D83BookValue.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D83NoOfExtraCosts.ToString().PadLeft(2, '0'));
        result.Append(_D83OverrideFlag.PadRight(1));
        result.Append(_D83TrancheFlag.PadRight(1));
        result.Append(_D83TaperDate.PadRight(6));
        result.Append(_D83TaperUnits.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        
        return result.ToString();
    }
    
    public void SetD83RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD83FundCode(extracted);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetD83SedolCode(extracted);
        }
        offset += 7;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetD83ContractNo(extracted);
        }
        offset += 10;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD83BalanceType(extracted);
        }
        offset += 0;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD83Quantity(parsedDec);
        }
        offset += 12;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD83AcqCost(parsedDec);
        }
        offset += 12;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetD83AcqDate(extracted);
        }
        offset += 6;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD83IndexCost(parsedDec);
        }
        offset += 12;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetD83IndexDate(extracted);
        }
        offset += 6;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD831965Value(parsedDec);
        }
        offset += 12;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD831982Value(parsedDec);
        }
        offset += 12;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD83BookValue(parsedDec);
        }
        offset += 12;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD83NoOfExtraCosts(parsedInt);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD83OverrideFlag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD83TrancheFlag(extracted);
        }
        offset += 1;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetD83TaperDate(extracted);
        }
        offset += 6;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD83TaperUnits(parsedDec);
        }
        offset += 12;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD83RecordAsString();
    }
    // Set<>String Override function
    public void SetD83Record(string value)
    {
        SetD83RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD83FundCode()
    {
        return _D83FundCode;
    }
    
    // Standard Setter
    public void SetD83FundCode(string value)
    {
        _D83FundCode = value;
    }
    
    // Get<>AsString()
    public string GetD83FundCodeAsString()
    {
        return _D83FundCode.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD83FundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D83FundCode = value;
    }
    
    // Standard Getter
    public string GetD83SedolCode()
    {
        return _D83SedolCode;
    }
    
    // Standard Setter
    public void SetD83SedolCode(string value)
    {
        _D83SedolCode = value;
    }
    
    // Get<>AsString()
    public string GetD83SedolCodeAsString()
    {
        return _D83SedolCode.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetD83SedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D83SedolCode = value;
    }
    
    // Standard Getter
    public string GetD83ContractNo()
    {
        return _D83ContractNo;
    }
    
    // Standard Setter
    public void SetD83ContractNo(string value)
    {
        _D83ContractNo = value;
    }
    
    // Get<>AsString()
    public string GetD83ContractNoAsString()
    {
        return _D83ContractNo.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetD83ContractNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D83ContractNo = value;
    }
    
    // Standard Getter
    public string GetD83BalanceType()
    {
        return _D83BalanceType;
    }
    
    // Standard Setter
    public void SetD83BalanceType(string value)
    {
        _D83BalanceType = value;
    }
    
    // Get<>AsString()
    public string GetD83BalanceTypeAsString()
    {
        return _D83BalanceType.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD83BalanceTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D83BalanceType = value;
    }
    
    // Standard Getter
    public decimal GetD83Quantity()
    {
        return _D83Quantity;
    }
    
    // Standard Setter
    public void SetD83Quantity(decimal value)
    {
        _D83Quantity = value;
    }
    
    // Get<>AsString()
    public string GetD83QuantityAsString()
    {
        return _D83Quantity.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD83QuantityAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D83Quantity = parsed;
    }
    
    // Standard Getter
    public decimal GetD83AcqCost()
    {
        return _D83AcqCost;
    }
    
    // Standard Setter
    public void SetD83AcqCost(decimal value)
    {
        _D83AcqCost = value;
    }
    
    // Get<>AsString()
    public string GetD83AcqCostAsString()
    {
        return _D83AcqCost.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD83AcqCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D83AcqCost = parsed;
    }
    
    // Standard Getter
    public string GetD83AcqDate()
    {
        return _D83AcqDate;
    }
    
    // Standard Setter
    public void SetD83AcqDate(string value)
    {
        _D83AcqDate = value;
    }
    
    // Get<>AsString()
    public string GetD83AcqDateAsString()
    {
        return _D83AcqDate.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetD83AcqDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D83AcqDate = value;
    }
    
    // Standard Getter
    public decimal GetD83IndexCost()
    {
        return _D83IndexCost;
    }
    
    // Standard Setter
    public void SetD83IndexCost(decimal value)
    {
        _D83IndexCost = value;
    }
    
    // Get<>AsString()
    public string GetD83IndexCostAsString()
    {
        return _D83IndexCost.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD83IndexCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D83IndexCost = parsed;
    }
    
    // Standard Getter
    public string GetD83IndexDate()
    {
        return _D83IndexDate;
    }
    
    // Standard Setter
    public void SetD83IndexDate(string value)
    {
        _D83IndexDate = value;
    }
    
    // Get<>AsString()
    public string GetD83IndexDateAsString()
    {
        return _D83IndexDate.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetD83IndexDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D83IndexDate = value;
    }
    
    // Standard Getter
    public decimal GetD831965Value()
    {
        return _D831965Value;
    }
    
    // Standard Setter
    public void SetD831965Value(decimal value)
    {
        _D831965Value = value;
    }
    
    // Get<>AsString()
    public string GetD831965ValueAsString()
    {
        return _D831965Value.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD831965ValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D831965Value = parsed;
    }
    
    // Standard Getter
    public decimal GetD831982Value()
    {
        return _D831982Value;
    }
    
    // Standard Setter
    public void SetD831982Value(decimal value)
    {
        _D831982Value = value;
    }
    
    // Get<>AsString()
    public string GetD831982ValueAsString()
    {
        return _D831982Value.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD831982ValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D831982Value = parsed;
    }
    
    // Standard Getter
    public decimal GetD83BookValue()
    {
        return _D83BookValue;
    }
    
    // Standard Setter
    public void SetD83BookValue(decimal value)
    {
        _D83BookValue = value;
    }
    
    // Get<>AsString()
    public string GetD83BookValueAsString()
    {
        return _D83BookValue.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD83BookValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D83BookValue = parsed;
    }
    
    // Standard Getter
    public int GetD83NoOfExtraCosts()
    {
        return _D83NoOfExtraCosts;
    }
    
    // Standard Setter
    public void SetD83NoOfExtraCosts(int value)
    {
        _D83NoOfExtraCosts = value;
    }
    
    // Get<>AsString()
    public string GetD83NoOfExtraCostsAsString()
    {
        return _D83NoOfExtraCosts.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD83NoOfExtraCostsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D83NoOfExtraCosts = parsed;
    }
    
    // Standard Getter
    public string GetD83OverrideFlag()
    {
        return _D83OverrideFlag;
    }
    
    // Standard Setter
    public void SetD83OverrideFlag(string value)
    {
        _D83OverrideFlag = value;
    }
    
    // Get<>AsString()
    public string GetD83OverrideFlagAsString()
    {
        return _D83OverrideFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD83OverrideFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D83OverrideFlag = value;
    }
    
    // Standard Getter
    public string GetD83TrancheFlag()
    {
        return _D83TrancheFlag;
    }
    
    // Standard Setter
    public void SetD83TrancheFlag(string value)
    {
        _D83TrancheFlag = value;
    }
    
    // Get<>AsString()
    public string GetD83TrancheFlagAsString()
    {
        return _D83TrancheFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD83TrancheFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D83TrancheFlag = value;
    }
    
    // Standard Getter
    public string GetD83TaperDate()
    {
        return _D83TaperDate;
    }
    
    // Standard Setter
    public void SetD83TaperDate(string value)
    {
        _D83TaperDate = value;
    }
    
    // Get<>AsString()
    public string GetD83TaperDateAsString()
    {
        return _D83TaperDate.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetD83TaperDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D83TaperDate = value;
    }
    
    // Standard Getter
    public decimal GetD83TaperUnits()
    {
        return _D83TaperUnits;
    }
    
    // Standard Setter
    public void SetD83TaperUnits(decimal value)
    {
        _D83TaperUnits = value;
    }
    
    // Get<>AsString()
    public string GetD83TaperUnitsAsString()
    {
        return _D83TaperUnits.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD83TaperUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D83TaperUnits = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}