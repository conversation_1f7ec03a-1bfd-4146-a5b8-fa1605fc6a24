using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtRestructureDates Data Structure

public class WtRestructureDates
{
    private static int _size = 46;
    // [DEBUG] Class: WtRestructureDates, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler478, is_external=, is_static_class=False, static_prefix=
    private string _Filler478 ="RESTRUCTURE-DATES TABLE=========";
    
    
    
    
    // [DEBUG] Field: WtrsdMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtrsdMaxTableSize =300;
    
    
    
    
    // [DEBUG] Field: WtrsdOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtrsdOccurs =0;
    
    
    
    
    // [DEBUG] Field: WtrsdDatesTable, is_external=, is_static_class=False, static_prefix=
    private WtrsdDatesTable _WtrsdDatesTable = new WtrsdDatesTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtRestructureDatesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler478.PadRight(32));
        result.Append(_WtrsdMaxTableSize.ToString().PadLeft(3, '0'));
        result.Append(_WtrsdOccurs.ToString().PadLeft(3, '0'));
        result.Append(_WtrsdDatesTable.GetWtrsdDatesTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtRestructureDatesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 32 <= data.Length)
        {
            string extracted = data.Substring(offset, 32).Trim();
            SetFiller478(extracted);
        }
        offset += 32;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtrsdMaxTableSize(parsedInt);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtrsdOccurs(parsedInt);
        }
        offset += 3;
        if (offset + 8 <= data.Length)
        {
            _WtrsdDatesTable.SetWtrsdDatesTableAsString(data.Substring(offset, 8));
        }
        else
        {
            _WtrsdDatesTable.SetWtrsdDatesTableAsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtRestructureDatesAsString();
    }
    // Set<>String Override function
    public void SetWtRestructureDates(string value)
    {
        SetWtRestructureDatesAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller478()
    {
        return _Filler478;
    }
    
    // Standard Setter
    public void SetFiller478(string value)
    {
        _Filler478 = value;
    }
    
    // Get<>AsString()
    public string GetFiller478AsString()
    {
        return _Filler478.PadRight(32);
    }
    
    // Set<>AsString()
    public void SetFiller478AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler478 = value;
    }
    
    // Standard Getter
    public int GetWtrsdMaxTableSize()
    {
        return _WtrsdMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtrsdMaxTableSize(int value)
    {
        _WtrsdMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtrsdMaxTableSizeAsString()
    {
        return _WtrsdMaxTableSize.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetWtrsdMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtrsdMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtrsdOccurs()
    {
        return _WtrsdOccurs;
    }
    
    // Standard Setter
    public void SetWtrsdOccurs(int value)
    {
        _WtrsdOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtrsdOccursAsString()
    {
        return _WtrsdOccurs.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetWtrsdOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtrsdOccurs = parsed;
    }
    
    // Standard Getter
    public WtrsdDatesTable GetWtrsdDatesTable()
    {
        return _WtrsdDatesTable;
    }
    
    // Standard Setter
    public void SetWtrsdDatesTable(WtrsdDatesTable value)
    {
        _WtrsdDatesTable = value;
    }
    
    // Get<>AsString()
    public string GetWtrsdDatesTableAsString()
    {
        return _WtrsdDatesTable != null ? _WtrsdDatesTable.GetWtrsdDatesTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtrsdDatesTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtrsdDatesTable == null)
        {
            _WtrsdDatesTable = new WtrsdDatesTable();
        }
        _WtrsdDatesTable.SetWtrsdDatesTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtrsdDatesTable(string value)
    {
        _WtrsdDatesTable.SetWtrsdDatesTableAsString(value);
    }
    // Nested Class: WtrsdDatesTable
    public class WtrsdDatesTable
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtrsdElement, is_external=, is_static_class=False, static_prefix=
        private WtrsdDatesTable.WtrsdElement[] _WtrsdElement = new WtrsdDatesTable.WtrsdElement[300];
        
        public void InitializeWtrsdElementArray()
        {
            for (int i = 0; i < 300; i++)
            {
                _WtrsdElement[i] = new WtrsdDatesTable.WtrsdElement();
            }
        }
        
        
        
    public WtrsdDatesTable() {}
    
    public WtrsdDatesTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWtrsdElementArray();
        for (int i = 0; i < 300; i++)
        {
            _WtrsdElement[i].SetWtrsdElementAsString(data.Substring(offset, 8));
            offset += 8;
        }
        
    }
    
    // Serialization methods
    public string GetWtrsdDatesTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 300; i++)
        {
            result.Append(_WtrsdElement[i].GetWtrsdElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtrsdDatesTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 300; i++)
        {
            if (offset + 8 > data.Length) break;
            string val = data.Substring(offset, 8);
            
            _WtrsdElement[i].SetWtrsdElementAsString(val);
            offset += 8;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtrsdElement
    public WtrsdElement GetWtrsdElementAt(int index)
    {
        return _WtrsdElement[index];
    }
    
    public void SetWtrsdElementAt(int index, WtrsdElement value)
    {
        _WtrsdElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtrsdElement GetWtrsdElement()
    {
        return _WtrsdElement != null && _WtrsdElement.Length > 0
        ? _WtrsdElement[0]
        : new WtrsdElement();
    }
    
    public void SetWtrsdElement(WtrsdElement value)
    {
        if (_WtrsdElement == null || _WtrsdElement.Length == 0)
        _WtrsdElement = new WtrsdElement[1];
        _WtrsdElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtrsdElement
    public class WtrsdElement
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtrsdKey, is_external=, is_static_class=False, static_prefix=
        private WtrsdElement.WtrsdKey _WtrsdKey = new WtrsdElement.WtrsdKey();
        
        
        
        
    public WtrsdElement() {}
    
    public WtrsdElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WtrsdKey.SetWtrsdKeyAsString(data.Substring(offset, WtrsdKey.GetSize()));
        offset += 8;
        
    }
    
    // Serialization methods
    public string GetWtrsdElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtrsdKey.GetWtrsdKeyAsString());
        
        return result.ToString();
    }
    
    public void SetWtrsdElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            _WtrsdKey.SetWtrsdKeyAsString(data.Substring(offset, 8));
        }
        else
        {
            _WtrsdKey.SetWtrsdKeyAsString(data.Substring(offset));
        }
        offset += 8;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WtrsdKey GetWtrsdKey()
    {
        return _WtrsdKey;
    }
    
    // Standard Setter
    public void SetWtrsdKey(WtrsdKey value)
    {
        _WtrsdKey = value;
    }
    
    // Get<>AsString()
    public string GetWtrsdKeyAsString()
    {
        return _WtrsdKey != null ? _WtrsdKey.GetWtrsdKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtrsdKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtrsdKey == null)
        {
            _WtrsdKey = new WtrsdKey();
        }
        _WtrsdKey.SetWtrsdKeyAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtrsdKey
    public class WtrsdKey
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtrsdDate, is_external=, is_static_class=False, static_prefix=
        private string _WtrsdDate ="";
        
        
        
        
    public WtrsdKey() {}
    
    public WtrsdKey(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtrsdDate(data.Substring(offset, 8).Trim());
        offset += 8;
        
    }
    
    // Serialization methods
    public string GetWtrsdKeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtrsdDate.PadRight(8));
        
        return result.ToString();
    }
    
    public void SetWtrsdKeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWtrsdDate(extracted);
        }
        offset += 8;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtrsdDate()
    {
        return _WtrsdDate;
    }
    
    // Standard Setter
    public void SetWtrsdDate(string value)
    {
        _WtrsdDate = value;
    }
    
    // Get<>AsString()
    public string GetWtrsdDateAsString()
    {
        return _WtrsdDate.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWtrsdDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtrsdDate = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}

}}