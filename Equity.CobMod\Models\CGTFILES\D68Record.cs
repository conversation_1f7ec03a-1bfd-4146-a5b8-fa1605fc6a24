using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D68Record Data Structure

public class D68Record
{
    private static int _size = 53;
    // [DEBUG] Class: D68Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D68FundCode, is_external=, is_static_class=False, static_prefix=
    private string _D68FundCode ="";
    
    
    
    
    // [DEBUG] Field: D68FundName, is_external=, is_static_class=False, static_prefix=
    private string _D68FundName ="";
    
    
    
    
    // [DEBUG] Field: D68SummaryFund, is_external=, is_static_class=False, static_prefix=
    private string _D68SummaryFund ="";
    
    
    
    
    // [DEBUG] Field: D68FundType, is_external=, is_static_class=False, static_prefix=
    private string _D68FundType ="";
    
    
    
    
    // [DEBUG] Field: D68Election1965Ord, is_external=, is_static_class=False, static_prefix=
    private string _D68Election1965Ord ="";
    
    
    
    
    // [DEBUG] Field: D68Election1965Fixed, is_external=, is_static_class=False, static_prefix=
    private string _D68Election1965Fixed ="";
    
    
    
    
    // [DEBUG] Field: D68Election1982, is_external=, is_static_class=False, static_prefix=
    private string _D68Election1982 ="";
    
    
    
    
    // [DEBUG] Field: D68PeriodStartDate, is_external=, is_static_class=False, static_prefix=
    private D68PeriodStartDate _D68PeriodStartDate = new D68PeriodStartDate();
    
    
    
    
    // [DEBUG] Field: D68PeriodEndDate, is_external=, is_static_class=False, static_prefix=
    private D68PeriodEndDate _D68PeriodEndDate = new D68PeriodEndDate();
    
    
    
    
    // [DEBUG] Field: D68OlabFund, is_external=, is_static_class=False, static_prefix=
    private string _D68OlabFund ="";
    
    
    
    
    // [DEBUG] Field: D68LifeSummaryFund, is_external=, is_static_class=False, static_prefix=
    private string _D68LifeSummaryFund ="";
    
    
    
    
    // [DEBUG] Field: D68UseLosses, is_external=, is_static_class=False, static_prefix=
    private string _D68UseLosses ="";
    
    
    
    
    // [DEBUG] Field: D68PriceTypeCode, is_external=, is_static_class=False, static_prefix=
    private string _D68PriceTypeCode ="";
    
    
    
    
    // [DEBUG] Field: D68UseEarlierPrice, is_external=, is_static_class=False, static_prefix=
    private string _D68UseEarlierPrice ="";
    
    
    
    
    
    // Serialization methods
    public string GetD68RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D68FundCode.PadRight(4));
        result.Append(_D68FundName.PadRight(30));
        result.Append(_D68SummaryFund.PadRight(4));
        result.Append(_D68FundType.PadRight(1));
        result.Append(_D68Election1965Ord.PadRight(1));
        result.Append(_D68Election1965Fixed.PadRight(1));
        result.Append(_D68Election1982.PadRight(1));
        result.Append(_D68PeriodStartDate.GetD68PeriodStartDateAsString());
        result.Append(_D68PeriodEndDate.GetD68PeriodEndDateAsString());
        result.Append(_D68OlabFund.PadRight(1));
        result.Append(_D68LifeSummaryFund.PadRight(1));
        result.Append(_D68UseLosses.PadRight(1));
        result.Append(_D68PriceTypeCode.PadRight(0));
        result.Append(_D68UseEarlierPrice.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD68RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD68FundCode(extracted);
        }
        offset += 4;
        if (offset + 30 <= data.Length)
        {
            string extracted = data.Substring(offset, 30).Trim();
            SetD68FundName(extracted);
        }
        offset += 30;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD68SummaryFund(extracted);
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD68FundType(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD68Election1965Ord(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD68Election1965Fixed(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD68Election1982(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            _D68PeriodStartDate.SetD68PeriodStartDateAsString(data.Substring(offset, 4));
        }
        else
        {
            _D68PeriodStartDate.SetD68PeriodStartDateAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            _D68PeriodEndDate.SetD68PeriodEndDateAsString(data.Substring(offset, 4));
        }
        else
        {
            _D68PeriodEndDate.SetD68PeriodEndDateAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD68OlabFund(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD68LifeSummaryFund(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD68UseLosses(extracted);
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD68PriceTypeCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD68UseEarlierPrice(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD68RecordAsString();
    }
    // Set<>String Override function
    public void SetD68Record(string value)
    {
        SetD68RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD68FundCode()
    {
        return _D68FundCode;
    }
    
    // Standard Setter
    public void SetD68FundCode(string value)
    {
        _D68FundCode = value;
    }
    
    // Get<>AsString()
    public string GetD68FundCodeAsString()
    {
        return _D68FundCode.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD68FundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D68FundCode = value;
    }
    
    // Standard Getter
    public string GetD68FundName()
    {
        return _D68FundName;
    }
    
    // Standard Setter
    public void SetD68FundName(string value)
    {
        _D68FundName = value;
    }
    
    // Get<>AsString()
    public string GetD68FundNameAsString()
    {
        return _D68FundName.PadRight(30);
    }
    
    // Set<>AsString()
    public void SetD68FundNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D68FundName = value;
    }
    
    // Standard Getter
    public string GetD68SummaryFund()
    {
        return _D68SummaryFund;
    }
    
    // Standard Setter
    public void SetD68SummaryFund(string value)
    {
        _D68SummaryFund = value;
    }
    
    // Get<>AsString()
    public string GetD68SummaryFundAsString()
    {
        return _D68SummaryFund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD68SummaryFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D68SummaryFund = value;
    }
    
    // Standard Getter
    public string GetD68FundType()
    {
        return _D68FundType;
    }
    
    // Standard Setter
    public void SetD68FundType(string value)
    {
        _D68FundType = value;
    }
    
    // Get<>AsString()
    public string GetD68FundTypeAsString()
    {
        return _D68FundType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD68FundTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D68FundType = value;
    }
    
    // Standard Getter
    public string GetD68Election1965Ord()
    {
        return _D68Election1965Ord;
    }
    
    // Standard Setter
    public void SetD68Election1965Ord(string value)
    {
        _D68Election1965Ord = value;
    }
    
    // Get<>AsString()
    public string GetD68Election1965OrdAsString()
    {
        return _D68Election1965Ord.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD68Election1965OrdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D68Election1965Ord = value;
    }
    
    // Standard Getter
    public string GetD68Election1965Fixed()
    {
        return _D68Election1965Fixed;
    }
    
    // Standard Setter
    public void SetD68Election1965Fixed(string value)
    {
        _D68Election1965Fixed = value;
    }
    
    // Get<>AsString()
    public string GetD68Election1965FixedAsString()
    {
        return _D68Election1965Fixed.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD68Election1965FixedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D68Election1965Fixed = value;
    }
    
    // Standard Getter
    public string GetD68Election1982()
    {
        return _D68Election1982;
    }
    
    // Standard Setter
    public void SetD68Election1982(string value)
    {
        _D68Election1982 = value;
    }
    
    // Get<>AsString()
    public string GetD68Election1982AsString()
    {
        return _D68Election1982.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD68Election1982AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D68Election1982 = value;
    }
    
    // Standard Getter
    public D68PeriodStartDate GetD68PeriodStartDate()
    {
        return _D68PeriodStartDate;
    }
    
    // Standard Setter
    public void SetD68PeriodStartDate(D68PeriodStartDate value)
    {
        _D68PeriodStartDate = value;
    }
    
    // Get<>AsString()
    public string GetD68PeriodStartDateAsString()
    {
        return _D68PeriodStartDate != null ? _D68PeriodStartDate.GetD68PeriodStartDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD68PeriodStartDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D68PeriodStartDate == null)
        {
            _D68PeriodStartDate = new D68PeriodStartDate();
        }
        _D68PeriodStartDate.SetD68PeriodStartDateAsString(value);
    }
    
    // Standard Getter
    public D68PeriodEndDate GetD68PeriodEndDate()
    {
        return _D68PeriodEndDate;
    }
    
    // Standard Setter
    public void SetD68PeriodEndDate(D68PeriodEndDate value)
    {
        _D68PeriodEndDate = value;
    }
    
    // Get<>AsString()
    public string GetD68PeriodEndDateAsString()
    {
        return _D68PeriodEndDate != null ? _D68PeriodEndDate.GetD68PeriodEndDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD68PeriodEndDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D68PeriodEndDate == null)
        {
            _D68PeriodEndDate = new D68PeriodEndDate();
        }
        _D68PeriodEndDate.SetD68PeriodEndDateAsString(value);
    }
    
    // Standard Getter
    public string GetD68OlabFund()
    {
        return _D68OlabFund;
    }
    
    // Standard Setter
    public void SetD68OlabFund(string value)
    {
        _D68OlabFund = value;
    }
    
    // Get<>AsString()
    public string GetD68OlabFundAsString()
    {
        return _D68OlabFund.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD68OlabFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D68OlabFund = value;
    }
    
    // Standard Getter
    public string GetD68LifeSummaryFund()
    {
        return _D68LifeSummaryFund;
    }
    
    // Standard Setter
    public void SetD68LifeSummaryFund(string value)
    {
        _D68LifeSummaryFund = value;
    }
    
    // Get<>AsString()
    public string GetD68LifeSummaryFundAsString()
    {
        return _D68LifeSummaryFund.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD68LifeSummaryFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D68LifeSummaryFund = value;
    }
    
    // Standard Getter
    public string GetD68UseLosses()
    {
        return _D68UseLosses;
    }
    
    // Standard Setter
    public void SetD68UseLosses(string value)
    {
        _D68UseLosses = value;
    }
    
    // Get<>AsString()
    public string GetD68UseLossesAsString()
    {
        return _D68UseLosses.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD68UseLossesAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D68UseLosses = value;
    }
    
    // Standard Getter
    public string GetD68PriceTypeCode()
    {
        return _D68PriceTypeCode;
    }
    
    // Standard Setter
    public void SetD68PriceTypeCode(string value)
    {
        _D68PriceTypeCode = value;
    }
    
    // Get<>AsString()
    public string GetD68PriceTypeCodeAsString()
    {
        return _D68PriceTypeCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD68PriceTypeCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D68PriceTypeCode = value;
    }
    
    // Standard Getter
    public string GetD68UseEarlierPrice()
    {
        return _D68UseEarlierPrice;
    }
    
    // Standard Setter
    public void SetD68UseEarlierPrice(string value)
    {
        _D68UseEarlierPrice = value;
    }
    
    // Get<>AsString()
    public string GetD68UseEarlierPriceAsString()
    {
        return _D68UseEarlierPrice.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD68UseEarlierPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D68UseEarlierPrice = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD68PeriodStartDate(string value)
    {
        _D68PeriodStartDate.SetD68PeriodStartDateAsString(value);
    }
    // Nested Class: D68PeriodStartDate
    public class D68PeriodStartDate
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D68PeriodStartDateMm, is_external=, is_static_class=False, static_prefix=
        private string _D68PeriodStartDateMm ="";
        
        
        
        
        // [DEBUG] Field: D68PeriodStartDateDd, is_external=, is_static_class=False, static_prefix=
        private string _D68PeriodStartDateDd ="";
        
        
        
        
    public D68PeriodStartDate() {}
    
    public D68PeriodStartDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD68PeriodStartDateMm(data.Substring(offset, 2).Trim());
        offset += 2;
        SetD68PeriodStartDateDd(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetD68PeriodStartDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D68PeriodStartDateMm.PadRight(2));
        result.Append(_D68PeriodStartDateDd.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetD68PeriodStartDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD68PeriodStartDateMm(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD68PeriodStartDateDd(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD68PeriodStartDateMm()
    {
        return _D68PeriodStartDateMm;
    }
    
    // Standard Setter
    public void SetD68PeriodStartDateMm(string value)
    {
        _D68PeriodStartDateMm = value;
    }
    
    // Get<>AsString()
    public string GetD68PeriodStartDateMmAsString()
    {
        return _D68PeriodStartDateMm.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD68PeriodStartDateMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D68PeriodStartDateMm = value;
    }
    
    // Standard Getter
    public string GetD68PeriodStartDateDd()
    {
        return _D68PeriodStartDateDd;
    }
    
    // Standard Setter
    public void SetD68PeriodStartDateDd(string value)
    {
        _D68PeriodStartDateDd = value;
    }
    
    // Get<>AsString()
    public string GetD68PeriodStartDateDdAsString()
    {
        return _D68PeriodStartDateDd.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD68PeriodStartDateDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D68PeriodStartDateDd = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetD68PeriodEndDate(string value)
{
    _D68PeriodEndDate.SetD68PeriodEndDateAsString(value);
}
// Nested Class: D68PeriodEndDate
public class D68PeriodEndDate
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D68PeriodEndDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D68PeriodEndDateMm ="";
    
    
    
    
    // [DEBUG] Field: D68PeriodEndDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D68PeriodEndDateDd ="";
    
    
    
    
public D68PeriodEndDate() {}

public D68PeriodEndDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD68PeriodEndDateMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD68PeriodEndDateDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD68PeriodEndDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D68PeriodEndDateMm.PadRight(2));
    result.Append(_D68PeriodEndDateDd.PadRight(2));
    
    return result.ToString();
}

public void SetD68PeriodEndDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD68PeriodEndDateMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD68PeriodEndDateDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD68PeriodEndDateMm()
{
    return _D68PeriodEndDateMm;
}

// Standard Setter
public void SetD68PeriodEndDateMm(string value)
{
    _D68PeriodEndDateMm = value;
}

// Get<>AsString()
public string GetD68PeriodEndDateMmAsString()
{
    return _D68PeriodEndDateMm.PadRight(2);
}

// Set<>AsString()
public void SetD68PeriodEndDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D68PeriodEndDateMm = value;
}

// Standard Getter
public string GetD68PeriodEndDateDd()
{
    return _D68PeriodEndDateDd;
}

// Standard Setter
public void SetD68PeriodEndDateDd(string value)
{
    _D68PeriodEndDateDd = value;
}

// Get<>AsString()
public string GetD68PeriodEndDateDdAsString()
{
    return _D68PeriodEndDateDd.PadRight(2);
}

// Set<>AsString()
public void SetD68PeriodEndDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D68PeriodEndDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}

}}