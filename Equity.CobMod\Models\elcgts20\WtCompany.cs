using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtCompany Data Structure

public class WtCompany
{
    private static int _size = 111;
    // [DEBUG] Class: WtCompany, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler375, is_external=, is_static_class=False, static_prefix=
    private string _Filler375 ="COMPANIES=======";
    
    
    
    
    // [DEBUG] Field: WtcMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtcMaxTableSize =2;
    
    
    
    
    // [DEBUG] Field: WtcOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtcOccurs =2;
    
    
    
    
    // [DEBUG] Field: WtcCompanyTable, is_external=, is_static_class=False, static_prefix=
    private WtcCompanyTable _WtcCompanyTable = new WtcCompanyTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtCompanyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler375.PadRight(16));
        result.Append(_WtcMaxTableSize.ToString().PadLeft(3, '0'));
        result.Append(_WtcOccurs.ToString().PadLeft(3, '0'));
        result.Append(_WtcCompanyTable.GetWtcCompanyTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtCompanyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller375(extracted);
        }
        offset += 16;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtcMaxTableSize(parsedInt);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtcOccurs(parsedInt);
        }
        offset += 3;
        if (offset + 89 <= data.Length)
        {
            _WtcCompanyTable.SetWtcCompanyTableAsString(data.Substring(offset, 89));
        }
        else
        {
            _WtcCompanyTable.SetWtcCompanyTableAsString(data.Substring(offset));
        }
        offset += 89;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtCompanyAsString();
    }
    // Set<>String Override function
    public void SetWtCompany(string value)
    {
        SetWtCompanyAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller375()
    {
        return _Filler375;
    }
    
    // Standard Setter
    public void SetFiller375(string value)
    {
        _Filler375 = value;
    }
    
    // Get<>AsString()
    public string GetFiller375AsString()
    {
        return _Filler375.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller375AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler375 = value;
    }
    
    // Standard Getter
    public int GetWtcMaxTableSize()
    {
        return _WtcMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtcMaxTableSize(int value)
    {
        _WtcMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtcMaxTableSizeAsString()
    {
        return _WtcMaxTableSize.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetWtcMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtcMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtcOccurs()
    {
        return _WtcOccurs;
    }
    
    // Standard Setter
    public void SetWtcOccurs(int value)
    {
        _WtcOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtcOccursAsString()
    {
        return _WtcOccurs.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetWtcOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtcOccurs = parsed;
    }
    
    // Standard Getter
    public WtcCompanyTable GetWtcCompanyTable()
    {
        return _WtcCompanyTable;
    }
    
    // Standard Setter
    public void SetWtcCompanyTable(WtcCompanyTable value)
    {
        _WtcCompanyTable = value;
    }
    
    // Get<>AsString()
    public string GetWtcCompanyTableAsString()
    {
        return _WtcCompanyTable != null ? _WtcCompanyTable.GetWtcCompanyTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtcCompanyTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtcCompanyTable == null)
        {
            _WtcCompanyTable = new WtcCompanyTable();
        }
        _WtcCompanyTable.SetWtcCompanyTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtcCompanyTable(string value)
    {
        _WtcCompanyTable.SetWtcCompanyTableAsString(value);
    }
    // Nested Class: WtcCompanyTable
    public class WtcCompanyTable
    {
        private static int _size = 89;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtcElement, is_external=, is_static_class=False, static_prefix=
        private WtcCompanyTable.WtcElement[] _WtcElement = new WtcCompanyTable.WtcElement[2];
        
        public void InitializeWtcElementArray()
        {
            for (int i = 0; i < 2; i++)
            {
                _WtcElement[i] = new WtcCompanyTable.WtcElement();
            }
        }
        
        
        
    public WtcCompanyTable() {}
    
    public WtcCompanyTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWtcElementArray();
        for (int i = 0; i < 2; i++)
        {
            _WtcElement[i].SetWtcElementAsString(data.Substring(offset, 89));
            offset += 89;
        }
        
    }
    
    // Serialization methods
    public string GetWtcCompanyTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 2; i++)
        {
            result.Append(_WtcElement[i].GetWtcElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtcCompanyTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 2; i++)
        {
            if (offset + 89 > data.Length) break;
            string val = data.Substring(offset, 89);
            
            _WtcElement[i].SetWtcElementAsString(val);
            offset += 89;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtcElement
    public WtcElement GetWtcElementAt(int index)
    {
        return _WtcElement[index];
    }
    
    public void SetWtcElementAt(int index, WtcElement value)
    {
        _WtcElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtcElement GetWtcElement()
    {
        return _WtcElement != null && _WtcElement.Length > 0
        ? _WtcElement[0]
        : new WtcElement();
    }
    
    public void SetWtcElement(WtcElement value)
    {
        if (_WtcElement == null || _WtcElement.Length == 0)
        _WtcElement = new WtcElement[1];
        _WtcElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtcElement
    public class WtcElement
    {
        private static int _size = 89;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtcCal, is_external=, is_static_class=False, static_prefix=
        private string _WtcCal ="";
        
        
        
        
        // [DEBUG] Field: WtcStartDate, is_external=, is_static_class=False, static_prefix=
        private string _WtcStartDate ="";
        
        
        
        
        // [DEBUG] Field: WtcEndDate, is_external=, is_static_class=False, static_prefix=
        private WtcElement.WtcEndDate _WtcEndDate = new WtcElement.WtcEndDate();
        
        
        
        
        // [DEBUG] Field: WtcEndPlusMonthDate, is_external=, is_static_class=False, static_prefix=
        private WtcElement.WtcEndPlusMonthDate _WtcEndPlusMonthDate = new WtcElement.WtcEndPlusMonthDate();
        
        
        
        
        // [DEBUG] Field: WtcEndPlus6MthDate, is_external=, is_static_class=False, static_prefix=
        private WtcElement.WtcEndPlus6MthDate _WtcEndPlus6MthDate = new WtcElement.WtcEndPlus6MthDate();
        
        
        
        
        // [DEBUG] Field: WtcParallelPooling, is_external=, is_static_class=False, static_prefix=
        private string _WtcParallelPooling ="";
        
        
        
        
        // [DEBUG] Field: WtcSummaryFundFlag, is_external=, is_static_class=False, static_prefix=
        private string _WtcSummaryFundFlag ="";
        
        
        // 88-level condition checks for WtcSummaryFundFlag
        public bool IsWtcSummaryFund()
        {
            if (this._WtcSummaryFundFlag == "'S'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WtcPre65FixedInterest, is_external=, is_static_class=False, static_prefix=
        private string _WtcPre65FixedInterest ="";
        
        
        
        
        // [DEBUG] Field: WtcPre65Ordinary, is_external=, is_static_class=False, static_prefix=
        private string _WtcPre65Ordinary ="";
        
        
        
        
        // [DEBUG] Field: WtcCalculationRequest, is_external=, is_static_class=False, static_prefix=
        private string _WtcCalculationRequest ="";
        
        
        // 88-level condition checks for WtcCalculationRequest
        public bool IsWtcNoSmallDstbns()
        {
            if (this._WtcCalculationRequest == "'2'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WtcFundName, is_external=, is_static_class=False, static_prefix=
        private string _WtcFundName ="";
        
        
        
        
        // [DEBUG] Field: WtcFundType, is_external=, is_static_class=False, static_prefix=
        private string _WtcFundType ="";
        
        
        
        
        // [DEBUG] Field: WtcDeemedDisposalState, is_external=, is_static_class=False, static_prefix=
        private int _WtcDeemedDisposalState =0;
        
        
        
        
        // [DEBUG] Field: WtcCalendarCode, is_external=, is_static_class=False, static_prefix=
        private string _WtcCalendarCode ="";
        
        
        
        
        // [DEBUG] Field: WtcPriceTypeId, is_external=, is_static_class=False, static_prefix=
        private string _WtcPriceTypeId ="";
        
        
        
        
        // [DEBUG] Field: WtcUseEarlierPrice, is_external=, is_static_class=False, static_prefix=
        private string _WtcUseEarlierPrice ="";
        
        
        
        
        // [DEBUG] Field: WtcOlabFund, is_external=, is_static_class=False, static_prefix=
        private string _WtcOlabFund ="";
        
        
        
        
        // [DEBUG] Field: WtcLifeSummaryFund, is_external=, is_static_class=False, static_prefix=
        private string _WtcLifeSummaryFund ="";
        
        
        
        
    public WtcElement() {}
    
    public WtcElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtcCal(data.Substring(offset, 4).Trim());
        offset += 4;
        SetWtcStartDate(data.Substring(offset, 6).Trim());
        offset += 6;
        _WtcEndDate.SetWtcEndDateAsString(data.Substring(offset, WtcEndDate.GetSize()));
        offset += 6;
        _WtcEndPlusMonthDate.SetWtcEndPlusMonthDateAsString(data.Substring(offset, WtcEndPlusMonthDate.GetSize()));
        offset += 6;
        _WtcEndPlus6MthDate.SetWtcEndPlus6MthDateAsString(data.Substring(offset, WtcEndPlus6MthDate.GetSize()));
        offset += 6;
        SetWtcParallelPooling(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWtcSummaryFundFlag(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWtcPre65FixedInterest(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWtcPre65Ordinary(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWtcCalculationRequest(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWtcFundName(data.Substring(offset, 50).Trim());
        offset += 50;
        SetWtcFundType(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWtcDeemedDisposalState(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetWtcCalendarCode(data.Substring(offset, 4).Trim());
        offset += 4;
        SetWtcPriceTypeId(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWtcUseEarlierPrice(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWtcOlabFund(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWtcLifeSummaryFund(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetWtcElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtcCal.PadRight(4));
        result.Append(_WtcStartDate.PadRight(6));
        result.Append(_WtcEndDate.GetWtcEndDateAsString());
        result.Append(_WtcEndPlusMonthDate.GetWtcEndPlusMonthDateAsString());
        result.Append(_WtcEndPlus6MthDate.GetWtcEndPlus6MthDateAsString());
        result.Append(_WtcParallelPooling.PadRight(1));
        result.Append(_WtcSummaryFundFlag.PadRight(1));
        result.Append(_WtcPre65FixedInterest.PadRight(1));
        result.Append(_WtcPre65Ordinary.PadRight(1));
        result.Append(_WtcCalculationRequest.PadRight(1));
        result.Append(_WtcFundName.PadRight(50));
        result.Append(_WtcFundType.PadRight(1));
        result.Append(_WtcDeemedDisposalState.ToString().PadLeft(1, '0'));
        result.Append(_WtcCalendarCode.PadRight(4));
        result.Append(_WtcPriceTypeId.PadRight(0));
        result.Append(_WtcUseEarlierPrice.PadRight(0));
        result.Append(_WtcOlabFund.PadRight(0));
        result.Append(_WtcLifeSummaryFund.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetWtcElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtcCal(extracted);
        }
        offset += 4;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetWtcStartDate(extracted);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            _WtcEndDate.SetWtcEndDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _WtcEndDate.SetWtcEndDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            _WtcEndPlusMonthDate.SetWtcEndPlusMonthDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _WtcEndPlusMonthDate.SetWtcEndPlusMonthDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            _WtcEndPlus6MthDate.SetWtcEndPlus6MthDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _WtcEndPlus6MthDate.SetWtcEndPlus6MthDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtcParallelPooling(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtcSummaryFundFlag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtcPre65FixedInterest(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtcPre65Ordinary(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtcCalculationRequest(extracted);
        }
        offset += 1;
        if (offset + 50 <= data.Length)
        {
            string extracted = data.Substring(offset, 50).Trim();
            SetWtcFundName(extracted);
        }
        offset += 50;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtcFundType(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtcDeemedDisposalState(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtcCalendarCode(extracted);
        }
        offset += 4;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWtcPriceTypeId(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWtcUseEarlierPrice(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWtcOlabFund(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWtcLifeSummaryFund(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtcCal()
    {
        return _WtcCal;
    }
    
    // Standard Setter
    public void SetWtcCal(string value)
    {
        _WtcCal = value;
    }
    
    // Get<>AsString()
    public string GetWtcCalAsString()
    {
        return _WtcCal.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtcCalAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtcCal = value;
    }
    
    // Standard Getter
    public string GetWtcStartDate()
    {
        return _WtcStartDate;
    }
    
    // Standard Setter
    public void SetWtcStartDate(string value)
    {
        _WtcStartDate = value;
    }
    
    // Get<>AsString()
    public string GetWtcStartDateAsString()
    {
        return _WtcStartDate.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetWtcStartDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtcStartDate = value;
    }
    
    // Standard Getter
    public WtcEndDate GetWtcEndDate()
    {
        return _WtcEndDate;
    }
    
    // Standard Setter
    public void SetWtcEndDate(WtcEndDate value)
    {
        _WtcEndDate = value;
    }
    
    // Get<>AsString()
    public string GetWtcEndDateAsString()
    {
        return _WtcEndDate != null ? _WtcEndDate.GetWtcEndDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtcEndDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtcEndDate == null)
        {
            _WtcEndDate = new WtcEndDate();
        }
        _WtcEndDate.SetWtcEndDateAsString(value);
    }
    
    // Standard Getter
    public WtcEndPlusMonthDate GetWtcEndPlusMonthDate()
    {
        return _WtcEndPlusMonthDate;
    }
    
    // Standard Setter
    public void SetWtcEndPlusMonthDate(WtcEndPlusMonthDate value)
    {
        _WtcEndPlusMonthDate = value;
    }
    
    // Get<>AsString()
    public string GetWtcEndPlusMonthDateAsString()
    {
        return _WtcEndPlusMonthDate != null ? _WtcEndPlusMonthDate.GetWtcEndPlusMonthDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtcEndPlusMonthDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtcEndPlusMonthDate == null)
        {
            _WtcEndPlusMonthDate = new WtcEndPlusMonthDate();
        }
        _WtcEndPlusMonthDate.SetWtcEndPlusMonthDateAsString(value);
    }
    
    // Standard Getter
    public WtcEndPlus6MthDate GetWtcEndPlus6MthDate()
    {
        return _WtcEndPlus6MthDate;
    }
    
    // Standard Setter
    public void SetWtcEndPlus6MthDate(WtcEndPlus6MthDate value)
    {
        _WtcEndPlus6MthDate = value;
    }
    
    // Get<>AsString()
    public string GetWtcEndPlus6MthDateAsString()
    {
        return _WtcEndPlus6MthDate != null ? _WtcEndPlus6MthDate.GetWtcEndPlus6MthDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtcEndPlus6MthDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtcEndPlus6MthDate == null)
        {
            _WtcEndPlus6MthDate = new WtcEndPlus6MthDate();
        }
        _WtcEndPlus6MthDate.SetWtcEndPlus6MthDateAsString(value);
    }
    
    // Standard Getter
    public string GetWtcParallelPooling()
    {
        return _WtcParallelPooling;
    }
    
    // Standard Setter
    public void SetWtcParallelPooling(string value)
    {
        _WtcParallelPooling = value;
    }
    
    // Get<>AsString()
    public string GetWtcParallelPoolingAsString()
    {
        return _WtcParallelPooling.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtcParallelPoolingAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtcParallelPooling = value;
    }
    
    // Standard Getter
    public string GetWtcSummaryFundFlag()
    {
        return _WtcSummaryFundFlag;
    }
    
    // Standard Setter
    public void SetWtcSummaryFundFlag(string value)
    {
        _WtcSummaryFundFlag = value;
    }
    
    // Get<>AsString()
    public string GetWtcSummaryFundFlagAsString()
    {
        return _WtcSummaryFundFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtcSummaryFundFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtcSummaryFundFlag = value;
    }
    
    // Standard Getter
    public string GetWtcPre65FixedInterest()
    {
        return _WtcPre65FixedInterest;
    }
    
    // Standard Setter
    public void SetWtcPre65FixedInterest(string value)
    {
        _WtcPre65FixedInterest = value;
    }
    
    // Get<>AsString()
    public string GetWtcPre65FixedInterestAsString()
    {
        return _WtcPre65FixedInterest.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtcPre65FixedInterestAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtcPre65FixedInterest = value;
    }
    
    // Standard Getter
    public string GetWtcPre65Ordinary()
    {
        return _WtcPre65Ordinary;
    }
    
    // Standard Setter
    public void SetWtcPre65Ordinary(string value)
    {
        _WtcPre65Ordinary = value;
    }
    
    // Get<>AsString()
    public string GetWtcPre65OrdinaryAsString()
    {
        return _WtcPre65Ordinary.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtcPre65OrdinaryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtcPre65Ordinary = value;
    }
    
    // Standard Getter
    public string GetWtcCalculationRequest()
    {
        return _WtcCalculationRequest;
    }
    
    // Standard Setter
    public void SetWtcCalculationRequest(string value)
    {
        _WtcCalculationRequest = value;
    }
    
    // Get<>AsString()
    public string GetWtcCalculationRequestAsString()
    {
        return _WtcCalculationRequest.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtcCalculationRequestAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtcCalculationRequest = value;
    }
    
    // Standard Getter
    public string GetWtcFundName()
    {
        return _WtcFundName;
    }
    
    // Standard Setter
    public void SetWtcFundName(string value)
    {
        _WtcFundName = value;
    }
    
    // Get<>AsString()
    public string GetWtcFundNameAsString()
    {
        return _WtcFundName.PadRight(50);
    }
    
    // Set<>AsString()
    public void SetWtcFundNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtcFundName = value;
    }
    
    // Standard Getter
    public string GetWtcFundType()
    {
        return _WtcFundType;
    }
    
    // Standard Setter
    public void SetWtcFundType(string value)
    {
        _WtcFundType = value;
    }
    
    // Get<>AsString()
    public string GetWtcFundTypeAsString()
    {
        return _WtcFundType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtcFundTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtcFundType = value;
    }
    
    // Standard Getter
    public int GetWtcDeemedDisposalState()
    {
        return _WtcDeemedDisposalState;
    }
    
    // Standard Setter
    public void SetWtcDeemedDisposalState(int value)
    {
        _WtcDeemedDisposalState = value;
    }
    
    // Get<>AsString()
    public string GetWtcDeemedDisposalStateAsString()
    {
        return _WtcDeemedDisposalState.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetWtcDeemedDisposalStateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtcDeemedDisposalState = parsed;
    }
    
    // Standard Getter
    public string GetWtcCalendarCode()
    {
        return _WtcCalendarCode;
    }
    
    // Standard Setter
    public void SetWtcCalendarCode(string value)
    {
        _WtcCalendarCode = value;
    }
    
    // Get<>AsString()
    public string GetWtcCalendarCodeAsString()
    {
        return _WtcCalendarCode.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtcCalendarCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtcCalendarCode = value;
    }
    
    // Standard Getter
    public string GetWtcPriceTypeId()
    {
        return _WtcPriceTypeId;
    }
    
    // Standard Setter
    public void SetWtcPriceTypeId(string value)
    {
        _WtcPriceTypeId = value;
    }
    
    // Get<>AsString()
    public string GetWtcPriceTypeIdAsString()
    {
        return _WtcPriceTypeId.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWtcPriceTypeIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtcPriceTypeId = value;
    }
    
    // Standard Getter
    public string GetWtcUseEarlierPrice()
    {
        return _WtcUseEarlierPrice;
    }
    
    // Standard Setter
    public void SetWtcUseEarlierPrice(string value)
    {
        _WtcUseEarlierPrice = value;
    }
    
    // Get<>AsString()
    public string GetWtcUseEarlierPriceAsString()
    {
        return _WtcUseEarlierPrice.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWtcUseEarlierPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtcUseEarlierPrice = value;
    }
    
    // Standard Getter
    public string GetWtcOlabFund()
    {
        return _WtcOlabFund;
    }
    
    // Standard Setter
    public void SetWtcOlabFund(string value)
    {
        _WtcOlabFund = value;
    }
    
    // Get<>AsString()
    public string GetWtcOlabFundAsString()
    {
        return _WtcOlabFund.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWtcOlabFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtcOlabFund = value;
    }
    
    // Standard Getter
    public string GetWtcLifeSummaryFund()
    {
        return _WtcLifeSummaryFund;
    }
    
    // Standard Setter
    public void SetWtcLifeSummaryFund(string value)
    {
        _WtcLifeSummaryFund = value;
    }
    
    // Get<>AsString()
    public string GetWtcLifeSummaryFundAsString()
    {
        return _WtcLifeSummaryFund.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWtcLifeSummaryFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtcLifeSummaryFund = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtcEndDate
    public class WtcEndDate
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtcEndYymm, is_external=, is_static_class=False, static_prefix=
        private string _WtcEndYymm ="";
        
        
        
        
        // [DEBUG] Field: Filler376, is_external=, is_static_class=False, static_prefix=
        private string _Filler376 ="";
        
        
        
        
    public WtcEndDate() {}
    
    public WtcEndDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtcEndYymm(data.Substring(offset, 4).Trim());
        offset += 4;
        SetFiller376(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetWtcEndDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtcEndYymm.PadRight(4));
        result.Append(_Filler376.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetWtcEndDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtcEndYymm(extracted);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller376(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtcEndYymm()
    {
        return _WtcEndYymm;
    }
    
    // Standard Setter
    public void SetWtcEndYymm(string value)
    {
        _WtcEndYymm = value;
    }
    
    // Get<>AsString()
    public string GetWtcEndYymmAsString()
    {
        return _WtcEndYymm.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtcEndYymmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtcEndYymm = value;
    }
    
    // Standard Getter
    public string GetFiller376()
    {
        return _Filler376;
    }
    
    // Standard Setter
    public void SetFiller376(string value)
    {
        _Filler376 = value;
    }
    
    // Get<>AsString()
    public string GetFiller376AsString()
    {
        return _Filler376.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller376AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler376 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: WtcEndPlusMonthDate
public class WtcEndPlusMonthDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtcEndPlusMonthYy, is_external=, is_static_class=False, static_prefix=
    private int _WtcEndPlusMonthYy =0;
    
    
    
    
    // [DEBUG] Field: WtcEndPlusMonthMm, is_external=, is_static_class=False, static_prefix=
    private int _WtcEndPlusMonthMm =0;
    
    
    
    
    // [DEBUG] Field: WtcEndPlusMonthDd, is_external=, is_static_class=False, static_prefix=
    private int _WtcEndPlusMonthDd =0;
    
    
    
    
public WtcEndPlusMonthDate() {}

public WtcEndPlusMonthDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtcEndPlusMonthYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWtcEndPlusMonthMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWtcEndPlusMonthDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWtcEndPlusMonthDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtcEndPlusMonthYy.ToString().PadLeft(2, '0'));
    result.Append(_WtcEndPlusMonthMm.ToString().PadLeft(2, '0'));
    result.Append(_WtcEndPlusMonthDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWtcEndPlusMonthDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtcEndPlusMonthYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtcEndPlusMonthMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtcEndPlusMonthDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWtcEndPlusMonthYy()
{
    return _WtcEndPlusMonthYy;
}

// Standard Setter
public void SetWtcEndPlusMonthYy(int value)
{
    _WtcEndPlusMonthYy = value;
}

// Get<>AsString()
public string GetWtcEndPlusMonthYyAsString()
{
    return _WtcEndPlusMonthYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWtcEndPlusMonthYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtcEndPlusMonthYy = parsed;
}

// Standard Getter
public int GetWtcEndPlusMonthMm()
{
    return _WtcEndPlusMonthMm;
}

// Standard Setter
public void SetWtcEndPlusMonthMm(int value)
{
    _WtcEndPlusMonthMm = value;
}

// Get<>AsString()
public string GetWtcEndPlusMonthMmAsString()
{
    return _WtcEndPlusMonthMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWtcEndPlusMonthMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtcEndPlusMonthMm = parsed;
}

// Standard Getter
public int GetWtcEndPlusMonthDd()
{
    return _WtcEndPlusMonthDd;
}

// Standard Setter
public void SetWtcEndPlusMonthDd(int value)
{
    _WtcEndPlusMonthDd = value;
}

// Get<>AsString()
public string GetWtcEndPlusMonthDdAsString()
{
    return _WtcEndPlusMonthDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWtcEndPlusMonthDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtcEndPlusMonthDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WtcEndPlus6MthDate
public class WtcEndPlus6MthDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtcEndPlus6MthYy, is_external=, is_static_class=False, static_prefix=
    private int _WtcEndPlus6MthYy =0;
    
    
    
    
    // [DEBUG] Field: WtcEndPlus6MthMm, is_external=, is_static_class=False, static_prefix=
    private int _WtcEndPlus6MthMm =0;
    
    
    
    
    // [DEBUG] Field: WtcEndPlus6MthDd, is_external=, is_static_class=False, static_prefix=
    private int _WtcEndPlus6MthDd =0;
    
    
    
    
public WtcEndPlus6MthDate() {}

public WtcEndPlus6MthDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtcEndPlus6MthYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWtcEndPlus6MthMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWtcEndPlus6MthDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWtcEndPlus6MthDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtcEndPlus6MthYy.ToString().PadLeft(2, '0'));
    result.Append(_WtcEndPlus6MthMm.ToString().PadLeft(2, '0'));
    result.Append(_WtcEndPlus6MthDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWtcEndPlus6MthDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtcEndPlus6MthYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtcEndPlus6MthMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtcEndPlus6MthDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWtcEndPlus6MthYy()
{
    return _WtcEndPlus6MthYy;
}

// Standard Setter
public void SetWtcEndPlus6MthYy(int value)
{
    _WtcEndPlus6MthYy = value;
}

// Get<>AsString()
public string GetWtcEndPlus6MthYyAsString()
{
    return _WtcEndPlus6MthYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWtcEndPlus6MthYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtcEndPlus6MthYy = parsed;
}

// Standard Getter
public int GetWtcEndPlus6MthMm()
{
    return _WtcEndPlus6MthMm;
}

// Standard Setter
public void SetWtcEndPlus6MthMm(int value)
{
    _WtcEndPlus6MthMm = value;
}

// Get<>AsString()
public string GetWtcEndPlus6MthMmAsString()
{
    return _WtcEndPlus6MthMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWtcEndPlus6MthMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtcEndPlus6MthMm = parsed;
}

// Standard Getter
public int GetWtcEndPlus6MthDd()
{
    return _WtcEndPlus6MthDd;
}

// Standard Setter
public void SetWtcEndPlus6MthDd(int value)
{
    _WtcEndPlus6MthDd = value;
}

// Get<>AsString()
public string GetWtcEndPlus6MthDdAsString()
{
    return _WtcEndPlus6MthDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWtcEndPlus6MthDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtcEndPlus6MthDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
}

}}
