﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Legacy4.Equity.CobMod.Models;
using Legacy4.Equity.CobMod.Utils;
using WK.UK.CCH.Equity.CobolDataTransformation;

namespace Legacy4.Equity.CobMod.FIleHandlers
{
    public class UserFundsDAL
    {
        private UserInfoDA oUserInfoDA;
        private UserFundsDA oUserFundsDA;
        private string sRecordArea;
        private int iUserNo;
        private bool bFirstTime;

        private D37Record D37Record = new D37Record();
        private D37RecordFormat2 D37RecordFormat2 = new D37RecordFormat2();
        private UserInfoDARecord UserInfoDARecord = new UserInfoDARecord();
        private UserFundsDARecord UserFundsDARecord = new UserFundsDARecord();

        // Main entry point - equivalent to COBOL procedure division
        public string Execute(string fileAction, ref string fileRecordArea, int userNumber)
        {
            iUserNo = userNumber;

            switch (fileAction)
            {
                case Constants.OPEN_INPUT:
                case Constants.OPEN_I_O:
                    oUserInfoDA = new UserInfoDA();
                    oUserFundsDA = new UserFundsDA(iUserNo);
                    bFirstTime = true;
                    break;

                case Constants.READ_NEXT:
                    return ReadNext(ref fileRecordArea);

                case Constants.READ_RECORD:
                    return ReadRecord(ref fileRecordArea);

                case Constants.CLOSE_FILE:
                    // do nothing
                    break;
            }
            return string.Empty;
        }

        private string ReadNext(ref string fileRecordArea)
        {
            if (bFirstTime)
            {
                // return header record derived from UserInfoDA
                GetUserInfo();
                fileRecordArea = D37Record.ToString();
                bFirstTime = false;
           
            }
            else
            {
                // return user fund record derived from UserFundsDA
                sRecordArea = oUserFundsDA.ReadNext();
                SetupCobolRecord();
                fileRecordArea = D37RecordFormat2.ToString();
            }
            return fileRecordArea;
        }

        private string ReadRecord(ref string fileRecordArea)
        {
            string wFundCode = fileRecordArea.Substring(0, 4);

            if (string.IsNullOrEmpty(wFundCode) || wFundCode == "\0\0\0\0" || wFundCode == "\xFF\xFF\xFF\xFF" || wFundCode == "    ")
            {
                if (string.IsNullOrEmpty(wFundCode) || wFundCode == "\0\0\0\0" || wFundCode == "\xFF\xFF\xFF\xFF")
                {
                    // to prevent crash
                    fileRecordArea = new string(' ', fileRecordArea.Length);
                }
                else
                {
                    // return header record derived from UserInfoDA
                    GetUserInfo();
                    fileRecordArea = D37Record.ToString();
                }
            }
            else
            {
                // return user fund record derived from UserFundsDA
                sRecordArea = wFundCode;
                sRecordArea = oUserFundsDA.ReadByFundCode(sRecordArea);
                SetupCobolRecord();
                fileRecordArea = D37RecordFormat2.ToString();
            }
            return fileRecordArea;
        }

        private void GetUserInfo()
        {
            sRecordArea = oUserInfoDA.GetUserInfo(iUserNo);
            UserInfoDARecord = UserInfoDARecord.FromString(sRecordArea);
            D37Record = new D37Record();
            MoveCorr.PerformMoveCorr(UserInfoDARecord, D37Record);
        }

        private void SetupCobolRecord()
        {
            UserFundsDARecord = UserFundsDARecord.FromString(sRecordArea);
            D37RecordFormat2 = new D37RecordFormat2();

            if (!string.IsNullOrEmpty(sRecordArea))
            {
                D37RecordFormat2 = new D37RecordFormat2();
                MoveCorr.PerformMoveCorr(UserFundsDARecord, D37RecordFormat2);

                if (UserFundsDARecord.D37SmallDistribution == "0")
                {
                    D37RecordFormat2.D37CalcRequestFlag = "2";
                }
                else
                {
                    D37RecordFormat2.D37CalcRequestFlag = "1";
                }
            }
        }
    }
}
