using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WeErrorMessages Data Structure

public class WeErrorMessages
{
    private static int _size = 1040;
    // [DEBUG] Class: WeErrorMessages, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WeMsgs, is_external=, is_static_class=False, static_prefix=
    private WeMsgs _WeMsgs = new WeMsgs();
    
    
    
    
    // [DEBUG] Field: WeErrmsg, is_external=, is_static_class=False, static_prefix=
    private string[] _WeErrmsg = new string[13];
    
    
    
    
    
    // Serialization methods
    public string GetWeErrorMessagesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WeMsgs.GetWeMsgsAsString());
        for (int i = 0; i < 13; i++)
        {
            result.Append(_WeErrmsg[i].PadRight(40));
        }
        
        return result.ToString();
    }
    
    public void SetWeErrorMessagesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 520 <= data.Length)
        {
            _WeMsgs.SetWeMsgsAsString(data.Substring(offset, 520));
        }
        else
        {
            _WeMsgs.SetWeMsgsAsString(data.Substring(offset));
        }
        offset += 520;
        for (int i = 0; i < 13; i++)
        {
            if (offset + 40 > data.Length) break;
            string val = data.Substring(offset, 40);
            
            _WeErrmsg[i] = val.Trim();
            offset += 40;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWeErrorMessagesAsString();
    }
    // Set<>String Override function
    public void SetWeErrorMessages(string value)
    {
        SetWeErrorMessagesAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public WeMsgs GetWeMsgs()
    {
        return _WeMsgs;
    }
    
    // Standard Setter
    public void SetWeMsgs(WeMsgs value)
    {
        _WeMsgs = value;
    }
    
    // Get<>AsString()
    public string GetWeMsgsAsString()
    {
        return _WeMsgs != null ? _WeMsgs.GetWeMsgsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWeMsgsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WeMsgs == null)
        {
            _WeMsgs = new WeMsgs();
        }
        _WeMsgs.SetWeMsgsAsString(value);
    }
    
    // Array Accessors for WeErrmsg
    public string GetWeErrmsgAt(int index)
    {
        return _WeErrmsg[index];
    }
    
    public void SetWeErrmsgAt(int index, string value)
    {
        _WeErrmsg[index] = value;
    }
    
    public string GetWeErrmsgAsStringAt(int index)
    {
        return _WeErrmsg[index].PadRight(40);
    }
    
    public void SetWeErrmsgAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _WeErrmsg[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetWeErrmsg()
    {
        return _WeErrmsg != null && _WeErrmsg.Length > 0
        ? _WeErrmsg[0]
        : default(string);
    }
    
    public void SetWeErrmsg(string value)
    {
        if (_WeErrmsg == null || _WeErrmsg.Length == 0)
        _WeErrmsg = new string[1];
        _WeErrmsg[0] = value;
    }
    
    public string GetWeErrmsgAsString()
    {
        return _WeErrmsg != null && _WeErrmsg.Length > 0
        ? _WeErrmsg[0].ToString()
        : string.Empty;
    }
    
    public void SetWeErrmsgAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_WeErrmsg == null || _WeErrmsg.Length == 0)
        _WeErrmsg = new string[1];
        
        _WeErrmsg[0] = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWeMsgs(string value)
    {
        _WeMsgs.SetWeMsgsAsString(value);
    }
    // Nested Class: WeMsgs
    public class WeMsgs
    {
        private static int _size = 520;
        
        // Fields in the class
        
        
        // [DEBUG] Field: We1, is_external=, is_static_class=False, static_prefix=
        private string _We1 ="TOO MANY RPI RECORDS";
        
        
        
        
        // [DEBUG] Field: We2, is_external=, is_static_class=False, static_prefix=
        private string _We2 ="TOO MANY RECURSIONS";
        
        
        
        
        // [DEBUG] Field: We3, is_external=, is_static_class=False, static_prefix=
        private string _We3 ="CAL/SEDOL TERMINATED";
        
        
        
        
        // [DEBUG] Field: We4, is_external=, is_static_class=False, static_prefix=
        private string _We4 ="TOO MANY DIFFERENT SEDOLS ASSOCIATED";
        
        
        
        
        // [DEBUG] Field: We5, is_external=, is_static_class=False, static_prefix=
        private string _We5 ="TOO MANY DETAILS FOR SEDOL";
        
        
        
        
        // [DEBUG] Field: We6, is_external=, is_static_class=False, static_prefix=
        private string _We6 ="MASTER FILE WRITE FAILED";
        
        
        
        
        // [DEBUG] Field: We7, is_external=, is_static_class=False, static_prefix=
        private string _We7 ="MASTER FILE DELETE FAILED";
        
        
        
        
        // [DEBUG] Field: We8, is_external=, is_static_class=False, static_prefix=
        private string _We8 ="MASTER FILE OPEN FAILED";
        
        
        
        
        // [DEBUG] Field: We9, is_external=, is_static_class=False, static_prefix=
        private string _We9 ="MASTER FILE CLOSE FAILED";
        
        
        
        
        // [DEBUG] Field: We10, is_external=, is_static_class=False, static_prefix=
        private string _We10 ="TOO MANY COSTS";
        
        
        
        
        // [DEBUG] Field: We11, is_external=, is_static_class=False, static_prefix=
        private string _We11 ="COMPANY FILE OUT OF SEQUENCE";
        
        
        
        
        // [DEBUG] Field: We12, is_external=, is_static_class=False, static_prefix=
        private string _We12 ="FUND FILE LOCK FAILED       ";
        
        
        
        
        // [DEBUG] Field: We13, is_external=, is_static_class=False, static_prefix=
        private string _We13 ="TOO MANY FUNDS - TABLE FULL             ";
        
        
        
        
    public WeMsgs() {}
    
    public WeMsgs(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWe1(data.Substring(offset, 40).Trim());
        offset += 40;
        SetWe2(data.Substring(offset, 40).Trim());
        offset += 40;
        SetWe3(data.Substring(offset, 40).Trim());
        offset += 40;
        SetWe4(data.Substring(offset, 40).Trim());
        offset += 40;
        SetWe5(data.Substring(offset, 40).Trim());
        offset += 40;
        SetWe6(data.Substring(offset, 40).Trim());
        offset += 40;
        SetWe7(data.Substring(offset, 40).Trim());
        offset += 40;
        SetWe8(data.Substring(offset, 40).Trim());
        offset += 40;
        SetWe9(data.Substring(offset, 40).Trim());
        offset += 40;
        SetWe10(data.Substring(offset, 40).Trim());
        offset += 40;
        SetWe11(data.Substring(offset, 40).Trim());
        offset += 40;
        SetWe12(data.Substring(offset, 40).Trim());
        offset += 40;
        SetWe13(data.Substring(offset, 40).Trim());
        offset += 40;
        
    }
    
    // Serialization methods
    public string GetWeMsgsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_We1.PadRight(40));
        result.Append(_We2.PadRight(40));
        result.Append(_We3.PadRight(40));
        result.Append(_We4.PadRight(40));
        result.Append(_We5.PadRight(40));
        result.Append(_We6.PadRight(40));
        result.Append(_We7.PadRight(40));
        result.Append(_We8.PadRight(40));
        result.Append(_We9.PadRight(40));
        result.Append(_We10.PadRight(40));
        result.Append(_We11.PadRight(40));
        result.Append(_We12.PadRight(40));
        result.Append(_We13.PadRight(40));
        
        return result.ToString();
    }
    
    public void SetWeMsgsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetWe1(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetWe2(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetWe3(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetWe4(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetWe5(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetWe6(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetWe7(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetWe8(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetWe9(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetWe10(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetWe11(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetWe12(extracted);
        }
        offset += 40;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetWe13(extracted);
        }
        offset += 40;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWe1()
    {
        return _We1;
    }
    
    // Standard Setter
    public void SetWe1(string value)
    {
        _We1 = value;
    }
    
    // Get<>AsString()
    public string GetWe1AsString()
    {
        return _We1.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetWe1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _We1 = value;
    }
    
    // Standard Getter
    public string GetWe2()
    {
        return _We2;
    }
    
    // Standard Setter
    public void SetWe2(string value)
    {
        _We2 = value;
    }
    
    // Get<>AsString()
    public string GetWe2AsString()
    {
        return _We2.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetWe2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _We2 = value;
    }
    
    // Standard Getter
    public string GetWe3()
    {
        return _We3;
    }
    
    // Standard Setter
    public void SetWe3(string value)
    {
        _We3 = value;
    }
    
    // Get<>AsString()
    public string GetWe3AsString()
    {
        return _We3.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetWe3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _We3 = value;
    }
    
    // Standard Getter
    public string GetWe4()
    {
        return _We4;
    }
    
    // Standard Setter
    public void SetWe4(string value)
    {
        _We4 = value;
    }
    
    // Get<>AsString()
    public string GetWe4AsString()
    {
        return _We4.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetWe4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _We4 = value;
    }
    
    // Standard Getter
    public string GetWe5()
    {
        return _We5;
    }
    
    // Standard Setter
    public void SetWe5(string value)
    {
        _We5 = value;
    }
    
    // Get<>AsString()
    public string GetWe5AsString()
    {
        return _We5.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetWe5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _We5 = value;
    }
    
    // Standard Getter
    public string GetWe6()
    {
        return _We6;
    }
    
    // Standard Setter
    public void SetWe6(string value)
    {
        _We6 = value;
    }
    
    // Get<>AsString()
    public string GetWe6AsString()
    {
        return _We6.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetWe6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _We6 = value;
    }
    
    // Standard Getter
    public string GetWe7()
    {
        return _We7;
    }
    
    // Standard Setter
    public void SetWe7(string value)
    {
        _We7 = value;
    }
    
    // Get<>AsString()
    public string GetWe7AsString()
    {
        return _We7.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetWe7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _We7 = value;
    }
    
    // Standard Getter
    public string GetWe8()
    {
        return _We8;
    }
    
    // Standard Setter
    public void SetWe8(string value)
    {
        _We8 = value;
    }
    
    // Get<>AsString()
    public string GetWe8AsString()
    {
        return _We8.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetWe8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _We8 = value;
    }
    
    // Standard Getter
    public string GetWe9()
    {
        return _We9;
    }
    
    // Standard Setter
    public void SetWe9(string value)
    {
        _We9 = value;
    }
    
    // Get<>AsString()
    public string GetWe9AsString()
    {
        return _We9.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetWe9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _We9 = value;
    }
    
    // Standard Getter
    public string GetWe10()
    {
        return _We10;
    }
    
    // Standard Setter
    public void SetWe10(string value)
    {
        _We10 = value;
    }
    
    // Get<>AsString()
    public string GetWe10AsString()
    {
        return _We10.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetWe10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _We10 = value;
    }
    
    // Standard Getter
    public string GetWe11()
    {
        return _We11;
    }
    
    // Standard Setter
    public void SetWe11(string value)
    {
        _We11 = value;
    }
    
    // Get<>AsString()
    public string GetWe11AsString()
    {
        return _We11.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetWe11AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _We11 = value;
    }
    
    // Standard Getter
    public string GetWe12()
    {
        return _We12;
    }
    
    // Standard Setter
    public void SetWe12(string value)
    {
        _We12 = value;
    }
    
    // Get<>AsString()
    public string GetWe12AsString()
    {
        return _We12.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetWe12AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _We12 = value;
    }
    
    // Standard Getter
    public string GetWe13()
    {
        return _We13;
    }
    
    // Standard Setter
    public void SetWe13(string value)
    {
        _We13 = value;
    }
    
    // Get<>AsString()
    public string GetWe13AsString()
    {
        return _We13.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetWe13AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _We13 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}