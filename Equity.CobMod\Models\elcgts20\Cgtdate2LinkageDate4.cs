using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing Cgtdate2LinkageDate4 Data Structure

public class Cgtdate2LinkageDate4
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate4, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd4 =0;
    
    
    
    
    // [DEBUG] Field: Filler25, is_external=, is_static_class=False, static_prefix=
    private Filler25 _Filler25 = new Filler25();
    
    
    
    
    // [DEBUG] Field: Filler26, is_external=, is_static_class=False, static_prefix=
    private Filler26 _Filler26 = new Filler26();
    
    
    
    
    // [DEBUG] Field: Filler27, is_external=, is_static_class=False, static_prefix=
    private Filler27 _Filler27 = new Filler27();
    
    
    
    
    // [DEBUG] Field: Filler28, is_external=, is_static_class=False, static_prefix=
    private Filler28 _Filler28 = new Filler28();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate4AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd4.ToString().PadLeft(8, '0'));
        result.Append(_Filler25.GetFiller25AsString());
        result.Append(_Filler26.GetFiller26AsString());
        result.Append(_Filler27.GetFiller27AsString());
        result.Append(_Filler28.GetFiller28AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate4AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd4(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler25.SetFiller25AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler25.SetFiller25AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler26.SetFiller26AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler26.SetFiller26AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler27.SetFiller27AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler27.SetFiller27AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler28.SetFiller28AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler28.SetFiller28AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate4AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate4(string value)
    {
        SetCgtdate2LinkageDate4AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd4()
    {
        return _Cgtdate2Ccyymmdd4;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd4(int value)
    {
        _Cgtdate2Ccyymmdd4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd4AsString()
    {
        return _Cgtdate2Ccyymmdd4.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd4 = parsed;
    }
    
    // Standard Getter
    public Filler25 GetFiller25()
    {
        return _Filler25;
    }
    
    // Standard Setter
    public void SetFiller25(Filler25 value)
    {
        _Filler25 = value;
    }
    
    // Get<>AsString()
    public string GetFiller25AsString()
    {
        return _Filler25 != null ? _Filler25.GetFiller25AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller25AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler25 == null)
        {
            _Filler25 = new Filler25();
        }
        _Filler25.SetFiller25AsString(value);
    }
    
    // Standard Getter
    public Filler26 GetFiller26()
    {
        return _Filler26;
    }
    
    // Standard Setter
    public void SetFiller26(Filler26 value)
    {
        _Filler26 = value;
    }
    
    // Get<>AsString()
    public string GetFiller26AsString()
    {
        return _Filler26 != null ? _Filler26.GetFiller26AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller26AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler26 == null)
        {
            _Filler26 = new Filler26();
        }
        _Filler26.SetFiller26AsString(value);
    }
    
    // Standard Getter
    public Filler27 GetFiller27()
    {
        return _Filler27;
    }
    
    // Standard Setter
    public void SetFiller27(Filler27 value)
    {
        _Filler27 = value;
    }
    
    // Get<>AsString()
    public string GetFiller27AsString()
    {
        return _Filler27 != null ? _Filler27.GetFiller27AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller27AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler27 == null)
        {
            _Filler27 = new Filler27();
        }
        _Filler27.SetFiller27AsString(value);
    }
    
    // Standard Getter
    public Filler28 GetFiller28()
    {
        return _Filler28;
    }
    
    // Standard Setter
    public void SetFiller28(Filler28 value)
    {
        _Filler28 = value;
    }
    
    // Get<>AsString()
    public string GetFiller28AsString()
    {
        return _Filler28 != null ? _Filler28.GetFiller28AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller28AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler28 == null)
        {
            _Filler28 = new Filler28();
        }
        _Filler28.SetFiller28AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller25(string value)
    {
        _Filler25.SetFiller25AsString(value);
    }
    // Nested Class: Filler25
    public class Filler25
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc4, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc4 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd4, is_external=, is_static_class=False, static_prefix=
        private Filler25.Cgtdate2Yymmdd4 _Cgtdate2Yymmdd4 = new Filler25.Cgtdate2Yymmdd4();
        
        
        
        
    public Filler25() {}
    
    public Filler25(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc4(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd4.SetCgtdate2Yymmdd4AsString(data.Substring(offset, Cgtdate2Yymmdd4.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller25AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc4.PadRight(2));
        result.Append(_Cgtdate2Yymmdd4.GetCgtdate2Yymmdd4AsString());
        
        return result.ToString();
    }
    
    public void SetFiller25AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc4(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd4.SetCgtdate2Yymmdd4AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd4.SetCgtdate2Yymmdd4AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc4()
    {
        return _Cgtdate2Cc4;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc4(string value)
    {
        _Cgtdate2Cc4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc4AsString()
    {
        return _Cgtdate2Cc4.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc4 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd4 GetCgtdate2Yymmdd4()
    {
        return _Cgtdate2Yymmdd4;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd4(Cgtdate2Yymmdd4 value)
    {
        _Cgtdate2Yymmdd4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd4AsString()
    {
        return _Cgtdate2Yymmdd4 != null ? _Cgtdate2Yymmdd4.GetCgtdate2Yymmdd4AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd4 == null)
        {
            _Cgtdate2Yymmdd4 = new Cgtdate2Yymmdd4();
        }
        _Cgtdate2Yymmdd4.SetCgtdate2Yymmdd4AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd4
    public class Cgtdate2Yymmdd4
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy4, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy4 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd4, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd4.Cgtdate2Mmdd4 _Cgtdate2Mmdd4 = new Cgtdate2Yymmdd4.Cgtdate2Mmdd4();
        
        
        
        
    public Cgtdate2Yymmdd4() {}
    
    public Cgtdate2Yymmdd4(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy4(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd4.SetCgtdate2Mmdd4AsString(data.Substring(offset, Cgtdate2Mmdd4.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd4AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy4.PadRight(2));
        result.Append(_Cgtdate2Mmdd4.GetCgtdate2Mmdd4AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd4AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy4(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd4.SetCgtdate2Mmdd4AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd4.SetCgtdate2Mmdd4AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy4()
    {
        return _Cgtdate2Yy4;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy4(string value)
    {
        _Cgtdate2Yy4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy4AsString()
    {
        return _Cgtdate2Yy4.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy4 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd4 GetCgtdate2Mmdd4()
    {
        return _Cgtdate2Mmdd4;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd4(Cgtdate2Mmdd4 value)
    {
        _Cgtdate2Mmdd4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd4AsString()
    {
        return _Cgtdate2Mmdd4 != null ? _Cgtdate2Mmdd4.GetCgtdate2Mmdd4AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd4 == null)
        {
            _Cgtdate2Mmdd4 = new Cgtdate2Mmdd4();
        }
        _Cgtdate2Mmdd4.SetCgtdate2Mmdd4AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd4
    public class Cgtdate2Mmdd4
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm4, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm4 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd4, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd4 ="";
        
        
        
        
    public Cgtdate2Mmdd4() {}
    
    public Cgtdate2Mmdd4(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm4(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd4(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd4AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm4.PadRight(2));
        result.Append(_Cgtdate2Dd4.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd4AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm4(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd4(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm4()
    {
        return _Cgtdate2Mm4;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm4(string value)
    {
        _Cgtdate2Mm4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm4AsString()
    {
        return _Cgtdate2Mm4.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm4 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd4()
    {
        return _Cgtdate2Dd4;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd4(string value)
    {
        _Cgtdate2Dd4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd4AsString()
    {
        return _Cgtdate2Dd4.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd4 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller26(string value)
{
    _Filler26.SetFiller26AsString(value);
}
// Nested Class: Filler26
public class Filler26
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy4 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd4 =0;
    
    
    
    
public Filler26() {}

public Filler26(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy4(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd4(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller26AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy4.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd4.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller26AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy4(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd4(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy4()
{
    return _Cgtdate2CCcyy4;
}

// Standard Setter
public void SetCgtdate2CCcyy4(int value)
{
    _Cgtdate2CCcyy4 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy4AsString()
{
    return _Cgtdate2CCcyy4.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy4 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd4()
{
    return _Cgtdate2CMmdd4;
}

// Standard Setter
public void SetCgtdate2CMmdd4(int value)
{
    _Cgtdate2CMmdd4 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd4AsString()
{
    return _Cgtdate2CMmdd4.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd4 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller27(string value)
{
    _Filler27.SetFiller27AsString(value);
}
// Nested Class: Filler27
public class Filler27
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc4 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy4 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm4 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd4 =0;
    
    
    
    
public Filler27() {}

public Filler27(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc4(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy4(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm4(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd4(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller27AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc4.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy4.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm4.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd4.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller27AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc4(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy4(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm4(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd4(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc4()
{
    return _Cgtdate2CCc4;
}

// Standard Setter
public void SetCgtdate2CCc4(int value)
{
    _Cgtdate2CCc4 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc4AsString()
{
    return _Cgtdate2CCc4.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc4 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy4()
{
    return _Cgtdate2CYy4;
}

// Standard Setter
public void SetCgtdate2CYy4(int value)
{
    _Cgtdate2CYy4 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy4AsString()
{
    return _Cgtdate2CYy4.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy4 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm4()
{
    return _Cgtdate2CMm4;
}

// Standard Setter
public void SetCgtdate2CMm4(int value)
{
    _Cgtdate2CMm4 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm4AsString()
{
    return _Cgtdate2CMm4.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm4 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd4()
{
    return _Cgtdate2CDd4;
}

// Standard Setter
public void SetCgtdate2CDd4(int value)
{
    _Cgtdate2CDd4 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd4AsString()
{
    return _Cgtdate2CDd4.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd4 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller28(string value)
{
    _Filler28.SetFiller28AsString(value);
}
// Nested Class: Filler28
public class Filler28
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm4 =0;
    
    
    
    
    // [DEBUG] Field: Filler29, is_external=, is_static_class=False, static_prefix=
    private string _Filler29 ="";
    
    
    
    
public Filler28() {}

public Filler28(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm4(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller29(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller28AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm4.ToString().PadLeft(6, '0'));
    result.Append(_Filler29.PadRight(2));
    
    return result.ToString();
}

public void SetFiller28AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm4(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller29(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm4()
{
    return _Cgtdate2CCcyymm4;
}

// Standard Setter
public void SetCgtdate2CCcyymm4(int value)
{
    _Cgtdate2CCcyymm4 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm4AsString()
{
    return _Cgtdate2CCcyymm4.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm4 = parsed;
}

// Standard Getter
public string GetFiller29()
{
    return _Filler29;
}

// Standard Setter
public void SetFiller29(string value)
{
    _Filler29 = value;
}

// Get<>AsString()
public string GetFiller29AsString()
{
    return _Filler29.PadRight(2);
}

// Set<>AsString()
public void SetFiller29AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler29 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}
