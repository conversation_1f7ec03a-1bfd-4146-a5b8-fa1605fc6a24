using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing WSchedRecTable Data Structure

public class WSchedRecTable
{
    private static int _size = 1710;
    // [DEBUG] Class: WSchedRecTable, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WRecStoreRealised, is_external=, is_static_class=False, static_prefix=
    private string _WRecStoreRealised ="";
    
    
    
    
    // [DEBUG] Field: WRecStoreUnrealised, is_external=, is_static_class=False, static_prefix=
    private string _WRecStoreUnrealised ="";
    
    
    
    
    // [DEBUG] Field: WRecStoreDeemedDisp, is_external=, is_static_class=False, static_prefix=
    private string _WRecStoreDeemedDisp ="";
    
    
    
    
    // [DEBUG] Field: WRecStoreRealisedTax, is_external=, is_static_class=False, static_prefix=
    private string _WRecStoreRealisedTax ="";
    
    
    
    
    // [DEBUG] Field: WRecStoreUnrealisedTax, is_external=, is_static_class=False, static_prefix=
    private string _WRecStoreUnrealisedTax ="";
    
    
    
    
    // [DEBUG] Field: WRecStoreCurrent, is_external=, is_static_class=False, static_prefix=
    private string _WRecStoreCurrent ="";
    
    
    
    
    
    // Serialization methods
    public string GetWSchedRecTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WRecStoreRealised.PadRight(285));
        result.Append(_WRecStoreUnrealised.PadRight(285));
        result.Append(_WRecStoreDeemedDisp.PadRight(285));
        result.Append(_WRecStoreRealisedTax.PadRight(285));
        result.Append(_WRecStoreUnrealisedTax.PadRight(285));
        result.Append(_WRecStoreCurrent.PadRight(285));
        
        return result.ToString();
    }
    
    public void SetWSchedRecTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 285 <= data.Length)
        {
            string extracted = data.Substring(offset, 285).Trim();
            SetWRecStoreRealised(extracted);
        }
        offset += 285;
        if (offset + 285 <= data.Length)
        {
            string extracted = data.Substring(offset, 285).Trim();
            SetWRecStoreUnrealised(extracted);
        }
        offset += 285;
        if (offset + 285 <= data.Length)
        {
            string extracted = data.Substring(offset, 285).Trim();
            SetWRecStoreDeemedDisp(extracted);
        }
        offset += 285;
        if (offset + 285 <= data.Length)
        {
            string extracted = data.Substring(offset, 285).Trim();
            SetWRecStoreRealisedTax(extracted);
        }
        offset += 285;
        if (offset + 285 <= data.Length)
        {
            string extracted = data.Substring(offset, 285).Trim();
            SetWRecStoreUnrealisedTax(extracted);
        }
        offset += 285;
        if (offset + 285 <= data.Length)
        {
            string extracted = data.Substring(offset, 285).Trim();
            SetWRecStoreCurrent(extracted);
        }
        offset += 285;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWSchedRecTableAsString();
    }
    // Set<>String Override function
    public void SetWSchedRecTable(string value)
    {
        SetWSchedRecTableAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWRecStoreRealised()
    {
        return _WRecStoreRealised;
    }
    
    // Standard Setter
    public void SetWRecStoreRealised(string value)
    {
        _WRecStoreRealised = value;
    }
    
    // Get<>AsString()
    public string GetWRecStoreRealisedAsString()
    {
        return _WRecStoreRealised.PadRight(285);
    }
    
    // Set<>AsString()
    public void SetWRecStoreRealisedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WRecStoreRealised = value;
    }
    
    // Standard Getter
    public string GetWRecStoreUnrealised()
    {
        return _WRecStoreUnrealised;
    }
    
    // Standard Setter
    public void SetWRecStoreUnrealised(string value)
    {
        _WRecStoreUnrealised = value;
    }
    
    // Get<>AsString()
    public string GetWRecStoreUnrealisedAsString()
    {
        return _WRecStoreUnrealised.PadRight(285);
    }
    
    // Set<>AsString()
    public void SetWRecStoreUnrealisedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WRecStoreUnrealised = value;
    }
    
    // Standard Getter
    public string GetWRecStoreDeemedDisp()
    {
        return _WRecStoreDeemedDisp;
    }
    
    // Standard Setter
    public void SetWRecStoreDeemedDisp(string value)
    {
        _WRecStoreDeemedDisp = value;
    }
    
    // Get<>AsString()
    public string GetWRecStoreDeemedDispAsString()
    {
        return _WRecStoreDeemedDisp.PadRight(285);
    }
    
    // Set<>AsString()
    public void SetWRecStoreDeemedDispAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WRecStoreDeemedDisp = value;
    }
    
    // Standard Getter
    public string GetWRecStoreRealisedTax()
    {
        return _WRecStoreRealisedTax;
    }
    
    // Standard Setter
    public void SetWRecStoreRealisedTax(string value)
    {
        _WRecStoreRealisedTax = value;
    }
    
    // Get<>AsString()
    public string GetWRecStoreRealisedTaxAsString()
    {
        return _WRecStoreRealisedTax.PadRight(285);
    }
    
    // Set<>AsString()
    public void SetWRecStoreRealisedTaxAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WRecStoreRealisedTax = value;
    }
    
    // Standard Getter
    public string GetWRecStoreUnrealisedTax()
    {
        return _WRecStoreUnrealisedTax;
    }
    
    // Standard Setter
    public void SetWRecStoreUnrealisedTax(string value)
    {
        _WRecStoreUnrealisedTax = value;
    }
    
    // Get<>AsString()
    public string GetWRecStoreUnrealisedTaxAsString()
    {
        return _WRecStoreUnrealisedTax.PadRight(285);
    }
    
    // Set<>AsString()
    public void SetWRecStoreUnrealisedTaxAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WRecStoreUnrealisedTax = value;
    }
    
    // Standard Getter
    public string GetWRecStoreCurrent()
    {
        return _WRecStoreCurrent;
    }
    
    // Standard Setter
    public void SetWRecStoreCurrent(string value)
    {
        _WRecStoreCurrent = value;
    }
    
    // Get<>AsString()
    public string GetWRecStoreCurrentAsString()
    {
        return _WRecStoreCurrent.PadRight(285);
    }
    
    // Set<>AsString()
    public void SetWRecStoreCurrentAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WRecStoreCurrent = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}