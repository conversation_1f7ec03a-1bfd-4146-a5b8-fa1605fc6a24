using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D73File Data Structure

public class D73File
{
    private static int _size = 12;
    // [DEBUG] Class: D73File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler229, is_external=, is_static_class=False, static_prefix=
    private string _Filler229 ="$";
    
    
    
    
    // [DEBUG] Field: D73UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D73UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler230, is_external=, is_static_class=False, static_prefix=
    private string _Filler230 ="RD";
    
    
    
    
    // [DEBUG] Field: D73ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D73ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler231, is_external=, is_static_class=False, static_prefix=
    private string _Filler231 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD73FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler229.PadRight(1));
        result.Append(_D73UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler230.PadRight(2));
        result.Append(_D73ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler231.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD73FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller229(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD73UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller230(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD73ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller231(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD73FileAsString();
    }
    // Set<>String Override function
    public void SetD73File(string value)
    {
        SetD73FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller229()
    {
        return _Filler229;
    }
    
    // Standard Setter
    public void SetFiller229(string value)
    {
        _Filler229 = value;
    }
    
    // Get<>AsString()
    public string GetFiller229AsString()
    {
        return _Filler229.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller229AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler229 = value;
    }
    
    // Standard Getter
    public int GetD73UserNo()
    {
        return _D73UserNo;
    }
    
    // Standard Setter
    public void SetD73UserNo(int value)
    {
        _D73UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD73UserNoAsString()
    {
        return _D73UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD73UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D73UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller230()
    {
        return _Filler230;
    }
    
    // Standard Setter
    public void SetFiller230(string value)
    {
        _Filler230 = value;
    }
    
    // Get<>AsString()
    public string GetFiller230AsString()
    {
        return _Filler230.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller230AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler230 = value;
    }
    
    // Standard Getter
    public int GetD73ReportNo()
    {
        return _D73ReportNo;
    }
    
    // Standard Setter
    public void SetD73ReportNo(int value)
    {
        _D73ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD73ReportNoAsString()
    {
        return _D73ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD73ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D73ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller231()
    {
        return _Filler231;
    }
    
    // Standard Setter
    public void SetFiller231(string value)
    {
        _Filler231 = value;
    }
    
    // Get<>AsString()
    public string GetFiller231AsString()
    {
        return _Filler231.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller231AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler231 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}