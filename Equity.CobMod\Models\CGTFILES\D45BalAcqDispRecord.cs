using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D45BalAcqDispRecord Data Structure

public class D45BalAcqDispRecord
{
    private static int _size = 32985;
    // [DEBUG] Class: D45BalAcqDispRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: YeD45DetailFront, is_external=, is_static_class=False, static_prefix=
    private YeD45DetailFront _YeD45DetailFront = new YeD45DetailFront();
    
    
    
    
    // [DEBUG] Field: YeD45DetailIds, is_external=, is_static_class=False, static_prefix=
    private YeD45DetailIds _YeD45DetailIds = new YeD45DetailIds();
    
    
    
    
    // [DEBUG] Field: YeD45DetailRemainder1, is_external=, is_static_class=False, static_prefix=
    private YeD45DetailRemainder1 _YeD45DetailRemainder1 = new YeD45DetailRemainder1();
    
    
    
    
    
    // Serialization methods
    public string GetD45BalAcqDispRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_YeD45DetailFront.GetYeD45DetailFrontAsString());
        result.Append(_YeD45DetailIds.GetYeD45DetailIdsAsString());
        result.Append(_YeD45DetailRemainder1.GetYeD45DetailRemainder1AsString());
        
        return result.ToString();
    }
    
    public void SetD45BalAcqDispRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 186 <= data.Length)
        {
            _YeD45DetailFront.SetYeD45DetailFrontAsString(data.Substring(offset, 186));
        }
        else
        {
            _YeD45DetailFront.SetYeD45DetailFrontAsString(data.Substring(offset));
        }
        offset += 186;
        if (offset + 224 <= data.Length)
        {
            _YeD45DetailIds.SetYeD45DetailIdsAsString(data.Substring(offset, 224));
        }
        else
        {
            _YeD45DetailIds.SetYeD45DetailIdsAsString(data.Substring(offset));
        }
        offset += 224;
        if (offset + 32575 <= data.Length)
        {
            _YeD45DetailRemainder1.SetYeD45DetailRemainder1AsString(data.Substring(offset, 32575));
        }
        else
        {
            _YeD45DetailRemainder1.SetYeD45DetailRemainder1AsString(data.Substring(offset));
        }
        offset += 32575;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD45BalAcqDispRecordAsString();
    }
    // Set<>String Override function
    public void SetD45BalAcqDispRecord(string value)
    {
        SetD45BalAcqDispRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public YeD45DetailFront GetYeD45DetailFront()
    {
        return _YeD45DetailFront;
    }
    
    // Standard Setter
    public void SetYeD45DetailFront(YeD45DetailFront value)
    {
        _YeD45DetailFront = value;
    }
    
    // Get<>AsString()
    public string GetYeD45DetailFrontAsString()
    {
        return _YeD45DetailFront != null ? _YeD45DetailFront.GetYeD45DetailFrontAsString() : "";
    }
    
    // Set<>AsString()
    public void SetYeD45DetailFrontAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_YeD45DetailFront == null)
        {
            _YeD45DetailFront = new YeD45DetailFront();
        }
        _YeD45DetailFront.SetYeD45DetailFrontAsString(value);
    }
    
    // Standard Getter
    public YeD45DetailIds GetYeD45DetailIds()
    {
        return _YeD45DetailIds;
    }
    
    // Standard Setter
    public void SetYeD45DetailIds(YeD45DetailIds value)
    {
        _YeD45DetailIds = value;
    }
    
    // Get<>AsString()
    public string GetYeD45DetailIdsAsString()
    {
        return _YeD45DetailIds != null ? _YeD45DetailIds.GetYeD45DetailIdsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetYeD45DetailIdsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_YeD45DetailIds == null)
        {
            _YeD45DetailIds = new YeD45DetailIds();
        }
        _YeD45DetailIds.SetYeD45DetailIdsAsString(value);
    }
    
    // Standard Getter
    public YeD45DetailRemainder1 GetYeD45DetailRemainder1()
    {
        return _YeD45DetailRemainder1;
    }
    
    // Standard Setter
    public void SetYeD45DetailRemainder1(YeD45DetailRemainder1 value)
    {
        _YeD45DetailRemainder1 = value;
    }
    
    // Get<>AsString()
    public string GetYeD45DetailRemainder1AsString()
    {
        return _YeD45DetailRemainder1 != null ? _YeD45DetailRemainder1.GetYeD45DetailRemainder1AsString() : "";
    }
    
    // Set<>AsString()
    public void SetYeD45DetailRemainder1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_YeD45DetailRemainder1 == null)
        {
            _YeD45DetailRemainder1 = new YeD45DetailRemainder1();
        }
        _YeD45DetailRemainder1.SetYeD45DetailRemainder1AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetYeD45DetailFront(string value)
    {
        _YeD45DetailFront.SetYeD45DetailFrontAsString(value);
    }
    // Nested Class: YeD45DetailFront
    public class YeD45DetailFront
    {
        private static int _size = 186;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D452Key, is_external=, is_static_class=False, static_prefix=
        private YeD45DetailFront.D452Key _D452Key = new YeD45DetailFront.D452Key();
        
        
        
        
        // [DEBUG] Field: D452TransactionCategory, is_external=, is_static_class=False, static_prefix=
        private string _D452TransactionCategory ="";
        
        
        // 88-level condition checks for D452TransactionCategory
        public bool IsD452IdentifiableTran()
        {
            if (this._D452TransactionCategory == "'00'") return true;
            if (this._D452TransactionCategory == "'SP'") return true;
            if (this._D452TransactionCategory == "'PP'") return true;
            if (this._D452TransactionCategory == "'TB'") return true;
            if (this._D452TransactionCategory == "'NI'") return true;
            if (this._D452TransactionCategory == "'RP'") return true;
            if (this._D452TransactionCategory == "'UL'") return true;
            if (this._D452TransactionCategory == "'01'") return true;
            if (this._D452TransactionCategory == "'SX'") return true;
            return false;
        }
        public bool IsD452ApportionedTran()
        {
            if (this._D452TransactionCategory == "'RG'") return true;
            if (this._D452TransactionCategory == "'CN'") return true;
            if (this._D452TransactionCategory == "'TR'") return true;
            if (this._D452TransactionCategory == "'TS'") return true;
            if (this._D452TransactionCategory == "'CS'") return true;
            if (this._D452TransactionCategory == "'CD'") return true;
            if (this._D452TransactionCategory == "'RF'") return true;
            return false;
        }
        public bool IsD452ApportionedTransfer()
        {
            if (this._D452TransactionCategory == "'RG'") return true;
            if (this._D452TransactionCategory == "'CN'") return true;
            if (this._D452TransactionCategory == "'TR'") return true;
            if (this._D452TransactionCategory == "'TS'") return true;
            if (this._D452TransactionCategory == "'CS'") return true;
            return false;
        }
        public bool IsD452TransToBeConsolidated()
        {
            if (this._D452TransactionCategory == "'RG'") return true;
            if (this._D452TransactionCategory == "'CN'") return true;
            if (this._D452TransactionCategory == "'TR'") return true;
            if (this._D452TransactionCategory == "'TS'") return true;
            if (this._D452TransactionCategory == "'CS'") return true;
            if (this._D452TransactionCategory == "'TP'") return true;
            if (this._D452TransactionCategory == "'CP'") return true;
            if (this._D452TransactionCategory == "'GP'") return true;
            if (this._D452TransactionCategory == "'GS'") return true;
            if (this._D452TransactionCategory == "'RS'") return true;
            if (this._D452TransactionCategory == "'RL'") return true;
            if (this._D452TransactionCategory == "'RD'") return true;
            if (this._D452TransactionCategory == "'CD'") return true;
            if (this._D452TransactionCategory == "'LQ'") return true;
            if (this._D452TransactionCategory == "'RF'") return true;
            if (this._D452TransactionCategory == "'SS'") return true;
            return false;
        }
        public bool IsD452DisposalTran()
        {
            if (this._D452TransactionCategory == "'RS'") return true;
            if (this._D452TransactionCategory == "'RL'") return true;
            if (this._D452TransactionCategory == "'RD'") return true;
            if (this._D452TransactionCategory == "'CD'") return true;
            if (this._D452TransactionCategory == "'LQ'") return true;
            if (this._D452TransactionCategory == "'RF'") return true;
            if (this._D452TransactionCategory == "'SS'") return true;
            return false;
        }
        public bool IsD452TransLinkedByBargain()
        {
            if (this._D452TransactionCategory == "'TS'") return true;
            if (this._D452TransactionCategory == "'CS'") return true;
            if (this._D452TransactionCategory == "'TP'") return true;
            if (this._D452TransactionCategory == "'CP'") return true;
            if (this._D452TransactionCategory == "'GP'") return true;
            if (this._D452TransactionCategory == "'GS'") return true;
            return false;
        }
        public bool IsD452TransLinkedByPrevRef()
        {
            if (this._D452TransactionCategory == "'RG'") return true;
            if (this._D452TransactionCategory == "'CN'") return true;
            if (this._D452TransactionCategory == "'TR'") return true;
            if (this._D452TransactionCategory == "'CL'") return true;
            if (this._D452TransactionCategory == "'RC'") return true;
            if (this._D452TransactionCategory == "'RR'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: D452DateTimeStamp, is_external=, is_static_class=False, static_prefix=
        private YeD45DetailFront.D452DateTimeStamp _D452DateTimeStamp = new YeD45DetailFront.D452DateTimeStamp();
        
        
        
        
        // [DEBUG] Field: Filler38, is_external=, is_static_class=False, static_prefix=
        private YeD45DetailFront.Filler38 _Filler38 = new YeD45DetailFront.Filler38();
        
        
        
        
        // [DEBUG] Field: D452CalParentSedolCode, is_external=, is_static_class=False, static_prefix=
        private YeD45DetailFront.D452CalParentSedolCode _D452CalParentSedolCode = new YeD45DetailFront.D452CalParentSedolCode();
        
        
        
        
        // [DEBUG] Field: D452CalPreviousSedolCode, is_external=, is_static_class=False, static_prefix=
        private YeD45DetailFront.D452CalPreviousSedolCode _D452CalPreviousSedolCode = new YeD45DetailFront.D452CalPreviousSedolCode();
        
        
        
        
        // [DEBUG] Field: D452OriginalBargainNo, is_external=, is_static_class=False, static_prefix=
        private YeD45DetailFront.D452OriginalBargainNo _D452OriginalBargainNo = new YeD45DetailFront.D452OriginalBargainNo();
        
        
        
        
        // [DEBUG] Field: D452BargainDate, is_external=, is_static_class=False, static_prefix=
        private YeD45DetailFront.D452BargainDate _D452BargainDate = new YeD45DetailFront.D452BargainDate();
        
        
        
        
        // [DEBUG] Field: D452BargainDate9, is_external=, is_static_class=False, static_prefix=
        private int _D452BargainDate9 =0;
        
        
        
        
        // [DEBUG] Field: D452SettlementDate, is_external=, is_static_class=False, static_prefix=
        private YeD45DetailFront.D452SettlementDate _D452SettlementDate = new YeD45DetailFront.D452SettlementDate();
        
        
        
        
        // [DEBUG] Field: D452CurrencyCode, is_external=, is_static_class=False, static_prefix=
        private string _D452CurrencyCode ="";
        
        
        
        
        // [DEBUG] Field: D452NotesComments, is_external=, is_static_class=False, static_prefix=
        private string _D452NotesComments ="";
        
        
        
        
        // [DEBUG] Field: D452CtLinkFund, is_external=, is_static_class=False, static_prefix=
        private string _D452CtLinkFund ="";
        
        
        
        
        // [DEBUG] Field: D452SubFund, is_external=, is_static_class=False, static_prefix=
        private string _D452SubFund ="";
        
        
        
        
        // [DEBUG] Field: D452TransactionOverride, is_external=, is_static_class=False, static_prefix=
        private string _D452TransactionOverride ="";
        
        
        
        
        // [DEBUG] Field: D452ParentMarketPrice, is_external=, is_static_class=False, static_prefix=
        private decimal _D452ParentMarketPrice =0;
        
        
        
        
        // [DEBUG] Field: D452TransUnitPrice, is_external=, is_static_class=False, static_prefix=
        private decimal _D452TransUnitPrice =0;
        
        
        
        
        // [DEBUG] Field: D452PricePctIndicator, is_external=, is_static_class=False, static_prefix=
        private string _D452PricePctIndicator ="";
        
        
        
        
        // [DEBUG] Field: D452LiabilityPerShare, is_external=, is_static_class=False, static_prefix=
        private decimal _D452LiabilityPerShare =0;
        
        
        
        
        // [DEBUG] Field: D452OutstandingLiability, is_external=, is_static_class=False, static_prefix=
        private decimal _D452OutstandingLiability =0;
        
        
        
        
        // [DEBUG] Field: D452StockExchIndicator, is_external=, is_static_class=False, static_prefix=
        private string _D452StockExchIndicator ="";
        
        
        
        
    public YeD45DetailFront() {}
    
    public YeD45DetailFront(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _D452Key.SetD452KeyAsString(data.Substring(offset, D452Key.GetSize()));
        offset += 25;
        SetD452TransactionCategory(data.Substring(offset, 2).Trim());
        offset += 2;
        _D452DateTimeStamp.SetD452DateTimeStampAsString(data.Substring(offset, D452DateTimeStamp.GetSize()));
        offset += 14;
        _Filler38.SetFiller38AsString(data.Substring(offset, Filler38.GetSize()));
        offset += 14;
        _D452CalParentSedolCode.SetD452CalParentSedolCodeAsString(data.Substring(offset, D452CalParentSedolCode.GetSize()));
        offset += 11;
        _D452CalPreviousSedolCode.SetD452CalPreviousSedolCodeAsString(data.Substring(offset, D452CalPreviousSedolCode.GetSize()));
        offset += 11;
        _D452OriginalBargainNo.SetD452OriginalBargainNoAsString(data.Substring(offset, D452OriginalBargainNo.GetSize()));
        offset += 10;
        _D452BargainDate.SetD452BargainDateAsString(data.Substring(offset, D452BargainDate.GetSize()));
        offset += 6;
        SetD452BargainDate9(int.Parse(data.Substring(offset, 6).Trim()));
        offset += 6;
        _D452SettlementDate.SetD452SettlementDateAsString(data.Substring(offset, D452SettlementDate.GetSize()));
        offset += 6;
        SetD452CurrencyCode(data.Substring(offset, 3).Trim());
        offset += 3;
        SetD452NotesComments(data.Substring(offset, 39).Trim());
        offset += 39;
        SetD452CtLinkFund(data.Substring(offset, 4).Trim());
        offset += 4;
        SetD452SubFund(data.Substring(offset, 4).Trim());
        offset += 4;
        SetD452TransactionOverride(data.Substring(offset, 1).Trim());
        offset += 1;
        SetD452ParentMarketPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 10)));
        offset += 10;
        SetD452TransUnitPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 6)));
        offset += 6;
        SetD452PricePctIndicator(data.Substring(offset, 1).Trim());
        offset += 1;
        SetD452LiabilityPerShare(PackedDecimalConverter.ToDecimal(data.Substring(offset, 6)));
        offset += 6;
        SetD452OutstandingLiability(PackedDecimalConverter.ToDecimal(data.Substring(offset, 6)));
        offset += 6;
        SetD452StockExchIndicator(data.Substring(offset, 1).Trim());
        offset += 1;
        
    }
    
    // Serialization methods
    public string GetYeD45DetailFrontAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D452Key.GetD452KeyAsString());
        result.Append(_D452TransactionCategory.PadRight(2));
        result.Append(_D452DateTimeStamp.GetD452DateTimeStampAsString());
        result.Append(_Filler38.GetFiller38AsString());
        result.Append(_D452CalParentSedolCode.GetD452CalParentSedolCodeAsString());
        result.Append(_D452CalPreviousSedolCode.GetD452CalPreviousSedolCodeAsString());
        result.Append(_D452OriginalBargainNo.GetD452OriginalBargainNoAsString());
        result.Append(_D452BargainDate.GetD452BargainDateAsString());
        result.Append(_D452BargainDate9.ToString().PadLeft(6, '0'));
        result.Append(_D452SettlementDate.GetD452SettlementDateAsString());
        result.Append(_D452CurrencyCode.PadRight(3));
        result.Append(_D452NotesComments.PadRight(39));
        result.Append(_D452CtLinkFund.PadRight(4));
        result.Append(_D452SubFund.PadRight(4));
        result.Append(_D452TransactionOverride.PadRight(1));
        result.Append(_D452ParentMarketPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D452TransUnitPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D452PricePctIndicator.PadRight(1));
        result.Append(_D452LiabilityPerShare.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D452OutstandingLiability.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D452StockExchIndicator.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetYeD45DetailFrontAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 25 <= data.Length)
        {
            _D452Key.SetD452KeyAsString(data.Substring(offset, 25));
        }
        else
        {
            _D452Key.SetD452KeyAsString(data.Substring(offset));
        }
        offset += 25;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD452TransactionCategory(extracted);
        }
        offset += 2;
        if (offset + 14 <= data.Length)
        {
            _D452DateTimeStamp.SetD452DateTimeStampAsString(data.Substring(offset, 14));
        }
        else
        {
            _D452DateTimeStamp.SetD452DateTimeStampAsString(data.Substring(offset));
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            _Filler38.SetFiller38AsString(data.Substring(offset, 14));
        }
        else
        {
            _Filler38.SetFiller38AsString(data.Substring(offset));
        }
        offset += 14;
        if (offset + 11 <= data.Length)
        {
            _D452CalParentSedolCode.SetD452CalParentSedolCodeAsString(data.Substring(offset, 11));
        }
        else
        {
            _D452CalParentSedolCode.SetD452CalParentSedolCodeAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 11 <= data.Length)
        {
            _D452CalPreviousSedolCode.SetD452CalPreviousSedolCodeAsString(data.Substring(offset, 11));
        }
        else
        {
            _D452CalPreviousSedolCode.SetD452CalPreviousSedolCodeAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 10 <= data.Length)
        {
            _D452OriginalBargainNo.SetD452OriginalBargainNoAsString(data.Substring(offset, 10));
        }
        else
        {
            _D452OriginalBargainNo.SetD452OriginalBargainNoAsString(data.Substring(offset));
        }
        offset += 10;
        if (offset + 6 <= data.Length)
        {
            _D452BargainDate.SetD452BargainDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _D452BargainDate.SetD452BargainDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD452BargainDate9(parsedInt);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            _D452SettlementDate.SetD452SettlementDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _D452SettlementDate.SetD452SettlementDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD452CurrencyCode(extracted);
        }
        offset += 3;
        if (offset + 39 <= data.Length)
        {
            string extracted = data.Substring(offset, 39).Trim();
            SetD452NotesComments(extracted);
        }
        offset += 39;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD452CtLinkFund(extracted);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD452SubFund(extracted);
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD452TransactionOverride(extracted);
        }
        offset += 1;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD452ParentMarketPrice(parsedDec);
        }
        offset += 10;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD452TransUnitPrice(parsedDec);
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD452PricePctIndicator(extracted);
        }
        offset += 1;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD452LiabilityPerShare(parsedDec);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD452OutstandingLiability(parsedDec);
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD452StockExchIndicator(extracted);
        }
        offset += 1;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public D452Key GetD452Key()
    {
        return _D452Key;
    }
    
    // Standard Setter
    public void SetD452Key(D452Key value)
    {
        _D452Key = value;
    }
    
    // Get<>AsString()
    public string GetD452KeyAsString()
    {
        return _D452Key != null ? _D452Key.GetD452KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD452KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D452Key == null)
        {
            _D452Key = new D452Key();
        }
        _D452Key.SetD452KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD452TransactionCategory()
    {
        return _D452TransactionCategory;
    }
    
    // Standard Setter
    public void SetD452TransactionCategory(string value)
    {
        _D452TransactionCategory = value;
    }
    
    // Get<>AsString()
    public string GetD452TransactionCategoryAsString()
    {
        return _D452TransactionCategory.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD452TransactionCategoryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D452TransactionCategory = value;
    }
    
    // Standard Getter
    public D452DateTimeStamp GetD452DateTimeStamp()
    {
        return _D452DateTimeStamp;
    }
    
    // Standard Setter
    public void SetD452DateTimeStamp(D452DateTimeStamp value)
    {
        _D452DateTimeStamp = value;
    }
    
    // Get<>AsString()
    public string GetD452DateTimeStampAsString()
    {
        return _D452DateTimeStamp != null ? _D452DateTimeStamp.GetD452DateTimeStampAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD452DateTimeStampAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D452DateTimeStamp == null)
        {
            _D452DateTimeStamp = new D452DateTimeStamp();
        }
        _D452DateTimeStamp.SetD452DateTimeStampAsString(value);
    }
    
    // Standard Getter
    public Filler38 GetFiller38()
    {
        return _Filler38;
    }
    
    // Standard Setter
    public void SetFiller38(Filler38 value)
    {
        _Filler38 = value;
    }
    
    // Get<>AsString()
    public string GetFiller38AsString()
    {
        return _Filler38 != null ? _Filler38.GetFiller38AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller38AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler38 == null)
        {
            _Filler38 = new Filler38();
        }
        _Filler38.SetFiller38AsString(value);
    }
    
    // Standard Getter
    public D452CalParentSedolCode GetD452CalParentSedolCode()
    {
        return _D452CalParentSedolCode;
    }
    
    // Standard Setter
    public void SetD452CalParentSedolCode(D452CalParentSedolCode value)
    {
        _D452CalParentSedolCode = value;
    }
    
    // Get<>AsString()
    public string GetD452CalParentSedolCodeAsString()
    {
        return _D452CalParentSedolCode != null ? _D452CalParentSedolCode.GetD452CalParentSedolCodeAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD452CalParentSedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D452CalParentSedolCode == null)
        {
            _D452CalParentSedolCode = new D452CalParentSedolCode();
        }
        _D452CalParentSedolCode.SetD452CalParentSedolCodeAsString(value);
    }
    
    // Standard Getter
    public D452CalPreviousSedolCode GetD452CalPreviousSedolCode()
    {
        return _D452CalPreviousSedolCode;
    }
    
    // Standard Setter
    public void SetD452CalPreviousSedolCode(D452CalPreviousSedolCode value)
    {
        _D452CalPreviousSedolCode = value;
    }
    
    // Get<>AsString()
    public string GetD452CalPreviousSedolCodeAsString()
    {
        return _D452CalPreviousSedolCode != null ? _D452CalPreviousSedolCode.GetD452CalPreviousSedolCodeAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD452CalPreviousSedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D452CalPreviousSedolCode == null)
        {
            _D452CalPreviousSedolCode = new D452CalPreviousSedolCode();
        }
        _D452CalPreviousSedolCode.SetD452CalPreviousSedolCodeAsString(value);
    }
    
    // Standard Getter
    public D452OriginalBargainNo GetD452OriginalBargainNo()
    {
        return _D452OriginalBargainNo;
    }
    
    // Standard Setter
    public void SetD452OriginalBargainNo(D452OriginalBargainNo value)
    {
        _D452OriginalBargainNo = value;
    }
    
    // Get<>AsString()
    public string GetD452OriginalBargainNoAsString()
    {
        return _D452OriginalBargainNo != null ? _D452OriginalBargainNo.GetD452OriginalBargainNoAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD452OriginalBargainNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D452OriginalBargainNo == null)
        {
            _D452OriginalBargainNo = new D452OriginalBargainNo();
        }
        _D452OriginalBargainNo.SetD452OriginalBargainNoAsString(value);
    }
    
    // Standard Getter
    public D452BargainDate GetD452BargainDate()
    {
        return _D452BargainDate;
    }
    
    // Standard Setter
    public void SetD452BargainDate(D452BargainDate value)
    {
        _D452BargainDate = value;
    }
    
    // Get<>AsString()
    public string GetD452BargainDateAsString()
    {
        return _D452BargainDate != null ? _D452BargainDate.GetD452BargainDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD452BargainDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D452BargainDate == null)
        {
            _D452BargainDate = new D452BargainDate();
        }
        _D452BargainDate.SetD452BargainDateAsString(value);
    }
    
    // Standard Getter
    public int GetD452BargainDate9()
    {
        return _D452BargainDate9;
    }
    
    // Standard Setter
    public void SetD452BargainDate9(int value)
    {
        _D452BargainDate9 = value;
    }
    
    // Get<>AsString()
    public string GetD452BargainDate9AsString()
    {
        return _D452BargainDate9.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD452BargainDate9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D452BargainDate9 = parsed;
    }
    
    // Standard Getter
    public D452SettlementDate GetD452SettlementDate()
    {
        return _D452SettlementDate;
    }
    
    // Standard Setter
    public void SetD452SettlementDate(D452SettlementDate value)
    {
        _D452SettlementDate = value;
    }
    
    // Get<>AsString()
    public string GetD452SettlementDateAsString()
    {
        return _D452SettlementDate != null ? _D452SettlementDate.GetD452SettlementDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD452SettlementDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D452SettlementDate == null)
        {
            _D452SettlementDate = new D452SettlementDate();
        }
        _D452SettlementDate.SetD452SettlementDateAsString(value);
    }
    
    // Standard Getter
    public string GetD452CurrencyCode()
    {
        return _D452CurrencyCode;
    }
    
    // Standard Setter
    public void SetD452CurrencyCode(string value)
    {
        _D452CurrencyCode = value;
    }
    
    // Get<>AsString()
    public string GetD452CurrencyCodeAsString()
    {
        return _D452CurrencyCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD452CurrencyCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D452CurrencyCode = value;
    }
    
    // Standard Getter
    public string GetD452NotesComments()
    {
        return _D452NotesComments;
    }
    
    // Standard Setter
    public void SetD452NotesComments(string value)
    {
        _D452NotesComments = value;
    }
    
    // Get<>AsString()
    public string GetD452NotesCommentsAsString()
    {
        return _D452NotesComments.PadRight(39);
    }
    
    // Set<>AsString()
    public void SetD452NotesCommentsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D452NotesComments = value;
    }
    
    // Standard Getter
    public string GetD452CtLinkFund()
    {
        return _D452CtLinkFund;
    }
    
    // Standard Setter
    public void SetD452CtLinkFund(string value)
    {
        _D452CtLinkFund = value;
    }
    
    // Get<>AsString()
    public string GetD452CtLinkFundAsString()
    {
        return _D452CtLinkFund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD452CtLinkFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D452CtLinkFund = value;
    }
    
    // Standard Getter
    public string GetD452SubFund()
    {
        return _D452SubFund;
    }
    
    // Standard Setter
    public void SetD452SubFund(string value)
    {
        _D452SubFund = value;
    }
    
    // Get<>AsString()
    public string GetD452SubFundAsString()
    {
        return _D452SubFund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD452SubFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D452SubFund = value;
    }
    
    // Standard Getter
    public string GetD452TransactionOverride()
    {
        return _D452TransactionOverride;
    }
    
    // Standard Setter
    public void SetD452TransactionOverride(string value)
    {
        _D452TransactionOverride = value;
    }
    
    // Get<>AsString()
    public string GetD452TransactionOverrideAsString()
    {
        return _D452TransactionOverride.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD452TransactionOverrideAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D452TransactionOverride = value;
    }
    
    // Standard Getter
    public decimal GetD452ParentMarketPrice()
    {
        return _D452ParentMarketPrice;
    }
    
    // Standard Setter
    public void SetD452ParentMarketPrice(decimal value)
    {
        _D452ParentMarketPrice = value;
    }
    
    // Get<>AsString()
    public string GetD452ParentMarketPriceAsString()
    {
        return _D452ParentMarketPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD452ParentMarketPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452ParentMarketPrice = parsed;
    }
    
    // Standard Getter
    public decimal GetD452TransUnitPrice()
    {
        return _D452TransUnitPrice;
    }
    
    // Standard Setter
    public void SetD452TransUnitPrice(decimal value)
    {
        _D452TransUnitPrice = value;
    }
    
    // Get<>AsString()
    public string GetD452TransUnitPriceAsString()
    {
        return _D452TransUnitPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD452TransUnitPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452TransUnitPrice = parsed;
    }
    
    // Standard Getter
    public string GetD452PricePctIndicator()
    {
        return _D452PricePctIndicator;
    }
    
    // Standard Setter
    public void SetD452PricePctIndicator(string value)
    {
        _D452PricePctIndicator = value;
    }
    
    // Get<>AsString()
    public string GetD452PricePctIndicatorAsString()
    {
        return _D452PricePctIndicator.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD452PricePctIndicatorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D452PricePctIndicator = value;
    }
    
    // Standard Getter
    public decimal GetD452LiabilityPerShare()
    {
        return _D452LiabilityPerShare;
    }
    
    // Standard Setter
    public void SetD452LiabilityPerShare(decimal value)
    {
        _D452LiabilityPerShare = value;
    }
    
    // Get<>AsString()
    public string GetD452LiabilityPerShareAsString()
    {
        return _D452LiabilityPerShare.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD452LiabilityPerShareAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452LiabilityPerShare = parsed;
    }
    
    // Standard Getter
    public decimal GetD452OutstandingLiability()
    {
        return _D452OutstandingLiability;
    }
    
    // Standard Setter
    public void SetD452OutstandingLiability(decimal value)
    {
        _D452OutstandingLiability = value;
    }
    
    // Get<>AsString()
    public string GetD452OutstandingLiabilityAsString()
    {
        return _D452OutstandingLiability.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD452OutstandingLiabilityAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452OutstandingLiability = parsed;
    }
    
    // Standard Getter
    public string GetD452StockExchIndicator()
    {
        return _D452StockExchIndicator;
    }
    
    // Standard Setter
    public void SetD452StockExchIndicator(string value)
    {
        _D452StockExchIndicator = value;
    }
    
    // Get<>AsString()
    public string GetD452StockExchIndicatorAsString()
    {
        return _D452StockExchIndicator.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD452StockExchIndicatorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D452StockExchIndicator = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D452Key
    public class D452Key
    {
        private static int _size = 25;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D452CalSedol, is_external=, is_static_class=False, static_prefix=
        private D452Key.D452CalSedol _D452CalSedol = new D452Key.D452CalSedol();
        
        
        
        
        // [DEBUG] Field: D452ContractNo, is_external=, is_static_class=False, static_prefix=
        private D452Key.D452ContractNo _D452ContractNo = new D452Key.D452ContractNo();
        
        
        
        
        // [DEBUG] Field: D452RecordCode, is_external=, is_static_class=False, static_prefix=
        private int _D452RecordCode =0;
        
        
        
        
        // [DEBUG] Field: D452RecordCodeX, is_external=, is_static_class=False, static_prefix=
        private D452Key.D452RecordCodeX _D452RecordCodeX = new D452Key.D452RecordCodeX();
        
        
        
        
    public D452Key() {}
    
    public D452Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _D452CalSedol.SetD452CalSedolAsString(data.Substring(offset, D452CalSedol.GetSize()));
        offset += 11;
        _D452ContractNo.SetD452ContractNoAsString(data.Substring(offset, D452ContractNo.GetSize()));
        offset += 10;
        SetD452RecordCode(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        _D452RecordCodeX.SetD452RecordCodeXAsString(data.Substring(offset, D452RecordCodeX.GetSize()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetD452KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D452CalSedol.GetD452CalSedolAsString());
        result.Append(_D452ContractNo.GetD452ContractNoAsString());
        result.Append(_D452RecordCode.ToString().PadLeft(2, '0'));
        result.Append(_D452RecordCodeX.GetD452RecordCodeXAsString());
        
        return result.ToString();
    }
    
    public void SetD452KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 11 <= data.Length)
        {
            _D452CalSedol.SetD452CalSedolAsString(data.Substring(offset, 11));
        }
        else
        {
            _D452CalSedol.SetD452CalSedolAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 10 <= data.Length)
        {
            _D452ContractNo.SetD452ContractNoAsString(data.Substring(offset, 10));
        }
        else
        {
            _D452ContractNo.SetD452ContractNoAsString(data.Substring(offset));
        }
        offset += 10;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD452RecordCode(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            _D452RecordCodeX.SetD452RecordCodeXAsString(data.Substring(offset, 2));
        }
        else
        {
            _D452RecordCodeX.SetD452RecordCodeXAsString(data.Substring(offset));
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public D452CalSedol GetD452CalSedol()
    {
        return _D452CalSedol;
    }
    
    // Standard Setter
    public void SetD452CalSedol(D452CalSedol value)
    {
        _D452CalSedol = value;
    }
    
    // Get<>AsString()
    public string GetD452CalSedolAsString()
    {
        return _D452CalSedol != null ? _D452CalSedol.GetD452CalSedolAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD452CalSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D452CalSedol == null)
        {
            _D452CalSedol = new D452CalSedol();
        }
        _D452CalSedol.SetD452CalSedolAsString(value);
    }
    
    // Standard Getter
    public D452ContractNo GetD452ContractNo()
    {
        return _D452ContractNo;
    }
    
    // Standard Setter
    public void SetD452ContractNo(D452ContractNo value)
    {
        _D452ContractNo = value;
    }
    
    // Get<>AsString()
    public string GetD452ContractNoAsString()
    {
        return _D452ContractNo != null ? _D452ContractNo.GetD452ContractNoAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD452ContractNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D452ContractNo == null)
        {
            _D452ContractNo = new D452ContractNo();
        }
        _D452ContractNo.SetD452ContractNoAsString(value);
    }
    
    // Standard Getter
    public int GetD452RecordCode()
    {
        return _D452RecordCode;
    }
    
    // Standard Setter
    public void SetD452RecordCode(int value)
    {
        _D452RecordCode = value;
    }
    
    // Get<>AsString()
    public string GetD452RecordCodeAsString()
    {
        return _D452RecordCode.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD452RecordCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D452RecordCode = parsed;
    }
    
    // Standard Getter
    public D452RecordCodeX GetD452RecordCodeX()
    {
        return _D452RecordCodeX;
    }
    
    // Standard Setter
    public void SetD452RecordCodeX(D452RecordCodeX value)
    {
        _D452RecordCodeX = value;
    }
    
    // Get<>AsString()
    public string GetD452RecordCodeXAsString()
    {
        return _D452RecordCodeX != null ? _D452RecordCodeX.GetD452RecordCodeXAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD452RecordCodeXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D452RecordCodeX == null)
        {
            _D452RecordCodeX = new D452RecordCodeX();
        }
        _D452RecordCodeX.SetD452RecordCodeXAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D452CalSedol
    public class D452CalSedol
    {
        private static int _size = 11;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D452CoAcLk, is_external=, is_static_class=False, static_prefix=
        private string _D452CoAcLk ="";
        
        
        
        
        // [DEBUG] Field: D452Sedol, is_external=, is_static_class=False, static_prefix=
        private string _D452Sedol ="";
        
        
        
        
    public D452CalSedol() {}
    
    public D452CalSedol(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD452CoAcLk(data.Substring(offset, 4).Trim());
        offset += 4;
        SetD452Sedol(data.Substring(offset, 7).Trim());
        offset += 7;
        
    }
    
    // Serialization methods
    public string GetD452CalSedolAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D452CoAcLk.PadRight(4));
        result.Append(_D452Sedol.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD452CalSedolAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD452CoAcLk(extracted);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetD452Sedol(extracted);
        }
        offset += 7;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD452CoAcLk()
    {
        return _D452CoAcLk;
    }
    
    // Standard Setter
    public void SetD452CoAcLk(string value)
    {
        _D452CoAcLk = value;
    }
    
    // Get<>AsString()
    public string GetD452CoAcLkAsString()
    {
        return _D452CoAcLk.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD452CoAcLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D452CoAcLk = value;
    }
    
    // Standard Getter
    public string GetD452Sedol()
    {
        return _D452Sedol;
    }
    
    // Standard Setter
    public void SetD452Sedol(string value)
    {
        _D452Sedol = value;
    }
    
    // Get<>AsString()
    public string GetD452SedolAsString()
    {
        return _D452Sedol.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetD452SedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D452Sedol = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: D452ContractNo
public class D452ContractNo
{
    private static int _size = 10;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452ContractPrefix, is_external=, is_static_class=False, static_prefix=
    private string _D452ContractPrefix ="";
    
    
    
    
    // [DEBUG] Field: Filler37, is_external=, is_static_class=False, static_prefix=
    private string _Filler37 ="";
    
    
    
    
public D452ContractNo() {}

public D452ContractNo(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452ContractPrefix(data.Substring(offset, 7).Trim());
    offset += 7;
    SetFiller37(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetD452ContractNoAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452ContractPrefix.PadRight(7));
    result.Append(_Filler37.PadRight(3));
    
    return result.ToString();
}

public void SetD452ContractNoAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetD452ContractPrefix(extracted);
    }
    offset += 7;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller37(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetD452ContractPrefix()
{
    return _D452ContractPrefix;
}

// Standard Setter
public void SetD452ContractPrefix(string value)
{
    _D452ContractPrefix = value;
}

// Get<>AsString()
public string GetD452ContractPrefixAsString()
{
    return _D452ContractPrefix.PadRight(7);
}

// Set<>AsString()
public void SetD452ContractPrefixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452ContractPrefix = value;
}

// Standard Getter
public string GetFiller37()
{
    return _Filler37;
}

// Standard Setter
public void SetFiller37(string value)
{
    _Filler37 = value;
}

// Get<>AsString()
public string GetFiller37AsString()
{
    return _Filler37.PadRight(3);
}

// Set<>AsString()
public void SetFiller37AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler37 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D452RecordCodeX
public class D452RecordCodeX
{
    private static int _size = 2;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452RecordCodeX1, is_external=, is_static_class=False, static_prefix=
    private string _D452RecordCodeX1 ="";
    
    
    
    
    // [DEBUG] Field: D452RecordCodeX2, is_external=, is_static_class=False, static_prefix=
    private string _D452RecordCodeX2 ="";
    
    
    
    
public D452RecordCodeX() {}

public D452RecordCodeX(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452RecordCodeX1(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD452RecordCodeX2(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetD452RecordCodeXAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452RecordCodeX1.PadRight(1));
    result.Append(_D452RecordCodeX2.PadRight(1));
    
    return result.ToString();
}

public void SetD452RecordCodeXAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD452RecordCodeX1(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD452RecordCodeX2(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetD452RecordCodeX1()
{
    return _D452RecordCodeX1;
}

// Standard Setter
public void SetD452RecordCodeX1(string value)
{
    _D452RecordCodeX1 = value;
}

// Get<>AsString()
public string GetD452RecordCodeX1AsString()
{
    return _D452RecordCodeX1.PadRight(1);
}

// Set<>AsString()
public void SetD452RecordCodeX1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452RecordCodeX1 = value;
}

// Standard Getter
public string GetD452RecordCodeX2()
{
    return _D452RecordCodeX2;
}

// Standard Setter
public void SetD452RecordCodeX2(string value)
{
    _D452RecordCodeX2 = value;
}

// Get<>AsString()
public string GetD452RecordCodeX2AsString()
{
    return _D452RecordCodeX2.PadRight(1);
}

// Set<>AsString()
public void SetD452RecordCodeX2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452RecordCodeX2 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: D452DateTimeStamp
public class D452DateTimeStamp
{
    private static int _size = 14;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452DateStamp, is_external=, is_static_class=False, static_prefix=
    private string _D452DateStamp ="";
    
    
    
    
    // [DEBUG] Field: D452TimeStamp, is_external=, is_static_class=False, static_prefix=
    private string _D452TimeStamp ="";
    
    
    
    
public D452DateTimeStamp() {}

public D452DateTimeStamp(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452DateStamp(data.Substring(offset, 6).Trim());
    offset += 6;
    SetD452TimeStamp(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetD452DateTimeStampAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452DateStamp.PadRight(6));
    result.Append(_D452TimeStamp.PadRight(8));
    
    return result.ToString();
}

public void SetD452DateTimeStampAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetD452DateStamp(extracted);
    }
    offset += 6;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD452TimeStamp(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetD452DateStamp()
{
    return _D452DateStamp;
}

// Standard Setter
public void SetD452DateStamp(string value)
{
    _D452DateStamp = value;
}

// Get<>AsString()
public string GetD452DateStampAsString()
{
    return _D452DateStamp.PadRight(6);
}

// Set<>AsString()
public void SetD452DateStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452DateStamp = value;
}

// Standard Getter
public string GetD452TimeStamp()
{
    return _D452TimeStamp;
}

// Standard Setter
public void SetD452TimeStamp(string value)
{
    _D452TimeStamp = value;
}

// Get<>AsString()
public string GetD452TimeStampAsString()
{
    return _D452TimeStamp.PadRight(8);
}

// Set<>AsString()
public void SetD452TimeStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452TimeStamp = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler38
public class Filler38
{
    private static int _size = 14;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452Stamp, is_external=, is_static_class=False, static_prefix=
    private string _D452Stamp ="";
    
    
    
    
    // [DEBUG] Field: D452Partly, is_external=, is_static_class=False, static_prefix=
    private string _D452Partly ="";
    
    
    
    
public Filler38() {}

public Filler38(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452Stamp(data.Substring(offset, 13).Trim());
    offset += 13;
    SetD452Partly(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetFiller38AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452Stamp.PadRight(13));
    result.Append(_D452Partly.PadRight(1));
    
    return result.ToString();
}

public void SetFiller38AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetD452Stamp(extracted);
    }
    offset += 13;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD452Partly(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetD452Stamp()
{
    return _D452Stamp;
}

// Standard Setter
public void SetD452Stamp(string value)
{
    _D452Stamp = value;
}

// Get<>AsString()
public string GetD452StampAsString()
{
    return _D452Stamp.PadRight(13);
}

// Set<>AsString()
public void SetD452StampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452Stamp = value;
}

// Standard Getter
public string GetD452Partly()
{
    return _D452Partly;
}

// Standard Setter
public void SetD452Partly(string value)
{
    _D452Partly = value;
}

// Get<>AsString()
public string GetD452PartlyAsString()
{
    return _D452Partly.PadRight(1);
}

// Set<>AsString()
public void SetD452PartlyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452Partly = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D452CalParentSedolCode
public class D452CalParentSedolCode
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452ParentCal, is_external=, is_static_class=False, static_prefix=
    private string _D452ParentCal ="";
    
    
    
    
    // [DEBUG] Field: D452ParentSedolCode, is_external=, is_static_class=False, static_prefix=
    private string _D452ParentSedolCode ="";
    
    
    
    
public D452CalParentSedolCode() {}

public D452CalParentSedolCode(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452ParentCal(data.Substring(offset, 4).Trim());
    offset += 4;
    SetD452ParentSedolCode(data.Substring(offset, 7).Trim());
    offset += 7;
    
}

// Serialization methods
public string GetD452CalParentSedolCodeAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452ParentCal.PadRight(4));
    result.Append(_D452ParentSedolCode.PadRight(7));
    
    return result.ToString();
}

public void SetD452CalParentSedolCodeAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetD452ParentCal(extracted);
    }
    offset += 4;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetD452ParentSedolCode(extracted);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetD452ParentCal()
{
    return _D452ParentCal;
}

// Standard Setter
public void SetD452ParentCal(string value)
{
    _D452ParentCal = value;
}

// Get<>AsString()
public string GetD452ParentCalAsString()
{
    return _D452ParentCal.PadRight(4);
}

// Set<>AsString()
public void SetD452ParentCalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452ParentCal = value;
}

// Standard Getter
public string GetD452ParentSedolCode()
{
    return _D452ParentSedolCode;
}

// Standard Setter
public void SetD452ParentSedolCode(string value)
{
    _D452ParentSedolCode = value;
}

// Get<>AsString()
public string GetD452ParentSedolCodeAsString()
{
    return _D452ParentSedolCode.PadRight(7);
}

// Set<>AsString()
public void SetD452ParentSedolCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452ParentSedolCode = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D452CalPreviousSedolCode
public class D452CalPreviousSedolCode
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452PreviousCal, is_external=, is_static_class=False, static_prefix=
    private string _D452PreviousCal ="";
    
    
    
    
    // [DEBUG] Field: D452PreviousSedolCode, is_external=, is_static_class=False, static_prefix=
    private string _D452PreviousSedolCode ="";
    
    
    
    
public D452CalPreviousSedolCode() {}

public D452CalPreviousSedolCode(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452PreviousCal(data.Substring(offset, 4).Trim());
    offset += 4;
    SetD452PreviousSedolCode(data.Substring(offset, 7).Trim());
    offset += 7;
    
}

// Serialization methods
public string GetD452CalPreviousSedolCodeAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452PreviousCal.PadRight(4));
    result.Append(_D452PreviousSedolCode.PadRight(7));
    
    return result.ToString();
}

public void SetD452CalPreviousSedolCodeAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetD452PreviousCal(extracted);
    }
    offset += 4;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetD452PreviousSedolCode(extracted);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetD452PreviousCal()
{
    return _D452PreviousCal;
}

// Standard Setter
public void SetD452PreviousCal(string value)
{
    _D452PreviousCal = value;
}

// Get<>AsString()
public string GetD452PreviousCalAsString()
{
    return _D452PreviousCal.PadRight(4);
}

// Set<>AsString()
public void SetD452PreviousCalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452PreviousCal = value;
}

// Standard Getter
public string GetD452PreviousSedolCode()
{
    return _D452PreviousSedolCode;
}

// Standard Setter
public void SetD452PreviousSedolCode(string value)
{
    _D452PreviousSedolCode = value;
}

// Get<>AsString()
public string GetD452PreviousSedolCodeAsString()
{
    return _D452PreviousSedolCode.PadRight(7);
}

// Set<>AsString()
public void SetD452PreviousSedolCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452PreviousSedolCode = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D452OriginalBargainNo
public class D452OriginalBargainNo
{
    private static int _size = 10;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452BargainPrefix, is_external=, is_static_class=False, static_prefix=
    private string _D452BargainPrefix ="";
    
    
    
    
    // [DEBUG] Field: Filler39, is_external=, is_static_class=False, static_prefix=
    private string _Filler39 ="";
    
    
    
    
public D452OriginalBargainNo() {}

public D452OriginalBargainNo(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452BargainPrefix(data.Substring(offset, 7).Trim());
    offset += 7;
    SetFiller39(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetD452OriginalBargainNoAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452BargainPrefix.PadRight(7));
    result.Append(_Filler39.PadRight(3));
    
    return result.ToString();
}

public void SetD452OriginalBargainNoAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetD452BargainPrefix(extracted);
    }
    offset += 7;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller39(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetD452BargainPrefix()
{
    return _D452BargainPrefix;
}

// Standard Setter
public void SetD452BargainPrefix(string value)
{
    _D452BargainPrefix = value;
}

// Get<>AsString()
public string GetD452BargainPrefixAsString()
{
    return _D452BargainPrefix.PadRight(7);
}

// Set<>AsString()
public void SetD452BargainPrefixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452BargainPrefix = value;
}

// Standard Getter
public string GetFiller39()
{
    return _Filler39;
}

// Standard Setter
public void SetFiller39(string value)
{
    _Filler39 = value;
}

// Get<>AsString()
public string GetFiller39AsString()
{
    return _Filler39.PadRight(3);
}

// Set<>AsString()
public void SetFiller39AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler39 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D452BargainDate
public class D452BargainDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452BargainDateYymm, is_external=, is_static_class=False, static_prefix=
    private D452BargainDate.D452BargainDateYymm _D452BargainDateYymm = new D452BargainDate.D452BargainDateYymm();
    
    
    
    
    // [DEBUG] Field: D452BargainDateDd, is_external=, is_static_class=False, static_prefix=
    private int _D452BargainDateDd =0;
    
    
    
    
public D452BargainDate() {}

public D452BargainDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D452BargainDateYymm.SetD452BargainDateYymmAsString(data.Substring(offset, D452BargainDateYymm.GetSize()));
    offset += 4;
    SetD452BargainDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD452BargainDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452BargainDateYymm.GetD452BargainDateYymmAsString());
    result.Append(_D452BargainDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD452BargainDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        _D452BargainDateYymm.SetD452BargainDateYymmAsString(data.Substring(offset, 4));
    }
    else
    {
        _D452BargainDateYymm.SetD452BargainDateYymmAsString(data.Substring(offset));
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452BargainDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public D452BargainDateYymm GetD452BargainDateYymm()
{
    return _D452BargainDateYymm;
}

// Standard Setter
public void SetD452BargainDateYymm(D452BargainDateYymm value)
{
    _D452BargainDateYymm = value;
}

// Get<>AsString()
public string GetD452BargainDateYymmAsString()
{
    return _D452BargainDateYymm != null ? _D452BargainDateYymm.GetD452BargainDateYymmAsString() : "";
}

// Set<>AsString()
public void SetD452BargainDateYymmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D452BargainDateYymm == null)
    {
        _D452BargainDateYymm = new D452BargainDateYymm();
    }
    _D452BargainDateYymm.SetD452BargainDateYymmAsString(value);
}

// Standard Getter
public int GetD452BargainDateDd()
{
    return _D452BargainDateDd;
}

// Standard Setter
public void SetD452BargainDateDd(int value)
{
    _D452BargainDateDd = value;
}

// Get<>AsString()
public string GetD452BargainDateDdAsString()
{
    return _D452BargainDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD452BargainDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452BargainDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D452BargainDateYymm
public class D452BargainDateYymm
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452BargainDateYy, is_external=, is_static_class=False, static_prefix=
    private int _D452BargainDateYy =0;
    
    
    
    
    // [DEBUG] Field: D452BargainDateMm, is_external=, is_static_class=False, static_prefix=
    private int _D452BargainDateMm =0;
    
    
    
    
public D452BargainDateYymm() {}

public D452BargainDateYymm(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452BargainDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD452BargainDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD452BargainDateYymmAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452BargainDateYy.ToString().PadLeft(2, '0'));
    result.Append(_D452BargainDateMm.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD452BargainDateYymmAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452BargainDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452BargainDateMm(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetD452BargainDateYy()
{
    return _D452BargainDateYy;
}

// Standard Setter
public void SetD452BargainDateYy(int value)
{
    _D452BargainDateYy = value;
}

// Get<>AsString()
public string GetD452BargainDateYyAsString()
{
    return _D452BargainDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD452BargainDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452BargainDateYy = parsed;
}

// Standard Getter
public int GetD452BargainDateMm()
{
    return _D452BargainDateMm;
}

// Standard Setter
public void SetD452BargainDateMm(int value)
{
    _D452BargainDateMm = value;
}

// Get<>AsString()
public string GetD452BargainDateMmAsString()
{
    return _D452BargainDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD452BargainDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452BargainDateMm = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: D452SettlementDate
public class D452SettlementDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452SettlementDateYy, is_external=, is_static_class=False, static_prefix=
    private int _D452SettlementDateYy =0;
    
    
    
    
    // [DEBUG] Field: D452SettlementDateMm, is_external=, is_static_class=False, static_prefix=
    private int _D452SettlementDateMm =0;
    
    
    
    
    // [DEBUG] Field: D452SettlementDateDd, is_external=, is_static_class=False, static_prefix=
    private int _D452SettlementDateDd =0;
    
    
    
    
public D452SettlementDate() {}

public D452SettlementDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452SettlementDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD452SettlementDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD452SettlementDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD452SettlementDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452SettlementDateYy.ToString().PadLeft(2, '0'));
    result.Append(_D452SettlementDateMm.ToString().PadLeft(2, '0'));
    result.Append(_D452SettlementDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD452SettlementDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452SettlementDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452SettlementDateMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452SettlementDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetD452SettlementDateYy()
{
    return _D452SettlementDateYy;
}

// Standard Setter
public void SetD452SettlementDateYy(int value)
{
    _D452SettlementDateYy = value;
}

// Get<>AsString()
public string GetD452SettlementDateYyAsString()
{
    return _D452SettlementDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD452SettlementDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452SettlementDateYy = parsed;
}

// Standard Getter
public int GetD452SettlementDateMm()
{
    return _D452SettlementDateMm;
}

// Standard Setter
public void SetD452SettlementDateMm(int value)
{
    _D452SettlementDateMm = value;
}

// Get<>AsString()
public string GetD452SettlementDateMmAsString()
{
    return _D452SettlementDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD452SettlementDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452SettlementDateMm = parsed;
}

// Standard Getter
public int GetD452SettlementDateDd()
{
    return _D452SettlementDateDd;
}

// Standard Setter
public void SetD452SettlementDateDd(int value)
{
    _D452SettlementDateDd = value;
}

// Get<>AsString()
public string GetD452SettlementDateDdAsString()
{
    return _D452SettlementDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD452SettlementDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452SettlementDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetYeD45DetailIds(string value)
{
    _YeD45DetailIds.SetYeD45DetailIdsAsString(value);
}
// Nested Class: YeD45DetailIds
public class YeD45DetailIds
{
    private static int _size = 224;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452Ids, is_external=, is_static_class=False, static_prefix=
    private YeD45DetailIds.D452Ids _D452Ids = new YeD45DetailIds.D452Ids();
    
    
    
    
public YeD45DetailIds() {}

public YeD45DetailIds(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D452Ids.SetD452IdsAsString(data.Substring(offset, D452Ids.GetSize()));
    offset += 224;
    
}

// Serialization methods
public string GetYeD45DetailIdsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452Ids.GetD452IdsAsString());
    
    return result.ToString();
}

public void SetYeD45DetailIdsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 224 <= data.Length)
    {
        _D452Ids.SetD452IdsAsString(data.Substring(offset, 224));
    }
    else
    {
        _D452Ids.SetD452IdsAsString(data.Substring(offset));
    }
    offset += 224;
}

// Getter and Setter methods

// Standard Getter
public D452Ids GetD452Ids()
{
    return _D452Ids;
}

// Standard Setter
public void SetD452Ids(D452Ids value)
{
    _D452Ids = value;
}

// Get<>AsString()
public string GetD452IdsAsString()
{
    return _D452Ids != null ? _D452Ids.GetD452IdsAsString() : "";
}

// Set<>AsString()
public void SetD452IdsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D452Ids == null)
    {
        _D452Ids = new D452Ids();
    }
    _D452Ids.SetD452IdsAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D452Ids
public class D452Ids
{
    private static int _size = 224;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452YearEndIds, is_external=, is_static_class=False, static_prefix=
    private D452Ids.D452YearEndIds _D452YearEndIds = new D452Ids.D452YearEndIds();
    
    
    
    
    // [DEBUG] Field: D452BalanceIds, is_external=, is_static_class=False, static_prefix=
    private D452Ids.D452BalanceIds _D452BalanceIds = new D452Ids.D452BalanceIds();
    
    
    
    
    // [DEBUG] Field: D452AcquisitionIds, is_external=, is_static_class=False, static_prefix=
    private D452Ids.D452AcquisitionIds _D452AcquisitionIds = new D452Ids.D452AcquisitionIds();
    
    
    
    
    // [DEBUG] Field: D452DisposalIds, is_external=, is_static_class=False, static_prefix=
    private D452Ids.D452DisposalIds _D452DisposalIds = new D452Ids.D452DisposalIds();
    
    
    
    
public D452Ids() {}

public D452Ids(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D452YearEndIds.SetD452YearEndIdsAsString(data.Substring(offset, D452YearEndIds.GetSize()));
    offset += 56;
    _D452BalanceIds.SetD452BalanceIdsAsString(data.Substring(offset, D452BalanceIds.GetSize()));
    offset += 56;
    _D452AcquisitionIds.SetD452AcquisitionIdsAsString(data.Substring(offset, D452AcquisitionIds.GetSize()));
    offset += 56;
    _D452DisposalIds.SetD452DisposalIdsAsString(data.Substring(offset, D452DisposalIds.GetSize()));
    offset += 56;
    
}

// Serialization methods
public string GetD452IdsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452YearEndIds.GetD452YearEndIdsAsString());
    result.Append(_D452BalanceIds.GetD452BalanceIdsAsString());
    result.Append(_D452AcquisitionIds.GetD452AcquisitionIdsAsString());
    result.Append(_D452DisposalIds.GetD452DisposalIdsAsString());
    
    return result.ToString();
}

public void SetD452IdsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 56 <= data.Length)
    {
        _D452YearEndIds.SetD452YearEndIdsAsString(data.Substring(offset, 56));
    }
    else
    {
        _D452YearEndIds.SetD452YearEndIdsAsString(data.Substring(offset));
    }
    offset += 56;
    if (offset + 56 <= data.Length)
    {
        _D452BalanceIds.SetD452BalanceIdsAsString(data.Substring(offset, 56));
    }
    else
    {
        _D452BalanceIds.SetD452BalanceIdsAsString(data.Substring(offset));
    }
    offset += 56;
    if (offset + 56 <= data.Length)
    {
        _D452AcquisitionIds.SetD452AcquisitionIdsAsString(data.Substring(offset, 56));
    }
    else
    {
        _D452AcquisitionIds.SetD452AcquisitionIdsAsString(data.Substring(offset));
    }
    offset += 56;
    if (offset + 56 <= data.Length)
    {
        _D452DisposalIds.SetD452DisposalIdsAsString(data.Substring(offset, 56));
    }
    else
    {
        _D452DisposalIds.SetD452DisposalIdsAsString(data.Substring(offset));
    }
    offset += 56;
}

// Getter and Setter methods

// Standard Getter
public D452YearEndIds GetD452YearEndIds()
{
    return _D452YearEndIds;
}

// Standard Setter
public void SetD452YearEndIds(D452YearEndIds value)
{
    _D452YearEndIds = value;
}

// Get<>AsString()
public string GetD452YearEndIdsAsString()
{
    return _D452YearEndIds != null ? _D452YearEndIds.GetD452YearEndIdsAsString() : "";
}

// Set<>AsString()
public void SetD452YearEndIdsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D452YearEndIds == null)
    {
        _D452YearEndIds = new D452YearEndIds();
    }
    _D452YearEndIds.SetD452YearEndIdsAsString(value);
}

// Standard Getter
public D452BalanceIds GetD452BalanceIds()
{
    return _D452BalanceIds;
}

// Standard Setter
public void SetD452BalanceIds(D452BalanceIds value)
{
    _D452BalanceIds = value;
}

// Get<>AsString()
public string GetD452BalanceIdsAsString()
{
    return _D452BalanceIds != null ? _D452BalanceIds.GetD452BalanceIdsAsString() : "";
}

// Set<>AsString()
public void SetD452BalanceIdsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D452BalanceIds == null)
    {
        _D452BalanceIds = new D452BalanceIds();
    }
    _D452BalanceIds.SetD452BalanceIdsAsString(value);
}

// Standard Getter
public D452AcquisitionIds GetD452AcquisitionIds()
{
    return _D452AcquisitionIds;
}

// Standard Setter
public void SetD452AcquisitionIds(D452AcquisitionIds value)
{
    _D452AcquisitionIds = value;
}

// Get<>AsString()
public string GetD452AcquisitionIdsAsString()
{
    return _D452AcquisitionIds != null ? _D452AcquisitionIds.GetD452AcquisitionIdsAsString() : "";
}

// Set<>AsString()
public void SetD452AcquisitionIdsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D452AcquisitionIds == null)
    {
        _D452AcquisitionIds = new D452AcquisitionIds();
    }
    _D452AcquisitionIds.SetD452AcquisitionIdsAsString(value);
}

// Standard Getter
public D452DisposalIds GetD452DisposalIds()
{
    return _D452DisposalIds;
}

// Standard Setter
public void SetD452DisposalIds(D452DisposalIds value)
{
    _D452DisposalIds = value;
}

// Get<>AsString()
public string GetD452DisposalIdsAsString()
{
    return _D452DisposalIds != null ? _D452DisposalIds.GetD452DisposalIdsAsString() : "";
}

// Set<>AsString()
public void SetD452DisposalIdsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D452DisposalIds == null)
    {
        _D452DisposalIds = new D452DisposalIds();
    }
    _D452DisposalIds.SetD452DisposalIdsAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D452YearEndIds
public class D452YearEndIds
{
    private static int _size = 56;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler40, is_external=, is_static_class=False, static_prefix=
    private string _Filler40 ="";
    
    
    
    
    // [DEBUG] Field: Filler41, is_external=, is_static_class=False, static_prefix=
    private string _Filler41 ="";
    
    
    
    
    // [DEBUG] Field: Filler42, is_external=, is_static_class=False, static_prefix=
    private string _Filler42 ="";
    
    
    
    
    // [DEBUG] Field: Filler43, is_external=, is_static_class=False, static_prefix=
    private string _Filler43 ="";
    
    
    
    
    // [DEBUG] Field: Filler44, is_external=, is_static_class=False, static_prefix=
    private string _Filler44 ="";
    
    
    
    
    // [DEBUG] Field: Filler45, is_external=, is_static_class=False, static_prefix=
    private string _Filler45 ="";
    
    
    
    
    // [DEBUG] Field: Filler46, is_external=, is_static_class=False, static_prefix=
    private string _Filler46 ="";
    
    
    
    
public D452YearEndIds() {}

public D452YearEndIds(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller40(data.Substring(offset, 8).Trim());
    offset += 8;
    SetFiller41(data.Substring(offset, 8).Trim());
    offset += 8;
    SetFiller42(data.Substring(offset, 8).Trim());
    offset += 8;
    SetFiller43(data.Substring(offset, 8).Trim());
    offset += 8;
    SetFiller44(data.Substring(offset, 8).Trim());
    offset += 8;
    SetFiller45(data.Substring(offset, 8).Trim());
    offset += 8;
    SetFiller46(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetD452YearEndIdsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler40.PadRight(8));
    result.Append(_Filler41.PadRight(8));
    result.Append(_Filler42.PadRight(8));
    result.Append(_Filler43.PadRight(8));
    result.Append(_Filler44.PadRight(8));
    result.Append(_Filler45.PadRight(8));
    result.Append(_Filler46.PadRight(8));
    
    return result.ToString();
}

public void SetD452YearEndIdsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller40(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller41(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller42(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller43(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller44(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller45(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller46(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller40()
{
    return _Filler40;
}

// Standard Setter
public void SetFiller40(string value)
{
    _Filler40 = value;
}

// Get<>AsString()
public string GetFiller40AsString()
{
    return _Filler40.PadRight(8);
}

// Set<>AsString()
public void SetFiller40AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler40 = value;
}

// Standard Getter
public string GetFiller41()
{
    return _Filler41;
}

// Standard Setter
public void SetFiller41(string value)
{
    _Filler41 = value;
}

// Get<>AsString()
public string GetFiller41AsString()
{
    return _Filler41.PadRight(8);
}

// Set<>AsString()
public void SetFiller41AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler41 = value;
}

// Standard Getter
public string GetFiller42()
{
    return _Filler42;
}

// Standard Setter
public void SetFiller42(string value)
{
    _Filler42 = value;
}

// Get<>AsString()
public string GetFiller42AsString()
{
    return _Filler42.PadRight(8);
}

// Set<>AsString()
public void SetFiller42AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler42 = value;
}

// Standard Getter
public string GetFiller43()
{
    return _Filler43;
}

// Standard Setter
public void SetFiller43(string value)
{
    _Filler43 = value;
}

// Get<>AsString()
public string GetFiller43AsString()
{
    return _Filler43.PadRight(8);
}

// Set<>AsString()
public void SetFiller43AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler43 = value;
}

// Standard Getter
public string GetFiller44()
{
    return _Filler44;
}

// Standard Setter
public void SetFiller44(string value)
{
    _Filler44 = value;
}

// Get<>AsString()
public string GetFiller44AsString()
{
    return _Filler44.PadRight(8);
}

// Set<>AsString()
public void SetFiller44AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler44 = value;
}

// Standard Getter
public string GetFiller45()
{
    return _Filler45;
}

// Standard Setter
public void SetFiller45(string value)
{
    _Filler45 = value;
}

// Get<>AsString()
public string GetFiller45AsString()
{
    return _Filler45.PadRight(8);
}

// Set<>AsString()
public void SetFiller45AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler45 = value;
}

// Standard Getter
public string GetFiller46()
{
    return _Filler46;
}

// Standard Setter
public void SetFiller46(string value)
{
    _Filler46 = value;
}

// Get<>AsString()
public string GetFiller46AsString()
{
    return _Filler46.PadRight(8);
}

// Set<>AsString()
public void SetFiller46AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler46 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D452BalanceIds
public class D452BalanceIds
{
    private static int _size = 56;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452BalanceId, is_external=, is_static_class=False, static_prefix=
    private string _D452BalanceId ="";
    
    
    
    
    // [DEBUG] Field: D452HoldingId, is_external=, is_static_class=False, static_prefix=
    private string _D452HoldingId ="";
    
    
    
    
    // [DEBUG] Field: D452ParentStockId, is_external=, is_static_class=False, static_prefix=
    private string _D452ParentStockId ="";
    
    
    
    
    // [DEBUG] Field: Filler47, is_external=, is_static_class=False, static_prefix=
    private string _Filler47 ="";
    
    
    
    
    // [DEBUG] Field: Filler48, is_external=, is_static_class=False, static_prefix=
    private string _Filler48 ="";
    
    
    
    
    // [DEBUG] Field: Filler49, is_external=, is_static_class=False, static_prefix=
    private string _Filler49 ="";
    
    
    
    
    // [DEBUG] Field: Filler50, is_external=, is_static_class=False, static_prefix=
    private string _Filler50 ="";
    
    
    
    
public D452BalanceIds() {}

public D452BalanceIds(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452BalanceId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetD452HoldingId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetD452ParentStockId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetFiller47(data.Substring(offset, 8).Trim());
    offset += 8;
    SetFiller48(data.Substring(offset, 8).Trim());
    offset += 8;
    SetFiller49(data.Substring(offset, 8).Trim());
    offset += 8;
    SetFiller50(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetD452BalanceIdsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452BalanceId.PadRight(8));
    result.Append(_D452HoldingId.PadRight(8));
    result.Append(_D452ParentStockId.PadRight(8));
    result.Append(_Filler47.PadRight(8));
    result.Append(_Filler48.PadRight(8));
    result.Append(_Filler49.PadRight(8));
    result.Append(_Filler50.PadRight(8));
    
    return result.ToString();
}

public void SetD452BalanceIdsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD452BalanceId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD452HoldingId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD452ParentStockId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller47(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller48(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller49(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller50(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetD452BalanceId()
{
    return _D452BalanceId;
}

// Standard Setter
public void SetD452BalanceId(string value)
{
    _D452BalanceId = value;
}

// Get<>AsString()
public string GetD452BalanceIdAsString()
{
    return _D452BalanceId.PadRight(8);
}

// Set<>AsString()
public void SetD452BalanceIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452BalanceId = value;
}

// Standard Getter
public string GetD452HoldingId()
{
    return _D452HoldingId;
}

// Standard Setter
public void SetD452HoldingId(string value)
{
    _D452HoldingId = value;
}

// Get<>AsString()
public string GetD452HoldingIdAsString()
{
    return _D452HoldingId.PadRight(8);
}

// Set<>AsString()
public void SetD452HoldingIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452HoldingId = value;
}

// Standard Getter
public string GetD452ParentStockId()
{
    return _D452ParentStockId;
}

// Standard Setter
public void SetD452ParentStockId(string value)
{
    _D452ParentStockId = value;
}

// Get<>AsString()
public string GetD452ParentStockIdAsString()
{
    return _D452ParentStockId.PadRight(8);
}

// Set<>AsString()
public void SetD452ParentStockIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452ParentStockId = value;
}

// Standard Getter
public string GetFiller47()
{
    return _Filler47;
}

// Standard Setter
public void SetFiller47(string value)
{
    _Filler47 = value;
}

// Get<>AsString()
public string GetFiller47AsString()
{
    return _Filler47.PadRight(8);
}

// Set<>AsString()
public void SetFiller47AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler47 = value;
}

// Standard Getter
public string GetFiller48()
{
    return _Filler48;
}

// Standard Setter
public void SetFiller48(string value)
{
    _Filler48 = value;
}

// Get<>AsString()
public string GetFiller48AsString()
{
    return _Filler48.PadRight(8);
}

// Set<>AsString()
public void SetFiller48AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler48 = value;
}

// Standard Getter
public string GetFiller49()
{
    return _Filler49;
}

// Standard Setter
public void SetFiller49(string value)
{
    _Filler49 = value;
}

// Get<>AsString()
public string GetFiller49AsString()
{
    return _Filler49.PadRight(8);
}

// Set<>AsString()
public void SetFiller49AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler49 = value;
}

// Standard Getter
public string GetFiller50()
{
    return _Filler50;
}

// Standard Setter
public void SetFiller50(string value)
{
    _Filler50 = value;
}

// Get<>AsString()
public string GetFiller50AsString()
{
    return _Filler50.PadRight(8);
}

// Set<>AsString()
public void SetFiller50AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler50 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D452AcquisitionIds
public class D452AcquisitionIds
{
    private static int _size = 56;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452AcquisitionId, is_external=, is_static_class=False, static_prefix=
    private string _D452AcquisitionId ="";
    
    
    
    
    // [DEBUG] Field: D452HoldingId, is_external=, is_static_class=False, static_prefix=
    private string _D452HoldingId ="";
    
    
    
    
    // [DEBUG] Field: D452ParentStockId, is_external=, is_static_class=False, static_prefix=
    private string _D452ParentStockId ="";
    
    
    
    
    // [DEBUG] Field: D452TransactionCategoryId, is_external=, is_static_class=False, static_prefix=
    private string _D452TransactionCategoryId ="";
    
    
    
    
    // [DEBUG] Field: D452CtLinkFundId, is_external=, is_static_class=False, static_prefix=
    private string _D452CtLinkFundId ="";
    
    
    
    
    // [DEBUG] Field: D452PreviousDisposalId, is_external=, is_static_class=False, static_prefix=
    private string _D452PreviousDisposalId ="";
    
    
    
    
    // [DEBUG] Field: D452PreviousAcquisitionId, is_external=, is_static_class=False, static_prefix=
    private string _D452PreviousAcquisitionId ="";
    
    
    
    
public D452AcquisitionIds() {}

public D452AcquisitionIds(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452AcquisitionId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetD452HoldingId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetD452ParentStockId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetD452TransactionCategoryId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetD452CtLinkFundId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetD452PreviousDisposalId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetD452PreviousAcquisitionId(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetD452AcquisitionIdsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452AcquisitionId.PadRight(8));
    result.Append(_D452HoldingId.PadRight(8));
    result.Append(_D452ParentStockId.PadRight(8));
    result.Append(_D452TransactionCategoryId.PadRight(8));
    result.Append(_D452CtLinkFundId.PadRight(8));
    result.Append(_D452PreviousDisposalId.PadRight(8));
    result.Append(_D452PreviousAcquisitionId.PadRight(8));
    
    return result.ToString();
}

public void SetD452AcquisitionIdsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD452AcquisitionId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD452HoldingId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD452ParentStockId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD452TransactionCategoryId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD452CtLinkFundId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD452PreviousDisposalId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD452PreviousAcquisitionId(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetD452AcquisitionId()
{
    return _D452AcquisitionId;
}

// Standard Setter
public void SetD452AcquisitionId(string value)
{
    _D452AcquisitionId = value;
}

// Get<>AsString()
public string GetD452AcquisitionIdAsString()
{
    return _D452AcquisitionId.PadRight(8);
}

// Set<>AsString()
public void SetD452AcquisitionIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452AcquisitionId = value;
}

// Standard Getter
public string GetD452HoldingId()
{
    return _D452HoldingId;
}

// Standard Setter
public void SetD452HoldingId(string value)
{
    _D452HoldingId = value;
}

// Get<>AsString()
public string GetD452HoldingIdAsString()
{
    return _D452HoldingId.PadRight(8);
}

// Set<>AsString()
public void SetD452HoldingIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452HoldingId = value;
}

// Standard Getter
public string GetD452ParentStockId()
{
    return _D452ParentStockId;
}

// Standard Setter
public void SetD452ParentStockId(string value)
{
    _D452ParentStockId = value;
}

// Get<>AsString()
public string GetD452ParentStockIdAsString()
{
    return _D452ParentStockId.PadRight(8);
}

// Set<>AsString()
public void SetD452ParentStockIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452ParentStockId = value;
}

// Standard Getter
public string GetD452TransactionCategoryId()
{
    return _D452TransactionCategoryId;
}

// Standard Setter
public void SetD452TransactionCategoryId(string value)
{
    _D452TransactionCategoryId = value;
}

// Get<>AsString()
public string GetD452TransactionCategoryIdAsString()
{
    return _D452TransactionCategoryId.PadRight(8);
}

// Set<>AsString()
public void SetD452TransactionCategoryIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452TransactionCategoryId = value;
}

// Standard Getter
public string GetD452CtLinkFundId()
{
    return _D452CtLinkFundId;
}

// Standard Setter
public void SetD452CtLinkFundId(string value)
{
    _D452CtLinkFundId = value;
}

// Get<>AsString()
public string GetD452CtLinkFundIdAsString()
{
    return _D452CtLinkFundId.PadRight(8);
}

// Set<>AsString()
public void SetD452CtLinkFundIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452CtLinkFundId = value;
}

// Standard Getter
public string GetD452PreviousDisposalId()
{
    return _D452PreviousDisposalId;
}

// Standard Setter
public void SetD452PreviousDisposalId(string value)
{
    _D452PreviousDisposalId = value;
}

// Get<>AsString()
public string GetD452PreviousDisposalIdAsString()
{
    return _D452PreviousDisposalId.PadRight(8);
}

// Set<>AsString()
public void SetD452PreviousDisposalIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452PreviousDisposalId = value;
}

// Standard Getter
public string GetD452PreviousAcquisitionId()
{
    return _D452PreviousAcquisitionId;
}

// Standard Setter
public void SetD452PreviousAcquisitionId(string value)
{
    _D452PreviousAcquisitionId = value;
}

// Get<>AsString()
public string GetD452PreviousAcquisitionIdAsString()
{
    return _D452PreviousAcquisitionId.PadRight(8);
}

// Set<>AsString()
public void SetD452PreviousAcquisitionIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452PreviousAcquisitionId = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D452DisposalIds
public class D452DisposalIds
{
    private static int _size = 56;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452DisposalId, is_external=, is_static_class=False, static_prefix=
    private string _D452DisposalId ="";
    
    
    
    
    // [DEBUG] Field: D452HoldingId, is_external=, is_static_class=False, static_prefix=
    private string _D452HoldingId ="";
    
    
    
    
    // [DEBUG] Field: D452ParentStockId, is_external=, is_static_class=False, static_prefix=
    private string _D452ParentStockId ="";
    
    
    
    
    // [DEBUG] Field: D452TransactionCategoryId, is_external=, is_static_class=False, static_prefix=
    private string _D452TransactionCategoryId ="";
    
    
    
    
    // [DEBUG] Field: D452CtLinkFundId, is_external=, is_static_class=False, static_prefix=
    private string _D452CtLinkFundId ="";
    
    
    
    
    // [DEBUG] Field: Filler51, is_external=, is_static_class=False, static_prefix=
    private string _Filler51 ="";
    
    
    
    
    // [DEBUG] Field: Filler52, is_external=, is_static_class=False, static_prefix=
    private string _Filler52 ="";
    
    
    
    
public D452DisposalIds() {}

public D452DisposalIds(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452DisposalId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetD452HoldingId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetD452ParentStockId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetD452TransactionCategoryId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetD452CtLinkFundId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetFiller51(data.Substring(offset, 8).Trim());
    offset += 8;
    SetFiller52(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetD452DisposalIdsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452DisposalId.PadRight(8));
    result.Append(_D452HoldingId.PadRight(8));
    result.Append(_D452ParentStockId.PadRight(8));
    result.Append(_D452TransactionCategoryId.PadRight(8));
    result.Append(_D452CtLinkFundId.PadRight(8));
    result.Append(_Filler51.PadRight(8));
    result.Append(_Filler52.PadRight(8));
    
    return result.ToString();
}

public void SetD452DisposalIdsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD452DisposalId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD452HoldingId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD452ParentStockId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD452TransactionCategoryId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD452CtLinkFundId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller51(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetFiller52(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetD452DisposalId()
{
    return _D452DisposalId;
}

// Standard Setter
public void SetD452DisposalId(string value)
{
    _D452DisposalId = value;
}

// Get<>AsString()
public string GetD452DisposalIdAsString()
{
    return _D452DisposalId.PadRight(8);
}

// Set<>AsString()
public void SetD452DisposalIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452DisposalId = value;
}

// Standard Getter
public string GetD452HoldingId()
{
    return _D452HoldingId;
}

// Standard Setter
public void SetD452HoldingId(string value)
{
    _D452HoldingId = value;
}

// Get<>AsString()
public string GetD452HoldingIdAsString()
{
    return _D452HoldingId.PadRight(8);
}

// Set<>AsString()
public void SetD452HoldingIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452HoldingId = value;
}

// Standard Getter
public string GetD452ParentStockId()
{
    return _D452ParentStockId;
}

// Standard Setter
public void SetD452ParentStockId(string value)
{
    _D452ParentStockId = value;
}

// Get<>AsString()
public string GetD452ParentStockIdAsString()
{
    return _D452ParentStockId.PadRight(8);
}

// Set<>AsString()
public void SetD452ParentStockIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452ParentStockId = value;
}

// Standard Getter
public string GetD452TransactionCategoryId()
{
    return _D452TransactionCategoryId;
}

// Standard Setter
public void SetD452TransactionCategoryId(string value)
{
    _D452TransactionCategoryId = value;
}

// Get<>AsString()
public string GetD452TransactionCategoryIdAsString()
{
    return _D452TransactionCategoryId.PadRight(8);
}

// Set<>AsString()
public void SetD452TransactionCategoryIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452TransactionCategoryId = value;
}

// Standard Getter
public string GetD452CtLinkFundId()
{
    return _D452CtLinkFundId;
}

// Standard Setter
public void SetD452CtLinkFundId(string value)
{
    _D452CtLinkFundId = value;
}

// Get<>AsString()
public string GetD452CtLinkFundIdAsString()
{
    return _D452CtLinkFundId.PadRight(8);
}

// Set<>AsString()
public void SetD452CtLinkFundIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452CtLinkFundId = value;
}

// Standard Getter
public string GetFiller51()
{
    return _Filler51;
}

// Standard Setter
public void SetFiller51(string value)
{
    _Filler51 = value;
}

// Get<>AsString()
public string GetFiller51AsString()
{
    return _Filler51.PadRight(8);
}

// Set<>AsString()
public void SetFiller51AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler51 = value;
}

// Standard Getter
public string GetFiller52()
{
    return _Filler52;
}

// Standard Setter
public void SetFiller52(string value)
{
    _Filler52 = value;
}

// Get<>AsString()
public string GetFiller52AsString()
{
    return _Filler52.PadRight(8);
}

// Set<>AsString()
public void SetFiller52AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler52 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}
// Set<>String Override function (Nested)
public void SetYeD45DetailRemainder1(string value)
{
    _YeD45DetailRemainder1.SetYeD45DetailRemainder1AsString(value);
}
// Nested Class: YeD45DetailRemainder1
public class YeD45DetailRemainder1
{
    private static int _size = 32575;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452Remainder, is_external=, is_static_class=False, static_prefix=
    private YeD45DetailRemainder1.D452Remainder _D452Remainder = new YeD45DetailRemainder1.D452Remainder();
    
    
    
    
    // [DEBUG] Field: D452Record02Fields, is_external=, is_static_class=False, static_prefix=
    private YeD45DetailRemainder1.D452Record02Fields _D452Record02Fields = new YeD45DetailRemainder1.D452Record02Fields();
    
    
    
    
    // [DEBUG] Field: D452Record03Fields, is_external=, is_static_class=False, static_prefix=
    private YeD45DetailRemainder1.D452Record03Fields _D452Record03Fields = new YeD45DetailRemainder1.D452Record03Fields();
    
    
    
    
public YeD45DetailRemainder1() {}

public YeD45DetailRemainder1(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D452Remainder.SetD452RemainderAsString(data.Substring(offset, D452Remainder.GetSize()));
    offset += 16132;
    _D452Record02Fields.SetD452Record02FieldsAsString(data.Substring(offset, D452Record02Fields.GetSize()));
    offset += 267;
    _D452Record03Fields.SetD452Record03FieldsAsString(data.Substring(offset, D452Record03Fields.GetSize()));
    offset += 16176;
    
}

// Serialization methods
public string GetYeD45DetailRemainder1AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452Remainder.GetD452RemainderAsString());
    result.Append(_D452Record02Fields.GetD452Record02FieldsAsString());
    result.Append(_D452Record03Fields.GetD452Record03FieldsAsString());
    
    return result.ToString();
}

public void SetYeD45DetailRemainder1AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 16132 <= data.Length)
    {
        _D452Remainder.SetD452RemainderAsString(data.Substring(offset, 16132));
    }
    else
    {
        _D452Remainder.SetD452RemainderAsString(data.Substring(offset));
    }
    offset += 16132;
    if (offset + 267 <= data.Length)
    {
        _D452Record02Fields.SetD452Record02FieldsAsString(data.Substring(offset, 267));
    }
    else
    {
        _D452Record02Fields.SetD452Record02FieldsAsString(data.Substring(offset));
    }
    offset += 267;
    if (offset + 16176 <= data.Length)
    {
        _D452Record03Fields.SetD452Record03FieldsAsString(data.Substring(offset, 16176));
    }
    else
    {
        _D452Record03Fields.SetD452Record03FieldsAsString(data.Substring(offset));
    }
    offset += 16176;
}

// Getter and Setter methods

// Standard Getter
public D452Remainder GetD452Remainder()
{
    return _D452Remainder;
}

// Standard Setter
public void SetD452Remainder(D452Remainder value)
{
    _D452Remainder = value;
}

// Get<>AsString()
public string GetD452RemainderAsString()
{
    return _D452Remainder != null ? _D452Remainder.GetD452RemainderAsString() : "";
}

// Set<>AsString()
public void SetD452RemainderAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D452Remainder == null)
    {
        _D452Remainder = new D452Remainder();
    }
    _D452Remainder.SetD452RemainderAsString(value);
}

// Standard Getter
public D452Record02Fields GetD452Record02Fields()
{
    return _D452Record02Fields;
}

// Standard Setter
public void SetD452Record02Fields(D452Record02Fields value)
{
    _D452Record02Fields = value;
}

// Get<>AsString()
public string GetD452Record02FieldsAsString()
{
    return _D452Record02Fields != null ? _D452Record02Fields.GetD452Record02FieldsAsString() : "";
}

// Set<>AsString()
public void SetD452Record02FieldsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D452Record02Fields == null)
    {
        _D452Record02Fields = new D452Record02Fields();
    }
    _D452Record02Fields.SetD452Record02FieldsAsString(value);
}

// Standard Getter
public D452Record03Fields GetD452Record03Fields()
{
    return _D452Record03Fields;
}

// Standard Setter
public void SetD452Record03Fields(D452Record03Fields value)
{
    _D452Record03Fields = value;
}

// Get<>AsString()
public string GetD452Record03FieldsAsString()
{
    return _D452Record03Fields != null ? _D452Record03Fields.GetD452Record03FieldsAsString() : "";
}

// Set<>AsString()
public void SetD452Record03FieldsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D452Record03Fields == null)
    {
        _D452Record03Fields = new D452Record03Fields();
    }
    _D452Record03Fields.SetD452Record03FieldsAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D452Remainder
public class D452Remainder
{
    private static int _size = 16132;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler53, is_external=, is_static_class=False, static_prefix=
    private string _Filler53 ="";
    
    
    
    
    // [DEBUG] Field: Filler54, is_external=, is_static_class=False, static_prefix=
    private string _Filler54 ="";
    
    
    
    
public D452Remainder() {}

public D452Remainder(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller53(data.Substring(offset, 132).Trim());
    offset += 132;
    SetFiller54(data.Substring(offset, 16000).Trim());
    offset += 16000;
    
}

// Serialization methods
public string GetD452RemainderAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler53.PadRight(132));
    result.Append(_Filler54.PadRight(16000));
    
    return result.ToString();
}

public void SetD452RemainderAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 132 <= data.Length)
    {
        string extracted = data.Substring(offset, 132).Trim();
        SetFiller53(extracted);
    }
    offset += 132;
    if (offset + 16000 <= data.Length)
    {
        string extracted = data.Substring(offset, 16000).Trim();
        SetFiller54(extracted);
    }
    offset += 16000;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller53()
{
    return _Filler53;
}

// Standard Setter
public void SetFiller53(string value)
{
    _Filler53 = value;
}

// Get<>AsString()
public string GetFiller53AsString()
{
    return _Filler53.PadRight(132);
}

// Set<>AsString()
public void SetFiller53AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler53 = value;
}

// Standard Getter
public string GetFiller54()
{
    return _Filler54;
}

// Standard Setter
public void SetFiller54(string value)
{
    _Filler54 = value;
}

// Get<>AsString()
public string GetFiller54AsString()
{
    return _Filler54.PadRight(16000);
}

// Set<>AsString()
public void SetFiller54AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler54 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D452Record02Fields
public class D452Record02Fields
{
    private static int _size = 267;
    
    // Fields in the class
    
    
    // [DEBUG] Field: YeD45DetailAcqBalNonCosts, is_external=, is_static_class=False, static_prefix=
    private D452Record02Fields.YeD45DetailAcqBalNonCosts _YeD45DetailAcqBalNonCosts = new D452Record02Fields.YeD45DetailAcqBalNonCosts();
    
    
    
    
    // [DEBUG] Field: YeD45DetailAcqBalCostTable, is_external=, is_static_class=False, static_prefix=
    private D452Record02Fields.YeD45DetailAcqBalCostTable _YeD45DetailAcqBalCostTable = new D452Record02Fields.YeD45DetailAcqBalCostTable();
    
    
    
    
public D452Record02Fields() {}

public D452Record02Fields(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _YeD45DetailAcqBalNonCosts.SetYeD45DetailAcqBalNonCostsAsString(data.Substring(offset, YeD45DetailAcqBalNonCosts.GetSize()));
    offset += 152;
    _YeD45DetailAcqBalCostTable.SetYeD45DetailAcqBalCostTableAsString(data.Substring(offset, YeD45DetailAcqBalCostTable.GetSize()));
    offset += 115;
    
}

// Serialization methods
public string GetD452Record02FieldsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_YeD45DetailAcqBalNonCosts.GetYeD45DetailAcqBalNonCostsAsString());
    result.Append(_YeD45DetailAcqBalCostTable.GetYeD45DetailAcqBalCostTableAsString());
    
    return result.ToString();
}

public void SetD452Record02FieldsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 152 <= data.Length)
    {
        _YeD45DetailAcqBalNonCosts.SetYeD45DetailAcqBalNonCostsAsString(data.Substring(offset, 152));
    }
    else
    {
        _YeD45DetailAcqBalNonCosts.SetYeD45DetailAcqBalNonCostsAsString(data.Substring(offset));
    }
    offset += 152;
    if (offset + 115 <= data.Length)
    {
        _YeD45DetailAcqBalCostTable.SetYeD45DetailAcqBalCostTableAsString(data.Substring(offset, 115));
    }
    else
    {
        _YeD45DetailAcqBalCostTable.SetYeD45DetailAcqBalCostTableAsString(data.Substring(offset));
    }
    offset += 115;
}

// Getter and Setter methods

// Standard Getter
public YeD45DetailAcqBalNonCosts GetYeD45DetailAcqBalNonCosts()
{
    return _YeD45DetailAcqBalNonCosts;
}

// Standard Setter
public void SetYeD45DetailAcqBalNonCosts(YeD45DetailAcqBalNonCosts value)
{
    _YeD45DetailAcqBalNonCosts = value;
}

// Get<>AsString()
public string GetYeD45DetailAcqBalNonCostsAsString()
{
    return _YeD45DetailAcqBalNonCosts != null ? _YeD45DetailAcqBalNonCosts.GetYeD45DetailAcqBalNonCostsAsString() : "";
}

// Set<>AsString()
public void SetYeD45DetailAcqBalNonCostsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_YeD45DetailAcqBalNonCosts == null)
    {
        _YeD45DetailAcqBalNonCosts = new YeD45DetailAcqBalNonCosts();
    }
    _YeD45DetailAcqBalNonCosts.SetYeD45DetailAcqBalNonCostsAsString(value);
}

// Standard Getter
public YeD45DetailAcqBalCostTable GetYeD45DetailAcqBalCostTable()
{
    return _YeD45DetailAcqBalCostTable;
}

// Standard Setter
public void SetYeD45DetailAcqBalCostTable(YeD45DetailAcqBalCostTable value)
{
    _YeD45DetailAcqBalCostTable = value;
}

// Get<>AsString()
public string GetYeD45DetailAcqBalCostTableAsString()
{
    return _YeD45DetailAcqBalCostTable != null ? _YeD45DetailAcqBalCostTable.GetYeD45DetailAcqBalCostTableAsString() : "";
}

// Set<>AsString()
public void SetYeD45DetailAcqBalCostTableAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_YeD45DetailAcqBalCostTable == null)
    {
        _YeD45DetailAcqBalCostTable = new YeD45DetailAcqBalCostTable();
    }
    _YeD45DetailAcqBalCostTable.SetYeD45DetailAcqBalCostTableAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: YeD45DetailAcqBalNonCosts
public class YeD45DetailAcqBalNonCosts
{
    private static int _size = 152;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452BfTrancheTotalUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _D452BfTrancheTotalUnits =0;
    
    
    
    
    // [DEBUG] Field: D452TrancheTotalUnitsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D452TrancheTotalUnitsYtd =0;
    
    
    
    
    // [DEBUG] Field: D452BfDispUnitsReac, is_external=, is_static_class=False, static_prefix=
    private decimal _D452BfDispUnitsReac =0;
    
    
    
    
    // [DEBUG] Field: D452DispUnitsReacYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D452DispUnitsReacYtd =0;
    
    
    
    
    // [DEBUG] Field: D452UnderwritingCommission, is_external=, is_static_class=False, static_prefix=
    private decimal _D452UnderwritingCommission =0;
    
    
    
    
    // [DEBUG] Field: D452BookCost, is_external=, is_static_class=False, static_prefix=
    private decimal _D452BookCost =0;
    
    
    
    
    // [DEBUG] Field: D452IssueSameClass, is_external=, is_static_class=False, static_prefix=
    private string _D452IssueSameClass ="";
    
    
    
    
    // [DEBUG] Field: D452BfDatePrevOpEvent, is_external=, is_static_class=False, static_prefix=
    private int _D452BfDatePrevOpEvent =0;
    
    
    
    
    // [DEBUG] Field: D452DatePrevOpEventYtd, is_external=, is_static_class=False, static_prefix=
    private YeD45DetailAcqBalNonCosts.D452DatePrevOpEventYtd _D452DatePrevOpEventYtd = new YeD45DetailAcqBalNonCosts.D452DatePrevOpEventYtd();
    
    
    
    
    // [DEBUG] Field: D452FirstDayDealingPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _D452FirstDayDealingPrice =0;
    
    
    
    
    // [DEBUG] Field: D452BfIndexedCostBalance, is_external=, is_static_class=False, static_prefix=
    private decimal _D452BfIndexedCostBalance =0;
    
    
    
    
    // [DEBUG] Field: D452IndexedCostBalanceYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D452IndexedCostBalanceYtd =0;
    
    
    
    
    // [DEBUG] Field: D452UnindexableFlag, is_external=, is_static_class=False, static_prefix=
    private string _D452UnindexableFlag ="";
    
    
    // 88-level condition checks for D452UnindexableFlag
    public bool IsD452UnindexableHolding()
    {
        if (this._D452UnindexableFlag == "'1'") return true;
        return false;
    }
    public bool IsD452Merged1982Pool()
    {
        if (this._D452UnindexableFlag == "'2'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D452GroupTransferFlag, is_external=, is_static_class=False, static_prefix=
    private string _D452GroupTransferFlag ="";
    
    
    // 88-level condition checks for D452GroupTransferFlag
    public bool IsD452GroupTransferBalance()
    {
        if (this._D452GroupTransferFlag == "'1'") return true;
        return false;
    }
    public bool IsD452ReorgBalance()
    {
        if (this._D452GroupTransferFlag == "'2'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D452BfLinkRecordIndic, is_external=, is_static_class=False, static_prefix=
    private int _D452BfLinkRecordIndic =0;
    
    
    
    
    // [DEBUG] Field: D452LinkRecordIndicYtd, is_external=, is_static_class=False, static_prefix=
    private int _D452LinkRecordIndicYtd =0;
    
    
    
    
    // [DEBUG] Field: D452BfIndex85CostBalance, is_external=, is_static_class=False, static_prefix=
    private decimal _D452BfIndex85CostBalance =0;
    
    
    
    
    // [DEBUG] Field: D452Index85CostBalanceYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D452Index85CostBalanceYtd =0;
    
    
    
    
    // [DEBUG] Field: Filler55, is_external=, is_static_class=False, static_prefix=
    private string _Filler55 ="";
    
    
    
    
    // [DEBUG] Field: D452NoOfInitialBookCosts, is_external=, is_static_class=False, static_prefix=
    private int _D452NoOfInitialBookCosts =0;
    
    
    
    
    // [DEBUG] Field: D452BfNoOfCostsHeld, is_external=, is_static_class=False, static_prefix=
    private int _D452BfNoOfCostsHeld =0;
    
    
    
    
    // [DEBUG] Field: D452NoOfCostsHeldYtd, is_external=, is_static_class=False, static_prefix=
    private int _D452NoOfCostsHeldYtd =0;
    
    
    
    
    // [DEBUG] Field: Filler56, is_external=, is_static_class=False, static_prefix=
    private string _Filler56 ="";
    
    
    
    
public YeD45DetailAcqBalNonCosts() {}

public YeD45DetailAcqBalNonCosts(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452BfTrancheTotalUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetD452TrancheTotalUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetD452BfDispUnitsReac(PackedDecimalConverter.ToDecimal(data.Substring(offset, 7)));
    offset += 7;
    SetD452DispUnitsReacYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 7)));
    offset += 7;
    SetD452UnderwritingCommission(PackedDecimalConverter.ToDecimal(data.Substring(offset, 7)));
    offset += 7;
    SetD452BookCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 6)));
    offset += 6;
    SetD452IssueSameClass(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD452BfDatePrevOpEvent(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    _D452DatePrevOpEventYtd.SetD452DatePrevOpEventYtdAsString(data.Substring(offset, D452DatePrevOpEventYtd.GetSize()));
    offset += 6;
    SetD452FirstDayDealingPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 6)));
    offset += 6;
    SetD452BfIndexedCostBalance(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetD452IndexedCostBalanceYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetD452UnindexableFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD452GroupTransferFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD452BfLinkRecordIndic(int.Parse(data.Substring(offset, 1).Trim()));
    offset += 1;
    SetD452LinkRecordIndicYtd(int.Parse(data.Substring(offset, 1).Trim()));
    offset += 1;
    SetD452BfIndex85CostBalance(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetD452Index85CostBalanceYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetFiller55(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD452NoOfInitialBookCosts(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD452BfNoOfCostsHeld(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD452NoOfCostsHeldYtd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetFiller56(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetYeD45DetailAcqBalNonCostsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452BfTrancheTotalUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452TrancheTotalUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452BfDispUnitsReac.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452DispUnitsReacYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452UnderwritingCommission.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452BookCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452IssueSameClass.PadRight(1));
    result.Append(_D452BfDatePrevOpEvent.ToString().PadLeft(6, '0'));
    result.Append(_D452DatePrevOpEventYtd.GetD452DatePrevOpEventYtdAsString());
    result.Append(_D452FirstDayDealingPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452BfIndexedCostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452IndexedCostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452UnindexableFlag.PadRight(1));
    result.Append(_D452GroupTransferFlag.PadRight(1));
    result.Append(_D452BfLinkRecordIndic.ToString().PadLeft(1, '0'));
    result.Append(_D452LinkRecordIndicYtd.ToString().PadLeft(1, '0'));
    result.Append(_D452BfIndex85CostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452Index85CostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler55.PadRight(2));
    result.Append(_D452NoOfInitialBookCosts.ToString().PadLeft(2, '0'));
    result.Append(_D452BfNoOfCostsHeld.ToString().PadLeft(2, '0'));
    result.Append(_D452NoOfCostsHeldYtd.ToString().PadLeft(2, '0'));
    result.Append(_Filler56.PadRight(0));
    
    return result.ToString();
}

public void SetYeD45DetailAcqBalNonCostsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452BfTrancheTotalUnits(parsedDec);
    }
    offset += 13;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452TrancheTotalUnitsYtd(parsedDec);
    }
    offset += 13;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452BfDispUnitsReac(parsedDec);
    }
    offset += 7;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452DispUnitsReacYtd(parsedDec);
    }
    offset += 7;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452UnderwritingCommission(parsedDec);
    }
    offset += 7;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452BookCost(parsedDec);
    }
    offset += 6;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD452IssueSameClass(extracted);
    }
    offset += 1;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452BfDatePrevOpEvent(parsedInt);
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        _D452DatePrevOpEventYtd.SetD452DatePrevOpEventYtdAsString(data.Substring(offset, 6));
    }
    else
    {
        _D452DatePrevOpEventYtd.SetD452DatePrevOpEventYtdAsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452FirstDayDealingPrice(parsedDec);
    }
    offset += 6;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452BfIndexedCostBalance(parsedDec);
    }
    offset += 17;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452IndexedCostBalanceYtd(parsedDec);
    }
    offset += 17;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD452UnindexableFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD452GroupTransferFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452BfLinkRecordIndic(parsedInt);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452LinkRecordIndicYtd(parsedInt);
    }
    offset += 1;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452BfIndex85CostBalance(parsedDec);
    }
    offset += 17;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452Index85CostBalanceYtd(parsedDec);
    }
    offset += 17;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller55(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452NoOfInitialBookCosts(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452BfNoOfCostsHeld(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452NoOfCostsHeldYtd(parsedInt);
    }
    offset += 2;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller56(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public decimal GetD452BfTrancheTotalUnits()
{
    return _D452BfTrancheTotalUnits;
}

// Standard Setter
public void SetD452BfTrancheTotalUnits(decimal value)
{
    _D452BfTrancheTotalUnits = value;
}

// Get<>AsString()
public string GetD452BfTrancheTotalUnitsAsString()
{
    return _D452BfTrancheTotalUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452BfTrancheTotalUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452BfTrancheTotalUnits = parsed;
}

// Standard Getter
public decimal GetD452TrancheTotalUnitsYtd()
{
    return _D452TrancheTotalUnitsYtd;
}

// Standard Setter
public void SetD452TrancheTotalUnitsYtd(decimal value)
{
    _D452TrancheTotalUnitsYtd = value;
}

// Get<>AsString()
public string GetD452TrancheTotalUnitsYtdAsString()
{
    return _D452TrancheTotalUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452TrancheTotalUnitsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452TrancheTotalUnitsYtd = parsed;
}

// Standard Getter
public decimal GetD452BfDispUnitsReac()
{
    return _D452BfDispUnitsReac;
}

// Standard Setter
public void SetD452BfDispUnitsReac(decimal value)
{
    _D452BfDispUnitsReac = value;
}

// Get<>AsString()
public string GetD452BfDispUnitsReacAsString()
{
    return _D452BfDispUnitsReac.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452BfDispUnitsReacAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452BfDispUnitsReac = parsed;
}

// Standard Getter
public decimal GetD452DispUnitsReacYtd()
{
    return _D452DispUnitsReacYtd;
}

// Standard Setter
public void SetD452DispUnitsReacYtd(decimal value)
{
    _D452DispUnitsReacYtd = value;
}

// Get<>AsString()
public string GetD452DispUnitsReacYtdAsString()
{
    return _D452DispUnitsReacYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452DispUnitsReacYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452DispUnitsReacYtd = parsed;
}

// Standard Getter
public decimal GetD452UnderwritingCommission()
{
    return _D452UnderwritingCommission;
}

// Standard Setter
public void SetD452UnderwritingCommission(decimal value)
{
    _D452UnderwritingCommission = value;
}

// Get<>AsString()
public string GetD452UnderwritingCommissionAsString()
{
    return _D452UnderwritingCommission.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452UnderwritingCommissionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452UnderwritingCommission = parsed;
}

// Standard Getter
public decimal GetD452BookCost()
{
    return _D452BookCost;
}

// Standard Setter
public void SetD452BookCost(decimal value)
{
    _D452BookCost = value;
}

// Get<>AsString()
public string GetD452BookCostAsString()
{
    return _D452BookCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452BookCostAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452BookCost = parsed;
}

// Standard Getter
public string GetD452IssueSameClass()
{
    return _D452IssueSameClass;
}

// Standard Setter
public void SetD452IssueSameClass(string value)
{
    _D452IssueSameClass = value;
}

// Get<>AsString()
public string GetD452IssueSameClassAsString()
{
    return _D452IssueSameClass.PadRight(1);
}

// Set<>AsString()
public void SetD452IssueSameClassAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452IssueSameClass = value;
}

// Standard Getter
public int GetD452BfDatePrevOpEvent()
{
    return _D452BfDatePrevOpEvent;
}

// Standard Setter
public void SetD452BfDatePrevOpEvent(int value)
{
    _D452BfDatePrevOpEvent = value;
}

// Get<>AsString()
public string GetD452BfDatePrevOpEventAsString()
{
    return _D452BfDatePrevOpEvent.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetD452BfDatePrevOpEventAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452BfDatePrevOpEvent = parsed;
}

// Standard Getter
public D452DatePrevOpEventYtd GetD452DatePrevOpEventYtd()
{
    return _D452DatePrevOpEventYtd;
}

// Standard Setter
public void SetD452DatePrevOpEventYtd(D452DatePrevOpEventYtd value)
{
    _D452DatePrevOpEventYtd = value;
}

// Get<>AsString()
public string GetD452DatePrevOpEventYtdAsString()
{
    return _D452DatePrevOpEventYtd != null ? _D452DatePrevOpEventYtd.GetD452DatePrevOpEventYtdAsString() : "";
}

// Set<>AsString()
public void SetD452DatePrevOpEventYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D452DatePrevOpEventYtd == null)
    {
        _D452DatePrevOpEventYtd = new D452DatePrevOpEventYtd();
    }
    _D452DatePrevOpEventYtd.SetD452DatePrevOpEventYtdAsString(value);
}

// Standard Getter
public decimal GetD452FirstDayDealingPrice()
{
    return _D452FirstDayDealingPrice;
}

// Standard Setter
public void SetD452FirstDayDealingPrice(decimal value)
{
    _D452FirstDayDealingPrice = value;
}

// Get<>AsString()
public string GetD452FirstDayDealingPriceAsString()
{
    return _D452FirstDayDealingPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452FirstDayDealingPriceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452FirstDayDealingPrice = parsed;
}

// Standard Getter
public decimal GetD452BfIndexedCostBalance()
{
    return _D452BfIndexedCostBalance;
}

// Standard Setter
public void SetD452BfIndexedCostBalance(decimal value)
{
    _D452BfIndexedCostBalance = value;
}

// Get<>AsString()
public string GetD452BfIndexedCostBalanceAsString()
{
    return _D452BfIndexedCostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452BfIndexedCostBalanceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452BfIndexedCostBalance = parsed;
}

// Standard Getter
public decimal GetD452IndexedCostBalanceYtd()
{
    return _D452IndexedCostBalanceYtd;
}

// Standard Setter
public void SetD452IndexedCostBalanceYtd(decimal value)
{
    _D452IndexedCostBalanceYtd = value;
}

// Get<>AsString()
public string GetD452IndexedCostBalanceYtdAsString()
{
    return _D452IndexedCostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452IndexedCostBalanceYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452IndexedCostBalanceYtd = parsed;
}

// Standard Getter
public string GetD452UnindexableFlag()
{
    return _D452UnindexableFlag;
}

// Standard Setter
public void SetD452UnindexableFlag(string value)
{
    _D452UnindexableFlag = value;
}

// Get<>AsString()
public string GetD452UnindexableFlagAsString()
{
    return _D452UnindexableFlag.PadRight(1);
}

// Set<>AsString()
public void SetD452UnindexableFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452UnindexableFlag = value;
}

// Standard Getter
public string GetD452GroupTransferFlag()
{
    return _D452GroupTransferFlag;
}

// Standard Setter
public void SetD452GroupTransferFlag(string value)
{
    _D452GroupTransferFlag = value;
}

// Get<>AsString()
public string GetD452GroupTransferFlagAsString()
{
    return _D452GroupTransferFlag.PadRight(1);
}

// Set<>AsString()
public void SetD452GroupTransferFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452GroupTransferFlag = value;
}

// Standard Getter
public int GetD452BfLinkRecordIndic()
{
    return _D452BfLinkRecordIndic;
}

// Standard Setter
public void SetD452BfLinkRecordIndic(int value)
{
    _D452BfLinkRecordIndic = value;
}

// Get<>AsString()
public string GetD452BfLinkRecordIndicAsString()
{
    return _D452BfLinkRecordIndic.ToString().PadLeft(1, '0');
}

// Set<>AsString()
public void SetD452BfLinkRecordIndicAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452BfLinkRecordIndic = parsed;
}

// Standard Getter
public int GetD452LinkRecordIndicYtd()
{
    return _D452LinkRecordIndicYtd;
}

// Standard Setter
public void SetD452LinkRecordIndicYtd(int value)
{
    _D452LinkRecordIndicYtd = value;
}

// Get<>AsString()
public string GetD452LinkRecordIndicYtdAsString()
{
    return _D452LinkRecordIndicYtd.ToString().PadLeft(1, '0');
}

// Set<>AsString()
public void SetD452LinkRecordIndicYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452LinkRecordIndicYtd = parsed;
}

// Standard Getter
public decimal GetD452BfIndex85CostBalance()
{
    return _D452BfIndex85CostBalance;
}

// Standard Setter
public void SetD452BfIndex85CostBalance(decimal value)
{
    _D452BfIndex85CostBalance = value;
}

// Get<>AsString()
public string GetD452BfIndex85CostBalanceAsString()
{
    return _D452BfIndex85CostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452BfIndex85CostBalanceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452BfIndex85CostBalance = parsed;
}

// Standard Getter
public decimal GetD452Index85CostBalanceYtd()
{
    return _D452Index85CostBalanceYtd;
}

// Standard Setter
public void SetD452Index85CostBalanceYtd(decimal value)
{
    _D452Index85CostBalanceYtd = value;
}

// Get<>AsString()
public string GetD452Index85CostBalanceYtdAsString()
{
    return _D452Index85CostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452Index85CostBalanceYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452Index85CostBalanceYtd = parsed;
}

// Standard Getter
public string GetFiller55()
{
    return _Filler55;
}

// Standard Setter
public void SetFiller55(string value)
{
    _Filler55 = value;
}

// Get<>AsString()
public string GetFiller55AsString()
{
    return _Filler55.PadRight(2);
}

// Set<>AsString()
public void SetFiller55AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler55 = value;
}

// Standard Getter
public int GetD452NoOfInitialBookCosts()
{
    return _D452NoOfInitialBookCosts;
}

// Standard Setter
public void SetD452NoOfInitialBookCosts(int value)
{
    _D452NoOfInitialBookCosts = value;
}

// Get<>AsString()
public string GetD452NoOfInitialBookCostsAsString()
{
    return _D452NoOfInitialBookCosts.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD452NoOfInitialBookCostsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452NoOfInitialBookCosts = parsed;
}

// Standard Getter
public int GetD452BfNoOfCostsHeld()
{
    return _D452BfNoOfCostsHeld;
}

// Standard Setter
public void SetD452BfNoOfCostsHeld(int value)
{
    _D452BfNoOfCostsHeld = value;
}

// Get<>AsString()
public string GetD452BfNoOfCostsHeldAsString()
{
    return _D452BfNoOfCostsHeld.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD452BfNoOfCostsHeldAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452BfNoOfCostsHeld = parsed;
}

// Standard Getter
public int GetD452NoOfCostsHeldYtd()
{
    return _D452NoOfCostsHeldYtd;
}

// Standard Setter
public void SetD452NoOfCostsHeldYtd(int value)
{
    _D452NoOfCostsHeldYtd = value;
}

// Get<>AsString()
public string GetD452NoOfCostsHeldYtdAsString()
{
    return _D452NoOfCostsHeldYtd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD452NoOfCostsHeldYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452NoOfCostsHeldYtd = parsed;
}

// Standard Getter
public string GetFiller56()
{
    return _Filler56;
}

// Standard Setter
public void SetFiller56(string value)
{
    _Filler56 = value;
}

// Get<>AsString()
public string GetFiller56AsString()
{
    return _Filler56.PadRight(0);
}

// Set<>AsString()
public void SetFiller56AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler56 = value;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D452DatePrevOpEventYtd
public class D452DatePrevOpEventYtd
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452YymmPrevOpEventYtd, is_external=, is_static_class=False, static_prefix=
    private int _D452YymmPrevOpEventYtd =0;
    
    
    
    
    // [DEBUG] Field: D452DdPrevOpEventYtd, is_external=, is_static_class=False, static_prefix=
    private int _D452DdPrevOpEventYtd =0;
    
    
    
    
public D452DatePrevOpEventYtd() {}

public D452DatePrevOpEventYtd(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452YymmPrevOpEventYtd(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetD452DdPrevOpEventYtd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD452DatePrevOpEventYtdAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452YymmPrevOpEventYtd.ToString().PadLeft(4, '0'));
    result.Append(_D452DdPrevOpEventYtd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD452DatePrevOpEventYtdAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452YymmPrevOpEventYtd(parsedInt);
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452DdPrevOpEventYtd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetD452YymmPrevOpEventYtd()
{
    return _D452YymmPrevOpEventYtd;
}

// Standard Setter
public void SetD452YymmPrevOpEventYtd(int value)
{
    _D452YymmPrevOpEventYtd = value;
}

// Get<>AsString()
public string GetD452YymmPrevOpEventYtdAsString()
{
    return _D452YymmPrevOpEventYtd.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetD452YymmPrevOpEventYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452YymmPrevOpEventYtd = parsed;
}

// Standard Getter
public int GetD452DdPrevOpEventYtd()
{
    return _D452DdPrevOpEventYtd;
}

// Standard Setter
public void SetD452DdPrevOpEventYtd(int value)
{
    _D452DdPrevOpEventYtd = value;
}

// Get<>AsString()
public string GetD452DdPrevOpEventYtdAsString()
{
    return _D452DdPrevOpEventYtd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD452DdPrevOpEventYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452DdPrevOpEventYtd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: YeD45DetailAcqBalCostTable
public class YeD45DetailAcqBalCostTable
{
    private static int _size = 115;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452CostTable, is_external=, is_static_class=False, static_prefix=
    private YeD45DetailAcqBalCostTable.D452CostTable[] _D452CostTable = new YeD45DetailAcqBalCostTable.D452CostTable[200];
    
    public void InitializeD452CostTableArray()
    {
        for (int i = 0; i < 200; i++)
        {
            _D452CostTable[i] = new YeD45DetailAcqBalCostTable.D452CostTable();
        }
    }
    
    
    
public YeD45DetailAcqBalCostTable() {}

public YeD45DetailAcqBalCostTable(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    InitializeD452CostTableArray();
    for (int i = 0; i < 200; i++)
    {
        _D452CostTable[i].SetD452CostTableAsString(data.Substring(offset, 115));
        offset += 115;
    }
    
}

// Serialization methods
public string GetYeD45DetailAcqBalCostTableAsString()
{
    StringBuilder result = new StringBuilder();
    
    for (int i = 0; i < 200; i++)
    {
        result.Append(_D452CostTable[i].GetD452CostTableAsString());
    }
    
    return result.ToString();
}

public void SetYeD45DetailAcqBalCostTableAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    for (int i = 0; i < 200; i++)
    {
        if (offset + 115 > data.Length) break;
        string val = data.Substring(offset, 115);
        
        _D452CostTable[i].SetD452CostTableAsString(val);
        offset += 115;
    }
}

// Getter and Setter methods

// Array Accessors for D452CostTable
public D452CostTable GetD452CostTableAt(int index)
{
    return _D452CostTable[index];
}

public void SetD452CostTableAt(int index, D452CostTable value)
{
    _D452CostTable[index] = value;
}

// Flattened accessors (index 0)
public D452CostTable GetD452CostTable()
{
    return _D452CostTable != null && _D452CostTable.Length > 0
    ? _D452CostTable[0]
    : new D452CostTable();
}

public void SetD452CostTable(D452CostTable value)
{
    if (_D452CostTable == null || _D452CostTable.Length == 0)
    _D452CostTable = new D452CostTable[1];
    _D452CostTable[0] = value;
}





public static int GetSize()
{
    return _size;
}

// Nested Class: D452CostTable
public class D452CostTable
{
    private static int _size = 115;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452BfBalanceUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _D452BfBalanceUnits =0;
    
    
    
    
    // [DEBUG] Field: D452BalanceUnitsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D452BalanceUnitsYtd =0;
    
    
    
    
    // [DEBUG] Field: D452UnitsPresent, is_external=, is_static_class=False, static_prefix=
    private string _D452UnitsPresent ="";
    
    
    // 88-level condition checks for D452UnitsPresent
    public bool IsD452RealCost()
    {
        if (this._D452UnitsPresent == "'0'") return true;
        if (this._D452UnitsPresent == "'1'") return true;
        if (this._D452UnitsPresent == "'I'") return true;
        if (this._D452UnitsPresent == "'4'") return true;
        return false;
    }
    public bool IsD452CostWithUnits()
    {
        if (this._D452UnitsPresent == "'1'") return true;
        if (this._D452UnitsPresent == "'I'") return true;
        return false;
    }
    public bool IsD452Indexed1982Cost()
    {
        if (this._D452UnitsPresent == "'I'") return true;
        return false;
    }
    public bool IsD452GiltLoss()
    {
        if (this._D452UnitsPresent == "'2'") return true;
        return false;
    }
    public bool IsD452BfGain()
    {
        if (this._D452UnitsPresent == "'3'") return true;
        return false;
    }
    public bool IsD452SmallDistribution()
    {
        if (this._D452UnitsPresent == "'4'") return true;
        return false;
    }
    public bool IsD452BfIndxn()
    {
        if (this._D452UnitsPresent == "'5'") return true;
        return false;
    }
    public bool IsD452OsLiability()
    {
        if (this._D452UnitsPresent == "'6'") return true;
        return false;
    }
    public bool IsD4521982Bdv()
    {
        if (this._D452UnitsPresent == "'7'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D452BfUnindCostBalance, is_external=, is_static_class=False, static_prefix=
    private decimal _D452BfUnindCostBalance =0;
    
    
    
    
    // [DEBUG] Field: D452UnindCostBalanceYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D452UnindCostBalanceYtd =0;
    
    
    
    
    // [DEBUG] Field: D452BfBudgetDayValue, is_external=, is_static_class=False, static_prefix=
    private decimal _D452BfBudgetDayValue =0;
    
    
    
    
    // [DEBUG] Field: D452BudgetDayValueYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D452BudgetDayValueYtd =0;
    
    
    
    
    // [DEBUG] Field: D452Dates, is_external=, is_static_class=False, static_prefix=
    private D452CostTable.D452Dates _D452Dates = new D452CostTable.D452Dates();
    
    
    
    
    // [DEBUG] Field: D132NiNpPiCosts, is_external=, is_static_class=False, static_prefix=
    private string _D132NiNpPiCosts ="";
    
    
    
    
    // [DEBUG] Field: D132TaperDate, is_external=, is_static_class=False, static_prefix=
    private D452CostTable.D132TaperDate _D132TaperDate = new D452CostTable.D132TaperDate();
    
    
    
    
    // [DEBUG] Field: Filler57, is_external=, is_static_class=False, static_prefix=
    private string _Filler57 ="";
    
    
    
    
public D452CostTable() {}

public D452CostTable(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452BfBalanceUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetD452BalanceUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetD452UnitsPresent(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD452BfUnindCostBalance(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetD452UnindCostBalanceYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetD452BfBudgetDayValue(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetD452BudgetDayValueYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    _D452Dates.SetD452DatesAsString(data.Substring(offset, D452Dates.GetSize()));
    offset += 12;
    SetD132NiNpPiCosts(data.Substring(offset, 0).Trim());
    offset += 0;
    _D132TaperDate.SetD132TaperDateAsString(data.Substring(offset, D132TaperDate.GetSize()));
    offset += 0;
    SetFiller57(data.Substring(offset, 12).Trim());
    offset += 12;
    
}

// Serialization methods
public string GetD452CostTableAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452BfBalanceUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452BalanceUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452UnitsPresent.PadRight(1));
    result.Append(_D452BfUnindCostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452UnindCostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452BfBudgetDayValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452BudgetDayValueYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452Dates.GetD452DatesAsString());
    result.Append(_D132NiNpPiCosts.PadRight(0));
    result.Append(_D132TaperDate.GetD132TaperDateAsString());
    result.Append(_Filler57.PadRight(12));
    
    return result.ToString();
}

public void SetD452CostTableAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452BfBalanceUnits(parsedDec);
    }
    offset += 13;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452BalanceUnitsYtd(parsedDec);
    }
    offset += 13;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD452UnitsPresent(extracted);
    }
    offset += 1;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452BfUnindCostBalance(parsedDec);
    }
    offset += 17;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452UnindCostBalanceYtd(parsedDec);
    }
    offset += 17;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452BfBudgetDayValue(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452BudgetDayValueYtd(parsedDec);
    }
    offset += 15;
    if (offset + 12 <= data.Length)
    {
        _D452Dates.SetD452DatesAsString(data.Substring(offset, 12));
    }
    else
    {
        _D452Dates.SetD452DatesAsString(data.Substring(offset));
    }
    offset += 12;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD132NiNpPiCosts(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        _D132TaperDate.SetD132TaperDateAsString(data.Substring(offset, 0));
    }
    else
    {
        _D132TaperDate.SetD132TaperDateAsString(data.Substring(offset));
    }
    offset += 0;
    if (offset + 12 <= data.Length)
    {
        string extracted = data.Substring(offset, 12).Trim();
        SetFiller57(extracted);
    }
    offset += 12;
}

// Getter and Setter methods

// Standard Getter
public decimal GetD452BfBalanceUnits()
{
    return _D452BfBalanceUnits;
}

// Standard Setter
public void SetD452BfBalanceUnits(decimal value)
{
    _D452BfBalanceUnits = value;
}

// Get<>AsString()
public string GetD452BfBalanceUnitsAsString()
{
    return _D452BfBalanceUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452BfBalanceUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452BfBalanceUnits = parsed;
}

// Standard Getter
public decimal GetD452BalanceUnitsYtd()
{
    return _D452BalanceUnitsYtd;
}

// Standard Setter
public void SetD452BalanceUnitsYtd(decimal value)
{
    _D452BalanceUnitsYtd = value;
}

// Get<>AsString()
public string GetD452BalanceUnitsYtdAsString()
{
    return _D452BalanceUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452BalanceUnitsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452BalanceUnitsYtd = parsed;
}

// Standard Getter
public string GetD452UnitsPresent()
{
    return _D452UnitsPresent;
}

// Standard Setter
public void SetD452UnitsPresent(string value)
{
    _D452UnitsPresent = value;
}

// Get<>AsString()
public string GetD452UnitsPresentAsString()
{
    return _D452UnitsPresent.PadRight(1);
}

// Set<>AsString()
public void SetD452UnitsPresentAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452UnitsPresent = value;
}

// Standard Getter
public decimal GetD452BfUnindCostBalance()
{
    return _D452BfUnindCostBalance;
}

// Standard Setter
public void SetD452BfUnindCostBalance(decimal value)
{
    _D452BfUnindCostBalance = value;
}

// Get<>AsString()
public string GetD452BfUnindCostBalanceAsString()
{
    return _D452BfUnindCostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452BfUnindCostBalanceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452BfUnindCostBalance = parsed;
}

// Standard Getter
public decimal GetD452UnindCostBalanceYtd()
{
    return _D452UnindCostBalanceYtd;
}

// Standard Setter
public void SetD452UnindCostBalanceYtd(decimal value)
{
    _D452UnindCostBalanceYtd = value;
}

// Get<>AsString()
public string GetD452UnindCostBalanceYtdAsString()
{
    return _D452UnindCostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452UnindCostBalanceYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452UnindCostBalanceYtd = parsed;
}

// Standard Getter
public decimal GetD452BfBudgetDayValue()
{
    return _D452BfBudgetDayValue;
}

// Standard Setter
public void SetD452BfBudgetDayValue(decimal value)
{
    _D452BfBudgetDayValue = value;
}

// Get<>AsString()
public string GetD452BfBudgetDayValueAsString()
{
    return _D452BfBudgetDayValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452BfBudgetDayValueAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452BfBudgetDayValue = parsed;
}

// Standard Getter
public decimal GetD452BudgetDayValueYtd()
{
    return _D452BudgetDayValueYtd;
}

// Standard Setter
public void SetD452BudgetDayValueYtd(decimal value)
{
    _D452BudgetDayValueYtd = value;
}

// Get<>AsString()
public string GetD452BudgetDayValueYtdAsString()
{
    return _D452BudgetDayValueYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452BudgetDayValueYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452BudgetDayValueYtd = parsed;
}

// Standard Getter
public D452Dates GetD452Dates()
{
    return _D452Dates;
}

// Standard Setter
public void SetD452Dates(D452Dates value)
{
    _D452Dates = value;
}

// Get<>AsString()
public string GetD452DatesAsString()
{
    return _D452Dates != null ? _D452Dates.GetD452DatesAsString() : "";
}

// Set<>AsString()
public void SetD452DatesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D452Dates == null)
    {
        _D452Dates = new D452Dates();
    }
    _D452Dates.SetD452DatesAsString(value);
}

// Standard Getter
public string GetD132NiNpPiCosts()
{
    return _D132NiNpPiCosts;
}

// Standard Setter
public void SetD132NiNpPiCosts(string value)
{
    _D132NiNpPiCosts = value;
}

// Get<>AsString()
public string GetD132NiNpPiCostsAsString()
{
    return _D132NiNpPiCosts.PadRight(0);
}

// Set<>AsString()
public void SetD132NiNpPiCostsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132NiNpPiCosts = value;
}

// Standard Getter
public D132TaperDate GetD132TaperDate()
{
    return _D132TaperDate;
}

// Standard Setter
public void SetD132TaperDate(D132TaperDate value)
{
    _D132TaperDate = value;
}

// Get<>AsString()
public string GetD132TaperDateAsString()
{
    return _D132TaperDate != null ? _D132TaperDate.GetD132TaperDateAsString() : "";
}

// Set<>AsString()
public void SetD132TaperDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D132TaperDate == null)
    {
        _D132TaperDate = new D132TaperDate();
    }
    _D132TaperDate.SetD132TaperDateAsString(value);
}

// Standard Getter
public string GetFiller57()
{
    return _Filler57;
}

// Standard Setter
public void SetFiller57(string value)
{
    _Filler57 = value;
}

// Get<>AsString()
public string GetFiller57AsString()
{
    return _Filler57.PadRight(12);
}

// Set<>AsString()
public void SetFiller57AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler57 = value;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D452Dates
public class D452Dates
{
    private static int _size = 12;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452IndexDate, is_external=, is_static_class=False, static_prefix=
    private D452Dates.D452IndexDate _D452IndexDate = new D452Dates.D452IndexDate();
    
    
    
    
    // [DEBUG] Field: D452CostDate, is_external=, is_static_class=False, static_prefix=
    private D452Dates.D452CostDate _D452CostDate = new D452Dates.D452CostDate();
    
    
    
    
public D452Dates() {}

public D452Dates(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D452IndexDate.SetD452IndexDateAsString(data.Substring(offset, D452IndexDate.GetSize()));
    offset += 6;
    _D452CostDate.SetD452CostDateAsString(data.Substring(offset, D452CostDate.GetSize()));
    offset += 6;
    
}

// Serialization methods
public string GetD452DatesAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452IndexDate.GetD452IndexDateAsString());
    result.Append(_D452CostDate.GetD452CostDateAsString());
    
    return result.ToString();
}

public void SetD452DatesAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        _D452IndexDate.SetD452IndexDateAsString(data.Substring(offset, 6));
    }
    else
    {
        _D452IndexDate.SetD452IndexDateAsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        _D452CostDate.SetD452CostDateAsString(data.Substring(offset, 6));
    }
    else
    {
        _D452CostDate.SetD452CostDateAsString(data.Substring(offset));
    }
    offset += 6;
}

// Getter and Setter methods

// Standard Getter
public D452IndexDate GetD452IndexDate()
{
    return _D452IndexDate;
}

// Standard Setter
public void SetD452IndexDate(D452IndexDate value)
{
    _D452IndexDate = value;
}

// Get<>AsString()
public string GetD452IndexDateAsString()
{
    return _D452IndexDate != null ? _D452IndexDate.GetD452IndexDateAsString() : "";
}

// Set<>AsString()
public void SetD452IndexDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D452IndexDate == null)
    {
        _D452IndexDate = new D452IndexDate();
    }
    _D452IndexDate.SetD452IndexDateAsString(value);
}

// Standard Getter
public D452CostDate GetD452CostDate()
{
    return _D452CostDate;
}

// Standard Setter
public void SetD452CostDate(D452CostDate value)
{
    _D452CostDate = value;
}

// Get<>AsString()
public string GetD452CostDateAsString()
{
    return _D452CostDate != null ? _D452CostDate.GetD452CostDateAsString() : "";
}

// Set<>AsString()
public void SetD452CostDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D452CostDate == null)
    {
        _D452CostDate = new D452CostDate();
    }
    _D452CostDate.SetD452CostDateAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D452IndexDate
public class D452IndexDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452IndexDateYymm, is_external=, is_static_class=False, static_prefix=
    private D452IndexDate.D452IndexDateYymm _D452IndexDateYymm = new D452IndexDate.D452IndexDateYymm();
    
    
    
    
public D452IndexDate() {}

public D452IndexDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D452IndexDateYymm.SetD452IndexDateYymmAsString(data.Substring(offset, D452IndexDateYymm.GetSize()));
    offset += 6;
    
}

// Serialization methods
public string GetD452IndexDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452IndexDateYymm.GetD452IndexDateYymmAsString());
    
    return result.ToString();
}

public void SetD452IndexDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        _D452IndexDateYymm.SetD452IndexDateYymmAsString(data.Substring(offset, 6));
    }
    else
    {
        _D452IndexDateYymm.SetD452IndexDateYymmAsString(data.Substring(offset));
    }
    offset += 6;
}

// Getter and Setter methods

// Standard Getter
public D452IndexDateYymm GetD452IndexDateYymm()
{
    return _D452IndexDateYymm;
}

// Standard Setter
public void SetD452IndexDateYymm(D452IndexDateYymm value)
{
    _D452IndexDateYymm = value;
}

// Get<>AsString()
public string GetD452IndexDateYymmAsString()
{
    return _D452IndexDateYymm != null ? _D452IndexDateYymm.GetD452IndexDateYymmAsString() : "";
}

// Set<>AsString()
public void SetD452IndexDateYymmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D452IndexDateYymm == null)
    {
        _D452IndexDateYymm = new D452IndexDateYymm();
    }
    _D452IndexDateYymm.SetD452IndexDateYymmAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D452IndexDateYymm
public class D452IndexDateYymm
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452IndexDateYy, is_external=, is_static_class=False, static_prefix=
    private int _D452IndexDateYy =0;
    
    
    
    
    // [DEBUG] Field: D452IndexDateMm, is_external=, is_static_class=False, static_prefix=
    private int _D452IndexDateMm =0;
    
    
    
    
    // [DEBUG] Field: D452IndexDateDd, is_external=, is_static_class=False, static_prefix=
    private int _D452IndexDateDd =0;
    
    
    
    
public D452IndexDateYymm() {}

public D452IndexDateYymm(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452IndexDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD452IndexDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD452IndexDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD452IndexDateYymmAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452IndexDateYy.ToString().PadLeft(2, '0'));
    result.Append(_D452IndexDateMm.ToString().PadLeft(2, '0'));
    result.Append(_D452IndexDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD452IndexDateYymmAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452IndexDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452IndexDateMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452IndexDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetD452IndexDateYy()
{
    return _D452IndexDateYy;
}

// Standard Setter
public void SetD452IndexDateYy(int value)
{
    _D452IndexDateYy = value;
}

// Get<>AsString()
public string GetD452IndexDateYyAsString()
{
    return _D452IndexDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD452IndexDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452IndexDateYy = parsed;
}

// Standard Getter
public int GetD452IndexDateMm()
{
    return _D452IndexDateMm;
}

// Standard Setter
public void SetD452IndexDateMm(int value)
{
    _D452IndexDateMm = value;
}

// Get<>AsString()
public string GetD452IndexDateMmAsString()
{
    return _D452IndexDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD452IndexDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452IndexDateMm = parsed;
}

// Standard Getter
public int GetD452IndexDateDd()
{
    return _D452IndexDateDd;
}

// Standard Setter
public void SetD452IndexDateDd(int value)
{
    _D452IndexDateDd = value;
}

// Get<>AsString()
public string GetD452IndexDateDdAsString()
{
    return _D452IndexDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD452IndexDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452IndexDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: D452CostDate
public class D452CostDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452CostDateYymm, is_external=, is_static_class=False, static_prefix=
    private D452CostDate.D452CostDateYymm _D452CostDateYymm = new D452CostDate.D452CostDateYymm();
    
    
    
    
    // [DEBUG] Field: D452CostDateDd, is_external=, is_static_class=False, static_prefix=
    private int _D452CostDateDd =0;
    
    
    
    
public D452CostDate() {}

public D452CostDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D452CostDateYymm.SetD452CostDateYymmAsString(data.Substring(offset, D452CostDateYymm.GetSize()));
    offset += 4;
    SetD452CostDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD452CostDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452CostDateYymm.GetD452CostDateYymmAsString());
    result.Append(_D452CostDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD452CostDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        _D452CostDateYymm.SetD452CostDateYymmAsString(data.Substring(offset, 4));
    }
    else
    {
        _D452CostDateYymm.SetD452CostDateYymmAsString(data.Substring(offset));
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452CostDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public D452CostDateYymm GetD452CostDateYymm()
{
    return _D452CostDateYymm;
}

// Standard Setter
public void SetD452CostDateYymm(D452CostDateYymm value)
{
    _D452CostDateYymm = value;
}

// Get<>AsString()
public string GetD452CostDateYymmAsString()
{
    return _D452CostDateYymm != null ? _D452CostDateYymm.GetD452CostDateYymmAsString() : "";
}

// Set<>AsString()
public void SetD452CostDateYymmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D452CostDateYymm == null)
    {
        _D452CostDateYymm = new D452CostDateYymm();
    }
    _D452CostDateYymm.SetD452CostDateYymmAsString(value);
}

// Standard Getter
public int GetD452CostDateDd()
{
    return _D452CostDateDd;
}

// Standard Setter
public void SetD452CostDateDd(int value)
{
    _D452CostDateDd = value;
}

// Get<>AsString()
public string GetD452CostDateDdAsString()
{
    return _D452CostDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD452CostDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452CostDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D452CostDateYymm
public class D452CostDateYymm
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452CostDateYy, is_external=, is_static_class=False, static_prefix=
    private int _D452CostDateYy =0;
    
    
    
    
    // [DEBUG] Field: D452CostDateMm, is_external=, is_static_class=False, static_prefix=
    private int _D452CostDateMm =0;
    
    
    
    
public D452CostDateYymm() {}

public D452CostDateYymm(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452CostDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetD452CostDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetD452CostDateYymmAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452CostDateYy.ToString().PadLeft(2, '0'));
    result.Append(_D452CostDateMm.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetD452CostDateYymmAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452CostDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD452CostDateMm(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetD452CostDateYy()
{
    return _D452CostDateYy;
}

// Standard Setter
public void SetD452CostDateYy(int value)
{
    _D452CostDateYy = value;
}

// Get<>AsString()
public string GetD452CostDateYyAsString()
{
    return _D452CostDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD452CostDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452CostDateYy = parsed;
}

// Standard Getter
public int GetD452CostDateMm()
{
    return _D452CostDateMm;
}

// Standard Setter
public void SetD452CostDateMm(int value)
{
    _D452CostDateMm = value;
}

// Get<>AsString()
public string GetD452CostDateMmAsString()
{
    return _D452CostDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetD452CostDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D452CostDateMm = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
}
// Nested Class: D132TaperDate
public class D132TaperDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D132TaperDateYy, is_external=, is_static_class=False, static_prefix=
    private string _D132TaperDateYy ="";
    
    
    
    
    // [DEBUG] Field: D132TaperDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D132TaperDateMm ="";
    
    
    
    
    // [DEBUG] Field: D132TaperDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D132TaperDateDd ="";
    
    
    
    
public D132TaperDate() {}

public D132TaperDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD132TaperDateYy(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD132TaperDateMm(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD132TaperDateDd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD132TaperDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D132TaperDateYy.PadRight(0));
    result.Append(_D132TaperDateMm.PadRight(0));
    result.Append(_D132TaperDateDd.PadRight(0));
    
    return result.ToString();
}

public void SetD132TaperDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD132TaperDateYy(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD132TaperDateMm(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD132TaperDateDd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD132TaperDateYy()
{
    return _D132TaperDateYy;
}

// Standard Setter
public void SetD132TaperDateYy(string value)
{
    _D132TaperDateYy = value;
}

// Get<>AsString()
public string GetD132TaperDateYyAsString()
{
    return _D132TaperDateYy.PadRight(0);
}

// Set<>AsString()
public void SetD132TaperDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132TaperDateYy = value;
}

// Standard Getter
public string GetD132TaperDateMm()
{
    return _D132TaperDateMm;
}

// Standard Setter
public void SetD132TaperDateMm(string value)
{
    _D132TaperDateMm = value;
}

// Get<>AsString()
public string GetD132TaperDateMmAsString()
{
    return _D132TaperDateMm.PadRight(0);
}

// Set<>AsString()
public void SetD132TaperDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132TaperDateMm = value;
}

// Standard Getter
public string GetD132TaperDateDd()
{
    return _D132TaperDateDd;
}

// Standard Setter
public void SetD132TaperDateDd(string value)
{
    _D132TaperDateDd = value;
}

// Get<>AsString()
public string GetD132TaperDateDdAsString()
{
    return _D132TaperDateDd.PadRight(0);
}

// Set<>AsString()
public void SetD132TaperDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D132TaperDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}
}
// Nested Class: D452Record03Fields
public class D452Record03Fields
{
    private static int _size = 16176;
    
    // Fields in the class
    
    
    // [DEBUG] Field: YeD45DetailDisposalFields, is_external=, is_static_class=False, static_prefix=
    private D452Record03Fields.YeD45DetailDisposalFields _YeD45DetailDisposalFields = new D452Record03Fields.YeD45DetailDisposalFields();
    
    
    
    
    // [DEBUG] Field: YeD45DetailDisposalUnused, is_external=, is_static_class=False, static_prefix=
    private D452Record03Fields.YeD45DetailDisposalUnused _YeD45DetailDisposalUnused = new D452Record03Fields.YeD45DetailDisposalUnused();
    
    
    
    
public D452Record03Fields() {}

public D452Record03Fields(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _YeD45DetailDisposalFields.SetYeD45DetailDisposalFieldsAsString(data.Substring(offset, YeD45DetailDisposalFields.GetSize()));
    offset += 153;
    _YeD45DetailDisposalUnused.SetYeD45DetailDisposalUnusedAsString(data.Substring(offset, YeD45DetailDisposalUnused.GetSize()));
    offset += 16023;
    
}

// Serialization methods
public string GetD452Record03FieldsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_YeD45DetailDisposalFields.GetYeD45DetailDisposalFieldsAsString());
    result.Append(_YeD45DetailDisposalUnused.GetYeD45DetailDisposalUnusedAsString());
    
    return result.ToString();
}

public void SetD452Record03FieldsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 153 <= data.Length)
    {
        _YeD45DetailDisposalFields.SetYeD45DetailDisposalFieldsAsString(data.Substring(offset, 153));
    }
    else
    {
        _YeD45DetailDisposalFields.SetYeD45DetailDisposalFieldsAsString(data.Substring(offset));
    }
    offset += 153;
    if (offset + 16023 <= data.Length)
    {
        _YeD45DetailDisposalUnused.SetYeD45DetailDisposalUnusedAsString(data.Substring(offset, 16023));
    }
    else
    {
        _YeD45DetailDisposalUnused.SetYeD45DetailDisposalUnusedAsString(data.Substring(offset));
    }
    offset += 16023;
}

// Getter and Setter methods

// Standard Getter
public YeD45DetailDisposalFields GetYeD45DetailDisposalFields()
{
    return _YeD45DetailDisposalFields;
}

// Standard Setter
public void SetYeD45DetailDisposalFields(YeD45DetailDisposalFields value)
{
    _YeD45DetailDisposalFields = value;
}

// Get<>AsString()
public string GetYeD45DetailDisposalFieldsAsString()
{
    return _YeD45DetailDisposalFields != null ? _YeD45DetailDisposalFields.GetYeD45DetailDisposalFieldsAsString() : "";
}

// Set<>AsString()
public void SetYeD45DetailDisposalFieldsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_YeD45DetailDisposalFields == null)
    {
        _YeD45DetailDisposalFields = new YeD45DetailDisposalFields();
    }
    _YeD45DetailDisposalFields.SetYeD45DetailDisposalFieldsAsString(value);
}

// Standard Getter
public YeD45DetailDisposalUnused GetYeD45DetailDisposalUnused()
{
    return _YeD45DetailDisposalUnused;
}

// Standard Setter
public void SetYeD45DetailDisposalUnused(YeD45DetailDisposalUnused value)
{
    _YeD45DetailDisposalUnused = value;
}

// Get<>AsString()
public string GetYeD45DetailDisposalUnusedAsString()
{
    return _YeD45DetailDisposalUnused != null ? _YeD45DetailDisposalUnused.GetYeD45DetailDisposalUnusedAsString() : "";
}

// Set<>AsString()
public void SetYeD45DetailDisposalUnusedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_YeD45DetailDisposalUnused == null)
    {
        _YeD45DetailDisposalUnused = new YeD45DetailDisposalUnused();
    }
    _YeD45DetailDisposalUnused.SetYeD45DetailDisposalUnusedAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: YeD45DetailDisposalFields
public class YeD45DetailDisposalFields
{
    private static int _size = 153;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D452NumberOfUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _D452NumberOfUnits =0;
    
    
    
    
    // [DEBUG] Field: D452Proceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _D452Proceeds =0;
    
    
    
    
    // [DEBUG] Field: D452CapitalGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D452CapitalGainLoss =0;
    
    
    
    
    // [DEBUG] Field: D452Force2PctMatchFlag, is_external=, is_static_class=False, static_prefix=
    private string _D452Force2PctMatchFlag ="";
    
    
    
    
    // [DEBUG] Field: D452CgtCost, is_external=, is_static_class=False, static_prefix=
    private decimal _D452CgtCost =0;
    
    
    
    
    // [DEBUG] Field: D452ProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D452ProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: Filler58, is_external=, is_static_class=False, static_prefix=
    private string _Filler58 ="";
    
    
    
    
    // [DEBUG] Field: D452ConsolidatedFlag, is_external=, is_static_class=False, static_prefix=
    private string _D452ConsolidatedFlag ="";
    
    
    
    
    // [DEBUG] Field: D452StoreUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _D452StoreUnits =0;
    
    
    
    
    // [DEBUG] Field: D452StoreProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _D452StoreProceeds =0;
    
    
    
    
    // [DEBUG] Field: D452ConsolidateKey, is_external=, is_static_class=False, static_prefix=
    private string _D452ConsolidateKey ="";
    
    
    
    
    // [DEBUG] Field: D452Fa2003Exemption, is_external=, is_static_class=False, static_prefix=
    private string _D452Fa2003Exemption ="";
    
    
    
    
    // [DEBUG] Field: D452GtProRataMatch, is_external=, is_static_class=False, static_prefix=
    private string _D452GtProRataMatch ="";
    
    
    
    
    // [DEBUG] Field: D452GtUseOriginalDates, is_external=, is_static_class=False, static_prefix=
    private string _D452GtUseOriginalDates ="";
    
    
    
    
    // [DEBUG] Field: D452NumberOfUnitsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D452NumberOfUnitsYtd =0;
    
    
    
    
    // [DEBUG] Field: D452ProceedsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D452ProceedsYtd =0;
    
    
    
    
public YeD45DetailDisposalFields() {}

public YeD45DetailDisposalFields(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD452NumberOfUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetD452Proceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetD452CapitalGainLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetD452Force2PctMatchFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD452CgtCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetD452ProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetFiller58(data.Substring(offset, 9).Trim());
    offset += 9;
    SetD452ConsolidatedFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD452StoreUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetD452StoreProceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetD452ConsolidateKey(data.Substring(offset, 23).Trim());
    offset += 23;
    SetD452Fa2003Exemption(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD452GtProRataMatch(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD452GtUseOriginalDates(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD452NumberOfUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 9)));
    offset += 9;
    SetD452ProceedsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 9)));
    offset += 9;
    
}

// Serialization methods
public string GetYeD45DetailDisposalFieldsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D452NumberOfUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452Proceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452CapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452Force2PctMatchFlag.PadRight(1));
    result.Append(_D452CgtCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452ProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler58.PadRight(9));
    result.Append(_D452ConsolidatedFlag.PadRight(1));
    result.Append(_D452StoreUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452StoreProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452ConsolidateKey.PadRight(23));
    result.Append(_D452Fa2003Exemption.PadRight(0));
    result.Append(_D452GtProRataMatch.PadRight(0));
    result.Append(_D452GtUseOriginalDates.PadRight(0));
    result.Append(_D452NumberOfUnitsYtd.ToString("F3", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_D452ProceedsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetYeD45DetailDisposalFieldsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452NumberOfUnits(parsedDec);
    }
    offset += 13;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452Proceeds(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452CapitalGainLoss(parsedDec);
    }
    offset += 15;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD452Force2PctMatchFlag(extracted);
    }
    offset += 1;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452CgtCost(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452ProfitLoss(parsedDec);
    }
    offset += 15;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        SetFiller58(extracted);
    }
    offset += 9;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD452ConsolidatedFlag(extracted);
    }
    offset += 1;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452StoreUnits(parsedDec);
    }
    offset += 13;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452StoreProceeds(parsedDec);
    }
    offset += 15;
    if (offset + 23 <= data.Length)
    {
        string extracted = data.Substring(offset, 23).Trim();
        SetD452ConsolidateKey(extracted);
    }
    offset += 23;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD452Fa2003Exemption(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD452GtProRataMatch(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD452GtUseOriginalDates(extracted);
    }
    offset += 0;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452NumberOfUnitsYtd(parsedDec);
    }
    offset += 9;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetD452ProceedsYtd(parsedDec);
    }
    offset += 9;
}

// Getter and Setter methods

// Standard Getter
public decimal GetD452NumberOfUnits()
{
    return _D452NumberOfUnits;
}

// Standard Setter
public void SetD452NumberOfUnits(decimal value)
{
    _D452NumberOfUnits = value;
}

// Get<>AsString()
public string GetD452NumberOfUnitsAsString()
{
    return _D452NumberOfUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452NumberOfUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452NumberOfUnits = parsed;
}

// Standard Getter
public decimal GetD452Proceeds()
{
    return _D452Proceeds;
}

// Standard Setter
public void SetD452Proceeds(decimal value)
{
    _D452Proceeds = value;
}

// Get<>AsString()
public string GetD452ProceedsAsString()
{
    return _D452Proceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452ProceedsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452Proceeds = parsed;
}

// Standard Getter
public decimal GetD452CapitalGainLoss()
{
    return _D452CapitalGainLoss;
}

// Standard Setter
public void SetD452CapitalGainLoss(decimal value)
{
    _D452CapitalGainLoss = value;
}

// Get<>AsString()
public string GetD452CapitalGainLossAsString()
{
    return _D452CapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452CapitalGainLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452CapitalGainLoss = parsed;
}

// Standard Getter
public string GetD452Force2PctMatchFlag()
{
    return _D452Force2PctMatchFlag;
}

// Standard Setter
public void SetD452Force2PctMatchFlag(string value)
{
    _D452Force2PctMatchFlag = value;
}

// Get<>AsString()
public string GetD452Force2PctMatchFlagAsString()
{
    return _D452Force2PctMatchFlag.PadRight(1);
}

// Set<>AsString()
public void SetD452Force2PctMatchFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452Force2PctMatchFlag = value;
}

// Standard Getter
public decimal GetD452CgtCost()
{
    return _D452CgtCost;
}

// Standard Setter
public void SetD452CgtCost(decimal value)
{
    _D452CgtCost = value;
}

// Get<>AsString()
public string GetD452CgtCostAsString()
{
    return _D452CgtCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452CgtCostAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452CgtCost = parsed;
}

// Standard Getter
public decimal GetD452ProfitLoss()
{
    return _D452ProfitLoss;
}

// Standard Setter
public void SetD452ProfitLoss(decimal value)
{
    _D452ProfitLoss = value;
}

// Get<>AsString()
public string GetD452ProfitLossAsString()
{
    return _D452ProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452ProfitLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452ProfitLoss = parsed;
}

// Standard Getter
public string GetFiller58()
{
    return _Filler58;
}

// Standard Setter
public void SetFiller58(string value)
{
    _Filler58 = value;
}

// Get<>AsString()
public string GetFiller58AsString()
{
    return _Filler58.PadRight(9);
}

// Set<>AsString()
public void SetFiller58AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler58 = value;
}

// Standard Getter
public string GetD452ConsolidatedFlag()
{
    return _D452ConsolidatedFlag;
}

// Standard Setter
public void SetD452ConsolidatedFlag(string value)
{
    _D452ConsolidatedFlag = value;
}

// Get<>AsString()
public string GetD452ConsolidatedFlagAsString()
{
    return _D452ConsolidatedFlag.PadRight(1);
}

// Set<>AsString()
public void SetD452ConsolidatedFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452ConsolidatedFlag = value;
}

// Standard Getter
public decimal GetD452StoreUnits()
{
    return _D452StoreUnits;
}

// Standard Setter
public void SetD452StoreUnits(decimal value)
{
    _D452StoreUnits = value;
}

// Get<>AsString()
public string GetD452StoreUnitsAsString()
{
    return _D452StoreUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452StoreUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452StoreUnits = parsed;
}

// Standard Getter
public decimal GetD452StoreProceeds()
{
    return _D452StoreProceeds;
}

// Standard Setter
public void SetD452StoreProceeds(decimal value)
{
    _D452StoreProceeds = value;
}

// Get<>AsString()
public string GetD452StoreProceedsAsString()
{
    return _D452StoreProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452StoreProceedsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452StoreProceeds = parsed;
}

// Standard Getter
public string GetD452ConsolidateKey()
{
    return _D452ConsolidateKey;
}

// Standard Setter
public void SetD452ConsolidateKey(string value)
{
    _D452ConsolidateKey = value;
}

// Get<>AsString()
public string GetD452ConsolidateKeyAsString()
{
    return _D452ConsolidateKey.PadRight(23);
}

// Set<>AsString()
public void SetD452ConsolidateKeyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452ConsolidateKey = value;
}

// Standard Getter
public string GetD452Fa2003Exemption()
{
    return _D452Fa2003Exemption;
}

// Standard Setter
public void SetD452Fa2003Exemption(string value)
{
    _D452Fa2003Exemption = value;
}

// Get<>AsString()
public string GetD452Fa2003ExemptionAsString()
{
    return _D452Fa2003Exemption.PadRight(0);
}

// Set<>AsString()
public void SetD452Fa2003ExemptionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452Fa2003Exemption = value;
}

// Standard Getter
public string GetD452GtProRataMatch()
{
    return _D452GtProRataMatch;
}

// Standard Setter
public void SetD452GtProRataMatch(string value)
{
    _D452GtProRataMatch = value;
}

// Get<>AsString()
public string GetD452GtProRataMatchAsString()
{
    return _D452GtProRataMatch.PadRight(0);
}

// Set<>AsString()
public void SetD452GtProRataMatchAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452GtProRataMatch = value;
}

// Standard Getter
public string GetD452GtUseOriginalDates()
{
    return _D452GtUseOriginalDates;
}

// Standard Setter
public void SetD452GtUseOriginalDates(string value)
{
    _D452GtUseOriginalDates = value;
}

// Get<>AsString()
public string GetD452GtUseOriginalDatesAsString()
{
    return _D452GtUseOriginalDates.PadRight(0);
}

// Set<>AsString()
public void SetD452GtUseOriginalDatesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D452GtUseOriginalDates = value;
}

// Standard Getter
public decimal GetD452NumberOfUnitsYtd()
{
    return _D452NumberOfUnitsYtd;
}

// Standard Setter
public void SetD452NumberOfUnitsYtd(decimal value)
{
    _D452NumberOfUnitsYtd = value;
}

// Get<>AsString()
public string GetD452NumberOfUnitsYtdAsString()
{
    return _D452NumberOfUnitsYtd.ToString("F3", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452NumberOfUnitsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452NumberOfUnitsYtd = parsed;
}

// Standard Getter
public decimal GetD452ProceedsYtd()
{
    return _D452ProceedsYtd;
}

// Standard Setter
public void SetD452ProceedsYtd(decimal value)
{
    _D452ProceedsYtd = value;
}

// Get<>AsString()
public string GetD452ProceedsYtdAsString()
{
    return _D452ProceedsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetD452ProceedsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D452ProceedsYtd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: YeD45DetailDisposalUnused
public class YeD45DetailDisposalUnused
{
    private static int _size = 16023;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler59, is_external=, is_static_class=False, static_prefix=
    private string _Filler59 ="";
    
    
    
    
public YeD45DetailDisposalUnused() {}

public YeD45DetailDisposalUnused(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller59(data.Substring(offset, 16023).Trim());
    offset += 16023;
    
}

// Serialization methods
public string GetYeD45DetailDisposalUnusedAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler59.PadRight(16023));
    
    return result.ToString();
}

public void SetYeD45DetailDisposalUnusedAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 16023 <= data.Length)
    {
        string extracted = data.Substring(offset, 16023).Trim();
        SetFiller59(extracted);
    }
    offset += 16023;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller59()
{
    return _Filler59;
}

// Standard Setter
public void SetFiller59(string value)
{
    _Filler59 = value;
}

// Get<>AsString()
public string GetFiller59AsString()
{
    return _Filler59.PadRight(16023);
}

// Set<>AsString()
public void SetFiller59AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler59 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}

}}