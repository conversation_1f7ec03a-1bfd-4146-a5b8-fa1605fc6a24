using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D46HeaderRecord Data Structure

public class D46HeaderRecord
{
    private static int _size = 190;
    // [DEBUG] Class: D46HeaderRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler70, is_external=, is_static_class=False, static_prefix=
    private string _Filler70 ="";
    
    
    // 88-level condition checks for Filler70
    public bool IsHeaderFound()
    {
        if (this._Filler70 == "'HEAD0000000INVESTOR+ '") return true;
        return false;
    }
    
    
    // [DEBUG] Field: Filler71, is_external=, is_static_class=False, static_prefix=
    private string _Filler71 ="";
    
    
    // 88-level condition checks for Filler71
    public bool IsD46InvokePreProcessor()
    {
        if (this._Filler71 == "'PP'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: Filler72, is_external=, is_static_class=False, static_prefix=
    private string _Filler72 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD46HeaderRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler70.PadRight(21));
        result.Append(_Filler71.PadRight(0));
        result.Append(_Filler72.PadRight(169));
        
        return result.ToString();
    }
    
    public void SetD46HeaderRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 21 <= data.Length)
        {
            string extracted = data.Substring(offset, 21).Trim();
            SetFiller70(extracted);
        }
        offset += 21;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller71(extracted);
        }
        offset += 0;
        if (offset + 169 <= data.Length)
        {
            string extracted = data.Substring(offset, 169).Trim();
            SetFiller72(extracted);
        }
        offset += 169;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD46HeaderRecordAsString();
    }
    // Set<>String Override function
    public void SetD46HeaderRecord(string value)
    {
        SetD46HeaderRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller70()
    {
        return _Filler70;
    }
    
    // Standard Setter
    public void SetFiller70(string value)
    {
        _Filler70 = value;
    }
    
    // Get<>AsString()
    public string GetFiller70AsString()
    {
        return _Filler70.PadRight(21);
    }
    
    // Set<>AsString()
    public void SetFiller70AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler70 = value;
    }
    
    // Standard Getter
    public string GetFiller71()
    {
        return _Filler71;
    }
    
    // Standard Setter
    public void SetFiller71(string value)
    {
        _Filler71 = value;
    }
    
    // Get<>AsString()
    public string GetFiller71AsString()
    {
        return _Filler71.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller71AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler71 = value;
    }
    
    // Standard Getter
    public string GetFiller72()
    {
        return _Filler72;
    }
    
    // Standard Setter
    public void SetFiller72(string value)
    {
        _Filler72 = value;
    }
    
    // Get<>AsString()
    public string GetFiller72AsString()
    {
        return _Filler72.PadRight(169);
    }
    
    // Set<>AsString()
    public void SetFiller72AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler72 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}