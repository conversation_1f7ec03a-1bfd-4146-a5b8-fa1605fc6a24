using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WcConstants Data Structure

public class WcConstants
{
    private static int _size = 12;
    // [DEBUG] Class: WcConstants, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler350, is_external=, is_static_class=False, static_prefix=
    private string _Filler350 ="CONSTANTS===";
    
    
    
    
    
    // Serialization methods
    public string GetWcConstantsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler350.PadRight(12));
        
        return result.ToString();
    }
    
    public void SetWcConstantsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            SetFiller350(extracted);
        }
        offset += 12;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWcConstantsAsString();
    }
    // Set<>String Override function
    public void SetWcConstants(string value)
    {
        SetWcConstantsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller350()
    {
        return _Filler350;
    }
    
    // Standard Setter
    public void SetFiller350(string value)
    {
        _Filler350 = value;
    }
    
    // Get<>AsString()
    public string GetFiller350AsString()
    {
        return _Filler350.PadRight(12);
    }
    
    // Set<>AsString()
    public void SetFiller350AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler350 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
