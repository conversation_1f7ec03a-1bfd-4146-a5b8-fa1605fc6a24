using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D81File Data Structure

public class D81File
{
    private static int _size = 12;
    // [DEBUG] Class: D81File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler251, is_external=, is_static_class=False, static_prefix=
    private string _Filler251 ="$";
    
    
    
    
    // [DEBUG] Field: D81UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D81UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler252, is_external=, is_static_class=False, static_prefix=
    private string _Filler252 ="DJ";
    
    
    
    
    // [DEBUG] Field: D81ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D81ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler253, is_external=, is_static_class=False, static_prefix=
    private string _Filler253 =".DAT";
    
    
    
    
    
    // Serialization methods
    public string GetD81FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler251.PadRight(1));
        result.Append(_D81UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler252.PadRight(2));
        result.Append(_D81ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler253.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD81FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller251(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD81UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller252(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD81ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller253(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD81FileAsString();
    }
    // Set<>String Override function
    public void SetD81File(string value)
    {
        SetD81FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller251()
    {
        return _Filler251;
    }
    
    // Standard Setter
    public void SetFiller251(string value)
    {
        _Filler251 = value;
    }
    
    // Get<>AsString()
    public string GetFiller251AsString()
    {
        return _Filler251.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller251AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler251 = value;
    }
    
    // Standard Getter
    public int GetD81UserNo()
    {
        return _D81UserNo;
    }
    
    // Standard Setter
    public void SetD81UserNo(int value)
    {
        _D81UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD81UserNoAsString()
    {
        return _D81UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD81UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D81UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller252()
    {
        return _Filler252;
    }
    
    // Standard Setter
    public void SetFiller252(string value)
    {
        _Filler252 = value;
    }
    
    // Get<>AsString()
    public string GetFiller252AsString()
    {
        return _Filler252.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller252AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler252 = value;
    }
    
    // Standard Getter
    public int GetD81ReportNo()
    {
        return _D81ReportNo;
    }
    
    // Standard Setter
    public void SetD81ReportNo(int value)
    {
        _D81ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD81ReportNoAsString()
    {
        return _D81ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD81ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D81ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller253()
    {
        return _Filler253;
    }
    
    // Standard Setter
    public void SetFiller253(string value)
    {
        _Filler253 = value;
    }
    
    // Get<>AsString()
    public string GetFiller253AsString()
    {
        return _Filler253.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller253AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler253 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}