using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D93Record Data Structure

public class D93Record
{
    private static int _size = 33;
    // [DEBUG] Class: D93Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D93Key, is_external=, is_static_class=False, static_prefix=
    private D93Key _D93Key = new D93Key();
    
    
    
    
    // [DEBUG] Field: D93Value, is_external=, is_static_class=False, static_prefix=
    private decimal _D93Value =0;
    
    
    
    
    // [DEBUG] Field: D93ValueX, is_external=, is_static_class=False, static_prefix=
    private string _D93ValueX ="";
    
    
    
    
    // [DEBUG] Field: D93RpiEst, is_external=, is_static_class=False, static_prefix=
    private string _D93RpiEst ="";
    
    
    
    
    // [DEBUG] Field: Filler131, is_external=, is_static_class=False, static_prefix=
    private string _Filler131 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD93RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D93Key.GetD93KeyAsString());
        result.Append(_D93Value.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D93ValueX.PadRight(5));
        result.Append(_D93RpiEst.PadRight(1));
        result.Append(_Filler131.PadRight(18));
        
        return result.ToString();
    }
    
    public void SetD93RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            _D93Key.SetD93KeyAsString(data.Substring(offset, 4));
        }
        else
        {
            _D93Key.SetD93KeyAsString(data.Substring(offset));
        }
        offset += 4;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD93Value(parsedDec);
        }
        offset += 5;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            SetD93ValueX(extracted);
        }
        offset += 5;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD93RpiEst(extracted);
        }
        offset += 1;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            SetFiller131(extracted);
        }
        offset += 18;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD93RecordAsString();
    }
    // Set<>String Override function
    public void SetD93Record(string value)
    {
        SetD93RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D93Key GetD93Key()
    {
        return _D93Key;
    }
    
    // Standard Setter
    public void SetD93Key(D93Key value)
    {
        _D93Key = value;
    }
    
    // Get<>AsString()
    public string GetD93KeyAsString()
    {
        return _D93Key != null ? _D93Key.GetD93KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD93KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D93Key == null)
        {
            _D93Key = new D93Key();
        }
        _D93Key.SetD93KeyAsString(value);
    }
    
    // Standard Getter
    public decimal GetD93Value()
    {
        return _D93Value;
    }
    
    // Standard Setter
    public void SetD93Value(decimal value)
    {
        _D93Value = value;
    }
    
    // Get<>AsString()
    public string GetD93ValueAsString()
    {
        return _D93Value.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD93ValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D93Value = parsed;
    }
    
    // Standard Getter
    public string GetD93ValueX()
    {
        return _D93ValueX;
    }
    
    // Standard Setter
    public void SetD93ValueX(string value)
    {
        _D93ValueX = value;
    }
    
    // Get<>AsString()
    public string GetD93ValueXAsString()
    {
        return _D93ValueX.PadRight(5);
    }
    
    // Set<>AsString()
    public void SetD93ValueXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D93ValueX = value;
    }
    
    // Standard Getter
    public string GetD93RpiEst()
    {
        return _D93RpiEst;
    }
    
    // Standard Setter
    public void SetD93RpiEst(string value)
    {
        _D93RpiEst = value;
    }
    
    // Get<>AsString()
    public string GetD93RpiEstAsString()
    {
        return _D93RpiEst.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD93RpiEstAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D93RpiEst = value;
    }
    
    // Standard Getter
    public string GetFiller131()
    {
        return _Filler131;
    }
    
    // Standard Setter
    public void SetFiller131(string value)
    {
        _Filler131 = value;
    }
    
    // Get<>AsString()
    public string GetFiller131AsString()
    {
        return _Filler131.PadRight(18);
    }
    
    // Set<>AsString()
    public void SetFiller131AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler131 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD93Key(string value)
    {
        _D93Key.SetD93KeyAsString(value);
    }
    // Nested Class: D93Key
    public class D93Key
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D93RpiDate, is_external=, is_static_class=False, static_prefix=
        private D93Key.D93RpiDate _D93RpiDate = new D93Key.D93RpiDate();
        
        
        
        
    public D93Key() {}
    
    public D93Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _D93RpiDate.SetD93RpiDateAsString(data.Substring(offset, D93RpiDate.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetD93KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D93RpiDate.GetD93RpiDateAsString());
        
        return result.ToString();
    }
    
    public void SetD93KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            _D93RpiDate.SetD93RpiDateAsString(data.Substring(offset, 4));
        }
        else
        {
            _D93RpiDate.SetD93RpiDateAsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public D93RpiDate GetD93RpiDate()
    {
        return _D93RpiDate;
    }
    
    // Standard Setter
    public void SetD93RpiDate(D93RpiDate value)
    {
        _D93RpiDate = value;
    }
    
    // Get<>AsString()
    public string GetD93RpiDateAsString()
    {
        return _D93RpiDate != null ? _D93RpiDate.GetD93RpiDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD93RpiDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D93RpiDate == null)
        {
            _D93RpiDate = new D93RpiDate();
        }
        _D93RpiDate.SetD93RpiDateAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D93RpiDate
    public class D93RpiDate
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D93RpiDateYy, is_external=, is_static_class=False, static_prefix=
        private int _D93RpiDateYy =0;
        
        
        
        
        // [DEBUG] Field: D93RpiDateMm, is_external=, is_static_class=False, static_prefix=
        private int _D93RpiDateMm =0;
        
        
        
        
    public D93RpiDate() {}
    
    public D93RpiDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD93RpiDateYy(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetD93RpiDateMm(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetD93RpiDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D93RpiDateYy.ToString().PadLeft(2, '0'));
        result.Append(_D93RpiDateMm.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetD93RpiDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD93RpiDateYy(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD93RpiDateMm(parsedInt);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD93RpiDateYy()
    {
        return _D93RpiDateYy;
    }
    
    // Standard Setter
    public void SetD93RpiDateYy(int value)
    {
        _D93RpiDateYy = value;
    }
    
    // Get<>AsString()
    public string GetD93RpiDateYyAsString()
    {
        return _D93RpiDateYy.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD93RpiDateYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D93RpiDateYy = parsed;
    }
    
    // Standard Getter
    public int GetD93RpiDateMm()
    {
        return _D93RpiDateMm;
    }
    
    // Standard Setter
    public void SetD93RpiDateMm(int value)
    {
        _D93RpiDateMm = value;
    }
    
    // Get<>AsString()
    public string GetD93RpiDateMmAsString()
    {
        return _D93RpiDateMm.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD93RpiDateMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D93RpiDateMm = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}

}}