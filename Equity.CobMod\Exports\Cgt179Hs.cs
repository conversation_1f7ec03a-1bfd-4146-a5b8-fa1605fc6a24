using System;
using System.IO;
using System.Text;
using EquityProject.Cgt179HsDTO;
using EquityProject.CgtabortPGM;
using EquityProject.EqtpathPGM;
using EquityProject.EquityGlobalParmsDTO;
namespace EquityProject.Cgt179HsPGM
{
    // Cgt179Hs Class Definition

    //Cgt179Hs Class Constructor
    public class Cgt179Hs
    {
        // Declare Cgt179Hs Class private variables
        private Fvar _fvar = new Fvar();
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();

        // File handle for GAINLOSS-EXPORT
        private System.IO.StreamWriter _gainlossExportFile;
        private EquityGlobalParms equityGlobalParms;

        // Declare {program_name} Class getters setters
        public Fvar GetFvar() { return _fvar; }
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        /// <summary>
        /// Helper method to call external subroutines
        /// </summary>
        /// <param name="ivar">The Ivar parameter to pass to the subroutine</param>
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
        }


        // Run() method
        public void Run(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Call the main entry point
            // No mainline procedure found, add your entry point here
            AControl(fvar, gvar, ivar);
        }

        // Methods representing paragraphs under procedure division
        /// <summary>
        ///   AControl processes different actions based on the value of CGTSKAN-ACTION.
        ///
        ///   In the COBOL original:
        ///     - EVALUATE   CGTSKAN-ACTION
        ///     - WHEN   'I'
        ///     - PERFORM   I-INITIALISE
        ///     - WHEN   'R'
        ///     - PERFORM   R-REPORT-DTL
        ///     - WHEN   'S'
        ///     - PERFORM   S-SEDOL-TOTAL
        ///     - WHEN   'F'
        ///     - PERFORM   S-SEDOL-TOTAL
        ///     - PERFORM   F-FUND-TOTAL
        ///     - WHEN   'G'
        ///     - PERFORM   G-FINAL-TOTAL
        ///     - WHEN   'Q'
        ///     - PERFORM   Q-QUIT-REPORT
        /// </summary>
        /// <param name="fvar">Global variables parameter</param>
        /// <param name="gvar">Global variables parameter</param>
        /// <param name="ivar">Item variables parameter</param>
        /// <remarks>
        ///    The C# method mimics the logic of the COBOL paragraph with modern C#
        ///    conventions and best practices.
        /// </remarks>
        public void AControl(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Get the value of CGTSKAN-ACTION from the appropriate object
            string cgtskanAction = ivar.GetCgtskanLinkage().GetCgtskanAction();

            // Evaluate the value of CGTSKAN-ACTION
            switch (cgtskanAction)
            {
                case "I":
                    // PERFORM I-INITIALISE
                    IInitialise(fvar, gvar, ivar);
                    break;
                case "R":
                    // PERFORM R-REPORT-DTL
                    RReportDtl(fvar, gvar, ivar);
                    break;
                case "S":
                    // PERFORM S-SEDOL-TOTAL
                    SSedolTotal(fvar, gvar, ivar);
                    break;
                case "F":
                    // PERFORM S-SEDOL-TOTAL
                    SSedolTotal(fvar, gvar, ivar);
                    // PERFORM F-FUND-TOTAL
                    FFundTotal(fvar, gvar, ivar);
                    break;
                case "G":
                    // PERFORM G-FINAL-TOTAL
                    GFinalTotal(fvar, gvar, ivar);
                    break;
                case "Q":
                    // PERFORM Q-QUIT-REPORT
                    QQuitReport(fvar, gvar, ivar);
                    break;
                default:
                    // Handle any unexpected values or cases
                    // Possibly log an error or take some other action
                    break;
            }
        }
        /// <summary>
        /// COBOL Paragraph Name: iInitialise
        ///
        ///Moves data between variables, performs initializations, and calls other paragraphs.
        /// </summary>
        ///
        /// <param name="fvar">Contains the D77-RECORD variable needed by this method.</param>
        /// <param name="gvar">Contains the global variables needed by this method.</param>
        /// <param name="ivar">Contains the input variables needed by this method and access to ExecActions and Executable. </param>
        /// <remarks>
        /// Created by AI.
        /// </remarks>
        public void IInitialise(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // MOVE   L-USER-NO                      TO   REPORT-USER-NO
            var lUserNo = ivar.GetCommonLinkage().GetLUserNo();
            gvar.GetReportFile().SetReportUserNo(lUserNo.ToString());

            // MOVE   CGTSKAN-REPORT-GENERATION-NO   TO   REPORT-GEN-NO
            var cgtskanReportGenerationNo = ivar.GetCgtskanLinkage().GetCgtskanReportGenerationNo();
            gvar.GetReportFile().SetReportGenNo(cgtskanReportGenerationNo);

            // MOVE   USER-DATA-PATH   TO   EQTPATH-PATH-ENV-VARIABLE
            var userDataPath = Gvar.USER_DATA_PATH;
            gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(userDataPath);

            // MOVE   REPORT-FILE      TO   EQTPATH-FILE-NAME
            var reportFile = gvar.GetReportFile();
            gvar.GetEqtpathLinkage().SetEqtpathFileName(reportFile.GetReportFileAsString());

            // PERFORM   X-Call-Eqtpath
            XCallEqtpath(fvar, gvar, ivar);

            // MOVE   EQTPATH-PATH-FILE-NAME   TO   REPORT-FILE-NAME
            var eqtpathPathFileName = gvar.GetEqtpathLinkage().GetEqtpathPathFileName();
            gvar.SetReportFileName(eqtpathPathFileName);

            // OPEN   OUTPUT   GAINLOSS-EXPORT
            try
            {
                _gainlossExportFile = new System.IO.StreamWriter(gvar.GetReportFileName());
                gvar.SetWFileReturnCode("0");
            }
            catch (System.Exception)
            {
                gvar.SetWFileReturnCode("99");
            }

            // IF    W-FILE-RETURN-CODE  NOT   =   ZERO
            if (gvar.GetWFileReturnCode() != "0")
            {
                // PERFORM   ZB-Cgtabort
                ZBCgtabort(fvar, gvar, ivar);
            }

            // INITIALIZE   D77-RECORD
            fvar.SetD77Record(new D77Record());

            // ACCEPT   W-TODAYS-DATE   FROM   DATE
            var date = DateTime.Now.ToString("yyMMdd");
            gvar.GetWTodaysDate().SetWTodaysDateAsString(date);

            // MOVE   W-TODAYS-DATE   TO   CGTDATE2-YYMMDD1
            var wTodaysDate = gvar.GetWTodaysDate();
            gvar.GetCgtdate2LinkageDate1().GetFiller39().SetCgtdate2Yymmdd1AsString(wTodaysDate.GetWTodaysDateAsString());

            // CALL "CGTDATE2" USING CGTDATE2-LINKAGE-DATE1
            // (Assuming this is a placeholder for a method call that processes CGTDATE2)
            // TODO: Implement the actual call to "CGTDATE2" if necessary.
            // STRING   W-TODAYS-DD   W-TODAYS-MM   CGTDATE2-C-CC1   W-TODAYS-YY
            var wTodaysDd = gvar.GetWTodaysDate().GetWTodaysDd();
            var wTodaysMm = gvar.GetWTodaysDate().GetWTodaysMm();
            var cgtdate2CCc1 = gvar.GetCgtdate2LinkageDate1().GetFiller39().GetCgtdate2Cc1();
            var wTodaysYy = gvar.GetWTodaysDate().GetWTodaysYy();

            // DELIMITED by SIZE
            // var delimitedBy = "SIZE"; // Assuming "SIZE" is a delimiter type
            // gvar.SetDelimited(delimitedBy);

            // INTO   I-TODAYS-DATE
            var iTodaysDate = string.Concat(wTodaysDd, wTodaysMm, cgtdate2CCc1, wTodaysYy); // COBOL STRING concatenates

            // TODO: Store iTodaysDate somewhere if needed
            // gvar.SetITodaysDate(iTodaysDate);

            // MOVE   NEW-DERIVATIVE-EXPORT-FORMAT   TO   ELCGMIO-LINKAGE-2
            var newDerivativeExportFormat = Gvar.NEW_DERIVATIVE_EXPORT_FORMAT;
            gvar.SetElcgmioLinkage2AsString(newDerivativeExportFormat);

            // PERFORM   X-Call-Mf-Handler-For-Config
            XCallMfHandlerForConfig(fvar, gvar, ivar);

            // MOVE   W-CONFIG-ITEM    TO   W-NEW-DERIVATIVE-EXPORT-FORMAT
            var wConfigItem = gvar.GetWConfigItem();
            gvar.SetWNewDerivativeExportFormat(wConfigItem);

            // MOVE   'I'   TO   W-LAST-CALL
            gvar.SetWLastCall("I");
        }
        /// <summary>
        /// CONVERTED FROM COBOL PARAGRAPH: rReportDtl
        /// </summary>
        /// <remarks>
        /// This method processes the D13-BAL-ACQ-DISP-RECORD and performs various business logic
        /// checks and calculations based on the COBOL paragraph "rReportDtl". It handles conditional
        /// logic, data movements, and prepares the D77-RECORD for further processing.
        /// </remarks>
        /// <param name="fvar">fvar parameters for accessing COBOL variables</param>
        /// <param name="gvar">gvar parameters for accessing COBOL variables</param>
        /// <param name="ivar">ivar parameters for accessing COBOL variables</param>
        public void RReportDtl(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // MOVE CGTSKAN-MASTER-RECORD TO D13-BAL-ACQ-DISP-RECORD
            // TODO: Implement proper conversion from CgtskanMasterRecord to D13BalAcqDispRecord
            gvar.SetD13BalAcqDispRecordAsString(ivar.GetCgtskanLinkage().GetCgtskanMasterRecordAsString());

            // IF D13-2-BOND-DISPOSAL = 'Y'
            if (gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra().GetD132BondDisposal() == "Y")
            {
                // GO TO R-EXIT
                return;
            }

            // IF CGTSKAN-OPTIONS AND (D13-2-DUAL-TRANS-EX-E OR D13-2-DUAL-TRANS-EX-W)
            if (ivar.GetCgtskanLinkage().IsCgtskanOptions() &&
                (gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().IsD132DualTransExE() ||
                 gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().IsD132DualTransExW()))
            {
                // GO TO R-EXIT
                return;
            }

            // IF CONFIG-NEW-DERIVATIVE-EXPORT-FORMAT
            if (gvar.IsConfigNewDerivativeExportFormat())
            {
                // MOVE W-NEW-INITIALISED-RECORD TO D77-RECORD
                fvar.SetD77RecordAsString(gvar.GetWInitialisedRecord().GetWNewInitialisedRecordAsString());
            }
            else
            {
                // MOVE W-OLD-INITIALISED-RECORD TO D77-RECORD
                fvar.SetD77RecordAsString(gvar.GetWInitialisedRecord().GetWNewInitialisedRecord().GetWOldInitialisedRecord().GetWOldInitialisedRecordAsString());
            }

            // MOVE D13-2-CO-AC-LK TO D77-FUND-CODE
            fvar.GetD77Record().SetD77FundCode(gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().GetD132Key().GetD132CalSedol().GetD132CoAcLk());

            // MOVE D13-2-SEDOL TO D77-SEDOL-CODE
            fvar.GetD77Record().SetD77SedolCode(gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().GetD132Key().GetD132CalSedol().GetD132Sedol());

            // MOVE D13-2-NUMBER-OF-UNITS TO D77-HOLDING
            fvar.GetD77Record().SetD77Holding(gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra().GetD132NumberOfUnits());

            // MOVE D13-2-BARGAIN-DATE TO CGTDATE2-YYMMDD1
            gvar.GetCgtdate2LinkageDate1().GetFiller39().SetCgtdate2Yymmdd1AsString(gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().GetD132BargainDate().GetD132BargainDateAsString());

            // CALL "CGTDATE2" USING CGTDATE2-LINKAGE-DATE1
            // STRING D13-2-BARGAIN-DATE-DD D13-2-BARGAIN-DATE-MM
            // CGTDATE2-LINKAGE-DATE.C-CC1 D13-2-BARGAIN-DATE-YY
            // DELIMITED BY SIZE INTO D77-DISPOSAL-DATE
            fvar.GetD77Record().SetD77DisposalDate(
                gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().GetD132BargainDate().GetD132BargainDateDd().ToString() +
                gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().GetD132BargainDate().GetD132BargainDateYymm().GetD132BargainDateMm().ToString() +
                gvar.GetCgtdate2LinkageDate1().GetFiller39().GetCgtdate2Cc1() +
                gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().GetD132BargainDate().GetD132BargainDateYymm().GetD132BargainDateYy().ToString());

            // IF D13-2-CAPITAL-GAIN-LOSS < 0
            if (gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra().GetD132CapitalGainLoss() < 0)
            {
                // MOVE ZERO TO D77-CAPITAL-GAIN
                fvar.GetD77Record().SetD77CapitalGain(0);
                // MOVE D13-2-CAPITAL-GAIN-LOSS TO D77-CAPITAL-LOSS
                fvar.GetD77Record().SetD77CapitalLoss(gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra().GetD132CapitalGainLoss());
            }
            else
            {
                // MOVE D13-2-CAPITAL-GAIN-LOSS TO D77-CAPITAL-GAIN
                fvar.GetD77Record().SetD77CapitalGain(gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra().GetD132CapitalGainLoss());
                // MOVE ZERO TO D77-CAPITAL-LOSS
                fvar.GetD77Record().SetD77CapitalLoss(0);
            }

            // IF WS-HOLDING-FLAG = 'Y'
            if (ivar.GetWsHoldingFlag() == "Y")
            {
                // MOVE 'Y' TO D77-HOLDING-FLAG
                fvar.GetD77Record().SetD77HoldingFlag("Y");
            }
            else
            {
                // MOVE ' ' TO D77-HOLDING-FLAG
                fvar.GetD77Record().SetD77HoldingFlag(" ");
            }

            // MOVE D13-2-CONTRACT-NO TO D77-TRANS-REFERENCE
            fvar.GetD77Record().SetD77TransReference(gvar.GetD13BalAcqDispRecord().GetD13BalAcqDispCommon().GetD132Key().GetD132ContractNo().GetD132ContractNoAsString());
            //If condition
            if (gvar.GetD13BalAcqDispRecord()
                .GetD13BalAcqDispCommon().IsD132DualTransExEp())
            {
                fvar.GetD77Record().SetD77TransBaseCost(
                    gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra().GetD132ProceedsYtd()
                    - gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra().GetD132IndexationUsed()
                    - gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra().GetD132CapitalGainLoss());
            }
            // Nested if condition
            else if (gvar.GetD13BalAcqDispRecord()
                .GetD13BalAcqDispCommon().IsD132DualTransExWc())
            {
                fvar.GetD77Record().SetD77TransBaseCost(
                    gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra().GetD132CgtCost());
            }
            // Nested ELSE conditions
            else if (ivar.GetCgtskanLinkage().IsCgtskanShortWrittenDeriv())
            {
                fvar.GetD77Record().SetD77TransBaseCost(
                    -gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra().GetD132Proceeds()
                    - gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra().GetD132IndexationUsed()
                    - gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra().GetD132CapitalGainLoss());
            }
            else
            {
                fvar.GetD77Record().SetD77TransBaseCost(
                    gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra().GetD132Proceeds()
                    - gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra().GetD132IndexationUsed()
                    - gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra().GetD132CapitalGainLoss());
            }
            // MOVE D13-2-INDEXATION-USED TO D77-TRANS-INDEXATION
            fvar.GetD77Record().SetD77TransIndexation(gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra().GetD132IndexationUsed());

            // IF condition
            if (gvar.GetD13BalAcqDispRecord()
                .GetD13BalAcqDispCommon().IsD132DualTransExEp())
            {
                fvar.GetD77Record().SetD77TransProceeds(gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra()
                    .GetD132ProceedsYtd());
            }
            // Nested if condition
            else if (gvar.GetD13BalAcqDispRecord()
                .GetD13BalAcqDispCommon().IsD132DualTransExWc())
            {
                fvar.GetD77Record().SetD77TransProceeds(gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra()
                    .GetD132CgtCost() + gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra()
                    .GetD132CapitalGainLoss() + gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra()
                    .GetD132IndexationUsed());
            }
            else
            {
                fvar.GetD77Record().SetD77TransProceeds(gvar.GetD13BalAcqDispRecord().GetFiller36().GetD132Record03Fields().GetD132DispExtra()
                    .GetD132Proceeds());
            }

            // IF CONFIG-NEW-DERIVATIVE-EXPORT-FORMAT
            if (gvar.IsConfigNewDerivativeExportFormat())
            {
                // IF CGTSKAN-SHORT-WRITTEN-DERIV
                if (ivar.GetCgtskanLinkage().IsCgtskanShortWrittenDeriv())
                {
                    // MOVE '+' TO D77-HOLDING-SIGN
                    fvar.GetD77Record().SetD77HoldingSign("+");
                }
                else
                {
                    // MOVE '-' TO D77-HOLDING-SIGN
                    fvar.GetD77Record().SetD77HoldingSign("-");
                }
            }

            // PERFORM ZA-WRITE-FUND-EXPORT-FILE.
            ZaWriteFundExportFile(fvar, gvar, ivar);

            // MOVE 'R' TO W-LAST-CALL.
            gvar.SetWLastCall("R");
        }
        /// <summary>
        /// SET W-LAST-CALL TO 'S'.
        /// </summary>
        /// <param name="fvar">A collection of Kohlscheut fields (f).</param>
        /// <param name="gvar">A collection of globally significant data items (g).</param>
        /// <param name="ivar">A collection of working storage variables (i).</param>
        ///
        /// <remarks>
        /// <para>This method translates the COBOL paragraph <c>sSedolTotal</c>.</para>
        /// <para>It performs the logic of moving the value 'S' to the variable W-LAST-CALL.</para>
        ///
        /// </remarks>
        public void SSedolTotal(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move the value 'S' to W-LAST-CALL
            // COBOL: MOVE   'S'    TO   W-LAST-CALL.
            gvar.SetWLastCall("S");
        }
        /// <summary>
        /// The FFundTotal method performs operations specific to the original COBOL paragraph named 'FFundTotal'.
        /// </summary>
        /// <param name="fvar">An instance of the Fvar class representing any necessary variables and their accessors.</param>
        /// <param name="gvar">An instance of the Gvar class containing variables and their accessors.</param>
        /// <param name="ivar">An instance of the Ivar class containing variables and their accessors.</param>
        /// <remarks>
        /// The method essentially performs the MOVE operation from the original COBOL paragraph to the target variable.
        /// </remarks>
        public void FFundTotal(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // The MOVEs if from the right side - f to the left side - gvar
            // To replicate COBOL's MOVE 'F' TO W-LAST-CALL logic
            // Set the W-Last-Call to the string value 'F'
            gvar.SetWLastCall("F");
        }
        /// <summary>
        /// COBOL Paragraph: gFinalTotal
        ///
        /// The purpose of this method is to perform the logic specified in the COBOL gFinalTotal paragraph.
        /// It includes closing the GAINLOSS-EXPORT file and updating the W-LAST-CALL variable.
        /// </summary>
        ///
        /// <param name="fvar">Fvar parameter to access COBOL variables.</param>
        /// <param name="gvar">Gvar parameter to access COBOL variables.</param>
        /// <param name="ivar">Ivar parameter to access COBOL variables.</param>
        ///
        /// <remarks>
        /// This method translates COBOL paragraph to C# method by using getter and setter methods instead of property access.
        /// It maintains the logical flow and business rules, including setting values and interaction with files.
        /// COBOL GO TO statements have not been used in this snippet.
        ///
        /// The following COBOL logic is implemented:
        /// 1. Closing a file
        /// 2. Performing data operations: closing the GAINLOSS-EXPORT file and updating the W-LAST-CALL variable.
        ///
        /// Variable mappings:
        /// - gVar.GetGainlossExport to represent GAINLOSS-EXPORT
        /// - gVar.GetWLastCall to represent W-LAST-CALL
        ///
        /// </remarks>
        public void GFinalTotal(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Close the gainloss export file represented by GAINLOSS-EXPORT
            try
            {
                if (_gainlossExportFile != null)
                {
                    _gainlossExportFile.Close();
                    _gainlossExportFile = null;
                }
                gvar.SetWFileReturnCode("0");
            }
            catch (System.Exception)
            {
                gvar.SetWFileReturnCode("99");
            }

            // Move value 'G' into variable represented by W-LAST-CALL
            gvar.SetWLastCall("G");
        }
        /// <summary>
        /// Quit report processing.
        ///
        /// <para>COBOL Paragraph Name: qQuitReport</para>
        /// </summary>
        /// <param name="fvar"></param>
        /// <param name="gvar">Global variables</param>
        /// <param name="ivar"></param>
        /// <remarks>
        /// <list type="bullet">
        /// <item><description>Close the GAINLOSS-EXPORT file.</description></item>
        /// <item><description>Check the file return code and perform ZB-CGTABORT if not zero.</description></item>
        /// <item><description>Set W-LAST-CALL to 'Q'.</description></item>
        /// </list>
        /// </remarks>
        public void QQuitReport(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Close the GAINLOSS-EXPORT file.
            try
            {
                if (_gainlossExportFile != null)
                {
                    _gainlossExportFile.Close();
                    _gainlossExportFile = null;
                }
                gvar.SetWFileReturnCode("0");
            }
            catch (System.Exception)
            {
                gvar.SetWFileReturnCode("99");
            }

            // Check the file return code.
            if (gvar.GetWFileReturnCode() != "0")
            {
                // Perform ZB-CGTABORT if the file return code is not zero.
                // This is equivalent to calling the ZBCgtabort method.
                this.ZBCgtabort(fvar, gvar, ivar);
            }

            // Set W-LAST-CALL to 'Q'.
            gvar.SetWLastCall("Q");
        }
        ///
        /// <summary>
        /// Implements the logic for the "WRITE D77-RECORD" statement and conditional PERFORM
        /// <remarks>
        /// This method writes the D77-RECORD and checks if W-FILE-RETURN-CODE is not equal to ZERO.
        /// If the condition is met, it performs the ZbCgtabort method.
        /// </remarks>
        public void ZaWriteFundExportFile(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Write the D77-RECORD
            try
            {
                if (_gainlossExportFile != null)
                {
                    _gainlossExportFile.WriteLine(fvar.GetD77Record().GetD77RecordAsString());
                    _gainlossExportFile.Flush();
                }
                gvar.SetWFileReturnCode("0");
            }
            catch (System.Exception)
            {
                gvar.SetWFileReturnCode("99");
            }

            // Check if W-FILE-RETURN-CODE is not equal to ZERO
            if (gvar.GetWFileReturnCode() != "0")
            {
                // Perform the ZB-CGTABORT paragraph
                ZBCgtabort(fvar, gvar, ivar);
            }
        }
        /// <summary>
        /// Zigbop zigzag code: invoke zebra at battleop:
        /// </summary>
        /// <param name="fvar">The Fvar parameter containing field variables.</param>
        /// <param name="gvar">The Gvar parameter containing global variables.</param>
        /// <param name="ivar">The Ivar parameter containing item variables.</param>
        /// <remarks>
        /// <para>Converted from the COBOL paragraph "zbCgtabort".</para>
        /// <para>This method handles the logic equivalent to the original COBOL code by moving values to specific variables and calling an external program.</para>
        /// </remarks>
        public void ZBCgtabort(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move PROGRAM-NAME to L-ABORT-PROGRAM-NAME
            var currentProgramName = gvar.GetProgramName();
            var cgtabortLinkage = gvar.GetCgtabortLinkage();

            cgtabortLinkage.SetLAbortProgramName(currentProgramName);

            // Move W-FILE-RETURN-CODE to L-ABORT-FILE-STATUS
            var fileReturnCode = gvar.GetWFileReturnCode();
            cgtabortLinkage.SetLAbortFileStatus(fileReturnCode);

            // Move REPORT-FILE to L-ABORT-FILE-NAME
            var reportFile = gvar.GetReportFile();
            cgtabortLinkage.SetLAbortFileName(reportFile.GetReportFileAsString());

            // Call the external program 'CGTABORT' using COMMON-LINKAGE
            var commonLinkage = ivar.GetCommonLinkage();

            // TODO: Implement external program call to CGTABORT
            Cgtabort cgtabort = new Cgtabort();
            cgtabort.GetIvar().SetCgtabortLinkageAsString(gvar.GetCgtabortLinkageAsString());
            cgtabort.GetIvar().SetCommonLinkageAsString(ivar.GetCommonLinkageAsString());
            cgtabort.Run(cgtabort.GetGvar(), cgtabort.GetIvar());
            // cgtabort.Run(commonLinkage);

            // Execute paragraph CGTABORT-LINKAGE if needed based on the original logic (assuming it's self-contained)
            // Since the details of CGTABORT-LINKAGE are not provided, we assume it completes in the external call
        }
        /// <summary>
        /// COBOL to C# Converted Method
        /// Original COBOL Paragraph: xCallEqtpath
        /// </summary>
        ///
        /// <param name="fvar">Function variable used to pass functional parameters</param>
        /// <param name="gvar">Global variable used to pass global parameters</param>
        /// <param name="ivar">Input variable used to pass local parameters</param>
        ///
        /// <remarks>
        /// This method calls an external program "EQTPATH" with the specified linkage parameters.
        /// The COBOL paragraph name was xCallEqtpath.
        /// </remarks>
        public void XCallEqtpath(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the EQTPATH program
            // TODO: Implement external program call to EQTPATH
            Eqtpath eqtpath = new Eqtpath();
            eqtpath.GetIvar().SetEqtpathLinkageAsString(gvar.GetEqtpathLinkageAsString());

            eqtpath.Run(eqtpath.GetGvar(), eqtpath.GetIvar(), equityGlobalParms);
        }
        /// <summary>
        /// Implementation of the COBOL paragraph xCallMfHandlerForConfig.
        ///
        /// This method handles the configuration value retrieval and
        /// passes it to an external handler using the appropriate linkage data.
        /// </summary>
        ///
        /// <param name="fvar">The instance containing the functionrelated variables.</param>
        /// <param name="gvar">The instance containing the global variables.</param>
        /// <param name="ivar">The instance containing the input/output variables.</param>
        /// <remarks>
        /// COBOL takes the value from GET-CONFIG-VALUE and stores it in L-ACT. It then calls the
        /// external program 'ELCGMIO' using the linkage data specified. Finally, it moves the result
        /// from ELCGMIO-LINKAGE-2 to W-CONFIG-ITEM.
        ///
        /// Conversion notes:
        /// 1. The MOVE statement is implemented using setter methods.
        /// 2. The CALL statement is translated to creating an instance of ELCGMIO
        ///    and invoking the Run method with the appropriate parameters.
        /// 3. The final MOVE statement is implemented using setter methods.
        ///
        /// </remarks>
        public void XCallMfHandlerForConfig(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move the value of GET-CONFIG-VALUE to L-ACT
            gvar.GetElcgmioLinkage1().SetLAct(Ivar.GET_CONFIG_VALUE);

            // Call the external program 'ELCGMIO' using the specified linkage data
            // must uncomment
            // ELCGMIO elcgmio = new ELCGMIO();
            // elcgmio.Run(elcgmio.getGvar(), elcgmio.getIvar());

            // Move the result from ELCGMIO-LINKAGE-2 to W-CONFIG-ITEM
            gvar.SetWConfigItem(gvar.GetElcgmioLinkage2AsString());
        }

    }
}
