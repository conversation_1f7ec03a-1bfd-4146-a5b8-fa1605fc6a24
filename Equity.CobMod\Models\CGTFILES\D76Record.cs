using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D76Record Data Structure

public class D76Record
{
    private static int _size = 42;
    // [DEBUG] Class: D76Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D76FundCode, is_external=, is_static_class=False, static_prefix=
    private string _D76FundCode ="";
    
    
    
    
    // [DEBUG] Field: D76SedolCode, is_external=, is_static_class=False, static_prefix=
    private string _D76SedolCode ="";
    
    
    
    
    // [DEBUG] Field: D76Holding, is_external=, is_static_class=False, static_prefix=
    private decimal _D76Holding =0;
    
    
    
    
    // [DEBUG] Field: D76CgtCost, is_external=, is_static_class=False, static_prefix=
    private decimal _D76CgtCost =0;
    
    
    
    
    // [DEBUG] Field: D76IndexedCost, is_external=, is_static_class=False, static_prefix=
    private decimal _D76IndexedCost =0;
    
    
    
    
    // [DEBUG] Field: D76LastEventDate, is_external=, is_static_class=False, static_prefix=
    private string _D76LastEventDate ="";
    
    
    
    
    // [DEBUG] Field: D76HoldingFlag, is_external=, is_static_class=False, static_prefix=
    private string _D76HoldingFlag ="";
    
    
    
    
    // [DEBUG] Field: D76HoldingSign, is_external=, is_static_class=False, static_prefix=
    private string _D76HoldingSign ="";
    
    
    
    
    
    // Serialization methods
    public string GetD76RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D76FundCode.PadRight(0));
        result.Append(_D76SedolCode.PadRight(0));
        result.Append(_D76Holding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D76CgtCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D76IndexedCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D76LastEventDate.PadRight(0));
        result.Append(_D76HoldingFlag.PadRight(0));
        result.Append(_D76HoldingSign.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD76RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD76FundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD76SedolCode(extracted);
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD76Holding(parsedDec);
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD76CgtCost(parsedDec);
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD76IndexedCost(parsedDec);
        }
        offset += 14;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD76LastEventDate(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD76HoldingFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD76HoldingSign(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD76RecordAsString();
    }
    // Set<>String Override function
    public void SetD76Record(string value)
    {
        SetD76RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD76FundCode()
    {
        return _D76FundCode;
    }
    
    // Standard Setter
    public void SetD76FundCode(string value)
    {
        _D76FundCode = value;
    }
    
    // Get<>AsString()
    public string GetD76FundCodeAsString()
    {
        return _D76FundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD76FundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D76FundCode = value;
    }
    
    // Standard Getter
    public string GetD76SedolCode()
    {
        return _D76SedolCode;
    }
    
    // Standard Setter
    public void SetD76SedolCode(string value)
    {
        _D76SedolCode = value;
    }
    
    // Get<>AsString()
    public string GetD76SedolCodeAsString()
    {
        return _D76SedolCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD76SedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D76SedolCode = value;
    }
    
    // Standard Getter
    public decimal GetD76Holding()
    {
        return _D76Holding;
    }
    
    // Standard Setter
    public void SetD76Holding(decimal value)
    {
        _D76Holding = value;
    }
    
    // Get<>AsString()
    public string GetD76HoldingAsString()
    {
        return _D76Holding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD76HoldingAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D76Holding = parsed;
    }
    
    // Standard Getter
    public decimal GetD76CgtCost()
    {
        return _D76CgtCost;
    }
    
    // Standard Setter
    public void SetD76CgtCost(decimal value)
    {
        _D76CgtCost = value;
    }
    
    // Get<>AsString()
    public string GetD76CgtCostAsString()
    {
        return _D76CgtCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD76CgtCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D76CgtCost = parsed;
    }
    
    // Standard Getter
    public decimal GetD76IndexedCost()
    {
        return _D76IndexedCost;
    }
    
    // Standard Setter
    public void SetD76IndexedCost(decimal value)
    {
        _D76IndexedCost = value;
    }
    
    // Get<>AsString()
    public string GetD76IndexedCostAsString()
    {
        return _D76IndexedCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD76IndexedCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D76IndexedCost = parsed;
    }
    
    // Standard Getter
    public string GetD76LastEventDate()
    {
        return _D76LastEventDate;
    }
    
    // Standard Setter
    public void SetD76LastEventDate(string value)
    {
        _D76LastEventDate = value;
    }
    
    // Get<>AsString()
    public string GetD76LastEventDateAsString()
    {
        return _D76LastEventDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD76LastEventDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D76LastEventDate = value;
    }
    
    // Standard Getter
    public string GetD76HoldingFlag()
    {
        return _D76HoldingFlag;
    }
    
    // Standard Setter
    public void SetD76HoldingFlag(string value)
    {
        _D76HoldingFlag = value;
    }
    
    // Get<>AsString()
    public string GetD76HoldingFlagAsString()
    {
        return _D76HoldingFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD76HoldingFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D76HoldingFlag = value;
    }
    
    // Standard Getter
    public string GetD76HoldingSign()
    {
        return _D76HoldingSign;
    }
    
    // Standard Setter
    public void SetD76HoldingSign(string value)
    {
        _D76HoldingSign = value;
    }
    
    // Get<>AsString()
    public string GetD76HoldingSignAsString()
    {
        return _D76HoldingSign.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD76HoldingSignAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D76HoldingSign = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}