using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D73Record Data Structure

public class D73Record
{
    private static int _size = 151;
    // [DEBUG] Class: D73Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D73PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D73PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D73ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D73ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD73RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D73PrintControl.PadRight(1));
        result.Append(_D73ReportLine.PadRight(150));
        
        return result.ToString();
    }
    
    public void SetD73RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD73PrintControl(extracted);
        }
        offset += 1;
        if (offset + 150 <= data.Length)
        {
            string extracted = data.Substring(offset, 150).Trim();
            SetD73ReportLine(extracted);
        }
        offset += 150;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD73RecordAsString();
    }
    // Set<>String Override function
    public void SetD73Record(string value)
    {
        SetD73RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD73PrintControl()
    {
        return _D73PrintControl;
    }
    
    // Standard Setter
    public void SetD73PrintControl(string value)
    {
        _D73PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD73PrintControlAsString()
    {
        return _D73PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD73PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D73PrintControl = value;
    }
    
    // Standard Getter
    public string GetD73ReportLine()
    {
        return _D73ReportLine;
    }
    
    // Standard Setter
    public void SetD73ReportLine(string value)
    {
        _D73ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD73ReportLineAsString()
    {
        return _D73ReportLine.PadRight(150);
    }
    
    // Set<>AsString()
    public void SetD73ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D73ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}