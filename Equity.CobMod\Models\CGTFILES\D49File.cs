using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D49File Data Structure

public class D49File
{
    private static int _size = 12;
    // [DEBUG] Class: D49File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler214, is_external=, is_static_class=False, static_prefix=
    private string _Filler214 ="$";
    
    
    
    
    // [DEBUG] Field: D49UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D49UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler215, is_external=, is_static_class=False, static_prefix=
    private string _Filler215 ="BU";
    
    
    
    
    // [DEBUG] Field: D49ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D49ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler216, is_external=, is_static_class=False, static_prefix=
    private string _Filler216 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD49FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler214.PadRight(1));
        result.Append(_D49UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler215.PadRight(2));
        result.Append(_D49ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler216.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD49FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller214(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD49UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller215(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD49ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller216(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD49FileAsString();
    }
    // Set<>String Override function
    public void SetD49File(string value)
    {
        SetD49FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller214()
    {
        return _Filler214;
    }
    
    // Standard Setter
    public void SetFiller214(string value)
    {
        _Filler214 = value;
    }
    
    // Get<>AsString()
    public string GetFiller214AsString()
    {
        return _Filler214.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller214AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler214 = value;
    }
    
    // Standard Getter
    public int GetD49UserNo()
    {
        return _D49UserNo;
    }
    
    // Standard Setter
    public void SetD49UserNo(int value)
    {
        _D49UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD49UserNoAsString()
    {
        return _D49UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD49UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D49UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller215()
    {
        return _Filler215;
    }
    
    // Standard Setter
    public void SetFiller215(string value)
    {
        _Filler215 = value;
    }
    
    // Get<>AsString()
    public string GetFiller215AsString()
    {
        return _Filler215.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller215AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler215 = value;
    }
    
    // Standard Getter
    public int GetD49ReportNo()
    {
        return _D49ReportNo;
    }
    
    // Standard Setter
    public void SetD49ReportNo(int value)
    {
        _D49ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD49ReportNoAsString()
    {
        return _D49ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD49ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D49ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller216()
    {
        return _Filler216;
    }
    
    // Standard Setter
    public void SetFiller216(string value)
    {
        _Filler216 = value;
    }
    
    // Get<>AsString()
    public string GetFiller216AsString()
    {
        return _Filler216.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller216AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler216 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}