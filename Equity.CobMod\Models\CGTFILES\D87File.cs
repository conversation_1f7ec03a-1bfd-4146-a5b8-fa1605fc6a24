using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D87File Data Structure

public class D87File
{
    private static int _size = 128;
    // [DEBUG] Class: D87File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler263, is_external=, is_static_class=False, static_prefix=
    private string _Filler263 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD87FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler263.PadRight(128));
        
        return result.ToString();
    }
    
    public void SetD87FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 128 <= data.Length)
        {
            string extracted = data.Substring(offset, 128).Trim();
            SetFiller263(extracted);
        }
        offset += 128;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD87FileAsString();
    }
    // Set<>String Override function
    public void SetD87File(string value)
    {
        SetD87FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller263()
    {
        return _Filler263;
    }
    
    // Standard Setter
    public void SetFiller263(string value)
    {
        _Filler263 = value;
    }
    
    // Get<>AsString()
    public string GetFiller263AsString()
    {
        return _Filler263.PadRight(128);
    }
    
    // Set<>AsString()
    public void SetFiller263AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler263 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}