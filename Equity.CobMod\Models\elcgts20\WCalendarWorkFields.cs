using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WCalendarWorkFields Data Structure

public class WCalendarWorkFields
{
    private static int _size = 19;
    // [DEBUG] Class: WCalendarWorkFields, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WCalendarTotalDays, is_external=, is_static_class=False, static_prefix=
    private int _WCalendarTotalDays =0;
    
    
    
    
    // [DEBUG] Field: WCalendarBusinessDays, is_external=, is_static_class=False, static_prefix=
    private int _WCalendarBusinessDays =0;
    
    
    
    
    // [DEBUG] Field: WCalendarFromDate, is_external=, is_static_class=False, static_prefix=
    private string _WCalendarFromDate ="";
    
    
    
    
    // [DEBUG] Field: WCalendarToDate, is_external=, is_static_class=False, static_prefix=
    private string _WCalendarToDate ="";
    
    
    
    
    // [DEBUG] Field: WCalendarBusinessAsset, is_external=, is_static_class=False, static_prefix=
    private string _WCalendarBusinessAsset ="";
    
    
    
    
    
    // Serialization methods
    public string GetWCalendarWorkFieldsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WCalendarTotalDays.ToString().PadLeft(9, '0'));
        result.Append(_WCalendarBusinessDays.ToString().PadLeft(9, '0'));
        result.Append(_WCalendarFromDate.PadRight(0));
        result.Append(_WCalendarToDate.PadRight(0));
        result.Append(_WCalendarBusinessAsset.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWCalendarWorkFieldsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 9 <= data.Length)
        {
            string extracted = data.Substring(offset, 9).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWCalendarTotalDays(parsedInt);
        }
        offset += 9;
        if (offset + 9 <= data.Length)
        {
            string extracted = data.Substring(offset, 9).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWCalendarBusinessDays(parsedInt);
        }
        offset += 9;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWCalendarFromDate(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWCalendarToDate(extracted);
        }
        offset += 0;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWCalendarBusinessAsset(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWCalendarWorkFieldsAsString();
    }
    // Set<>String Override function
    public void SetWCalendarWorkFields(string value)
    {
        SetWCalendarWorkFieldsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWCalendarTotalDays()
    {
        return _WCalendarTotalDays;
    }
    
    // Standard Setter
    public void SetWCalendarTotalDays(int value)
    {
        _WCalendarTotalDays = value;
    }
    
    // Get<>AsString()
    public string GetWCalendarTotalDaysAsString()
    {
        return _WCalendarTotalDays.ToString().PadLeft(9, '0');
    }
    
    // Set<>AsString()
    public void SetWCalendarTotalDaysAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WCalendarTotalDays = parsed;
    }
    
    // Standard Getter
    public int GetWCalendarBusinessDays()
    {
        return _WCalendarBusinessDays;
    }
    
    // Standard Setter
    public void SetWCalendarBusinessDays(int value)
    {
        _WCalendarBusinessDays = value;
    }
    
    // Get<>AsString()
    public string GetWCalendarBusinessDaysAsString()
    {
        return _WCalendarBusinessDays.ToString().PadLeft(9, '0');
    }
    
    // Set<>AsString()
    public void SetWCalendarBusinessDaysAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WCalendarBusinessDays = parsed;
    }
    
    // Standard Getter
    public string GetWCalendarFromDate()
    {
        return _WCalendarFromDate;
    }
    
    // Standard Setter
    public void SetWCalendarFromDate(string value)
    {
        _WCalendarFromDate = value;
    }
    
    // Get<>AsString()
    public string GetWCalendarFromDateAsString()
    {
        return _WCalendarFromDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWCalendarFromDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WCalendarFromDate = value;
    }
    
    // Standard Getter
    public string GetWCalendarToDate()
    {
        return _WCalendarToDate;
    }
    
    // Standard Setter
    public void SetWCalendarToDate(string value)
    {
        _WCalendarToDate = value;
    }
    
    // Get<>AsString()
    public string GetWCalendarToDateAsString()
    {
        return _WCalendarToDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWCalendarToDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WCalendarToDate = value;
    }
    
    // Standard Getter
    public string GetWCalendarBusinessAsset()
    {
        return _WCalendarBusinessAsset;
    }
    
    // Standard Setter
    public void SetWCalendarBusinessAsset(string value)
    {
        _WCalendarBusinessAsset = value;
    }
    
    // Get<>AsString()
    public string GetWCalendarBusinessAssetAsString()
    {
        return _WCalendarBusinessAsset.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWCalendarBusinessAssetAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WCalendarBusinessAsset = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
