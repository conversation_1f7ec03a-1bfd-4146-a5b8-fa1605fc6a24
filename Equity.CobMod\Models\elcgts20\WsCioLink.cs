using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WsCioLink Data Structure

public class WsCioLink
{
    private static int _size = 45;
    // [DEBUG] Class: WsCioLink, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsCioName, is_external=, is_static_class=False, static_prefix=
    private string _WsCioName ="?      ";
    
    
    
    
    // [DEBUG] Field: WsCioAct, is_external=, is_static_class=False, static_prefix=
    private string _WsCioAct ="";
    
    
    
    
    // [DEBUG] Field: WsCioRet, is_external=, is_static_class=False, static_prefix=
    private WsCioRet _WsCioRet = new WsCioRet();
    
    
    
    
    // [DEBUG] Field: Filler116, is_external=, is_static_class=False, static_prefix=
    private string _Filler116 ="ELCGTS20";
    
    
    
    
    // [DEBUG] Field: WsCioPgm, is_external=, is_static_class=False, static_prefix=
    private string _WsCioPgm ="ELCGCIO ";
    
    
    
    
    // [DEBUG] Field: WsDioPgm, is_external=, is_static_class=False, static_prefix=
    private string _WsDioPgm ="ELCGCIO ";
    
    
    
    
    // [DEBUG] Field: WsTioPgm, is_external=, is_static_class=False, static_prefix=
    private string _WsTioPgm ="ELCGTIO ";
    
    
    
    
    
    // Serialization methods
    public string GetWsCioLinkAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsCioName.PadRight(8));
        result.Append(_WsCioAct.PadRight(3));
        result.Append(_WsCioRet.GetWsCioRetAsString());
        result.Append(_Filler116.PadRight(8));
        result.Append(_WsCioPgm.PadRight(8));
        result.Append(_WsDioPgm.PadRight(8));
        result.Append(_WsTioPgm.PadRight(8));
        
        return result.ToString();
    }
    
    public void SetWsCioLinkAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWsCioName(extracted);
        }
        offset += 8;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetWsCioAct(extracted);
        }
        offset += 3;
        if (offset + 2 <= data.Length)
        {
            _WsCioRet.SetWsCioRetAsString(data.Substring(offset, 2));
        }
        else
        {
            _WsCioRet.SetWsCioRetAsString(data.Substring(offset));
        }
        offset += 2;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller116(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWsCioPgm(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWsDioPgm(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWsTioPgm(extracted);
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsCioLinkAsString();
    }
    // Set<>String Override function
    public void SetWsCioLink(string value)
    {
        SetWsCioLinkAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsCioName()
    {
        return _WsCioName;
    }
    
    // Standard Setter
    public void SetWsCioName(string value)
    {
        _WsCioName = value;
    }
    
    // Get<>AsString()
    public string GetWsCioNameAsString()
    {
        return _WsCioName.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWsCioNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsCioName = value;
    }
    
    // Standard Getter
    public string GetWsCioAct()
    {
        return _WsCioAct;
    }
    
    // Standard Setter
    public void SetWsCioAct(string value)
    {
        _WsCioAct = value;
    }
    
    // Get<>AsString()
    public string GetWsCioActAsString()
    {
        return _WsCioAct.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetWsCioActAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsCioAct = value;
    }
    
    // Standard Getter
    public WsCioRet GetWsCioRet()
    {
        return _WsCioRet;
    }
    
    // Standard Setter
    public void SetWsCioRet(WsCioRet value)
    {
        _WsCioRet = value;
    }
    
    // Get<>AsString()
    public string GetWsCioRetAsString()
    {
        return _WsCioRet != null ? _WsCioRet.GetWsCioRetAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWsCioRetAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WsCioRet == null)
        {
            _WsCioRet = new WsCioRet();
        }
        _WsCioRet.SetWsCioRetAsString(value);
    }
    
    // Standard Getter
    public string GetFiller116()
    {
        return _Filler116;
    }
    
    // Standard Setter
    public void SetFiller116(string value)
    {
        _Filler116 = value;
    }
    
    // Get<>AsString()
    public string GetFiller116AsString()
    {
        return _Filler116.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller116AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler116 = value;
    }
    
    // Standard Getter
    public string GetWsCioPgm()
    {
        return _WsCioPgm;
    }
    
    // Standard Setter
    public void SetWsCioPgm(string value)
    {
        _WsCioPgm = value;
    }
    
    // Get<>AsString()
    public string GetWsCioPgmAsString()
    {
        return _WsCioPgm.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWsCioPgmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsCioPgm = value;
    }
    
    // Standard Getter
    public string GetWsDioPgm()
    {
        return _WsDioPgm;
    }
    
    // Standard Setter
    public void SetWsDioPgm(string value)
    {
        _WsDioPgm = value;
    }
    
    // Get<>AsString()
    public string GetWsDioPgmAsString()
    {
        return _WsDioPgm.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWsDioPgmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsDioPgm = value;
    }
    
    // Standard Getter
    public string GetWsTioPgm()
    {
        return _WsTioPgm;
    }
    
    // Standard Setter
    public void SetWsTioPgm(string value)
    {
        _WsTioPgm = value;
    }
    
    // Get<>AsString()
    public string GetWsTioPgmAsString()
    {
        return _WsTioPgm.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWsTioPgmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsTioPgm = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWsCioRet(string value)
    {
        _WsCioRet.SetWsCioRetAsString(value);
    }
    // Nested Class: WsCioRet
    public class WsCioRet
    {
        private static int _size = 2;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WsCioRet1, is_external=, is_static_class=False, static_prefix=
        private string _WsCioRet1 ="";
        
        
        
        
        // [DEBUG] Field: WsCioRet2, is_external=, is_static_class=False, static_prefix=
        private string _WsCioRet2 ="";
        
        
        
        
    public WsCioRet() {}
    
    public WsCioRet(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWsCioRet1(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsCioRet2(data.Substring(offset, 1).Trim());
        offset += 1;
        
    }
    
    // Serialization methods
    public string GetWsCioRetAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsCioRet1.PadRight(1));
        result.Append(_WsCioRet2.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWsCioRetAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsCioRet1(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsCioRet2(extracted);
        }
        offset += 1;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsCioRet1()
    {
        return _WsCioRet1;
    }
    
    // Standard Setter
    public void SetWsCioRet1(string value)
    {
        _WsCioRet1 = value;
    }
    
    // Get<>AsString()
    public string GetWsCioRet1AsString()
    {
        return _WsCioRet1.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsCioRet1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsCioRet1 = value;
    }
    
    // Standard Getter
    public string GetWsCioRet2()
    {
        return _WsCioRet2;
    }
    
    // Standard Setter
    public void SetWsCioRet2(string value)
    {
        _WsCioRet2 = value;
    }
    
    // Get<>AsString()
    public string GetWsCioRet2AsString()
    {
        return _WsCioRet2.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsCioRet2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsCioRet2 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}