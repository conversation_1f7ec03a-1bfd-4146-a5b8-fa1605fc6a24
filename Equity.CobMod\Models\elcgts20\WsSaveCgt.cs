using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WsSaveCgt Data Structure

public class WsSaveCgt
{
    private static int _size = 270;
    // [DEBUG] Class: WsSaveCgt, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: FixedPortion, is_external=, is_static_class=False, static_prefix=
    private string _FixedPortion ="";
    
    
    
    
    // [DEBUG] Field: Filler348, is_external=, is_static_class=False, static_prefix=
    private string _Filler348 ="";
    
    
    
    
    // [DEBUG] Field: BalanceCosts, is_external=, is_static_class=False, static_prefix=
    private string[] _BalanceCosts = new string[200];
    
    
    
    
    
    // Serialization methods
    public string GetWsSaveCgtAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_FixedPortion.PadRight(270));
        result.Append(_Filler348.PadRight(0));
        for (int i = 0; i < 200; i++)
        {
            result.Append(_BalanceCosts[i].PadRight(0));
        }
        
        return result.ToString();
    }
    
    public void SetWsSaveCgtAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 270 <= data.Length)
        {
            string extracted = data.Substring(offset, 270).Trim();
            SetFixedPortion(extracted);
        }
        offset += 270;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller348(extracted);
        }
        offset += 0;
        for (int i = 0; i < 200; i++)
        {
            if (offset + 0 > data.Length) break;
            string val = data.Substring(offset, 0);
            
            _BalanceCosts[i] = val.Trim();
            offset += 0;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsSaveCgtAsString();
    }
    // Set<>String Override function
    public void SetWsSaveCgt(string value)
    {
        SetWsSaveCgtAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFixedPortion()
    {
        return _FixedPortion;
    }
    
    // Standard Setter
    public void SetFixedPortion(string value)
    {
        _FixedPortion = value;
    }
    
    // Get<>AsString()
    public string GetFixedPortionAsString()
    {
        return _FixedPortion.PadRight(270);
    }
    
    // Set<>AsString()
    public void SetFixedPortionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _FixedPortion = value;
    }
    
    // Standard Getter
    public string GetFiller348()
    {
        return _Filler348;
    }
    
    // Standard Setter
    public void SetFiller348(string value)
    {
        _Filler348 = value;
    }
    
    // Get<>AsString()
    public string GetFiller348AsString()
    {
        return _Filler348.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller348AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler348 = value;
    }
    
    // Array Accessors for BalanceCosts
    public string GetBalanceCostsAt(int index)
    {
        return _BalanceCosts[index];
    }
    
    public void SetBalanceCostsAt(int index, string value)
    {
        _BalanceCosts[index] = value;
    }
    
    public string GetBalanceCostsAsStringAt(int index)
    {
        return _BalanceCosts[index].PadRight(0);
    }
    
    public void SetBalanceCostsAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _BalanceCosts[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetBalanceCosts()
    {
        return _BalanceCosts != null && _BalanceCosts.Length > 0
        ? _BalanceCosts[0]
        : default(string);
    }
    
    public void SetBalanceCosts(string value)
    {
        if (_BalanceCosts == null || _BalanceCosts.Length == 0)
        _BalanceCosts = new string[1];
        _BalanceCosts[0] = value;
    }
    
    public string GetBalanceCostsAsString()
    {
        return _BalanceCosts != null && _BalanceCosts.Length > 0
        ? _BalanceCosts[0].ToString()
        : string.Empty;
    }
    
    public void SetBalanceCostsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_BalanceCosts == null || _BalanceCosts.Length == 0)
        _BalanceCosts = new string[1];
        
        _BalanceCosts[0] = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
