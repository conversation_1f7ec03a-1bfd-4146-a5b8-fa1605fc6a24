using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdailyDTO
{// DTO class representing WInitialisedRecord Data Structure

public class WInitialisedRecord
{
    private static int _size = 66;
    // [DEBUG] Class: WInitialisedRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler30, is_external=, is_static_class=False, static_prefix=
    private string _Filler30 ="";
    
    
    
    
    // [DEBUG] Field: IFundCode, is_external=, is_static_class=False, static_prefix=
    private string _IFundCode ="";
    
    
    
    
    // [DEBUG] Field: Filler31, is_external=, is_static_class=False, static_prefix=
    private string _Filler31 =",";
    
    
    
    
    // [DEBUG] Field: ISedolCode, is_external=, is_static_class=False, static_prefix=
    private string _ISedolCode ="";
    
    
    
    
    // [DEBUG] Field: Filler32, is_external=, is_static_class=False, static_prefix=
    private string _Filler32 ="\",";
    
    
    
    
    // [DEBUG] Field: IQuantity, is_external=, is_static_class=False, static_prefix=
    private decimal _IQuantity =0;
    
    
    
    
    // [DEBUG] Field: Filler33, is_external=, is_static_class=False, static_prefix=
    private string _Filler33 =",\"";
    
    
    
    
    // [DEBUG] Field: IBargainDate, is_external=, is_static_class=False, static_prefix=
    private string _IBargainDate ="";
    
    
    
    
    // [DEBUG] Field: Filler34, is_external=, is_static_class=False, static_prefix=
    private string _Filler34 =",";
    
    
    
    
    // [DEBUG] Field: ISettlementDate, is_external=, is_static_class=False, static_prefix=
    private string _ISettlementDate ="";
    
    
    
    
    // [DEBUG] Field: Filler35, is_external=, is_static_class=False, static_prefix=
    private string _Filler35 =",";
    
    
    
    
    // [DEBUG] Field: ITransactionCategory, is_external=, is_static_class=False, static_prefix=
    private string _ITransactionCategory ="";
    
    
    
    
    // [DEBUG] Field: Filler36, is_external=, is_static_class=False, static_prefix=
    private string _Filler36 ="\",";
    
    
    
    
    // [DEBUG] Field: IValue, is_external=, is_static_class=False, static_prefix=
    private decimal _IValue =0;
    
    
    
    
    // [DEBUG] Field: Filler37, is_external=, is_static_class=False, static_prefix=
    private string _Filler37 =",\"";
    
    
    
    
    // [DEBUG] Field: IContractNumber, is_external=, is_static_class=False, static_prefix=
    private string _IContractNumber ="";
    
    
    
    
    // [DEBUG] Field: Filler38, is_external=, is_static_class=False, static_prefix=
    private string _Filler38 ="";
    
    
    
    
    
    // Serialization methods
    public string GetWInitialisedRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler30.PadRight(0));
        result.Append(_IFundCode.PadRight(0));
        result.Append(_Filler31.PadRight(0));
        result.Append(_ISedolCode.PadRight(0));
        result.Append(_Filler32.PadRight(0));
        result.Append(_IQuantity.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler33.PadRight(0));
        result.Append(_IBargainDate.PadRight(10));
        result.Append(_Filler34.PadRight(0));
        result.Append(_ISettlementDate.PadRight(10));
        result.Append(_Filler35.PadRight(0));
        result.Append(_ITransactionCategory.PadRight(0));
        result.Append(_Filler36.PadRight(0));
        result.Append(_IValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler37.PadRight(0));
        result.Append(_IContractNumber.PadRight(10));
        result.Append(_Filler38.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetWInitialisedRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller30(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetIFundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller31(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetISedolCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller32(extracted);
        }
        offset += 0;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetIQuantity(parsedDec);
        }
        offset += 18;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller33(extracted);
        }
        offset += 0;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetIBargainDate(extracted);
        }
        offset += 10;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller34(extracted);
        }
        offset += 0;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetISettlementDate(extracted);
        }
        offset += 10;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller35(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetITransactionCategory(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller36(extracted);
        }
        offset += 0;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetIValue(parsedDec);
        }
        offset += 18;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller37(extracted);
        }
        offset += 0;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetIContractNumber(extracted);
        }
        offset += 10;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller38(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWInitialisedRecordAsString();
    }
    // Set<>String Override function
    public void SetWInitialisedRecord(string value)
    {
        SetWInitialisedRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller30()
    {
        return _Filler30;
    }
    
    // Standard Setter
    public void SetFiller30(string value)
    {
        _Filler30 = value;
    }
    
    // Get<>AsString()
    public string GetFiller30AsString()
    {
        return _Filler30.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller30AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler30 = value;
    }
    
    // Standard Getter
    public string GetIFundCode()
    {
        return _IFundCode;
    }
    
    // Standard Setter
    public void SetIFundCode(string value)
    {
        _IFundCode = value;
    }
    
    // Get<>AsString()
    public string GetIFundCodeAsString()
    {
        return _IFundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetIFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _IFundCode = value;
    }
    
    // Standard Getter
    public string GetFiller31()
    {
        return _Filler31;
    }
    
    // Standard Setter
    public void SetFiller31(string value)
    {
        _Filler31 = value;
    }
    
    // Get<>AsString()
    public string GetFiller31AsString()
    {
        return _Filler31.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller31AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler31 = value;
    }
    
    // Standard Getter
    public string GetISedolCode()
    {
        return _ISedolCode;
    }
    
    // Standard Setter
    public void SetISedolCode(string value)
    {
        _ISedolCode = value;
    }
    
    // Get<>AsString()
    public string GetISedolCodeAsString()
    {
        return _ISedolCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetISedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ISedolCode = value;
    }
    
    // Standard Getter
    public string GetFiller32()
    {
        return _Filler32;
    }
    
    // Standard Setter
    public void SetFiller32(string value)
    {
        _Filler32 = value;
    }
    
    // Get<>AsString()
    public string GetFiller32AsString()
    {
        return _Filler32.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller32AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler32 = value;
    }
    
    // Standard Getter
    public decimal GetIQuantity()
    {
        return _IQuantity;
    }
    
    // Standard Setter
    public void SetIQuantity(decimal value)
    {
        _IQuantity = value;
    }
    
    // Get<>AsString()
    public string GetIQuantityAsString()
    {
        return _IQuantity.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetIQuantityAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _IQuantity = parsed;
    }
    
    // Standard Getter
    public string GetFiller33()
    {
        return _Filler33;
    }
    
    // Standard Setter
    public void SetFiller33(string value)
    {
        _Filler33 = value;
    }
    
    // Get<>AsString()
    public string GetFiller33AsString()
    {
        return _Filler33.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller33AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler33 = value;
    }
    
    // Standard Getter
    public string GetIBargainDate()
    {
        return _IBargainDate;
    }
    
    // Standard Setter
    public void SetIBargainDate(string value)
    {
        _IBargainDate = value;
    }
    
    // Get<>AsString()
    public string GetIBargainDateAsString()
    {
        return _IBargainDate.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetIBargainDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _IBargainDate = value;
    }
    
    // Standard Getter
    public string GetFiller34()
    {
        return _Filler34;
    }
    
    // Standard Setter
    public void SetFiller34(string value)
    {
        _Filler34 = value;
    }
    
    // Get<>AsString()
    public string GetFiller34AsString()
    {
        return _Filler34.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller34AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler34 = value;
    }
    
    // Standard Getter
    public string GetISettlementDate()
    {
        return _ISettlementDate;
    }
    
    // Standard Setter
    public void SetISettlementDate(string value)
    {
        _ISettlementDate = value;
    }
    
    // Get<>AsString()
    public string GetISettlementDateAsString()
    {
        return _ISettlementDate.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetISettlementDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ISettlementDate = value;
    }
    
    // Standard Getter
    public string GetFiller35()
    {
        return _Filler35;
    }
    
    // Standard Setter
    public void SetFiller35(string value)
    {
        _Filler35 = value;
    }
    
    // Get<>AsString()
    public string GetFiller35AsString()
    {
        return _Filler35.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller35AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler35 = value;
    }
    
    // Standard Getter
    public string GetITransactionCategory()
    {
        return _ITransactionCategory;
    }
    
    // Standard Setter
    public void SetITransactionCategory(string value)
    {
        _ITransactionCategory = value;
    }
    
    // Get<>AsString()
    public string GetITransactionCategoryAsString()
    {
        return _ITransactionCategory.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetITransactionCategoryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ITransactionCategory = value;
    }
    
    // Standard Getter
    public string GetFiller36()
    {
        return _Filler36;
    }
    
    // Standard Setter
    public void SetFiller36(string value)
    {
        _Filler36 = value;
    }
    
    // Get<>AsString()
    public string GetFiller36AsString()
    {
        return _Filler36.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller36AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler36 = value;
    }
    
    // Standard Getter
    public decimal GetIValue()
    {
        return _IValue;
    }
    
    // Standard Setter
    public void SetIValue(decimal value)
    {
        _IValue = value;
    }
    
    // Get<>AsString()
    public string GetIValueAsString()
    {
        return _IValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetIValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _IValue = parsed;
    }
    
    // Standard Getter
    public string GetFiller37()
    {
        return _Filler37;
    }
    
    // Standard Setter
    public void SetFiller37(string value)
    {
        _Filler37 = value;
    }
    
    // Get<>AsString()
    public string GetFiller37AsString()
    {
        return _Filler37.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller37AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler37 = value;
    }
    
    // Standard Getter
    public string GetIContractNumber()
    {
        return _IContractNumber;
    }
    
    // Standard Setter
    public void SetIContractNumber(string value)
    {
        _IContractNumber = value;
    }
    
    // Get<>AsString()
    public string GetIContractNumberAsString()
    {
        return _IContractNumber.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetIContractNumberAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _IContractNumber = value;
    }
    
    // Standard Getter
    public string GetFiller38()
    {
        return _Filler38;
    }
    
    // Standard Setter
    public void SetFiller38(string value)
    {
        _Filler38 = value;
    }
    
    // Get<>AsString()
    public string GetFiller38AsString()
    {
        return _Filler38.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller38AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler38 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}