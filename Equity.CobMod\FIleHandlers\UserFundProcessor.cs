﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WK.UK.CCH.Equity.CobolDataTransformation;

namespace Legacy4.Equity.CobMod.FIleHandlers
{
    public static class UserFundProcessor
    {
        private static string _recordArea = string.Empty;

        public static string ReadNext(UserInfoDA userInfoDA, UserFundsDA userFundsDA, int userID , bool _isFirstTime)
        {
            if (_isFirstTime)
            {
                // Return header record derived from UserInfoDA
                _recordArea = GetUserInfo(userInfoDA, userID);
                _isFirstTime = false;
            }
            else
            {
                // Return user fund record derived from UserFundsDA
                _recordArea = userFundsDA.ReadNext();
                _recordArea = SetupCobolRecord(_recordArea);
            }
            return _recordArea;
        }

        public static string ReadRecord(string fileRecordArea, UserInfoDA userInfoDA, UserFundsDA userFundsDA, int userID)
        {
            string fundCode = fileRecordArea.Substring(0, 4);

            switch (fundCode)
            {
                case "\0\0\0\0": // Low values
                case "\uFFFF\uFFFF\uFFFF\uFFFF": // High values
                                                 // Prevent crash
                    return new string(' ', fileRecordArea.Length);

                case "    ": // Spaces (Header record derived from UserInfoDA)
                    return GetUserInfo(userInfoDA, userID);

                default:
                    // Return user fund record derived from UserFundsDA
                    string recordArea = fundCode;
                    recordArea = userFundsDA.ReadByFundCode(recordArea);
                    return SetupCobolRecord(recordArea);
            }
        }

        private static string GetUserInfo(UserInfoDA userInfoDA, int userID)
        {
            string userInfoRecord = userInfoDA.GetUserInfo(userID);
            string formattedRecord = FormatUserInfo(userInfoRecord);
            return formattedRecord;
        }

        private static string SetupCobolRecord(string userFundsRecord)
        {
            if (string.IsNullOrEmpty(userFundsRecord))
            {
                return new string(' ', userFundsRecord.Length);
            }

            string formattedRecord = FormatUserFunds(userFundsRecord);

            // Business rule for D37-CALC-REQUEST-FLAG
            if (formattedRecord.Contains("0"))
            {
                formattedRecord = formattedRecord.Replace("0", "2");
            }
            else
            {
                formattedRecord = formattedRecord.Replace("1", "1");
            }

            return formattedRecord;
        }

        private static string FormatUserInfo(string userInfoRecord)
        {
            // Simulating COBOL MOVE CORR operation
            return userInfoRecord.PadRight(37);
        }

        private static string FormatUserFunds(string userFundsRecord)
        {
            // Simulating COBOL MOVE CORR operation for D37-RECORD-FORMAT-2
            return userFundsRecord.PadRight(37);
        }
    }
}
