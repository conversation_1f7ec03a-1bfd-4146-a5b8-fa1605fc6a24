using System;
using System.Text;
using EquityProject.CgtinvrtDTO;
namespace EquityProject.CgtinvrtPGM
{
    // Cgtinvrt Class Definition
    
    //Cgtinvrt Class Constructor
    public class Cgtinvrt
    {
        // Declare Cgtinvrt Class private variablesf
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();
        // Represents the COBOL W-HV VALUE +255 (HIGH-VALUES for a single byte)
        private const int HighValue = 255; // 0xFF

        // Represents the expected key length from COBOL OCCURS 100
        private const int KeyLength = 100;
        // Declare Cgtinvrt Class getters setters
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }



        // Run() method
        public void Run(Gvar gvar, Ivar ivar)
        {
            // Implement main program logic here
    
        }

        // CallSub() method
        public void CallSub(Ivar ivar)
        {

            // Implement subroutine call logic here
        }

        /// <summary>
        /// Inverts the provided byte array key in place.
        /// Corresponds to the PROCEDURE DIVISION of CGTINVRT.
        /// </summary>
        /// <param name="key">The 100-byte array to be inverted. It will be modified directly.</param>
        /// <exception cref="ArgumentNullException">Thrown if the key is null.</exception>
        /// <exception cref="ArgumentException">Thrown if the key is not exactly 100 bytes long.</exception>
        public void MainProc(Gvar gvar, Ivar ivar)

{
            // Corresponds to: PERFORM A-CONVERT-CHAR
            // VARYING CHAR-SUB FROM 1 BY 1
            // UNTIL   CHAR-SUB > 100.
            // Note: C# uses 0-based indexing, COBOL uses 1-based.
            // The loop iterates 100 times.
            char[] key = ivar.GetLKeyAsString().ToCharArray();
            // --- Input Validation (Good practice in C#) ---
            if (key == null)
            {
                throw new ArgumentNullException(nameof(key), "Input key array cannot be null.");
            }
            if (key.Length != KeyLength)
            {
                throw new ArgumentException($"Input key array must be exactly {KeyLength} bytes long.", nameof(key));
            }
            AConvertChar(gvar, ivar,key);

            ivar.SetLKeyAsString(new string(key)); // Update the Ivar with the modified key

            // EXIT PROGRAM (implicit return at end of method)
        }

      
        public void AConvertChar(Gvar gvar, Ivar ivar, char[] key)
        {
            // Corresponds to:
            // PERFORM A-CONVERT-CHAR
            //         VARYING CHAR-SUB FROM 1 BY 1
            //         UNTIL   CHAR-SUB > 100.
            // Note: C# loop is 0-based, COBOL loop is 1-based (CHAR-SUB goes 1 to 100)
            for (int i = 0; i < KeyLength; i++) // i goes 0 to 99 (accessing key[0] to key[99])
            {
                // --- Inside the loop (A-CONVERT-CHAR paragraph) ---

                // MOVE L-CHAR(CHAR-SUB) TO W-CHAR.
                // (Implicitly get the byte value)
                char currentByte = key[i];
                // COMPUTE W-BIN-CHAR = (W-HV - W-BIN-CHAR).
                // Perform subtraction. Need to cast back to byte as arithmetic promotes to int.
                char invertedByte = (char)(HighValue - currentByte);

                // MOVE W-CHAR TO L-CHAR(CHAR-SUB).
                // Store the inverted byte back into the original array

                key[i] = invertedByte;
            }
        }

        // Methods representing sections
    }
}
