using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WsDispEyMsg Data Structure

public class WsDispEyMsg
{
    private static int _size = 155;
    // [DEBUG] Class: WsDispEyMsg, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsDispEyMsgNo, is_external=, is_static_class=False, static_prefix=
    private int _WsDispEyMsgNo =0;
    
    
    
    
    // [DEBUG] Field: WsEy, is_external=, is_static_class=False, static_prefix=
    private string _WsEy =" ";
    
    
    
    
    // [DEBUG] Field: Filler480, is_external=, is_static_class=False, static_prefix=
    private Filler480 _Filler480 = new Filler480();
    
    
    
    
    
    // Serialization methods
    public string GetWsDispEyMsgAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsDispEyMsgNo.ToString().PadLeft(5, '0'));
        result.Append(_WsEy.PadRight(75));
        result.Append(_Filler480.GetFiller480AsString());
        
        return result.ToString();
    }
    
    public void SetWsDispEyMsgAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWsDispEyMsgNo(parsedInt);
        }
        offset += 5;
        if (offset + 75 <= data.Length)
        {
            string extracted = data.Substring(offset, 75).Trim();
            SetWsEy(extracted);
        }
        offset += 75;
        if (offset + 75 <= data.Length)
        {
            _Filler480.SetFiller480AsString(data.Substring(offset, 75));
        }
        else
        {
            _Filler480.SetFiller480AsString(data.Substring(offset));
        }
        offset += 75;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsDispEyMsgAsString();
    }
    // Set<>String Override function
    public void SetWsDispEyMsg(string value)
    {
        SetWsDispEyMsgAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWsDispEyMsgNo()
    {
        return _WsDispEyMsgNo;
    }
    
    // Standard Setter
    public void SetWsDispEyMsgNo(int value)
    {
        _WsDispEyMsgNo = value;
    }
    
    // Get<>AsString()
    public string GetWsDispEyMsgNoAsString()
    {
        return _WsDispEyMsgNo.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWsDispEyMsgNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WsDispEyMsgNo = parsed;
    }
    
    // Standard Getter
    public string GetWsEy()
    {
        return _WsEy;
    }
    
    // Standard Setter
    public void SetWsEy(string value)
    {
        _WsEy = value;
    }
    
    // Get<>AsString()
    public string GetWsEyAsString()
    {
        return _WsEy.PadRight(75);
    }
    
    // Set<>AsString()
    public void SetWsEyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsEy = value;
    }
    
    // Standard Getter
    public Filler480 GetFiller480()
    {
        return _Filler480;
    }
    
    // Standard Setter
    public void SetFiller480(Filler480 value)
    {
        _Filler480 = value;
    }
    
    // Get<>AsString()
    public string GetFiller480AsString()
    {
        return _Filler480 != null ? _Filler480.GetFiller480AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller480AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler480 == null)
        {
            _Filler480 = new Filler480();
        }
        _Filler480.SetFiller480AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller480(string value)
    {
        _Filler480.SetFiller480AsString(value);
    }
    // Nested Class: Filler480
    public class Filler480
    {
        private static int _size = 75;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WsEyMsgType, is_external=, is_static_class=False, static_prefix=
        private string _WsEyMsgType ="";
        
        
        
        
        // [DEBUG] Field: WsEyMsg, is_external=, is_static_class=False, static_prefix=
        private string _WsEyMsg ="";
        
        
        
        
    public Filler480() {}
    
    public Filler480(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWsEyMsgType(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWsEyMsg(data.Substring(offset, 74).Trim());
        offset += 74;
        
    }
    
    // Serialization methods
    public string GetFiller480AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsEyMsgType.PadRight(1));
        result.Append(_WsEyMsg.PadRight(74));
        
        return result.ToString();
    }
    
    public void SetFiller480AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsEyMsgType(extracted);
        }
        offset += 1;
        if (offset + 74 <= data.Length)
        {
            string extracted = data.Substring(offset, 74).Trim();
            SetWsEyMsg(extracted);
        }
        offset += 74;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsEyMsgType()
    {
        return _WsEyMsgType;
    }
    
    // Standard Setter
    public void SetWsEyMsgType(string value)
    {
        _WsEyMsgType = value;
    }
    
    // Get<>AsString()
    public string GetWsEyMsgTypeAsString()
    {
        return _WsEyMsgType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsEyMsgTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsEyMsgType = value;
    }
    
    // Standard Getter
    public string GetWsEyMsg()
    {
        return _WsEyMsg;
    }
    
    // Standard Setter
    public void SetWsEyMsg(string value)
    {
        _WsEyMsg = value;
    }
    
    // Get<>AsString()
    public string GetWsEyMsgAsString()
    {
        return _WsEyMsg.PadRight(74);
    }
    
    // Set<>AsString()
    public void SetWsEyMsgAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsEyMsg = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}