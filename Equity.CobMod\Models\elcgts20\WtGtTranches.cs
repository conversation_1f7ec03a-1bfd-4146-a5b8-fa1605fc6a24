using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtGtTranches Data Structure

public class WtGtTranches
{
    private static int _size = 61;
    // [DEBUG] Class: WtGtTranches, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler445, is_external=, is_static_class=False, static_prefix=
    private string _Filler445 ="GRP-TRF-TRANCHES";
    
    
    
    
    // [DEBUG] Field: WtgtMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtgtMaxTableSize =4000;
    
    
    
    
    // [DEBUG] Field: WtgtOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtgtOccurs =4000;
    
    
    
    
    // [DEBUG] Field: WtgtTable, is_external=, is_static_class=False, static_prefix=
    private WtgtTable _WtgtTable = new WtgtTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtGtTranchesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler445.PadRight(16));
        result.Append(_WtgtMaxTableSize.ToString().PadLeft(4, '0'));
        result.Append(_WtgtOccurs.ToString().PadLeft(4, '0'));
        result.Append(_WtgtTable.GetWtgtTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtGtTranchesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller445(extracted);
        }
        offset += 16;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtgtMaxTableSize(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtgtOccurs(parsedInt);
        }
        offset += 4;
        if (offset + 37 <= data.Length)
        {
            _WtgtTable.SetWtgtTableAsString(data.Substring(offset, 37));
        }
        else
        {
            _WtgtTable.SetWtgtTableAsString(data.Substring(offset));
        }
        offset += 37;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtGtTranchesAsString();
    }
    // Set<>String Override function
    public void SetWtGtTranches(string value)
    {
        SetWtGtTranchesAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller445()
    {
        return _Filler445;
    }
    
    // Standard Setter
    public void SetFiller445(string value)
    {
        _Filler445 = value;
    }
    
    // Get<>AsString()
    public string GetFiller445AsString()
    {
        return _Filler445.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller445AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler445 = value;
    }
    
    // Standard Getter
    public int GetWtgtMaxTableSize()
    {
        return _WtgtMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtgtMaxTableSize(int value)
    {
        _WtgtMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtgtMaxTableSizeAsString()
    {
        return _WtgtMaxTableSize.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtgtMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtgtMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtgtOccurs()
    {
        return _WtgtOccurs;
    }
    
    // Standard Setter
    public void SetWtgtOccurs(int value)
    {
        _WtgtOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtgtOccursAsString()
    {
        return _WtgtOccurs.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtgtOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtgtOccurs = parsed;
    }
    
    // Standard Getter
    public WtgtTable GetWtgtTable()
    {
        return _WtgtTable;
    }
    
    // Standard Setter
    public void SetWtgtTable(WtgtTable value)
    {
        _WtgtTable = value;
    }
    
    // Get<>AsString()
    public string GetWtgtTableAsString()
    {
        return _WtgtTable != null ? _WtgtTable.GetWtgtTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtgtTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtgtTable == null)
        {
            _WtgtTable = new WtgtTable();
        }
        _WtgtTable.SetWtgtTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtgtTable(string value)
    {
        _WtgtTable.SetWtgtTableAsString(value);
    }
    // Nested Class: WtgtTable
    public class WtgtTable
    {
        private static int _size = 37;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtgtElement, is_external=, is_static_class=False, static_prefix=
        private WtgtTable.WtgtElement[] _WtgtElement = new WtgtTable.WtgtElement[4000];
        
        public void InitializeWtgtElementArray()
        {
            for (int i = 0; i < 4000; i++)
            {
                _WtgtElement[i] = new WtgtTable.WtgtElement();
            }
        }
        
        
        
    public WtgtTable() {}
    
    public WtgtTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWtgtElementArray();
        for (int i = 0; i < 4000; i++)
        {
            _WtgtElement[i].SetWtgtElementAsString(data.Substring(offset, 37));
            offset += 37;
        }
        
    }
    
    // Serialization methods
    public string GetWtgtTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 4000; i++)
        {
            result.Append(_WtgtElement[i].GetWtgtElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtgtTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 4000; i++)
        {
            if (offset + 37 > data.Length) break;
            string val = data.Substring(offset, 37);
            
            _WtgtElement[i].SetWtgtElementAsString(val);
            offset += 37;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtgtElement
    public WtgtElement GetWtgtElementAt(int index)
    {
        return _WtgtElement[index];
    }
    
    public void SetWtgtElementAt(int index, WtgtElement value)
    {
        _WtgtElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtgtElement GetWtgtElement()
    {
        return _WtgtElement != null && _WtgtElement.Length > 0
        ? _WtgtElement[0]
        : new WtgtElement();
    }
    
    public void SetWtgtElement(WtgtElement value)
    {
        if (_WtgtElement == null || _WtgtElement.Length == 0)
        _WtgtElement = new WtgtElement[1];
        _WtgtElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtgtElement
    public class WtgtElement
    {
        private static int _size = 37;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtgtFmBalIndex, is_external=, is_static_class=False, static_prefix=
        private string _WtgtFmBalIndex ="";
        
        
        
        
        // [DEBUG] Field: WtgtTbIndex, is_external=, is_static_class=False, static_prefix=
        private string _WtgtTbIndex ="";
        
        
        
        
        // [DEBUG] Field: WtgtDates, is_external=, is_static_class=False, static_prefix=
        private WtgtElement.WtgtDates _WtgtDates = new WtgtElement.WtgtDates();
        
        
        
        
        // [DEBUG] Field: WtgtTaperUnitsYtd, is_external=, is_static_class=False, static_prefix=
        private decimal _WtgtTaperUnitsYtd =0;
        
        
        
        
    public WtgtElement() {}
    
    public WtgtElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtgtFmBalIndex(data.Substring(offset, 4).Trim());
        offset += 4;
        SetWtgtTbIndex(data.Substring(offset, 4).Trim());
        offset += 4;
        _WtgtDates.SetWtgtDatesAsString(data.Substring(offset, WtgtDates.GetSize()));
        offset += 16;
        SetWtgtTaperUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        
    }
    
    // Serialization methods
    public string GetWtgtElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtgtFmBalIndex.PadRight(4));
        result.Append(_WtgtTbIndex.PadRight(4));
        result.Append(_WtgtDates.GetWtgtDatesAsString());
        result.Append(_WtgtTaperUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        
        return result.ToString();
    }
    
    public void SetWtgtElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtgtFmBalIndex(extracted);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtgtTbIndex(extracted);
        }
        offset += 4;
        if (offset + 16 <= data.Length)
        {
            _WtgtDates.SetWtgtDatesAsString(data.Substring(offset, 16));
        }
        else
        {
            _WtgtDates.SetWtgtDatesAsString(data.Substring(offset));
        }
        offset += 16;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtgtTaperUnitsYtd(parsedDec);
        }
        offset += 13;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtgtFmBalIndex()
    {
        return _WtgtFmBalIndex;
    }
    
    // Standard Setter
    public void SetWtgtFmBalIndex(string value)
    {
        _WtgtFmBalIndex = value;
    }
    
    // Get<>AsString()
    public string GetWtgtFmBalIndexAsString()
    {
        return _WtgtFmBalIndex.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtgtFmBalIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtgtFmBalIndex = value;
    }
    
    // Standard Getter
    public string GetWtgtTbIndex()
    {
        return _WtgtTbIndex;
    }
    
    // Standard Setter
    public void SetWtgtTbIndex(string value)
    {
        _WtgtTbIndex = value;
    }
    
    // Get<>AsString()
    public string GetWtgtTbIndexAsString()
    {
        return _WtgtTbIndex.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtgtTbIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtgtTbIndex = value;
    }
    
    // Standard Getter
    public WtgtDates GetWtgtDates()
    {
        return _WtgtDates;
    }
    
    // Standard Setter
    public void SetWtgtDates(WtgtDates value)
    {
        _WtgtDates = value;
    }
    
    // Get<>AsString()
    public string GetWtgtDatesAsString()
    {
        return _WtgtDates != null ? _WtgtDates.GetWtgtDatesAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtgtDatesAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtgtDates == null)
        {
            _WtgtDates = new WtgtDates();
        }
        _WtgtDates.SetWtgtDatesAsString(value);
    }
    
    // Standard Getter
    public decimal GetWtgtTaperUnitsYtd()
    {
        return _WtgtTaperUnitsYtd;
    }
    
    // Standard Setter
    public void SetWtgtTaperUnitsYtd(decimal value)
    {
        _WtgtTaperUnitsYtd = value;
    }
    
    // Get<>AsString()
    public string GetWtgtTaperUnitsYtdAsString()
    {
        return _WtgtTaperUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtgtTaperUnitsYtdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtgtTaperUnitsYtd = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtgtDates
    public class WtgtDates
    {
        private static int _size = 16;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtgtBargainDateCcyymmdd, is_external=, is_static_class=False, static_prefix=
        private WtgtDates.WtgtBargainDateCcyymmdd _WtgtBargainDateCcyymmdd = new WtgtDates.WtgtBargainDateCcyymmdd();
        
        
        
        
        // [DEBUG] Field: WtgtTaperDateCcyymmdd, is_external=, is_static_class=False, static_prefix=
        private WtgtDates.WtgtTaperDateCcyymmdd _WtgtTaperDateCcyymmdd = new WtgtDates.WtgtTaperDateCcyymmdd();
        
        
        
        
    public WtgtDates() {}
    
    public WtgtDates(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WtgtBargainDateCcyymmdd.SetWtgtBargainDateCcyymmddAsString(data.Substring(offset, WtgtBargainDateCcyymmdd.GetSize()));
        offset += 8;
        _WtgtTaperDateCcyymmdd.SetWtgtTaperDateCcyymmddAsString(data.Substring(offset, WtgtTaperDateCcyymmdd.GetSize()));
        offset += 8;
        
    }
    
    // Serialization methods
    public string GetWtgtDatesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtgtBargainDateCcyymmdd.GetWtgtBargainDateCcyymmddAsString());
        result.Append(_WtgtTaperDateCcyymmdd.GetWtgtTaperDateCcyymmddAsString());
        
        return result.ToString();
    }
    
    public void SetWtgtDatesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            _WtgtBargainDateCcyymmdd.SetWtgtBargainDateCcyymmddAsString(data.Substring(offset, 8));
        }
        else
        {
            _WtgtBargainDateCcyymmdd.SetWtgtBargainDateCcyymmddAsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _WtgtTaperDateCcyymmdd.SetWtgtTaperDateCcyymmddAsString(data.Substring(offset, 8));
        }
        else
        {
            _WtgtTaperDateCcyymmdd.SetWtgtTaperDateCcyymmddAsString(data.Substring(offset));
        }
        offset += 8;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WtgtBargainDateCcyymmdd GetWtgtBargainDateCcyymmdd()
    {
        return _WtgtBargainDateCcyymmdd;
    }
    
    // Standard Setter
    public void SetWtgtBargainDateCcyymmdd(WtgtBargainDateCcyymmdd value)
    {
        _WtgtBargainDateCcyymmdd = value;
    }
    
    // Get<>AsString()
    public string GetWtgtBargainDateCcyymmddAsString()
    {
        return _WtgtBargainDateCcyymmdd != null ? _WtgtBargainDateCcyymmdd.GetWtgtBargainDateCcyymmddAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtgtBargainDateCcyymmddAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtgtBargainDateCcyymmdd == null)
        {
            _WtgtBargainDateCcyymmdd = new WtgtBargainDateCcyymmdd();
        }
        _WtgtBargainDateCcyymmdd.SetWtgtBargainDateCcyymmddAsString(value);
    }
    
    // Standard Getter
    public WtgtTaperDateCcyymmdd GetWtgtTaperDateCcyymmdd()
    {
        return _WtgtTaperDateCcyymmdd;
    }
    
    // Standard Setter
    public void SetWtgtTaperDateCcyymmdd(WtgtTaperDateCcyymmdd value)
    {
        _WtgtTaperDateCcyymmdd = value;
    }
    
    // Get<>AsString()
    public string GetWtgtTaperDateCcyymmddAsString()
    {
        return _WtgtTaperDateCcyymmdd != null ? _WtgtTaperDateCcyymmdd.GetWtgtTaperDateCcyymmddAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtgtTaperDateCcyymmddAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtgtTaperDateCcyymmdd == null)
        {
            _WtgtTaperDateCcyymmdd = new WtgtTaperDateCcyymmdd();
        }
        _WtgtTaperDateCcyymmdd.SetWtgtTaperDateCcyymmddAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtgtBargainDateCcyymmdd
    public class WtgtBargainDateCcyymmdd
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtgtBargainDateCc, is_external=, is_static_class=False, static_prefix=
        private string _WtgtBargainDateCc ="";
        
        
        
        
        // [DEBUG] Field: WtgtBargainDateYymmdd, is_external=, is_static_class=False, static_prefix=
        private string _WtgtBargainDateYymmdd ="";
        
        
        
        
    public WtgtBargainDateCcyymmdd() {}
    
    public WtgtBargainDateCcyymmdd(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtgtBargainDateCc(data.Substring(offset, 2).Trim());
        offset += 2;
        SetWtgtBargainDateYymmdd(data.Substring(offset, 6).Trim());
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetWtgtBargainDateCcyymmddAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtgtBargainDateCc.PadRight(2));
        result.Append(_WtgtBargainDateYymmdd.PadRight(6));
        
        return result.ToString();
    }
    
    public void SetWtgtBargainDateCcyymmddAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetWtgtBargainDateCc(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetWtgtBargainDateYymmdd(extracted);
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtgtBargainDateCc()
    {
        return _WtgtBargainDateCc;
    }
    
    // Standard Setter
    public void SetWtgtBargainDateCc(string value)
    {
        _WtgtBargainDateCc = value;
    }
    
    // Get<>AsString()
    public string GetWtgtBargainDateCcAsString()
    {
        return _WtgtBargainDateCc.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetWtgtBargainDateCcAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtgtBargainDateCc = value;
    }
    
    // Standard Getter
    public string GetWtgtBargainDateYymmdd()
    {
        return _WtgtBargainDateYymmdd;
    }
    
    // Standard Setter
    public void SetWtgtBargainDateYymmdd(string value)
    {
        _WtgtBargainDateYymmdd = value;
    }
    
    // Get<>AsString()
    public string GetWtgtBargainDateYymmddAsString()
    {
        return _WtgtBargainDateYymmdd.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetWtgtBargainDateYymmddAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtgtBargainDateYymmdd = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: WtgtTaperDateCcyymmdd
public class WtgtTaperDateCcyymmdd
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtgtTaperDateCc, is_external=, is_static_class=False, static_prefix=
    private string _WtgtTaperDateCc ="";
    
    
    
    
    // [DEBUG] Field: WtgtTaperDateYymmdd, is_external=, is_static_class=False, static_prefix=
    private string _WtgtTaperDateYymmdd ="";
    
    
    
    
public WtgtTaperDateCcyymmdd() {}

public WtgtTaperDateCcyymmdd(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtgtTaperDateCc(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWtgtTaperDateYymmdd(data.Substring(offset, 6).Trim());
    offset += 6;
    
}

// Serialization methods
public string GetWtgtTaperDateCcyymmddAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtgtTaperDateCc.PadRight(2));
    result.Append(_WtgtTaperDateYymmdd.PadRight(6));
    
    return result.ToString();
}

public void SetWtgtTaperDateCcyymmddAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWtgtTaperDateCc(extracted);
    }
    offset += 2;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetWtgtTaperDateYymmdd(extracted);
    }
    offset += 6;
}

// Getter and Setter methods

// Standard Getter
public string GetWtgtTaperDateCc()
{
    return _WtgtTaperDateCc;
}

// Standard Setter
public void SetWtgtTaperDateCc(string value)
{
    _WtgtTaperDateCc = value;
}

// Get<>AsString()
public string GetWtgtTaperDateCcAsString()
{
    return _WtgtTaperDateCc.PadRight(2);
}

// Set<>AsString()
public void SetWtgtTaperDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtgtTaperDateCc = value;
}

// Standard Getter
public string GetWtgtTaperDateYymmdd()
{
    return _WtgtTaperDateYymmdd;
}

// Standard Setter
public void SetWtgtTaperDateYymmdd(string value)
{
    _WtgtTaperDateYymmdd = value;
}

// Get<>AsString()
public string GetWtgtTaperDateYymmddAsString()
{
    return _WtgtTaperDateYymmdd.PadRight(6);
}

// Set<>AsString()
public void SetWtgtTaperDateYymmddAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtgtTaperDateYymmdd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}
}

}}