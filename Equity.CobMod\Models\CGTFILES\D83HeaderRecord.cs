using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D83HeaderRecord Data Structure

public class D83HeaderRecord
{
    private static int _size = 132;
    // [DEBUG] Class: D83HeaderRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler119, is_external=, is_static_class=False, static_prefix=
    private string _Filler119 ="";
    
    
    // 88-level condition checks for Filler119
    public bool IsD83HeaderFound()
    {
        if (this._Filler119 == "'00000000000MICRO CGT BALANCES HEADER    '") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D83ExtractDate, is_external=, is_static_class=False, static_prefix=
    private D83ExtractDate _D83ExtractDate = new D83ExtractDate();
    
    
    
    
    // [DEBUG] Field: D83HeaderId, is_external=, is_static_class=False, static_prefix=
    private string _D83HeaderId ="";
    
    
    
    
    // [DEBUG] Field: Filler120, is_external=, is_static_class=False, static_prefix=
    private string _Filler120 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD83HeaderRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler119.PadRight(40));
        result.Append(_D83ExtractDate.GetD83ExtractDateAsString());
        result.Append(_D83HeaderId.PadRight(30));
        result.Append(_Filler120.PadRight(62));
        
        return result.ToString();
    }
    
    public void SetD83HeaderRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetFiller119(extracted);
        }
        offset += 40;
        if (offset + 0 <= data.Length)
        {
            _D83ExtractDate.SetD83ExtractDateAsString(data.Substring(offset, 0));
        }
        else
        {
            _D83ExtractDate.SetD83ExtractDateAsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 30 <= data.Length)
        {
            string extracted = data.Substring(offset, 30).Trim();
            SetD83HeaderId(extracted);
        }
        offset += 30;
        if (offset + 62 <= data.Length)
        {
            string extracted = data.Substring(offset, 62).Trim();
            SetFiller120(extracted);
        }
        offset += 62;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD83HeaderRecordAsString();
    }
    // Set<>String Override function
    public void SetD83HeaderRecord(string value)
    {
        SetD83HeaderRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller119()
    {
        return _Filler119;
    }
    
    // Standard Setter
    public void SetFiller119(string value)
    {
        _Filler119 = value;
    }
    
    // Get<>AsString()
    public string GetFiller119AsString()
    {
        return _Filler119.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetFiller119AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler119 = value;
    }
    
    // Standard Getter
    public D83ExtractDate GetD83ExtractDate()
    {
        return _D83ExtractDate;
    }
    
    // Standard Setter
    public void SetD83ExtractDate(D83ExtractDate value)
    {
        _D83ExtractDate = value;
    }
    
    // Get<>AsString()
    public string GetD83ExtractDateAsString()
    {
        return _D83ExtractDate != null ? _D83ExtractDate.GetD83ExtractDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD83ExtractDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D83ExtractDate == null)
        {
            _D83ExtractDate = new D83ExtractDate();
        }
        _D83ExtractDate.SetD83ExtractDateAsString(value);
    }
    
    // Standard Getter
    public string GetD83HeaderId()
    {
        return _D83HeaderId;
    }
    
    // Standard Setter
    public void SetD83HeaderId(string value)
    {
        _D83HeaderId = value;
    }
    
    // Get<>AsString()
    public string GetD83HeaderIdAsString()
    {
        return _D83HeaderId.PadRight(30);
    }
    
    // Set<>AsString()
    public void SetD83HeaderIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D83HeaderId = value;
    }
    
    // Standard Getter
    public string GetFiller120()
    {
        return _Filler120;
    }
    
    // Standard Setter
    public void SetFiller120(string value)
    {
        _Filler120 = value;
    }
    
    // Get<>AsString()
    public string GetFiller120AsString()
    {
        return _Filler120.PadRight(62);
    }
    
    // Set<>AsString()
    public void SetFiller120AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler120 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD83ExtractDate(string value)
    {
        _D83ExtractDate.SetD83ExtractDateAsString(value);
    }
    // Nested Class: D83ExtractDate
    public class D83ExtractDate
    {
        private static int _size = 0;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D83ExtractDateYy, is_external=, is_static_class=False, static_prefix=
        private string _D83ExtractDateYy ="";
        
        
        
        
        // [DEBUG] Field: D83ExtractDateMm, is_external=, is_static_class=False, static_prefix=
        private string _D83ExtractDateMm ="";
        
        
        
        
        // [DEBUG] Field: D83ExtractDateDd, is_external=, is_static_class=False, static_prefix=
        private string _D83ExtractDateDd ="";
        
        
        
        
    public D83ExtractDate() {}
    
    public D83ExtractDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD83ExtractDateYy(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD83ExtractDateMm(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD83ExtractDateDd(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD83ExtractDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D83ExtractDateYy.PadRight(0));
        result.Append(_D83ExtractDateMm.PadRight(0));
        result.Append(_D83ExtractDateDd.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD83ExtractDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD83ExtractDateYy(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD83ExtractDateMm(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD83ExtractDateDd(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD83ExtractDateYy()
    {
        return _D83ExtractDateYy;
    }
    
    // Standard Setter
    public void SetD83ExtractDateYy(string value)
    {
        _D83ExtractDateYy = value;
    }
    
    // Get<>AsString()
    public string GetD83ExtractDateYyAsString()
    {
        return _D83ExtractDateYy.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD83ExtractDateYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D83ExtractDateYy = value;
    }
    
    // Standard Getter
    public string GetD83ExtractDateMm()
    {
        return _D83ExtractDateMm;
    }
    
    // Standard Setter
    public void SetD83ExtractDateMm(string value)
    {
        _D83ExtractDateMm = value;
    }
    
    // Get<>AsString()
    public string GetD83ExtractDateMmAsString()
    {
        return _D83ExtractDateMm.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD83ExtractDateMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D83ExtractDateMm = value;
    }
    
    // Standard Getter
    public string GetD83ExtractDateDd()
    {
        return _D83ExtractDateDd;
    }
    
    // Standard Setter
    public void SetD83ExtractDateDd(string value)
    {
        _D83ExtractDateDd = value;
    }
    
    // Get<>AsString()
    public string GetD83ExtractDateDdAsString()
    {
        return _D83ExtractDateDd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD83ExtractDateDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D83ExtractDateDd = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}