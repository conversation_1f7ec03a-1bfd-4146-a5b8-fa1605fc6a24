using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D72File Data Structure

public class D72File
{
    private static int _size = 12;
    // [DEBUG] Class: D72File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler226, is_external=, is_static_class=False, static_prefix=
    private string _Filler226 ="$";
    
    
    
    
    // [DEBUG] Field: D72UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D72UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler227, is_external=, is_static_class=False, static_prefix=
    private string _Filler227 ="RC";
    
    
    
    
    // [DEBUG] Field: D72ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D72ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler228, is_external=, is_static_class=False, static_prefix=
    private string _Filler228 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD72FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler226.PadRight(1));
        result.Append(_D72UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler227.PadRight(2));
        result.Append(_D72ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler228.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD72FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller226(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD72UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller227(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD72ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller228(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD72FileAsString();
    }
    // Set<>String Override function
    public void SetD72File(string value)
    {
        SetD72FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller226()
    {
        return _Filler226;
    }
    
    // Standard Setter
    public void SetFiller226(string value)
    {
        _Filler226 = value;
    }
    
    // Get<>AsString()
    public string GetFiller226AsString()
    {
        return _Filler226.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller226AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler226 = value;
    }
    
    // Standard Getter
    public int GetD72UserNo()
    {
        return _D72UserNo;
    }
    
    // Standard Setter
    public void SetD72UserNo(int value)
    {
        _D72UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD72UserNoAsString()
    {
        return _D72UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD72UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D72UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller227()
    {
        return _Filler227;
    }
    
    // Standard Setter
    public void SetFiller227(string value)
    {
        _Filler227 = value;
    }
    
    // Get<>AsString()
    public string GetFiller227AsString()
    {
        return _Filler227.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller227AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler227 = value;
    }
    
    // Standard Getter
    public int GetD72ReportNo()
    {
        return _D72ReportNo;
    }
    
    // Standard Setter
    public void SetD72ReportNo(int value)
    {
        _D72ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD72ReportNoAsString()
    {
        return _D72ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD72ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D72ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller228()
    {
        return _Filler228;
    }
    
    // Standard Setter
    public void SetFiller228(string value)
    {
        _Filler228 = value;
    }
    
    // Get<>AsString()
    public string GetFiller228AsString()
    {
        return _Filler228.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller228AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler228 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}