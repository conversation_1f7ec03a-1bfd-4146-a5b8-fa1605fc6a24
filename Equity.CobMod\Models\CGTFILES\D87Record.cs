using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D87Record Data Structure

public class D87Record
{
    private static int _size = 7;
    // [DEBUG] Class: D87Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D87Key, is_external=, is_static_class=False, static_prefix=
    private D87Key _D87Key = new D87Key();
    
    
    
    
    // [DEBUG] Field: D87RcfFraction, is_external=, is_static_class=False, static_prefix=
    private int _D87RcfFraction =0;
    
    
    
    
    
    // Serialization methods
    public string GetD87RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D87Key.GetD87KeyAsString());
        result.Append(_D87RcfFraction.ToString().PadLeft(1, '0'));
        
        return result.ToString();
    }
    
    public void SetD87RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 6 <= data.Length)
        {
            _D87Key.SetD87KeyAsString(data.Substring(offset, 6));
        }
        else
        {
            _D87Key.SetD87KeyAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD87RcfFraction(parsedInt);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD87RecordAsString();
    }
    // Set<>String Override function
    public void SetD87Record(string value)
    {
        SetD87RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D87Key GetD87Key()
    {
        return _D87Key;
    }
    
    // Standard Setter
    public void SetD87Key(D87Key value)
    {
        _D87Key = value;
    }
    
    // Get<>AsString()
    public string GetD87KeyAsString()
    {
        return _D87Key != null ? _D87Key.GetD87KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD87KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D87Key == null)
        {
            _D87Key = new D87Key();
        }
        _D87Key.SetD87KeyAsString(value);
    }
    
    // Standard Getter
    public int GetD87RcfFraction()
    {
        return _D87RcfFraction;
    }
    
    // Standard Setter
    public void SetD87RcfFraction(int value)
    {
        _D87RcfFraction = value;
    }
    
    // Get<>AsString()
    public string GetD87RcfFractionAsString()
    {
        return _D87RcfFraction.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD87RcfFractionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D87RcfFraction = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD87Key(string value)
    {
        _D87Key.SetD87KeyAsString(value);
    }
    // Nested Class: D87Key
    public class D87Key
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D87RcfYear, is_external=, is_static_class=False, static_prefix=
        private string _D87RcfYear ="";
        
        
        
        
        // [DEBUG] Field: D87RcfFund, is_external=, is_static_class=False, static_prefix=
        private string _D87RcfFund ="";
        
        
        
        
    public D87Key() {}
    
    public D87Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD87RcfYear(data.Substring(offset, 2).Trim());
        offset += 2;
        SetD87RcfFund(data.Substring(offset, 4).Trim());
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetD87KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D87RcfYear.PadRight(2));
        result.Append(_D87RcfFund.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD87KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD87RcfYear(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD87RcfFund(extracted);
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD87RcfYear()
    {
        return _D87RcfYear;
    }
    
    // Standard Setter
    public void SetD87RcfYear(string value)
    {
        _D87RcfYear = value;
    }
    
    // Get<>AsString()
    public string GetD87RcfYearAsString()
    {
        return _D87RcfYear.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD87RcfYearAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D87RcfYear = value;
    }
    
    // Standard Getter
    public string GetD87RcfFund()
    {
        return _D87RcfFund;
    }
    
    // Standard Setter
    public void SetD87RcfFund(string value)
    {
        _D87RcfFund = value;
    }
    
    // Get<>AsString()
    public string GetD87RcfFundAsString()
    {
        return _D87RcfFund.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD87RcfFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D87RcfFund = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}