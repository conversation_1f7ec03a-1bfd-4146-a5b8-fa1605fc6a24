using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtRetailPriceIndexes Data Structure

public class WtRetailPriceIndexes
{
    private static int _size = 53;
    // [DEBUG] Class: WtRetailPriceIndexes, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler451, is_external=, is_static_class=False, static_prefix=
    private string _Filler451 ="RETAIL-PRICE-INDEXES============";
    
    
    
    
    // [DEBUG] Field: WtrMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtrMaxTableSize =754;
    
    
    
    
    // [DEBUG] Field: WtrOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtrOccurs =754;
    
    
    
    
    // [DEBUG] Field: WtrRetailPriceIndexTable, is_external=, is_static_class=False, static_prefix=
    private WtrRetailPriceIndexTable _WtrRetailPriceIndexTable = new WtrRetailPriceIndexTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtRetailPriceIndexesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler451.PadRight(32));
        result.Append(_WtrMaxTableSize.ToString().PadLeft(4, '0'));
        result.Append(_WtrOccurs.ToString().PadLeft(4, '0'));
        result.Append(_WtrRetailPriceIndexTable.GetWtrRetailPriceIndexTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtRetailPriceIndexesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 32 <= data.Length)
        {
            string extracted = data.Substring(offset, 32).Trim();
            SetFiller451(extracted);
        }
        offset += 32;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtrMaxTableSize(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtrOccurs(parsedInt);
        }
        offset += 4;
        if (offset + 13 <= data.Length)
        {
            _WtrRetailPriceIndexTable.SetWtrRetailPriceIndexTableAsString(data.Substring(offset, 13));
        }
        else
        {
            _WtrRetailPriceIndexTable.SetWtrRetailPriceIndexTableAsString(data.Substring(offset));
        }
        offset += 13;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtRetailPriceIndexesAsString();
    }
    // Set<>String Override function
    public void SetWtRetailPriceIndexes(string value)
    {
        SetWtRetailPriceIndexesAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller451()
    {
        return _Filler451;
    }
    
    // Standard Setter
    public void SetFiller451(string value)
    {
        _Filler451 = value;
    }
    
    // Get<>AsString()
    public string GetFiller451AsString()
    {
        return _Filler451.PadRight(32);
    }
    
    // Set<>AsString()
    public void SetFiller451AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler451 = value;
    }
    
    // Standard Getter
    public int GetWtrMaxTableSize()
    {
        return _WtrMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtrMaxTableSize(int value)
    {
        _WtrMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtrMaxTableSizeAsString()
    {
        return _WtrMaxTableSize.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtrMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtrMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtrOccurs()
    {
        return _WtrOccurs;
    }
    
    // Standard Setter
    public void SetWtrOccurs(int value)
    {
        _WtrOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtrOccursAsString()
    {
        return _WtrOccurs.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtrOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtrOccurs = parsed;
    }
    
    // Standard Getter
    public WtrRetailPriceIndexTable GetWtrRetailPriceIndexTable()
    {
        return _WtrRetailPriceIndexTable;
    }
    
    // Standard Setter
    public void SetWtrRetailPriceIndexTable(WtrRetailPriceIndexTable value)
    {
        _WtrRetailPriceIndexTable = value;
    }
    
    // Get<>AsString()
    public string GetWtrRetailPriceIndexTableAsString()
    {
        return _WtrRetailPriceIndexTable != null ? _WtrRetailPriceIndexTable.GetWtrRetailPriceIndexTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtrRetailPriceIndexTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtrRetailPriceIndexTable == null)
        {
            _WtrRetailPriceIndexTable = new WtrRetailPriceIndexTable();
        }
        _WtrRetailPriceIndexTable.SetWtrRetailPriceIndexTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtrRetailPriceIndexTable(string value)
    {
        _WtrRetailPriceIndexTable.SetWtrRetailPriceIndexTableAsString(value);
    }
    // Nested Class: WtrRetailPriceIndexTable
    public class WtrRetailPriceIndexTable
    {
        private static int _size = 13;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtrElement, is_external=, is_static_class=False, static_prefix=
        private WtrRetailPriceIndexTable.WtrElement[] _WtrElement = new WtrRetailPriceIndexTable.WtrElement[754];
        
        public void InitializeWtrElementArray()
        {
            for (int i = 0; i < 754; i++)
            {
                _WtrElement[i] = new WtrRetailPriceIndexTable.WtrElement();
            }
        }
        
        
        
    public WtrRetailPriceIndexTable() {}
    
    public WtrRetailPriceIndexTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWtrElementArray();
        for (int i = 0; i < 754; i++)
        {
            _WtrElement[i].SetWtrElementAsString(data.Substring(offset, 13));
            offset += 13;
        }
        
    }
    
    // Serialization methods
    public string GetWtrRetailPriceIndexTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 754; i++)
        {
            result.Append(_WtrElement[i].GetWtrElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtrRetailPriceIndexTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 754; i++)
        {
            if (offset + 13 > data.Length) break;
            string val = data.Substring(offset, 13);
            
            _WtrElement[i].SetWtrElementAsString(val);
            offset += 13;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtrElement
    public WtrElement GetWtrElementAt(int index)
    {
        return _WtrElement[index];
    }
    
    public void SetWtrElementAt(int index, WtrElement value)
    {
        _WtrElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtrElement GetWtrElement()
    {
        return _WtrElement != null && _WtrElement.Length > 0
        ? _WtrElement[0]
        : new WtrElement();
    }
    
    public void SetWtrElement(WtrElement value)
    {
        if (_WtrElement == null || _WtrElement.Length == 0)
        _WtrElement = new WtrElement[1];
        _WtrElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtrElement
    public class WtrElement
    {
        private static int _size = 13;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtrDate, is_external=, is_static_class=False, static_prefix=
        private WtrElement.WtrDate _WtrDate = new WtrElement.WtrDate();
        
        
        
        
        // [DEBUG] Field: WtrValue, is_external=, is_static_class=False, static_prefix=
        private decimal _WtrValue =0;
        
        
        
        
        // [DEBUG] Field: WtrEst, is_external=, is_static_class=False, static_prefix=
        private string _WtrEst ="";
        
        
        
        
    public WtrElement() {}
    
    public WtrElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WtrDate.SetWtrDateAsString(data.Substring(offset, WtrDate.GetSize()));
        offset += 6;
        SetWtrValue(PackedDecimalConverter.ToDecimal(data.Substring(offset, 6)));
        offset += 6;
        SetWtrEst(data.Substring(offset, 1).Trim());
        offset += 1;
        
    }
    
    // Serialization methods
    public string GetWtrElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtrDate.GetWtrDateAsString());
        result.Append(_WtrValue.ToString("F0", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtrEst.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetWtrElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 6 <= data.Length)
        {
            _WtrDate.SetWtrDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _WtrDate.SetWtrDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtrValue(parsedDec);
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtrEst(extracted);
        }
        offset += 1;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WtrDate GetWtrDate()
    {
        return _WtrDate;
    }
    
    // Standard Setter
    public void SetWtrDate(WtrDate value)
    {
        _WtrDate = value;
    }
    
    // Get<>AsString()
    public string GetWtrDateAsString()
    {
        return _WtrDate != null ? _WtrDate.GetWtrDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtrDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtrDate == null)
        {
            _WtrDate = new WtrDate();
        }
        _WtrDate.SetWtrDateAsString(value);
    }
    
    // Standard Getter
    public decimal GetWtrValue()
    {
        return _WtrValue;
    }
    
    // Standard Setter
    public void SetWtrValue(decimal value)
    {
        _WtrValue = value;
    }
    
    // Get<>AsString()
    public string GetWtrValueAsString()
    {
        return _WtrValue.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtrValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtrValue = parsed;
    }
    
    // Standard Getter
    public string GetWtrEst()
    {
        return _WtrEst;
    }
    
    // Standard Setter
    public void SetWtrEst(string value)
    {
        _WtrEst = value;
    }
    
    // Get<>AsString()
    public string GetWtrEstAsString()
    {
        return _WtrEst.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtrEstAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtrEst = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtrDate
    public class WtrDate
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtrCc, is_external=, is_static_class=False, static_prefix=
        private int _WtrCc =0;
        
        
        
        
        // [DEBUG] Field: WtrDateYymm, is_external=, is_static_class=False, static_prefix=
        private WtrDate.WtrDateYymm _WtrDateYymm = new WtrDate.WtrDateYymm();
        
        
        
        
    public WtrDate() {}
    
    public WtrDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtrCc(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        _WtrDateYymm.SetWtrDateYymmAsString(data.Substring(offset, WtrDateYymm.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetWtrDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtrCc.ToString().PadLeft(2, '0'));
        result.Append(_WtrDateYymm.GetWtrDateYymmAsString());
        
        return result.ToString();
    }
    
    public void SetWtrDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtrCc(parsedInt);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _WtrDateYymm.SetWtrDateYymmAsString(data.Substring(offset, 4));
        }
        else
        {
            _WtrDateYymm.SetWtrDateYymmAsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWtrCc()
    {
        return _WtrCc;
    }
    
    // Standard Setter
    public void SetWtrCc(int value)
    {
        _WtrCc = value;
    }
    
    // Get<>AsString()
    public string GetWtrCcAsString()
    {
        return _WtrCc.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWtrCcAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtrCc = parsed;
    }
    
    // Standard Getter
    public WtrDateYymm GetWtrDateYymm()
    {
        return _WtrDateYymm;
    }
    
    // Standard Setter
    public void SetWtrDateYymm(WtrDateYymm value)
    {
        _WtrDateYymm = value;
    }
    
    // Get<>AsString()
    public string GetWtrDateYymmAsString()
    {
        return _WtrDateYymm != null ? _WtrDateYymm.GetWtrDateYymmAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtrDateYymmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtrDateYymm == null)
        {
            _WtrDateYymm = new WtrDateYymm();
        }
        _WtrDateYymm.SetWtrDateYymmAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtrDateYymm
    public class WtrDateYymm
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtrYy, is_external=, is_static_class=False, static_prefix=
        private int _WtrYy =0;
        
        
        
        
        // [DEBUG] Field: WtrMm, is_external=, is_static_class=False, static_prefix=
        private int _WtrMm =0;
        
        
        
        
    public WtrDateYymm() {}
    
    public WtrDateYymm(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtrYy(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetWtrMm(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetWtrDateYymmAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtrYy.ToString().PadLeft(2, '0'));
        result.Append(_WtrMm.ToString().PadLeft(2, '0'));
        
        return result.ToString();
    }
    
    public void SetWtrDateYymmAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtrYy(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtrMm(parsedInt);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWtrYy()
    {
        return _WtrYy;
    }
    
    // Standard Setter
    public void SetWtrYy(int value)
    {
        _WtrYy = value;
    }
    
    // Get<>AsString()
    public string GetWtrYyAsString()
    {
        return _WtrYy.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWtrYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtrYy = parsed;
    }
    
    // Standard Getter
    public int GetWtrMm()
    {
        return _WtrMm;
    }
    
    // Standard Setter
    public void SetWtrMm(int value)
    {
        _WtrMm = value;
    }
    
    // Get<>AsString()
    public string GetWtrMmAsString()
    {
        return _WtrMm.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWtrMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtrMm = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
}

}}