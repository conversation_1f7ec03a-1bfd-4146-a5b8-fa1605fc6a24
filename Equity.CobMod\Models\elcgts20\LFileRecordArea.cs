using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing LFileRecordArea Data Structure

public class LFileRecordArea
{
    private static int _size = 485;
    // [DEBUG] Class: LFileRecordArea, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: FixedPortion, is_external=, is_static_class=False, static_prefix=
    private string _FixedPortion ="";
    
    
    
    
    // [DEBUG] Field: FixedPortionR, is_external=, is_static_class=False, static_prefix=
    private FixedPortionR _FixedPortionR = new FixedPortionR();
    
    
    
    
    // [DEBUG] Field: LrTransactionCategory, is_external=, is_static_class=False, static_prefix=
    private string _LrTransactionCategory ="";
    
    
    
    
    // [DEBUG] Field: Filler346, is_external=, is_static_class=False, static_prefix=
    private string _Filler346 ="";
    
    
    
    
    // [DEBUG] Field: Filler347, is_external=, is_static_class=False, static_prefix=
    private string _Filler347 ="";
    
    
    
    
    // [DEBUG] Field: BalanceCosts, is_external=, is_static_class=False, static_prefix=
    private string[] _BalanceCosts = new string[200];
    
    
    
    
    
    // Serialization methods
    public string GetLFileRecordAreaAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_FixedPortion.PadRight(270));
        result.Append(_FixedPortionR.GetFixedPortionRAsString());
        result.Append(_LrTransactionCategory.PadRight(0));
        result.Append(_Filler346.PadRight(0));
        result.Append(_Filler347.PadRight(0));
        for (int i = 0; i < 200; i++)
        {
            result.Append(_BalanceCosts[i].PadRight(0));
        }
        
        return result.ToString();
    }
    
    public void SetLFileRecordAreaAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 270 <= data.Length)
        {
            string extracted = data.Substring(offset, 270).Trim();
            SetFixedPortion(extracted);
        }
        offset += 270;
        if (offset + 215 <= data.Length)
        {
            _FixedPortionR.SetFixedPortionRAsString(data.Substring(offset, 215));
        }
        else
        {
            _FixedPortionR.SetFixedPortionRAsString(data.Substring(offset));
        }
        offset += 215;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetLrTransactionCategory(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller346(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller347(extracted);
        }
        offset += 0;
        for (int i = 0; i < 200; i++)
        {
            if (offset + 0 > data.Length) break;
            string val = data.Substring(offset, 0);
            
            _BalanceCosts[i] = val.Trim();
            offset += 0;
        }
    }
    // ToString Override function
    public override string ToString()
    {
        return GetLFileRecordAreaAsString();
    }
    // Set<>String Override function
    public void SetLFileRecordArea(string value)
    {
        SetLFileRecordAreaAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFixedPortion()
    {
        return _FixedPortion;
    }
    
    // Standard Setter
    public void SetFixedPortion(string value)
    {
        _FixedPortion = value;
    }
    
    // Get<>AsString()
    public string GetFixedPortionAsString()
    {
        return _FixedPortion.PadRight(270);
    }
    
    // Set<>AsString()
    public void SetFixedPortionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _FixedPortion = value;
    }
    
    // Standard Getter
    public FixedPortionR GetFixedPortionR()
    {
        return _FixedPortionR;
    }
    
    // Standard Setter
    public void SetFixedPortionR(FixedPortionR value)
    {
        _FixedPortionR = value;
    }
    
    // Get<>AsString()
    public string GetFixedPortionRAsString()
    {
        return _FixedPortionR != null ? _FixedPortionR.GetFixedPortionRAsString() : "";
    }
    
    // Set<>AsString()
    public void SetFixedPortionRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_FixedPortionR == null)
        {
            _FixedPortionR = new FixedPortionR();
        }
        _FixedPortionR.SetFixedPortionRAsString(value);
    }
    
    // Standard Getter
    public string GetLrTransactionCategory()
    {
        return _LrTransactionCategory;
    }
    
    // Standard Setter
    public void SetLrTransactionCategory(string value)
    {
        _LrTransactionCategory = value;
    }
    
    // Get<>AsString()
    public string GetLrTransactionCategoryAsString()
    {
        return _LrTransactionCategory.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetLrTransactionCategoryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LrTransactionCategory = value;
    }
    
    // Standard Getter
    public string GetFiller346()
    {
        return _Filler346;
    }
    
    // Standard Setter
    public void SetFiller346(string value)
    {
        _Filler346 = value;
    }
    
    // Get<>AsString()
    public string GetFiller346AsString()
    {
        return _Filler346.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller346AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler346 = value;
    }
    
    // Standard Getter
    public string GetFiller347()
    {
        return _Filler347;
    }
    
    // Standard Setter
    public void SetFiller347(string value)
    {
        _Filler347 = value;
    }
    
    // Get<>AsString()
    public string GetFiller347AsString()
    {
        return _Filler347.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller347AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler347 = value;
    }
    
    // Array Accessors for BalanceCosts
    public string GetBalanceCostsAt(int index)
    {
        return _BalanceCosts[index];
    }
    
    public void SetBalanceCostsAt(int index, string value)
    {
        _BalanceCosts[index] = value;
    }
    
    public string GetBalanceCostsAsStringAt(int index)
    {
        return _BalanceCosts[index].PadRight(0);
    }
    
    public void SetBalanceCostsAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _BalanceCosts[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetBalanceCosts()
    {
        return _BalanceCosts != null && _BalanceCosts.Length > 0
        ? _BalanceCosts[0]
        : default(string);
    }
    
    public void SetBalanceCosts(string value)
    {
        if (_BalanceCosts == null || _BalanceCosts.Length == 0)
        _BalanceCosts = new string[1];
        _BalanceCosts[0] = value;
    }
    
    public string GetBalanceCostsAsString()
    {
        return _BalanceCosts != null && _BalanceCosts.Length > 0
        ? _BalanceCosts[0].ToString()
        : string.Empty;
    }
    
    public void SetBalanceCostsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_BalanceCosts == null || _BalanceCosts.Length == 0)
        _BalanceCosts = new string[1];
        
        _BalanceCosts[0] = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFixedPortionR(string value)
    {
        _FixedPortionR.SetFixedPortionRAsString(value);
    }
    // Nested Class: FixedPortionR
    public class FixedPortionR
    {
        private static int _size = 215;
        
        // Fields in the class
        
        
        // [DEBUG] Field: LFund, is_external=, is_static_class=False, static_prefix=
        private string _LFund ="";
        
        
        
        
        // [DEBUG] Field: LSedol, is_external=, is_static_class=False, static_prefix=
        private string _LSedol ="";
        
        
        
        
        // [DEBUG] Field: Filler344, is_external=, is_static_class=False, static_prefix=
        private string _Filler344 ="";
        
        
        
        
        // [DEBUG] Field: FundType, is_external=, is_static_class=False, static_prefix=
        private string _FundType ="";
        
        
        
        
        // [DEBUG] Field: Filler345, is_external=, is_static_class=False, static_prefix=
        private string _Filler345 ="";
        
        
        
        
    public FixedPortionR() {}
    
    public FixedPortionR(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetLFund(data.Substring(offset, 0).Trim());
        offset += 0;
        SetLSedol(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller344(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFundType(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller345(data.Substring(offset, 215).Trim());
        offset += 215;
        
    }
    
    // Serialization methods
    public string GetFixedPortionRAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LFund.PadRight(0));
        result.Append(_LSedol.PadRight(0));
        result.Append(_Filler344.PadRight(0));
        result.Append(_FundType.PadRight(0));
        result.Append(_Filler345.PadRight(215));
        
        return result.ToString();
    }
    
    public void SetFixedPortionRAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetLFund(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetLSedol(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller344(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFundType(extracted);
        }
        offset += 0;
        if (offset + 215 <= data.Length)
        {
            string extracted = data.Substring(offset, 215).Trim();
            SetFiller345(extracted);
        }
        offset += 215;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetLFund()
    {
        return _LFund;
    }
    
    // Standard Setter
    public void SetLFund(string value)
    {
        _LFund = value;
    }
    
    // Get<>AsString()
    public string GetLFundAsString()
    {
        return _LFund.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetLFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LFund = value;
    }
    
    // Standard Getter
    public string GetLSedol()
    {
        return _LSedol;
    }
    
    // Standard Setter
    public void SetLSedol(string value)
    {
        _LSedol = value;
    }
    
    // Get<>AsString()
    public string GetLSedolAsString()
    {
        return _LSedol.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetLSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _LSedol = value;
    }
    
    // Standard Getter
    public string GetFiller344()
    {
        return _Filler344;
    }
    
    // Standard Setter
    public void SetFiller344(string value)
    {
        _Filler344 = value;
    }
    
    // Get<>AsString()
    public string GetFiller344AsString()
    {
        return _Filler344.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller344AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler344 = value;
    }
    
    // Standard Getter
    public string GetFundType()
    {
        return _FundType;
    }
    
    // Standard Setter
    public void SetFundType(string value)
    {
        _FundType = value;
    }
    
    // Get<>AsString()
    public string GetFundTypeAsString()
    {
        return _FundType.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFundTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _FundType = value;
    }
    
    // Standard Getter
    public string GetFiller345()
    {
        return _Filler345;
    }
    
    // Standard Setter
    public void SetFiller345(string value)
    {
        _Filler345 = value;
    }
    
    // Get<>AsString()
    public string GetFiller345AsString()
    {
        return _Filler345.PadRight(215);
    }
    
    // Set<>AsString()
    public void SetFiller345AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler345 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}
