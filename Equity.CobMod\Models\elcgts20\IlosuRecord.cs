using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing IlosuRecord Data Structure

public class IlosuRecord
{
    private static int _size = 186;
    // [DEBUG] Class: IlosuRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler4, is_external=, is_static_class=False, static_prefix=
    private string _Filler4 ="";
    
    
    
    
    // [DEBUG] Field: IlosuPrintLine, is_external=, is_static_class=False, static_prefix=
    private string _IlosuPrintLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetIlosuRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler4.PadRight(1));
        result.Append(_IlosuPrintLine.PadRight(185));
        
        return result.ToString();
    }
    
    public void SetIlosuRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller4(extracted);
        }
        offset += 1;
        if (offset + 185 <= data.Length)
        {
            string extracted = data.Substring(offset, 185).Trim();
            SetIlosuPrintLine(extracted);
        }
        offset += 185;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetIlosuRecordAsString();
    }
    // Set<>String Override function
    public void SetIlosuRecord(string value)
    {
        SetIlosuRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller4()
    {
        return _Filler4;
    }
    
    // Standard Setter
    public void SetFiller4(string value)
    {
        _Filler4 = value;
    }
    
    // Get<>AsString()
    public string GetFiller4AsString()
    {
        return _Filler4.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler4 = value;
    }
    
    // Standard Getter
    public string GetIlosuPrintLine()
    {
        return _IlosuPrintLine;
    }
    
    // Standard Setter
    public void SetIlosuPrintLine(string value)
    {
        _IlosuPrintLine = value;
    }
    
    // Get<>AsString()
    public string GetIlosuPrintLineAsString()
    {
        return _IlosuPrintLine.PadRight(185);
    }
    
    // Set<>AsString()
    public void SetIlosuPrintLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _IlosuPrintLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
