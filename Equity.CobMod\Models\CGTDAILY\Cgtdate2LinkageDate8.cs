using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdailyDTO
{// DTO class representing Cgtdate2LinkageDate8 Data Structure

public class Cgtdate2LinkageDate8
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate8, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd8, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd8 =0;
    
    
    
    
    // [DEBUG] Field: Filler85, is_external=, is_static_class=False, static_prefix=
    private Filler85 _Filler85 = new Filler85();
    
    
    
    
    // [DEBUG] Field: Filler86, is_external=, is_static_class=False, static_prefix=
    private Filler86 _Filler86 = new Filler86();
    
    
    
    
    // [DEBUG] Field: Filler87, is_external=, is_static_class=False, static_prefix=
    private Filler87 _Filler87 = new Filler87();
    
    
    
    
    // [DEBUG] Field: Filler88, is_external=, is_static_class=False, static_prefix=
    private Filler88 _Filler88 = new Filler88();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate8AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd8.ToString().PadLeft(8, '0'));
        result.Append(_Filler85.GetFiller85AsString());
        result.Append(_Filler86.GetFiller86AsString());
        result.Append(_Filler87.GetFiller87AsString());
        result.Append(_Filler88.GetFiller88AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate8AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd8(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler85.SetFiller85AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler85.SetFiller85AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler86.SetFiller86AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler86.SetFiller86AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler87.SetFiller87AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler87.SetFiller87AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler88.SetFiller88AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler88.SetFiller88AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate8AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate8(string value)
    {
        SetCgtdate2LinkageDate8AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd8()
    {
        return _Cgtdate2Ccyymmdd8;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd8(int value)
    {
        _Cgtdate2Ccyymmdd8 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd8AsString()
    {
        return _Cgtdate2Ccyymmdd8.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd8 = parsed;
    }
    
    // Standard Getter
    public Filler85 GetFiller85()
    {
        return _Filler85;
    }
    
    // Standard Setter
    public void SetFiller85(Filler85 value)
    {
        _Filler85 = value;
    }
    
    // Get<>AsString()
    public string GetFiller85AsString()
    {
        return _Filler85 != null ? _Filler85.GetFiller85AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller85AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler85 == null)
        {
            _Filler85 = new Filler85();
        }
        _Filler85.SetFiller85AsString(value);
    }
    
    // Standard Getter
    public Filler86 GetFiller86()
    {
        return _Filler86;
    }
    
    // Standard Setter
    public void SetFiller86(Filler86 value)
    {
        _Filler86 = value;
    }
    
    // Get<>AsString()
    public string GetFiller86AsString()
    {
        return _Filler86 != null ? _Filler86.GetFiller86AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller86AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler86 == null)
        {
            _Filler86 = new Filler86();
        }
        _Filler86.SetFiller86AsString(value);
    }
    
    // Standard Getter
    public Filler87 GetFiller87()
    {
        return _Filler87;
    }
    
    // Standard Setter
    public void SetFiller87(Filler87 value)
    {
        _Filler87 = value;
    }
    
    // Get<>AsString()
    public string GetFiller87AsString()
    {
        return _Filler87 != null ? _Filler87.GetFiller87AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller87AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler87 == null)
        {
            _Filler87 = new Filler87();
        }
        _Filler87.SetFiller87AsString(value);
    }
    
    // Standard Getter
    public Filler88 GetFiller88()
    {
        return _Filler88;
    }
    
    // Standard Setter
    public void SetFiller88(Filler88 value)
    {
        _Filler88 = value;
    }
    
    // Get<>AsString()
    public string GetFiller88AsString()
    {
        return _Filler88 != null ? _Filler88.GetFiller88AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller88AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler88 == null)
        {
            _Filler88 = new Filler88();
        }
        _Filler88.SetFiller88AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller85(string value)
    {
        _Filler85.SetFiller85AsString(value);
    }
    // Nested Class: Filler85
    public class Filler85
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc8, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc8 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd8, is_external=, is_static_class=False, static_prefix=
        private Filler85.Cgtdate2Yymmdd8 _Cgtdate2Yymmdd8 = new Filler85.Cgtdate2Yymmdd8();
        
        
        
        
    public Filler85() {}
    
    public Filler85(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc8(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd8.SetCgtdate2Yymmdd8AsString(data.Substring(offset, Cgtdate2Yymmdd8.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller85AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc8.PadRight(2));
        result.Append(_Cgtdate2Yymmdd8.GetCgtdate2Yymmdd8AsString());
        
        return result.ToString();
    }
    
    public void SetFiller85AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc8(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd8.SetCgtdate2Yymmdd8AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd8.SetCgtdate2Yymmdd8AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc8()
    {
        return _Cgtdate2Cc8;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc8(string value)
    {
        _Cgtdate2Cc8 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc8AsString()
    {
        return _Cgtdate2Cc8.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc8 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd8 GetCgtdate2Yymmdd8()
    {
        return _Cgtdate2Yymmdd8;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd8(Cgtdate2Yymmdd8 value)
    {
        _Cgtdate2Yymmdd8 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd8AsString()
    {
        return _Cgtdate2Yymmdd8 != null ? _Cgtdate2Yymmdd8.GetCgtdate2Yymmdd8AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd8 == null)
        {
            _Cgtdate2Yymmdd8 = new Cgtdate2Yymmdd8();
        }
        _Cgtdate2Yymmdd8.SetCgtdate2Yymmdd8AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd8
    public class Cgtdate2Yymmdd8
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy8, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy8 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd8, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd8.Cgtdate2Mmdd8 _Cgtdate2Mmdd8 = new Cgtdate2Yymmdd8.Cgtdate2Mmdd8();
        
        
        
        
    public Cgtdate2Yymmdd8() {}
    
    public Cgtdate2Yymmdd8(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy8(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd8.SetCgtdate2Mmdd8AsString(data.Substring(offset, Cgtdate2Mmdd8.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd8AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy8.PadRight(2));
        result.Append(_Cgtdate2Mmdd8.GetCgtdate2Mmdd8AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd8AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy8(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd8.SetCgtdate2Mmdd8AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd8.SetCgtdate2Mmdd8AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy8()
    {
        return _Cgtdate2Yy8;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy8(string value)
    {
        _Cgtdate2Yy8 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy8AsString()
    {
        return _Cgtdate2Yy8.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy8 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd8 GetCgtdate2Mmdd8()
    {
        return _Cgtdate2Mmdd8;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd8(Cgtdate2Mmdd8 value)
    {
        _Cgtdate2Mmdd8 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd8AsString()
    {
        return _Cgtdate2Mmdd8 != null ? _Cgtdate2Mmdd8.GetCgtdate2Mmdd8AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd8 == null)
        {
            _Cgtdate2Mmdd8 = new Cgtdate2Mmdd8();
        }
        _Cgtdate2Mmdd8.SetCgtdate2Mmdd8AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd8
    public class Cgtdate2Mmdd8
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm8, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm8 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd8, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd8 ="";
        
        
        
        
    public Cgtdate2Mmdd8() {}
    
    public Cgtdate2Mmdd8(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm8(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd8(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd8AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm8.PadRight(2));
        result.Append(_Cgtdate2Dd8.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd8AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm8(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd8(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm8()
    {
        return _Cgtdate2Mm8;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm8(string value)
    {
        _Cgtdate2Mm8 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm8AsString()
    {
        return _Cgtdate2Mm8.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm8 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd8()
    {
        return _Cgtdate2Dd8;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd8(string value)
    {
        _Cgtdate2Dd8 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd8AsString()
    {
        return _Cgtdate2Dd8.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd8 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller86(string value)
{
    _Filler86.SetFiller86AsString(value);
}
// Nested Class: Filler86
public class Filler86
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy8, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy8 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd8, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd8 =0;
    
    
    
    
public Filler86() {}

public Filler86(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy8(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd8(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller86AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy8.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd8.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller86AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy8(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd8(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy8()
{
    return _Cgtdate2CCcyy8;
}

// Standard Setter
public void SetCgtdate2CCcyy8(int value)
{
    _Cgtdate2CCcyy8 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy8AsString()
{
    return _Cgtdate2CCcyy8.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy8 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd8()
{
    return _Cgtdate2CMmdd8;
}

// Standard Setter
public void SetCgtdate2CMmdd8(int value)
{
    _Cgtdate2CMmdd8 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd8AsString()
{
    return _Cgtdate2CMmdd8.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd8 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller87(string value)
{
    _Filler87.SetFiller87AsString(value);
}
// Nested Class: Filler87
public class Filler87
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc8, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc8 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy8, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy8 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm8, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm8 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd8, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd8 =0;
    
    
    
    
public Filler87() {}

public Filler87(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc8(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy8(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm8(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd8(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller87AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc8.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy8.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm8.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd8.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller87AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc8(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy8(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm8(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd8(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc8()
{
    return _Cgtdate2CCc8;
}

// Standard Setter
public void SetCgtdate2CCc8(int value)
{
    _Cgtdate2CCc8 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc8AsString()
{
    return _Cgtdate2CCc8.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc8 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy8()
{
    return _Cgtdate2CYy8;
}

// Standard Setter
public void SetCgtdate2CYy8(int value)
{
    _Cgtdate2CYy8 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy8AsString()
{
    return _Cgtdate2CYy8.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy8 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm8()
{
    return _Cgtdate2CMm8;
}

// Standard Setter
public void SetCgtdate2CMm8(int value)
{
    _Cgtdate2CMm8 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm8AsString()
{
    return _Cgtdate2CMm8.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm8 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd8()
{
    return _Cgtdate2CDd8;
}

// Standard Setter
public void SetCgtdate2CDd8(int value)
{
    _Cgtdate2CDd8 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd8AsString()
{
    return _Cgtdate2CDd8.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd8 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller88(string value)
{
    _Filler88.SetFiller88AsString(value);
}
// Nested Class: Filler88
public class Filler88
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm8, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm8 =0;
    
    
    
    
    // [DEBUG] Field: Filler89, is_external=, is_static_class=False, static_prefix=
    private string _Filler89 ="";
    
    
    
    
public Filler88() {}

public Filler88(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm8(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller89(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller88AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm8.ToString().PadLeft(6, '0'));
    result.Append(_Filler89.PadRight(2));
    
    return result.ToString();
}

public void SetFiller88AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm8(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller89(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm8()
{
    return _Cgtdate2CCcyymm8;
}

// Standard Setter
public void SetCgtdate2CCcyymm8(int value)
{
    _Cgtdate2CCcyymm8 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm8AsString()
{
    return _Cgtdate2CCcyymm8.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm8 = parsed;
}

// Standard Getter
public string GetFiller89()
{
    return _Filler89;
}

// Standard Setter
public void SetFiller89(string value)
{
    _Filler89 = value;
}

// Get<>AsString()
public string GetFiller89AsString()
{
    return _Filler89.PadRight(2);
}

// Set<>AsString()
public void SetFiller89AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler89 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}