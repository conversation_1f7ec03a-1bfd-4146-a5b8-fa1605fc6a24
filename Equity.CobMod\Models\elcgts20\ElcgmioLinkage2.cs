using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing ElcgmioLinkage2 Data Structure

public class ElcgmioLinkage2
{
    private static int _size = 548;
    // [DEBUG] Class: ElcgmioLinkage2, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: LRecordArea, is_external=, is_static_class=False, static_prefix=
    private LRecordArea _LRecordArea = new LRecordArea();
    
    
    
    
    // [DEBUG] Field: Filler112, is_external=, is_static_class=False, static_prefix=
    private Filler112 _Filler112 = new Filler112();
    
    
    
    
    
    // Serialization methods
    public string GetElcgmioLinkage2AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_LRecordArea.GetLRecordAreaAsString());
        result.Append(_Filler112.GetFiller112AsString());
        
        return result.ToString();
    }
    
    public void SetElcgmioLinkage2AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 270 <= data.Length)
        {
            _LRecordArea.SetLRecordAreaAsString(data.Substring(offset, 270));
        }
        else
        {
            _LRecordArea.SetLRecordAreaAsString(data.Substring(offset));
        }
        offset += 270;
        if (offset + 278 <= data.Length)
        {
            _Filler112.SetFiller112AsString(data.Substring(offset, 278));
        }
        else
        {
            _Filler112.SetFiller112AsString(data.Substring(offset));
        }
        offset += 278;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetElcgmioLinkage2AsString();
    }
    // Set<>String Override function
    public void SetElcgmioLinkage2(string value)
    {
        SetElcgmioLinkage2AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public LRecordArea GetLRecordArea()
    {
        return _LRecordArea;
    }
    
    // Standard Setter
    public void SetLRecordArea(LRecordArea value)
    {
        _LRecordArea = value;
    }
    
    // Get<>AsString()
    public string GetLRecordAreaAsString()
    {
        return _LRecordArea != null ? _LRecordArea.GetLRecordAreaAsString() : "";
    }
    
    // Set<>AsString()
    public void SetLRecordAreaAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_LRecordArea == null)
        {
            _LRecordArea = new LRecordArea();
        }
        _LRecordArea.SetLRecordAreaAsString(value);
    }
    
    // Standard Getter
    public Filler112 GetFiller112()
    {
        return _Filler112;
    }
    
    // Standard Setter
    public void SetFiller112(Filler112 value)
    {
        _Filler112 = value;
    }
    
    // Get<>AsString()
    public string GetFiller112AsString()
    {
        return _Filler112 != null ? _Filler112.GetFiller112AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller112AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler112 == null)
        {
            _Filler112 = new Filler112();
        }
        _Filler112.SetFiller112AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetLRecordArea(string value)
    {
        _LRecordArea.SetLRecordAreaAsString(value);
    }
    // Nested Class: LRecordArea
    public class LRecordArea
    {
        private static int _size = 270;
        
        // Fields in the class
        
        
        // [DEBUG] Field: FixedPortion, is_external=, is_static_class=False, static_prefix=
        private string _FixedPortion ="";
        
        
        
        
        // [DEBUG] Field: Filler111, is_external=, is_static_class=False, static_prefix=
        private string _Filler111 ="";
        
        
        
        
        // [DEBUG] Field: BalanceCosts, is_external=, is_static_class=False, static_prefix=
        private string[] _BalanceCosts = new string[200];
        
        
        
        
    public LRecordArea() {}
    
    public LRecordArea(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFixedPortion(data.Substring(offset, 270).Trim());
        offset += 270;
        SetFiller111(data.Substring(offset, 0).Trim());
        offset += 0;
        for (int i = 0; i < 200; i++)
        {
            string value = data.Substring(offset, 0);
            _BalanceCosts[i] = value.Trim();
            offset += 0;
        }
        
    }
    
    // Serialization methods
    public string GetLRecordAreaAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_FixedPortion.PadRight(270));
        result.Append(_Filler111.PadRight(0));
        for (int i = 0; i < 200; i++)
        {
            result.Append(_BalanceCosts[i].PadRight(0));
        }
        
        return result.ToString();
    }
    
    public void SetLRecordAreaAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 270 <= data.Length)
        {
            string extracted = data.Substring(offset, 270).Trim();
            SetFixedPortion(extracted);
        }
        offset += 270;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller111(extracted);
        }
        offset += 0;
        for (int i = 0; i < 200; i++)
        {
            if (offset + 0 > data.Length) break;
            string val = data.Substring(offset, 0);
            
            _BalanceCosts[i] = val.Trim();
            offset += 0;
        }
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFixedPortion()
    {
        return _FixedPortion;
    }
    
    // Standard Setter
    public void SetFixedPortion(string value)
    {
        _FixedPortion = value;
    }
    
    // Get<>AsString()
    public string GetFixedPortionAsString()
    {
        return _FixedPortion.PadRight(270);
    }
    
    // Set<>AsString()
    public void SetFixedPortionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _FixedPortion = value;
    }
    
    // Standard Getter
    public string GetFiller111()
    {
        return _Filler111;
    }
    
    // Standard Setter
    public void SetFiller111(string value)
    {
        _Filler111 = value;
    }
    
    // Get<>AsString()
    public string GetFiller111AsString()
    {
        return _Filler111.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller111AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler111 = value;
    }
    
    // Array Accessors for BalanceCosts
    public string GetBalanceCostsAt(int index)
    {
        return _BalanceCosts[index];
    }
    
    public void SetBalanceCostsAt(int index, string value)
    {
        _BalanceCosts[index] = value;
    }
    
    public string GetBalanceCostsAsStringAt(int index)
    {
        return _BalanceCosts[index].PadRight(0);
    }
    
    public void SetBalanceCostsAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _BalanceCosts[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetBalanceCosts()
    {
        return _BalanceCosts != null && _BalanceCosts.Length > 0
        ? _BalanceCosts[0]
        : default(string);
    }
    
    public void SetBalanceCosts(string value)
    {
        if (_BalanceCosts == null || _BalanceCosts.Length == 0)
        _BalanceCosts = new string[1];
        _BalanceCosts[0] = value;
    }
    
    public string GetBalanceCostsAsString()
    {
        return _BalanceCosts != null && _BalanceCosts.Length > 0
        ? _BalanceCosts[0].ToString()
        : string.Empty;
    }
    
    public void SetBalanceCostsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_BalanceCosts == null || _BalanceCosts.Length == 0)
        _BalanceCosts = new string[1];
        
        _BalanceCosts[0] = value;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetFiller112(string value)
{
    _Filler112.SetFiller112AsString(value);
}
// Nested Class: Filler112
public class Filler112
{
    private static int _size = 278;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler113, is_external=, is_static_class=False, static_prefix=
    private string _Filler113 ="";
    
    
    
    
    // [DEBUG] Field: LFnUserNo, is_external=, is_static_class=False, static_prefix=
    private string _LFnUserNo ="";
    
    
    
    
    // [DEBUG] Field: LFnGenerationNo, is_external=, is_static_class=False, static_prefix=
    private string _LFnGenerationNo ="";
    
    
    
    
    // [DEBUG] Field: LFnYy, is_external=, is_static_class=False, static_prefix=
    private int _LFnYy =0;
    
    
    
    
    // [DEBUG] Field: Filler114, is_external=, is_static_class=False, static_prefix=
    private Filler112.Filler114 _Filler114 = new Filler112.Filler114();
    
    
    
    
public Filler112() {}

public Filler112(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller113(data.Substring(offset, 1).Trim());
    offset += 1;
    SetLFnUserNo(data.Substring(offset, 4).Trim());
    offset += 4;
    SetLFnGenerationNo(data.Substring(offset, 1).Trim());
    offset += 1;
    SetLFnYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    _Filler114.SetFiller114AsString(data.Substring(offset, Filler114.GetSize()));
    offset += 270;
    
}

// Serialization methods
public string GetFiller112AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler113.PadRight(1));
    result.Append(_LFnUserNo.PadRight(4));
    result.Append(_LFnGenerationNo.PadRight(1));
    result.Append(_LFnYy.ToString().PadLeft(2, '0'));
    result.Append(_Filler114.GetFiller114AsString());
    
    return result.ToString();
}

public void SetFiller112AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller113(extracted);
    }
    offset += 1;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetLFnUserNo(extracted);
    }
    offset += 4;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetLFnGenerationNo(extracted);
    }
    offset += 1;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetLFnYy(parsedInt);
    }
    offset += 2;
    if (offset + 270 <= data.Length)
    {
        _Filler114.SetFiller114AsString(data.Substring(offset, 270));
    }
    else
    {
        _Filler114.SetFiller114AsString(data.Substring(offset));
    }
    offset += 270;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller113()
{
    return _Filler113;
}

// Standard Setter
public void SetFiller113(string value)
{
    _Filler113 = value;
}

// Get<>AsString()
public string GetFiller113AsString()
{
    return _Filler113.PadRight(1);
}

// Set<>AsString()
public void SetFiller113AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler113 = value;
}

// Standard Getter
public string GetLFnUserNo()
{
    return _LFnUserNo;
}

// Standard Setter
public void SetLFnUserNo(string value)
{
    _LFnUserNo = value;
}

// Get<>AsString()
public string GetLFnUserNoAsString()
{
    return _LFnUserNo.PadRight(4);
}

// Set<>AsString()
public void SetLFnUserNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LFnUserNo = value;
}

// Standard Getter
public string GetLFnGenerationNo()
{
    return _LFnGenerationNo;
}

// Standard Setter
public void SetLFnGenerationNo(string value)
{
    _LFnGenerationNo = value;
}

// Get<>AsString()
public string GetLFnGenerationNoAsString()
{
    return _LFnGenerationNo.PadRight(1);
}

// Set<>AsString()
public void SetLFnGenerationNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _LFnGenerationNo = value;
}

// Standard Getter
public int GetLFnYy()
{
    return _LFnYy;
}

// Standard Setter
public void SetLFnYy(int value)
{
    _LFnYy = value;
}

// Get<>AsString()
public string GetLFnYyAsString()
{
    return _LFnYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetLFnYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _LFnYy = parsed;
}

// Standard Getter
public Filler114 GetFiller114()
{
    return _Filler114;
}

// Standard Setter
public void SetFiller114(Filler114 value)
{
    _Filler114 = value;
}

// Get<>AsString()
public string GetFiller114AsString()
{
    return _Filler114 != null ? _Filler114.GetFiller114AsString() : "";
}

// Set<>AsString()
public void SetFiller114AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler114 == null)
    {
        _Filler114 = new Filler114();
    }
    _Filler114.SetFiller114AsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: Filler114
public class Filler114
{
    private static int _size = 270;
    
    // Fields in the class
    
    
    // [DEBUG] Field: FixedPortion, is_external=, is_static_class=False, static_prefix=
    private string _FixedPortion ="";
    
    
    
    
    // [DEBUG] Field: Filler115, is_external=, is_static_class=False, static_prefix=
    private string _Filler115 ="";
    
    
    
    
    // [DEBUG] Field: BalanceCosts, is_external=, is_static_class=False, static_prefix=
    private string[] _BalanceCosts = new string[200];
    
    
    
    
public Filler114() {}

public Filler114(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFixedPortion(data.Substring(offset, 270).Trim());
    offset += 270;
    SetFiller115(data.Substring(offset, 0).Trim());
    offset += 0;
    for (int i = 0; i < 200; i++)
    {
        string value = data.Substring(offset, 0);
        _BalanceCosts[i] = value.Trim();
        offset += 0;
    }
    
}

// Serialization methods
public string GetFiller114AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_FixedPortion.PadRight(270));
    result.Append(_Filler115.PadRight(0));
    for (int i = 0; i < 200; i++)
    {
        result.Append(_BalanceCosts[i].PadRight(0));
    }
    
    return result.ToString();
}

public void SetFiller114AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 270 <= data.Length)
    {
        string extracted = data.Substring(offset, 270).Trim();
        SetFixedPortion(extracted);
    }
    offset += 270;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller115(extracted);
    }
    offset += 0;
    for (int i = 0; i < 200; i++)
    {
        if (offset + 0 > data.Length) break;
        string val = data.Substring(offset, 0);
        
        _BalanceCosts[i] = val.Trim();
        offset += 0;
    }
}

// Getter and Setter methods

// Standard Getter
public string GetFixedPortion()
{
    return _FixedPortion;
}

// Standard Setter
public void SetFixedPortion(string value)
{
    _FixedPortion = value;
}

// Get<>AsString()
public string GetFixedPortionAsString()
{
    return _FixedPortion.PadRight(270);
}

// Set<>AsString()
public void SetFixedPortionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _FixedPortion = value;
}

// Standard Getter
public string GetFiller115()
{
    return _Filler115;
}

// Standard Setter
public void SetFiller115(string value)
{
    _Filler115 = value;
}

// Get<>AsString()
public string GetFiller115AsString()
{
    return _Filler115.PadRight(0);
}

// Set<>AsString()
public void SetFiller115AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler115 = value;
}

// Array Accessors for BalanceCosts
public string GetBalanceCostsAt(int index)
{
    return _BalanceCosts[index];
}

public void SetBalanceCostsAt(int index, string value)
{
    _BalanceCosts[index] = value;
}

public string GetBalanceCostsAsStringAt(int index)
{
    return _BalanceCosts[index].PadRight(0);
}

public void SetBalanceCostsAsStringAt(int index, string value)
{
    if (string.IsNullOrEmpty(value)) return;
    _BalanceCosts[index] = value;
}

// Flattened accessors (index 0)
public string GetBalanceCosts()
{
    return _BalanceCosts != null && _BalanceCosts.Length > 0
    ? _BalanceCosts[0]
    : default(string);
}

public void SetBalanceCosts(string value)
{
    if (_BalanceCosts == null || _BalanceCosts.Length == 0)
    _BalanceCosts = new string[1];
    _BalanceCosts[0] = value;
}

public string GetBalanceCostsAsString()
{
    return _BalanceCosts != null && _BalanceCosts.Length > 0
    ? _BalanceCosts[0].ToString()
    : string.Empty;
}

public void SetBalanceCostsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    if (_BalanceCosts == null || _BalanceCosts.Length == 0)
    _BalanceCosts = new string[1];
    
    _BalanceCosts[0] = value;
}




public static int GetSize()
{
    return _size;
}

}
}

}}
