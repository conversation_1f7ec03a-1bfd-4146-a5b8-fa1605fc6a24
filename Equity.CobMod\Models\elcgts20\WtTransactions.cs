using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtTransactions Data Structure

public class WtTransactions
{
    private static int _size = 1673;
    // [DEBUG] Class: WtTransactions, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler461, is_external=, is_static_class=False, static_prefix=
    private string _Filler461 ="TRANSACTIONS====";
    
    
    
    
    // [DEBUG] Field: WttMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WttMaxTableSize =6000;
    
    
    
    
    // [DEBUG] Field: WttOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WttOccurs =6000;
    
    
    
    
    // [DEBUG] Field: WttTransactionTable, is_external=, is_static_class=False, static_prefix=
    private WttTransactionTable _WttTransactionTable = new WttTransactionTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtTransactionsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler461.PadRight(16));
        result.Append(_WttMaxTableSize.ToString().PadLeft(5, '0'));
        result.Append(_WttOccurs.ToString().PadLeft(5, '0'));
        result.Append(_WttTransactionTable.GetWttTransactionTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtTransactionsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller461(extracted);
        }
        offset += 16;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttMaxTableSize(parsedInt);
        }
        offset += 5;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttOccurs(parsedInt);
        }
        offset += 5;
        if (offset + 1647 <= data.Length)
        {
            _WttTransactionTable.SetWttTransactionTableAsString(data.Substring(offset, 1647));
        }
        else
        {
            _WttTransactionTable.SetWttTransactionTableAsString(data.Substring(offset));
        }
        offset += 1647;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtTransactionsAsString();
    }
    // Set<>String Override function
    public void SetWtTransactions(string value)
    {
        SetWtTransactionsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller461()
    {
        return _Filler461;
    }
    
    // Standard Setter
    public void SetFiller461(string value)
    {
        _Filler461 = value;
    }
    
    // Get<>AsString()
    public string GetFiller461AsString()
    {
        return _Filler461.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller461AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler461 = value;
    }
    
    // Standard Getter
    public int GetWttMaxTableSize()
    {
        return _WttMaxTableSize;
    }
    
    // Standard Setter
    public void SetWttMaxTableSize(int value)
    {
        _WttMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWttMaxTableSizeAsString()
    {
        return _WttMaxTableSize.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWttMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWttOccurs()
    {
        return _WttOccurs;
    }
    
    // Standard Setter
    public void SetWttOccurs(int value)
    {
        _WttOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWttOccursAsString()
    {
        return _WttOccurs.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWttOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttOccurs = parsed;
    }
    
    // Standard Getter
    public WttTransactionTable GetWttTransactionTable()
    {
        return _WttTransactionTable;
    }
    
    // Standard Setter
    public void SetWttTransactionTable(WttTransactionTable value)
    {
        _WttTransactionTable = value;
    }
    
    // Get<>AsString()
    public string GetWttTransactionTableAsString()
    {
        return _WttTransactionTable != null ? _WttTransactionTable.GetWttTransactionTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttTransactionTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttTransactionTable == null)
        {
            _WttTransactionTable = new WttTransactionTable();
        }
        _WttTransactionTable.SetWttTransactionTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWttTransactionTable(string value)
    {
        _WttTransactionTable.SetWttTransactionTableAsString(value);
    }
    // Nested Class: WttTransactionTable
    public class WttTransactionTable
    {
        private static int _size = 1647;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WttElement, is_external=, is_static_class=False, static_prefix=
        private WttTransactionTable.WttElement[] _WttElement = new WttTransactionTable.WttElement[6000];
        
        public void InitializeWttElementArray()
        {
            for (int i = 0; i < 6000; i++)
            {
                _WttElement[i] = new WttTransactionTable.WttElement();
            }
        }
        
        
        
    public WttTransactionTable() {}
    
    public WttTransactionTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWttElementArray();
        for (int i = 0; i < 6000; i++)
        {
            _WttElement[i].SetWttElementAsString(data.Substring(offset, 1647));
            offset += 1647;
        }
        
    }
    
    // Serialization methods
    public string GetWttTransactionTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 6000; i++)
        {
            result.Append(_WttElement[i].GetWttElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWttTransactionTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 6000; i++)
        {
            if (offset + 1647 > data.Length) break;
            string val = data.Substring(offset, 1647);
            
            _WttElement[i].SetWttElementAsString(val);
            offset += 1647;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WttElement
    public WttElement GetWttElementAt(int index)
    {
        return _WttElement[index];
    }
    
    public void SetWttElementAt(int index, WttElement value)
    {
        _WttElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WttElement GetWttElement()
    {
        return _WttElement != null && _WttElement.Length > 0
        ? _WttElement[0]
        : new WttElement();
    }
    
    public void SetWttElement(WttElement value)
    {
        if (_WttElement == null || _WttElement.Length == 0)
        _WttElement = new WttElement[1];
        _WttElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WttElement
    public class WttElement
    {
        private static int _size = 1647;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WttPrefix, is_external=, is_static_class=False, static_prefix=
        private WttElement.WttPrefix _WttPrefix = new WttElement.WttPrefix();
        
        
        
        
        // [DEBUG] Field: WttRecord, is_external=, is_static_class=False, static_prefix=
        private WttElement.WttRecord _WttRecord = new WttElement.WttRecord();
        
        
        
        
        // [DEBUG] Field: Filler470, is_external=, is_static_class=False, static_prefix=
        private WttElement.Filler470 _Filler470 = new WttElement.Filler470();
        
        
        
        
        // [DEBUG] Field: Filler472, is_external=, is_static_class=False, static_prefix=
        private WttElement.Filler472 _Filler472 = new WttElement.Filler472();
        
        
        
        
    public WttElement() {}
    
    public WttElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WttPrefix.SetWttPrefixAsString(data.Substring(offset, WttPrefix.GetSize()));
        offset += 335;
        _WttRecord.SetWttRecordAsString(data.Substring(offset, WttRecord.GetSize()));
        offset += 724;
        _Filler470.SetFiller470AsString(data.Substring(offset, Filler470.GetSize()));
        offset += 294;
        _Filler472.SetFiller472AsString(data.Substring(offset, Filler472.GetSize()));
        offset += 294;
        
    }
    
    // Serialization methods
    public string GetWttElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WttPrefix.GetWttPrefixAsString());
        result.Append(_WttRecord.GetWttRecordAsString());
        result.Append(_Filler470.GetFiller470AsString());
        result.Append(_Filler472.GetFiller472AsString());
        
        return result.ToString();
    }
    
    public void SetWttElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 335 <= data.Length)
        {
            _WttPrefix.SetWttPrefixAsString(data.Substring(offset, 335));
        }
        else
        {
            _WttPrefix.SetWttPrefixAsString(data.Substring(offset));
        }
        offset += 335;
        if (offset + 724 <= data.Length)
        {
            _WttRecord.SetWttRecordAsString(data.Substring(offset, 724));
        }
        else
        {
            _WttRecord.SetWttRecordAsString(data.Substring(offset));
        }
        offset += 724;
        if (offset + 294 <= data.Length)
        {
            _Filler470.SetFiller470AsString(data.Substring(offset, 294));
        }
        else
        {
            _Filler470.SetFiller470AsString(data.Substring(offset));
        }
        offset += 294;
        if (offset + 294 <= data.Length)
        {
            _Filler472.SetFiller472AsString(data.Substring(offset, 294));
        }
        else
        {
            _Filler472.SetFiller472AsString(data.Substring(offset));
        }
        offset += 294;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WttPrefix GetWttPrefix()
    {
        return _WttPrefix;
    }
    
    // Standard Setter
    public void SetWttPrefix(WttPrefix value)
    {
        _WttPrefix = value;
    }
    
    // Get<>AsString()
    public string GetWttPrefixAsString()
    {
        return _WttPrefix != null ? _WttPrefix.GetWttPrefixAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttPrefixAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttPrefix == null)
        {
            _WttPrefix = new WttPrefix();
        }
        _WttPrefix.SetWttPrefixAsString(value);
    }
    
    // Standard Getter
    public WttRecord GetWttRecord()
    {
        return _WttRecord;
    }
    
    // Standard Setter
    public void SetWttRecord(WttRecord value)
    {
        _WttRecord = value;
    }
    
    // Get<>AsString()
    public string GetWttRecordAsString()
    {
        return _WttRecord != null ? _WttRecord.GetWttRecordAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttRecord == null)
        {
            _WttRecord = new WttRecord();
        }
        _WttRecord.SetWttRecordAsString(value);
    }
    
    // Standard Getter
    public Filler470 GetFiller470()
    {
        return _Filler470;
    }
    
    // Standard Setter
    public void SetFiller470(Filler470 value)
    {
        _Filler470 = value;
    }
    
    // Get<>AsString()
    public string GetFiller470AsString()
    {
        return _Filler470 != null ? _Filler470.GetFiller470AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller470AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler470 == null)
        {
            _Filler470 = new Filler470();
        }
        _Filler470.SetFiller470AsString(value);
    }
    
    // Standard Getter
    public Filler472 GetFiller472()
    {
        return _Filler472;
    }
    
    // Standard Setter
    public void SetFiller472(Filler472 value)
    {
        _Filler472 = value;
    }
    
    // Get<>AsString()
    public string GetFiller472AsString()
    {
        return _Filler472 != null ? _Filler472.GetFiller472AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller472AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler472 == null)
        {
            _Filler472 = new Filler472();
        }
        _Filler472.SetFiller472AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WttPrefix
    public class WttPrefix
    {
        private static int _size = 335;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WttError, is_external=, is_static_class=False, static_prefix=
        private int _WttError =0;
        
        
        
        
        // [DEBUG] Field: WttSort, is_external=, is_static_class=False, static_prefix=
        private WttPrefix.WttSort _WttSort = new WttPrefix.WttSort();
        
        
        
        
        // [DEBUG] Field: WttStatusFlag, is_external=, is_static_class=False, static_prefix=
        private string _WttStatusFlag ="";
        
        
        // 88-level condition checks for WttStatusFlag
        public bool IsWttDeleteRecord()
        {
            if (this._WttStatusFlag == "'D'") return true;
            return false;
        }
        public bool IsWttCreateRecord()
        {
            if (this._WttStatusFlag == "'C'") return true;
            return false;
        }
        public bool IsWttTemporaryRecord()
        {
            if (this._WttStatusFlag == "'T'") return true;
            return false;
        }
        public bool IsWttOriginalRecord()
        {
            if (this._WttStatusFlag == "SPACE") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WttLineNumber, is_external=, is_static_class=False, static_prefix=
        private int _WttLineNumber =0;
        
        
        
        
        // [DEBUG] Field: WttBdvLineCount, is_external=, is_static_class=False, static_prefix=
        private int _WttBdvLineCount =0;
        
        
        
        
        // [DEBUG] Field: WttLinesSuppressed, is_external=, is_static_class=False, static_prefix=
        private int _WttLinesSuppressed =0;
        
        
        
        
        // [DEBUG] Field: WttSourceCategory, is_external=, is_static_class=False, static_prefix=
        private string _WttSourceCategory ="";
        
        
        
        
        // [DEBUG] Field: WttSourceMovementDate, is_external=, is_static_class=False, static_prefix=
        private string _WttSourceMovementDate ="";
        
        
        
        
        // [DEBUG] Field: WttIndexationFlag1, is_external=, is_static_class=False, static_prefix=
        private string _WttIndexationFlag1 ="";
        
        
        
        
        // [DEBUG] Field: WttIndexationFlag2, is_external=, is_static_class=False, static_prefix=
        private string _WttIndexationFlag2 ="";
        
        
        
        
        // [DEBUG] Field: WttAvailableUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WttAvailableUnits =0;
        
        
        
        
        // [DEBUG] Field: WttEligibleUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WttEligibleUnits =0;
        
        
        
        
        // [DEBUG] Field: WttPrintStatus, is_external=, is_static_class=False, static_prefix=
        private string _WttPrintStatus ="";
        
        
        // 88-level condition checks for WttPrintStatus
        public bool IsWttBalanceNeeded()
        {
            if (this._WttPrintStatus == "SPACES") return true;
            if (this._WttPrintStatus == "'P'") return true;
            if (this._WttPrintStatus == "'F'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WttTrancheType, is_external=, is_static_class=False, static_prefix=
        private string _WttTrancheType ="";
        
        
        // 88-level condition checks for WttTrancheType
        public bool IsWttTrancheTypeNotSet()
        {
            if (this._WttTrancheType == "'X'") return true;
            return false;
        }
        public bool IsWttIndexedTranche()
        {
            if (this._WttTrancheType == "'B'") return true;
            if (this._WttTrancheType == "'C'") return true;
            return false;
        }
        public bool IsWttPool()
        {
            if (this._WttTrancheType == "'A'") return true;
            return false;
        }
        public bool IsWttParallelPool()
        {
            if (this._WttTrancheType == "'B'") return true;
            return false;
        }
        public bool IsWttIndex85Pool()
        {
            if (this._WttTrancheType == "'C'") return true;
            return false;
        }
        public bool IsWttUnusableTranche()
        {
            if (this._WttTrancheType == "'U'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WttMovementIndicator, is_external=, is_static_class=False, static_prefix=
        private string _WttMovementIndicator ="";
        
        
        
        
        // [DEBUG] Field: WttSecurityType, is_external=, is_static_class=False, static_prefix=
        private string _WttSecurityType ="";
        
        
        // 88-level condition checks for WttSecurityType
        public bool IsWttRelevantSecurities()
        {
            if (this._WttSecurityType == "'B'") return true;
            if (this._WttSecurityType == "'X'") return true;
            if (this._WttSecurityType == "'C'") return true;
            if (this._WttSecurityType == "'E'") return true;
            if (this._WttSecurityType == "'F'") return true;
            if (this._WttSecurityType == "'Y'") return true;
            return false;
        }
        public bool IsWttIntegralUnits()
        {
            if (this._WttSecurityType == "'A'") return true;
            return false;
        }
        public bool IsWttCorporateBonds()
        {
            if (this._WttSecurityType == "'C'") return true;
            return false;
        }
        public bool IsWttUntaxableFa89()
        {
            if (this._WttSecurityType == "'E'") return true;
            return false;
        }
        public bool IsWttGilts()
        {
            if (this._WttSecurityType == "'B'") return true;
            if (this._WttSecurityType == "'X'") return true;
            if (this._WttSecurityType == "'Y'") return true;
            return false;
        }
        public bool IsWttIndexLinkedGilts()
        {
            if (this._WttSecurityType == "'B'") return true;
            if (this._WttSecurityType == "'X'") return true;
            if (this._WttSecurityType == "'Y'") return true;
            return false;
        }
        public bool IsWttShortWrittenDerivative()
        {
            if (this._WttSecurityType == "'G'") return true;
            if (this._WttSecurityType == "'K'") return true;
            if (this._WttSecurityType == "'L'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WttPrefixPpi, is_external=, is_static_class=False, static_prefix=
        private string _WttPrefixPpi ="";
        
        
        
        
        // [DEBUG] Field: WttWttcStart, is_external=, is_static_class=False, static_prefix=
        private string _WttWttcStart ="";
        
        
        
        
        // [DEBUG] Field: WttIds, is_external=, is_static_class=False, static_prefix=
        private WttPrefix.WttIds _WttIds = new WttPrefix.WttIds();
        
        
        
        
        // [DEBUG] Field: WttDisplayCategoryCode, is_external=, is_static_class=False, static_prefix=
        private string _WttDisplayCategoryCode ="";
        
        
        
        
        // [DEBUG] Field: WttFa08PoolFlag, is_external=, is_static_class=False, static_prefix=
        private string _WttFa08PoolFlag ="";
        
        
        // 88-level condition checks for WttFa08PoolFlag
        public bool IsWttFa08Pool()
        {
            if (this._WttFa08PoolFlag == "'Y'") return true;
            return false;
        }
        public bool IsWttNotFa08Pool()
        {
            if (this._WttFa08PoolFlag == "'N'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WttGainlossRpiBasis, is_external=, is_static_class=False, static_prefix=
        private string _WttGainlossRpiBasis ="";
        
        
        
        
        // [DEBUG] Field: WttWriteCompleted, is_external=, is_static_class=False, static_prefix=
        private string _WttWriteCompleted ="";
        
        
        
        
        // [DEBUG] Field: WttRealisedMovement, is_external=, is_static_class=False, static_prefix=
        private string _WttRealisedMovement ="";
        
        
        
        
        // [DEBUG] Field: WttTempTaperDate, is_external=, is_static_class=False, static_prefix=
        private string _WttTempTaperDate ="";
        
        
        
        
        // [DEBUG] Field: WttTransferTaperUnits, is_external=, is_static_class=False, static_prefix=
        private string _WttTransferTaperUnits ="";
        
        
        
        
        // [DEBUG] Field: WttTempTaperUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WttTempTaperUnits =0;
        
        
        
        
        // [DEBUG] Field: WttFa03Consolidate, is_external=, is_static_class=False, static_prefix=
        private int _WttFa03Consolidate =0;
        
        
        // 88-level condition checks for WttFa03Consolidate
        public bool IsWttFa03ConsolidateInc()
        {
            if (this._WttFa03Consolidate == 0) return true;
            return false;
        }
        public bool IsWttFa03ConsolidateExc()
        {
            if (this._WttFa03Consolidate == 1) return true;
            return false;
        }
        public bool IsWttFa03ConsolidateMix()
        {
            if (this._WttFa03Consolidate == 2) return true;
            return false;
        }
        
        
        // [DEBUG] Field: WttFa03ProcessingFlag, is_external=, is_static_class=False, static_prefix=
        private int _WttFa03ProcessingFlag =0;
        
        
        // 88-level condition checks for WttFa03ProcessingFlag
        public bool IsWttFa03TrialSale()
        {
            if (this._WttFa03ProcessingFlag == 0) return true;
            return false;
        }
        public bool IsWttFa03NewRulesSale()
        {
            if (this._WttFa03ProcessingFlag == 1) return true;
            return false;
        }
        public bool IsWttFa03OldRulesExcluded()
        {
            if (this._WttFa03ProcessingFlag == 2) return true;
            return false;
        }
        public bool IsWttFa03OldRulesMixed()
        {
            if (this._WttFa03ProcessingFlag == 3) return true;
            return false;
        }
        public bool IsWttFa03OldRulesNormal()
        {
            if (this._WttFa03ProcessingFlag == 4) return true;
            return false;
        }
        public bool IsWttFa03OldRulesSale()
        {
            if (this._WttFa03ProcessingFlag == 0) return true;
            if (this._WttFa03ProcessingFlag == 2) return true;
            if (this._WttFa03ProcessingFlag == 3) return true;
            if (this._WttFa03ProcessingFlag == 4) return true;
            return false;
        }
        
        
        // [DEBUG] Field: WttConsolidatedFlag, is_external=, is_static_class=False, static_prefix=
        private string _WttConsolidatedFlag ="";
        
        
        
        
        // [DEBUG] Field: WttStoreUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WttStoreUnits =0;
        
        
        
        
        // [DEBUG] Field: WttStoreProceeds, is_external=, is_static_class=False, static_prefix=
        private decimal _WttStoreProceeds =0;
        
        
        
        
        // [DEBUG] Field: WttConsolidateKey, is_external=, is_static_class=False, static_prefix=
        private string _WttConsolidateKey ="";
        
        
        
        
        // [DEBUG] Field: WttGtOriginalDatesFlag, is_external=, is_static_class=False, static_prefix=
        private int _WttGtOriginalDatesFlag =0;
        
        
        // 88-level condition checks for WttGtOriginalDatesFlag
        public bool IsWttNotGtOriginalDates()
        {
            if (this._WttGtOriginalDatesFlag == 0) return true;
            return false;
        }
        public bool IsWttGtOriginalDates()
        {
            if (this._WttGtOriginalDatesFlag == 1) return true;
            return false;
        }
        
        
        // [DEBUG] Field: WttTotalNdlUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WttTotalNdlUnits =0;
        
        
        
        
        // [DEBUG] Field: WttTotalNdlLoss, is_external=, is_static_class=False, static_prefix=
        private decimal _WttTotalNdlLoss =0;
        
        
        
        
        // [DEBUG] Field: WttUnusedNdlUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WttUnusedNdlUnits =0;
        
        
        
        
        // [DEBUG] Field: WttUnusedNdlLoss, is_external=, is_static_class=False, static_prefix=
        private decimal _WttUnusedNdlLoss =0;
        
        
        
        
        // [DEBUG] Field: WttDerivativeToFmSedol, is_external=, is_static_class=False, static_prefix=
        private string _WttDerivativeToFmSedol ="";
        
        
        
        
    public WttPrefix() {}
    
    public WttPrefix(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWttError(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        _WttSort.SetWttSortAsString(data.Substring(offset, WttSort.GetSize()));
        offset += 51;
        SetWttStatusFlag(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWttLineNumber(int.Parse(data.Substring(offset, 5).Trim()));
        offset += 5;
        SetWttBdvLineCount(int.Parse(data.Substring(offset, 5).Trim()));
        offset += 5;
        SetWttLinesSuppressed(int.Parse(data.Substring(offset, 5).Trim()));
        offset += 5;
        SetWttSourceCategory(data.Substring(offset, 2).Trim());
        offset += 2;
        SetWttSourceMovementDate(data.Substring(offset, 6).Trim());
        offset += 6;
        SetWttIndexationFlag1(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWttIndexationFlag2(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWttAvailableUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetWttEligibleUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetWttPrintStatus(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWttTrancheType(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWttMovementIndicator(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWttSecurityType(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWttPrefixPpi(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWttWttcStart(data.Substring(offset, 4).Trim());
        offset += 4;
        _WttIds.SetWttIdsAsString(data.Substring(offset, WttIds.GetSize()));
        offset += 89;
        SetWttDisplayCategoryCode(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWttFa08PoolFlag(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWttGainlossRpiBasis(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWttWriteCompleted(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWttRealisedMovement(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWttTempTaperDate(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWttTransferTaperUnits(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWttTempTaperUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetWttFa03Consolidate(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetWttFa03ProcessingFlag(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetWttConsolidatedFlag(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWttStoreUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetWttStoreProceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetWttConsolidateKey(data.Substring(offset, 23).Trim());
        offset += 23;
        SetWttGtOriginalDatesFlag(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetWttTotalNdlUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetWttTotalNdlLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetWttUnusedNdlUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetWttUnusedNdlLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetWttDerivativeToFmSedol(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetWttPrefixAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WttError.ToString().PadLeft(2, '0'));
        result.Append(_WttSort.GetWttSortAsString());
        result.Append(_WttStatusFlag.PadRight(1));
        result.Append(_WttLineNumber.ToString().PadLeft(5, '0'));
        result.Append(_WttBdvLineCount.ToString().PadLeft(5, '0'));
        result.Append(_WttLinesSuppressed.ToString().PadLeft(5, '0'));
        result.Append(_WttSourceCategory.PadRight(2));
        result.Append(_WttSourceMovementDate.PadRight(6));
        result.Append(_WttIndexationFlag1.PadRight(1));
        result.Append(_WttIndexationFlag2.PadRight(1));
        result.Append(_WttAvailableUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttEligibleUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttPrintStatus.PadRight(1));
        result.Append(_WttTrancheType.PadRight(1));
        result.Append(_WttMovementIndicator.PadRight(1));
        result.Append(_WttSecurityType.PadRight(1));
        result.Append(_WttPrefixPpi.PadRight(1));
        result.Append(_WttWttcStart.PadRight(4));
        result.Append(_WttIds.GetWttIdsAsString());
        result.Append(_WttDisplayCategoryCode.PadRight(0));
        result.Append(_WttFa08PoolFlag.PadRight(1));
        result.Append(_WttGainlossRpiBasis.PadRight(1));
        result.Append(_WttWriteCompleted.PadRight(1));
        result.Append(_WttRealisedMovement.PadRight(1));
        result.Append(_WttTempTaperDate.PadRight(0));
        result.Append(_WttTransferTaperUnits.PadRight(0));
        result.Append(_WttTempTaperUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttFa03Consolidate.ToString().PadLeft(1, '0'));
        result.Append(_WttFa03ProcessingFlag.ToString().PadLeft(1, '0'));
        result.Append(_WttConsolidatedFlag.PadRight(1));
        result.Append(_WttStoreUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttStoreProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttConsolidateKey.PadRight(23));
        result.Append(_WttGtOriginalDatesFlag.ToString().PadLeft(1, '0'));
        result.Append(_WttTotalNdlUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttTotalNdlLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttUnusedNdlUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttUnusedNdlLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WttDerivativeToFmSedol.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetWttPrefixAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttError(parsedInt);
        }
        offset += 2;
        if (offset + 51 <= data.Length)
        {
            _WttSort.SetWttSortAsString(data.Substring(offset, 51));
        }
        else
        {
            _WttSort.SetWttSortAsString(data.Substring(offset));
        }
        offset += 51;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWttStatusFlag(extracted);
        }
        offset += 1;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttLineNumber(parsedInt);
        }
        offset += 5;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttBdvLineCount(parsedInt);
        }
        offset += 5;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttLinesSuppressed(parsedInt);
        }
        offset += 5;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetWttSourceCategory(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetWttSourceMovementDate(extracted);
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWttIndexationFlag1(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWttIndexationFlag2(extracted);
        }
        offset += 1;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttAvailableUnits(parsedDec);
        }
        offset += 13;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttEligibleUnits(parsedDec);
        }
        offset += 13;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWttPrintStatus(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWttTrancheType(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWttMovementIndicator(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWttSecurityType(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWttPrefixPpi(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWttWttcStart(extracted);
        }
        offset += 4;
        if (offset + 89 <= data.Length)
        {
            _WttIds.SetWttIdsAsString(data.Substring(offset, 89));
        }
        else
        {
            _WttIds.SetWttIdsAsString(data.Substring(offset));
        }
        offset += 89;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWttDisplayCategoryCode(extracted);
        }
        offset += 0;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWttFa08PoolFlag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWttGainlossRpiBasis(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWttWriteCompleted(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWttRealisedMovement(extracted);
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWttTempTaperDate(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWttTransferTaperUnits(extracted);
        }
        offset += 0;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttTempTaperUnits(parsedDec);
        }
        offset += 13;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttFa03Consolidate(parsedInt);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttFa03ProcessingFlag(parsedInt);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWttConsolidatedFlag(extracted);
        }
        offset += 1;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttStoreUnits(parsedDec);
        }
        offset += 13;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttStoreProceeds(parsedDec);
        }
        offset += 15;
        if (offset + 23 <= data.Length)
        {
            string extracted = data.Substring(offset, 23).Trim();
            SetWttConsolidateKey(extracted);
        }
        offset += 23;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttGtOriginalDatesFlag(parsedInt);
        }
        offset += 1;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttTotalNdlUnits(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttTotalNdlLoss(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttUnusedNdlUnits(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWttUnusedNdlLoss(parsedDec);
        }
        offset += 15;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWttDerivativeToFmSedol(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWttError()
    {
        return _WttError;
    }
    
    // Standard Setter
    public void SetWttError(int value)
    {
        _WttError = value;
    }
    
    // Get<>AsString()
    public string GetWttErrorAsString()
    {
        return _WttError.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWttErrorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttError = parsed;
    }
    
    // Standard Getter
    public WttSort GetWttSort()
    {
        return _WttSort;
    }
    
    // Standard Setter
    public void SetWttSort(WttSort value)
    {
        _WttSort = value;
    }
    
    // Get<>AsString()
    public string GetWttSortAsString()
    {
        return _WttSort != null ? _WttSort.GetWttSortAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttSortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttSort == null)
        {
            _WttSort = new WttSort();
        }
        _WttSort.SetWttSortAsString(value);
    }
    
    // Standard Getter
    public string GetWttStatusFlag()
    {
        return _WttStatusFlag;
    }
    
    // Standard Setter
    public void SetWttStatusFlag(string value)
    {
        _WttStatusFlag = value;
    }
    
    // Get<>AsString()
    public string GetWttStatusFlagAsString()
    {
        return _WttStatusFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWttStatusFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttStatusFlag = value;
    }
    
    // Standard Getter
    public int GetWttLineNumber()
    {
        return _WttLineNumber;
    }
    
    // Standard Setter
    public void SetWttLineNumber(int value)
    {
        _WttLineNumber = value;
    }
    
    // Get<>AsString()
    public string GetWttLineNumberAsString()
    {
        return _WttLineNumber.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWttLineNumberAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttLineNumber = parsed;
    }
    
    // Standard Getter
    public int GetWttBdvLineCount()
    {
        return _WttBdvLineCount;
    }
    
    // Standard Setter
    public void SetWttBdvLineCount(int value)
    {
        _WttBdvLineCount = value;
    }
    
    // Get<>AsString()
    public string GetWttBdvLineCountAsString()
    {
        return _WttBdvLineCount.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWttBdvLineCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttBdvLineCount = parsed;
    }
    
    // Standard Getter
    public int GetWttLinesSuppressed()
    {
        return _WttLinesSuppressed;
    }
    
    // Standard Setter
    public void SetWttLinesSuppressed(int value)
    {
        _WttLinesSuppressed = value;
    }
    
    // Get<>AsString()
    public string GetWttLinesSuppressedAsString()
    {
        return _WttLinesSuppressed.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWttLinesSuppressedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttLinesSuppressed = parsed;
    }
    
    // Standard Getter
    public string GetWttSourceCategory()
    {
        return _WttSourceCategory;
    }
    
    // Standard Setter
    public void SetWttSourceCategory(string value)
    {
        _WttSourceCategory = value;
    }
    
    // Get<>AsString()
    public string GetWttSourceCategoryAsString()
    {
        return _WttSourceCategory.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetWttSourceCategoryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttSourceCategory = value;
    }
    
    // Standard Getter
    public string GetWttSourceMovementDate()
    {
        return _WttSourceMovementDate;
    }
    
    // Standard Setter
    public void SetWttSourceMovementDate(string value)
    {
        _WttSourceMovementDate = value;
    }
    
    // Get<>AsString()
    public string GetWttSourceMovementDateAsString()
    {
        return _WttSourceMovementDate.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetWttSourceMovementDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttSourceMovementDate = value;
    }
    
    // Standard Getter
    public string GetWttIndexationFlag1()
    {
        return _WttIndexationFlag1;
    }
    
    // Standard Setter
    public void SetWttIndexationFlag1(string value)
    {
        _WttIndexationFlag1 = value;
    }
    
    // Get<>AsString()
    public string GetWttIndexationFlag1AsString()
    {
        return _WttIndexationFlag1.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWttIndexationFlag1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttIndexationFlag1 = value;
    }
    
    // Standard Getter
    public string GetWttIndexationFlag2()
    {
        return _WttIndexationFlag2;
    }
    
    // Standard Setter
    public void SetWttIndexationFlag2(string value)
    {
        _WttIndexationFlag2 = value;
    }
    
    // Get<>AsString()
    public string GetWttIndexationFlag2AsString()
    {
        return _WttIndexationFlag2.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWttIndexationFlag2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttIndexationFlag2 = value;
    }
    
    // Standard Getter
    public decimal GetWttAvailableUnits()
    {
        return _WttAvailableUnits;
    }
    
    // Standard Setter
    public void SetWttAvailableUnits(decimal value)
    {
        _WttAvailableUnits = value;
    }
    
    // Get<>AsString()
    public string GetWttAvailableUnitsAsString()
    {
        return _WttAvailableUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttAvailableUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttAvailableUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetWttEligibleUnits()
    {
        return _WttEligibleUnits;
    }
    
    // Standard Setter
    public void SetWttEligibleUnits(decimal value)
    {
        _WttEligibleUnits = value;
    }
    
    // Get<>AsString()
    public string GetWttEligibleUnitsAsString()
    {
        return _WttEligibleUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttEligibleUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttEligibleUnits = parsed;
    }
    
    // Standard Getter
    public string GetWttPrintStatus()
    {
        return _WttPrintStatus;
    }
    
    // Standard Setter
    public void SetWttPrintStatus(string value)
    {
        _WttPrintStatus = value;
    }
    
    // Get<>AsString()
    public string GetWttPrintStatusAsString()
    {
        return _WttPrintStatus.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWttPrintStatusAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttPrintStatus = value;
    }
    
    // Standard Getter
    public string GetWttTrancheType()
    {
        return _WttTrancheType;
    }
    
    // Standard Setter
    public void SetWttTrancheType(string value)
    {
        _WttTrancheType = value;
    }
    
    // Get<>AsString()
    public string GetWttTrancheTypeAsString()
    {
        return _WttTrancheType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWttTrancheTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttTrancheType = value;
    }
    
    // Standard Getter
    public string GetWttMovementIndicator()
    {
        return _WttMovementIndicator;
    }
    
    // Standard Setter
    public void SetWttMovementIndicator(string value)
    {
        _WttMovementIndicator = value;
    }
    
    // Get<>AsString()
    public string GetWttMovementIndicatorAsString()
    {
        return _WttMovementIndicator.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWttMovementIndicatorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttMovementIndicator = value;
    }
    
    // Standard Getter
    public string GetWttSecurityType()
    {
        return _WttSecurityType;
    }
    
    // Standard Setter
    public void SetWttSecurityType(string value)
    {
        _WttSecurityType = value;
    }
    
    // Get<>AsString()
    public string GetWttSecurityTypeAsString()
    {
        return _WttSecurityType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWttSecurityTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttSecurityType = value;
    }
    
    // Standard Getter
    public string GetWttPrefixPpi()
    {
        return _WttPrefixPpi;
    }
    
    // Standard Setter
    public void SetWttPrefixPpi(string value)
    {
        _WttPrefixPpi = value;
    }
    
    // Get<>AsString()
    public string GetWttPrefixPpiAsString()
    {
        return _WttPrefixPpi.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWttPrefixPpiAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttPrefixPpi = value;
    }
    
    // Standard Getter
    public string GetWttWttcStart()
    {
        return _WttWttcStart;
    }
    
    // Standard Setter
    public void SetWttWttcStart(string value)
    {
        _WttWttcStart = value;
    }
    
    // Get<>AsString()
    public string GetWttWttcStartAsString()
    {
        return _WttWttcStart.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWttWttcStartAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttWttcStart = value;
    }
    
    // Standard Getter
    public WttIds GetWttIds()
    {
        return _WttIds;
    }
    
    // Standard Setter
    public void SetWttIds(WttIds value)
    {
        _WttIds = value;
    }
    
    // Get<>AsString()
    public string GetWttIdsAsString()
    {
        return _WttIds != null ? _WttIds.GetWttIdsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttIdsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttIds == null)
        {
            _WttIds = new WttIds();
        }
        _WttIds.SetWttIdsAsString(value);
    }
    
    // Standard Getter
    public string GetWttDisplayCategoryCode()
    {
        return _WttDisplayCategoryCode;
    }
    
    // Standard Setter
    public void SetWttDisplayCategoryCode(string value)
    {
        _WttDisplayCategoryCode = value;
    }
    
    // Get<>AsString()
    public string GetWttDisplayCategoryCodeAsString()
    {
        return _WttDisplayCategoryCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWttDisplayCategoryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttDisplayCategoryCode = value;
    }
    
    // Standard Getter
    public string GetWttFa08PoolFlag()
    {
        return _WttFa08PoolFlag;
    }
    
    // Standard Setter
    public void SetWttFa08PoolFlag(string value)
    {
        _WttFa08PoolFlag = value;
    }
    
    // Get<>AsString()
    public string GetWttFa08PoolFlagAsString()
    {
        return _WttFa08PoolFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWttFa08PoolFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttFa08PoolFlag = value;
    }
    
    // Standard Getter
    public string GetWttGainlossRpiBasis()
    {
        return _WttGainlossRpiBasis;
    }
    
    // Standard Setter
    public void SetWttGainlossRpiBasis(string value)
    {
        _WttGainlossRpiBasis = value;
    }
    
    // Get<>AsString()
    public string GetWttGainlossRpiBasisAsString()
    {
        return _WttGainlossRpiBasis.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWttGainlossRpiBasisAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttGainlossRpiBasis = value;
    }
    
    // Standard Getter
    public string GetWttWriteCompleted()
    {
        return _WttWriteCompleted;
    }
    
    // Standard Setter
    public void SetWttWriteCompleted(string value)
    {
        _WttWriteCompleted = value;
    }
    
    // Get<>AsString()
    public string GetWttWriteCompletedAsString()
    {
        return _WttWriteCompleted.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWttWriteCompletedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttWriteCompleted = value;
    }
    
    // Standard Getter
    public string GetWttRealisedMovement()
    {
        return _WttRealisedMovement;
    }
    
    // Standard Setter
    public void SetWttRealisedMovement(string value)
    {
        _WttRealisedMovement = value;
    }
    
    // Get<>AsString()
    public string GetWttRealisedMovementAsString()
    {
        return _WttRealisedMovement.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWttRealisedMovementAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttRealisedMovement = value;
    }
    
    // Standard Getter
    public string GetWttTempTaperDate()
    {
        return _WttTempTaperDate;
    }
    
    // Standard Setter
    public void SetWttTempTaperDate(string value)
    {
        _WttTempTaperDate = value;
    }
    
    // Get<>AsString()
    public string GetWttTempTaperDateAsString()
    {
        return _WttTempTaperDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWttTempTaperDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttTempTaperDate = value;
    }
    
    // Standard Getter
    public string GetWttTransferTaperUnits()
    {
        return _WttTransferTaperUnits;
    }
    
    // Standard Setter
    public void SetWttTransferTaperUnits(string value)
    {
        _WttTransferTaperUnits = value;
    }
    
    // Get<>AsString()
    public string GetWttTransferTaperUnitsAsString()
    {
        return _WttTransferTaperUnits.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWttTransferTaperUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttTransferTaperUnits = value;
    }
    
    // Standard Getter
    public decimal GetWttTempTaperUnits()
    {
        return _WttTempTaperUnits;
    }
    
    // Standard Setter
    public void SetWttTempTaperUnits(decimal value)
    {
        _WttTempTaperUnits = value;
    }
    
    // Get<>AsString()
    public string GetWttTempTaperUnitsAsString()
    {
        return _WttTempTaperUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttTempTaperUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttTempTaperUnits = parsed;
    }
    
    // Standard Getter
    public int GetWttFa03Consolidate()
    {
        return _WttFa03Consolidate;
    }
    
    // Standard Setter
    public void SetWttFa03Consolidate(int value)
    {
        _WttFa03Consolidate = value;
    }
    
    // Get<>AsString()
    public string GetWttFa03ConsolidateAsString()
    {
        return _WttFa03Consolidate.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetWttFa03ConsolidateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttFa03Consolidate = parsed;
    }
    
    // Standard Getter
    public int GetWttFa03ProcessingFlag()
    {
        return _WttFa03ProcessingFlag;
    }
    
    // Standard Setter
    public void SetWttFa03ProcessingFlag(int value)
    {
        _WttFa03ProcessingFlag = value;
    }
    
    // Get<>AsString()
    public string GetWttFa03ProcessingFlagAsString()
    {
        return _WttFa03ProcessingFlag.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetWttFa03ProcessingFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttFa03ProcessingFlag = parsed;
    }
    
    // Standard Getter
    public string GetWttConsolidatedFlag()
    {
        return _WttConsolidatedFlag;
    }
    
    // Standard Setter
    public void SetWttConsolidatedFlag(string value)
    {
        _WttConsolidatedFlag = value;
    }
    
    // Get<>AsString()
    public string GetWttConsolidatedFlagAsString()
    {
        return _WttConsolidatedFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWttConsolidatedFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttConsolidatedFlag = value;
    }
    
    // Standard Getter
    public decimal GetWttStoreUnits()
    {
        return _WttStoreUnits;
    }
    
    // Standard Setter
    public void SetWttStoreUnits(decimal value)
    {
        _WttStoreUnits = value;
    }
    
    // Get<>AsString()
    public string GetWttStoreUnitsAsString()
    {
        return _WttStoreUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttStoreUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttStoreUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetWttStoreProceeds()
    {
        return _WttStoreProceeds;
    }
    
    // Standard Setter
    public void SetWttStoreProceeds(decimal value)
    {
        _WttStoreProceeds = value;
    }
    
    // Get<>AsString()
    public string GetWttStoreProceedsAsString()
    {
        return _WttStoreProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttStoreProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttStoreProceeds = parsed;
    }
    
    // Standard Getter
    public string GetWttConsolidateKey()
    {
        return _WttConsolidateKey;
    }
    
    // Standard Setter
    public void SetWttConsolidateKey(string value)
    {
        _WttConsolidateKey = value;
    }
    
    // Get<>AsString()
    public string GetWttConsolidateKeyAsString()
    {
        return _WttConsolidateKey.PadRight(23);
    }
    
    // Set<>AsString()
    public void SetWttConsolidateKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttConsolidateKey = value;
    }
    
    // Standard Getter
    public int GetWttGtOriginalDatesFlag()
    {
        return _WttGtOriginalDatesFlag;
    }
    
    // Standard Setter
    public void SetWttGtOriginalDatesFlag(int value)
    {
        _WttGtOriginalDatesFlag = value;
    }
    
    // Get<>AsString()
    public string GetWttGtOriginalDatesFlagAsString()
    {
        return _WttGtOriginalDatesFlag.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetWttGtOriginalDatesFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttGtOriginalDatesFlag = parsed;
    }
    
    // Standard Getter
    public decimal GetWttTotalNdlUnits()
    {
        return _WttTotalNdlUnits;
    }
    
    // Standard Setter
    public void SetWttTotalNdlUnits(decimal value)
    {
        _WttTotalNdlUnits = value;
    }
    
    // Get<>AsString()
    public string GetWttTotalNdlUnitsAsString()
    {
        return _WttTotalNdlUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttTotalNdlUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttTotalNdlUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetWttTotalNdlLoss()
    {
        return _WttTotalNdlLoss;
    }
    
    // Standard Setter
    public void SetWttTotalNdlLoss(decimal value)
    {
        _WttTotalNdlLoss = value;
    }
    
    // Get<>AsString()
    public string GetWttTotalNdlLossAsString()
    {
        return _WttTotalNdlLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttTotalNdlLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttTotalNdlLoss = parsed;
    }
    
    // Standard Getter
    public decimal GetWttUnusedNdlUnits()
    {
        return _WttUnusedNdlUnits;
    }
    
    // Standard Setter
    public void SetWttUnusedNdlUnits(decimal value)
    {
        _WttUnusedNdlUnits = value;
    }
    
    // Get<>AsString()
    public string GetWttUnusedNdlUnitsAsString()
    {
        return _WttUnusedNdlUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttUnusedNdlUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttUnusedNdlUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetWttUnusedNdlLoss()
    {
        return _WttUnusedNdlLoss;
    }
    
    // Standard Setter
    public void SetWttUnusedNdlLoss(decimal value)
    {
        _WttUnusedNdlLoss = value;
    }
    
    // Get<>AsString()
    public string GetWttUnusedNdlLossAsString()
    {
        return _WttUnusedNdlLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWttUnusedNdlLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttUnusedNdlLoss = parsed;
    }
    
    // Standard Getter
    public string GetWttDerivativeToFmSedol()
    {
        return _WttDerivativeToFmSedol;
    }
    
    // Standard Setter
    public void SetWttDerivativeToFmSedol(string value)
    {
        _WttDerivativeToFmSedol = value;
    }
    
    // Get<>AsString()
    public string GetWttDerivativeToFmSedolAsString()
    {
        return _WttDerivativeToFmSedol.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWttDerivativeToFmSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttDerivativeToFmSedol = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WttSort
    public class WttSort
    {
        private static int _size = 51;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WttLongSortDate, is_external=, is_static_class=False, static_prefix=
        private WttSort.WttLongSortDate _WttLongSortDate = new WttSort.WttLongSortDate();
        
        
        
        
        // [DEBUG] Field: WttSortTransferFlag, is_external=, is_static_class=False, static_prefix=
        private int _WttSortTransferFlag =0;
        
        
        // 88-level condition checks for WttSortTransferFlag
        public bool IsWttSortReindexationTo2013()
        {
            if (this._WttSortTransferFlag == 025) return true;
            return false;
        }
        public bool IsWttBaNotSettledAfterBargn()
        {
            if (this._WttSortTransferFlag == 050) return true;
            return false;
        }
        public bool IsWttSortAcqBal()
        {
            if (this._WttSortTransferFlag == 100) return true;
            return false;
        }
        public bool IsWttSortZeroDisp()
        {
            if (this._WttSortTransferFlag == 200) return true;
            return false;
        }
        public bool IsWttSortNonZeroDispRgDisp()
        {
            if (this._WttSortTransferFlag == 250) return true;
            return false;
        }
        public bool IsWttSortExerciseOption()
        {
            if (this._WttSortTransferFlag == 399) return true;
            return false;
        }
        public bool IsWttSortTrfDisp()
        {
            if (this._WttSortTransferFlag == 399) return true;
            return false;
        }
        public bool IsWttSortTrfAcq()
        {
            if (this._WttSortTransferFlag == 400) return true;
            return false;
        }
        public bool IsWttSortExerciseStock()
        {
            if (this._WttSortTransferFlag == 400) return true;
            return false;
        }
        public bool IsWttSortLastLoopSeq()
        {
            if (this._WttSortTransferFlag == 599) return true;
            return false;
        }
        public bool IsWttSortZeroDispBaWithRga()
        {
            if (this._WttSortTransferFlag == 600) return true;
            return false;
        }
        public bool IsWttSortTemporaryBalance()
        {
            if (this._WttSortTransferFlag == 650) return true;
            return false;
        }
        public bool IsWttSortNonZeroDisp()
        {
            if (this._WttSortTransferFlag == 700) return true;
            return false;
        }
        public bool IsWttSortBondDisp()
        {
            if (this._WttSortTransferFlag == 800) return true;
            return false;
        }
        public bool IsWttSortCreatePool()
        {
            if (this._WttSortTransferFlag == 900) return true;
            return false;
        }
        public bool IsWttSortOptionLapse()
        {
            if (this._WttSortTransferFlag == 900) return true;
            return false;
        }
        public bool IsWttBaSettledAfterBargain()
        {
            if (this._WttSortTransferFlag == 950) return true;
            return false;
        }
        public bool IsWttSortReindexationTill2012()
        {
            if (this._WttSortTransferFlag == 975) return true;
            return false;
        }
        public bool IsWttSortFutureDelivery()
        {
            if (this._WttSortTransferFlag == 980) return true;
            return false;
        }
        
        
        // [DEBUG] Field: WttSortTransferFlagX, is_external=, is_static_class=False, static_prefix=
        private string _WttSortTransferFlagX ="";
        
        
        
        
        // [DEBUG] Field: WttSortTransferFlag2, is_external=, is_static_class=False, static_prefix=
        private int _WttSortTransferFlag2 =0;
        
        
        // 88-level condition checks for WttSortTransferFlag2
        public bool IsWttSortTrfFlag2Other()
        {
            if (this._WttSortTransferFlag2 == 0) return true;
            return false;
        }
        public bool IsWttSortTfrFlag2MultiTb()
        {
            if (this._WttSortTransferFlag2 == 5) return true;
            return false;
        }
        public bool IsWttSortTfrFlag2IpExStk()
        {
            if (this._WttSortTransferFlag2 == 5) return true;
            return false;
        }
        
        
        // [DEBUG] Field: WttSortRecordCode, is_external=, is_static_class=False, static_prefix=
        private string _WttSortRecordCode ="";
        
        
        
        
        // [DEBUG] Field: WttSortContractNo, is_external=, is_static_class=False, static_prefix=
        private WttSort.WttSortContractNo _WttSortContractNo = new WttSort.WttSortContractNo();
        
        
        
        
        // [DEBUG] Field: WttSortSedolCode, is_external=, is_static_class=False, static_prefix=
        private string _WttSortSedolCode ="";
        
        
        
        
        // [DEBUG] Field: WttSortContractNo2, is_external=, is_static_class=False, static_prefix=
        private string _WttSortContractNo2 ="";
        
        
        
        
    public WttSort() {}
    
    public WttSort(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WttLongSortDate.SetWttLongSortDateAsString(data.Substring(offset, WttLongSortDate.GetSize()));
        offset += 8;
        SetWttSortTransferFlag(int.Parse(data.Substring(offset, 3).Trim()));
        offset += 3;
        SetWttSortTransferFlagX(data.Substring(offset, 3).Trim());
        offset += 3;
        SetWttSortTransferFlag2(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetWttSortRecordCode(data.Substring(offset, 2).Trim());
        offset += 2;
        _WttSortContractNo.SetWttSortContractNoAsString(data.Substring(offset, WttSortContractNo.GetSize()));
        offset += 17;
        SetWttSortSedolCode(data.Substring(offset, 7).Trim());
        offset += 7;
        SetWttSortContractNo2(data.Substring(offset, 10).Trim());
        offset += 10;
        
    }
    
    // Serialization methods
    public string GetWttSortAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WttLongSortDate.GetWttLongSortDateAsString());
        result.Append(_WttSortTransferFlag.ToString().PadLeft(3, '0'));
        result.Append(_WttSortTransferFlagX.PadRight(3));
        result.Append(_WttSortTransferFlag2.ToString().PadLeft(1, '0'));
        result.Append(_WttSortRecordCode.PadRight(2));
        result.Append(_WttSortContractNo.GetWttSortContractNoAsString());
        result.Append(_WttSortSedolCode.PadRight(7));
        result.Append(_WttSortContractNo2.PadRight(10));
        
        return result.ToString();
    }
    
    public void SetWttSortAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            _WttLongSortDate.SetWttLongSortDateAsString(data.Substring(offset, 8));
        }
        else
        {
            _WttLongSortDate.SetWttLongSortDateAsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttSortTransferFlag(parsedInt);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetWttSortTransferFlagX(extracted);
        }
        offset += 3;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttSortTransferFlag2(parsedInt);
        }
        offset += 1;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetWttSortRecordCode(extracted);
        }
        offset += 2;
        if (offset + 17 <= data.Length)
        {
            _WttSortContractNo.SetWttSortContractNoAsString(data.Substring(offset, 17));
        }
        else
        {
            _WttSortContractNo.SetWttSortContractNoAsString(data.Substring(offset));
        }
        offset += 17;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetWttSortSedolCode(extracted);
        }
        offset += 7;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetWttSortContractNo2(extracted);
        }
        offset += 10;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WttLongSortDate GetWttLongSortDate()
    {
        return _WttLongSortDate;
    }
    
    // Standard Setter
    public void SetWttLongSortDate(WttLongSortDate value)
    {
        _WttLongSortDate = value;
    }
    
    // Get<>AsString()
    public string GetWttLongSortDateAsString()
    {
        return _WttLongSortDate != null ? _WttLongSortDate.GetWttLongSortDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttLongSortDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttLongSortDate == null)
        {
            _WttLongSortDate = new WttLongSortDate();
        }
        _WttLongSortDate.SetWttLongSortDateAsString(value);
    }
    
    // Standard Getter
    public int GetWttSortTransferFlag()
    {
        return _WttSortTransferFlag;
    }
    
    // Standard Setter
    public void SetWttSortTransferFlag(int value)
    {
        _WttSortTransferFlag = value;
    }
    
    // Get<>AsString()
    public string GetWttSortTransferFlagAsString()
    {
        return _WttSortTransferFlag.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetWttSortTransferFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttSortTransferFlag = parsed;
    }
    
    // Standard Getter
    public string GetWttSortTransferFlagX()
    {
        return _WttSortTransferFlagX;
    }
    
    // Standard Setter
    public void SetWttSortTransferFlagX(string value)
    {
        _WttSortTransferFlagX = value;
    }
    
    // Get<>AsString()
    public string GetWttSortTransferFlagXAsString()
    {
        return _WttSortTransferFlagX.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetWttSortTransferFlagXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttSortTransferFlagX = value;
    }
    
    // Standard Getter
    public int GetWttSortTransferFlag2()
    {
        return _WttSortTransferFlag2;
    }
    
    // Standard Setter
    public void SetWttSortTransferFlag2(int value)
    {
        _WttSortTransferFlag2 = value;
    }
    
    // Get<>AsString()
    public string GetWttSortTransferFlag2AsString()
    {
        return _WttSortTransferFlag2.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetWttSortTransferFlag2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttSortTransferFlag2 = parsed;
    }
    
    // Standard Getter
    public string GetWttSortRecordCode()
    {
        return _WttSortRecordCode;
    }
    
    // Standard Setter
    public void SetWttSortRecordCode(string value)
    {
        _WttSortRecordCode = value;
    }
    
    // Get<>AsString()
    public string GetWttSortRecordCodeAsString()
    {
        return _WttSortRecordCode.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetWttSortRecordCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttSortRecordCode = value;
    }
    
    // Standard Getter
    public WttSortContractNo GetWttSortContractNo()
    {
        return _WttSortContractNo;
    }
    
    // Standard Setter
    public void SetWttSortContractNo(WttSortContractNo value)
    {
        _WttSortContractNo = value;
    }
    
    // Get<>AsString()
    public string GetWttSortContractNoAsString()
    {
        return _WttSortContractNo != null ? _WttSortContractNo.GetWttSortContractNoAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttSortContractNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttSortContractNo == null)
        {
            _WttSortContractNo = new WttSortContractNo();
        }
        _WttSortContractNo.SetWttSortContractNoAsString(value);
    }
    
    // Standard Getter
    public string GetWttSortSedolCode()
    {
        return _WttSortSedolCode;
    }
    
    // Standard Setter
    public void SetWttSortSedolCode(string value)
    {
        _WttSortSedolCode = value;
    }
    
    // Get<>AsString()
    public string GetWttSortSedolCodeAsString()
    {
        return _WttSortSedolCode.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetWttSortSedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttSortSedolCode = value;
    }
    
    // Standard Getter
    public string GetWttSortContractNo2()
    {
        return _WttSortContractNo2;
    }
    
    // Standard Setter
    public void SetWttSortContractNo2(string value)
    {
        _WttSortContractNo2 = value;
    }
    
    // Get<>AsString()
    public string GetWttSortContractNo2AsString()
    {
        return _WttSortContractNo2.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetWttSortContractNo2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WttSortContractNo2 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WttLongSortDate
    public class WttLongSortDate
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WttSortDateCc, is_external=, is_static_class=False, static_prefix=
        private int _WttSortDateCc =0;
        
        
        
        
        // [DEBUG] Field: WttSortDate, is_external=, is_static_class=False, static_prefix=
        private WttLongSortDate.WttSortDate _WttSortDate = new WttLongSortDate.WttSortDate();
        
        
        
        
    public WttLongSortDate() {}
    
    public WttLongSortDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWttSortDateCc(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        _WttSortDate.SetWttSortDateAsString(data.Substring(offset, WttSortDate.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetWttLongSortDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WttSortDateCc.ToString().PadLeft(2, '0'));
        result.Append(_WttSortDate.GetWttSortDateAsString());
        
        return result.ToString();
    }
    
    public void SetWttLongSortDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttSortDateCc(parsedInt);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _WttSortDate.SetWttSortDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _WttSortDate.SetWttSortDateAsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWttSortDateCc()
    {
        return _WttSortDateCc;
    }
    
    // Standard Setter
    public void SetWttSortDateCc(int value)
    {
        _WttSortDateCc = value;
    }
    
    // Get<>AsString()
    public string GetWttSortDateCcAsString()
    {
        return _WttSortDateCc.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWttSortDateCcAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttSortDateCc = parsed;
    }
    
    // Standard Getter
    public WttSortDate GetWttSortDate()
    {
        return _WttSortDate;
    }
    
    // Standard Setter
    public void SetWttSortDate(WttSortDate value)
    {
        _WttSortDate = value;
    }
    
    // Get<>AsString()
    public string GetWttSortDateAsString()
    {
        return _WttSortDate != null ? _WttSortDate.GetWttSortDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWttSortDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WttSortDate == null)
        {
            _WttSortDate = new WttSortDate();
        }
        _WttSortDate.SetWttSortDateAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WttSortDate
    public class WttSortDate
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WttSortDateYy, is_external=, is_static_class=False, static_prefix=
        private int _WttSortDateYy =0;
        
        
        
        
        // [DEBUG] Field: Filler462, is_external=, is_static_class=False, static_prefix=
        private string _Filler462 ="";
        
        
        
        
    public WttSortDate() {}
    
    public WttSortDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWttSortDateYy(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetFiller462(data.Substring(offset, 4).Trim());
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetWttSortDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WttSortDateYy.ToString().PadLeft(2, '0'));
        result.Append(_Filler462.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetWttSortDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWttSortDateYy(parsedInt);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller462(extracted);
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWttSortDateYy()
    {
        return _WttSortDateYy;
    }
    
    // Standard Setter
    public void SetWttSortDateYy(int value)
    {
        _WttSortDateYy = value;
    }
    
    // Get<>AsString()
    public string GetWttSortDateYyAsString()
    {
        return _WttSortDateYy.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWttSortDateYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WttSortDateYy = parsed;
    }
    
    // Standard Getter
    public string GetFiller462()
    {
        return _Filler462;
    }
    
    // Standard Setter
    public void SetFiller462(string value)
    {
        _Filler462 = value;
    }
    
    // Get<>AsString()
    public string GetFiller462AsString()
    {
        return _Filler462.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller462AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler462 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
// Nested Class: WttSortContractNo
public class WttSortContractNo
{
    private static int _size = 17;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttSortUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _WttSortUnits =0;
    
    
    
    
public WttSortContractNo() {}

public WttSortContractNo(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttSortUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    
}

// Serialization methods
public string GetWttSortContractNoAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttSortUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetWttSortContractNoAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttSortUnits(parsedDec);
    }
    offset += 17;
}

// Getter and Setter methods

// Standard Getter
public decimal GetWttSortUnits()
{
    return _WttSortUnits;
}

// Standard Setter
public void SetWttSortUnits(decimal value)
{
    _WttSortUnits = value;
}

// Get<>AsString()
public string GetWttSortUnitsAsString()
{
    return _WttSortUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttSortUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttSortUnits = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: WttIds
public class WttIds
{
    private static int _size = 89;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttBalAcqDispId, is_external=, is_static_class=False, static_prefix=
    private string _WttBalAcqDispId ="";
    
    
    
    
    // [DEBUG] Field: WttHoldingId, is_external=, is_static_class=False, static_prefix=
    private string _WttHoldingId ="";
    
    
    
    
    // [DEBUG] Field: WttParentStockId, is_external=, is_static_class=False, static_prefix=
    private string _WttParentStockId ="";
    
    
    
    
    // [DEBUG] Field: WttTransactionCategoryId, is_external=, is_static_class=False, static_prefix=
    private string _WttTransactionCategoryId ="";
    
    
    
    
    // [DEBUG] Field: WttTransactionCategoryId9, is_external=, is_static_class=False, static_prefix=
    private int _WttTransactionCategoryId9 =0;
    
    
    
    
    // [DEBUG] Field: WttCtLinkFundId, is_external=, is_static_class=False, static_prefix=
    private string _WttCtLinkFundId ="";
    
    
    
    
    // [DEBUG] Field: WttPrevDispId, is_external=, is_static_class=False, static_prefix=
    private string _WttPrevDispId ="";
    
    
    
    
    // [DEBUG] Field: WttPrevAcqnId, is_external=, is_static_class=False, static_prefix=
    private string _WttPrevAcqnId ="";
    
    
    
    
    // [DEBUG] Field: WttDbTimestamp, is_external=, is_static_class=False, static_prefix=
    private string _WttDbTimestamp ="";
    
    
    
    
    // [DEBUG] Field: WttIlgIndex, is_external=, is_static_class=False, static_prefix=
    private int _WttIlgIndex =0;
    
    
    
    
public WttIds() {}

public WttIds(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttBalAcqDispId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetWttHoldingId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetWttParentStockId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetWttTransactionCategoryId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetWttTransactionCategoryId9(int.Parse(data.Substring(offset, 8).Trim()));
    offset += 8;
    SetWttCtLinkFundId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetWttPrevDispId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetWttPrevAcqnId(data.Substring(offset, 8).Trim());
    offset += 8;
    SetWttDbTimestamp(data.Substring(offset, 24).Trim());
    offset += 24;
    SetWttIlgIndex(int.Parse(data.Substring(offset, 1).Trim()));
    offset += 1;
    
}

// Serialization methods
public string GetWttIdsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttBalAcqDispId.PadRight(8));
    result.Append(_WttHoldingId.PadRight(8));
    result.Append(_WttParentStockId.PadRight(8));
    result.Append(_WttTransactionCategoryId.PadRight(8));
    result.Append(_WttTransactionCategoryId9.ToString().PadLeft(8, '0'));
    result.Append(_WttCtLinkFundId.PadRight(8));
    result.Append(_WttPrevDispId.PadRight(8));
    result.Append(_WttPrevAcqnId.PadRight(8));
    result.Append(_WttDbTimestamp.PadRight(24));
    result.Append(_WttIlgIndex.ToString().PadLeft(1, '0'));
    
    return result.ToString();
}

public void SetWttIdsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWttBalAcqDispId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWttHoldingId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWttParentStockId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWttTransactionCategoryId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttTransactionCategoryId9(parsedInt);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWttCtLinkFundId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWttPrevDispId(extracted);
    }
    offset += 8;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWttPrevAcqnId(extracted);
    }
    offset += 8;
    if (offset + 24 <= data.Length)
    {
        string extracted = data.Substring(offset, 24).Trim();
        SetWttDbTimestamp(extracted);
    }
    offset += 24;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttIlgIndex(parsedInt);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetWttBalAcqDispId()
{
    return _WttBalAcqDispId;
}

// Standard Setter
public void SetWttBalAcqDispId(string value)
{
    _WttBalAcqDispId = value;
}

// Get<>AsString()
public string GetWttBalAcqDispIdAsString()
{
    return _WttBalAcqDispId.PadRight(8);
}

// Set<>AsString()
public void SetWttBalAcqDispIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttBalAcqDispId = value;
}

// Standard Getter
public string GetWttHoldingId()
{
    return _WttHoldingId;
}

// Standard Setter
public void SetWttHoldingId(string value)
{
    _WttHoldingId = value;
}

// Get<>AsString()
public string GetWttHoldingIdAsString()
{
    return _WttHoldingId.PadRight(8);
}

// Set<>AsString()
public void SetWttHoldingIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttHoldingId = value;
}

// Standard Getter
public string GetWttParentStockId()
{
    return _WttParentStockId;
}

// Standard Setter
public void SetWttParentStockId(string value)
{
    _WttParentStockId = value;
}

// Get<>AsString()
public string GetWttParentStockIdAsString()
{
    return _WttParentStockId.PadRight(8);
}

// Set<>AsString()
public void SetWttParentStockIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttParentStockId = value;
}

// Standard Getter
public string GetWttTransactionCategoryId()
{
    return _WttTransactionCategoryId;
}

// Standard Setter
public void SetWttTransactionCategoryId(string value)
{
    _WttTransactionCategoryId = value;
}

// Get<>AsString()
public string GetWttTransactionCategoryIdAsString()
{
    return _WttTransactionCategoryId.PadRight(8);
}

// Set<>AsString()
public void SetWttTransactionCategoryIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttTransactionCategoryId = value;
}

// Standard Getter
public int GetWttTransactionCategoryId9()
{
    return _WttTransactionCategoryId9;
}

// Standard Setter
public void SetWttTransactionCategoryId9(int value)
{
    _WttTransactionCategoryId9 = value;
}

// Get<>AsString()
public string GetWttTransactionCategoryId9AsString()
{
    return _WttTransactionCategoryId9.ToString().PadLeft(8, '0');
}

// Set<>AsString()
public void SetWttTransactionCategoryId9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttTransactionCategoryId9 = parsed;
}

// Standard Getter
public string GetWttCtLinkFundId()
{
    return _WttCtLinkFundId;
}

// Standard Setter
public void SetWttCtLinkFundId(string value)
{
    _WttCtLinkFundId = value;
}

// Get<>AsString()
public string GetWttCtLinkFundIdAsString()
{
    return _WttCtLinkFundId.PadRight(8);
}

// Set<>AsString()
public void SetWttCtLinkFundIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttCtLinkFundId = value;
}

// Standard Getter
public string GetWttPrevDispId()
{
    return _WttPrevDispId;
}

// Standard Setter
public void SetWttPrevDispId(string value)
{
    _WttPrevDispId = value;
}

// Get<>AsString()
public string GetWttPrevDispIdAsString()
{
    return _WttPrevDispId.PadRight(8);
}

// Set<>AsString()
public void SetWttPrevDispIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttPrevDispId = value;
}

// Standard Getter
public string GetWttPrevAcqnId()
{
    return _WttPrevAcqnId;
}

// Standard Setter
public void SetWttPrevAcqnId(string value)
{
    _WttPrevAcqnId = value;
}

// Get<>AsString()
public string GetWttPrevAcqnIdAsString()
{
    return _WttPrevAcqnId.PadRight(8);
}

// Set<>AsString()
public void SetWttPrevAcqnIdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttPrevAcqnId = value;
}

// Standard Getter
public string GetWttDbTimestamp()
{
    return _WttDbTimestamp;
}

// Standard Setter
public void SetWttDbTimestamp(string value)
{
    _WttDbTimestamp = value;
}

// Get<>AsString()
public string GetWttDbTimestampAsString()
{
    return _WttDbTimestamp.PadRight(24);
}

// Set<>AsString()
public void SetWttDbTimestampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttDbTimestamp = value;
}

// Standard Getter
public int GetWttIlgIndex()
{
    return _WttIlgIndex;
}

// Standard Setter
public void SetWttIlgIndex(int value)
{
    _WttIlgIndex = value;
}

// Get<>AsString()
public string GetWttIlgIndexAsString()
{
    return _WttIlgIndex.ToString().PadLeft(1, '0');
}

// Set<>AsString()
public void SetWttIlgIndexAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttIlgIndex = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: WttRecord
public class WttRecord
{
    private static int _size = 724;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttKey, is_external=, is_static_class=False, static_prefix=
    private WttRecord.WttKey _WttKey = new WttRecord.WttKey();
    
    
    
    
    // [DEBUG] Field: WttTransactionCategory, is_external=, is_static_class=False, static_prefix=
    private string _WttTransactionCategory ="";
    
    
    // 88-level condition checks for WttTransactionCategory
    public bool IsWttIdentifiableTran()
    {
        if (this._WttTransactionCategory == "'00'") return true;
        if (this._WttTransactionCategory == "'SP'") return true;
        if (this._WttTransactionCategory == "'PP'") return true;
        if (this._WttTransactionCategory == "'CT'") return true;
        if (this._WttTransactionCategory == "'PI'") return true;
        if (this._WttTransactionCategory == "'NP'") return true;
        if (this._WttTransactionCategory == "'TB'") return true;
        if (this._WttTransactionCategory == "'NI'") return true;
        if (this._WttTransactionCategory == "'RP'") return true;
        if (this._WttTransactionCategory == "'UL'") return true;
        if (this._WttTransactionCategory == "'01'") return true;
        if (this._WttTransactionCategory == "'SX'") return true;
        if (this._WttTransactionCategory == "'EC'") return true;
        if (this._WttTransactionCategory == "'WP'") return true;
        return false;
    }
    public bool IsWttApportionedTran()
    {
        if (this._WttTransactionCategory == "'RG'") return true;
        if (this._WttTransactionCategory == "'CN'") return true;
        if (this._WttTransactionCategory == "'TR'") return true;
        if (this._WttTransactionCategory == "'TS'") return true;
        if (this._WttTransactionCategory == "'CS'") return true;
        if (this._WttTransactionCategory == "'RO'") return true;
        if (this._WttTransactionCategory == "'TD'") return true;
        if (this._WttTransactionCategory == "'CD'") return true;
        if (this._WttTransactionCategory == "'RF'") return true;
        return false;
    }
    public bool IsWttApportionedTransfer()
    {
        if (this._WttTransactionCategory == "'RG'") return true;
        if (this._WttTransactionCategory == "'CN'") return true;
        if (this._WttTransactionCategory == "'TR'") return true;
        if (this._WttTransactionCategory == "'TS'") return true;
        if (this._WttTransactionCategory == "'CS'") return true;
        if (this._WttTransactionCategory == "'TD'") return true;
        if (this._WttTransactionCategory == "'RO'") return true;
        return false;
    }
    public bool IsWttGroupTransfer()
    {
        if (this._WttTransactionCategory == "'GS'") return true;
        if (this._WttTransactionCategory == "'GP'") return true;
        return false;
    }
    public bool IsWttChargeableTransfer()
    {
        if (this._WttTransactionCategory == "'CT'") return true;
        return false;
    }
    public bool IsWttTransToBeConsolidated()
    {
        if (this._WttTransactionCategory == "'RG'") return true;
        if (this._WttTransactionCategory == "'CN'") return true;
        if (this._WttTransactionCategory == "'TR'") return true;
        if (this._WttTransactionCategory == "'TS'") return true;
        if (this._WttTransactionCategory == "'CS'") return true;
        if (this._WttTransactionCategory == "'RO'") return true;
        if (this._WttTransactionCategory == "'NS'") return true;
        if (this._WttTransactionCategory == "'TD'") return true;
        if (this._WttTransactionCategory == "'CT'") return true;
        if (this._WttTransactionCategory == "'TP'") return true;
        if (this._WttTransactionCategory == "'CP'") return true;
        if (this._WttTransactionCategory == "'GP'") return true;
        if (this._WttTransactionCategory == "'GS'") return true;
        if (this._WttTransactionCategory == "'RS'") return true;
        if (this._WttTransactionCategory == "'RL'") return true;
        if (this._WttTransactionCategory == "'RD'") return true;
        if (this._WttTransactionCategory == "'EQ'") return true;
        if (this._WttTransactionCategory == "'CD'") return true;
        if (this._WttTransactionCategory == "'LQ'") return true;
        if (this._WttTransactionCategory == "'RF'") return true;
        if (this._WttTransactionCategory == "'SS'") return true;
        if (this._WttTransactionCategory == "'AC'") return true;
        if (this._WttTransactionCategory == "'EP'") return true;
        if (this._WttTransactionCategory == "'WC'") return true;
        if (this._WttTransactionCategory == "'SP'") return true;
        return false;
    }
    public bool IsWttDisposalTran()
    {
        if (this._WttTransactionCategory == "'RS'") return true;
        if (this._WttTransactionCategory == "'RL'") return true;
        if (this._WttTransactionCategory == "'RD'") return true;
        if (this._WttTransactionCategory == "'NS'") return true;
        if (this._WttTransactionCategory == "'CT'") return true;
        if (this._WttTransactionCategory == "'CD'") return true;
        if (this._WttTransactionCategory == "'LQ'") return true;
        if (this._WttTransactionCategory == "'RF'") return true;
        if (this._WttTransactionCategory == "'SS'") return true;
        return false;
    }
    public bool IsWttTransLinkedByBargain()
    {
        if (this._WttTransactionCategory == "'TS'") return true;
        if (this._WttTransactionCategory == "'CS'") return true;
        if (this._WttTransactionCategory == "'TP'") return true;
        if (this._WttTransactionCategory == "'CP'") return true;
        if (this._WttTransactionCategory == "'GP'") return true;
        if (this._WttTransactionCategory == "'GS'") return true;
        return false;
    }
    public bool IsWttTransLinkedByPrevRef()
    {
        if (this._WttTransactionCategory == "'RG'") return true;
        if (this._WttTransactionCategory == "'CN'") return true;
        if (this._WttTransactionCategory == "'TR'") return true;
        if (this._WttTransactionCategory == "'TD'") return true;
        if (this._WttTransactionCategory == "'RO'") return true;
        if (this._WttTransactionCategory == "'CL'") return true;
        if (this._WttTransactionCategory == "'RC'") return true;
        if (this._WttTransactionCategory == "'RR'") return true;
        return false;
    }
    public bool IsWttDualOrMultiTransaction()
    {
        if (this._WttTransactionCategory == "'RG'") return true;
        if (this._WttTransactionCategory == "'CN'") return true;
        if (this._WttTransactionCategory == "'TR'") return true;
        if (this._WttTransactionCategory == "'TD'") return true;
        if (this._WttTransactionCategory == "'RO'") return true;
        if (this._WttTransactionCategory == "'CS'") return true;
        if (this._WttTransactionCategory == "'CP'") return true;
        if (this._WttTransactionCategory == "'TS'") return true;
        if (this._WttTransactionCategory == "'TP'") return true;
        if (this._WttTransactionCategory == "'GS'") return true;
        if (this._WttTransactionCategory == "'GP'") return true;
        if (this._WttTransactionCategory == "'CT'") return true;
        if (this._WttTransactionCategory == "'EP'") return true;
        if (this._WttTransactionCategory == "'EC'") return true;
        if (this._WttTransactionCategory == "'WP'") return true;
        if (this._WttTransactionCategory == "'WC'") return true;
        if (this._WttTransactionCategory == "'CL'") return true;
        if (this._WttTransactionCategory == "'RC'") return true;
        if (this._WttTransactionCategory == "'RR'") return true;
        return false;
    }
    public bool IsWttBonusAllotment()
    {
        if (this._WttTransactionCategory == "'BA'") return true;
        return false;
    }
    public bool IsWttExercisePurchasedPut()
    {
        if (this._WttTransactionCategory == "'EP'") return true;
        return false;
    }
    public bool IsWttExercisePurchasedCall()
    {
        if (this._WttTransactionCategory == "'EC'") return true;
        return false;
    }
    public bool IsWttExerciseWrittenPut()
    {
        if (this._WttTransactionCategory == "'WP'") return true;
        return false;
    }
    public bool IsWttExerciseWrittenCall()
    {
        if (this._WttTransactionCategory == "'WC'") return true;
        return false;
    }
    public bool IsWttOptionLapse()
    {
        if (this._WttTransactionCategory == "'OL'") return true;
        return false;
    }
    public bool IsWttFutureDelivery()
    {
        if (this._WttTransactionCategory == "'FD'") return true;
        return false;
    }
    public bool IsWttDerivativeTransaction()
    {
        if (this._WttTransactionCategory == "'SS'") return true;
        if (this._WttTransactionCategory == "'DS'") return true;
        if (this._WttTransactionCategory == "'DP'") return true;
        if (this._WttTransactionCategory == "'SP'") return true;
        if (this._WttTransactionCategory == "'PP'") return true;
        if (this._WttTransactionCategory == "'EC'") return true;
        if (this._WttTransactionCategory == "'EP'") return true;
        if (this._WttTransactionCategory == "'WC'") return true;
        if (this._WttTransactionCategory == "'WP'") return true;
        if (this._WttTransactionCategory == "'OL'") return true;
        if (this._WttTransactionCategory == "'FD'") return true;
        if (this._WttTransactionCategory == "'00'") return true;
        return false;
    }
    public bool IsWttExerciseTransaction()
    {
        if (this._WttTransactionCategory == "'EC'") return true;
        if (this._WttTransactionCategory == "'EP'") return true;
        if (this._WttTransactionCategory == "'WC'") return true;
        if (this._WttTransactionCategory == "'WP'") return true;
        return false;
    }
    public bool IsWttCreateFa08PoolEvent()
    {
        if (this._WttTransactionCategory == "'P8'") return true;
        return false;
    }
    public bool IsWttBondRevaluation()
    {
        if (this._WttTransactionCategory == "'RV'") return true;
        return false;
    }
    public bool IsWttDummySale()
    {
        if (this._WttTransactionCategory == "'DS'") return true;
        return false;
    }
    public bool IsWttStockSale()
    {
        if (this._WttTransactionCategory == "'SS'") return true;
        return false;
    }
    public bool IsWttStockPurchase()
    {
        if (this._WttTransactionCategory == "'SP'") return true;
        return false;
    }
    public bool IsWttBalance()
    {
        if (this._WttTransactionCategory == "'PP'") return true;
        if (this._WttTransactionCategory == "'00'") return true;
        if (this._WttTransactionCategory == "'01'") return true;
        if (this._WttTransactionCategory == "'TB'") return true;
        return false;
    }
    public bool IsWttTemporaryBalance()
    {
        if (this._WttTransactionCategory == "'01'") return true;
        if (this._WttTransactionCategory == "'TB'") return true;
        return false;
    }
    public bool IsWttSystemGeneratedEvent()
    {
        if (this._WttTransactionCategory == "'P8'") return true;
        if (this._WttTransactionCategory == "'RV'") return true;
        if (this._WttTransactionCategory == "'DS'") return true;
        if (this._WttTransactionCategory == "'IP'") return true;
        if (this._WttTransactionCategory == "'DD'") return true;
        if (this._WttTransactionCategory == "'OL'") return true;
        if (this._WttTransactionCategory == "'RI'") return true;
        if (this._WttTransactionCategory == "'FD'") return true;
        return false;
    }
    public bool IsWttAcqnTransactions()
    {
        if (this._WttTransactionCategory == "'SP'") return true;
        if (this._WttTransactionCategory == "'EC'") return true;
        if (this._WttTransactionCategory == "'WC'") return true;
        if (this._WttTransactionCategory == "'WP'") return true;
        return false;
    }
    public bool IsWttPoolIndexation()
    {
        if (this._WttTransactionCategory == "'PP'") return true;
        if (this._WttTransactionCategory == "'EP'") return true;
        if (this._WttTransactionCategory == "'WC'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WttDateTimeStamp, is_external=, is_static_class=False, static_prefix=
    private WttRecord.WttDateTimeStamp _WttDateTimeStamp = new WttRecord.WttDateTimeStamp();
    
    
    
    
    // [DEBUG] Field: Filler464, is_external=, is_static_class=False, static_prefix=
    private WttRecord.Filler464 _Filler464 = new WttRecord.Filler464();
    
    
    
    
    // [DEBUG] Field: WttCalParentSedolCode, is_external=, is_static_class=False, static_prefix=
    private WttRecord.WttCalParentSedolCode _WttCalParentSedolCode = new WttRecord.WttCalParentSedolCode();
    
    
    
    
    // [DEBUG] Field: WttCalPreviousSedolCode, is_external=, is_static_class=False, static_prefix=
    private WttRecord.WttCalPreviousSedolCode _WttCalPreviousSedolCode = new WttRecord.WttCalPreviousSedolCode();
    
    
    
    
    // [DEBUG] Field: WttOriginalBargainNo, is_external=, is_static_class=False, static_prefix=
    private WttRecord.WttOriginalBargainNo _WttOriginalBargainNo = new WttRecord.WttOriginalBargainNo();
    
    
    
    
    // [DEBUG] Field: Filler466, is_external=, is_static_class=False, static_prefix=
    private WttRecord.Filler466 _Filler466 = new WttRecord.Filler466();
    
    
    
    
    // [DEBUG] Field: WttBargainDate, is_external=, is_static_class=False, static_prefix=
    private WttRecord.WttBargainDate _WttBargainDate = new WttRecord.WttBargainDate();
    
    
    
    
    // [DEBUG] Field: WttBargainDate9, is_external=, is_static_class=False, static_prefix=
    private int _WttBargainDate9 =0;
    
    
    // 88-level condition checks for WttBargainDate9
    public bool IsWttPost30Mar96()
    {
        if (this._WttBargainDate9 >= 960331 && this._WttBargainDate9 <= 991231) return true;
        if (this._WttBargainDate9 >= 101 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    public bool IsWtt06Apr65ToMar81()
    {
        if (this._WttBargainDate9 >= 650406 && this._WttBargainDate9 <= 810331) return true;
        return false;
    }
    public bool IsWttPre06Apr65()
    {
        if (this._WttBargainDate9 >= 450101 && this._WttBargainDate9 <= 650405) return true;
        return false;
    }
    public bool IsWttPost05Apr65()
    {
        if (this._WttBargainDate9 >= 650406 && this._WttBargainDate9 <= 991231) return true;
        if (this._WttBargainDate9 >= 101 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    public bool IsWttPostMar82()
    {
        if (this._WttBargainDate9 >= 820401 && this._WttBargainDate9 <= 991231) return true;
        if (this._WttBargainDate9 >= 101 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    public bool IsWttPreApr82()
    {
        if (this._WttBargainDate9 >= 450101 && this._WttBargainDate9 <= 820331) return true;
        return false;
    }
    public bool IsWttPostMar83()
    {
        if (this._WttBargainDate9 >= 830401 && this._WttBargainDate9 <= 991231) return true;
        if (this._WttBargainDate9 >= 101 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    public bool IsWttPost13Mar84()
    {
        if (this._WttBargainDate9 >= 840314 && this._WttBargainDate9 <= 991231) return true;
        if (this._WttBargainDate9 >= 101 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    public bool IsWttPostMar85()
    {
        if (this._WttBargainDate9 >= 850401 && this._WttBargainDate9 <= 991231) return true;
        if (this._WttBargainDate9 >= 101 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    public bool IsWttPre28Feb86()
    {
        if (this._WttBargainDate9 >= 450101 && this._WttBargainDate9 <= 860227) return true;
        return false;
    }
    public bool IsWttPost01Jul86()
    {
        if (this._WttBargainDate9 >= 860702 && this._WttBargainDate9 <= 991231) return true;
        if (this._WttBargainDate9 >= 101 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    public bool IsWttPre22Dec89()
    {
        if (this._WttBargainDate9 >= 450101 && this._WttBargainDate9 <= 891221) return true;
        return false;
    }
    public bool IsWttPost89()
    {
        if (this._WttBargainDate9 >= 890101 && this._WttBargainDate9 <= 991231) return true;
        if (this._WttBargainDate9 >= 101 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    public bool IsWttPost19Mar90()
    {
        if (this._WttBargainDate9 >= 900320 && this._WttBargainDate9 <= 991231) return true;
        if (this._WttBargainDate9 >= 101 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    public bool IsWttPost29Nov93()
    {
        if (this._WttBargainDate9 >= 931130 && this._WttBargainDate9 <= 991231) return true;
        if (this._WttBargainDate9 >= 101 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    public bool IsWttPostMar96()
    {
        if (this._WttBargainDate9 >= 960401 && this._WttBargainDate9 <= 991231) return true;
        if (this._WttBargainDate9 >= 101 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    public bool IsWttPreApr96()
    {
        if (this._WttBargainDate9 >= 450101 && this._WttBargainDate9 <= 960331) return true;
        return false;
    }
    public bool IsWttPost16Mar98()
    {
        if (this._WttBargainDate9 >= 980317 && this._WttBargainDate9 <= 991231) return true;
        if (this._WttBargainDate9 >= 101 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    public bool IsWttPost05Apr88()
    {
        if (this._WttBargainDate9 >= 880406 && this._WttBargainDate9 <= 991231) return true;
        if (this._WttBargainDate9 >= 101 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    public bool IsWttPost14Mar89()
    {
        if (this._WttBargainDate9 >= 880315 && this._WttBargainDate9 <= 991231) return true;
        if (this._WttBargainDate9 >= 101 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    public bool IsWttPost05Apr98()
    {
        if (this._WttBargainDate9 >= 980406 && this._WttBargainDate9 <= 991231) return true;
        if (this._WttBargainDate9 >= 101 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    public bool IsWttPre31Mar96()
    {
        if (this._WttBargainDate9 >= 450101 && this._WttBargainDate9 <= 960330) return true;
        return false;
    }
    public bool IsWttPost31Dec02()
    {
        if (this._WttBargainDate9 >= 30101 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    public bool IsWttPost05Apr08()
    {
        if (this._WttBargainDate9 >= 80406 && this._WttBargainDate9 <= 441231) return true;
        return false;
    }
    
    
    // [DEBUG] Field: WttSettlementDate, is_external=, is_static_class=False, static_prefix=
    private WttRecord.WttSettlementDate _WttSettlementDate = new WttRecord.WttSettlementDate();
    
    
    
    
    // [DEBUG] Field: WttCurrencyCode, is_external=, is_static_class=False, static_prefix=
    private string _WttCurrencyCode ="";
    
    
    
    
    // [DEBUG] Field: WttNotesComments, is_external=, is_static_class=False, static_prefix=
    private string _WttNotesComments ="";
    
    
    // 88-level condition checks for WttNotesComments
    public bool IsBondReacquisition()
    {
        if (this._WttNotesComments == "'BOND REACQUISITION'") return true;
        return false;
    }
    public bool IsLrToLrTransferBondPool()
    {
        if (this._WttNotesComments == "'LR TO LR TRANSFER BOND POOL'") return true;
        return false;
    }
    public bool IsEcOptionExercise()
    {
        if (this._WttNotesComments == "'EC EXERCISE:OPTION COST'") return true;
        return false;
    }
    public bool IsEpOptionExercise()
    {
        if (this._WttNotesComments == "'EP EXERCISE:OPTION COST'") return true;
        return false;
    }
    public bool IsWcOptionExercise()
    {
        if (this._WttNotesComments == "'WC EXERCISE:OPTION PROCEEDS'") return true;
        return false;
    }
    public bool IsWpOptionExercise()
    {
        if (this._WttNotesComments == "'WP EXERCISE:OPTION PROCEEDS'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WttBfNiPiFlag, is_external=, is_static_class=False, static_prefix=
    private string _WttBfNiPiFlag ="";
    
    
    
    
    // [DEBUG] Field: WttNiPiFlagYtd, is_external=, is_static_class=False, static_prefix=
    private string _WttNiPiFlagYtd ="";
    
    
    
    
    // [DEBUG] Field: WttTransactionExported, is_external=, is_static_class=False, static_prefix=
    private string _WttTransactionExported ="";
    
    
    
    
    // [DEBUG] Field: WttCtLinkFund, is_external=, is_static_class=False, static_prefix=
    private string _WttCtLinkFund ="";
    
    
    
    
    // [DEBUG] Field: WttSubFund, is_external=, is_static_class=False, static_prefix=
    private string _WttSubFund ="";
    
    
    
    
    // [DEBUG] Field: WttTransactionOverride, is_external=, is_static_class=False, static_prefix=
    private string _WttTransactionOverride ="";
    
    
    
    
    // [DEBUG] Field: WttParentMarketPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _WttParentMarketPrice =0;
    
    
    
    
    // [DEBUG] Field: WttTransUnitPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _WttTransUnitPrice =0;
    
    
    
    
    // [DEBUG] Field: WttPricePctIndicator, is_external=, is_static_class=False, static_prefix=
    private string _WttPricePctIndicator ="";
    
    
    
    
    // [DEBUG] Field: WttLiabilityPerShare, is_external=, is_static_class=False, static_prefix=
    private decimal _WttLiabilityPerShare =0;
    
    
    
    
    // [DEBUG] Field: WttOutstandingLiability, is_external=, is_static_class=False, static_prefix=
    private decimal _WttOutstandingLiability =0;
    
    
    
    
    // [DEBUG] Field: WttStockExchIndicator, is_external=, is_static_class=False, static_prefix=
    private string _WttStockExchIndicator ="";
    
    
    
    
    // [DEBUG] Field: WttRemainder, is_external=, is_static_class=False, static_prefix=
    private string _WttRemainder ="";
    
    
    
    
    // [DEBUG] Field: WttRecord02Fields, is_external=, is_static_class=False, static_prefix=
    private WttRecord.WttRecord02Fields _WttRecord02Fields = new WttRecord.WttRecord02Fields();
    
    
    
    
    // [DEBUG] Field: WttRecord03Fields, is_external=, is_static_class=False, static_prefix=
    private WttRecord.WttRecord03Fields _WttRecord03Fields = new WttRecord.WttRecord03Fields();
    
    
    
    
public WttRecord() {}

public WttRecord(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WttKey.SetWttKeyAsString(data.Substring(offset, WttKey.GetSize()));
    offset += 35;
    SetWttTransactionCategory(data.Substring(offset, 2).Trim());
    offset += 2;
    _WttDateTimeStamp.SetWttDateTimeStampAsString(data.Substring(offset, WttDateTimeStamp.GetSize()));
    offset += 14;
    _Filler464.SetFiller464AsString(data.Substring(offset, Filler464.GetSize()));
    offset += 14;
    _WttCalParentSedolCode.SetWttCalParentSedolCodeAsString(data.Substring(offset, WttCalParentSedolCode.GetSize()));
    offset += 11;
    _WttCalPreviousSedolCode.SetWttCalPreviousSedolCodeAsString(data.Substring(offset, WttCalPreviousSedolCode.GetSize()));
    offset += 18;
    _WttOriginalBargainNo.SetWttOriginalBargainNoAsString(data.Substring(offset, WttOriginalBargainNo.GetSize()));
    offset += 10;
    _Filler466.SetFiller466AsString(data.Substring(offset, Filler466.GetSize()));
    offset += 10;
    _WttBargainDate.SetWttBargainDateAsString(data.Substring(offset, WttBargainDate.GetSize()));
    offset += 6;
    SetWttBargainDate9(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    _WttSettlementDate.SetWttSettlementDateAsString(data.Substring(offset, WttSettlementDate.GetSize()));
    offset += 6;
    SetWttCurrencyCode(data.Substring(offset, 3).Trim());
    offset += 3;
    SetWttNotesComments(data.Substring(offset, 36).Trim());
    offset += 36;
    SetWttBfNiPiFlag(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWttNiPiFlagYtd(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWttTransactionExported(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWttCtLinkFund(data.Substring(offset, 4).Trim());
    offset += 4;
    SetWttSubFund(data.Substring(offset, 4).Trim());
    offset += 4;
    SetWttTransactionOverride(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttParentMarketPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 10)));
    offset += 10;
    SetWttTransUnitPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 6)));
    offset += 6;
    SetWttPricePctIndicator(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttLiabilityPerShare(PackedDecimalConverter.ToDecimal(data.Substring(offset, 6)));
    offset += 6;
    SetWttOutstandingLiability(PackedDecimalConverter.ToDecimal(data.Substring(offset, 6)));
    offset += 6;
    SetWttStockExchIndicator(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttRemainder(data.Substring(offset, 132).Trim());
    offset += 132;
    _WttRecord02Fields.SetWttRecord02FieldsAsString(data.Substring(offset, WttRecord02Fields.GetSize()));
    offset += 199;
    _WttRecord03Fields.SetWttRecord03FieldsAsString(data.Substring(offset, WttRecord03Fields.GetSize()));
    offset += 183;
    
}

// Serialization methods
public string GetWttRecordAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttKey.GetWttKeyAsString());
    result.Append(_WttTransactionCategory.PadRight(2));
    result.Append(_WttDateTimeStamp.GetWttDateTimeStampAsString());
    result.Append(_Filler464.GetFiller464AsString());
    result.Append(_WttCalParentSedolCode.GetWttCalParentSedolCodeAsString());
    result.Append(_WttCalPreviousSedolCode.GetWttCalPreviousSedolCodeAsString());
    result.Append(_WttOriginalBargainNo.GetWttOriginalBargainNoAsString());
    result.Append(_Filler466.GetFiller466AsString());
    result.Append(_WttBargainDate.GetWttBargainDateAsString());
    result.Append(_WttBargainDate9.ToString().PadLeft(6, '0'));
    result.Append(_WttSettlementDate.GetWttSettlementDateAsString());
    result.Append(_WttCurrencyCode.PadRight(3));
    result.Append(_WttNotesComments.PadRight(36));
    result.Append(_WttBfNiPiFlag.PadRight(0));
    result.Append(_WttNiPiFlagYtd.PadRight(0));
    result.Append(_WttTransactionExported.PadRight(0));
    result.Append(_WttCtLinkFund.PadRight(4));
    result.Append(_WttSubFund.PadRight(4));
    result.Append(_WttTransactionOverride.PadRight(1));
    result.Append(_WttParentMarketPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttTransUnitPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttPricePctIndicator.PadRight(1));
    result.Append(_WttLiabilityPerShare.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttOutstandingLiability.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttStockExchIndicator.PadRight(1));
    result.Append(_WttRemainder.PadRight(132));
    result.Append(_WttRecord02Fields.GetWttRecord02FieldsAsString());
    result.Append(_WttRecord03Fields.GetWttRecord03FieldsAsString());
    
    return result.ToString();
}

public void SetWttRecordAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 35 <= data.Length)
    {
        _WttKey.SetWttKeyAsString(data.Substring(offset, 35));
    }
    else
    {
        _WttKey.SetWttKeyAsString(data.Substring(offset));
    }
    offset += 35;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWttTransactionCategory(extracted);
    }
    offset += 2;
    if (offset + 14 <= data.Length)
    {
        _WttDateTimeStamp.SetWttDateTimeStampAsString(data.Substring(offset, 14));
    }
    else
    {
        _WttDateTimeStamp.SetWttDateTimeStampAsString(data.Substring(offset));
    }
    offset += 14;
    if (offset + 14 <= data.Length)
    {
        _Filler464.SetFiller464AsString(data.Substring(offset, 14));
    }
    else
    {
        _Filler464.SetFiller464AsString(data.Substring(offset));
    }
    offset += 14;
    if (offset + 11 <= data.Length)
    {
        _WttCalParentSedolCode.SetWttCalParentSedolCodeAsString(data.Substring(offset, 11));
    }
    else
    {
        _WttCalParentSedolCode.SetWttCalParentSedolCodeAsString(data.Substring(offset));
    }
    offset += 11;
    if (offset + 18 <= data.Length)
    {
        _WttCalPreviousSedolCode.SetWttCalPreviousSedolCodeAsString(data.Substring(offset, 18));
    }
    else
    {
        _WttCalPreviousSedolCode.SetWttCalPreviousSedolCodeAsString(data.Substring(offset));
    }
    offset += 18;
    if (offset + 10 <= data.Length)
    {
        _WttOriginalBargainNo.SetWttOriginalBargainNoAsString(data.Substring(offset, 10));
    }
    else
    {
        _WttOriginalBargainNo.SetWttOriginalBargainNoAsString(data.Substring(offset));
    }
    offset += 10;
    if (offset + 10 <= data.Length)
    {
        _Filler466.SetFiller466AsString(data.Substring(offset, 10));
    }
    else
    {
        _Filler466.SetFiller466AsString(data.Substring(offset));
    }
    offset += 10;
    if (offset + 6 <= data.Length)
    {
        _WttBargainDate.SetWttBargainDateAsString(data.Substring(offset, 6));
    }
    else
    {
        _WttBargainDate.SetWttBargainDateAsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttBargainDate9(parsedInt);
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        _WttSettlementDate.SetWttSettlementDateAsString(data.Substring(offset, 6));
    }
    else
    {
        _WttSettlementDate.SetWttSettlementDateAsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetWttCurrencyCode(extracted);
    }
    offset += 3;
    if (offset + 36 <= data.Length)
    {
        string extracted = data.Substring(offset, 36).Trim();
        SetWttNotesComments(extracted);
    }
    offset += 36;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWttBfNiPiFlag(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWttNiPiFlagYtd(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWttTransactionExported(extracted);
    }
    offset += 0;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWttCtLinkFund(extracted);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWttSubFund(extracted);
    }
    offset += 4;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttTransactionOverride(extracted);
    }
    offset += 1;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttParentMarketPrice(parsedDec);
    }
    offset += 10;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttTransUnitPrice(parsedDec);
    }
    offset += 6;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttPricePctIndicator(extracted);
    }
    offset += 1;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttLiabilityPerShare(parsedDec);
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttOutstandingLiability(parsedDec);
    }
    offset += 6;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttStockExchIndicator(extracted);
    }
    offset += 1;
    if (offset + 132 <= data.Length)
    {
        string extracted = data.Substring(offset, 132).Trim();
        SetWttRemainder(extracted);
    }
    offset += 132;
    if (offset + 199 <= data.Length)
    {
        _WttRecord02Fields.SetWttRecord02FieldsAsString(data.Substring(offset, 199));
    }
    else
    {
        _WttRecord02Fields.SetWttRecord02FieldsAsString(data.Substring(offset));
    }
    offset += 199;
    if (offset + 183 <= data.Length)
    {
        _WttRecord03Fields.SetWttRecord03FieldsAsString(data.Substring(offset, 183));
    }
    else
    {
        _WttRecord03Fields.SetWttRecord03FieldsAsString(data.Substring(offset));
    }
    offset += 183;
}

// Getter and Setter methods

// Standard Getter
public WttKey GetWttKey()
{
    return _WttKey;
}

// Standard Setter
public void SetWttKey(WttKey value)
{
    _WttKey = value;
}

// Get<>AsString()
public string GetWttKeyAsString()
{
    return _WttKey != null ? _WttKey.GetWttKeyAsString() : "";
}

// Set<>AsString()
public void SetWttKeyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttKey == null)
    {
        _WttKey = new WttKey();
    }
    _WttKey.SetWttKeyAsString(value);
}

// Standard Getter
public string GetWttTransactionCategory()
{
    return _WttTransactionCategory;
}

// Standard Setter
public void SetWttTransactionCategory(string value)
{
    _WttTransactionCategory = value;
}

// Get<>AsString()
public string GetWttTransactionCategoryAsString()
{
    return _WttTransactionCategory.PadRight(2);
}

// Set<>AsString()
public void SetWttTransactionCategoryAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttTransactionCategory = value;
}

// Standard Getter
public WttDateTimeStamp GetWttDateTimeStamp()
{
    return _WttDateTimeStamp;
}

// Standard Setter
public void SetWttDateTimeStamp(WttDateTimeStamp value)
{
    _WttDateTimeStamp = value;
}

// Get<>AsString()
public string GetWttDateTimeStampAsString()
{
    return _WttDateTimeStamp != null ? _WttDateTimeStamp.GetWttDateTimeStampAsString() : "";
}

// Set<>AsString()
public void SetWttDateTimeStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttDateTimeStamp == null)
    {
        _WttDateTimeStamp = new WttDateTimeStamp();
    }
    _WttDateTimeStamp.SetWttDateTimeStampAsString(value);
}

// Standard Getter
public Filler464 GetFiller464()
{
    return _Filler464;
}

// Standard Setter
public void SetFiller464(Filler464 value)
{
    _Filler464 = value;
}

// Get<>AsString()
public string GetFiller464AsString()
{
    return _Filler464 != null ? _Filler464.GetFiller464AsString() : "";
}

// Set<>AsString()
public void SetFiller464AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler464 == null)
    {
        _Filler464 = new Filler464();
    }
    _Filler464.SetFiller464AsString(value);
}

// Standard Getter
public WttCalParentSedolCode GetWttCalParentSedolCode()
{
    return _WttCalParentSedolCode;
}

// Standard Setter
public void SetWttCalParentSedolCode(WttCalParentSedolCode value)
{
    _WttCalParentSedolCode = value;
}

// Get<>AsString()
public string GetWttCalParentSedolCodeAsString()
{
    return _WttCalParentSedolCode != null ? _WttCalParentSedolCode.GetWttCalParentSedolCodeAsString() : "";
}

// Set<>AsString()
public void SetWttCalParentSedolCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttCalParentSedolCode == null)
    {
        _WttCalParentSedolCode = new WttCalParentSedolCode();
    }
    _WttCalParentSedolCode.SetWttCalParentSedolCodeAsString(value);
}

// Standard Getter
public WttCalPreviousSedolCode GetWttCalPreviousSedolCode()
{
    return _WttCalPreviousSedolCode;
}

// Standard Setter
public void SetWttCalPreviousSedolCode(WttCalPreviousSedolCode value)
{
    _WttCalPreviousSedolCode = value;
}

// Get<>AsString()
public string GetWttCalPreviousSedolCodeAsString()
{
    return _WttCalPreviousSedolCode != null ? _WttCalPreviousSedolCode.GetWttCalPreviousSedolCodeAsString() : "";
}

// Set<>AsString()
public void SetWttCalPreviousSedolCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttCalPreviousSedolCode == null)
    {
        _WttCalPreviousSedolCode = new WttCalPreviousSedolCode();
    }
    _WttCalPreviousSedolCode.SetWttCalPreviousSedolCodeAsString(value);
}

// Standard Getter
public WttOriginalBargainNo GetWttOriginalBargainNo()
{
    return _WttOriginalBargainNo;
}

// Standard Setter
public void SetWttOriginalBargainNo(WttOriginalBargainNo value)
{
    _WttOriginalBargainNo = value;
}

// Get<>AsString()
public string GetWttOriginalBargainNoAsString()
{
    return _WttOriginalBargainNo != null ? _WttOriginalBargainNo.GetWttOriginalBargainNoAsString() : "";
}

// Set<>AsString()
public void SetWttOriginalBargainNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttOriginalBargainNo == null)
    {
        _WttOriginalBargainNo = new WttOriginalBargainNo();
    }
    _WttOriginalBargainNo.SetWttOriginalBargainNoAsString(value);
}

// Standard Getter
public Filler466 GetFiller466()
{
    return _Filler466;
}

// Standard Setter
public void SetFiller466(Filler466 value)
{
    _Filler466 = value;
}

// Get<>AsString()
public string GetFiller466AsString()
{
    return _Filler466 != null ? _Filler466.GetFiller466AsString() : "";
}

// Set<>AsString()
public void SetFiller466AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler466 == null)
    {
        _Filler466 = new Filler466();
    }
    _Filler466.SetFiller466AsString(value);
}

// Standard Getter
public WttBargainDate GetWttBargainDate()
{
    return _WttBargainDate;
}

// Standard Setter
public void SetWttBargainDate(WttBargainDate value)
{
    _WttBargainDate = value;
}

// Get<>AsString()
public string GetWttBargainDateAsString()
{
    return _WttBargainDate != null ? _WttBargainDate.GetWttBargainDateAsString() : "";
}

// Set<>AsString()
public void SetWttBargainDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttBargainDate == null)
    {
        _WttBargainDate = new WttBargainDate();
    }
    _WttBargainDate.SetWttBargainDateAsString(value);
}

// Standard Getter
public int GetWttBargainDate9()
{
    return _WttBargainDate9;
}

// Standard Setter
public void SetWttBargainDate9(int value)
{
    _WttBargainDate9 = value;
}

// Get<>AsString()
public string GetWttBargainDate9AsString()
{
    return _WttBargainDate9.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetWttBargainDate9AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttBargainDate9 = parsed;
}

// Standard Getter
public WttSettlementDate GetWttSettlementDate()
{
    return _WttSettlementDate;
}

// Standard Setter
public void SetWttSettlementDate(WttSettlementDate value)
{
    _WttSettlementDate = value;
}

// Get<>AsString()
public string GetWttSettlementDateAsString()
{
    return _WttSettlementDate != null ? _WttSettlementDate.GetWttSettlementDateAsString() : "";
}

// Set<>AsString()
public void SetWttSettlementDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttSettlementDate == null)
    {
        _WttSettlementDate = new WttSettlementDate();
    }
    _WttSettlementDate.SetWttSettlementDateAsString(value);
}

// Standard Getter
public string GetWttCurrencyCode()
{
    return _WttCurrencyCode;
}

// Standard Setter
public void SetWttCurrencyCode(string value)
{
    _WttCurrencyCode = value;
}

// Get<>AsString()
public string GetWttCurrencyCodeAsString()
{
    return _WttCurrencyCode.PadRight(3);
}

// Set<>AsString()
public void SetWttCurrencyCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttCurrencyCode = value;
}

// Standard Getter
public string GetWttNotesComments()
{
    return _WttNotesComments;
}

// Standard Setter
public void SetWttNotesComments(string value)
{
    _WttNotesComments = value;
}

// Get<>AsString()
public string GetWttNotesCommentsAsString()
{
    return _WttNotesComments.PadRight(36);
}

// Set<>AsString()
public void SetWttNotesCommentsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttNotesComments = value;
}

// Standard Getter
public string GetWttBfNiPiFlag()
{
    return _WttBfNiPiFlag;
}

// Standard Setter
public void SetWttBfNiPiFlag(string value)
{
    _WttBfNiPiFlag = value;
}

// Get<>AsString()
public string GetWttBfNiPiFlagAsString()
{
    return _WttBfNiPiFlag.PadRight(0);
}

// Set<>AsString()
public void SetWttBfNiPiFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttBfNiPiFlag = value;
}

// Standard Getter
public string GetWttNiPiFlagYtd()
{
    return _WttNiPiFlagYtd;
}

// Standard Setter
public void SetWttNiPiFlagYtd(string value)
{
    _WttNiPiFlagYtd = value;
}

// Get<>AsString()
public string GetWttNiPiFlagYtdAsString()
{
    return _WttNiPiFlagYtd.PadRight(0);
}

// Set<>AsString()
public void SetWttNiPiFlagYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttNiPiFlagYtd = value;
}

// Standard Getter
public string GetWttTransactionExported()
{
    return _WttTransactionExported;
}

// Standard Setter
public void SetWttTransactionExported(string value)
{
    _WttTransactionExported = value;
}

// Get<>AsString()
public string GetWttTransactionExportedAsString()
{
    return _WttTransactionExported.PadRight(0);
}

// Set<>AsString()
public void SetWttTransactionExportedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttTransactionExported = value;
}

// Standard Getter
public string GetWttCtLinkFund()
{
    return _WttCtLinkFund;
}

// Standard Setter
public void SetWttCtLinkFund(string value)
{
    _WttCtLinkFund = value;
}

// Get<>AsString()
public string GetWttCtLinkFundAsString()
{
    return _WttCtLinkFund.PadRight(4);
}

// Set<>AsString()
public void SetWttCtLinkFundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttCtLinkFund = value;
}

// Standard Getter
public string GetWttSubFund()
{
    return _WttSubFund;
}

// Standard Setter
public void SetWttSubFund(string value)
{
    _WttSubFund = value;
}

// Get<>AsString()
public string GetWttSubFundAsString()
{
    return _WttSubFund.PadRight(4);
}

// Set<>AsString()
public void SetWttSubFundAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttSubFund = value;
}

// Standard Getter
public string GetWttTransactionOverride()
{
    return _WttTransactionOverride;
}

// Standard Setter
public void SetWttTransactionOverride(string value)
{
    _WttTransactionOverride = value;
}

// Get<>AsString()
public string GetWttTransactionOverrideAsString()
{
    return _WttTransactionOverride.PadRight(1);
}

// Set<>AsString()
public void SetWttTransactionOverrideAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttTransactionOverride = value;
}

// Standard Getter
public decimal GetWttParentMarketPrice()
{
    return _WttParentMarketPrice;
}

// Standard Setter
public void SetWttParentMarketPrice(decimal value)
{
    _WttParentMarketPrice = value;
}

// Get<>AsString()
public string GetWttParentMarketPriceAsString()
{
    return _WttParentMarketPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttParentMarketPriceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttParentMarketPrice = parsed;
}

// Standard Getter
public decimal GetWttTransUnitPrice()
{
    return _WttTransUnitPrice;
}

// Standard Setter
public void SetWttTransUnitPrice(decimal value)
{
    _WttTransUnitPrice = value;
}

// Get<>AsString()
public string GetWttTransUnitPriceAsString()
{
    return _WttTransUnitPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttTransUnitPriceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttTransUnitPrice = parsed;
}

// Standard Getter
public string GetWttPricePctIndicator()
{
    return _WttPricePctIndicator;
}

// Standard Setter
public void SetWttPricePctIndicator(string value)
{
    _WttPricePctIndicator = value;
}

// Get<>AsString()
public string GetWttPricePctIndicatorAsString()
{
    return _WttPricePctIndicator.PadRight(1);
}

// Set<>AsString()
public void SetWttPricePctIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttPricePctIndicator = value;
}

// Standard Getter
public decimal GetWttLiabilityPerShare()
{
    return _WttLiabilityPerShare;
}

// Standard Setter
public void SetWttLiabilityPerShare(decimal value)
{
    _WttLiabilityPerShare = value;
}

// Get<>AsString()
public string GetWttLiabilityPerShareAsString()
{
    return _WttLiabilityPerShare.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttLiabilityPerShareAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttLiabilityPerShare = parsed;
}

// Standard Getter
public decimal GetWttOutstandingLiability()
{
    return _WttOutstandingLiability;
}

// Standard Setter
public void SetWttOutstandingLiability(decimal value)
{
    _WttOutstandingLiability = value;
}

// Get<>AsString()
public string GetWttOutstandingLiabilityAsString()
{
    return _WttOutstandingLiability.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttOutstandingLiabilityAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttOutstandingLiability = parsed;
}

// Standard Getter
public string GetWttStockExchIndicator()
{
    return _WttStockExchIndicator;
}

// Standard Setter
public void SetWttStockExchIndicator(string value)
{
    _WttStockExchIndicator = value;
}

// Get<>AsString()
public string GetWttStockExchIndicatorAsString()
{
    return _WttStockExchIndicator.PadRight(1);
}

// Set<>AsString()
public void SetWttStockExchIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttStockExchIndicator = value;
}

// Standard Getter
public string GetWttRemainder()
{
    return _WttRemainder;
}

// Standard Setter
public void SetWttRemainder(string value)
{
    _WttRemainder = value;
}

// Get<>AsString()
public string GetWttRemainderAsString()
{
    return _WttRemainder.PadRight(132);
}

// Set<>AsString()
public void SetWttRemainderAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttRemainder = value;
}

// Standard Getter
public WttRecord02Fields GetWttRecord02Fields()
{
    return _WttRecord02Fields;
}

// Standard Setter
public void SetWttRecord02Fields(WttRecord02Fields value)
{
    _WttRecord02Fields = value;
}

// Get<>AsString()
public string GetWttRecord02FieldsAsString()
{
    return _WttRecord02Fields != null ? _WttRecord02Fields.GetWttRecord02FieldsAsString() : "";
}

// Set<>AsString()
public void SetWttRecord02FieldsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttRecord02Fields == null)
    {
        _WttRecord02Fields = new WttRecord02Fields();
    }
    _WttRecord02Fields.SetWttRecord02FieldsAsString(value);
}

// Standard Getter
public WttRecord03Fields GetWttRecord03Fields()
{
    return _WttRecord03Fields;
}

// Standard Setter
public void SetWttRecord03Fields(WttRecord03Fields value)
{
    _WttRecord03Fields = value;
}

// Get<>AsString()
public string GetWttRecord03FieldsAsString()
{
    return _WttRecord03Fields != null ? _WttRecord03Fields.GetWttRecord03FieldsAsString() : "";
}

// Set<>AsString()
public void SetWttRecord03FieldsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttRecord03Fields == null)
    {
        _WttRecord03Fields = new WttRecord03Fields();
    }
    _WttRecord03Fields.SetWttRecord03FieldsAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WttKey
public class WttKey
{
    private static int _size = 35;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttCalSedol, is_external=, is_static_class=False, static_prefix=
    private WttKey.WttCalSedol _WttCalSedol = new WttKey.WttCalSedol();
    
    
    
    
    // [DEBUG] Field: WttContractNo, is_external=, is_static_class=False, static_prefix=
    private WttKey.WttContractNo _WttContractNo = new WttKey.WttContractNo();
    
    
    
    
    // [DEBUG] Field: WttContractNoRed, is_external=, is_static_class=False, static_prefix=
    private string _WttContractNoRed ="";
    
    
    
    
    // [DEBUG] Field: WttRecordCode, is_external=, is_static_class=False, static_prefix=
    private int _WttRecordCode =0;
    
    
    
    
    // [DEBUG] Field: WttRecordCodeX, is_external=, is_static_class=False, static_prefix=
    private WttKey.WttRecordCodeX _WttRecordCodeX = new WttKey.WttRecordCodeX();
    
    
    
    
public WttKey() {}

public WttKey(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WttCalSedol.SetWttCalSedolAsString(data.Substring(offset, WttCalSedol.GetSize()));
    offset += 11;
    _WttContractNo.SetWttContractNoAsString(data.Substring(offset, WttContractNo.GetSize()));
    offset += 10;
    SetWttContractNoRed(data.Substring(offset, 10).Trim());
    offset += 10;
    SetWttRecordCode(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    _WttRecordCodeX.SetWttRecordCodeXAsString(data.Substring(offset, WttRecordCodeX.GetSize()));
    offset += 2;
    
}

// Serialization methods
public string GetWttKeyAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttCalSedol.GetWttCalSedolAsString());
    result.Append(_WttContractNo.GetWttContractNoAsString());
    result.Append(_WttContractNoRed.PadRight(10));
    result.Append(_WttRecordCode.ToString().PadLeft(2, '0'));
    result.Append(_WttRecordCodeX.GetWttRecordCodeXAsString());
    
    return result.ToString();
}

public void SetWttKeyAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 11 <= data.Length)
    {
        _WttCalSedol.SetWttCalSedolAsString(data.Substring(offset, 11));
    }
    else
    {
        _WttCalSedol.SetWttCalSedolAsString(data.Substring(offset));
    }
    offset += 11;
    if (offset + 10 <= data.Length)
    {
        _WttContractNo.SetWttContractNoAsString(data.Substring(offset, 10));
    }
    else
    {
        _WttContractNo.SetWttContractNoAsString(data.Substring(offset));
    }
    offset += 10;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetWttContractNoRed(extracted);
    }
    offset += 10;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttRecordCode(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        _WttRecordCodeX.SetWttRecordCodeXAsString(data.Substring(offset, 2));
    }
    else
    {
        _WttRecordCodeX.SetWttRecordCodeXAsString(data.Substring(offset));
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public WttCalSedol GetWttCalSedol()
{
    return _WttCalSedol;
}

// Standard Setter
public void SetWttCalSedol(WttCalSedol value)
{
    _WttCalSedol = value;
}

// Get<>AsString()
public string GetWttCalSedolAsString()
{
    return _WttCalSedol != null ? _WttCalSedol.GetWttCalSedolAsString() : "";
}

// Set<>AsString()
public void SetWttCalSedolAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttCalSedol == null)
    {
        _WttCalSedol = new WttCalSedol();
    }
    _WttCalSedol.SetWttCalSedolAsString(value);
}

// Standard Getter
public WttContractNo GetWttContractNo()
{
    return _WttContractNo;
}

// Standard Setter
public void SetWttContractNo(WttContractNo value)
{
    _WttContractNo = value;
}

// Get<>AsString()
public string GetWttContractNoAsString()
{
    return _WttContractNo != null ? _WttContractNo.GetWttContractNoAsString() : "";
}

// Set<>AsString()
public void SetWttContractNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttContractNo == null)
    {
        _WttContractNo = new WttContractNo();
    }
    _WttContractNo.SetWttContractNoAsString(value);
}

// Standard Getter
public string GetWttContractNoRed()
{
    return _WttContractNoRed;
}

// Standard Setter
public void SetWttContractNoRed(string value)
{
    _WttContractNoRed = value;
}

// Get<>AsString()
public string GetWttContractNoRedAsString()
{
    return _WttContractNoRed.PadRight(10);
}

// Set<>AsString()
public void SetWttContractNoRedAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttContractNoRed = value;
}

// Standard Getter
public int GetWttRecordCode()
{
    return _WttRecordCode;
}

// Standard Setter
public void SetWttRecordCode(int value)
{
    _WttRecordCode = value;
}

// Get<>AsString()
public string GetWttRecordCodeAsString()
{
    return _WttRecordCode.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttRecordCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttRecordCode = parsed;
}

// Standard Getter
public WttRecordCodeX GetWttRecordCodeX()
{
    return _WttRecordCodeX;
}

// Standard Setter
public void SetWttRecordCodeX(WttRecordCodeX value)
{
    _WttRecordCodeX = value;
}

// Get<>AsString()
public string GetWttRecordCodeXAsString()
{
    return _WttRecordCodeX != null ? _WttRecordCodeX.GetWttRecordCodeXAsString() : "";
}

// Set<>AsString()
public void SetWttRecordCodeXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttRecordCodeX == null)
    {
        _WttRecordCodeX = new WttRecordCodeX();
    }
    _WttRecordCodeX.SetWttRecordCodeXAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WttCalSedol
public class WttCalSedol
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttCoAcLk, is_external=, is_static_class=False, static_prefix=
    private string _WttCoAcLk ="";
    
    
    
    
    // [DEBUG] Field: WttSedol, is_external=, is_static_class=False, static_prefix=
    private string _WttSedol ="";
    
    
    
    
public WttCalSedol() {}

public WttCalSedol(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttCoAcLk(data.Substring(offset, 4).Trim());
    offset += 4;
    SetWttSedol(data.Substring(offset, 7).Trim());
    offset += 7;
    
}

// Serialization methods
public string GetWttCalSedolAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttCoAcLk.PadRight(4));
    result.Append(_WttSedol.PadRight(7));
    
    return result.ToString();
}

public void SetWttCalSedolAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWttCoAcLk(extracted);
    }
    offset += 4;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetWttSedol(extracted);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetWttCoAcLk()
{
    return _WttCoAcLk;
}

// Standard Setter
public void SetWttCoAcLk(string value)
{
    _WttCoAcLk = value;
}

// Get<>AsString()
public string GetWttCoAcLkAsString()
{
    return _WttCoAcLk.PadRight(4);
}

// Set<>AsString()
public void SetWttCoAcLkAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttCoAcLk = value;
}

// Standard Getter
public string GetWttSedol()
{
    return _WttSedol;
}

// Standard Setter
public void SetWttSedol(string value)
{
    _WttSedol = value;
}

// Get<>AsString()
public string GetWttSedolAsString()
{
    return _WttSedol.PadRight(7);
}

// Set<>AsString()
public void SetWttSedolAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttSedol = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WttContractNo
public class WttContractNo
{
    private static int _size = 10;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttContractPrefix, is_external=, is_static_class=False, static_prefix=
    private string _WttContractPrefix ="";
    
    
    
    
    // [DEBUG] Field: Filler463, is_external=, is_static_class=False, static_prefix=
    private string _Filler463 ="";
    
    
    
    
public WttContractNo() {}

public WttContractNo(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttContractPrefix(data.Substring(offset, 7).Trim());
    offset += 7;
    SetFiller463(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetWttContractNoAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttContractPrefix.PadRight(7));
    result.Append(_Filler463.PadRight(3));
    
    return result.ToString();
}

public void SetWttContractNoAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetWttContractPrefix(extracted);
    }
    offset += 7;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller463(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetWttContractPrefix()
{
    return _WttContractPrefix;
}

// Standard Setter
public void SetWttContractPrefix(string value)
{
    _WttContractPrefix = value;
}

// Get<>AsString()
public string GetWttContractPrefixAsString()
{
    return _WttContractPrefix.PadRight(7);
}

// Set<>AsString()
public void SetWttContractPrefixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttContractPrefix = value;
}

// Standard Getter
public string GetFiller463()
{
    return _Filler463;
}

// Standard Setter
public void SetFiller463(string value)
{
    _Filler463 = value;
}

// Get<>AsString()
public string GetFiller463AsString()
{
    return _Filler463.PadRight(3);
}

// Set<>AsString()
public void SetFiller463AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler463 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WttRecordCodeX
public class WttRecordCodeX
{
    private static int _size = 2;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttRecordCodeX1, is_external=, is_static_class=False, static_prefix=
    private string _WttRecordCodeX1 ="";
    
    
    
    
    // [DEBUG] Field: WttRecordCodeX2, is_external=, is_static_class=False, static_prefix=
    private string _WttRecordCodeX2 ="";
    
    
    
    
public WttRecordCodeX() {}

public WttRecordCodeX(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttRecordCodeX1(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttRecordCodeX2(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetWttRecordCodeXAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttRecordCodeX1.PadRight(1));
    result.Append(_WttRecordCodeX2.PadRight(1));
    
    return result.ToString();
}

public void SetWttRecordCodeXAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttRecordCodeX1(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttRecordCodeX2(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetWttRecordCodeX1()
{
    return _WttRecordCodeX1;
}

// Standard Setter
public void SetWttRecordCodeX1(string value)
{
    _WttRecordCodeX1 = value;
}

// Get<>AsString()
public string GetWttRecordCodeX1AsString()
{
    return _WttRecordCodeX1.PadRight(1);
}

// Set<>AsString()
public void SetWttRecordCodeX1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttRecordCodeX1 = value;
}

// Standard Getter
public string GetWttRecordCodeX2()
{
    return _WttRecordCodeX2;
}

// Standard Setter
public void SetWttRecordCodeX2(string value)
{
    _WttRecordCodeX2 = value;
}

// Get<>AsString()
public string GetWttRecordCodeX2AsString()
{
    return _WttRecordCodeX2.PadRight(1);
}

// Set<>AsString()
public void SetWttRecordCodeX2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttRecordCodeX2 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: WttDateTimeStamp
public class WttDateTimeStamp
{
    private static int _size = 14;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttDateStamp, is_external=, is_static_class=False, static_prefix=
    private string _WttDateStamp ="";
    
    
    
    
    // [DEBUG] Field: WttTimeStamp, is_external=, is_static_class=False, static_prefix=
    private string _WttTimeStamp ="";
    
    
    
    
public WttDateTimeStamp() {}

public WttDateTimeStamp(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttDateStamp(data.Substring(offset, 6).Trim());
    offset += 6;
    SetWttTimeStamp(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetWttDateTimeStampAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttDateStamp.PadRight(6));
    result.Append(_WttTimeStamp.PadRight(8));
    
    return result.ToString();
}

public void SetWttDateTimeStampAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetWttDateStamp(extracted);
    }
    offset += 6;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWttTimeStamp(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetWttDateStamp()
{
    return _WttDateStamp;
}

// Standard Setter
public void SetWttDateStamp(string value)
{
    _WttDateStamp = value;
}

// Get<>AsString()
public string GetWttDateStampAsString()
{
    return _WttDateStamp.PadRight(6);
}

// Set<>AsString()
public void SetWttDateStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttDateStamp = value;
}

// Standard Getter
public string GetWttTimeStamp()
{
    return _WttTimeStamp;
}

// Standard Setter
public void SetWttTimeStamp(string value)
{
    _WttTimeStamp = value;
}

// Get<>AsString()
public string GetWttTimeStampAsString()
{
    return _WttTimeStamp.PadRight(8);
}

// Set<>AsString()
public void SetWttTimeStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttTimeStamp = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler464
public class Filler464
{
    private static int _size = 14;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttStamp, is_external=, is_static_class=False, static_prefix=
    private string _WttStamp ="";
    
    
    
    
    // [DEBUG] Field: WttPartly, is_external=, is_static_class=False, static_prefix=
    private string _WttPartly ="";
    
    
    
    
public Filler464() {}

public Filler464(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttStamp(data.Substring(offset, 13).Trim());
    offset += 13;
    SetWttPartly(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetFiller464AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttStamp.PadRight(13));
    result.Append(_WttPartly.PadRight(1));
    
    return result.ToString();
}

public void SetFiller464AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetWttStamp(extracted);
    }
    offset += 13;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttPartly(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetWttStamp()
{
    return _WttStamp;
}

// Standard Setter
public void SetWttStamp(string value)
{
    _WttStamp = value;
}

// Get<>AsString()
public string GetWttStampAsString()
{
    return _WttStamp.PadRight(13);
}

// Set<>AsString()
public void SetWttStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttStamp = value;
}

// Standard Getter
public string GetWttPartly()
{
    return _WttPartly;
}

// Standard Setter
public void SetWttPartly(string value)
{
    _WttPartly = value;
}

// Get<>AsString()
public string GetWttPartlyAsString()
{
    return _WttPartly.PadRight(1);
}

// Set<>AsString()
public void SetWttPartlyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttPartly = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WttCalParentSedolCode
public class WttCalParentSedolCode
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttParentCal, is_external=, is_static_class=False, static_prefix=
    private string _WttParentCal ="";
    
    
    
    
    // [DEBUG] Field: WttParentSedolCode, is_external=, is_static_class=False, static_prefix=
    private int _WttParentSedolCode =0;
    
    
    
    
public WttCalParentSedolCode() {}

public WttCalParentSedolCode(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttParentCal(data.Substring(offset, 4).Trim());
    offset += 4;
    SetWttParentSedolCode(int.Parse(data.Substring(offset, 7).Trim()));
    offset += 7;
    
}

// Serialization methods
public string GetWttCalParentSedolCodeAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttParentCal.PadRight(4));
    result.Append(_WttParentSedolCode.ToString().PadLeft(7, '0'));
    
    return result.ToString();
}

public void SetWttCalParentSedolCodeAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWttParentCal(extracted);
    }
    offset += 4;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttParentSedolCode(parsedInt);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetWttParentCal()
{
    return _WttParentCal;
}

// Standard Setter
public void SetWttParentCal(string value)
{
    _WttParentCal = value;
}

// Get<>AsString()
public string GetWttParentCalAsString()
{
    return _WttParentCal.PadRight(4);
}

// Set<>AsString()
public void SetWttParentCalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttParentCal = value;
}

// Standard Getter
public int GetWttParentSedolCode()
{
    return _WttParentSedolCode;
}

// Standard Setter
public void SetWttParentSedolCode(int value)
{
    _WttParentSedolCode = value;
}

// Get<>AsString()
public string GetWttParentSedolCodeAsString()
{
    return _WttParentSedolCode.ToString().PadLeft(7, '0');
}

// Set<>AsString()
public void SetWttParentSedolCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttParentSedolCode = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WttCalPreviousSedolCode
public class WttCalPreviousSedolCode
{
    private static int _size = 18;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttPreviousCal, is_external=, is_static_class=False, static_prefix=
    private string _WttPreviousCal ="";
    
    
    
    
    // [DEBUG] Field: WttPreviousSedolCode, is_external=, is_static_class=False, static_prefix=
    private int _WttPreviousSedolCode =0;
    
    
    
    
    // [DEBUG] Field: WttPreviousSedolCodeX, is_external=, is_static_class=False, static_prefix=
    private string _WttPreviousSedolCodeX ="";
    
    
    
    
public WttCalPreviousSedolCode() {}

public WttCalPreviousSedolCode(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttPreviousCal(data.Substring(offset, 4).Trim());
    offset += 4;
    SetWttPreviousSedolCode(int.Parse(data.Substring(offset, 7).Trim()));
    offset += 7;
    SetWttPreviousSedolCodeX(data.Substring(offset, 7).Trim());
    offset += 7;
    
}

// Serialization methods
public string GetWttCalPreviousSedolCodeAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttPreviousCal.PadRight(4));
    result.Append(_WttPreviousSedolCode.ToString().PadLeft(7, '0'));
    result.Append(_WttPreviousSedolCodeX.PadRight(7));
    
    return result.ToString();
}

public void SetWttCalPreviousSedolCodeAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWttPreviousCal(extracted);
    }
    offset += 4;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttPreviousSedolCode(parsedInt);
    }
    offset += 7;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetWttPreviousSedolCodeX(extracted);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetWttPreviousCal()
{
    return _WttPreviousCal;
}

// Standard Setter
public void SetWttPreviousCal(string value)
{
    _WttPreviousCal = value;
}

// Get<>AsString()
public string GetWttPreviousCalAsString()
{
    return _WttPreviousCal.PadRight(4);
}

// Set<>AsString()
public void SetWttPreviousCalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttPreviousCal = value;
}

// Standard Getter
public int GetWttPreviousSedolCode()
{
    return _WttPreviousSedolCode;
}

// Standard Setter
public void SetWttPreviousSedolCode(int value)
{
    _WttPreviousSedolCode = value;
}

// Get<>AsString()
public string GetWttPreviousSedolCodeAsString()
{
    return _WttPreviousSedolCode.ToString().PadLeft(7, '0');
}

// Set<>AsString()
public void SetWttPreviousSedolCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttPreviousSedolCode = parsed;
}

// Standard Getter
public string GetWttPreviousSedolCodeX()
{
    return _WttPreviousSedolCodeX;
}

// Standard Setter
public void SetWttPreviousSedolCodeX(string value)
{
    _WttPreviousSedolCodeX = value;
}

// Get<>AsString()
public string GetWttPreviousSedolCodeXAsString()
{
    return _WttPreviousSedolCodeX.PadRight(7);
}

// Set<>AsString()
public void SetWttPreviousSedolCodeXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttPreviousSedolCodeX = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WttOriginalBargainNo
public class WttOriginalBargainNo
{
    private static int _size = 10;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttBargainPrefix, is_external=, is_static_class=False, static_prefix=
    private string _WttBargainPrefix ="";
    
    
    
    
    // [DEBUG] Field: Filler465, is_external=, is_static_class=False, static_prefix=
    private string _Filler465 ="";
    
    
    
    
public WttOriginalBargainNo() {}

public WttOriginalBargainNo(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttBargainPrefix(data.Substring(offset, 7).Trim());
    offset += 7;
    SetFiller465(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetWttOriginalBargainNoAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttBargainPrefix.PadRight(7));
    result.Append(_Filler465.PadRight(3));
    
    return result.ToString();
}

public void SetWttOriginalBargainNoAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetWttBargainPrefix(extracted);
    }
    offset += 7;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller465(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetWttBargainPrefix()
{
    return _WttBargainPrefix;
}

// Standard Setter
public void SetWttBargainPrefix(string value)
{
    _WttBargainPrefix = value;
}

// Get<>AsString()
public string GetWttBargainPrefixAsString()
{
    return _WttBargainPrefix.PadRight(7);
}

// Set<>AsString()
public void SetWttBargainPrefixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttBargainPrefix = value;
}

// Standard Getter
public string GetFiller465()
{
    return _Filler465;
}

// Standard Setter
public void SetFiller465(string value)
{
    _Filler465 = value;
}

// Get<>AsString()
public string GetFiller465AsString()
{
    return _Filler465.PadRight(3);
}

// Set<>AsString()
public void SetFiller465AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler465 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler466
public class Filler466
{
    private static int _size = 10;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler467, is_external=, is_static_class=False, static_prefix=
    private string _Filler467 ="";
    
    
    
    
    // [DEBUG] Field: WttBargainPrefix27, is_external=, is_static_class=False, static_prefix=
    private string _WttBargainPrefix27 ="";
    
    
    
    
    // [DEBUG] Field: Filler468, is_external=, is_static_class=False, static_prefix=
    private string _Filler468 ="";
    
    
    
    
public Filler466() {}

public Filler466(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller467(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttBargainPrefix27(data.Substring(offset, 6).Trim());
    offset += 6;
    SetFiller468(data.Substring(offset, 3).Trim());
    offset += 3;
    
}

// Serialization methods
public string GetFiller466AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler467.PadRight(1));
    result.Append(_WttBargainPrefix27.PadRight(6));
    result.Append(_Filler468.PadRight(3));
    
    return result.ToString();
}

public void SetFiller466AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetFiller467(extracted);
    }
    offset += 1;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetWttBargainPrefix27(extracted);
    }
    offset += 6;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller468(extracted);
    }
    offset += 3;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller467()
{
    return _Filler467;
}

// Standard Setter
public void SetFiller467(string value)
{
    _Filler467 = value;
}

// Get<>AsString()
public string GetFiller467AsString()
{
    return _Filler467.PadRight(1);
}

// Set<>AsString()
public void SetFiller467AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler467 = value;
}

// Standard Getter
public string GetWttBargainPrefix27()
{
    return _WttBargainPrefix27;
}

// Standard Setter
public void SetWttBargainPrefix27(string value)
{
    _WttBargainPrefix27 = value;
}

// Get<>AsString()
public string GetWttBargainPrefix27AsString()
{
    return _WttBargainPrefix27.PadRight(6);
}

// Set<>AsString()
public void SetWttBargainPrefix27AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttBargainPrefix27 = value;
}

// Standard Getter
public string GetFiller468()
{
    return _Filler468;
}

// Standard Setter
public void SetFiller468(string value)
{
    _Filler468 = value;
}

// Get<>AsString()
public string GetFiller468AsString()
{
    return _Filler468.PadRight(3);
}

// Set<>AsString()
public void SetFiller468AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler468 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WttBargainDate
public class WttBargainDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttBargainDateYymm, is_external=, is_static_class=False, static_prefix=
    private WttBargainDate.WttBargainDateYymm _WttBargainDateYymm = new WttBargainDate.WttBargainDateYymm();
    
    
    
    
    // [DEBUG] Field: WttBargainDateDd, is_external=, is_static_class=False, static_prefix=
    private int _WttBargainDateDd =0;
    
    
    
    
public WttBargainDate() {}

public WttBargainDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WttBargainDateYymm.SetWttBargainDateYymmAsString(data.Substring(offset, WttBargainDateYymm.GetSize()));
    offset += 4;
    SetWttBargainDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWttBargainDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttBargainDateYymm.GetWttBargainDateYymmAsString());
    result.Append(_WttBargainDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWttBargainDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        _WttBargainDateYymm.SetWttBargainDateYymmAsString(data.Substring(offset, 4));
    }
    else
    {
        _WttBargainDateYymm.SetWttBargainDateYymmAsString(data.Substring(offset));
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttBargainDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public WttBargainDateYymm GetWttBargainDateYymm()
{
    return _WttBargainDateYymm;
}

// Standard Setter
public void SetWttBargainDateYymm(WttBargainDateYymm value)
{
    _WttBargainDateYymm = value;
}

// Get<>AsString()
public string GetWttBargainDateYymmAsString()
{
    return _WttBargainDateYymm != null ? _WttBargainDateYymm.GetWttBargainDateYymmAsString() : "";
}

// Set<>AsString()
public void SetWttBargainDateYymmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttBargainDateYymm == null)
    {
        _WttBargainDateYymm = new WttBargainDateYymm();
    }
    _WttBargainDateYymm.SetWttBargainDateYymmAsString(value);
}

// Standard Getter
public int GetWttBargainDateDd()
{
    return _WttBargainDateDd;
}

// Standard Setter
public void SetWttBargainDateDd(int value)
{
    _WttBargainDateDd = value;
}

// Get<>AsString()
public string GetWttBargainDateDdAsString()
{
    return _WttBargainDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttBargainDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttBargainDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WttBargainDateYymm
public class WttBargainDateYymm
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttBargainDateYy, is_external=, is_static_class=False, static_prefix=
    private int _WttBargainDateYy =0;
    
    
    
    
    // [DEBUG] Field: WttBargainDateMm, is_external=, is_static_class=False, static_prefix=
    private int _WttBargainDateMm =0;
    
    
    
    
public WttBargainDateYymm() {}

public WttBargainDateYymm(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttBargainDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWttBargainDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWttBargainDateYymmAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttBargainDateYy.ToString().PadLeft(2, '0'));
    result.Append(_WttBargainDateMm.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWttBargainDateYymmAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttBargainDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttBargainDateMm(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWttBargainDateYy()
{
    return _WttBargainDateYy;
}

// Standard Setter
public void SetWttBargainDateYy(int value)
{
    _WttBargainDateYy = value;
}

// Get<>AsString()
public string GetWttBargainDateYyAsString()
{
    return _WttBargainDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttBargainDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttBargainDateYy = parsed;
}

// Standard Getter
public int GetWttBargainDateMm()
{
    return _WttBargainDateMm;
}

// Standard Setter
public void SetWttBargainDateMm(int value)
{
    _WttBargainDateMm = value;
}

// Get<>AsString()
public string GetWttBargainDateMmAsString()
{
    return _WttBargainDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttBargainDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttBargainDateMm = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: WttSettlementDate
public class WttSettlementDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttSettlementDateYy, is_external=, is_static_class=False, static_prefix=
    private int _WttSettlementDateYy =0;
    
    
    
    
    // [DEBUG] Field: WttSettlementDateMm, is_external=, is_static_class=False, static_prefix=
    private int _WttSettlementDateMm =0;
    
    
    
    
    // [DEBUG] Field: WttSettlementDateDd, is_external=, is_static_class=False, static_prefix=
    private int _WttSettlementDateDd =0;
    
    
    
    
public WttSettlementDate() {}

public WttSettlementDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttSettlementDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWttSettlementDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWttSettlementDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWttSettlementDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttSettlementDateYy.ToString().PadLeft(2, '0'));
    result.Append(_WttSettlementDateMm.ToString().PadLeft(2, '0'));
    result.Append(_WttSettlementDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWttSettlementDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttSettlementDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttSettlementDateMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttSettlementDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWttSettlementDateYy()
{
    return _WttSettlementDateYy;
}

// Standard Setter
public void SetWttSettlementDateYy(int value)
{
    _WttSettlementDateYy = value;
}

// Get<>AsString()
public string GetWttSettlementDateYyAsString()
{
    return _WttSettlementDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttSettlementDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttSettlementDateYy = parsed;
}

// Standard Getter
public int GetWttSettlementDateMm()
{
    return _WttSettlementDateMm;
}

// Standard Setter
public void SetWttSettlementDateMm(int value)
{
    _WttSettlementDateMm = value;
}

// Get<>AsString()
public string GetWttSettlementDateMmAsString()
{
    return _WttSettlementDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttSettlementDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttSettlementDateMm = parsed;
}

// Standard Getter
public int GetWttSettlementDateDd()
{
    return _WttSettlementDateDd;
}

// Standard Setter
public void SetWttSettlementDateDd(int value)
{
    _WttSettlementDateDd = value;
}

// Get<>AsString()
public string GetWttSettlementDateDdAsString()
{
    return _WttSettlementDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttSettlementDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttSettlementDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WttRecord02Fields
public class WttRecord02Fields
{
    private static int _size = 199;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttBfTrancheTotalUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _WttBfTrancheTotalUnits =0;
    
    
    
    
    // [DEBUG] Field: WttTrancheTotalUnitsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WttTrancheTotalUnitsYtd =0;
    
    
    
    
    // [DEBUG] Field: WttBfDispUnitsReac, is_external=, is_static_class=False, static_prefix=
    private decimal _WttBfDispUnitsReac =0;
    
    
    
    
    // [DEBUG] Field: WttDispUnitsReacYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WttDispUnitsReacYtd =0;
    
    
    
    
    // [DEBUG] Field: WttUnderwritingCommission, is_external=, is_static_class=False, static_prefix=
    private decimal _WttUnderwritingCommission =0;
    
    
    
    
    // [DEBUG] Field: WttBookCost, is_external=, is_static_class=False, static_prefix=
    private decimal _WttBookCost =0;
    
    
    
    
    // [DEBUG] Field: WttIssueSameClass, is_external=, is_static_class=False, static_prefix=
    private string _WttIssueSameClass ="";
    
    
    
    
    // [DEBUG] Field: WttBfDatePrevOpEvent, is_external=, is_static_class=False, static_prefix=
    private int _WttBfDatePrevOpEvent =0;
    
    
    
    
    // [DEBUG] Field: WttDatePrevOpEventYtd, is_external=, is_static_class=False, static_prefix=
    private WttRecord02Fields.WttDatePrevOpEventYtd _WttDatePrevOpEventYtd = new WttRecord02Fields.WttDatePrevOpEventYtd();
    
    
    
    
    // [DEBUG] Field: WttFirstDayDealingPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _WttFirstDayDealingPrice =0;
    
    
    
    
    // [DEBUG] Field: WttBfIndexedCostBalance, is_external=, is_static_class=False, static_prefix=
    private decimal _WttBfIndexedCostBalance =0;
    
    
    
    
    // [DEBUG] Field: WttIndexedCostBalanceYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WttIndexedCostBalanceYtd =0;
    
    
    
    
    // [DEBUG] Field: WttUnindexableFlag, is_external=, is_static_class=False, static_prefix=
    private string _WttUnindexableFlag ="";
    
    
    // 88-level condition checks for WttUnindexableFlag
    public bool IsWttUnindexableHolding()
    {
        if (this._WttUnindexableFlag == "'1'") return true;
        return false;
    }
    public bool IsWttMerged1982Pool()
    {
        if (this._WttUnindexableFlag == "'2'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WttGroupTransferFlag, is_external=, is_static_class=False, static_prefix=
    private string _WttGroupTransferFlag ="";
    
    
    // 88-level condition checks for WttGroupTransferFlag
    public bool IsWttGroupTransferBalance()
    {
        if (this._WttGroupTransferFlag == "'1'") return true;
        return false;
    }
    public bool IsWttReorgBalance()
    {
        if (this._WttGroupTransferFlag == "'2'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WttBfLinkRecordIndic, is_external=, is_static_class=False, static_prefix=
    private int _WttBfLinkRecordIndic =0;
    
    
    
    
    // [DEBUG] Field: WttLinkRecordIndicYtd, is_external=, is_static_class=False, static_prefix=
    private int _WttLinkRecordIndicYtd =0;
    
    
    
    
    // [DEBUG] Field: WttBfIndex85CostBalance, is_external=, is_static_class=False, static_prefix=
    private decimal _WttBfIndex85CostBalance =0;
    
    
    
    
    // [DEBUG] Field: WttIndex85CostBalanceYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WttIndex85CostBalanceYtd =0;
    
    
    
    
    // [DEBUG] Field: WttTrancheFlag, is_external=, is_static_class=False, static_prefix=
    private string _WttTrancheFlag ="";
    
    
    
    
    // [DEBUG] Field: WttResetTaperDate, is_external=, is_static_class=False, static_prefix=
    private string _WttResetTaperDate ="";
    
    
    
    
    // [DEBUG] Field: WttNoOfInitialBookCosts, is_external=, is_static_class=False, static_prefix=
    private int _WttNoOfInitialBookCosts =0;
    
    
    
    
    // [DEBUG] Field: WttBfNoOfCostsHeld, is_external=, is_static_class=False, static_prefix=
    private int _WttBfNoOfCostsHeld =0;
    
    
    
    
    // [DEBUG] Field: WttBfNoOfCostsHeldX, is_external=, is_static_class=False, static_prefix=
    private string _WttBfNoOfCostsHeldX ="";
    
    
    
    
    // [DEBUG] Field: WttNoOfCostsHeldYtd, is_external=, is_static_class=False, static_prefix=
    private int _WttNoOfCostsHeldYtd =0;
    
    
    
    
    // [DEBUG] Field: WttNoOfCostsHeldYtdX, is_external=, is_static_class=False, static_prefix=
    private string _WttNoOfCostsHeldYtdX ="";
    
    
    
    
    // [DEBUG] Field: WttCapitalGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WttCapitalGainLoss =0;
    
    
    
    
    // [DEBUG] Field: WttProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WttProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: WttProceedsOfDisposal, is_external=, is_static_class=False, static_prefix=
    private decimal _WttProceedsOfDisposal =0;
    
    
    
    
public WttRecord02Fields() {}

public WttRecord02Fields(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttBfTrancheTotalUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetWttTrancheTotalUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetWttBfDispUnitsReac(PackedDecimalConverter.ToDecimal(data.Substring(offset, 7)));
    offset += 7;
    SetWttDispUnitsReacYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 7)));
    offset += 7;
    SetWttUnderwritingCommission(PackedDecimalConverter.ToDecimal(data.Substring(offset, 7)));
    offset += 7;
    SetWttBookCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 11)));
    offset += 11;
    SetWttIssueSameClass(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttBfDatePrevOpEvent(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    _WttDatePrevOpEventYtd.SetWttDatePrevOpEventYtdAsString(data.Substring(offset, WttDatePrevOpEventYtd.GetSize()));
    offset += 6;
    SetWttFirstDayDealingPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 6)));
    offset += 6;
    SetWttBfIndexedCostBalance(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetWttIndexedCostBalanceYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetWttUnindexableFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttGroupTransferFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttBfLinkRecordIndic(int.Parse(data.Substring(offset, 1).Trim()));
    offset += 1;
    SetWttLinkRecordIndicYtd(int.Parse(data.Substring(offset, 1).Trim()));
    offset += 1;
    SetWttBfIndex85CostBalance(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetWttIndex85CostBalanceYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetWttTrancheFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttResetTaperDate(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttNoOfInitialBookCosts(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWttBfNoOfCostsHeld(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWttBfNoOfCostsHeldX(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWttNoOfCostsHeldYtd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWttNoOfCostsHeldYtdX(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWttCapitalGainLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWttProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWttProceedsOfDisposal(PackedDecimalConverter.ToDecimal(data.Substring(offset, 8)));
    offset += 8;
    
}

// Serialization methods
public string GetWttRecord02FieldsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttBfTrancheTotalUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttTrancheTotalUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttBfDispUnitsReac.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttDispUnitsReacYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttUnderwritingCommission.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttBookCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttIssueSameClass.PadRight(1));
    result.Append(_WttBfDatePrevOpEvent.ToString().PadLeft(6, '0'));
    result.Append(_WttDatePrevOpEventYtd.GetWttDatePrevOpEventYtdAsString());
    result.Append(_WttFirstDayDealingPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttBfIndexedCostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttIndexedCostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttUnindexableFlag.PadRight(1));
    result.Append(_WttGroupTransferFlag.PadRight(1));
    result.Append(_WttBfLinkRecordIndic.ToString().PadLeft(1, '0'));
    result.Append(_WttLinkRecordIndicYtd.ToString().PadLeft(1, '0'));
    result.Append(_WttBfIndex85CostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttIndex85CostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttTrancheFlag.PadRight(1));
    result.Append(_WttResetTaperDate.PadRight(1));
    result.Append(_WttNoOfInitialBookCosts.ToString().PadLeft(2, '0'));
    result.Append(_WttBfNoOfCostsHeld.ToString().PadLeft(2, '0'));
    result.Append(_WttBfNoOfCostsHeldX.PadRight(2));
    result.Append(_WttNoOfCostsHeldYtd.ToString().PadLeft(2, '0'));
    result.Append(_WttNoOfCostsHeldYtdX.PadRight(2));
    result.Append(_WttCapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttProceedsOfDisposal.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    
    return result.ToString();
}

public void SetWttRecord02FieldsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttBfTrancheTotalUnits(parsedDec);
    }
    offset += 13;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttTrancheTotalUnitsYtd(parsedDec);
    }
    offset += 13;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttBfDispUnitsReac(parsedDec);
    }
    offset += 7;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttDispUnitsReacYtd(parsedDec);
    }
    offset += 7;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttUnderwritingCommission(parsedDec);
    }
    offset += 7;
    if (offset + 11 <= data.Length)
    {
        string extracted = data.Substring(offset, 11).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttBookCost(parsedDec);
    }
    offset += 11;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttIssueSameClass(extracted);
    }
    offset += 1;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttBfDatePrevOpEvent(parsedInt);
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        _WttDatePrevOpEventYtd.SetWttDatePrevOpEventYtdAsString(data.Substring(offset, 6));
    }
    else
    {
        _WttDatePrevOpEventYtd.SetWttDatePrevOpEventYtdAsString(data.Substring(offset));
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttFirstDayDealingPrice(parsedDec);
    }
    offset += 6;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttBfIndexedCostBalance(parsedDec);
    }
    offset += 17;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttIndexedCostBalanceYtd(parsedDec);
    }
    offset += 17;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttUnindexableFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttGroupTransferFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttBfLinkRecordIndic(parsedInt);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttLinkRecordIndicYtd(parsedInt);
    }
    offset += 1;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttBfIndex85CostBalance(parsedDec);
    }
    offset += 17;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttIndex85CostBalanceYtd(parsedDec);
    }
    offset += 17;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttTrancheFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttResetTaperDate(extracted);
    }
    offset += 1;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttNoOfInitialBookCosts(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttBfNoOfCostsHeld(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWttBfNoOfCostsHeldX(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttNoOfCostsHeldYtd(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWttNoOfCostsHeldYtdX(extracted);
    }
    offset += 2;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttCapitalGainLoss(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttProfitLoss(parsedDec);
    }
    offset += 15;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttProceedsOfDisposal(parsedDec);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public decimal GetWttBfTrancheTotalUnits()
{
    return _WttBfTrancheTotalUnits;
}

// Standard Setter
public void SetWttBfTrancheTotalUnits(decimal value)
{
    _WttBfTrancheTotalUnits = value;
}

// Get<>AsString()
public string GetWttBfTrancheTotalUnitsAsString()
{
    return _WttBfTrancheTotalUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttBfTrancheTotalUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttBfTrancheTotalUnits = parsed;
}

// Standard Getter
public decimal GetWttTrancheTotalUnitsYtd()
{
    return _WttTrancheTotalUnitsYtd;
}

// Standard Setter
public void SetWttTrancheTotalUnitsYtd(decimal value)
{
    _WttTrancheTotalUnitsYtd = value;
}

// Get<>AsString()
public string GetWttTrancheTotalUnitsYtdAsString()
{
    return _WttTrancheTotalUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttTrancheTotalUnitsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttTrancheTotalUnitsYtd = parsed;
}

// Standard Getter
public decimal GetWttBfDispUnitsReac()
{
    return _WttBfDispUnitsReac;
}

// Standard Setter
public void SetWttBfDispUnitsReac(decimal value)
{
    _WttBfDispUnitsReac = value;
}

// Get<>AsString()
public string GetWttBfDispUnitsReacAsString()
{
    return _WttBfDispUnitsReac.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttBfDispUnitsReacAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttBfDispUnitsReac = parsed;
}

// Standard Getter
public decimal GetWttDispUnitsReacYtd()
{
    return _WttDispUnitsReacYtd;
}

// Standard Setter
public void SetWttDispUnitsReacYtd(decimal value)
{
    _WttDispUnitsReacYtd = value;
}

// Get<>AsString()
public string GetWttDispUnitsReacYtdAsString()
{
    return _WttDispUnitsReacYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttDispUnitsReacYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttDispUnitsReacYtd = parsed;
}

// Standard Getter
public decimal GetWttUnderwritingCommission()
{
    return _WttUnderwritingCommission;
}

// Standard Setter
public void SetWttUnderwritingCommission(decimal value)
{
    _WttUnderwritingCommission = value;
}

// Get<>AsString()
public string GetWttUnderwritingCommissionAsString()
{
    return _WttUnderwritingCommission.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttUnderwritingCommissionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttUnderwritingCommission = parsed;
}

// Standard Getter
public decimal GetWttBookCost()
{
    return _WttBookCost;
}

// Standard Setter
public void SetWttBookCost(decimal value)
{
    _WttBookCost = value;
}

// Get<>AsString()
public string GetWttBookCostAsString()
{
    return _WttBookCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttBookCostAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttBookCost = parsed;
}

// Standard Getter
public string GetWttIssueSameClass()
{
    return _WttIssueSameClass;
}

// Standard Setter
public void SetWttIssueSameClass(string value)
{
    _WttIssueSameClass = value;
}

// Get<>AsString()
public string GetWttIssueSameClassAsString()
{
    return _WttIssueSameClass.PadRight(1);
}

// Set<>AsString()
public void SetWttIssueSameClassAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttIssueSameClass = value;
}

// Standard Getter
public int GetWttBfDatePrevOpEvent()
{
    return _WttBfDatePrevOpEvent;
}

// Standard Setter
public void SetWttBfDatePrevOpEvent(int value)
{
    _WttBfDatePrevOpEvent = value;
}

// Get<>AsString()
public string GetWttBfDatePrevOpEventAsString()
{
    return _WttBfDatePrevOpEvent.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetWttBfDatePrevOpEventAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttBfDatePrevOpEvent = parsed;
}

// Standard Getter
public WttDatePrevOpEventYtd GetWttDatePrevOpEventYtd()
{
    return _WttDatePrevOpEventYtd;
}

// Standard Setter
public void SetWttDatePrevOpEventYtd(WttDatePrevOpEventYtd value)
{
    _WttDatePrevOpEventYtd = value;
}

// Get<>AsString()
public string GetWttDatePrevOpEventYtdAsString()
{
    return _WttDatePrevOpEventYtd != null ? _WttDatePrevOpEventYtd.GetWttDatePrevOpEventYtdAsString() : "";
}

// Set<>AsString()
public void SetWttDatePrevOpEventYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WttDatePrevOpEventYtd == null)
    {
        _WttDatePrevOpEventYtd = new WttDatePrevOpEventYtd();
    }
    _WttDatePrevOpEventYtd.SetWttDatePrevOpEventYtdAsString(value);
}

// Standard Getter
public decimal GetWttFirstDayDealingPrice()
{
    return _WttFirstDayDealingPrice;
}

// Standard Setter
public void SetWttFirstDayDealingPrice(decimal value)
{
    _WttFirstDayDealingPrice = value;
}

// Get<>AsString()
public string GetWttFirstDayDealingPriceAsString()
{
    return _WttFirstDayDealingPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttFirstDayDealingPriceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttFirstDayDealingPrice = parsed;
}

// Standard Getter
public decimal GetWttBfIndexedCostBalance()
{
    return _WttBfIndexedCostBalance;
}

// Standard Setter
public void SetWttBfIndexedCostBalance(decimal value)
{
    _WttBfIndexedCostBalance = value;
}

// Get<>AsString()
public string GetWttBfIndexedCostBalanceAsString()
{
    return _WttBfIndexedCostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttBfIndexedCostBalanceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttBfIndexedCostBalance = parsed;
}

// Standard Getter
public decimal GetWttIndexedCostBalanceYtd()
{
    return _WttIndexedCostBalanceYtd;
}

// Standard Setter
public void SetWttIndexedCostBalanceYtd(decimal value)
{
    _WttIndexedCostBalanceYtd = value;
}

// Get<>AsString()
public string GetWttIndexedCostBalanceYtdAsString()
{
    return _WttIndexedCostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttIndexedCostBalanceYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttIndexedCostBalanceYtd = parsed;
}

// Standard Getter
public string GetWttUnindexableFlag()
{
    return _WttUnindexableFlag;
}

// Standard Setter
public void SetWttUnindexableFlag(string value)
{
    _WttUnindexableFlag = value;
}

// Get<>AsString()
public string GetWttUnindexableFlagAsString()
{
    return _WttUnindexableFlag.PadRight(1);
}

// Set<>AsString()
public void SetWttUnindexableFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttUnindexableFlag = value;
}

// Standard Getter
public string GetWttGroupTransferFlag()
{
    return _WttGroupTransferFlag;
}

// Standard Setter
public void SetWttGroupTransferFlag(string value)
{
    _WttGroupTransferFlag = value;
}

// Get<>AsString()
public string GetWttGroupTransferFlagAsString()
{
    return _WttGroupTransferFlag.PadRight(1);
}

// Set<>AsString()
public void SetWttGroupTransferFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttGroupTransferFlag = value;
}

// Standard Getter
public int GetWttBfLinkRecordIndic()
{
    return _WttBfLinkRecordIndic;
}

// Standard Setter
public void SetWttBfLinkRecordIndic(int value)
{
    _WttBfLinkRecordIndic = value;
}

// Get<>AsString()
public string GetWttBfLinkRecordIndicAsString()
{
    return _WttBfLinkRecordIndic.ToString().PadLeft(1, '0');
}

// Set<>AsString()
public void SetWttBfLinkRecordIndicAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttBfLinkRecordIndic = parsed;
}

// Standard Getter
public int GetWttLinkRecordIndicYtd()
{
    return _WttLinkRecordIndicYtd;
}

// Standard Setter
public void SetWttLinkRecordIndicYtd(int value)
{
    _WttLinkRecordIndicYtd = value;
}

// Get<>AsString()
public string GetWttLinkRecordIndicYtdAsString()
{
    return _WttLinkRecordIndicYtd.ToString().PadLeft(1, '0');
}

// Set<>AsString()
public void SetWttLinkRecordIndicYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttLinkRecordIndicYtd = parsed;
}

// Standard Getter
public decimal GetWttBfIndex85CostBalance()
{
    return _WttBfIndex85CostBalance;
}

// Standard Setter
public void SetWttBfIndex85CostBalance(decimal value)
{
    _WttBfIndex85CostBalance = value;
}

// Get<>AsString()
public string GetWttBfIndex85CostBalanceAsString()
{
    return _WttBfIndex85CostBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttBfIndex85CostBalanceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttBfIndex85CostBalance = parsed;
}

// Standard Getter
public decimal GetWttIndex85CostBalanceYtd()
{
    return _WttIndex85CostBalanceYtd;
}

// Standard Setter
public void SetWttIndex85CostBalanceYtd(decimal value)
{
    _WttIndex85CostBalanceYtd = value;
}

// Get<>AsString()
public string GetWttIndex85CostBalanceYtdAsString()
{
    return _WttIndex85CostBalanceYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttIndex85CostBalanceYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttIndex85CostBalanceYtd = parsed;
}

// Standard Getter
public string GetWttTrancheFlag()
{
    return _WttTrancheFlag;
}

// Standard Setter
public void SetWttTrancheFlag(string value)
{
    _WttTrancheFlag = value;
}

// Get<>AsString()
public string GetWttTrancheFlagAsString()
{
    return _WttTrancheFlag.PadRight(1);
}

// Set<>AsString()
public void SetWttTrancheFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttTrancheFlag = value;
}

// Standard Getter
public string GetWttResetTaperDate()
{
    return _WttResetTaperDate;
}

// Standard Setter
public void SetWttResetTaperDate(string value)
{
    _WttResetTaperDate = value;
}

// Get<>AsString()
public string GetWttResetTaperDateAsString()
{
    return _WttResetTaperDate.PadRight(1);
}

// Set<>AsString()
public void SetWttResetTaperDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttResetTaperDate = value;
}

// Standard Getter
public int GetWttNoOfInitialBookCosts()
{
    return _WttNoOfInitialBookCosts;
}

// Standard Setter
public void SetWttNoOfInitialBookCosts(int value)
{
    _WttNoOfInitialBookCosts = value;
}

// Get<>AsString()
public string GetWttNoOfInitialBookCostsAsString()
{
    return _WttNoOfInitialBookCosts.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttNoOfInitialBookCostsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttNoOfInitialBookCosts = parsed;
}

// Standard Getter
public int GetWttBfNoOfCostsHeld()
{
    return _WttBfNoOfCostsHeld;
}

// Standard Setter
public void SetWttBfNoOfCostsHeld(int value)
{
    _WttBfNoOfCostsHeld = value;
}

// Get<>AsString()
public string GetWttBfNoOfCostsHeldAsString()
{
    return _WttBfNoOfCostsHeld.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttBfNoOfCostsHeldAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttBfNoOfCostsHeld = parsed;
}

// Standard Getter
public string GetWttBfNoOfCostsHeldX()
{
    return _WttBfNoOfCostsHeldX;
}

// Standard Setter
public void SetWttBfNoOfCostsHeldX(string value)
{
    _WttBfNoOfCostsHeldX = value;
}

// Get<>AsString()
public string GetWttBfNoOfCostsHeldXAsString()
{
    return _WttBfNoOfCostsHeldX.PadRight(2);
}

// Set<>AsString()
public void SetWttBfNoOfCostsHeldXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttBfNoOfCostsHeldX = value;
}

// Standard Getter
public int GetWttNoOfCostsHeldYtd()
{
    return _WttNoOfCostsHeldYtd;
}

// Standard Setter
public void SetWttNoOfCostsHeldYtd(int value)
{
    _WttNoOfCostsHeldYtd = value;
}

// Get<>AsString()
public string GetWttNoOfCostsHeldYtdAsString()
{
    return _WttNoOfCostsHeldYtd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttNoOfCostsHeldYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttNoOfCostsHeldYtd = parsed;
}

// Standard Getter
public string GetWttNoOfCostsHeldYtdX()
{
    return _WttNoOfCostsHeldYtdX;
}

// Standard Setter
public void SetWttNoOfCostsHeldYtdX(string value)
{
    _WttNoOfCostsHeldYtdX = value;
}

// Get<>AsString()
public string GetWttNoOfCostsHeldYtdXAsString()
{
    return _WttNoOfCostsHeldYtdX.PadRight(2);
}

// Set<>AsString()
public void SetWttNoOfCostsHeldYtdXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttNoOfCostsHeldYtdX = value;
}

// Standard Getter
public decimal GetWttCapitalGainLoss()
{
    return _WttCapitalGainLoss;
}

// Standard Setter
public void SetWttCapitalGainLoss(decimal value)
{
    _WttCapitalGainLoss = value;
}

// Get<>AsString()
public string GetWttCapitalGainLossAsString()
{
    return _WttCapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttCapitalGainLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttCapitalGainLoss = parsed;
}

// Standard Getter
public decimal GetWttProfitLoss()
{
    return _WttProfitLoss;
}

// Standard Setter
public void SetWttProfitLoss(decimal value)
{
    _WttProfitLoss = value;
}

// Get<>AsString()
public string GetWttProfitLossAsString()
{
    return _WttProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttProfitLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttProfitLoss = parsed;
}

// Standard Getter
public decimal GetWttProceedsOfDisposal()
{
    return _WttProceedsOfDisposal;
}

// Standard Setter
public void SetWttProceedsOfDisposal(decimal value)
{
    _WttProceedsOfDisposal = value;
}

// Get<>AsString()
public string GetWttProceedsOfDisposalAsString()
{
    return _WttProceedsOfDisposal.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttProceedsOfDisposalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttProceedsOfDisposal = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WttDatePrevOpEventYtd
public class WttDatePrevOpEventYtd
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttYymmPrevOpEventYtd, is_external=, is_static_class=False, static_prefix=
    private int _WttYymmPrevOpEventYtd =0;
    
    
    
    
    // [DEBUG] Field: WttDdPrevOpEventYtd, is_external=, is_static_class=False, static_prefix=
    private int _WttDdPrevOpEventYtd =0;
    
    
    
    
public WttDatePrevOpEventYtd() {}

public WttDatePrevOpEventYtd(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttYymmPrevOpEventYtd(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetWttDdPrevOpEventYtd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetWttDatePrevOpEventYtdAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttYymmPrevOpEventYtd.ToString().PadLeft(4, '0'));
    result.Append(_WttDdPrevOpEventYtd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetWttDatePrevOpEventYtdAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttYymmPrevOpEventYtd(parsedInt);
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWttDdPrevOpEventYtd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWttYymmPrevOpEventYtd()
{
    return _WttYymmPrevOpEventYtd;
}

// Standard Setter
public void SetWttYymmPrevOpEventYtd(int value)
{
    _WttYymmPrevOpEventYtd = value;
}

// Get<>AsString()
public string GetWttYymmPrevOpEventYtdAsString()
{
    return _WttYymmPrevOpEventYtd.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetWttYymmPrevOpEventYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttYymmPrevOpEventYtd = parsed;
}

// Standard Getter
public int GetWttDdPrevOpEventYtd()
{
    return _WttDdPrevOpEventYtd;
}

// Standard Setter
public void SetWttDdPrevOpEventYtd(int value)
{
    _WttDdPrevOpEventYtd = value;
}

// Get<>AsString()
public string GetWttDdPrevOpEventYtdAsString()
{
    return _WttDdPrevOpEventYtd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWttDdPrevOpEventYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WttDdPrevOpEventYtd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: WttRecord03Fields
public class WttRecord03Fields
{
    private static int _size = 183;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttNumberOfUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _WttNumberOfUnits =0;
    
    
    
    
    // [DEBUG] Field: WttProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _WttProceeds =0;
    
    
    
    
    // [DEBUG] Field: WttCapitalGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WttCapitalGainLoss =0;
    
    
    
    
    // [DEBUG] Field: WttForce2PctMatchFlag, is_external=, is_static_class=False, static_prefix=
    private string _WttForce2PctMatchFlag ="";
    
    
    
    
    // [DEBUG] Field: WttCgtCost, is_external=, is_static_class=False, static_prefix=
    private decimal _WttCgtCost =0;
    
    
    
    
    // [DEBUG] Field: WttProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WttProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: WttIndexation, is_external=, is_static_class=False, static_prefix=
    private decimal _WttIndexation =0;
    
    
    
    
    // [DEBUG] Field: WttBondDisposal, is_external=, is_static_class=False, static_prefix=
    private string _WttBondDisposal ="";
    
    
    
    
    // [DEBUG] Field: WtdConsolidatedFlag, is_external=, is_static_class=False, static_prefix=
    private string _WtdConsolidatedFlag ="";
    
    
    
    
    // [DEBUG] Field: WtdStoreUnits, is_external=, is_static_class=False, static_prefix=
    private decimal _WtdStoreUnits =0;
    
    
    
    
    // [DEBUG] Field: WtdStoreProceeds, is_external=, is_static_class=False, static_prefix=
    private decimal _WtdStoreProceeds =0;
    
    
    
    
    // [DEBUG] Field: WtdConsolidateKey, is_external=, is_static_class=False, static_prefix=
    private string _WtdConsolidateKey ="";
    
    
    
    
    // [DEBUG] Field: WttFa03Exemption, is_external=, is_static_class=False, static_prefix=
    private string _WttFa03Exemption ="";
    
    
    
    
    // [DEBUG] Field: WttGtProRataMatch, is_external=, is_static_class=False, static_prefix=
    private string _WttGtProRataMatch ="";
    
    
    
    
    // [DEBUG] Field: WttGtUseOriginalDates, is_external=, is_static_class=False, static_prefix=
    private string _WttGtUseOriginalDates ="";
    
    
    
    
    // [DEBUG] Field: WttNumberOfUnitsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WttNumberOfUnitsYtd =0;
    
    
    
    
    // [DEBUG] Field: WttProceedsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WttProceedsYtd =0;
    
    
    
    
    // [DEBUG] Field: Filler469, is_external=, is_static_class=False, static_prefix=
    private string _Filler469 ="";
    
    
    
    
public WttRecord03Fields() {}

public WttRecord03Fields(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttNumberOfUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetWttProceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWttCapitalGainLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWttForce2PctMatchFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWttCgtCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWttProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWttIndexation(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWttBondDisposal(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWtdConsolidatedFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWtdStoreUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetWtdStoreProceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWtdConsolidateKey(data.Substring(offset, 23).Trim());
    offset += 23;
    SetWttFa03Exemption(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWttGtProRataMatch(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWttGtUseOriginalDates(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWttNumberOfUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 9)));
    offset += 9;
    SetWttProceedsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 9)));
    offset += 9;
    SetFiller469(data.Substring(offset, 23).Trim());
    offset += 23;
    
}

// Serialization methods
public string GetWttRecord03FieldsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttNumberOfUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttCapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttForce2PctMatchFlag.PadRight(1));
    result.Append(_WttCgtCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttBondDisposal.PadRight(1));
    result.Append(_WtdConsolidatedFlag.PadRight(1));
    result.Append(_WtdStoreUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtdStoreProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtdConsolidateKey.PadRight(23));
    result.Append(_WttFa03Exemption.PadRight(0));
    result.Append(_WttGtProRataMatch.PadRight(0));
    result.Append(_WttGtUseOriginalDates.PadRight(0));
    result.Append(_WttNumberOfUnitsYtd.ToString("F3", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WttProceedsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler469.PadRight(23));
    
    return result.ToString();
}

public void SetWttRecord03FieldsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttNumberOfUnits(parsedDec);
    }
    offset += 13;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttProceeds(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttCapitalGainLoss(parsedDec);
    }
    offset += 15;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttForce2PctMatchFlag(extracted);
    }
    offset += 1;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttCgtCost(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttProfitLoss(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttIndexation(parsedDec);
    }
    offset += 15;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWttBondDisposal(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtdConsolidatedFlag(extracted);
    }
    offset += 1;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtdStoreUnits(parsedDec);
    }
    offset += 13;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtdStoreProceeds(parsedDec);
    }
    offset += 15;
    if (offset + 23 <= data.Length)
    {
        string extracted = data.Substring(offset, 23).Trim();
        SetWtdConsolidateKey(extracted);
    }
    offset += 23;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWttFa03Exemption(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWttGtProRataMatch(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWttGtUseOriginalDates(extracted);
    }
    offset += 0;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttNumberOfUnitsYtd(parsedDec);
    }
    offset += 9;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWttProceedsYtd(parsedDec);
    }
    offset += 9;
    if (offset + 23 <= data.Length)
    {
        string extracted = data.Substring(offset, 23).Trim();
        SetFiller469(extracted);
    }
    offset += 23;
}

// Getter and Setter methods

// Standard Getter
public decimal GetWttNumberOfUnits()
{
    return _WttNumberOfUnits;
}

// Standard Setter
public void SetWttNumberOfUnits(decimal value)
{
    _WttNumberOfUnits = value;
}

// Get<>AsString()
public string GetWttNumberOfUnitsAsString()
{
    return _WttNumberOfUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttNumberOfUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttNumberOfUnits = parsed;
}

// Standard Getter
public decimal GetWttProceeds()
{
    return _WttProceeds;
}

// Standard Setter
public void SetWttProceeds(decimal value)
{
    _WttProceeds = value;
}

// Get<>AsString()
public string GetWttProceedsAsString()
{
    return _WttProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttProceedsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttProceeds = parsed;
}

// Standard Getter
public decimal GetWttCapitalGainLoss()
{
    return _WttCapitalGainLoss;
}

// Standard Setter
public void SetWttCapitalGainLoss(decimal value)
{
    _WttCapitalGainLoss = value;
}

// Get<>AsString()
public string GetWttCapitalGainLossAsString()
{
    return _WttCapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttCapitalGainLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttCapitalGainLoss = parsed;
}

// Standard Getter
public string GetWttForce2PctMatchFlag()
{
    return _WttForce2PctMatchFlag;
}

// Standard Setter
public void SetWttForce2PctMatchFlag(string value)
{
    _WttForce2PctMatchFlag = value;
}

// Get<>AsString()
public string GetWttForce2PctMatchFlagAsString()
{
    return _WttForce2PctMatchFlag.PadRight(1);
}

// Set<>AsString()
public void SetWttForce2PctMatchFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttForce2PctMatchFlag = value;
}

// Standard Getter
public decimal GetWttCgtCost()
{
    return _WttCgtCost;
}

// Standard Setter
public void SetWttCgtCost(decimal value)
{
    _WttCgtCost = value;
}

// Get<>AsString()
public string GetWttCgtCostAsString()
{
    return _WttCgtCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttCgtCostAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttCgtCost = parsed;
}

// Standard Getter
public decimal GetWttProfitLoss()
{
    return _WttProfitLoss;
}

// Standard Setter
public void SetWttProfitLoss(decimal value)
{
    _WttProfitLoss = value;
}

// Get<>AsString()
public string GetWttProfitLossAsString()
{
    return _WttProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttProfitLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttProfitLoss = parsed;
}

// Standard Getter
public decimal GetWttIndexation()
{
    return _WttIndexation;
}

// Standard Setter
public void SetWttIndexation(decimal value)
{
    _WttIndexation = value;
}

// Get<>AsString()
public string GetWttIndexationAsString()
{
    return _WttIndexation.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttIndexationAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttIndexation = parsed;
}

// Standard Getter
public string GetWttBondDisposal()
{
    return _WttBondDisposal;
}

// Standard Setter
public void SetWttBondDisposal(string value)
{
    _WttBondDisposal = value;
}

// Get<>AsString()
public string GetWttBondDisposalAsString()
{
    return _WttBondDisposal.PadRight(1);
}

// Set<>AsString()
public void SetWttBondDisposalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttBondDisposal = value;
}

// Standard Getter
public string GetWtdConsolidatedFlag()
{
    return _WtdConsolidatedFlag;
}

// Standard Setter
public void SetWtdConsolidatedFlag(string value)
{
    _WtdConsolidatedFlag = value;
}

// Get<>AsString()
public string GetWtdConsolidatedFlagAsString()
{
    return _WtdConsolidatedFlag.PadRight(1);
}

// Set<>AsString()
public void SetWtdConsolidatedFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtdConsolidatedFlag = value;
}

// Standard Getter
public decimal GetWtdStoreUnits()
{
    return _WtdStoreUnits;
}

// Standard Setter
public void SetWtdStoreUnits(decimal value)
{
    _WtdStoreUnits = value;
}

// Get<>AsString()
public string GetWtdStoreUnitsAsString()
{
    return _WtdStoreUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtdStoreUnitsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtdStoreUnits = parsed;
}

// Standard Getter
public decimal GetWtdStoreProceeds()
{
    return _WtdStoreProceeds;
}

// Standard Setter
public void SetWtdStoreProceeds(decimal value)
{
    _WtdStoreProceeds = value;
}

// Get<>AsString()
public string GetWtdStoreProceedsAsString()
{
    return _WtdStoreProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtdStoreProceedsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtdStoreProceeds = parsed;
}

// Standard Getter
public string GetWtdConsolidateKey()
{
    return _WtdConsolidateKey;
}

// Standard Setter
public void SetWtdConsolidateKey(string value)
{
    _WtdConsolidateKey = value;
}

// Get<>AsString()
public string GetWtdConsolidateKeyAsString()
{
    return _WtdConsolidateKey.PadRight(23);
}

// Set<>AsString()
public void SetWtdConsolidateKeyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtdConsolidateKey = value;
}

// Standard Getter
public string GetWttFa03Exemption()
{
    return _WttFa03Exemption;
}

// Standard Setter
public void SetWttFa03Exemption(string value)
{
    _WttFa03Exemption = value;
}

// Get<>AsString()
public string GetWttFa03ExemptionAsString()
{
    return _WttFa03Exemption.PadRight(0);
}

// Set<>AsString()
public void SetWttFa03ExemptionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttFa03Exemption = value;
}

// Standard Getter
public string GetWttGtProRataMatch()
{
    return _WttGtProRataMatch;
}

// Standard Setter
public void SetWttGtProRataMatch(string value)
{
    _WttGtProRataMatch = value;
}

// Get<>AsString()
public string GetWttGtProRataMatchAsString()
{
    return _WttGtProRataMatch.PadRight(0);
}

// Set<>AsString()
public void SetWttGtProRataMatchAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttGtProRataMatch = value;
}

// Standard Getter
public string GetWttGtUseOriginalDates()
{
    return _WttGtUseOriginalDates;
}

// Standard Setter
public void SetWttGtUseOriginalDates(string value)
{
    _WttGtUseOriginalDates = value;
}

// Get<>AsString()
public string GetWttGtUseOriginalDatesAsString()
{
    return _WttGtUseOriginalDates.PadRight(0);
}

// Set<>AsString()
public void SetWttGtUseOriginalDatesAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttGtUseOriginalDates = value;
}

// Standard Getter
public decimal GetWttNumberOfUnitsYtd()
{
    return _WttNumberOfUnitsYtd;
}

// Standard Setter
public void SetWttNumberOfUnitsYtd(decimal value)
{
    _WttNumberOfUnitsYtd = value;
}

// Get<>AsString()
public string GetWttNumberOfUnitsYtdAsString()
{
    return _WttNumberOfUnitsYtd.ToString("F3", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttNumberOfUnitsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttNumberOfUnitsYtd = parsed;
}

// Standard Getter
public decimal GetWttProceedsYtd()
{
    return _WttProceedsYtd;
}

// Standard Setter
public void SetWttProceedsYtd(decimal value)
{
    _WttProceedsYtd = value;
}

// Get<>AsString()
public string GetWttProceedsYtdAsString()
{
    return _WttProceedsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWttProceedsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WttProceedsYtd = parsed;
}

// Standard Getter
public string GetFiller469()
{
    return _Filler469;
}

// Standard Setter
public void SetFiller469(string value)
{
    _Filler469 = value;
}

// Get<>AsString()
public string GetFiller469AsString()
{
    return _Filler469.PadRight(23);
}

// Set<>AsString()
public void SetFiller469AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler469 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: Filler470
public class Filler470
{
    private static int _size = 294;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttRecord1100, is_external=, is_static_class=False, static_prefix=
    private string _WttRecord1100 ="";
    
    
    
    
    // [DEBUG] Field: Filler471, is_external=, is_static_class=False, static_prefix=
    private string _Filler471 ="";
    
    
    
    
public Filler470() {}

public Filler470(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttRecord1100(data.Substring(offset, 100).Trim());
    offset += 100;
    SetFiller471(data.Substring(offset, 194).Trim());
    offset += 194;
    
}

// Serialization methods
public string GetFiller470AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttRecord1100.PadRight(100));
    result.Append(_Filler471.PadRight(194));
    
    return result.ToString();
}

public void SetFiller470AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 100 <= data.Length)
    {
        string extracted = data.Substring(offset, 100).Trim();
        SetWttRecord1100(extracted);
    }
    offset += 100;
    if (offset + 194 <= data.Length)
    {
        string extracted = data.Substring(offset, 194).Trim();
        SetFiller471(extracted);
    }
    offset += 194;
}

// Getter and Setter methods

// Standard Getter
public string GetWttRecord1100()
{
    return _WttRecord1100;
}

// Standard Setter
public void SetWttRecord1100(string value)
{
    _WttRecord1100 = value;
}

// Get<>AsString()
public string GetWttRecord1100AsString()
{
    return _WttRecord1100.PadRight(100);
}

// Set<>AsString()
public void SetWttRecord1100AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttRecord1100 = value;
}

// Standard Getter
public string GetFiller471()
{
    return _Filler471;
}

// Standard Setter
public void SetFiller471(string value)
{
    _Filler471 = value;
}

// Get<>AsString()
public string GetFiller471AsString()
{
    return _Filler471.PadRight(194);
}

// Set<>AsString()
public void SetFiller471AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler471 = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler472
public class Filler472
{
    private static int _size = 294;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WttRecord1294, is_external=, is_static_class=False, static_prefix=
    private string _WttRecord1294 ="";
    
    
    
    
public Filler472() {}

public Filler472(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWttRecord1294(data.Substring(offset, 294).Trim());
    offset += 294;
    
}

// Serialization methods
public string GetFiller472AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WttRecord1294.PadRight(294));
    
    return result.ToString();
}

public void SetFiller472AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 294 <= data.Length)
    {
        string extracted = data.Substring(offset, 294).Trim();
        SetWttRecord1294(extracted);
    }
    offset += 294;
}

// Getter and Setter methods

// Standard Getter
public string GetWttRecord1294()
{
    return _WttRecord1294;
}

// Standard Setter
public void SetWttRecord1294(string value)
{
    _WttRecord1294 = value;
}

// Get<>AsString()
public string GetWttRecord1294AsString()
{
    return _WttRecord1294.PadRight(294);
}

// Set<>AsString()
public void SetWttRecord1294AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WttRecord1294 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}

}}
