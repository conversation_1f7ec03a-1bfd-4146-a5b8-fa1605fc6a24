using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D77File Data Structure

public class D77File
{
    private static int _size = 12;
    // [DEBUG] Class: D77File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler241, is_external=, is_static_class=False, static_prefix=
    private string _Filler241 ="$";
    
    
    
    
    // [DEBUG] Field: D77UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D77UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler242, is_external=, is_static_class=False, static_prefix=
    private string _Filler242 ="RH";
    
    
    
    
    // [DEBUG] Field: D77ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D77ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler243, is_external=, is_static_class=False, static_prefix=
    private string _Filler243 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD77FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler241.PadRight(1));
        result.Append(_D77UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler242.PadRight(2));
        result.Append(_D77ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler243.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD77FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller241(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD77UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller242(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD77ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller243(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD77FileAsString();
    }
    // Set<>String Override function
    public void SetD77File(string value)
    {
        SetD77FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller241()
    {
        return _Filler241;
    }
    
    // Standard Setter
    public void SetFiller241(string value)
    {
        _Filler241 = value;
    }
    
    // Get<>AsString()
    public string GetFiller241AsString()
    {
        return _Filler241.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller241AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler241 = value;
    }
    
    // Standard Getter
    public int GetD77UserNo()
    {
        return _D77UserNo;
    }
    
    // Standard Setter
    public void SetD77UserNo(int value)
    {
        _D77UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD77UserNoAsString()
    {
        return _D77UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD77UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D77UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller242()
    {
        return _Filler242;
    }
    
    // Standard Setter
    public void SetFiller242(string value)
    {
        _Filler242 = value;
    }
    
    // Get<>AsString()
    public string GetFiller242AsString()
    {
        return _Filler242.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller242AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler242 = value;
    }
    
    // Standard Getter
    public int GetD77ReportNo()
    {
        return _D77ReportNo;
    }
    
    // Standard Setter
    public void SetD77ReportNo(int value)
    {
        _D77ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD77ReportNoAsString()
    {
        return _D77ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD77ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D77ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller243()
    {
        return _Filler243;
    }
    
    // Standard Setter
    public void SetFiller243(string value)
    {
        _Filler243 = value;
    }
    
    // Get<>AsString()
    public string GetFiller243AsString()
    {
        return _Filler243.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller243AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler243 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}