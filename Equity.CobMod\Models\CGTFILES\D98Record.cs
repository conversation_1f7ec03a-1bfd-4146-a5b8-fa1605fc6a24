using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D98Record Data Structure

public class D98Record
{
    private static int _size = 120;
    // [DEBUG] Class: D98Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D98KeyRecord, is_external=, is_static_class=False, static_prefix=
    private D98KeyRecord _D98KeyRecord = new D98KeyRecord();
    
    
    
    
    
    // Serialization methods
    public string GetD98RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D98KeyRecord.GetD98KeyRecordAsString());
        
        return result.ToString();
    }
    
    public void SetD98RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 120 <= data.Length)
        {
            _D98KeyRecord.SetD98KeyRecordAsString(data.Substring(offset, 120));
        }
        else
        {
            _D98KeyRecord.SetD98KeyRecordAsString(data.Substring(offset));
        }
        offset += 120;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD98RecordAsString();
    }
    // Set<>String Override function
    public void SetD98Record(string value)
    {
        SetD98RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D98KeyRecord GetD98KeyRecord()
    {
        return _D98KeyRecord;
    }
    
    // Standard Setter
    public void SetD98KeyRecord(D98KeyRecord value)
    {
        _D98KeyRecord = value;
    }
    
    // Get<>AsString()
    public string GetD98KeyRecordAsString()
    {
        return _D98KeyRecord != null ? _D98KeyRecord.GetD98KeyRecordAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD98KeyRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D98KeyRecord == null)
        {
            _D98KeyRecord = new D98KeyRecord();
        }
        _D98KeyRecord.SetD98KeyRecordAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD98KeyRecord(string value)
    {
        _D98KeyRecord.SetD98KeyRecordAsString(value);
    }
    // Nested Class: D98KeyRecord
    public class D98KeyRecord
    {
        private static int _size = 120;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D98SplitKey01, is_external=, is_static_class=False, static_prefix=
        private D98KeyRecord.D98SplitKey01 _D98SplitKey01 = new D98KeyRecord.D98SplitKey01();
        
        
        
        
        // [DEBUG] Field: Filler134, is_external=, is_static_class=False, static_prefix=
        private string _Filler134 ="";
        
        
        
        
        // [DEBUG] Field: D98SplitKey02, is_external=, is_static_class=False, static_prefix=
        private D98KeyRecord.D98SplitKey02 _D98SplitKey02 = new D98KeyRecord.D98SplitKey02();
        
        
        
        
        // [DEBUG] Field: D98SplitKey04, is_external=, is_static_class=False, static_prefix=
        private D98KeyRecord.D98SplitKey04 _D98SplitKey04 = new D98KeyRecord.D98SplitKey04();
        
        
        
        
        // [DEBUG] Field: D98SplitKey06, is_external=, is_static_class=False, static_prefix=
        private D98KeyRecord.D98SplitKey06 _D98SplitKey06 = new D98KeyRecord.D98SplitKey06();
        
        
        
        
        // [DEBUG] Field: Filler135, is_external=, is_static_class=False, static_prefix=
        private string _Filler135 ="";
        
        
        
        
        // [DEBUG] Field: D98SplitKey07, is_external=, is_static_class=False, static_prefix=
        private D98KeyRecord.D98SplitKey07 _D98SplitKey07 = new D98KeyRecord.D98SplitKey07();
        
        
        
        
        // [DEBUG] Field: D98SplitKey03, is_external=, is_static_class=False, static_prefix=
        private D98KeyRecord.D98SplitKey03 _D98SplitKey03 = new D98KeyRecord.D98SplitKey03();
        
        
        
        
        // [DEBUG] Field: Filler136, is_external=, is_static_class=False, static_prefix=
        private string _Filler136 ="";
        
        
        
        
        // [DEBUG] Field: D98TrancheFlag, is_external=, is_static_class=False, static_prefix=
        private string _D98TrancheFlag ="";
        
        
        
        
        // [DEBUG] Field: Filler137, is_external=, is_static_class=False, static_prefix=
        private string _Filler137 ="";
        
        
        
        
        // [DEBUG] Field: Filler138, is_external=, is_static_class=False, static_prefix=
        private string _Filler138 ="";
        
        
        
        
        // [DEBUG] Field: Filler139, is_external=, is_static_class=False, static_prefix=
        private string _Filler139 ="";
        
        
        
        
        // [DEBUG] Field: D98SplitKey05, is_external=, is_static_class=False, static_prefix=
        private D98KeyRecord.D98SplitKey05 _D98SplitKey05 = new D98KeyRecord.D98SplitKey05();
        
        
        
        
        // [DEBUG] Field: Filler140, is_external=, is_static_class=False, static_prefix=
        private string _Filler140 ="";
        
        
        
        
        // [DEBUG] Field: Filler141, is_external=, is_static_class=False, static_prefix=
        private string _Filler141 ="";
        
        
        
        
        // [DEBUG] Field: Filler142, is_external=, is_static_class=False, static_prefix=
        private string _Filler142 ="";
        
        
        
        
        // [DEBUG] Field: Filler143, is_external=, is_static_class=False, static_prefix=
        private string _Filler143 ="";
        
        
        
        
    public D98KeyRecord() {}
    
    public D98KeyRecord(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _D98SplitKey01.SetD98SplitKey01AsString(data.Substring(offset, D98SplitKey01.GetSize()));
        offset += 1;
        SetFiller134(data.Substring(offset, 0).Trim());
        offset += 0;
        _D98SplitKey02.SetD98SplitKey02AsString(data.Substring(offset, D98SplitKey02.GetSize()));
        offset += 0;
        _D98SplitKey04.SetD98SplitKey04AsString(data.Substring(offset, D98SplitKey04.GetSize()));
        offset += 0;
        _D98SplitKey06.SetD98SplitKey06AsString(data.Substring(offset, D98SplitKey06.GetSize()));
        offset += 0;
        SetFiller135(data.Substring(offset, 110).Trim());
        offset += 110;
        _D98SplitKey07.SetD98SplitKey07AsString(data.Substring(offset, D98SplitKey07.GetSize()));
        offset += 9;
        _D98SplitKey03.SetD98SplitKey03AsString(data.Substring(offset, D98SplitKey03.GetSize()));
        offset += 0;
        SetFiller136(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD98TrancheFlag(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller137(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller138(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller139(data.Substring(offset, 0).Trim());
        offset += 0;
        _D98SplitKey05.SetD98SplitKey05AsString(data.Substring(offset, D98SplitKey05.GetSize()));
        offset += 0;
        SetFiller140(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller141(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller142(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller143(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD98KeyRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D98SplitKey01.GetD98SplitKey01AsString());
        result.Append(_Filler134.PadRight(0));
        result.Append(_D98SplitKey02.GetD98SplitKey02AsString());
        result.Append(_D98SplitKey04.GetD98SplitKey04AsString());
        result.Append(_D98SplitKey06.GetD98SplitKey06AsString());
        result.Append(_Filler135.PadRight(110));
        result.Append(_D98SplitKey07.GetD98SplitKey07AsString());
        result.Append(_D98SplitKey03.GetD98SplitKey03AsString());
        result.Append(_Filler136.PadRight(0));
        result.Append(_D98TrancheFlag.PadRight(0));
        result.Append(_Filler137.PadRight(0));
        result.Append(_Filler138.PadRight(0));
        result.Append(_Filler139.PadRight(0));
        result.Append(_D98SplitKey05.GetD98SplitKey05AsString());
        result.Append(_Filler140.PadRight(0));
        result.Append(_Filler141.PadRight(0));
        result.Append(_Filler142.PadRight(0));
        result.Append(_Filler143.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD98KeyRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            _D98SplitKey01.SetD98SplitKey01AsString(data.Substring(offset, 1));
        }
        else
        {
            _D98SplitKey01.SetD98SplitKey01AsString(data.Substring(offset));
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller134(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D98SplitKey02.SetD98SplitKey02AsString(data.Substring(offset, 0));
        }
        else
        {
            _D98SplitKey02.SetD98SplitKey02AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D98SplitKey04.SetD98SplitKey04AsString(data.Substring(offset, 0));
        }
        else
        {
            _D98SplitKey04.SetD98SplitKey04AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D98SplitKey06.SetD98SplitKey06AsString(data.Substring(offset, 0));
        }
        else
        {
            _D98SplitKey06.SetD98SplitKey06AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 110 <= data.Length)
        {
            string extracted = data.Substring(offset, 110).Trim();
            SetFiller135(extracted);
        }
        offset += 110;
        if (offset + 9 <= data.Length)
        {
            _D98SplitKey07.SetD98SplitKey07AsString(data.Substring(offset, 9));
        }
        else
        {
            _D98SplitKey07.SetD98SplitKey07AsString(data.Substring(offset));
        }
        offset += 9;
        if (offset + 0 <= data.Length)
        {
            _D98SplitKey03.SetD98SplitKey03AsString(data.Substring(offset, 0));
        }
        else
        {
            _D98SplitKey03.SetD98SplitKey03AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller136(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD98TrancheFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller137(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller138(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller139(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D98SplitKey05.SetD98SplitKey05AsString(data.Substring(offset, 0));
        }
        else
        {
            _D98SplitKey05.SetD98SplitKey05AsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller140(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller141(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller142(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller143(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public D98SplitKey01 GetD98SplitKey01()
    {
        return _D98SplitKey01;
    }
    
    // Standard Setter
    public void SetD98SplitKey01(D98SplitKey01 value)
    {
        _D98SplitKey01 = value;
    }
    
    // Get<>AsString()
    public string GetD98SplitKey01AsString()
    {
        return _D98SplitKey01 != null ? _D98SplitKey01.GetD98SplitKey01AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD98SplitKey01AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D98SplitKey01 == null)
        {
            _D98SplitKey01 = new D98SplitKey01();
        }
        _D98SplitKey01.SetD98SplitKey01AsString(value);
    }
    
    // Standard Getter
    public string GetFiller134()
    {
        return _Filler134;
    }
    
    // Standard Setter
    public void SetFiller134(string value)
    {
        _Filler134 = value;
    }
    
    // Get<>AsString()
    public string GetFiller134AsString()
    {
        return _Filler134.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller134AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler134 = value;
    }
    
    // Standard Getter
    public D98SplitKey02 GetD98SplitKey02()
    {
        return _D98SplitKey02;
    }
    
    // Standard Setter
    public void SetD98SplitKey02(D98SplitKey02 value)
    {
        _D98SplitKey02 = value;
    }
    
    // Get<>AsString()
    public string GetD98SplitKey02AsString()
    {
        return _D98SplitKey02 != null ? _D98SplitKey02.GetD98SplitKey02AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD98SplitKey02AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D98SplitKey02 == null)
        {
            _D98SplitKey02 = new D98SplitKey02();
        }
        _D98SplitKey02.SetD98SplitKey02AsString(value);
    }
    
    // Standard Getter
    public D98SplitKey04 GetD98SplitKey04()
    {
        return _D98SplitKey04;
    }
    
    // Standard Setter
    public void SetD98SplitKey04(D98SplitKey04 value)
    {
        _D98SplitKey04 = value;
    }
    
    // Get<>AsString()
    public string GetD98SplitKey04AsString()
    {
        return _D98SplitKey04 != null ? _D98SplitKey04.GetD98SplitKey04AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD98SplitKey04AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D98SplitKey04 == null)
        {
            _D98SplitKey04 = new D98SplitKey04();
        }
        _D98SplitKey04.SetD98SplitKey04AsString(value);
    }
    
    // Standard Getter
    public D98SplitKey06 GetD98SplitKey06()
    {
        return _D98SplitKey06;
    }
    
    // Standard Setter
    public void SetD98SplitKey06(D98SplitKey06 value)
    {
        _D98SplitKey06 = value;
    }
    
    // Get<>AsString()
    public string GetD98SplitKey06AsString()
    {
        return _D98SplitKey06 != null ? _D98SplitKey06.GetD98SplitKey06AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD98SplitKey06AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D98SplitKey06 == null)
        {
            _D98SplitKey06 = new D98SplitKey06();
        }
        _D98SplitKey06.SetD98SplitKey06AsString(value);
    }
    
    // Standard Getter
    public string GetFiller135()
    {
        return _Filler135;
    }
    
    // Standard Setter
    public void SetFiller135(string value)
    {
        _Filler135 = value;
    }
    
    // Get<>AsString()
    public string GetFiller135AsString()
    {
        return _Filler135.PadRight(110);
    }
    
    // Set<>AsString()
    public void SetFiller135AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler135 = value;
    }
    
    // Standard Getter
    public D98SplitKey07 GetD98SplitKey07()
    {
        return _D98SplitKey07;
    }
    
    // Standard Setter
    public void SetD98SplitKey07(D98SplitKey07 value)
    {
        _D98SplitKey07 = value;
    }
    
    // Get<>AsString()
    public string GetD98SplitKey07AsString()
    {
        return _D98SplitKey07 != null ? _D98SplitKey07.GetD98SplitKey07AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD98SplitKey07AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D98SplitKey07 == null)
        {
            _D98SplitKey07 = new D98SplitKey07();
        }
        _D98SplitKey07.SetD98SplitKey07AsString(value);
    }
    
    // Standard Getter
    public D98SplitKey03 GetD98SplitKey03()
    {
        return _D98SplitKey03;
    }
    
    // Standard Setter
    public void SetD98SplitKey03(D98SplitKey03 value)
    {
        _D98SplitKey03 = value;
    }
    
    // Get<>AsString()
    public string GetD98SplitKey03AsString()
    {
        return _D98SplitKey03 != null ? _D98SplitKey03.GetD98SplitKey03AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD98SplitKey03AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D98SplitKey03 == null)
        {
            _D98SplitKey03 = new D98SplitKey03();
        }
        _D98SplitKey03.SetD98SplitKey03AsString(value);
    }
    
    // Standard Getter
    public string GetFiller136()
    {
        return _Filler136;
    }
    
    // Standard Setter
    public void SetFiller136(string value)
    {
        _Filler136 = value;
    }
    
    // Get<>AsString()
    public string GetFiller136AsString()
    {
        return _Filler136.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller136AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler136 = value;
    }
    
    // Standard Getter
    public string GetD98TrancheFlag()
    {
        return _D98TrancheFlag;
    }
    
    // Standard Setter
    public void SetD98TrancheFlag(string value)
    {
        _D98TrancheFlag = value;
    }
    
    // Get<>AsString()
    public string GetD98TrancheFlagAsString()
    {
        return _D98TrancheFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD98TrancheFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D98TrancheFlag = value;
    }
    
    // Standard Getter
    public string GetFiller137()
    {
        return _Filler137;
    }
    
    // Standard Setter
    public void SetFiller137(string value)
    {
        _Filler137 = value;
    }
    
    // Get<>AsString()
    public string GetFiller137AsString()
    {
        return _Filler137.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller137AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler137 = value;
    }
    
    // Standard Getter
    public string GetFiller138()
    {
        return _Filler138;
    }
    
    // Standard Setter
    public void SetFiller138(string value)
    {
        _Filler138 = value;
    }
    
    // Get<>AsString()
    public string GetFiller138AsString()
    {
        return _Filler138.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller138AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler138 = value;
    }
    
    // Standard Getter
    public string GetFiller139()
    {
        return _Filler139;
    }
    
    // Standard Setter
    public void SetFiller139(string value)
    {
        _Filler139 = value;
    }
    
    // Get<>AsString()
    public string GetFiller139AsString()
    {
        return _Filler139.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller139AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler139 = value;
    }
    
    // Standard Getter
    public D98SplitKey05 GetD98SplitKey05()
    {
        return _D98SplitKey05;
    }
    
    // Standard Setter
    public void SetD98SplitKey05(D98SplitKey05 value)
    {
        _D98SplitKey05 = value;
    }
    
    // Get<>AsString()
    public string GetD98SplitKey05AsString()
    {
        return _D98SplitKey05 != null ? _D98SplitKey05.GetD98SplitKey05AsString() : "";
    }
    
    // Set<>AsString()
    public void SetD98SplitKey05AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D98SplitKey05 == null)
        {
            _D98SplitKey05 = new D98SplitKey05();
        }
        _D98SplitKey05.SetD98SplitKey05AsString(value);
    }
    
    // Standard Getter
    public string GetFiller140()
    {
        return _Filler140;
    }
    
    // Standard Setter
    public void SetFiller140(string value)
    {
        _Filler140 = value;
    }
    
    // Get<>AsString()
    public string GetFiller140AsString()
    {
        return _Filler140.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller140AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler140 = value;
    }
    
    // Standard Getter
    public string GetFiller141()
    {
        return _Filler141;
    }
    
    // Standard Setter
    public void SetFiller141(string value)
    {
        _Filler141 = value;
    }
    
    // Get<>AsString()
    public string GetFiller141AsString()
    {
        return _Filler141.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller141AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler141 = value;
    }
    
    // Standard Getter
    public string GetFiller142()
    {
        return _Filler142;
    }
    
    // Standard Setter
    public void SetFiller142(string value)
    {
        _Filler142 = value;
    }
    
    // Get<>AsString()
    public string GetFiller142AsString()
    {
        return _Filler142.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller142AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler142 = value;
    }
    
    // Standard Getter
    public string GetFiller143()
    {
        return _Filler143;
    }
    
    // Standard Setter
    public void SetFiller143(string value)
    {
        _Filler143 = value;
    }
    
    // Get<>AsString()
    public string GetFiller143AsString()
    {
        return _Filler143.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller143AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler143 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D98SplitKey01
    public class D98SplitKey01
    {
        private static int _size = 1;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D98CurrencySort, is_external=, is_static_class=False, static_prefix=
        private int _D98CurrencySort =0;
        
        
        // 88-level condition checks for D98CurrencySort
        public bool IsD98CurrencySterling()
        {
            if (this._D98CurrencySort == 0) return true;
            return false;
        }
        public bool IsD98CurrencyEuro()
        {
            if (this._D98CurrencySort == 1) return true;
            return false;
        }
        
        
        // [DEBUG] Field: D98CoAcLk, is_external=, is_static_class=False, static_prefix=
        private string _D98CoAcLk ="";
        
        
        
        
        // [DEBUG] Field: D98CountryCode, is_external=, is_static_class=False, static_prefix=
        private string _D98CountryCode ="";
        
        
        
        
    public D98SplitKey01() {}
    
    public D98SplitKey01(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD98CurrencySort(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetD98CoAcLk(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD98CountryCode(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD98SplitKey01AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D98CurrencySort.ToString().PadLeft(1, '0'));
        result.Append(_D98CoAcLk.PadRight(0));
        result.Append(_D98CountryCode.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD98SplitKey01AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD98CurrencySort(parsedInt);
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD98CoAcLk(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD98CountryCode(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetD98CurrencySort()
    {
        return _D98CurrencySort;
    }
    
    // Standard Setter
    public void SetD98CurrencySort(int value)
    {
        _D98CurrencySort = value;
    }
    
    // Get<>AsString()
    public string GetD98CurrencySortAsString()
    {
        return _D98CurrencySort.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD98CurrencySortAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D98CurrencySort = parsed;
    }
    
    // Standard Getter
    public string GetD98CoAcLk()
    {
        return _D98CoAcLk;
    }
    
    // Standard Setter
    public void SetD98CoAcLk(string value)
    {
        _D98CoAcLk = value;
    }
    
    // Get<>AsString()
    public string GetD98CoAcLkAsString()
    {
        return _D98CoAcLk.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD98CoAcLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D98CoAcLk = value;
    }
    
    // Standard Getter
    public string GetD98CountryCode()
    {
        return _D98CountryCode;
    }
    
    // Standard Setter
    public void SetD98CountryCode(string value)
    {
        _D98CountryCode = value;
    }
    
    // Get<>AsString()
    public string GetD98CountryCodeAsString()
    {
        return _D98CountryCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD98CountryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D98CountryCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: D98SplitKey02
public class D98SplitKey02
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D98MainGroup23, is_external=, is_static_class=False, static_prefix=
    private string _D98MainGroup23 ="";
    
    
    
    
    // [DEBUG] Field: D98SecurityType, is_external=, is_static_class=False, static_prefix=
    private string _D98SecurityType ="";
    
    
    
    
    // [DEBUG] Field: D98SecuritySortCode, is_external=, is_static_class=False, static_prefix=
    private string _D98SecuritySortCode ="";
    
    
    
    
    // [DEBUG] Field: D98SedolSort, is_external=, is_static_class=False, static_prefix=
    private string _D98SedolSort ="";
    
    
    
    
    // [DEBUG] Field: D98RecordType, is_external=, is_static_class=False, static_prefix=
    private string _D98RecordType ="";
    
    
    
    
public D98SplitKey02() {}

public D98SplitKey02(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD98MainGroup23(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD98SecurityType(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD98SecuritySortCode(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD98SedolSort(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD98RecordType(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD98SplitKey02AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D98MainGroup23.PadRight(0));
    result.Append(_D98SecurityType.PadRight(0));
    result.Append(_D98SecuritySortCode.PadRight(0));
    result.Append(_D98SedolSort.PadRight(0));
    result.Append(_D98RecordType.PadRight(0));
    
    return result.ToString();
}

public void SetD98SplitKey02AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD98MainGroup23(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD98SecurityType(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD98SecuritySortCode(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD98SedolSort(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD98RecordType(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD98MainGroup23()
{
    return _D98MainGroup23;
}

// Standard Setter
public void SetD98MainGroup23(string value)
{
    _D98MainGroup23 = value;
}

// Get<>AsString()
public string GetD98MainGroup23AsString()
{
    return _D98MainGroup23.PadRight(0);
}

// Set<>AsString()
public void SetD98MainGroup23AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98MainGroup23 = value;
}

// Standard Getter
public string GetD98SecurityType()
{
    return _D98SecurityType;
}

// Standard Setter
public void SetD98SecurityType(string value)
{
    _D98SecurityType = value;
}

// Get<>AsString()
public string GetD98SecurityTypeAsString()
{
    return _D98SecurityType.PadRight(0);
}

// Set<>AsString()
public void SetD98SecurityTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98SecurityType = value;
}

// Standard Getter
public string GetD98SecuritySortCode()
{
    return _D98SecuritySortCode;
}

// Standard Setter
public void SetD98SecuritySortCode(string value)
{
    _D98SecuritySortCode = value;
}

// Get<>AsString()
public string GetD98SecuritySortCodeAsString()
{
    return _D98SecuritySortCode.PadRight(0);
}

// Set<>AsString()
public void SetD98SecuritySortCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98SecuritySortCode = value;
}

// Standard Getter
public string GetD98SedolSort()
{
    return _D98SedolSort;
}

// Standard Setter
public void SetD98SedolSort(string value)
{
    _D98SedolSort = value;
}

// Get<>AsString()
public string GetD98SedolSortAsString()
{
    return _D98SedolSort.PadRight(0);
}

// Set<>AsString()
public void SetD98SedolSortAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98SedolSort = value;
}

// Standard Getter
public string GetD98RecordType()
{
    return _D98RecordType;
}

// Standard Setter
public void SetD98RecordType(string value)
{
    _D98RecordType = value;
}

// Get<>AsString()
public string GetD98RecordTypeAsString()
{
    return _D98RecordType.PadRight(0);
}

// Set<>AsString()
public void SetD98RecordTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98RecordType = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D98SplitKey04
public class D98SplitKey04
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D98AcquisitionDate, is_external=, is_static_class=False, static_prefix=
    private D98SplitKey04.D98AcquisitionDate _D98AcquisitionDate = new D98SplitKey04.D98AcquisitionDate();
    
    
    
    
public D98SplitKey04() {}

public D98SplitKey04(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _D98AcquisitionDate.SetD98AcquisitionDateAsString(data.Substring(offset, D98AcquisitionDate.GetSize()));
    offset += 0;
    
}

// Serialization methods
public string GetD98SplitKey04AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D98AcquisitionDate.GetD98AcquisitionDateAsString());
    
    return result.ToString();
}

public void SetD98SplitKey04AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        _D98AcquisitionDate.SetD98AcquisitionDateAsString(data.Substring(offset, 0));
    }
    else
    {
        _D98AcquisitionDate.SetD98AcquisitionDateAsString(data.Substring(offset));
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public D98AcquisitionDate GetD98AcquisitionDate()
{
    return _D98AcquisitionDate;
}

// Standard Setter
public void SetD98AcquisitionDate(D98AcquisitionDate value)
{
    _D98AcquisitionDate = value;
}

// Get<>AsString()
public string GetD98AcquisitionDateAsString()
{
    return _D98AcquisitionDate != null ? _D98AcquisitionDate.GetD98AcquisitionDateAsString() : "";
}

// Set<>AsString()
public void SetD98AcquisitionDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D98AcquisitionDate == null)
    {
        _D98AcquisitionDate = new D98AcquisitionDate();
    }
    _D98AcquisitionDate.SetD98AcquisitionDateAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D98AcquisitionDate
public class D98AcquisitionDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D98AcquisitionDateYy, is_external=, is_static_class=False, static_prefix=
    private string _D98AcquisitionDateYy ="";
    
    
    
    
    // [DEBUG] Field: D98AcquisitionDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D98AcquisitionDateMm ="";
    
    
    
    
    // [DEBUG] Field: D98AcquisitionDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D98AcquisitionDateDd ="";
    
    
    
    
public D98AcquisitionDate() {}

public D98AcquisitionDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD98AcquisitionDateYy(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD98AcquisitionDateMm(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD98AcquisitionDateDd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD98AcquisitionDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D98AcquisitionDateYy.PadRight(0));
    result.Append(_D98AcquisitionDateMm.PadRight(0));
    result.Append(_D98AcquisitionDateDd.PadRight(0));
    
    return result.ToString();
}

public void SetD98AcquisitionDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD98AcquisitionDateYy(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD98AcquisitionDateMm(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD98AcquisitionDateDd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD98AcquisitionDateYy()
{
    return _D98AcquisitionDateYy;
}

// Standard Setter
public void SetD98AcquisitionDateYy(string value)
{
    _D98AcquisitionDateYy = value;
}

// Get<>AsString()
public string GetD98AcquisitionDateYyAsString()
{
    return _D98AcquisitionDateYy.PadRight(0);
}

// Set<>AsString()
public void SetD98AcquisitionDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98AcquisitionDateYy = value;
}

// Standard Getter
public string GetD98AcquisitionDateMm()
{
    return _D98AcquisitionDateMm;
}

// Standard Setter
public void SetD98AcquisitionDateMm(string value)
{
    _D98AcquisitionDateMm = value;
}

// Get<>AsString()
public string GetD98AcquisitionDateMmAsString()
{
    return _D98AcquisitionDateMm.PadRight(0);
}

// Set<>AsString()
public void SetD98AcquisitionDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98AcquisitionDateMm = value;
}

// Standard Getter
public string GetD98AcquisitionDateDd()
{
    return _D98AcquisitionDateDd;
}

// Standard Setter
public void SetD98AcquisitionDateDd(string value)
{
    _D98AcquisitionDateDd = value;
}

// Get<>AsString()
public string GetD98AcquisitionDateDdAsString()
{
    return _D98AcquisitionDateDd.PadRight(0);
}

// Set<>AsString()
public void SetD98AcquisitionDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98AcquisitionDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: D98SplitKey06
public class D98SplitKey06
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D98TrancheContractNumber, is_external=, is_static_class=False, static_prefix=
    private string _D98TrancheContractNumber ="";
    
    
    
    
    // [DEBUG] Field: D98LineNumber, is_external=, is_static_class=False, static_prefix=
    private string _D98LineNumber ="";
    
    
    
    
    // [DEBUG] Field: D98BdvIndicator, is_external=, is_static_class=False, static_prefix=
    private string _D98BdvIndicator ="";
    
    
    
    
public D98SplitKey06() {}

public D98SplitKey06(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD98TrancheContractNumber(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD98LineNumber(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD98BdvIndicator(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD98SplitKey06AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D98TrancheContractNumber.PadRight(0));
    result.Append(_D98LineNumber.PadRight(0));
    result.Append(_D98BdvIndicator.PadRight(0));
    
    return result.ToString();
}

public void SetD98SplitKey06AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD98TrancheContractNumber(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD98LineNumber(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD98BdvIndicator(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD98TrancheContractNumber()
{
    return _D98TrancheContractNumber;
}

// Standard Setter
public void SetD98TrancheContractNumber(string value)
{
    _D98TrancheContractNumber = value;
}

// Get<>AsString()
public string GetD98TrancheContractNumberAsString()
{
    return _D98TrancheContractNumber.PadRight(0);
}

// Set<>AsString()
public void SetD98TrancheContractNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98TrancheContractNumber = value;
}

// Standard Getter
public string GetD98LineNumber()
{
    return _D98LineNumber;
}

// Standard Setter
public void SetD98LineNumber(string value)
{
    _D98LineNumber = value;
}

// Get<>AsString()
public string GetD98LineNumberAsString()
{
    return _D98LineNumber.PadRight(0);
}

// Set<>AsString()
public void SetD98LineNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98LineNumber = value;
}

// Standard Getter
public string GetD98BdvIndicator()
{
    return _D98BdvIndicator;
}

// Standard Setter
public void SetD98BdvIndicator(string value)
{
    _D98BdvIndicator = value;
}

// Get<>AsString()
public string GetD98BdvIndicatorAsString()
{
    return _D98BdvIndicator.PadRight(0);
}

// Set<>AsString()
public void SetD98BdvIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98BdvIndicator = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D98SplitKey07
public class D98SplitKey07
{
    private static int _size = 9;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D98SequenceNumber, is_external=, is_static_class=False, static_prefix=
    private int _D98SequenceNumber =0;
    
    
    
    
public D98SplitKey07() {}

public D98SplitKey07(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD98SequenceNumber(int.Parse(data.Substring(offset, 9).Trim()));
    offset += 9;
    
}

// Serialization methods
public string GetD98SplitKey07AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D98SequenceNumber.ToString().PadLeft(9, '0'));
    
    return result.ToString();
}

public void SetD98SplitKey07AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetD98SequenceNumber(parsedInt);
    }
    offset += 9;
}

// Getter and Setter methods

// Standard Getter
public int GetD98SequenceNumber()
{
    return _D98SequenceNumber;
}

// Standard Setter
public void SetD98SequenceNumber(int value)
{
    _D98SequenceNumber = value;
}

// Get<>AsString()
public string GetD98SequenceNumberAsString()
{
    return _D98SequenceNumber.ToString().PadLeft(9, '0');
}

// Set<>AsString()
public void SetD98SequenceNumberAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _D98SequenceNumber = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D98SplitKey03
public class D98SplitKey03
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D98AcquisitionDateCc, is_external=, is_static_class=False, static_prefix=
    private string _D98AcquisitionDateCc ="";
    
    
    
    
public D98SplitKey03() {}

public D98SplitKey03(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD98AcquisitionDateCc(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD98SplitKey03AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D98AcquisitionDateCc.PadRight(0));
    
    return result.ToString();
}

public void SetD98SplitKey03AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD98AcquisitionDateCc(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD98AcquisitionDateCc()
{
    return _D98AcquisitionDateCc;
}

// Standard Setter
public void SetD98AcquisitionDateCc(string value)
{
    _D98AcquisitionDateCc = value;
}

// Get<>AsString()
public string GetD98AcquisitionDateCcAsString()
{
    return _D98AcquisitionDateCc.PadRight(0);
}

// Set<>AsString()
public void SetD98AcquisitionDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98AcquisitionDateCc = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: D98SplitKey05
public class D98SplitKey05
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D98TaperDateCc, is_external=, is_static_class=False, static_prefix=
    private string _D98TaperDateCc ="";
    
    
    
    
    // [DEBUG] Field: D98TaperDate, is_external=, is_static_class=False, static_prefix=
    private D98SplitKey05.D98TaperDate _D98TaperDate = new D98SplitKey05.D98TaperDate();
    
    
    
    
public D98SplitKey05() {}

public D98SplitKey05(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD98TaperDateCc(data.Substring(offset, 0).Trim());
    offset += 0;
    _D98TaperDate.SetD98TaperDateAsString(data.Substring(offset, D98TaperDate.GetSize()));
    offset += 0;
    
}

// Serialization methods
public string GetD98SplitKey05AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D98TaperDateCc.PadRight(0));
    result.Append(_D98TaperDate.GetD98TaperDateAsString());
    
    return result.ToString();
}

public void SetD98SplitKey05AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD98TaperDateCc(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        _D98TaperDate.SetD98TaperDateAsString(data.Substring(offset, 0));
    }
    else
    {
        _D98TaperDate.SetD98TaperDateAsString(data.Substring(offset));
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD98TaperDateCc()
{
    return _D98TaperDateCc;
}

// Standard Setter
public void SetD98TaperDateCc(string value)
{
    _D98TaperDateCc = value;
}

// Get<>AsString()
public string GetD98TaperDateCcAsString()
{
    return _D98TaperDateCc.PadRight(0);
}

// Set<>AsString()
public void SetD98TaperDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98TaperDateCc = value;
}

// Standard Getter
public D98TaperDate GetD98TaperDate()
{
    return _D98TaperDate;
}

// Standard Setter
public void SetD98TaperDate(D98TaperDate value)
{
    _D98TaperDate = value;
}

// Get<>AsString()
public string GetD98TaperDateAsString()
{
    return _D98TaperDate != null ? _D98TaperDate.GetD98TaperDateAsString() : "";
}

// Set<>AsString()
public void SetD98TaperDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_D98TaperDate == null)
    {
        _D98TaperDate = new D98TaperDate();
    }
    _D98TaperDate.SetD98TaperDateAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: D98TaperDate
public class D98TaperDate
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D98TaperDateYy, is_external=, is_static_class=False, static_prefix=
    private string _D98TaperDateYy ="";
    
    
    
    
    // [DEBUG] Field: D98TaperDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D98TaperDateMm ="";
    
    
    
    
    // [DEBUG] Field: D98TaperDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D98TaperDateDd ="";
    
    
    
    
public D98TaperDate() {}

public D98TaperDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD98TaperDateYy(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD98TaperDateMm(data.Substring(offset, 0).Trim());
    offset += 0;
    SetD98TaperDateDd(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetD98TaperDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D98TaperDateYy.PadRight(0));
    result.Append(_D98TaperDateMm.PadRight(0));
    result.Append(_D98TaperDateDd.PadRight(0));
    
    return result.ToString();
}

public void SetD98TaperDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD98TaperDateYy(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD98TaperDateMm(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetD98TaperDateDd(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetD98TaperDateYy()
{
    return _D98TaperDateYy;
}

// Standard Setter
public void SetD98TaperDateYy(string value)
{
    _D98TaperDateYy = value;
}

// Get<>AsString()
public string GetD98TaperDateYyAsString()
{
    return _D98TaperDateYy.PadRight(0);
}

// Set<>AsString()
public void SetD98TaperDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98TaperDateYy = value;
}

// Standard Getter
public string GetD98TaperDateMm()
{
    return _D98TaperDateMm;
}

// Standard Setter
public void SetD98TaperDateMm(string value)
{
    _D98TaperDateMm = value;
}

// Get<>AsString()
public string GetD98TaperDateMmAsString()
{
    return _D98TaperDateMm.PadRight(0);
}

// Set<>AsString()
public void SetD98TaperDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98TaperDateMm = value;
}

// Standard Getter
public string GetD98TaperDateDd()
{
    return _D98TaperDateDd;
}

// Standard Setter
public void SetD98TaperDateDd(string value)
{
    _D98TaperDateDd = value;
}

// Get<>AsString()
public string GetD98TaperDateDdAsString()
{
    return _D98TaperDateDd.PadRight(0);
}

// Set<>AsString()
public void SetD98TaperDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D98TaperDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}

}}