using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtNdlPurchasesTable Data Structure

public class WtNdlPurchasesTable
{
    private static int _size = 103;
    // [DEBUG] Class: WtNdlPurchasesTable, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler484, is_external=, is_static_class=False, static_prefix=
    private string _Filler484 ="NDL PURCHASES TABLE===============";
    
    
    
    
    // [DEBUG] Field: WtndlpMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtndlpMaxTableSize =1000;
    
    
    
    
    // [DEBUG] Field: WtndlpOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtndlpOccurs =0;
    
    
    
    
    // [DEBUG] Field: WtndlpFundRecordTable, is_external=, is_static_class=False, static_prefix=
    private WtndlpFundRecordTable _WtndlpFundRecordTable = new WtndlpFundRecordTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtNdlPurchasesTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler484.PadRight(42));
        result.Append(_WtndlpMaxTableSize.ToString().PadLeft(0, '0'));
        result.Append(_WtndlpOccurs.ToString().PadLeft(0, '0'));
        result.Append(_WtndlpFundRecordTable.GetWtndlpFundRecordTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtNdlPurchasesTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 42 <= data.Length)
        {
            string extracted = data.Substring(offset, 42).Trim();
            SetFiller484(extracted);
        }
        offset += 42;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtndlpMaxTableSize(parsedInt);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtndlpOccurs(parsedInt);
        }
        offset += 0;
        if (offset + 61 <= data.Length)
        {
            _WtndlpFundRecordTable.SetWtndlpFundRecordTableAsString(data.Substring(offset, 61));
        }
        else
        {
            _WtndlpFundRecordTable.SetWtndlpFundRecordTableAsString(data.Substring(offset));
        }
        offset += 61;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtNdlPurchasesTableAsString();
    }
    // Set<>String Override function
    public void SetWtNdlPurchasesTable(string value)
    {
        SetWtNdlPurchasesTableAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller484()
    {
        return _Filler484;
    }
    
    // Standard Setter
    public void SetFiller484(string value)
    {
        _Filler484 = value;
    }
    
    // Get<>AsString()
    public string GetFiller484AsString()
    {
        return _Filler484.PadRight(42);
    }
    
    // Set<>AsString()
    public void SetFiller484AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler484 = value;
    }
    
    // Standard Getter
    public int GetWtndlpMaxTableSize()
    {
        return _WtndlpMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtndlpMaxTableSize(int value)
    {
        _WtndlpMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtndlpMaxTableSizeAsString()
    {
        return _WtndlpMaxTableSize.ToString().PadLeft(0, '0');
    }
    
    // Set<>AsString()
    public void SetWtndlpMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtndlpMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtndlpOccurs()
    {
        return _WtndlpOccurs;
    }
    
    // Standard Setter
    public void SetWtndlpOccurs(int value)
    {
        _WtndlpOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtndlpOccursAsString()
    {
        return _WtndlpOccurs.ToString().PadLeft(0, '0');
    }
    
    // Set<>AsString()
    public void SetWtndlpOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtndlpOccurs = parsed;
    }
    
    // Standard Getter
    public WtndlpFundRecordTable GetWtndlpFundRecordTable()
    {
        return _WtndlpFundRecordTable;
    }
    
    // Standard Setter
    public void SetWtndlpFundRecordTable(WtndlpFundRecordTable value)
    {
        _WtndlpFundRecordTable = value;
    }
    
    // Get<>AsString()
    public string GetWtndlpFundRecordTableAsString()
    {
        return _WtndlpFundRecordTable != null ? _WtndlpFundRecordTable.GetWtndlpFundRecordTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtndlpFundRecordTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtndlpFundRecordTable == null)
        {
            _WtndlpFundRecordTable = new WtndlpFundRecordTable();
        }
        _WtndlpFundRecordTable.SetWtndlpFundRecordTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtndlpFundRecordTable(string value)
    {
        _WtndlpFundRecordTable.SetWtndlpFundRecordTableAsString(value);
    }
    // Nested Class: WtndlpFundRecordTable
    public class WtndlpFundRecordTable
    {
        private static int _size = 61;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtndlpElement, is_external=, is_static_class=False, static_prefix=
        private WtndlpFundRecordTable.WtndlpElement[] _WtndlpElement = new WtndlpFundRecordTable.WtndlpElement[1000];
        
        public void InitializeWtndlpElementArray()
        {
            for (int i = 0; i < 1000; i++)
            {
                _WtndlpElement[i] = new WtndlpFundRecordTable.WtndlpElement();
            }
        }
        
        
        
    public WtndlpFundRecordTable() {}
    
    public WtndlpFundRecordTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWtndlpElementArray();
        for (int i = 0; i < 1000; i++)
        {
            _WtndlpElement[i].SetWtndlpElementAsString(data.Substring(offset, 61));
            offset += 61;
        }
        
    }
    
    // Serialization methods
    public string GetWtndlpFundRecordTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 1000; i++)
        {
            result.Append(_WtndlpElement[i].GetWtndlpElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtndlpFundRecordTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 1000; i++)
        {
            if (offset + 61 > data.Length) break;
            string val = data.Substring(offset, 61);
            
            _WtndlpElement[i].SetWtndlpElementAsString(val);
            offset += 61;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtndlpElement
    public WtndlpElement GetWtndlpElementAt(int index)
    {
        return _WtndlpElement[index];
    }
    
    public void SetWtndlpElementAt(int index, WtndlpElement value)
    {
        _WtndlpElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtndlpElement GetWtndlpElement()
    {
        return _WtndlpElement != null && _WtndlpElement.Length > 0
        ? _WtndlpElement[0]
        : new WtndlpElement();
    }
    
    public void SetWtndlpElement(WtndlpElement value)
    {
        if (_WtndlpElement == null || _WtndlpElement.Length == 0)
        _WtndlpElement = new WtndlpElement[1];
        _WtndlpElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtndlpElement
    public class WtndlpElement
    {
        private static int _size = 61;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtndlpPurchaseKey, is_external=, is_static_class=False, static_prefix=
        private WtndlpElement.WtndlpPurchaseKey _WtndlpPurchaseKey = new WtndlpElement.WtndlpPurchaseKey();
        
        
        
        
        // [DEBUG] Field: WtndlpTotalPurchaseUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WtndlpTotalPurchaseUnits =0;
        
        
        
        
        // [DEBUG] Field: WtndlpTotalSaleUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WtndlpTotalSaleUnits =0;
        
        
        
        
        // [DEBUG] Field: WtndlpApportionmentRatio, is_external=, is_static_class=False, static_prefix=
        private decimal _WtndlpApportionmentRatio =0;
        
        
        
        
        // [DEBUG] Field: WtndlpSaleCount, is_external=, is_static_class=False, static_prefix=
        private int _WtndlpSaleCount =0;
        
        
        
        
    public WtndlpElement() {}
    
    public WtndlpElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WtndlpPurchaseKey.SetWtndlpPurchaseKeyAsString(data.Substring(offset, WtndlpPurchaseKey.GetSize()));
        offset += 10;
        SetWtndlpTotalPurchaseUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetWtndlpTotalSaleUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
        offset += 15;
        SetWtndlpApportionmentRatio(PackedDecimalConverter.ToDecimal(data.Substring(offset, 18)));
        offset += 18;
        SetWtndlpSaleCount(int.Parse(data.Substring(offset, 3).Trim()));
        offset += 3;
        
    }
    
    // Serialization methods
    public string GetWtndlpElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtndlpPurchaseKey.GetWtndlpPurchaseKeyAsString());
        result.Append(_WtndlpTotalPurchaseUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtndlpTotalSaleUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtndlpApportionmentRatio.ToString("F17", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtndlpSaleCount.ToString().PadLeft(3, '0'));
        
        return result.ToString();
    }
    
    public void SetWtndlpElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 10 <= data.Length)
        {
            _WtndlpPurchaseKey.SetWtndlpPurchaseKeyAsString(data.Substring(offset, 10));
        }
        else
        {
            _WtndlpPurchaseKey.SetWtndlpPurchaseKeyAsString(data.Substring(offset));
        }
        offset += 10;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtndlpTotalPurchaseUnits(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtndlpTotalSaleUnits(parsedDec);
        }
        offset += 15;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtndlpApportionmentRatio(parsedDec);
        }
        offset += 18;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtndlpSaleCount(parsedInt);
        }
        offset += 3;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WtndlpPurchaseKey GetWtndlpPurchaseKey()
    {
        return _WtndlpPurchaseKey;
    }
    
    // Standard Setter
    public void SetWtndlpPurchaseKey(WtndlpPurchaseKey value)
    {
        _WtndlpPurchaseKey = value;
    }
    
    // Get<>AsString()
    public string GetWtndlpPurchaseKeyAsString()
    {
        return _WtndlpPurchaseKey != null ? _WtndlpPurchaseKey.GetWtndlpPurchaseKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtndlpPurchaseKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtndlpPurchaseKey == null)
        {
            _WtndlpPurchaseKey = new WtndlpPurchaseKey();
        }
        _WtndlpPurchaseKey.SetWtndlpPurchaseKeyAsString(value);
    }
    
    // Standard Getter
    public decimal GetWtndlpTotalPurchaseUnits()
    {
        return _WtndlpTotalPurchaseUnits;
    }
    
    // Standard Setter
    public void SetWtndlpTotalPurchaseUnits(decimal value)
    {
        _WtndlpTotalPurchaseUnits = value;
    }
    
    // Get<>AsString()
    public string GetWtndlpTotalPurchaseUnitsAsString()
    {
        return _WtndlpTotalPurchaseUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtndlpTotalPurchaseUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtndlpTotalPurchaseUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetWtndlpTotalSaleUnits()
    {
        return _WtndlpTotalSaleUnits;
    }
    
    // Standard Setter
    public void SetWtndlpTotalSaleUnits(decimal value)
    {
        _WtndlpTotalSaleUnits = value;
    }
    
    // Get<>AsString()
    public string GetWtndlpTotalSaleUnitsAsString()
    {
        return _WtndlpTotalSaleUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtndlpTotalSaleUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtndlpTotalSaleUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetWtndlpApportionmentRatio()
    {
        return _WtndlpApportionmentRatio;
    }
    
    // Standard Setter
    public void SetWtndlpApportionmentRatio(decimal value)
    {
        _WtndlpApportionmentRatio = value;
    }
    
    // Get<>AsString()
    public string GetWtndlpApportionmentRatioAsString()
    {
        return _WtndlpApportionmentRatio.ToString("F17", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtndlpApportionmentRatioAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtndlpApportionmentRatio = parsed;
    }
    
    // Standard Getter
    public int GetWtndlpSaleCount()
    {
        return _WtndlpSaleCount;
    }
    
    // Standard Setter
    public void SetWtndlpSaleCount(int value)
    {
        _WtndlpSaleCount = value;
    }
    
    // Get<>AsString()
    public string GetWtndlpSaleCountAsString()
    {
        return _WtndlpSaleCount.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetWtndlpSaleCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtndlpSaleCount = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtndlpPurchaseKey
    public class WtndlpPurchaseKey
    {
        private static int _size = 10;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtndlpFundCode, is_external=, is_static_class=False, static_prefix=
        private string _WtndlpFundCode ="";
        
        
        
        
        // [DEBUG] Field: WtndlpSedolCode, is_external=, is_static_class=False, static_prefix=
        private string _WtndlpSedolCode ="";
        
        
        
        
        // [DEBUG] Field: WtndlpContractNo, is_external=, is_static_class=False, static_prefix=
        private string _WtndlpContractNo ="";
        
        
        
        
        // [DEBUG] Field: WtndlpRecordCode, is_external=, is_static_class=False, static_prefix=
        private string _WtndlpRecordCode ="";
        
        
        
        
    public WtndlpPurchaseKey() {}
    
    public WtndlpPurchaseKey(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtndlpFundCode(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWtndlpSedolCode(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWtndlpContractNo(data.Substring(offset, 10).Trim());
        offset += 10;
        SetWtndlpRecordCode(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetWtndlpPurchaseKeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtndlpFundCode.PadRight(0));
        result.Append(_WtndlpSedolCode.PadRight(0));
        result.Append(_WtndlpContractNo.PadRight(10));
        result.Append(_WtndlpRecordCode.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetWtndlpPurchaseKeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWtndlpFundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWtndlpSedolCode(extracted);
        }
        offset += 0;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetWtndlpContractNo(extracted);
        }
        offset += 10;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWtndlpRecordCode(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtndlpFundCode()
    {
        return _WtndlpFundCode;
    }
    
    // Standard Setter
    public void SetWtndlpFundCode(string value)
    {
        _WtndlpFundCode = value;
    }
    
    // Get<>AsString()
    public string GetWtndlpFundCodeAsString()
    {
        return _WtndlpFundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWtndlpFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtndlpFundCode = value;
    }
    
    // Standard Getter
    public string GetWtndlpSedolCode()
    {
        return _WtndlpSedolCode;
    }
    
    // Standard Setter
    public void SetWtndlpSedolCode(string value)
    {
        _WtndlpSedolCode = value;
    }
    
    // Get<>AsString()
    public string GetWtndlpSedolCodeAsString()
    {
        return _WtndlpSedolCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWtndlpSedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtndlpSedolCode = value;
    }
    
    // Standard Getter
    public string GetWtndlpContractNo()
    {
        return _WtndlpContractNo;
    }
    
    // Standard Setter
    public void SetWtndlpContractNo(string value)
    {
        _WtndlpContractNo = value;
    }
    
    // Get<>AsString()
    public string GetWtndlpContractNoAsString()
    {
        return _WtndlpContractNo.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetWtndlpContractNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtndlpContractNo = value;
    }
    
    // Standard Getter
    public string GetWtndlpRecordCode()
    {
        return _WtndlpRecordCode;
    }
    
    // Standard Setter
    public void SetWtndlpRecordCode(string value)
    {
        _WtndlpRecordCode = value;
    }
    
    // Get<>AsString()
    public string GetWtndlpRecordCodeAsString()
    {
        return _WtndlpRecordCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWtndlpRecordCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtndlpRecordCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}

}}