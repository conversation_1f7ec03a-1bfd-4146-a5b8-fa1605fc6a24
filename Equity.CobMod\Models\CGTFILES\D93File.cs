using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D93File Data Structure

public class D93File
{
    private static int _size = 128;
    // [DEBUG] Class: D93File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler272, is_external=, is_static_class=False, static_prefix=
    private string _Filler272 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD93FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler272.PadRight(128));
        
        return result.ToString();
    }
    
    public void SetD93FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 128 <= data.Length)
        {
            string extracted = data.Substring(offset, 128).Trim();
            SetFiller272(extracted);
        }
        offset += 128;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD93FileAsString();
    }
    // Set<>String Override function
    public void SetD93File(string value)
    {
        SetD93FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller272()
    {
        return _Filler272;
    }
    
    // Standard Setter
    public void SetFiller272(string value)
    {
        _Filler272 = value;
    }
    
    // Get<>AsString()
    public string GetFiller272AsString()
    {
        return _Filler272.PadRight(128);
    }
    
    // Set<>AsString()
    public void SetFiller272AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler272 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}