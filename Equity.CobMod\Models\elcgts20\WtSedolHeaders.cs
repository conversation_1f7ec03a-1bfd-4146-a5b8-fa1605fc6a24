using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtSedolHeaders Data Structure

public class WtSedolHeaders
{
    private static int _size = 721;
    // [DEBUG] Class: WtSedolHeaders, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler452, is_external=, is_static_class=False, static_prefix=
    private string _Filler452 ="SEDOL-HEADERS===";
    
    
    
    
    // [DEBUG] Field: WtsMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtsMaxTableSize =2000;
    
    
    
    
    // [DEBUG] Field: WtsOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtsOccurs =2000;
    
    
    
    
    // [DEBUG] Field: WtsSedolHeaderTable, is_external=, is_static_class=False, static_prefix=
    private WtsSedolHeaderTable _WtsSedolHeaderTable = new WtsSedolHeaderTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtSedolHeadersAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler452.PadRight(16));
        result.Append(_WtsMaxTableSize.ToString().PadLeft(4, '0'));
        result.Append(_WtsOccurs.ToString().PadLeft(4, '0'));
        result.Append(_WtsSedolHeaderTable.GetWtsSedolHeaderTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtSedolHeadersAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller452(extracted);
        }
        offset += 16;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtsMaxTableSize(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtsOccurs(parsedInt);
        }
        offset += 4;
        if (offset + 697 <= data.Length)
        {
            _WtsSedolHeaderTable.SetWtsSedolHeaderTableAsString(data.Substring(offset, 697));
        }
        else
        {
            _WtsSedolHeaderTable.SetWtsSedolHeaderTableAsString(data.Substring(offset));
        }
        offset += 697;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtSedolHeadersAsString();
    }
    // Set<>String Override function
    public void SetWtSedolHeaders(string value)
    {
        SetWtSedolHeadersAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller452()
    {
        return _Filler452;
    }
    
    // Standard Setter
    public void SetFiller452(string value)
    {
        _Filler452 = value;
    }
    
    // Get<>AsString()
    public string GetFiller452AsString()
    {
        return _Filler452.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller452AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler452 = value;
    }
    
    // Standard Getter
    public int GetWtsMaxTableSize()
    {
        return _WtsMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtsMaxTableSize(int value)
    {
        _WtsMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtsMaxTableSizeAsString()
    {
        return _WtsMaxTableSize.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtsMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtsMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtsOccurs()
    {
        return _WtsOccurs;
    }
    
    // Standard Setter
    public void SetWtsOccurs(int value)
    {
        _WtsOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtsOccursAsString()
    {
        return _WtsOccurs.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtsOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtsOccurs = parsed;
    }
    
    // Standard Getter
    public WtsSedolHeaderTable GetWtsSedolHeaderTable()
    {
        return _WtsSedolHeaderTable;
    }
    
    // Standard Setter
    public void SetWtsSedolHeaderTable(WtsSedolHeaderTable value)
    {
        _WtsSedolHeaderTable = value;
    }
    
    // Get<>AsString()
    public string GetWtsSedolHeaderTableAsString()
    {
        return _WtsSedolHeaderTable != null ? _WtsSedolHeaderTable.GetWtsSedolHeaderTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtsSedolHeaderTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtsSedolHeaderTable == null)
        {
            _WtsSedolHeaderTable = new WtsSedolHeaderTable();
        }
        _WtsSedolHeaderTable.SetWtsSedolHeaderTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtsSedolHeaderTable(string value)
    {
        _WtsSedolHeaderTable.SetWtsSedolHeaderTableAsString(value);
    }
    // Nested Class: WtsSedolHeaderTable
    public class WtsSedolHeaderTable
    {
        private static int _size = 697;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtsElement, is_external=, is_static_class=False, static_prefix=
        private WtsSedolHeaderTable.WtsElement[] _WtsElement = new WtsSedolHeaderTable.WtsElement[2000];
        
        public void InitializeWtsElementArray()
        {
            for (int i = 0; i < 2000; i++)
            {
                _WtsElement[i] = new WtsSedolHeaderTable.WtsElement();
            }
        }
        
        
        
    public WtsSedolHeaderTable() {}
    
    public WtsSedolHeaderTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWtsElementArray();
        for (int i = 0; i < 2000; i++)
        {
            _WtsElement[i].SetWtsElementAsString(data.Substring(offset, 697));
            offset += 697;
        }
        
    }
    
    // Serialization methods
    public string GetWtsSedolHeaderTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 2000; i++)
        {
            result.Append(_WtsElement[i].GetWtsElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtsSedolHeaderTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 2000; i++)
        {
            if (offset + 697 > data.Length) break;
            string val = data.Substring(offset, 697);
            
            _WtsElement[i].SetWtsElementAsString(val);
            offset += 697;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtsElement
    public WtsElement GetWtsElementAt(int index)
    {
        return _WtsElement[index];
    }
    
    public void SetWtsElementAt(int index, WtsElement value)
    {
        _WtsElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtsElement GetWtsElement()
    {
        return _WtsElement != null && _WtsElement.Length > 0
        ? _WtsElement[0]
        : new WtsElement();
    }
    
    public void SetWtsElement(WtsElement value)
    {
        if (_WtsElement == null || _WtsElement.Length == 0)
        _WtsElement = new WtsElement[1];
        _WtsElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtsElement
    public class WtsElement
    {
        private static int _size = 697;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtsPrefix, is_external=, is_static_class=False, static_prefix=
        private WtsElement.WtsPrefix _WtsPrefix = new WtsElement.WtsPrefix();
        
        
        
        
        // [DEBUG] Field: WtsRecord, is_external=, is_static_class=False, static_prefix=
        private WtsElement.WtsRecord _WtsRecord = new WtsElement.WtsRecord();
        
        
        
        
    public WtsElement() {}
    
    public WtsElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WtsPrefix.SetWtsPrefixAsString(data.Substring(offset, WtsPrefix.GetSize()));
        offset += 209;
        _WtsRecord.SetWtsRecordAsString(data.Substring(offset, WtsRecord.GetSize()));
        offset += 488;
        
    }
    
    // Serialization methods
    public string GetWtsElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtsPrefix.GetWtsPrefixAsString());
        result.Append(_WtsRecord.GetWtsRecordAsString());
        
        return result.ToString();
    }
    
    public void SetWtsElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 209 <= data.Length)
        {
            _WtsPrefix.SetWtsPrefixAsString(data.Substring(offset, 209));
        }
        else
        {
            _WtsPrefix.SetWtsPrefixAsString(data.Substring(offset));
        }
        offset += 209;
        if (offset + 488 <= data.Length)
        {
            _WtsRecord.SetWtsRecordAsString(data.Substring(offset, 488));
        }
        else
        {
            _WtsRecord.SetWtsRecordAsString(data.Substring(offset));
        }
        offset += 488;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WtsPrefix GetWtsPrefix()
    {
        return _WtsPrefix;
    }
    
    // Standard Setter
    public void SetWtsPrefix(WtsPrefix value)
    {
        _WtsPrefix = value;
    }
    
    // Get<>AsString()
    public string GetWtsPrefixAsString()
    {
        return _WtsPrefix != null ? _WtsPrefix.GetWtsPrefixAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtsPrefixAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtsPrefix == null)
        {
            _WtsPrefix = new WtsPrefix();
        }
        _WtsPrefix.SetWtsPrefixAsString(value);
    }
    
    // Standard Getter
    public WtsRecord GetWtsRecord()
    {
        return _WtsRecord;
    }
    
    // Standard Setter
    public void SetWtsRecord(WtsRecord value)
    {
        _WtsRecord = value;
    }
    
    // Get<>AsString()
    public string GetWtsRecordAsString()
    {
        return _WtsRecord != null ? _WtsRecord.GetWtsRecordAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtsRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtsRecord == null)
        {
            _WtsRecord = new WtsRecord();
        }
        _WtsRecord.SetWtsRecordAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtsPrefix
    public class WtsPrefix
    {
        private static int _size = 209;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtsError, is_external=, is_static_class=False, static_prefix=
        private int _WtsError =0;
        
        
        
        
        // [DEBUG] Field: WtsPpPointer, is_external=, is_static_class=False, static_prefix=
        private int _WtsPpPointer =0;
        
        
        
        
        // [DEBUG] Field: WtsPpPointer2, is_external=, is_static_class=False, static_prefix=
        private int _WtsPpPointer2 =0;
        
        
        
        
        // [DEBUG] Field: WtsCfPointer, is_external=, is_static_class=False, static_prefix=
        private int _WtsCfPointer =0;
        
        
        
        
        // [DEBUG] Field: WtsBfTotalUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WtsBfTotalUnits =0;
        
        
        
        
        // [DEBUG] Field: WtsPrintFlag, is_external=, is_static_class=False, static_prefix=
        private string _WtsPrintFlag ="";
        
        
        
        
        // [DEBUG] Field: WtsLastTrancheContractNo, is_external=, is_static_class=False, static_prefix=
        private string _WtsLastTrancheContractNo ="";
        
        
        
        
        // [DEBUG] Field: WtsSedolRecordCount, is_external=, is_static_class=False, static_prefix=
        private int _WtsSedolRecordCount =0;
        
        
        
        
        // [DEBUG] Field: WtsTrancheCount, is_external=, is_static_class=False, static_prefix=
        private int _WtsTrancheCount =0;
        
        
        
        
        // [DEBUG] Field: WtsParentRef, is_external=, is_static_class=False, static_prefix=
        private string _WtsParentRef ="";
        
        
        
        
        // [DEBUG] Field: WtsUnmatchedProceeds, is_external=, is_static_class=False, static_prefix=
        private decimal _WtsUnmatchedProceeds =0;
        
        
        
        
        // [DEBUG] Field: WtsUnmatchedCost, is_external=, is_static_class=False, static_prefix=
        private decimal _WtsUnmatchedCost =0;
        
        
        
        
        // [DEBUG] Field: WtsUnmatchedUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WtsUnmatchedUnits =0;
        
        
        
        
        // [DEBUG] Field: WtsIndex85Flag, is_external=, is_static_class=False, static_prefix=
        private string _WtsIndex85Flag ="";
        
        
        // 88-level condition checks for WtsIndex85Flag
        public bool IsWtsIndex85()
        {
            if (this._WtsIndex85Flag == "'Y'") return true;
            if (this._WtsIndex85Flag == "'S'") return true;
            if (this._WtsIndex85Flag == "'R'") return true;
            return false;
        }
        public bool IsWtsOnly85()
        {
            if (this._WtsIndex85Flag == "'Y'") return true;
            if (this._WtsIndex85Flag == "'R'") return true;
            return false;
        }
        public bool IsWtsSpecial85()
        {
            if (this._WtsIndex85Flag == "'S'") return true;
            return false;
        }
        public bool IsWtsRelevant85()
        {
            if (this._WtsIndex85Flag == "'R'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WtsPoolRpt, is_external=, is_static_class=False, static_prefix=
        private string _WtsPoolRpt ="";
        
        
        
        
        // [DEBUG] Field: WtsDsFound, is_external=, is_static_class=False, static_prefix=
        private string _WtsDsFound ="";
        
        
        
        
        // [DEBUG] Field: Wts2PctFlag, is_external=, is_static_class=False, static_prefix=
        private string _Wts2PctFlag ="";
        
        
        
        
        // [DEBUG] Field: Wts30DayFlag, is_external=, is_static_class=False, static_prefix=
        private int _Wts30DayFlag =0;
        
        
        // 88-level condition checks for Wts30DayFlag
        public bool IsNo30DayMatch()
        {
            if (this._Wts30DayFlag == 0) return true;
            return false;
        }
        public bool Is30DayMatchTest()
        {
            if (this._Wts30DayFlag == 1) return true;
            return false;
        }
        public bool Is30DayMatchInPeriod()
        {
            if (this._Wts30DayFlag == 2) return true;
            return false;
        }
        public bool Is30DayMatchPostPeriod()
        {
            if (this._Wts30DayFlag == 3) return true;
            return false;
        }
        
        
        // [DEBUG] Field: Wts30DayUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _Wts30DayUnits =0;
        
        
        
        
        // [DEBUG] Field: WtsFa03ForwardMatchedUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WtsFa03ForwardMatchedUnits =0;
        
        
        
        
        // [DEBUG] Field: WtsPriceTypeStatus, is_external=, is_static_class=False, static_prefix=
        private string _WtsPriceTypeStatus ="";
        
        
        // 88-level condition checks for WtsPriceTypeStatus
        public bool IsWtsSameDayPriceFound()
        {
            if (this._WtsPriceTypeStatus == "'0'") return true;
            return false;
        }
        public bool IsWtsPrevDayPriceFound()
        {
            if (this._WtsPriceTypeStatus == "'1'") return true;
            return false;
        }
        public bool IsWtsPrevSessionPriceFound()
        {
            if (this._WtsPriceTypeStatus == "'2'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WtsReitFlag, is_external=, is_static_class=False, static_prefix=
        private string _WtsReitFlag ="";
        
        
        // 88-level condition checks for WtsReitFlag
        public bool IsWtsReitSecurity()
        {
            if (this._WtsReitFlag == "'Y'") return true;
            return false;
        }
        public bool IsWtsNonReitSecurity()
        {
            if (this._WtsReitFlag == "'N'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: WtsIds, is_external=, is_static_class=False, static_prefix=
        private WtsPrefix.WtsIds _WtsIds = new WtsPrefix.WtsIds();
        
        
        
        
        // [DEBUG] Field: WtsFundType, is_external=, is_static_class=False, static_prefix=
        private string _WtsFundType ="";
        
        
        // 88-level condition checks for WtsFundType
        public bool IsWtsIrishLifeFund()
        {
            if (this._WtsFundType == "'X'") return true;
            return false;
        }
        
        
    public WtsPrefix() {}
    
    public WtsPrefix(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtsError(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetWtsPpPointer(int.Parse(data.Substring(offset, 5).Trim()));
        offset += 5;
        SetWtsPpPointer2(int.Parse(data.Substring(offset, 5).Trim()));
        offset += 5;
        SetWtsCfPointer(int.Parse(data.Substring(offset, 3).Trim()));
        offset += 3;
        SetWtsBfTotalUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetWtsPrintFlag(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWtsLastTrancheContractNo(data.Substring(offset, 10).Trim());
        offset += 10;
        SetWtsSedolRecordCount(int.Parse(data.Substring(offset, 4).Trim()));
        offset += 4;
        SetWtsTrancheCount(int.Parse(data.Substring(offset, 4).Trim()));
        offset += 4;
        SetWtsParentRef(data.Substring(offset, 11).Trim());
        offset += 11;
        SetWtsUnmatchedProceeds(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
        offset += 17;
        SetWtsUnmatchedCost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
        offset += 17;
        SetWtsUnmatchedUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetWtsIndex85Flag(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWtsPoolRpt(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWtsDsFound(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWts2PctFlag(data.Substring(offset, 1).Trim());
        offset += 1;
        SetWts30DayFlag(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetWts30DayUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 9)));
        offset += 9;
        SetWtsFa03ForwardMatchedUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 9)));
        offset += 9;
        SetWtsPriceTypeStatus(data.Substring(offset, 0).Trim());
        offset += 0;
        SetWtsReitFlag(data.Substring(offset, 0).Trim());
        offset += 0;
        _WtsIds.SetWtsIdsAsString(data.Substring(offset, WtsIds.GetSize()));
        offset += 81;
        SetWtsFundType(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetWtsPrefixAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtsError.ToString().PadLeft(2, '0'));
        result.Append(_WtsPpPointer.ToString().PadLeft(5, '0'));
        result.Append(_WtsPpPointer2.ToString().PadLeft(5, '0'));
        result.Append(_WtsCfPointer.ToString().PadLeft(3, '0'));
        result.Append(_WtsBfTotalUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsPrintFlag.PadRight(1));
        result.Append(_WtsLastTrancheContractNo.PadRight(10));
        result.Append(_WtsSedolRecordCount.ToString().PadLeft(4, '0'));
        result.Append(_WtsTrancheCount.ToString().PadLeft(4, '0'));
        result.Append(_WtsParentRef.PadRight(11));
        result.Append(_WtsUnmatchedProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsUnmatchedCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsUnmatchedUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsIndex85Flag.PadRight(1));
        result.Append(_WtsPoolRpt.PadRight(1));
        result.Append(_WtsDsFound.PadRight(1));
        result.Append(_Wts2PctFlag.PadRight(1));
        result.Append(_Wts30DayFlag.ToString().PadLeft(1, '0'));
        result.Append(_Wts30DayUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsFa03ForwardMatchedUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtsPriceTypeStatus.PadRight(0));
        result.Append(_WtsReitFlag.PadRight(0));
        result.Append(_WtsIds.GetWtsIdsAsString());
        result.Append(_WtsFundType.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetWtsPrefixAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtsError(parsedInt);
        }
        offset += 2;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtsPpPointer(parsedInt);
        }
        offset += 5;
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtsPpPointer2(parsedInt);
        }
        offset += 5;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtsCfPointer(parsedInt);
        }
        offset += 3;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsBfTotalUnits(parsedDec);
        }
        offset += 13;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtsPrintFlag(extracted);
        }
        offset += 1;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetWtsLastTrancheContractNo(extracted);
        }
        offset += 10;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtsSedolRecordCount(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtsTrancheCount(parsedInt);
        }
        offset += 4;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            SetWtsParentRef(extracted);
        }
        offset += 11;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsUnmatchedProceeds(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsUnmatchedCost(parsedDec);
        }
        offset += 17;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsUnmatchedUnits(parsedDec);
        }
        offset += 13;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtsIndex85Flag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtsPoolRpt(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWtsDsFound(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWts2PctFlag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWts30DayFlag(parsedInt);
        }
        offset += 1;
        if (offset + 9 <= data.Length)
        {
            string extracted = data.Substring(offset, 9).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWts30DayUnits(parsedDec);
        }
        offset += 9;
        if (offset + 9 <= data.Length)
        {
            string extracted = data.Substring(offset, 9).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtsFa03ForwardMatchedUnits(parsedDec);
        }
        offset += 9;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWtsPriceTypeStatus(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWtsReitFlag(extracted);
        }
        offset += 0;
        if (offset + 81 <= data.Length)
        {
            _WtsIds.SetWtsIdsAsString(data.Substring(offset, 81));
        }
        else
        {
            _WtsIds.SetWtsIdsAsString(data.Substring(offset));
        }
        offset += 81;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWtsFundType(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWtsError()
    {
        return _WtsError;
    }
    
    // Standard Setter
    public void SetWtsError(int value)
    {
        _WtsError = value;
    }
    
    // Get<>AsString()
    public string GetWtsErrorAsString()
    {
        return _WtsError.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWtsErrorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtsError = parsed;
    }
    
    // Standard Getter
    public int GetWtsPpPointer()
    {
        return _WtsPpPointer;
    }
    
    // Standard Setter
    public void SetWtsPpPointer(int value)
    {
        _WtsPpPointer = value;
    }
    
    // Get<>AsString()
    public string GetWtsPpPointerAsString()
    {
        return _WtsPpPointer.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWtsPpPointerAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtsPpPointer = parsed;
    }
    
    // Standard Getter
    public int GetWtsPpPointer2()
    {
        return _WtsPpPointer2;
    }
    
    // Standard Setter
    public void SetWtsPpPointer2(int value)
    {
        _WtsPpPointer2 = value;
    }
    
    // Get<>AsString()
    public string GetWtsPpPointer2AsString()
    {
        return _WtsPpPointer2.ToString().PadLeft(5, '0');
    }
    
    // Set<>AsString()
    public void SetWtsPpPointer2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtsPpPointer2 = parsed;
    }
    
    // Standard Getter
    public int GetWtsCfPointer()
    {
        return _WtsCfPointer;
    }
    
    // Standard Setter
    public void SetWtsCfPointer(int value)
    {
        _WtsCfPointer = value;
    }
    
    // Get<>AsString()
    public string GetWtsCfPointerAsString()
    {
        return _WtsCfPointer.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetWtsCfPointerAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtsCfPointer = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsBfTotalUnits()
    {
        return _WtsBfTotalUnits;
    }
    
    // Standard Setter
    public void SetWtsBfTotalUnits(decimal value)
    {
        _WtsBfTotalUnits = value;
    }
    
    // Get<>AsString()
    public string GetWtsBfTotalUnitsAsString()
    {
        return _WtsBfTotalUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsBfTotalUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsBfTotalUnits = parsed;
    }
    
    // Standard Getter
    public string GetWtsPrintFlag()
    {
        return _WtsPrintFlag;
    }
    
    // Standard Setter
    public void SetWtsPrintFlag(string value)
    {
        _WtsPrintFlag = value;
    }
    
    // Get<>AsString()
    public string GetWtsPrintFlagAsString()
    {
        return _WtsPrintFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtsPrintFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtsPrintFlag = value;
    }
    
    // Standard Getter
    public string GetWtsLastTrancheContractNo()
    {
        return _WtsLastTrancheContractNo;
    }
    
    // Standard Setter
    public void SetWtsLastTrancheContractNo(string value)
    {
        _WtsLastTrancheContractNo = value;
    }
    
    // Get<>AsString()
    public string GetWtsLastTrancheContractNoAsString()
    {
        return _WtsLastTrancheContractNo.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetWtsLastTrancheContractNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtsLastTrancheContractNo = value;
    }
    
    // Standard Getter
    public int GetWtsSedolRecordCount()
    {
        return _WtsSedolRecordCount;
    }
    
    // Standard Setter
    public void SetWtsSedolRecordCount(int value)
    {
        _WtsSedolRecordCount = value;
    }
    
    // Get<>AsString()
    public string GetWtsSedolRecordCountAsString()
    {
        return _WtsSedolRecordCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtsSedolRecordCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtsSedolRecordCount = parsed;
    }
    
    // Standard Getter
    public int GetWtsTrancheCount()
    {
        return _WtsTrancheCount;
    }
    
    // Standard Setter
    public void SetWtsTrancheCount(int value)
    {
        _WtsTrancheCount = value;
    }
    
    // Get<>AsString()
    public string GetWtsTrancheCountAsString()
    {
        return _WtsTrancheCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtsTrancheCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtsTrancheCount = parsed;
    }
    
    // Standard Getter
    public string GetWtsParentRef()
    {
        return _WtsParentRef;
    }
    
    // Standard Setter
    public void SetWtsParentRef(string value)
    {
        _WtsParentRef = value;
    }
    
    // Get<>AsString()
    public string GetWtsParentRefAsString()
    {
        return _WtsParentRef.PadRight(11);
    }
    
    // Set<>AsString()
    public void SetWtsParentRefAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtsParentRef = value;
    }
    
    // Standard Getter
    public decimal GetWtsUnmatchedProceeds()
    {
        return _WtsUnmatchedProceeds;
    }
    
    // Standard Setter
    public void SetWtsUnmatchedProceeds(decimal value)
    {
        _WtsUnmatchedProceeds = value;
    }
    
    // Get<>AsString()
    public string GetWtsUnmatchedProceedsAsString()
    {
        return _WtsUnmatchedProceeds.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsUnmatchedProceedsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsUnmatchedProceeds = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsUnmatchedCost()
    {
        return _WtsUnmatchedCost;
    }
    
    // Standard Setter
    public void SetWtsUnmatchedCost(decimal value)
    {
        _WtsUnmatchedCost = value;
    }
    
    // Get<>AsString()
    public string GetWtsUnmatchedCostAsString()
    {
        return _WtsUnmatchedCost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsUnmatchedCostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsUnmatchedCost = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsUnmatchedUnits()
    {
        return _WtsUnmatchedUnits;
    }
    
    // Standard Setter
    public void SetWtsUnmatchedUnits(decimal value)
    {
        _WtsUnmatchedUnits = value;
    }
    
    // Get<>AsString()
    public string GetWtsUnmatchedUnitsAsString()
    {
        return _WtsUnmatchedUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsUnmatchedUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsUnmatchedUnits = parsed;
    }
    
    // Standard Getter
    public string GetWtsIndex85Flag()
    {
        return _WtsIndex85Flag;
    }
    
    // Standard Setter
    public void SetWtsIndex85Flag(string value)
    {
        _WtsIndex85Flag = value;
    }
    
    // Get<>AsString()
    public string GetWtsIndex85FlagAsString()
    {
        return _WtsIndex85Flag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtsIndex85FlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtsIndex85Flag = value;
    }
    
    // Standard Getter
    public string GetWtsPoolRpt()
    {
        return _WtsPoolRpt;
    }
    
    // Standard Setter
    public void SetWtsPoolRpt(string value)
    {
        _WtsPoolRpt = value;
    }
    
    // Get<>AsString()
    public string GetWtsPoolRptAsString()
    {
        return _WtsPoolRpt.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtsPoolRptAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtsPoolRpt = value;
    }
    
    // Standard Getter
    public string GetWtsDsFound()
    {
        return _WtsDsFound;
    }
    
    // Standard Setter
    public void SetWtsDsFound(string value)
    {
        _WtsDsFound = value;
    }
    
    // Get<>AsString()
    public string GetWtsDsFoundAsString()
    {
        return _WtsDsFound.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWtsDsFoundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtsDsFound = value;
    }
    
    // Standard Getter
    public string GetWts2PctFlag()
    {
        return _Wts2PctFlag;
    }
    
    // Standard Setter
    public void SetWts2PctFlag(string value)
    {
        _Wts2PctFlag = value;
    }
    
    // Get<>AsString()
    public string GetWts2PctFlagAsString()
    {
        return _Wts2PctFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWts2PctFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Wts2PctFlag = value;
    }
    
    // Standard Getter
    public int GetWts30DayFlag()
    {
        return _Wts30DayFlag;
    }
    
    // Standard Setter
    public void SetWts30DayFlag(int value)
    {
        _Wts30DayFlag = value;
    }
    
    // Get<>AsString()
    public string GetWts30DayFlagAsString()
    {
        return _Wts30DayFlag.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetWts30DayFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Wts30DayFlag = parsed;
    }
    
    // Standard Getter
    public decimal GetWts30DayUnits()
    {
        return _Wts30DayUnits;
    }
    
    // Standard Setter
    public void SetWts30DayUnits(decimal value)
    {
        _Wts30DayUnits = value;
    }
    
    // Get<>AsString()
    public string GetWts30DayUnitsAsString()
    {
        return _Wts30DayUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWts30DayUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _Wts30DayUnits = parsed;
    }
    
    // Standard Getter
    public decimal GetWtsFa03ForwardMatchedUnits()
    {
        return _WtsFa03ForwardMatchedUnits;
    }
    
    // Standard Setter
    public void SetWtsFa03ForwardMatchedUnits(decimal value)
    {
        _WtsFa03ForwardMatchedUnits = value;
    }
    
    // Get<>AsString()
    public string GetWtsFa03ForwardMatchedUnitsAsString()
    {
        return _WtsFa03ForwardMatchedUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtsFa03ForwardMatchedUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsFa03ForwardMatchedUnits = parsed;
    }
    
    // Standard Getter
    public string GetWtsPriceTypeStatus()
    {
        return _WtsPriceTypeStatus;
    }
    
    // Standard Setter
    public void SetWtsPriceTypeStatus(string value)
    {
        _WtsPriceTypeStatus = value;
    }
    
    // Get<>AsString()
    public string GetWtsPriceTypeStatusAsString()
    {
        return _WtsPriceTypeStatus.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWtsPriceTypeStatusAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtsPriceTypeStatus = value;
    }
    
    // Standard Getter
    public string GetWtsReitFlag()
    {
        return _WtsReitFlag;
    }
    
    // Standard Setter
    public void SetWtsReitFlag(string value)
    {
        _WtsReitFlag = value;
    }
    
    // Get<>AsString()
    public string GetWtsReitFlagAsString()
    {
        return _WtsReitFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWtsReitFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtsReitFlag = value;
    }
    
    // Standard Getter
    public WtsIds GetWtsIds()
    {
        return _WtsIds;
    }
    
    // Standard Setter
    public void SetWtsIds(WtsIds value)
    {
        _WtsIds = value;
    }
    
    // Get<>AsString()
    public string GetWtsIdsAsString()
    {
        return _WtsIds != null ? _WtsIds.GetWtsIdsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtsIdsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtsIds == null)
        {
            _WtsIds = new WtsIds();
        }
        _WtsIds.SetWtsIdsAsString(value);
    }
    
    // Standard Getter
    public string GetWtsFundType()
    {
        return _WtsFundType;
    }
    
    // Standard Setter
    public void SetWtsFundType(string value)
    {
        _WtsFundType = value;
    }
    
    // Get<>AsString()
    public string GetWtsFundTypeAsString()
    {
        return _WtsFundType.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWtsFundTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtsFundType = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtsIds
    public class WtsIds
    {
        private static int _size = 81;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtsHoldingId, is_external=, is_static_class=False, static_prefix=
        private string _WtsHoldingId ="";
        
        
        
        
        // [DEBUG] Field: WtsFundId, is_external=, is_static_class=False, static_prefix=
        private string _WtsFundId ="";
        
        
        
        
        // [DEBUG] Field: WtsStockId, is_external=, is_static_class=False, static_prefix=
        private string _WtsStockId ="";
        
        
        
        
        // [DEBUG] Field: WtsBondOverrideId, is_external=, is_static_class=False, static_prefix=
        private string _WtsBondOverrideId ="";
        
        
        
        
        // [DEBUG] Field: WtsAssetUsageOverrideId, is_external=, is_static_class=False, static_prefix=
        private string _WtsAssetUsageOverrideId ="";
        
        
        
        
        // [DEBUG] Field: Filler453, is_external=, is_static_class=False, static_prefix=
        private string _Filler453 ="";
        
        
        
        
        // [DEBUG] Field: Filler454, is_external=, is_static_class=False, static_prefix=
        private string _Filler454 ="";
        
        
        
        
        // [DEBUG] Field: WtsDbTimestamp, is_external=, is_static_class=False, static_prefix=
        private string _WtsDbTimestamp ="";
        
        
        
        
        // [DEBUG] Field: WtsIlgIndex, is_external=, is_static_class=False, static_prefix=
        private int _WtsIlgIndex =0;
        
        
        // 88-level condition checks for WtsIlgIndex
        public bool IsWtsIlgIndexFirstPeriod()
        {
            if (this._WtsIlgIndex == 1) return true;
            return false;
        }
        public bool IsWtsIlgIndexOtherPeriod()
        {
            if (this._WtsIlgIndex == 2) return true;
            return false;
        }
        
        
    public WtsIds() {}
    
    public WtsIds(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtsHoldingId(data.Substring(offset, 8).Trim());
        offset += 8;
        SetWtsFundId(data.Substring(offset, 8).Trim());
        offset += 8;
        SetWtsStockId(data.Substring(offset, 8).Trim());
        offset += 8;
        SetWtsBondOverrideId(data.Substring(offset, 8).Trim());
        offset += 8;
        SetWtsAssetUsageOverrideId(data.Substring(offset, 8).Trim());
        offset += 8;
        SetFiller453(data.Substring(offset, 8).Trim());
        offset += 8;
        SetFiller454(data.Substring(offset, 8).Trim());
        offset += 8;
        SetWtsDbTimestamp(data.Substring(offset, 24).Trim());
        offset += 24;
        SetWtsIlgIndex(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        
    }
    
    // Serialization methods
    public string GetWtsIdsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtsHoldingId.PadRight(8));
        result.Append(_WtsFundId.PadRight(8));
        result.Append(_WtsStockId.PadRight(8));
        result.Append(_WtsBondOverrideId.PadRight(8));
        result.Append(_WtsAssetUsageOverrideId.PadRight(8));
        result.Append(_Filler453.PadRight(8));
        result.Append(_Filler454.PadRight(8));
        result.Append(_WtsDbTimestamp.PadRight(24));
        result.Append(_WtsIlgIndex.ToString().PadLeft(1, '0'));
        
        return result.ToString();
    }
    
    public void SetWtsIdsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWtsHoldingId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWtsFundId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWtsStockId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWtsBondOverrideId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetWtsAssetUsageOverrideId(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller453(extracted);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetFiller454(extracted);
        }
        offset += 8;
        if (offset + 24 <= data.Length)
        {
            string extracted = data.Substring(offset, 24).Trim();
            SetWtsDbTimestamp(extracted);
        }
        offset += 24;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtsIlgIndex(parsedInt);
        }
        offset += 1;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtsHoldingId()
    {
        return _WtsHoldingId;
    }
    
    // Standard Setter
    public void SetWtsHoldingId(string value)
    {
        _WtsHoldingId = value;
    }
    
    // Get<>AsString()
    public string GetWtsHoldingIdAsString()
    {
        return _WtsHoldingId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWtsHoldingIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtsHoldingId = value;
    }
    
    // Standard Getter
    public string GetWtsFundId()
    {
        return _WtsFundId;
    }
    
    // Standard Setter
    public void SetWtsFundId(string value)
    {
        _WtsFundId = value;
    }
    
    // Get<>AsString()
    public string GetWtsFundIdAsString()
    {
        return _WtsFundId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWtsFundIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtsFundId = value;
    }
    
    // Standard Getter
    public string GetWtsStockId()
    {
        return _WtsStockId;
    }
    
    // Standard Setter
    public void SetWtsStockId(string value)
    {
        _WtsStockId = value;
    }
    
    // Get<>AsString()
    public string GetWtsStockIdAsString()
    {
        return _WtsStockId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWtsStockIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtsStockId = value;
    }
    
    // Standard Getter
    public string GetWtsBondOverrideId()
    {
        return _WtsBondOverrideId;
    }
    
    // Standard Setter
    public void SetWtsBondOverrideId(string value)
    {
        _WtsBondOverrideId = value;
    }
    
    // Get<>AsString()
    public string GetWtsBondOverrideIdAsString()
    {
        return _WtsBondOverrideId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWtsBondOverrideIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtsBondOverrideId = value;
    }
    
    // Standard Getter
    public string GetWtsAssetUsageOverrideId()
    {
        return _WtsAssetUsageOverrideId;
    }
    
    // Standard Setter
    public void SetWtsAssetUsageOverrideId(string value)
    {
        _WtsAssetUsageOverrideId = value;
    }
    
    // Get<>AsString()
    public string GetWtsAssetUsageOverrideIdAsString()
    {
        return _WtsAssetUsageOverrideId.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetWtsAssetUsageOverrideIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtsAssetUsageOverrideId = value;
    }
    
    // Standard Getter
    public string GetFiller453()
    {
        return _Filler453;
    }
    
    // Standard Setter
    public void SetFiller453(string value)
    {
        _Filler453 = value;
    }
    
    // Get<>AsString()
    public string GetFiller453AsString()
    {
        return _Filler453.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller453AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler453 = value;
    }
    
    // Standard Getter
    public string GetFiller454()
    {
        return _Filler454;
    }
    
    // Standard Setter
    public void SetFiller454(string value)
    {
        _Filler454 = value;
    }
    
    // Get<>AsString()
    public string GetFiller454AsString()
    {
        return _Filler454.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetFiller454AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler454 = value;
    }
    
    // Standard Getter
    public string GetWtsDbTimestamp()
    {
        return _WtsDbTimestamp;
    }
    
    // Standard Setter
    public void SetWtsDbTimestamp(string value)
    {
        _WtsDbTimestamp = value;
    }
    
    // Get<>AsString()
    public string GetWtsDbTimestampAsString()
    {
        return _WtsDbTimestamp.PadRight(24);
    }
    
    // Set<>AsString()
    public void SetWtsDbTimestampAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtsDbTimestamp = value;
    }
    
    // Standard Getter
    public int GetWtsIlgIndex()
    {
        return _WtsIlgIndex;
    }
    
    // Standard Setter
    public void SetWtsIlgIndex(int value)
    {
        _WtsIlgIndex = value;
    }
    
    // Get<>AsString()
    public string GetWtsIlgIndexAsString()
    {
        return _WtsIlgIndex.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetWtsIlgIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtsIlgIndex = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
// Nested Class: WtsRecord
public class WtsRecord
{
    private static int _size = 488;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtsKey, is_external=, is_static_class=False, static_prefix=
    private WtsRecord.WtsKey _WtsKey = new WtsRecord.WtsKey();
    
    
    
    
    // [DEBUG] Field: WtsTransactionCategory, is_external=, is_static_class=False, static_prefix=
    private string _WtsTransactionCategory ="";
    
    
    
    
    // [DEBUG] Field: WtsDateTimeStamp, is_external=, is_static_class=False, static_prefix=
    private WtsRecord.WtsDateTimeStamp _WtsDateTimeStamp = new WtsRecord.WtsDateTimeStamp();
    
    
    
    
    // [DEBUG] Field: Filler455, is_external=, is_static_class=False, static_prefix=
    private WtsRecord.Filler455 _Filler455 = new WtsRecord.Filler455();
    
    
    
    
    // [DEBUG] Field: WtsCalcFlag, is_external=, is_static_class=False, static_prefix=
    private string _WtsCalcFlag ="";
    
    
    
    
    // [DEBUG] Field: WtsCalcRealDate, is_external=, is_static_class=False, static_prefix=
    private string _WtsCalcRealDate ="";
    
    
    
    
    // [DEBUG] Field: WtsCalcUnrealDate, is_external=, is_static_class=False, static_prefix=
    private string _WtsCalcUnrealDate ="";
    
    
    
    
    // [DEBUG] Field: Filler456, is_external=, is_static_class=False, static_prefix=
    private string _Filler456 ="";
    
    
    
    
    // [DEBUG] Field: WtsSecuritySortCode, is_external=, is_static_class=False, static_prefix=
    private string _WtsSecuritySortCode ="";
    
    
    
    
    // [DEBUG] Field: WtsCountryCode, is_external=, is_static_class=False, static_prefix=
    private string _WtsCountryCode ="";
    
    
    
    
    // [DEBUG] Field: WtsMainGroupCode, is_external=, is_static_class=False, static_prefix=
    private WtsRecord.WtsMainGroupCode _WtsMainGroupCode = new WtsRecord.WtsMainGroupCode();
    
    
    
    
    // [DEBUG] Field: WtsIndustrialClass, is_external=, is_static_class=False, static_prefix=
    private int _WtsIndustrialClass =0;
    
    
    
    
    // [DEBUG] Field: WtsSecurityType, is_external=, is_static_class=False, static_prefix=
    private string _WtsSecurityType ="";
    
    
    // 88-level condition checks for WtsSecurityType
    public bool IsWtsSecurityOther()
    {
        if (this._WtsSecurityType == "'A'") return true;
        return false;
    }
    public bool IsWtsSecurityGilts()
    {
        if (this._WtsSecurityType == "'B'") return true;
        if (this._WtsSecurityType == "'Y'") return true;
        return false;
    }
    public bool IsWtsIndexLinkedGilts()
    {
        if (this._WtsSecurityType == "'Y'") return true;
        return false;
    }
    public bool IsWtsCorporateBonds()
    {
        if (this._WtsSecurityType == "'C'") return true;
        return false;
    }
    public bool IsWtsFixedInterest()
    {
        if (this._WtsSecurityType == "'F'") return true;
        return false;
    }
    public bool IsWtsPrivatisation()
    {
        if (this._WtsSecurityType == "'P'") return true;
        return false;
    }
    public bool IsWtsSecurityTaxExemptGilts()
    {
        if (this._WtsSecurityType == "'X'") return true;
        return false;
    }
    public bool IsWtsRelevantSecurities()
    {
        if (this._WtsSecurityType == "'B'") return true;
        if (this._WtsSecurityType == "'C'") return true;
        if (this._WtsSecurityType == "'X'") return true;
        if (this._WtsSecurityType == "'F'") return true;
        if (this._WtsSecurityType == "'E'") return true;
        if (this._WtsSecurityType == "'Y'") return true;
        if (this._WtsSecurityType == "'M'") return true;
        return false;
    }
    public bool IsWtsUntaxableFa89()
    {
        if (this._WtsSecurityType == "'E'") return true;
        return false;
    }
    public bool IsWtsGilts()
    {
        if (this._WtsSecurityType == "'B'") return true;
        if (this._WtsSecurityType == "'X'") return true;
        if (this._WtsSecurityType == "'Y'") return true;
        return false;
    }
    public bool IsWtsIntegralUnits()
    {
        if (this._WtsSecurityType == "'A'") return true;
        if (this._WtsSecurityType == "'P'") return true;
        return false;
    }
    public bool IsWtsRelevantFa90()
    {
        if (this._WtsSecurityType == "'U'") return true;
        if (this._WtsSecurityType == "'O'") return true;
        return false;
    }
    public bool IsWtsOffshoreFund()
    {
        if (this._WtsSecurityType == "'O'") return true;
        return false;
    }
    public bool IsWtsBond()
    {
        if (this._WtsSecurityType == "'B'") return true;
        if (this._WtsSecurityType == "'C'") return true;
        if (this._WtsSecurityType == "'E'") return true;
        if (this._WtsSecurityType == "'F'") return true;
        if (this._WtsSecurityType == "'O'") return true;
        if (this._WtsSecurityType == "'U'") return true;
        if (this._WtsSecurityType == "'X'") return true;
        if (this._WtsSecurityType == "'Y'") return true;
        return false;
    }
    public bool IsWtsOrdinary()
    {
        if (this._WtsSecurityType == "' '") return true;
        return false;
    }
    public bool IsWtsUnitTrust()
    {
        if (this._WtsSecurityType == "'U'") return true;
        return false;
    }
    public bool IsWtsReit()
    {
        if (this._WtsSecurityType == "'R'") return true;
        return false;
    }
    public bool IsWtsShortFuture()
    {
        if (this._WtsSecurityType == "'G'") return true;
        return false;
    }
    public bool IsWtsLongFuture()
    {
        if (this._WtsSecurityType == "'H'") return true;
        return false;
    }
    public bool IsWtsPurchasedCall()
    {
        if (this._WtsSecurityType == "'I'") return true;
        return false;
    }
    public bool IsWtsPurchasedPut()
    {
        if (this._WtsSecurityType == "'J'") return true;
        return false;
    }
    public bool IsWtsWrittenCall()
    {
        if (this._WtsSecurityType == "'K'") return true;
        return false;
    }
    public bool IsWtsWrittenPut()
    {
        if (this._WtsSecurityType == "'L'") return true;
        return false;
    }
    public bool IsWtsDerivative()
    {
        if (!string.IsNullOrEmpty(this.GetWtsSecurityType()) && this.GetWtsSecurityType().Length == 1)
        {
            var ch = this.GetWtsSecurityType()[0];
            if (ch >= 'G' && ch <= 'L') return true;
        }
        return false;
    }
    public bool IsWtsOption()
    {
        if (!string.IsNullOrEmpty(this.GetWtsSecurityType()) && this.GetWtsSecurityType().Length == 1)
        {
            var ch = this.GetWtsSecurityType()[0];
            if (ch >= 'I' && ch <= 'L') return true;
        }
        return false;
    }
    public bool IsWtsShortWrittenDerivative()
    {
        if (this._WtsSecurityType == "'G'") return true;
        if (this._WtsSecurityType == "'K'") return true;
        if (this._WtsSecurityType == "'L'") return true;
        return false;
    }
    public bool IsWtsFutureWrittenDerivative()
    {
        if (this._WtsSecurityType == "'G'") return true;
        if (this._WtsSecurityType == "'H'") return true;
        if (this._WtsSecurityType == "'K'") return true;
        if (this._WtsSecurityType == "'L'") return true;
        return false;
    }
    public bool IsWtsIrishGovernmentBond()
    {
        if (this._WtsSecurityType == "'M'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WtsCurrentMarketPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsCurrentMarketPrice =0;
    
    
    
    
    // [DEBUG] Field: WtsPricePctIndicator, is_external=, is_static_class=False, static_prefix=
    private string _WtsPricePctIndicator ="";
    
    
    
    
    // [DEBUG] Field: WtsBfAccountingValue, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsBfAccountingValue =0;
    
    
    
    
    // [DEBUG] Field: WtsAccountingValueYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsAccountingValueYtd =0;
    
    
    
    
    // [DEBUG] Field: WtsProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: WtsIssuersName, is_external=, is_static_class=False, static_prefix=
    private string _WtsIssuersName ="";
    
    
    
    
    // [DEBUG] Field: WtsStockDescription, is_external=, is_static_class=False, static_prefix=
    private string _WtsStockDescription ="";
    
    
    
    
    // [DEBUG] Field: WtsIssuedCapital, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsIssuedCapital =0;
    
    
    
    
    // [DEBUG] Field: WtsIssuedCapitalX, is_external=, is_static_class=False, static_prefix=
    private string _WtsIssuedCapitalX ="";
    
    
    
    
    // [DEBUG] Field: WtsMovementIndicator, is_external=, is_static_class=False, static_prefix=
    private int _WtsMovementIndicator =0;
    
    
    
    
    // [DEBUG] Field: WtsBf2PctHoldingDate, is_external=, is_static_class=False, static_prefix=
    private int _WtsBf2PctHoldingDate =0;
    
    
    
    
    // [DEBUG] Field: Wts2PctHoldingDateYtd, is_external=, is_static_class=False, static_prefix=
    private int _Wts2PctHoldingDateYtd =0;
    
    
    // 88-level condition checks for Wts2PctHoldingDateYtd
    public bool IsWts2PctDateYtdNotSet()
    {
        if (this._Wts2PctHoldingDateYtd == 999999) return true;
        return false;
    }
    public bool IsWts2PctDateYtdSet()
    {
        if (this._Wts2PctHoldingDateYtd >= 101 && this._Wts2PctHoldingDateYtd <= 991231) return true;
        return false;
    }
    
    
    // [DEBUG] Field: WtsSecurityIndicator, is_external=, is_static_class=False, static_prefix=
    private string _WtsSecurityIndicator ="";
    
    
    // 88-level condition checks for WtsSecurityIndicator
    public bool IsWtsQuotedSecurity()
    {
        if (this._WtsSecurityIndicator == "'0'") return true;
        if (this._WtsSecurityIndicator == "LOW-VALUES") return true;
        return false;
    }
    public bool IsWtsUnquotedSecurity()
    {
        if (this._WtsSecurityIndicator == "'1'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WtsTotalUnitsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsTotalUnitsYtd =0;
    
    
    
    
    // [DEBUG] Field: WtsCapitalGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsCapitalGainLoss =0;
    
    
    
    
    // [DEBUG] Field: WtsUnrealGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsUnrealGainLoss =0;
    
    
    
    
    // [DEBUG] Field: WtsUnrealProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsUnrealProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: WtsFlagA, is_external=, is_static_class=False, static_prefix=
    private string _WtsFlagA ="";
    
    
    // 88-level condition checks for WtsFlagA
    public bool IsWtsValidFlagA()
    {
        if (this._WtsFlagA == "' '") return true;
        if (this._WtsFlagA == "'A'") return true;
        if (this._WtsFlagA == "'B'") return true;
        if (this._WtsFlagA == "'C'") return true;
        return false;
    }
    public bool IsWtsQualifying()
    {
        if (this._WtsFlagA == "' '") return true;
        if (this._WtsFlagA == "'A'") return true;
        return false;
    }
    public bool IsWtsSection54()
    {
        if (this._WtsFlagA == "'A'") return true;
        if (this._WtsFlagA == "'C'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WtsFlagB, is_external=, is_static_class=False, static_prefix=
    private string _WtsFlagB ="";
    
    
    
    
    // [DEBUG] Field: WtsDateOfIssue, is_external=, is_static_class=False, static_prefix=
    private int _WtsDateOfIssue =0;
    
    
    
    
    // [DEBUG] Field: Filler457, is_external=, is_static_class=False, static_prefix=
    private string _Filler457 ="";
    
    
    // 88-level condition checks for Filler457
    public bool IsWtsNullDateOfIssue()
    {
        if (this._Filler457 == "SPACES") return true;
        if (this._Filler457 == "ZEROES") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WtsIndexFromIssue, is_external=, is_static_class=False, static_prefix=
    private string _WtsIndexFromIssue ="";
    
    
    
    
    // [DEBUG] Field: WtsDeemedGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsDeemedGainLoss =0;
    
    
    
    
    // [DEBUG] Field: WtsDeemedProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsDeemedProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: WtsHoldingFlag, is_external=, is_static_class=False, static_prefix=
    private string _WtsHoldingFlag ="";
    
    
    
    
    // [DEBUG] Field: WtsBondOverride, is_external=, is_static_class=False, static_prefix=
    private string _WtsBondOverride ="";
    
    
    
    
    // [DEBUG] Field: WtsLrBasis, is_external=, is_static_class=False, static_prefix=
    private string _WtsLrBasis ="";
    
    
    // 88-level condition checks for WtsLrBasis
    public bool IsWtsBondProcessing()
    {
        if (this._WtsLrBasis == "'A'") return true;
        if (this._WtsLrBasis == "'M'") return true;
        return false;
    }
    public bool IsWtsMarkToMarket()
    {
        if (this._WtsLrBasis == "'M'") return true;
        return false;
    }
    public bool IsWtsAccrual()
    {
        if (this._WtsLrBasis == "'A'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WtsBondMaturityDate, is_external=, is_static_class=False, static_prefix=
    private string _WtsBondMaturityDate ="";
    
    
    
    
    // [DEBUG] Field: Filler458, is_external=, is_static_class=False, static_prefix=
    private WtsRecord.Filler458 _Filler458 = new WtsRecord.Filler458();
    
    
    
    
    // [DEBUG] Field: WtsOptionExpiryDate, is_external=, is_static_class=False, static_prefix=
    private string _WtsOptionExpiryDate ="";
    
    
    
    
    // [DEBUG] Field: WtsBondParValueX, is_external=, is_static_class=False, static_prefix=
    private string _WtsBondParValueX ="";
    
    
    
    
    // [DEBUG] Field: WtsBondParValue, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsBondParValue =0;
    
    
    
    
    // [DEBUG] Field: WtsBondGainOverride, is_external=, is_static_class=False, static_prefix=
    private string _WtsBondGainOverride ="";
    
    
    // 88-level condition checks for WtsBondGainOverride
    public bool IsWtsGainRealised()
    {
        if (this._WtsBondGainOverride == "'R'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WtsUnrealBondGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsUnrealBondGainLoss =0;
    
    
    
    
    // [DEBUG] Field: WtsUnrealBondProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsUnrealBondProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: WtsAssetUsage, is_external=, is_static_class=False, static_prefix=
    private string _WtsAssetUsage ="";
    
    
    
    
    // [DEBUG] Field: WtsRealBondGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsRealBondGainLoss =0;
    
    
    
    
    // [DEBUG] Field: WtsRealBondProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _WtsRealBondProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: Filler460, is_external=, is_static_class=False, static_prefix=
    private string _Filler460 ="";
    
    
    
    
public WtsRecord() {}

public WtsRecord(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WtsKey.SetWtsKeyAsString(data.Substring(offset, WtsKey.GetSize()));
    offset += 25;
    SetWtsTransactionCategory(data.Substring(offset, 2).Trim());
    offset += 2;
    _WtsDateTimeStamp.SetWtsDateTimeStampAsString(data.Substring(offset, WtsDateTimeStamp.GetSize()));
    offset += 14;
    _Filler455.SetFiller455AsString(data.Substring(offset, Filler455.GetSize()));
    offset += 14;
    SetWtsCalcFlag(data.Substring(offset, 4).Trim());
    offset += 4;
    SetWtsCalcRealDate(data.Substring(offset, 6).Trim());
    offset += 6;
    SetWtsCalcUnrealDate(data.Substring(offset, 6).Trim());
    offset += 6;
    SetFiller456(data.Substring(offset, 6).Trim());
    offset += 6;
    SetWtsSecuritySortCode(data.Substring(offset, 15).Trim());
    offset += 15;
    SetWtsCountryCode(data.Substring(offset, 3).Trim());
    offset += 3;
    _WtsMainGroupCode.SetWtsMainGroupCodeAsString(data.Substring(offset, WtsMainGroupCode.GetSize()));
    offset += 3;
    SetWtsIndustrialClass(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWtsSecurityType(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWtsCurrentMarketPrice(PackedDecimalConverter.ToDecimal(data.Substring(offset, 10)));
    offset += 10;
    SetWtsPricePctIndicator(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWtsBfAccountingValue(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetWtsAccountingValueYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 17)));
    offset += 17;
    SetWtsProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWtsIssuersName(data.Substring(offset, 35).Trim());
    offset += 35;
    SetWtsStockDescription(data.Substring(offset, 40).Trim());
    offset += 40;
    SetWtsIssuedCapital(PackedDecimalConverter.ToDecimal(data.Substring(offset, 10)));
    offset += 10;
    SetWtsIssuedCapitalX(data.Substring(offset, 8).Trim());
    offset += 8;
    SetWtsMovementIndicator(int.Parse(data.Substring(offset, 1).Trim()));
    offset += 1;
    SetWtsBf2PctHoldingDate(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetWts2PctHoldingDateYtd(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetWtsSecurityIndicator(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWtsTotalUnitsYtd(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
    offset += 13;
    SetWtsCapitalGainLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWtsUnrealGainLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWtsUnrealProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWtsFlagA(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWtsFlagB(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWtsDateOfIssue(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller457(data.Substring(offset, 6).Trim());
    offset += 6;
    SetWtsIndexFromIssue(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWtsDeemedGainLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWtsDeemedProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWtsHoldingFlag(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWtsBondOverride(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWtsLrBasis(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWtsBondMaturityDate(data.Substring(offset, 8).Trim());
    offset += 8;
    _Filler458.SetFiller458AsString(data.Substring(offset, Filler458.GetSize()));
    offset += 12;
    SetWtsOptionExpiryDate(data.Substring(offset, 8).Trim());
    offset += 8;
    SetWtsBondParValueX(data.Substring(offset, 6).Trim());
    offset += 6;
    SetWtsBondParValue(PackedDecimalConverter.ToDecimal(data.Substring(offset, 6)));
    offset += 6;
    SetWtsBondGainOverride(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWtsUnrealBondGainLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWtsUnrealBondProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWtsAssetUsage(data.Substring(offset, 0).Trim());
    offset += 0;
    SetWtsRealBondGainLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetWtsRealBondProfitLoss(PackedDecimalConverter.ToDecimal(data.Substring(offset, 15)));
    offset += 15;
    SetFiller460(data.Substring(offset, 13).Trim());
    offset += 13;
    
}

// Serialization methods
public string GetWtsRecordAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtsKey.GetWtsKeyAsString());
    result.Append(_WtsTransactionCategory.PadRight(2));
    result.Append(_WtsDateTimeStamp.GetWtsDateTimeStampAsString());
    result.Append(_Filler455.GetFiller455AsString());
    result.Append(_WtsCalcFlag.PadRight(4));
    result.Append(_WtsCalcRealDate.PadRight(6));
    result.Append(_WtsCalcUnrealDate.PadRight(6));
    result.Append(_Filler456.PadRight(6));
    result.Append(_WtsSecuritySortCode.PadRight(15));
    result.Append(_WtsCountryCode.PadRight(3));
    result.Append(_WtsMainGroupCode.GetWtsMainGroupCodeAsString());
    result.Append(_WtsIndustrialClass.ToString().PadLeft(2, '0'));
    result.Append(_WtsSecurityType.PadRight(1));
    result.Append(_WtsCurrentMarketPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtsPricePctIndicator.PadRight(1));
    result.Append(_WtsBfAccountingValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtsAccountingValueYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtsProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtsIssuersName.PadRight(35));
    result.Append(_WtsStockDescription.PadRight(40));
    result.Append(_WtsIssuedCapital.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtsIssuedCapitalX.PadRight(8));
    result.Append(_WtsMovementIndicator.ToString().PadLeft(1, '0'));
    result.Append(_WtsBf2PctHoldingDate.ToString().PadLeft(6, '0'));
    result.Append(_Wts2PctHoldingDateYtd.ToString().PadLeft(6, '0'));
    result.Append(_WtsSecurityIndicator.PadRight(1));
    result.Append(_WtsTotalUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtsCapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtsUnrealGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtsUnrealProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtsFlagA.PadRight(1));
    result.Append(_WtsFlagB.PadRight(1));
    result.Append(_WtsDateOfIssue.ToString().PadLeft(6, '0'));
    result.Append(_Filler457.PadRight(6));
    result.Append(_WtsIndexFromIssue.PadRight(1));
    result.Append(_WtsDeemedGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtsDeemedProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtsHoldingFlag.PadRight(1));
    result.Append(_WtsBondOverride.PadRight(1));
    result.Append(_WtsLrBasis.PadRight(1));
    result.Append(_WtsBondMaturityDate.PadRight(8));
    result.Append(_Filler458.GetFiller458AsString());
    result.Append(_WtsOptionExpiryDate.PadRight(8));
    result.Append(_WtsBondParValueX.PadRight(6));
    result.Append(_WtsBondParValue.ToString("F1", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtsBondGainOverride.PadRight(1));
    result.Append(_WtsUnrealBondGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtsUnrealBondProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtsAssetUsage.PadRight(0));
    result.Append(_WtsRealBondGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_WtsRealBondProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
    result.Append(_Filler460.PadRight(13));
    
    return result.ToString();
}

public void SetWtsRecordAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 25 <= data.Length)
    {
        _WtsKey.SetWtsKeyAsString(data.Substring(offset, 25));
    }
    else
    {
        _WtsKey.SetWtsKeyAsString(data.Substring(offset));
    }
    offset += 25;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWtsTransactionCategory(extracted);
    }
    offset += 2;
    if (offset + 14 <= data.Length)
    {
        _WtsDateTimeStamp.SetWtsDateTimeStampAsString(data.Substring(offset, 14));
    }
    else
    {
        _WtsDateTimeStamp.SetWtsDateTimeStampAsString(data.Substring(offset));
    }
    offset += 14;
    if (offset + 14 <= data.Length)
    {
        _Filler455.SetFiller455AsString(data.Substring(offset, 14));
    }
    else
    {
        _Filler455.SetFiller455AsString(data.Substring(offset));
    }
    offset += 14;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWtsCalcFlag(extracted);
    }
    offset += 4;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetWtsCalcRealDate(extracted);
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetWtsCalcUnrealDate(extracted);
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetFiller456(extracted);
    }
    offset += 6;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        SetWtsSecuritySortCode(extracted);
    }
    offset += 15;
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetWtsCountryCode(extracted);
    }
    offset += 3;
    if (offset + 3 <= data.Length)
    {
        _WtsMainGroupCode.SetWtsMainGroupCodeAsString(data.Substring(offset, 3));
    }
    else
    {
        _WtsMainGroupCode.SetWtsMainGroupCodeAsString(data.Substring(offset));
    }
    offset += 3;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtsIndustrialClass(parsedInt);
    }
    offset += 2;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtsSecurityType(extracted);
    }
    offset += 1;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtsCurrentMarketPrice(parsedDec);
    }
    offset += 10;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtsPricePctIndicator(extracted);
    }
    offset += 1;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtsBfAccountingValue(parsedDec);
    }
    offset += 17;
    if (offset + 17 <= data.Length)
    {
        string extracted = data.Substring(offset, 17).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtsAccountingValueYtd(parsedDec);
    }
    offset += 17;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtsProfitLoss(parsedDec);
    }
    offset += 15;
    if (offset + 35 <= data.Length)
    {
        string extracted = data.Substring(offset, 35).Trim();
        SetWtsIssuersName(extracted);
    }
    offset += 35;
    if (offset + 40 <= data.Length)
    {
        string extracted = data.Substring(offset, 40).Trim();
        SetWtsStockDescription(extracted);
    }
    offset += 40;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtsIssuedCapital(parsedDec);
    }
    offset += 10;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWtsIssuedCapitalX(extracted);
    }
    offset += 8;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtsMovementIndicator(parsedInt);
    }
    offset += 1;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtsBf2PctHoldingDate(parsedInt);
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWts2PctHoldingDateYtd(parsedInt);
    }
    offset += 6;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtsSecurityIndicator(extracted);
    }
    offset += 1;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtsTotalUnitsYtd(parsedDec);
    }
    offset += 13;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtsCapitalGainLoss(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtsUnrealGainLoss(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtsUnrealProfitLoss(parsedDec);
    }
    offset += 15;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtsFlagA(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtsFlagB(extracted);
    }
    offset += 1;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtsDateOfIssue(parsedInt);
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetFiller457(extracted);
    }
    offset += 6;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtsIndexFromIssue(extracted);
    }
    offset += 1;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtsDeemedGainLoss(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtsDeemedProfitLoss(parsedDec);
    }
    offset += 15;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtsHoldingFlag(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtsBondOverride(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtsLrBasis(extracted);
    }
    offset += 1;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWtsBondMaturityDate(extracted);
    }
    offset += 8;
    if (offset + 12 <= data.Length)
    {
        _Filler458.SetFiller458AsString(data.Substring(offset, 12));
    }
    else
    {
        _Filler458.SetFiller458AsString(data.Substring(offset));
    }
    offset += 12;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWtsOptionExpiryDate(extracted);
    }
    offset += 8;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetWtsBondParValueX(extracted);
    }
    offset += 6;
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtsBondParValue(parsedDec);
    }
    offset += 6;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtsBondGainOverride(extracted);
    }
    offset += 1;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtsUnrealBondGainLoss(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtsUnrealBondProfitLoss(parsedDec);
    }
    offset += 15;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetWtsAssetUsage(extracted);
    }
    offset += 0;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtsRealBondGainLoss(parsedDec);
    }
    offset += 15;
    if (offset + 15 <= data.Length)
    {
        string extracted = data.Substring(offset, 15).Trim();
        decimal parsedDec;
        if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
        SetWtsRealBondProfitLoss(parsedDec);
    }
    offset += 15;
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetFiller460(extracted);
    }
    offset += 13;
}

// Getter and Setter methods

// Standard Getter
public WtsKey GetWtsKey()
{
    return _WtsKey;
}

// Standard Setter
public void SetWtsKey(WtsKey value)
{
    _WtsKey = value;
}

// Get<>AsString()
public string GetWtsKeyAsString()
{
    return _WtsKey != null ? _WtsKey.GetWtsKeyAsString() : "";
}

// Set<>AsString()
public void SetWtsKeyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtsKey == null)
    {
        _WtsKey = new WtsKey();
    }
    _WtsKey.SetWtsKeyAsString(value);
}

// Standard Getter
public string GetWtsTransactionCategory()
{
    return _WtsTransactionCategory;
}

// Standard Setter
public void SetWtsTransactionCategory(string value)
{
    _WtsTransactionCategory = value;
}

// Get<>AsString()
public string GetWtsTransactionCategoryAsString()
{
    return _WtsTransactionCategory.PadRight(2);
}

// Set<>AsString()
public void SetWtsTransactionCategoryAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsTransactionCategory = value;
}

// Standard Getter
public WtsDateTimeStamp GetWtsDateTimeStamp()
{
    return _WtsDateTimeStamp;
}

// Standard Setter
public void SetWtsDateTimeStamp(WtsDateTimeStamp value)
{
    _WtsDateTimeStamp = value;
}

// Get<>AsString()
public string GetWtsDateTimeStampAsString()
{
    return _WtsDateTimeStamp != null ? _WtsDateTimeStamp.GetWtsDateTimeStampAsString() : "";
}

// Set<>AsString()
public void SetWtsDateTimeStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtsDateTimeStamp == null)
    {
        _WtsDateTimeStamp = new WtsDateTimeStamp();
    }
    _WtsDateTimeStamp.SetWtsDateTimeStampAsString(value);
}

// Standard Getter
public Filler455 GetFiller455()
{
    return _Filler455;
}

// Standard Setter
public void SetFiller455(Filler455 value)
{
    _Filler455 = value;
}

// Get<>AsString()
public string GetFiller455AsString()
{
    return _Filler455 != null ? _Filler455.GetFiller455AsString() : "";
}

// Set<>AsString()
public void SetFiller455AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler455 == null)
    {
        _Filler455 = new Filler455();
    }
    _Filler455.SetFiller455AsString(value);
}

// Standard Getter
public string GetWtsCalcFlag()
{
    return _WtsCalcFlag;
}

// Standard Setter
public void SetWtsCalcFlag(string value)
{
    _WtsCalcFlag = value;
}

// Get<>AsString()
public string GetWtsCalcFlagAsString()
{
    return _WtsCalcFlag.PadRight(4);
}

// Set<>AsString()
public void SetWtsCalcFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsCalcFlag = value;
}

// Standard Getter
public string GetWtsCalcRealDate()
{
    return _WtsCalcRealDate;
}

// Standard Setter
public void SetWtsCalcRealDate(string value)
{
    _WtsCalcRealDate = value;
}

// Get<>AsString()
public string GetWtsCalcRealDateAsString()
{
    return _WtsCalcRealDate.PadRight(6);
}

// Set<>AsString()
public void SetWtsCalcRealDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsCalcRealDate = value;
}

// Standard Getter
public string GetWtsCalcUnrealDate()
{
    return _WtsCalcUnrealDate;
}

// Standard Setter
public void SetWtsCalcUnrealDate(string value)
{
    _WtsCalcUnrealDate = value;
}

// Get<>AsString()
public string GetWtsCalcUnrealDateAsString()
{
    return _WtsCalcUnrealDate.PadRight(6);
}

// Set<>AsString()
public void SetWtsCalcUnrealDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsCalcUnrealDate = value;
}

// Standard Getter
public string GetFiller456()
{
    return _Filler456;
}

// Standard Setter
public void SetFiller456(string value)
{
    _Filler456 = value;
}

// Get<>AsString()
public string GetFiller456AsString()
{
    return _Filler456.PadRight(6);
}

// Set<>AsString()
public void SetFiller456AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler456 = value;
}

// Standard Getter
public string GetWtsSecuritySortCode()
{
    return _WtsSecuritySortCode;
}

// Standard Setter
public void SetWtsSecuritySortCode(string value)
{
    _WtsSecuritySortCode = value;
}

// Get<>AsString()
public string GetWtsSecuritySortCodeAsString()
{
    return _WtsSecuritySortCode.PadRight(15);
}

// Set<>AsString()
public void SetWtsSecuritySortCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsSecuritySortCode = value;
}

// Standard Getter
public string GetWtsCountryCode()
{
    return _WtsCountryCode;
}

// Standard Setter
public void SetWtsCountryCode(string value)
{
    _WtsCountryCode = value;
}

// Get<>AsString()
public string GetWtsCountryCodeAsString()
{
    return _WtsCountryCode.PadRight(3);
}

// Set<>AsString()
public void SetWtsCountryCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsCountryCode = value;
}

// Standard Getter
public WtsMainGroupCode GetWtsMainGroupCode()
{
    return _WtsMainGroupCode;
}

// Standard Setter
public void SetWtsMainGroupCode(WtsMainGroupCode value)
{
    _WtsMainGroupCode = value;
}

// Get<>AsString()
public string GetWtsMainGroupCodeAsString()
{
    return _WtsMainGroupCode != null ? _WtsMainGroupCode.GetWtsMainGroupCodeAsString() : "";
}

// Set<>AsString()
public void SetWtsMainGroupCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtsMainGroupCode == null)
    {
        _WtsMainGroupCode = new WtsMainGroupCode();
    }
    _WtsMainGroupCode.SetWtsMainGroupCodeAsString(value);
}

// Standard Getter
public int GetWtsIndustrialClass()
{
    return _WtsIndustrialClass;
}

// Standard Setter
public void SetWtsIndustrialClass(int value)
{
    _WtsIndustrialClass = value;
}

// Get<>AsString()
public string GetWtsIndustrialClassAsString()
{
    return _WtsIndustrialClass.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWtsIndustrialClassAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtsIndustrialClass = parsed;
}

// Standard Getter
public string GetWtsSecurityType()
{
    return _WtsSecurityType;
}

// Standard Setter
public void SetWtsSecurityType(string value)
{
    _WtsSecurityType = value;
}

// Get<>AsString()
public string GetWtsSecurityTypeAsString()
{
    return _WtsSecurityType.PadRight(1);
}

// Set<>AsString()
public void SetWtsSecurityTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsSecurityType = value;
}

// Standard Getter
public decimal GetWtsCurrentMarketPrice()
{
    return _WtsCurrentMarketPrice;
}

// Standard Setter
public void SetWtsCurrentMarketPrice(decimal value)
{
    _WtsCurrentMarketPrice = value;
}

// Get<>AsString()
public string GetWtsCurrentMarketPriceAsString()
{
    return _WtsCurrentMarketPrice.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtsCurrentMarketPriceAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsCurrentMarketPrice = parsed;
}

// Standard Getter
public string GetWtsPricePctIndicator()
{
    return _WtsPricePctIndicator;
}

// Standard Setter
public void SetWtsPricePctIndicator(string value)
{
    _WtsPricePctIndicator = value;
}

// Get<>AsString()
public string GetWtsPricePctIndicatorAsString()
{
    return _WtsPricePctIndicator.PadRight(1);
}

// Set<>AsString()
public void SetWtsPricePctIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsPricePctIndicator = value;
}

// Standard Getter
public decimal GetWtsBfAccountingValue()
{
    return _WtsBfAccountingValue;
}

// Standard Setter
public void SetWtsBfAccountingValue(decimal value)
{
    _WtsBfAccountingValue = value;
}

// Get<>AsString()
public string GetWtsBfAccountingValueAsString()
{
    return _WtsBfAccountingValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtsBfAccountingValueAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsBfAccountingValue = parsed;
}

// Standard Getter
public decimal GetWtsAccountingValueYtd()
{
    return _WtsAccountingValueYtd;
}

// Standard Setter
public void SetWtsAccountingValueYtd(decimal value)
{
    _WtsAccountingValueYtd = value;
}

// Get<>AsString()
public string GetWtsAccountingValueYtdAsString()
{
    return _WtsAccountingValueYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtsAccountingValueYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsAccountingValueYtd = parsed;
}

// Standard Getter
public decimal GetWtsProfitLoss()
{
    return _WtsProfitLoss;
}

// Standard Setter
public void SetWtsProfitLoss(decimal value)
{
    _WtsProfitLoss = value;
}

// Get<>AsString()
public string GetWtsProfitLossAsString()
{
    return _WtsProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtsProfitLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsProfitLoss = parsed;
}

// Standard Getter
public string GetWtsIssuersName()
{
    return _WtsIssuersName;
}

// Standard Setter
public void SetWtsIssuersName(string value)
{
    _WtsIssuersName = value;
}

// Get<>AsString()
public string GetWtsIssuersNameAsString()
{
    return _WtsIssuersName.PadRight(35);
}

// Set<>AsString()
public void SetWtsIssuersNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsIssuersName = value;
}

// Standard Getter
public string GetWtsStockDescription()
{
    return _WtsStockDescription;
}

// Standard Setter
public void SetWtsStockDescription(string value)
{
    _WtsStockDescription = value;
}

// Get<>AsString()
public string GetWtsStockDescriptionAsString()
{
    return _WtsStockDescription.PadRight(40);
}

// Set<>AsString()
public void SetWtsStockDescriptionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsStockDescription = value;
}

// Standard Getter
public decimal GetWtsIssuedCapital()
{
    return _WtsIssuedCapital;
}

// Standard Setter
public void SetWtsIssuedCapital(decimal value)
{
    _WtsIssuedCapital = value;
}

// Get<>AsString()
public string GetWtsIssuedCapitalAsString()
{
    return _WtsIssuedCapital.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtsIssuedCapitalAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsIssuedCapital = parsed;
}

// Standard Getter
public string GetWtsIssuedCapitalX()
{
    return _WtsIssuedCapitalX;
}

// Standard Setter
public void SetWtsIssuedCapitalX(string value)
{
    _WtsIssuedCapitalX = value;
}

// Get<>AsString()
public string GetWtsIssuedCapitalXAsString()
{
    return _WtsIssuedCapitalX.PadRight(8);
}

// Set<>AsString()
public void SetWtsIssuedCapitalXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsIssuedCapitalX = value;
}

// Standard Getter
public int GetWtsMovementIndicator()
{
    return _WtsMovementIndicator;
}

// Standard Setter
public void SetWtsMovementIndicator(int value)
{
    _WtsMovementIndicator = value;
}

// Get<>AsString()
public string GetWtsMovementIndicatorAsString()
{
    return _WtsMovementIndicator.ToString().PadLeft(1, '0');
}

// Set<>AsString()
public void SetWtsMovementIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtsMovementIndicator = parsed;
}

// Standard Getter
public int GetWtsBf2PctHoldingDate()
{
    return _WtsBf2PctHoldingDate;
}

// Standard Setter
public void SetWtsBf2PctHoldingDate(int value)
{
    _WtsBf2PctHoldingDate = value;
}

// Get<>AsString()
public string GetWtsBf2PctHoldingDateAsString()
{
    return _WtsBf2PctHoldingDate.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetWtsBf2PctHoldingDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtsBf2PctHoldingDate = parsed;
}

// Standard Getter
public int GetWts2PctHoldingDateYtd()
{
    return _Wts2PctHoldingDateYtd;
}

// Standard Setter
public void SetWts2PctHoldingDateYtd(int value)
{
    _Wts2PctHoldingDateYtd = value;
}

// Get<>AsString()
public string GetWts2PctHoldingDateYtdAsString()
{
    return _Wts2PctHoldingDateYtd.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetWts2PctHoldingDateYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Wts2PctHoldingDateYtd = parsed;
}

// Standard Getter
public string GetWtsSecurityIndicator()
{
    return _WtsSecurityIndicator;
}

// Standard Setter
public void SetWtsSecurityIndicator(string value)
{
    _WtsSecurityIndicator = value;
}

// Get<>AsString()
public string GetWtsSecurityIndicatorAsString()
{
    return _WtsSecurityIndicator.PadRight(1);
}

// Set<>AsString()
public void SetWtsSecurityIndicatorAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsSecurityIndicator = value;
}

// Standard Getter
public decimal GetWtsTotalUnitsYtd()
{
    return _WtsTotalUnitsYtd;
}

// Standard Setter
public void SetWtsTotalUnitsYtd(decimal value)
{
    _WtsTotalUnitsYtd = value;
}

// Get<>AsString()
public string GetWtsTotalUnitsYtdAsString()
{
    return _WtsTotalUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtsTotalUnitsYtdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsTotalUnitsYtd = parsed;
}

// Standard Getter
public decimal GetWtsCapitalGainLoss()
{
    return _WtsCapitalGainLoss;
}

// Standard Setter
public void SetWtsCapitalGainLoss(decimal value)
{
    _WtsCapitalGainLoss = value;
}

// Get<>AsString()
public string GetWtsCapitalGainLossAsString()
{
    return _WtsCapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtsCapitalGainLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsCapitalGainLoss = parsed;
}

// Standard Getter
public decimal GetWtsUnrealGainLoss()
{
    return _WtsUnrealGainLoss;
}

// Standard Setter
public void SetWtsUnrealGainLoss(decimal value)
{
    _WtsUnrealGainLoss = value;
}

// Get<>AsString()
public string GetWtsUnrealGainLossAsString()
{
    return _WtsUnrealGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtsUnrealGainLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsUnrealGainLoss = parsed;
}

// Standard Getter
public decimal GetWtsUnrealProfitLoss()
{
    return _WtsUnrealProfitLoss;
}

// Standard Setter
public void SetWtsUnrealProfitLoss(decimal value)
{
    _WtsUnrealProfitLoss = value;
}

// Get<>AsString()
public string GetWtsUnrealProfitLossAsString()
{
    return _WtsUnrealProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtsUnrealProfitLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsUnrealProfitLoss = parsed;
}

// Standard Getter
public string GetWtsFlagA()
{
    return _WtsFlagA;
}

// Standard Setter
public void SetWtsFlagA(string value)
{
    _WtsFlagA = value;
}

// Get<>AsString()
public string GetWtsFlagAAsString()
{
    return _WtsFlagA.PadRight(1);
}

// Set<>AsString()
public void SetWtsFlagAAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsFlagA = value;
}

// Standard Getter
public string GetWtsFlagB()
{
    return _WtsFlagB;
}

// Standard Setter
public void SetWtsFlagB(string value)
{
    _WtsFlagB = value;
}

// Get<>AsString()
public string GetWtsFlagBAsString()
{
    return _WtsFlagB.PadRight(1);
}

// Set<>AsString()
public void SetWtsFlagBAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsFlagB = value;
}

// Standard Getter
public int GetWtsDateOfIssue()
{
    return _WtsDateOfIssue;
}

// Standard Setter
public void SetWtsDateOfIssue(int value)
{
    _WtsDateOfIssue = value;
}

// Get<>AsString()
public string GetWtsDateOfIssueAsString()
{
    return _WtsDateOfIssue.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetWtsDateOfIssueAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtsDateOfIssue = parsed;
}

// Standard Getter
public string GetFiller457()
{
    return _Filler457;
}

// Standard Setter
public void SetFiller457(string value)
{
    _Filler457 = value;
}

// Get<>AsString()
public string GetFiller457AsString()
{
    return _Filler457.PadRight(6);
}

// Set<>AsString()
public void SetFiller457AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler457 = value;
}

// Standard Getter
public string GetWtsIndexFromIssue()
{
    return _WtsIndexFromIssue;
}

// Standard Setter
public void SetWtsIndexFromIssue(string value)
{
    _WtsIndexFromIssue = value;
}

// Get<>AsString()
public string GetWtsIndexFromIssueAsString()
{
    return _WtsIndexFromIssue.PadRight(1);
}

// Set<>AsString()
public void SetWtsIndexFromIssueAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsIndexFromIssue = value;
}

// Standard Getter
public decimal GetWtsDeemedGainLoss()
{
    return _WtsDeemedGainLoss;
}

// Standard Setter
public void SetWtsDeemedGainLoss(decimal value)
{
    _WtsDeemedGainLoss = value;
}

// Get<>AsString()
public string GetWtsDeemedGainLossAsString()
{
    return _WtsDeemedGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtsDeemedGainLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsDeemedGainLoss = parsed;
}

// Standard Getter
public decimal GetWtsDeemedProfitLoss()
{
    return _WtsDeemedProfitLoss;
}

// Standard Setter
public void SetWtsDeemedProfitLoss(decimal value)
{
    _WtsDeemedProfitLoss = value;
}

// Get<>AsString()
public string GetWtsDeemedProfitLossAsString()
{
    return _WtsDeemedProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtsDeemedProfitLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsDeemedProfitLoss = parsed;
}

// Standard Getter
public string GetWtsHoldingFlag()
{
    return _WtsHoldingFlag;
}

// Standard Setter
public void SetWtsHoldingFlag(string value)
{
    _WtsHoldingFlag = value;
}

// Get<>AsString()
public string GetWtsHoldingFlagAsString()
{
    return _WtsHoldingFlag.PadRight(1);
}

// Set<>AsString()
public void SetWtsHoldingFlagAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsHoldingFlag = value;
}

// Standard Getter
public string GetWtsBondOverride()
{
    return _WtsBondOverride;
}

// Standard Setter
public void SetWtsBondOverride(string value)
{
    _WtsBondOverride = value;
}

// Get<>AsString()
public string GetWtsBondOverrideAsString()
{
    return _WtsBondOverride.PadRight(1);
}

// Set<>AsString()
public void SetWtsBondOverrideAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsBondOverride = value;
}

// Standard Getter
public string GetWtsLrBasis()
{
    return _WtsLrBasis;
}

// Standard Setter
public void SetWtsLrBasis(string value)
{
    _WtsLrBasis = value;
}

// Get<>AsString()
public string GetWtsLrBasisAsString()
{
    return _WtsLrBasis.PadRight(1);
}

// Set<>AsString()
public void SetWtsLrBasisAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsLrBasis = value;
}

// Standard Getter
public string GetWtsBondMaturityDate()
{
    return _WtsBondMaturityDate;
}

// Standard Setter
public void SetWtsBondMaturityDate(string value)
{
    _WtsBondMaturityDate = value;
}

// Get<>AsString()
public string GetWtsBondMaturityDateAsString()
{
    return _WtsBondMaturityDate.PadRight(8);
}

// Set<>AsString()
public void SetWtsBondMaturityDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsBondMaturityDate = value;
}

// Standard Getter
public Filler458 GetFiller458()
{
    return _Filler458;
}

// Standard Setter
public void SetFiller458(Filler458 value)
{
    _Filler458 = value;
}

// Get<>AsString()
public string GetFiller458AsString()
{
    return _Filler458 != null ? _Filler458.GetFiller458AsString() : "";
}

// Set<>AsString()
public void SetFiller458AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler458 == null)
    {
        _Filler458 = new Filler458();
    }
    _Filler458.SetFiller458AsString(value);
}

// Standard Getter
public string GetWtsOptionExpiryDate()
{
    return _WtsOptionExpiryDate;
}

// Standard Setter
public void SetWtsOptionExpiryDate(string value)
{
    _WtsOptionExpiryDate = value;
}

// Get<>AsString()
public string GetWtsOptionExpiryDateAsString()
{
    return _WtsOptionExpiryDate.PadRight(8);
}

// Set<>AsString()
public void SetWtsOptionExpiryDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsOptionExpiryDate = value;
}

// Standard Getter
public string GetWtsBondParValueX()
{
    return _WtsBondParValueX;
}

// Standard Setter
public void SetWtsBondParValueX(string value)
{
    _WtsBondParValueX = value;
}

// Get<>AsString()
public string GetWtsBondParValueXAsString()
{
    return _WtsBondParValueX.PadRight(6);
}

// Set<>AsString()
public void SetWtsBondParValueXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsBondParValueX = value;
}

// Standard Getter
public decimal GetWtsBondParValue()
{
    return _WtsBondParValue;
}

// Standard Setter
public void SetWtsBondParValue(decimal value)
{
    _WtsBondParValue = value;
}

// Get<>AsString()
public string GetWtsBondParValueAsString()
{
    return _WtsBondParValue.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtsBondParValueAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsBondParValue = parsed;
}

// Standard Getter
public string GetWtsBondGainOverride()
{
    return _WtsBondGainOverride;
}

// Standard Setter
public void SetWtsBondGainOverride(string value)
{
    _WtsBondGainOverride = value;
}

// Get<>AsString()
public string GetWtsBondGainOverrideAsString()
{
    return _WtsBondGainOverride.PadRight(1);
}

// Set<>AsString()
public void SetWtsBondGainOverrideAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsBondGainOverride = value;
}

// Standard Getter
public decimal GetWtsUnrealBondGainLoss()
{
    return _WtsUnrealBondGainLoss;
}

// Standard Setter
public void SetWtsUnrealBondGainLoss(decimal value)
{
    _WtsUnrealBondGainLoss = value;
}

// Get<>AsString()
public string GetWtsUnrealBondGainLossAsString()
{
    return _WtsUnrealBondGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtsUnrealBondGainLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsUnrealBondGainLoss = parsed;
}

// Standard Getter
public decimal GetWtsUnrealBondProfitLoss()
{
    return _WtsUnrealBondProfitLoss;
}

// Standard Setter
public void SetWtsUnrealBondProfitLoss(decimal value)
{
    _WtsUnrealBondProfitLoss = value;
}

// Get<>AsString()
public string GetWtsUnrealBondProfitLossAsString()
{
    return _WtsUnrealBondProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtsUnrealBondProfitLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsUnrealBondProfitLoss = parsed;
}

// Standard Getter
public string GetWtsAssetUsage()
{
    return _WtsAssetUsage;
}

// Standard Setter
public void SetWtsAssetUsage(string value)
{
    _WtsAssetUsage = value;
}

// Get<>AsString()
public string GetWtsAssetUsageAsString()
{
    return _WtsAssetUsage.PadRight(0);
}

// Set<>AsString()
public void SetWtsAssetUsageAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsAssetUsage = value;
}

// Standard Getter
public decimal GetWtsRealBondGainLoss()
{
    return _WtsRealBondGainLoss;
}

// Standard Setter
public void SetWtsRealBondGainLoss(decimal value)
{
    _WtsRealBondGainLoss = value;
}

// Get<>AsString()
public string GetWtsRealBondGainLossAsString()
{
    return _WtsRealBondGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtsRealBondGainLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsRealBondGainLoss = parsed;
}

// Standard Getter
public decimal GetWtsRealBondProfitLoss()
{
    return _WtsRealBondProfitLoss;
}

// Standard Setter
public void SetWtsRealBondProfitLoss(decimal value)
{
    _WtsRealBondProfitLoss = value;
}

// Get<>AsString()
public string GetWtsRealBondProfitLossAsString()
{
    return _WtsRealBondProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
}

// Set<>AsString()
public void SetWtsRealBondProfitLossAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    decimal parsed;
    if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtsRealBondProfitLoss = parsed;
}

// Standard Getter
public string GetFiller460()
{
    return _Filler460;
}

// Standard Setter
public void SetFiller460(string value)
{
    _Filler460 = value;
}

// Get<>AsString()
public string GetFiller460AsString()
{
    return _Filler460.PadRight(13);
}

// Set<>AsString()
public void SetFiller460AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler460 = value;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WtsKey
public class WtsKey
{
    private static int _size = 25;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtsCalSedol, is_external=, is_static_class=False, static_prefix=
    private WtsKey.WtsCalSedol _WtsCalSedol = new WtsKey.WtsCalSedol();
    
    
    
    
    // [DEBUG] Field: WtsContractNo, is_external=, is_static_class=False, static_prefix=
    private string _WtsContractNo ="";
    
    
    
    
    // [DEBUG] Field: WtsRecordCode, is_external=, is_static_class=False, static_prefix=
    private int _WtsRecordCode =0;
    
    
    
    
    // [DEBUG] Field: WtsRecordCodeX, is_external=, is_static_class=False, static_prefix=
    private WtsKey.WtsRecordCodeX _WtsRecordCodeX = new WtsKey.WtsRecordCodeX();
    
    
    
    
public WtsKey() {}

public WtsKey(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    _WtsCalSedol.SetWtsCalSedolAsString(data.Substring(offset, WtsCalSedol.GetSize()));
    offset += 11;
    SetWtsContractNo(data.Substring(offset, 10).Trim());
    offset += 10;
    SetWtsRecordCode(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    _WtsRecordCodeX.SetWtsRecordCodeXAsString(data.Substring(offset, WtsRecordCodeX.GetSize()));
    offset += 2;
    
}

// Serialization methods
public string GetWtsKeyAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtsCalSedol.GetWtsCalSedolAsString());
    result.Append(_WtsContractNo.PadRight(10));
    result.Append(_WtsRecordCode.ToString().PadLeft(2, '0'));
    result.Append(_WtsRecordCodeX.GetWtsRecordCodeXAsString());
    
    return result.ToString();
}

public void SetWtsKeyAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 11 <= data.Length)
    {
        _WtsCalSedol.SetWtsCalSedolAsString(data.Substring(offset, 11));
    }
    else
    {
        _WtsCalSedol.SetWtsCalSedolAsString(data.Substring(offset));
    }
    offset += 11;
    if (offset + 10 <= data.Length)
    {
        string extracted = data.Substring(offset, 10).Trim();
        SetWtsContractNo(extracted);
    }
    offset += 10;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtsRecordCode(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        _WtsRecordCodeX.SetWtsRecordCodeXAsString(data.Substring(offset, 2));
    }
    else
    {
        _WtsRecordCodeX.SetWtsRecordCodeXAsString(data.Substring(offset));
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public WtsCalSedol GetWtsCalSedol()
{
    return _WtsCalSedol;
}

// Standard Setter
public void SetWtsCalSedol(WtsCalSedol value)
{
    _WtsCalSedol = value;
}

// Get<>AsString()
public string GetWtsCalSedolAsString()
{
    return _WtsCalSedol != null ? _WtsCalSedol.GetWtsCalSedolAsString() : "";
}

// Set<>AsString()
public void SetWtsCalSedolAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtsCalSedol == null)
    {
        _WtsCalSedol = new WtsCalSedol();
    }
    _WtsCalSedol.SetWtsCalSedolAsString(value);
}

// Standard Getter
public string GetWtsContractNo()
{
    return _WtsContractNo;
}

// Standard Setter
public void SetWtsContractNo(string value)
{
    _WtsContractNo = value;
}

// Get<>AsString()
public string GetWtsContractNoAsString()
{
    return _WtsContractNo.PadRight(10);
}

// Set<>AsString()
public void SetWtsContractNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsContractNo = value;
}

// Standard Getter
public int GetWtsRecordCode()
{
    return _WtsRecordCode;
}

// Standard Setter
public void SetWtsRecordCode(int value)
{
    _WtsRecordCode = value;
}

// Get<>AsString()
public string GetWtsRecordCodeAsString()
{
    return _WtsRecordCode.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWtsRecordCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtsRecordCode = parsed;
}

// Standard Getter
public WtsRecordCodeX GetWtsRecordCodeX()
{
    return _WtsRecordCodeX;
}

// Standard Setter
public void SetWtsRecordCodeX(WtsRecordCodeX value)
{
    _WtsRecordCodeX = value;
}

// Get<>AsString()
public string GetWtsRecordCodeXAsString()
{
    return _WtsRecordCodeX != null ? _WtsRecordCodeX.GetWtsRecordCodeXAsString() : "";
}

// Set<>AsString()
public void SetWtsRecordCodeXAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtsRecordCodeX == null)
    {
        _WtsRecordCodeX = new WtsRecordCodeX();
    }
    _WtsRecordCodeX.SetWtsRecordCodeXAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WtsCalSedol
public class WtsCalSedol
{
    private static int _size = 11;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtsCoAcLk, is_external=, is_static_class=False, static_prefix=
    private string _WtsCoAcLk ="";
    
    
    
    
    // [DEBUG] Field: WtsSedol, is_external=, is_static_class=False, static_prefix=
    private string _WtsSedol ="";
    
    
    
    
public WtsCalSedol() {}

public WtsCalSedol(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtsCoAcLk(data.Substring(offset, 4).Trim());
    offset += 4;
    SetWtsSedol(data.Substring(offset, 7).Trim());
    offset += 7;
    
}

// Serialization methods
public string GetWtsCalSedolAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtsCoAcLk.PadRight(4));
    result.Append(_WtsSedol.PadRight(7));
    
    return result.ToString();
}

public void SetWtsCalSedolAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetWtsCoAcLk(extracted);
    }
    offset += 4;
    if (offset + 7 <= data.Length)
    {
        string extracted = data.Substring(offset, 7).Trim();
        SetWtsSedol(extracted);
    }
    offset += 7;
}

// Getter and Setter methods

// Standard Getter
public string GetWtsCoAcLk()
{
    return _WtsCoAcLk;
}

// Standard Setter
public void SetWtsCoAcLk(string value)
{
    _WtsCoAcLk = value;
}

// Get<>AsString()
public string GetWtsCoAcLkAsString()
{
    return _WtsCoAcLk.PadRight(4);
}

// Set<>AsString()
public void SetWtsCoAcLkAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsCoAcLk = value;
}

// Standard Getter
public string GetWtsSedol()
{
    return _WtsSedol;
}

// Standard Setter
public void SetWtsSedol(string value)
{
    _WtsSedol = value;
}

// Get<>AsString()
public string GetWtsSedolAsString()
{
    return _WtsSedol.PadRight(7);
}

// Set<>AsString()
public void SetWtsSedolAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsSedol = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WtsRecordCodeX
public class WtsRecordCodeX
{
    private static int _size = 2;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtsRecordCodeX1, is_external=, is_static_class=False, static_prefix=
    private string _WtsRecordCodeX1 ="";
    
    
    
    
    // [DEBUG] Field: WtsRecordCodeX2, is_external=, is_static_class=False, static_prefix=
    private string _WtsRecordCodeX2 ="";
    
    
    
    
public WtsRecordCodeX() {}

public WtsRecordCodeX(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtsRecordCodeX1(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWtsRecordCodeX2(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetWtsRecordCodeXAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtsRecordCodeX1.PadRight(1));
    result.Append(_WtsRecordCodeX2.PadRight(1));
    
    return result.ToString();
}

public void SetWtsRecordCodeXAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtsRecordCodeX1(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtsRecordCodeX2(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetWtsRecordCodeX1()
{
    return _WtsRecordCodeX1;
}

// Standard Setter
public void SetWtsRecordCodeX1(string value)
{
    _WtsRecordCodeX1 = value;
}

// Get<>AsString()
public string GetWtsRecordCodeX1AsString()
{
    return _WtsRecordCodeX1.PadRight(1);
}

// Set<>AsString()
public void SetWtsRecordCodeX1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsRecordCodeX1 = value;
}

// Standard Getter
public string GetWtsRecordCodeX2()
{
    return _WtsRecordCodeX2;
}

// Standard Setter
public void SetWtsRecordCodeX2(string value)
{
    _WtsRecordCodeX2 = value;
}

// Get<>AsString()
public string GetWtsRecordCodeX2AsString()
{
    return _WtsRecordCodeX2.PadRight(1);
}

// Set<>AsString()
public void SetWtsRecordCodeX2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsRecordCodeX2 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Nested Class: WtsDateTimeStamp
public class WtsDateTimeStamp
{
    private static int _size = 14;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtsDateStamp, is_external=, is_static_class=False, static_prefix=
    private string _WtsDateStamp ="";
    
    
    
    
    // [DEBUG] Field: WtsTimeStamp, is_external=, is_static_class=False, static_prefix=
    private string _WtsTimeStamp ="";
    
    
    
    
public WtsDateTimeStamp() {}

public WtsDateTimeStamp(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtsDateStamp(data.Substring(offset, 6).Trim());
    offset += 6;
    SetWtsTimeStamp(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetWtsDateTimeStampAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtsDateStamp.PadRight(6));
    result.Append(_WtsTimeStamp.PadRight(8));
    
    return result.ToString();
}

public void SetWtsDateTimeStampAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetWtsDateStamp(extracted);
    }
    offset += 6;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetWtsTimeStamp(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetWtsDateStamp()
{
    return _WtsDateStamp;
}

// Standard Setter
public void SetWtsDateStamp(string value)
{
    _WtsDateStamp = value;
}

// Get<>AsString()
public string GetWtsDateStampAsString()
{
    return _WtsDateStamp.PadRight(6);
}

// Set<>AsString()
public void SetWtsDateStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsDateStamp = value;
}

// Standard Getter
public string GetWtsTimeStamp()
{
    return _WtsTimeStamp;
}

// Standard Setter
public void SetWtsTimeStamp(string value)
{
    _WtsTimeStamp = value;
}

// Get<>AsString()
public string GetWtsTimeStampAsString()
{
    return _WtsTimeStamp.PadRight(8);
}

// Set<>AsString()
public void SetWtsTimeStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsTimeStamp = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler455
public class Filler455
{
    private static int _size = 14;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtsStamp, is_external=, is_static_class=False, static_prefix=
    private string _WtsStamp ="";
    
    
    
    
    // [DEBUG] Field: WtsPartly, is_external=, is_static_class=False, static_prefix=
    private string _WtsPartly ="";
    
    
    
    
public Filler455() {}

public Filler455(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtsStamp(data.Substring(offset, 13).Trim());
    offset += 13;
    SetWtsPartly(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetFiller455AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtsStamp.PadRight(13));
    result.Append(_WtsPartly.PadRight(1));
    
    return result.ToString();
}

public void SetFiller455AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetWtsStamp(extracted);
    }
    offset += 13;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtsPartly(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetWtsStamp()
{
    return _WtsStamp;
}

// Standard Setter
public void SetWtsStamp(string value)
{
    _WtsStamp = value;
}

// Get<>AsString()
public string GetWtsStampAsString()
{
    return _WtsStamp.PadRight(13);
}

// Set<>AsString()
public void SetWtsStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsStamp = value;
}

// Standard Getter
public string GetWtsPartly()
{
    return _WtsPartly;
}

// Standard Setter
public void SetWtsPartly(string value)
{
    _WtsPartly = value;
}

// Get<>AsString()
public string GetWtsPartlyAsString()
{
    return _WtsPartly.PadRight(1);
}

// Set<>AsString()
public void SetWtsPartlyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsPartly = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: WtsMainGroupCode
public class WtsMainGroupCode
{
    private static int _size = 3;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtsGroupPrefix, is_external=, is_static_class=False, static_prefix=
    private string _WtsGroupPrefix ="";
    
    
    
    
    // [DEBUG] Field: WtsGroupCode, is_external=, is_static_class=False, static_prefix=
    private string _WtsGroupCode ="";
    
    
    
    
public WtsMainGroupCode() {}

public WtsMainGroupCode(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtsGroupPrefix(data.Substring(offset, 1).Trim());
    offset += 1;
    SetWtsGroupCode(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetWtsMainGroupCodeAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtsGroupPrefix.PadRight(1));
    result.Append(_WtsGroupCode.PadRight(2));
    
    return result.ToString();
}

public void SetWtsMainGroupCodeAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtsGroupPrefix(extracted);
    }
    offset += 1;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWtsGroupCode(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetWtsGroupPrefix()
{
    return _WtsGroupPrefix;
}

// Standard Setter
public void SetWtsGroupPrefix(string value)
{
    _WtsGroupPrefix = value;
}

// Get<>AsString()
public string GetWtsGroupPrefixAsString()
{
    return _WtsGroupPrefix.PadRight(1);
}

// Set<>AsString()
public void SetWtsGroupPrefixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsGroupPrefix = value;
}

// Standard Getter
public string GetWtsGroupCode()
{
    return _WtsGroupCode;
}

// Standard Setter
public void SetWtsGroupCode(string value)
{
    _WtsGroupCode = value;
}

// Get<>AsString()
public string GetWtsGroupCodeAsString()
{
    return _WtsGroupCode.PadRight(2);
}

// Set<>AsString()
public void SetWtsGroupCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtsGroupCode = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler458
public class Filler458
{
    private static int _size = 12;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtsBondMaturityDateCcyy, is_external=, is_static_class=False, static_prefix=
    private int _WtsBondMaturityDateCcyy =0;
    
    
    
    
    // [DEBUG] Field: Filler459, is_external=, is_static_class=False, static_prefix=
    private Filler458.Filler459 _Filler459 = new Filler458.Filler459();
    
    
    
    
    // [DEBUG] Field: WtsBondMaturityDateMm, is_external=, is_static_class=False, static_prefix=
    private int _WtsBondMaturityDateMm =0;
    
    
    
    
    // [DEBUG] Field: WtsBondMaturityDateDd, is_external=, is_static_class=False, static_prefix=
    private int _WtsBondMaturityDateDd =0;
    
    
    
    
public Filler458() {}

public Filler458(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtsBondMaturityDateCcyy(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    _Filler459.SetFiller459AsString(data.Substring(offset, Filler459.GetSize()));
    offset += 4;
    SetWtsBondMaturityDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWtsBondMaturityDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller458AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtsBondMaturityDateCcyy.ToString().PadLeft(4, '0'));
    result.Append(_Filler459.GetFiller459AsString());
    result.Append(_WtsBondMaturityDateMm.ToString().PadLeft(2, '0'));
    result.Append(_WtsBondMaturityDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller458AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtsBondMaturityDateCcyy(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        _Filler459.SetFiller459AsString(data.Substring(offset, 4));
    }
    else
    {
        _Filler459.SetFiller459AsString(data.Substring(offset));
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtsBondMaturityDateMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtsBondMaturityDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWtsBondMaturityDateCcyy()
{
    return _WtsBondMaturityDateCcyy;
}

// Standard Setter
public void SetWtsBondMaturityDateCcyy(int value)
{
    _WtsBondMaturityDateCcyy = value;
}

// Get<>AsString()
public string GetWtsBondMaturityDateCcyyAsString()
{
    return _WtsBondMaturityDateCcyy.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetWtsBondMaturityDateCcyyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtsBondMaturityDateCcyy = parsed;
}

// Standard Getter
public Filler459 GetFiller459()
{
    return _Filler459;
}

// Standard Setter
public void SetFiller459(Filler459 value)
{
    _Filler459 = value;
}

// Get<>AsString()
public string GetFiller459AsString()
{
    return _Filler459 != null ? _Filler459.GetFiller459AsString() : "";
}

// Set<>AsString()
public void SetFiller459AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_Filler459 == null)
    {
        _Filler459 = new Filler459();
    }
    _Filler459.SetFiller459AsString(value);
}

// Standard Getter
public int GetWtsBondMaturityDateMm()
{
    return _WtsBondMaturityDateMm;
}

// Standard Setter
public void SetWtsBondMaturityDateMm(int value)
{
    _WtsBondMaturityDateMm = value;
}

// Get<>AsString()
public string GetWtsBondMaturityDateMmAsString()
{
    return _WtsBondMaturityDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWtsBondMaturityDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtsBondMaturityDateMm = parsed;
}

// Standard Getter
public int GetWtsBondMaturityDateDd()
{
    return _WtsBondMaturityDateDd;
}

// Standard Setter
public void SetWtsBondMaturityDateDd(int value)
{
    _WtsBondMaturityDateDd = value;
}

// Get<>AsString()
public string GetWtsBondMaturityDateDdAsString()
{
    return _WtsBondMaturityDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWtsBondMaturityDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtsBondMaturityDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

// Nested Class: Filler459
public class Filler459
{
    private static int _size = 4;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtsBondMaturityDateCc, is_external=, is_static_class=False, static_prefix=
    private int _WtsBondMaturityDateCc =0;
    
    
    
    
    // [DEBUG] Field: WtsBondMaturityDateYy, is_external=, is_static_class=False, static_prefix=
    private int _WtsBondMaturityDateYy =0;
    
    
    
    
public Filler459() {}

public Filler459(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtsBondMaturityDateCc(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetWtsBondMaturityDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller459AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtsBondMaturityDateCc.ToString().PadLeft(2, '0'));
    result.Append(_WtsBondMaturityDateYy.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller459AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtsBondMaturityDateCc(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetWtsBondMaturityDateYy(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetWtsBondMaturityDateCc()
{
    return _WtsBondMaturityDateCc;
}

// Standard Setter
public void SetWtsBondMaturityDateCc(int value)
{
    _WtsBondMaturityDateCc = value;
}

// Get<>AsString()
public string GetWtsBondMaturityDateCcAsString()
{
    return _WtsBondMaturityDateCc.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWtsBondMaturityDateCcAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtsBondMaturityDateCc = parsed;
}

// Standard Getter
public int GetWtsBondMaturityDateYy()
{
    return _WtsBondMaturityDateYy;
}

// Standard Setter
public void SetWtsBondMaturityDateYy(int value)
{
    _WtsBondMaturityDateYy = value;
}

// Get<>AsString()
public string GetWtsBondMaturityDateYyAsString()
{
    return _WtsBondMaturityDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetWtsBondMaturityDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _WtsBondMaturityDateYy = parsed;
}



public static int GetSize()
{
    return _size;
}

}
}
}
}
}

}}
