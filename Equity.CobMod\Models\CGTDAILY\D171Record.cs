using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtdailyDTO
{// DTO class representing D171Record Data Structure

public class D171Record
{
    private static int _size = 66;
    // [DEBUG] Class: D171Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler1, is_external=, is_static_class=False, static_prefix=
    private string _Filler1 ="";
    
    
    
    
    // [DEBUG] Field: D171FundCode, is_external=, is_static_class=False, static_prefix=
    private string _D171FundCode ="";
    
    
    
    
    // [DEBUG] Field: Filler2, is_external=, is_static_class=False, static_prefix=
    private string _Filler2 ="";
    
    
    
    
    // [DEBUG] Field: D171SedolCode, is_external=, is_static_class=False, static_prefix=
    private string _D171SedolCode ="";
    
    
    
    
    // [DEBUG] Field: Filler3, is_external=, is_static_class=False, static_prefix=
    private string _Filler3 ="";
    
    
    
    
    // [DEBUG] Field: D171Quantity, is_external=, is_static_class=False, static_prefix=
    private decimal _D171Quantity =0;
    
    
    
    
    // [DEBUG] Field: Filler4, is_external=, is_static_class=False, static_prefix=
    private string _Filler4 ="";
    
    
    
    
    // [DEBUG] Field: D171BargainDate, is_external=, is_static_class=False, static_prefix=
    private string _D171BargainDate ="";
    
    
    
    
    // [DEBUG] Field: Filler5, is_external=, is_static_class=False, static_prefix=
    private string _Filler5 ="";
    
    
    
    
    // [DEBUG] Field: D171SettlementDate, is_external=, is_static_class=False, static_prefix=
    private string _D171SettlementDate ="";
    
    
    
    
    // [DEBUG] Field: Filler6, is_external=, is_static_class=False, static_prefix=
    private string _Filler6 ="";
    
    
    
    
    // [DEBUG] Field: D171TransactionCategory, is_external=, is_static_class=False, static_prefix=
    private string _D171TransactionCategory ="";
    
    
    
    
    // [DEBUG] Field: Filler7, is_external=, is_static_class=False, static_prefix=
    private string _Filler7 ="";
    
    
    
    
    // [DEBUG] Field: D171Value, is_external=, is_static_class=False, static_prefix=
    private decimal _D171Value =0;
    
    
    
    
    // [DEBUG] Field: Filler8, is_external=, is_static_class=False, static_prefix=
    private string _Filler8 ="";
    
    
    
    
    // [DEBUG] Field: D171ContractNumber, is_external=, is_static_class=False, static_prefix=
    private string _D171ContractNumber ="";
    
    
    
    
    // [DEBUG] Field: Filler9, is_external=, is_static_class=False, static_prefix=
    private string _Filler9 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD171RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler1.PadRight(0));
        result.Append(_D171FundCode.PadRight(0));
        result.Append(_Filler2.PadRight(0));
        result.Append(_D171SedolCode.PadRight(0));
        result.Append(_Filler3.PadRight(0));
        result.Append(_D171Quantity.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler4.PadRight(0));
        result.Append(_D171BargainDate.PadRight(10));
        result.Append(_Filler5.PadRight(0));
        result.Append(_D171SettlementDate.PadRight(10));
        result.Append(_Filler6.PadRight(0));
        result.Append(_D171TransactionCategory.PadRight(0));
        result.Append(_Filler7.PadRight(0));
        result.Append(_D171Value.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler8.PadRight(0));
        result.Append(_D171ContractNumber.PadRight(10));
        result.Append(_Filler9.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD171RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller1(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD171FundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller2(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD171SedolCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller3(extracted);
        }
        offset += 0;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD171Quantity(parsedDec);
        }
        offset += 18;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller4(extracted);
        }
        offset += 0;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetD171BargainDate(extracted);
        }
        offset += 10;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller5(extracted);
        }
        offset += 0;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetD171SettlementDate(extracted);
        }
        offset += 10;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller6(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD171TransactionCategory(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller7(extracted);
        }
        offset += 0;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD171Value(parsedDec);
        }
        offset += 18;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller8(extracted);
        }
        offset += 0;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetD171ContractNumber(extracted);
        }
        offset += 10;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller9(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD171RecordAsString();
    }
    // Set<>String Override function
    public void SetD171Record(string value)
    {
        SetD171RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller1()
    {
        return _Filler1;
    }
    
    // Standard Setter
    public void SetFiller1(string value)
    {
        _Filler1 = value;
    }
    
    // Get<>AsString()
    public string GetFiller1AsString()
    {
        return _Filler1.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler1 = value;
    }
    
    // Standard Getter
    public string GetD171FundCode()
    {
        return _D171FundCode;
    }
    
    // Standard Setter
    public void SetD171FundCode(string value)
    {
        _D171FundCode = value;
    }
    
    // Get<>AsString()
    public string GetD171FundCodeAsString()
    {
        return _D171FundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD171FundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D171FundCode = value;
    }
    
    // Standard Getter
    public string GetFiller2()
    {
        return _Filler2;
    }
    
    // Standard Setter
    public void SetFiller2(string value)
    {
        _Filler2 = value;
    }
    
    // Get<>AsString()
    public string GetFiller2AsString()
    {
        return _Filler2.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler2 = value;
    }
    
    // Standard Getter
    public string GetD171SedolCode()
    {
        return _D171SedolCode;
    }
    
    // Standard Setter
    public void SetD171SedolCode(string value)
    {
        _D171SedolCode = value;
    }
    
    // Get<>AsString()
    public string GetD171SedolCodeAsString()
    {
        return _D171SedolCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD171SedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D171SedolCode = value;
    }
    
    // Standard Getter
    public string GetFiller3()
    {
        return _Filler3;
    }
    
    // Standard Setter
    public void SetFiller3(string value)
    {
        _Filler3 = value;
    }
    
    // Get<>AsString()
    public string GetFiller3AsString()
    {
        return _Filler3.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler3 = value;
    }
    
    // Standard Getter
    public decimal GetD171Quantity()
    {
        return _D171Quantity;
    }
    
    // Standard Setter
    public void SetD171Quantity(decimal value)
    {
        _D171Quantity = value;
    }
    
    // Get<>AsString()
    public string GetD171QuantityAsString()
    {
        return _D171Quantity.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD171QuantityAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D171Quantity = parsed;
    }
    
    // Standard Getter
    public string GetFiller4()
    {
        return _Filler4;
    }
    
    // Standard Setter
    public void SetFiller4(string value)
    {
        _Filler4 = value;
    }
    
    // Get<>AsString()
    public string GetFiller4AsString()
    {
        return _Filler4.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler4 = value;
    }
    
    // Standard Getter
    public string GetD171BargainDate()
    {
        return _D171BargainDate;
    }
    
    // Standard Setter
    public void SetD171BargainDate(string value)
    {
        _D171BargainDate = value;
    }
    
    // Get<>AsString()
    public string GetD171BargainDateAsString()
    {
        return _D171BargainDate.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetD171BargainDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D171BargainDate = value;
    }
    
    // Standard Getter
    public string GetFiller5()
    {
        return _Filler5;
    }
    
    // Standard Setter
    public void SetFiller5(string value)
    {
        _Filler5 = value;
    }
    
    // Get<>AsString()
    public string GetFiller5AsString()
    {
        return _Filler5.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler5 = value;
    }
    
    // Standard Getter
    public string GetD171SettlementDate()
    {
        return _D171SettlementDate;
    }
    
    // Standard Setter
    public void SetD171SettlementDate(string value)
    {
        _D171SettlementDate = value;
    }
    
    // Get<>AsString()
    public string GetD171SettlementDateAsString()
    {
        return _D171SettlementDate.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetD171SettlementDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D171SettlementDate = value;
    }
    
    // Standard Getter
    public string GetFiller6()
    {
        return _Filler6;
    }
    
    // Standard Setter
    public void SetFiller6(string value)
    {
        _Filler6 = value;
    }
    
    // Get<>AsString()
    public string GetFiller6AsString()
    {
        return _Filler6.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler6 = value;
    }
    
    // Standard Getter
    public string GetD171TransactionCategory()
    {
        return _D171TransactionCategory;
    }
    
    // Standard Setter
    public void SetD171TransactionCategory(string value)
    {
        _D171TransactionCategory = value;
    }
    
    // Get<>AsString()
    public string GetD171TransactionCategoryAsString()
    {
        return _D171TransactionCategory.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD171TransactionCategoryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D171TransactionCategory = value;
    }
    
    // Standard Getter
    public string GetFiller7()
    {
        return _Filler7;
    }
    
    // Standard Setter
    public void SetFiller7(string value)
    {
        _Filler7 = value;
    }
    
    // Get<>AsString()
    public string GetFiller7AsString()
    {
        return _Filler7.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler7 = value;
    }
    
    // Standard Getter
    public decimal GetD171Value()
    {
        return _D171Value;
    }
    
    // Standard Setter
    public void SetD171Value(decimal value)
    {
        _D171Value = value;
    }
    
    // Get<>AsString()
    public string GetD171ValueAsString()
    {
        return _D171Value.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD171ValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D171Value = parsed;
    }
    
    // Standard Getter
    public string GetFiller8()
    {
        return _Filler8;
    }
    
    // Standard Setter
    public void SetFiller8(string value)
    {
        _Filler8 = value;
    }
    
    // Get<>AsString()
    public string GetFiller8AsString()
    {
        return _Filler8.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler8 = value;
    }
    
    // Standard Getter
    public string GetD171ContractNumber()
    {
        return _D171ContractNumber;
    }
    
    // Standard Setter
    public void SetD171ContractNumber(string value)
    {
        _D171ContractNumber = value;
    }
    
    // Get<>AsString()
    public string GetD171ContractNumberAsString()
    {
        return _D171ContractNumber.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetD171ContractNumberAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D171ContractNumber = value;
    }
    
    // Standard Getter
    public string GetFiller9()
    {
        return _Filler9;
    }
    
    // Standard Setter
    public void SetFiller9(string value)
    {
        _Filler9 = value;
    }
    
    // Get<>AsString()
    public string GetFiller9AsString()
    {
        return _Filler9.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler9 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}