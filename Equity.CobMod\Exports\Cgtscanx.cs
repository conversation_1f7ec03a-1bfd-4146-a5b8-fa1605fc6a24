//using System;
//using System.Text;
//using EquityProject.CgtscanxDTO;
//namespace EquityProject.CgtscanxPGM
//{
//    // Cgtscanx Class Definition

//    //Cgtscanx Class Constructor
//    public class Cgtscanx
//    {
//        // Declare Cgtscanx Class private variables
//        private Fvar _fvar = new Fvar();
//        private Gvar _gvar = new Gvar();
//        private Ivar _ivar = new Ivar();

//        // Declare {program_name} Class getters setters
//        public Fvar GetFvar() { return _fvar; }
//        public Gvar GetGvar() { return _gvar; }
//        public Ivar GetIvar() { return _ivar; }

//        /// <summary>
//        /// Helper method to call external subroutines
//        /// </summary>
//        /// <param name="ivar">The Ivar parameter to pass to the subroutine</param>
//        public void CallSub(Ivar ivar)
//        {
//            // Implement subroutine call logic here
//        }


//        // Run() method
//        public void Run(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Call the main entry point
//            // No mainline procedure found, add your entry point here
//            Mainline(fvar, gvar, ivar);
//        }

//        // Methods representing paragraphs under procedure division
//        /// <summary>
//        /// Executes the mainline logic from the COBOL program.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph name: mainline
//        /// This method handles program initialization, logging, and the main processing loop.
//        /// </remarks>
//        public void Mainline(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Set up log information
//            gvar.GetCgtlogLinkageArea1().SetLLogProgram(gvar.GetWProgramName());
//            gvar.GetCgtlogLinkageArea1().SetLLogAction(Gvar.OPEN_OUTPUT);
//            gvar.GetCgtlogLinkageArea1().SetLLogFileName(gvar.GetSpaces());
//            X4Cgtlog(gvar);

//            gvar.SetWsMessageNo(gvar.GetZero());
//            gvar.GetCgtlogLinkageArea2().SetLLogMessageType("I");
//            gvar.GetCgtlogLinkageArea1().SetLLogAction(Gvar.WRITE_RECORD);
//            gvar.SetTimeStamp(gvar.GetTime());
//            gvar.SetDateStamp(gvar.GetDate());
//            gvar.GetWsMessages().SetWsMessDd(gvar.GetDateStamp().GetWsDd());
//            gvar.GetWsMessages().SetWsMessMm(gvar.GetDateStamp().GetWsMm());
//            gvar.GetWsMessages().SetWsMessYy(gvar.GetDateStamp().GetWsYy());
//            gvar.GetWsMessages().SetWsMessHh(gvar.GetTimeStamp().GetWsHh());
//            gvar.GetWsMessages().SetWsMessNn(gvar.GetTimeStamp().GetWsNn());
//            gvar.SetWsWhenCompiled(gvar.GetWhenCompiled());

//            string message1 = $"{gvar.GetWProgramName()}: Version {gvar.GetVersionNumber()} " +
//                             $"{gvar.GetWsWhenCompiled().GetWsCompDate()} " +
//                             $"{gvar.GetWsWhenCompiled().GetWsCompTime()}; " +
//                             $"{gvar.GetWsMessages().GetWsMessage2()}";

//            gvar.GetWsMessages().SetWsMessage1(message1);
//            gvar.GetCgtlogLinkageArea2().SetLLogMessage(gvar.GetWsMessages().GetWsMessage1());
//            X4Cgtlog(gvar);

//            AaAskFile(gvar);
//            if (gvar.GetWsprocessflag().IsQuitprocess())
//            {
//                gvar.SetReturnCode(5);
//                gvar.SetMainExit(true);
//                return;
//            }

//            AInitialise(gvar);
//            while (!gvar.GetCgtfileslinkage().GetLfilereturncode().IsSuccessful() &&
//                   !gvar.GetWsprocessflag().IsQuitprocess())
//            {
//                BReadAccumulate(gvar);
//            }

//            CEnd(gvar);
//        }
//        /// <summary>
//        /// Implements the COBOL paragraph mainExit which handles program termination.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph: EXIT PROGRAM
//        /// This method represents the equivalent C# implementation of the COBOL program exit.
//        /// </remarks>
//        /// <param name="fvar">Container for FVAR variables</param>
//        /// <param name="gvar">Container for GVAR variables</param>
//        /// <param name="ivar">Container for IVAR variables</param>
//        public void Exit(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // This method intentionally left blank as the COBOL EXIT PROGRAM statement
//            // has no equivalent direct functionality in C# - program flow control
//            // should be handled by the calling code
//        }
//        /// <summary>
//        /// Original COBOL paragraph: aaAskFile
//        /// Performs the validation operation by calling A40Validate.
//        /// </summary>
//        /// <remarks>
//        /// This method is a direct translation of the COBOL PERFORM statement.
//        /// It executes the validation logic through the A40Validate method.
//        /// </remarks>
//        /// <param name="fvar">Fvar instance containing file-related data</param>
//        /// <param name="gvar">Gvar instance containing global variables and flags</param>
//        /// <param name="ivar">Ivar instance containing input-related data</param>
//        public void AaAskFile(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Convert COBOL PERFORM A40-VALIDATE to method call A40Validate
//            A40Validate(fvar, gvar, ivar);
//        }
//        /// <summary>
//        /// A40Validate - Performs validation and initialization for CGT processing.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph: a40Validate
//        /// This method validates the input and initializes necessary files for CGT processing.
//        /// It checks for successful file operations and handles error conditions.
//        /// </remarks>
//        public void A40Validate(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Transfer user number to various linkage areas
//            gvar.GetCommonLinkage().SetLUserNo(gvar.GetGlobalVars().GetGUserNo());
//            gvar.GetCgttempLinkage().SetCgttempUserNo(gvar.GetGlobalVars().GetGUserNo());

//            // Transfer report number to various linkage areas
//            gvar.GetCgtfilesLinkage().SetLReportNo(gvar.GetGlobalVars().GetGReportNo());
//            gvar.GetCgttempLinkage().SetCgttempReportNumber(gvar.GetGlobalVars().GetGReportNo());

//            // Set up file parameters
//            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.REALISED_DATA_FILE);
//            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.OPEN_INPUT);

//            // Call file operation
//            XCallCgtfiles(fvar, gvar, ivar);

//            if (gvar.GetCgtfileslinkage().GetLfilereturncode().IsSuccessful())
//            {
//                A411FirstRecord(fvar, gvar, ivar);
//            }
//            else
//            {
//                gvar.SetReturnCode(5);
//                return;
//            }

//            // Check if CGTSCOT1 gain/loss export is needed
//            if (gvar.GetEquityglobalparms().GetGgeneralparms().GetGcgtscot1format().IsCgtscot1gainlossexport())
//            {
//                // Handle temp file operations
//                gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_DELETE_FILE);
//                XCallCgttemp(fvar, gvar, ivar);

//                gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_OPEN_IO);
//                XCallCgttemp(fvar, gvar, ivar);

//                if (string.Compare(gvar.GetCgttempLinkage().GetCgttempStatus(), "05") > 0)
//                {
//                    // Log error message and exit
//                    string errorMessage = $"ERROR {gvar.GetCgttempLinkage().GetCgttempStatus()} OPENING TEMP FILE";
//                    gvar.GetCgtlogLinkageArea2().SetLLogMessage(errorMessage);
//                    X4Cgtlog(fvar, gvar, ivar);
//                    gvar.SetReturnCode(8);
//                    return;
//                }
//            }
//        }
//        /// <summary>
//        /// A411FirstRecord - COBOL paragraph that retrieves the first record matching search criteria.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL logic:
//        /// 1. Clear the file record area
//        /// 2. Set search action to 'START-NOT-LESS-THAN'
//        /// 3. Call the file operation
//        /// 4. Process the result or handle errors
//        /// </remarks>
//        /// <param name="fvar">Function variables</param>
//        /// <param name="gvar">Global variables</param>
//        /// <param name="ivar">Instance variables</param>
//        public void A411FirstRecord(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // MOVE SPACES TO L-FILE-RECORD-AREA
//            gvar.SetLFileRecordArea(gvar.GetSpaces());

//            // MOVE START-NOT-LESS-THAN TO L-FILE-ACTION
//            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.START_NOT_LESS_THAN);

//            // PERFORM X-CALL-CGTFILES
//            XCallCgtfiles(fvar, gvar, ivar);

//            // MOVE L-FILE-RECORD-AREA TO SCHEDULE-RECORD
//            gvar.SetScheduleRecord(gvar.GetLFileRecordArea());

//            // IF SUCCESSFUL
//            if (gvar.GetCgtfileslinkage().GetLfilereturncode().IsSuccessful())
//            {
//                // NEXT SENTENCE (no action needed)
//            }
//            else
//            {
//                // MOVE 'N' TO W-VALID
//                gvar.GetFlags().SetWValid("N");

//                // MOVE CLOSE-FILE TO L-FILE-ACTION
//                gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.CLOSE_FILE);

//                // PERFORM X-CALL-CGTFILES
//                XCallCgtfiles(fvar, gvar, ivar);

//                // MOVE 5 TO RETURN-CODE
//                gvar.SetReturnCode("5");
//            }
//        }
//        /// <summary>
//        /// Original COBOL paragraph: aInitialise
//        /// 
//        /// Initializes various variables and performs file-related operations.
//        /// </summary>
//        /// <remarks>
//        /// This method handles file initialization, string building, and date formatting logic.
//        /// Includes calls to external components via PERFORM statements.
//        /// </remarks>
//        public void AInitialise(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_NEXT);

//            gvar.SetReportFile("$" + gvar.GetCommonLinkage().GetLUserNo() + "D5" +
//                gvar.GetCgtfilesLinkage().GetLReportNo() + ".REP");

//            gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);
//            gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetReportFile());
//            XCallEqtpath(gvar);

//            gvar.SetReportFileName(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

//            gvar.SetElcgmioLinkage2(Gvar.NEW_DERIVATIVE_EXPORT_FORMAT);
//            XCallMfHandlerForConfig(gvar);

//            gvar.SetWNewDerivativeExportFormat(gvar.GetWConfigItem());

//            if (gvar.GetEquityglobalparms().GetGgeneralparms().GetGcgtscot1format().IsCgtscot1realisedextract())
//            {
//                gvar.SetRealisedExportFile(iVar.GetCgt0109ALinkage().GetOutput());
//                fvar.SetGlRecord(gvar.GetWInitialRecord());

//                gvar.GetCgtlogLinkageArea2().SetLLogMessage("Creating Realised Gain Extract: " + gvar.GetReportFile());
//                X4Cgtlog(gvar);
//            }

//            gvar.GetCgtdate2LinkageDate2().SetCgtdate2Yymmdd2(
//                gvar.GetWsMessages().GetWsMessYy() + gvar.GetWsMessages().GetWsMessMm() + gvar.GetWsMessages().GetWsMessDd());

//            gvar.SetCgtdate2(gvar.GetCgtdate2LinkageDate2().GetCgtdate2Yymmdd2());

//            gvar.GetWInitialRecord2().SetITodaysDate(
//                gvar.GetWsMessages().GetWsMessDd() + gvar.GetWsMessages().GetWsMessMm() +
//                gvar.GetCgtdate2LinkageDate2().GetCgtdate2CCc2() + gvar.GetWsMessages().GetWsMessYy());
//        }
//        /// <summary>
//        /// Original COBOL paragraph: bReadAccumulate
//        /// Reads and processes accumulated schedule records from the file.
//        /// </summary>
//        /// <remarks>
//        /// This method reads the next record from the file and processes it based on record type and line number.
//        /// It handles different record types (1, 2) and performs specific processing based on various indicators.
//        /// </remarks>
//        public void BReadAccumulate(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Set file action to READ_NEXT and call the file handler
//            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_NEXT);
//            XCallCgtfiles(gvar);

//            if (gvar.GetCgtfileslinkage().GetLfilereturncode().IsSuccessful())
//            {
//                // Copy file record to schedule record and test OL/SP
//                gvar.SetScheduleRecord(gvar.GetLFileRecordArea());
//                B4TestOlSp(gvar);

//                // Process based on record type
//                switch (gvar.GetScheduleRecord().GetSRecordType())
//                {
//                    case "1":
//                        B1NextFundSedol(gvar);
//                        break;
//                    case "2":
//                        // Process line number specific logic for type 2 records
//                        switch (gvar.GetScheduleRecord().GetSLineNumber())
//                        {
//                            case "00000":
//                                // No action needed for line number 00000
//                                break;
//                            default:
//                                // Process contract number and tranche flag if present
//                                if (gvar.GetScheduleRecord().GetSDisposalContractNo() != gvar.GetSpaces())
//                                {
//                                    fvar.GetGlRecord().SetGlDisposalContractNo(gvar.GetScheduleRecord().GetSDisposalContractNo());
//                                }

//                                if (gvar.GetScheduleRecord().GetSTrancheFlag() == "Y")
//                                {
//                                    fvar.GetGlRecord().SetGlTrancheFlag("Y");
//                                }

//                                // Process BDV line indicators
//                                switch (gvar.GetScheduleRecord().GetSBdvLineIndicator())
//                                {
//                                    case " ":
//                                        if (gvar.GetScheduleRecord().GetSIndexationLimit() == "A")
//                                        {
//                                            B51982Indexation(fvar, gvar);
//                                        }
//                                        else
//                                        {
//                                            B6Indexation(fvar, gvar);
//                                        }
//                                        break;
//                                    case "0":
//                                        B9DeemedProceeds(fvar, gvar);
//                                        break;
//                                    case "1":
//                                        B7StockHeader(gvar);
//                                        break;
//                                    case "3":
//                                        B11PriorGain(fvar, gvar);
//                                        break;
//                                    case "4":
//                                        B10Adjustments(fvar, gvar);
//                                        break;
//                                    case "5":
//                                        B12PriorIndex(fvar, gvar);
//                                        break;
//                                    case "6":
//                                        B9DeemedProceeds(fvar, gvar);
//                                        break;
//                                    case "7":
//                                        B81982Record(fvar, gvar);
//                                        break;
//                                }
//                                break;
//                        }
//                        break;
//                }
//            }
//        }
//        /// <summary>
//        /// Implements the COBOL paragraph B1-NEXT-FUND-SEDOL.
//        /// This method handles moving fund and SEDOL related data between records.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph: B1-NEXT-FUND-SEDOL
//        /// This method performs conditional writes, field mappings, and zero initialization
//        /// of various accounting fields in the GL and WS record structures.
//        /// </remarks>
//        public void B1NextFundSedol(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            if (gvar.GetWswriteflag().IsWritey())
//            {
//                B2WriteRecord(fvar, gvar, ivar);
//            }

//            if (gvar.GetScheduleRecord().GetSCoAcLk() != fvar.GetGlRecord().GetGlFund())
//            {
//                gvar.GetWsMessages().SetWsMess11Fund(gvar.GetScheduleRecord().GetSCoAcLk());
//                gvar.GetCgtlogLinkageArea2().SetLLogMessage(gvar.GetWsMessages().GetWsMessage11());
//                fvar.GetGlRecord().SetGlFund(gvar.GetScheduleRecord().GetSCoAcLk());
//            }

//            fvar.GetGlRecord().SetGlSedol(gvar.GetScheduleRecord().GetSSedolNumber());
//            gvar.GetWsTempFields().SetWsGlIndVal(gvar.GetZero());
//            gvar.GetWsTempFields().SetWsGlIndUse(gvar.GetZero());
//            gvar.GetWsTempFields().SetWsGlGainLoss9(gvar.GetZero());
//            gvar.GetWsTempFields().SetWsGlQty(gvar.GetZero());
//            gvar.GetWsTempFields().SetWsGlPrcds(gvar.GetZero());
//            gvar.GetWsTempFields().SetWsGlBook(gvar.GetZero());
//            gvar.GetWsTempFields().SetWsTempHoldingCosts(gvar.GetZero());
//            gvar.GetWsTempFields().SetWsTempIndexationCosts(gvar.GetZero());
//            fvar.GetGlRecord().SetGlMoveDate(gvar.GetZero());

//            if (gvar.GetScheduleRecord().GetSHoldingFlag() == gvar.GetEqual())
//            {
//                fvar.GetGlRecord().SetGlCgtFlag(gvar.GetSpaces());
//            }
//            else
//            {
//                fvar.GetGlRecord().SetGlCgtFlag(gvar.GetScheduleRecord().GetSHoldingFlag());
//            }

//            fvar.GetGlRecord().SetGlIndFlag(gvar.GetSpaces());
//            fvar.GetGlRecord().SetGlTrancheFlag("N");
//            fvar.GetGlRecord().SetGlDisposalContractNo(gvar.GetSpaces());

//            gvar.GetUnstring().Execute(gvar.GetScheduleRecord().GetSMainGroup(),
//                gvar.GetInto(),
//                gvar.GetWsGroupCodeChar1(),
//                fvar.GetGlRecord().GetGlGroupCode());

//            fvar.GetGlRecord().SetGlCountryCode(gvar.GetScheduleRecord().GetSCountryCode());
//        }
//        /// <summary>
//        /// Original COBOL paragraph: b2WriteRecord
//        /// Handles writing records and managing temporary storage for GL entries.
//        /// </summary>
//        /// <remarks>
//        /// Processes write flags, computes financial values, writes records to output,
//        /// and manages temporary storage for gain/loss exports.
//        /// </remarks>
//        public void B2WriteRecord(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            var wsWriteFlag = gvar.GetWsWriteFlag();
//            if (wsWriteFlag == "Y")
//            {
//                gvar.SetWsWriteFlag("N");
//                gvar.SetWsFirstValueFlag("Y");

//                if (gvar.GetFlags().GetW1982flag().IsW1982found())
//                {
//                    gvar.GetWsTempFields().SetWsGlBookS(
//                        (gvar.GetWsTempFields().GetWsOrigHoldingCost() * -1) +
//                        gvar.GetWsTempFields().GetWsTempHoldingCostsS() +
//                        gvar.GetWsTempFields().GetWsGlBookS()
//                    );

//                    gvar.GetWsTempFields().SetWsGlIndValS(
//                        (gvar.GetWsTempFields().GetWsOrigIndexCost() * -1) +
//                        gvar.GetWsTempFields().GetWsTempIndexationCostsS() +
//                        gvar.GetWsTempFields().GetWsGlIndValS()
//                    );
//                }

//                if (gvar.GetSShortWrittenDerivative())
//                {
//                    gvar.GetWsTempFields().SetWsGlIndUseS(
//                        gvar.GetWsTempFields().GetWsGlPrcdsS() -
//                        gvar.GetWsTempFields().GetWsGlGainLossS() -
//                        gvar.GetWsTempFields().GetWsGlBookS()
//                    );
//                }
//                else
//                {
//                    gvar.GetWsTempFields().SetWsGlIndUseS(
//                        gvar.GetWsTempFields().GetWsGlPrcdsS() -
//                        gvar.GetWsTempFields().GetWsGlGainLossS() -
//                        (gvar.GetWsTempFields().GetWsGlBookS() * -1)
//                    );
//                }

//                fvar.GetGlRecord().SetGlIndVal(gvar.GetWsTempFields().GetWsGlIndVal());

//                if (gvar.GetWsTempFields().GetWsGlIndUse() >= 0)
//                {
//                    fvar.GetGlRecord().SetGlIndUse(gvar.GetWsTempFields().GetWsGlIndUse());
//                }
//                else
//                {
//                    fvar.GetGlRecord().SetGlIndUse(gvar.GetZero());
//                }

//                fvar.GetGlRecord().SetGlQty(gvar.GetWsTempFields().GetWsGlQty());
//                fvar.GetGlRecord().SetGlBook(gvar.GetWsTempFields().GetWsGlBook());
//                fvar.GetGlRecord().SetGlGainLoss(gvar.GetWsTempFields().GetWsGlGainLoss9());
//                fvar.GetGlRecord().SetGlPrcds(gvar.GetWsTempFields().GetWsGlPrcds());

//                if (gvar.GetEquityglobalparms().GetGgeneralparms().GetGcgtscot1format().IsCgtscot1realisedextract())
//                {
//                    if (gvar.GetWnewderivativeexportformat().IsConfignewderivativeexportformat())
//                    {
//                        B21SetSign(fvar, gvar, ivar);
//                    }
//                    else
//                    {
//                        fvar.GetGlRecord().SetGlWithoutSign(gvar.GetSpaces());
//                    }
//                    // WRITE GL-RECORD equivalent would go here in actual implementation
//                }

//                if (gvar.GetEquityglobalparms().GetGgeneralparms().GetGcgtscot1format().IsCgtscot1gainlossexport())
//                {
//                    gvar.GetTempFields().SetTempDetails(gvar.GetSpaces());
//                    gvar.GetTempFields().SetTempKFundCode(fvar.GetGlRecord().GetGlFund());
//                    gvar.GetTempFields().SetTempKSedolCode(fvar.GetGlRecord().GetGlSedol());
//                    gvar.GetTempFields().SetTempKContractNo(fvar.GetGlRecord().GetGlDisposalContractNo());
//                    gvar.GetTempFields().SetTempQuantity(gvar.GetWsTempFields().GetWsGlQty());
//                    gvar.GetTempFields().SetTempGainLoss(gvar.GetWsTempFields().GetWsGlGainLoss9());

//                    var moveDate = fvar.GetGlRecord().GetGlMoveDate();
//                    gvar.GetTempFields().SetTempDisposalCcyy(moveDate.Substring(0, 4));
//                    gvar.GetTempFields().SetTempDisposalMm(moveDate.Substring(4, 2));
//                    gvar.GetTempFields().SetTempDisposalDd(moveDate.Substring(6, 2));

//                    var cgtFlag = fvar.GetGlRecord().GetGlCgtFlag().ToUpper();
//                    if (cgtFlag == "Y")
//                    {
//                        gvar.GetTempFields().SetTempHoldingFlag(fvar.GetGlRecord().GetGlCgtFlag());
//                    }

//                    var trancheFlag = fvar.GetGlRecord().GetGlTrancheFlag().ToUpper();
//                    if (trancheFlag == "Y")
//                    {
//                        gvar.GetTempFields().SetTempTrancheFlag(fvar.GetGlRecord().GetGlTrancheFlag());
//                    }

//                    gvar.GetTempFields().SetTempSign(fvar.GetGlRecord().GetGlSign());
//                    gvar.GetCgttempLinkage().SetCgttempKey(gvar.GetTempFields().GetTempKey());
//                    gvar.GetCgttempLinkage().SetCgttempDetails(gvar.GetTempFields().GetTempDetails());
//                    gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_WRITE);
//                    XCallCgttemp(fvar, gvar, ivar);

//                    if (gvar.GetCgttempLinkage().GetCgttempStatus() == "22")
//                    {
//                        gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_READ_ON_KEY);
//                        XCallCgttemp(fvar, gvar, ivar);
//                        gvar.GetTempFields().SetTempDetails(gvar.GetCgttempLinkage().GetCgttempDetails());

//                        gvar.GetTempFields().SetTempQuantity(
//                            gvar.GetTempFields().GetTempQuantity() + gvar.GetWsTempFields().GetWsGlQty());

//                        gvar.GetTempFields().SetTempGainLoss(
//                            gvar.GetTempFields().GetTempGainLoss() + gvar.GetWsTempFields().GetWsGlGainLoss9());

//                        if (fvar.GetGlRecord().GetGlCgtFlag() == "Y")
//                        {
//                            gvar.GetTempFields().SetTempHoldingFlag(fvar.GetGlRecord().GetGlCgtFlag());
//                        }

//                        if (fvar.GetGlRecord().GetGlTrancheFlag() == "Y")
//                        {
//                            gvar.GetTempFields().SetTempTrancheFlag(fvar.GetGlRecord().GetGlTrancheFlag());
//                        }

//                        gvar.GetCgttempLinkage().SetCgttempDetails(gvar.GetTempFields().GetTempDetails());
//                        gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_REWRITE);
//                        XCallCgttemp(fvar, gvar, ivar);
//                    }
//                }

//                // Reset working storage fields
//                gvar.GetWsTempFields().SetWsGlIndVal(gvar.GetZero());
//                gvar.GetWsTempFields().SetWsGlIndUse(gvar.GetZero());
//                gvar.GetWsTempFields().SetWsGlGainLossS(gvar.GetZero());
//                gvar.GetWsTempFields().SetWsGlQty(gvar.GetZero());
//                gvar.GetWsTempFields().SetWsGlPrcds(gvar.GetZero());
//                gvar.GetWsTempFields().SetWsGlBook(gvar.GetZero());
//                gvar.GetWsTempFields().SetWsTempHoldingCosts(gvar.GetZero());
//                gvar.GetWsTempFields().SetWsTempIndexationCosts(gvar.GetZero());
//                fvar.GetGlRecord().SetGlMoveDate(gvar.GetZero().ToString());
//                fvar.GetGlRecord().SetGlIndFlag(gvar.GetSpaces());
//                gvar.GetFlags().SetW1982Flag("N");
//            }
//        }
//        /// <summary>
//        /// B21SetSign - Converts the COBOL paragraph b21SetSign to C# method.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph: b21SetSign
//        /// Sets the GL-SIGN based on the value of S-SHORT-WRITTEN-DERIVATIVE.
//        /// If S-SHORT-WRITTEN-DERIVATIVE is true, sets GL-SIGN to '+', otherwise to '-'.
//        /// </remarks>
//        public void B21SetSign(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Get the current GlRecord
//            var glRecord = fvar.GetGlRecord();

//            // Set GL-SIGN based on S-SHORT-WRITTEN-DERIVATIVE value
//            if (gvar.GetSShortWrittenDerivative())
//            {
//                glRecord.SetGlSign("+");
//            }
//            else
//            {
//                glRecord.SetGlSign("-");
//            }

//            // Update the GlRecord in fvar
//            fvar.SetGlRecord(glRecord);
//        }
//        /// <summary>
//        /// Implements the COBOL B3DESC-TABLE paragraph functionality in C#.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph: B3DESC-TABLE
//        /// This method searches for a movement description in the descriptions table
//        /// and sets a flag if found or if it matches specific patterns.
//        /// </remarks>
//        public void B3DescTable(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Set initial flag value to 'N'
//            gvar.SetWsDescFlag("N");

//            // PERFORM VARYING loop equivalent
//            int wtddInx;
//            for (wtddInx = 1;
//                 wtddInx <= gvar.GetWtDescriptionsD().GetWtddOccurs() &&
//                 gvar.GetWtDescriptionsD().GetWtddDescAt(wtddInx) != gvar.GetScheduleRecord().GetSMovementDescription();
//                 wtddInx++)
//            {
//                // No operation - just continue looping
//            }

//            // Update WTDD-INX value
//            gvar.SetWtddInx(wtddInx);

//            // Check if we found the description in the table
//            if (gvar.GetWtddInx() <= gvar.GetWtDescriptionsD().GetWtddOccurs())
//            {
//                gvar.SetWsDescFlag("Y");
//            }
//            else
//            {
//                // Check for special cases
//                gvar.GetWsTempFields().SetWsTempDescription(gvar.GetScheduleRecord().GetSMovementDescription());
//                string tempDesc = gvar.GetWsTempFields().GetWsTempDescription();
//                bool sEpWcExercise = gvar.GetSchedulerecord().GetStranchedetaildata().GetStrancheline().GetSsourcecategory().IsSepwcexercise();

//                if (tempDesc == "CT TO" ||
//                    tempDesc == "GS TO" ||
//                    (tempDesc == "EX FM" && sEpWcExercise))
//                {
//                    gvar.SetWsDescFlag("Y");
//                }
//            }
//        }
//        /// <summary>
//        /// Implements the COBOL paragraph B4-TEST-OL-SP logic.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph: B4-TEST-OL-SP
//        /// Sets write flags and indicators based on capital gain/loss and movement description conditions.
//        /// </remarks>
//        public void B4TestOlSp(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Set WRITE-OL-SP-N to TRUE
//            gvar.GetWwriteolspflag().SetWriteolspn(true);

//            // Check if capital gain/loss is numeric and movement description matches conditions
//            if (gvar.GetScheduleRecord().GetSCapitalGainOrLoss9().GetNumeric() &&
//                ((gvar.GetScheduleRecord().GetSMovementDescription() == "STOCK PURCHASE" &&
//                  gvar.GetSShortWrittenDerivative()) ||
//                 (gvar.GetScheduleRecord().GetSMovementDescription() == "OPTION LAPSE")))
//            {
//                // Set write flag and indicators when conditions are met
//                gvar.SetWsWriteFlag("Y");
//                gvar.GetScheduleRecord().SetSBdvLineIndicator("1");
//                gvar.GetWwriteolspflag().SetWriteolspy(true);
//            }
//        }
//        /// <summary>
//        /// Implements the COBOL B51982-INDEXATION paragraph logic.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph: B51982-INDEXATION
//        /// Handles indexation logic for capital gains processing.
//        /// Sets flags and processes disposal proceeds and capital gain/loss amounts.
//        /// </remarks>
//        public void B51982Indexation(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            if (gvar.GetWswriteflag().IsWritey())
//            {
//                if (gvar.GetScheduleRecord().GetSDisposalProceedsX() != gvar.GetSpaces())
//                {
//                    gvar.GetWsTempFields().SetWsGlPrcdsS(gvar.GetScheduleRecord().GetSDisposalProceeds9());
//                }
//                if (gvar.GetScheduleRecord().GetSCapitalGainOrLossX() != gvar.GetSpaces())
//                {
//                    gvar.GetWsTempFields().SetWsGlGainLossS(gvar.GetScheduleRecord().GetSCapitalGainOrLoss9());
//                }
//                gvar.GetFlags().SetW1982Flag("Y");
//                if ((gvar.GetScheduleRecord().GetSDisposalProceedsX() != gvar.GetSpaces()) &&
//                    (gvar.GetScheduleRecord().GetSCapitalGainOrLossX() != gvar.GetSpaces()))
//                {
//                    B2WriteRecord(gvar, fvar, ivar);
//                }
//            }
//            else
//            {
//                if ((gvar.GetScheduleRecord().GetSDisposalProceedsX() == gvar.GetSpaces()) &&
//                    (gvar.GetScheduleRecord().GetSCapitalGainOrLossX() == gvar.GetSpaces()))
//                {
//                    gvar.GetFlags().SetW1982Flag("Y");
//                }
//            }
//        }
//        /// <summary>
//        /// Implements the COBOL paragraph B6-INDEXATION which handles indexation logic for capital gains.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph: b6Indexation
//        /// This method processes indexation flags and capital gain/loss values,
//        /// transferring data between schedule records and temporary fields when conditions are met.
//        /// </remarks>
//        /// <param name="fvar">Fvar object containing GL record data</param>
//        /// <param name="gvar">Gvar object containing schedule records and temporary fields</param>
//        /// <param name="ivar">Ivar object (not used in this method)</param>
//        public void B6Indexation(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Check if write flag is set
//            if (gvar.GetWswriteflag().IsWritey())
//            {
//                // Handle indexation limit flag
//                if (gvar.GetScheduleRecord().GetSIndexationLimit() == "I")
//                {
//                    fvar.GetGlRecord().SetGlIndFlag(gvar.GetScheduleRecord().GetSIndexationLimit());
//                }

//                // Process disposal proceeds if present
//                if (gvar.GetScheduleRecord().GetSDisposalProceedsX() != gvar.GetSpaces())
//                {
//                    gvar.GetWsTempFields().SetWsGlPrcdsS(gvar.GetScheduleRecord().GetSDisposalProceeds9());
//                }

//                // Process capital gain/loss if present
//                if (gvar.GetScheduleRecord().GetSCapitalGainOrLossX() != gvar.GetSpaces())
//                {
//                    gvar.GetWsTempFields().SetWsGlGainLossS(gvar.GetScheduleRecord().GetSCapitalGainOrLoss9());
//                }

//                // Handle different indexation limit cases
//                if (gvar.GetScheduleRecord().GetSIndexationLimit() != "B" &&
//                   gvar.GetScheduleRecord().GetSIndexationLimit() != "C")
//                {
//                    if (gvar.GetScheduleRecord().GetSDisposalProceedsX() != gvar.GetSpaces())
//                    {
//                        // Perform write record operation
//                        B2WriteRecord(fvar, gvar, ivar);
//                    }
//                }
//            }
//        }
//        /// <summary>
//        /// Implements the B7StockHeader paragraph from COBOL.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph: b7StockHeader
//        /// Handles stock header processing including:
//        /// - Date conversion
//        /// - Cost value assignments
//        /// - Flag management
//        /// </remarks>
//        public void B7StockHeader(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            gvar.SetWsDescFlag("N");
//            B3DescTable(fvar, gvar, ivar);

//            if (gvar.GetWsDescFlag().IsDescy() || gvar.GetWwriteolspflag().IsWriteolspy())
//            {
//                gvar.GetCgtdate2LinkageDate1().SetCgtdate2Yymmdd1(gvar.GetScheduleRecord().GetSMovementDate9());
//                gvar.SetCgtdate2LinkageDate1(gvar.GetCgtdate2LinkageDate1());
//                fvar.GetGlRecord().SetGlMoveDate(gvar.GetCgtdate2LinkageDate1().GetCgtdate2Ccyymmdd1().ToString());

//                if (gvar.GetScheduleRecord().GetSHoldingUnitsX() != gvar.GetSpaces())
//                {
//                    gvar.GetWsTempFields().SetWsGlQtyS(gvar.GetScheduleRecord().GetSHoldingUnits9());
//                }

//                if (gvar.GetScheduleRecord().GetSIndexationCostX() == gvar.GetSpaces())
//                {
//                    gvar.GetWsTempFields().SetWsGlIndValS(gvar.GetScheduleRecord().GetSHoldingCost9());
//                }
//                else
//                {
//                    gvar.GetWsTempFields().SetWsGlIndValS(gvar.GetScheduleRecord().GetSIndexationCost9());
//                }

//                if (gvar.GetScheduleRecord().GetSHoldingCostX() != gvar.GetSpaces())
//                {
//                    gvar.GetWsTempFields().SetWsGlBookS(gvar.GetScheduleRecord().GetSHoldingCost9());
//                }

//                gvar.SetWsWriteFlag("Y");

//                if (gvar.GetWsfirstvalueflag().IsWsfirstvalue())
//                {
//                    gvar.GetWsTempFields().SetWsOrigHoldingCost(gvar.GetScheduleRecord().GetSHoldingCost9());
//                    gvar.GetWsTempFields().SetWsOrigIndexCost(gvar.GetScheduleRecord().GetSIndexationCost9());
//                    gvar.SetWsfirstvalueflag(false);
//                }
//            }

//            B6Indexation(fvar, gvar, ivar);
//        }
//        /// <summary>
//        /// B81982Record - COBOL paragraph name
//        /// </summary>
//        /// <remarks>
//        /// Handles the conditional moves for holding and indexation costs in the schedule record.
//        /// If S-HOLDING-COST-X is not spaces, moves S-HOLDING-COST-9 to WS-TEMP-HOLDING-COSTS-S.
//        /// If S-INDEXATION-COST-X is not spaces, moves S-INDEXATION-COST-9 to WS-TEMP-INDEXATION-COSTS-S.
//        /// </remarks>
//        public void B81982Record(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Check if S-HOLDING-COST-X is not spaces
//            if (gvar.GetScheduleRecord().GetSHoldingCostX() != gvar.GetSpaces())
//            {
//                gvar.GetWsTempFields().SetWsTempHoldingCostsS(gvar.GetScheduleRecord().GetSHoldingCost9());
//            }

//            // Check if S-INDEXATION-COST-X is not spaces
//            if (gvar.GetScheduleRecord().GetSIndexationCostX() != gvar.GetSpaces())
//            {
//                gvar.GetWsTempFields().SetWsTempIndexationCostsS(gvar.GetScheduleRecord().GetSIndexationCost9());
//            }
//        }
//        /// <summary>
//        /// Implements the B9DeemedProceeds logic from COBOL to C#.
//        /// Original COBOL paragraph name: b9DeemedProceeds
//        /// </summary>
//        /// <remarks>
//        /// This method handles the addition of holding and indexation costs to GL book values
//        /// following the same conditional logic as the original COBOL paragraph.
//        /// </remarks>
//        public void B9DeemedProceeds(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // First condition: If S-HOLDING-COST-9 is numeric, add to WS-GL-BOOK-S
//            if (gvar.GetNumeric().Equals(gvar.GetScheduleRecord().GetSHoldingCost9()))
//            {
//                decimal currentBookS = gvar.GetWsTempFields().GetWsGlBookS();
//                gvar.GetWsTempFields().SetWsGlBookS(currentBookS + gvar.GetScheduleRecord().GetSHoldingCost9());
//            }

//            // Second condition: Check S-INDEXATION-COST-9
//            if (gvar.GetNumeric().Equals(gvar.GetScheduleRecord().GetSIndexationCost9()))
//            {
//                decimal currentIndValS = gvar.GetWsTempFields().GetWsGlIndValS();
//                gvar.GetWsTempFields().SetWsGlIndValS(currentIndValS + gvar.GetScheduleRecord().GetSIndexationCost9());
//            }
//            else if (gvar.GetScheduleRecord().GetSBdvLineIndicator() == "0" &&
//                     gvar.GetNumeric().Equals(gvar.GetScheduleRecord().GetSHoldingCost9()))
//            {
//                // Else If condition: When S-BDV-LINE-INDICATOR is '0' and S-HOLDING-COST-9 is numeric
//                decimal currentIndValS = gvar.GetWsTempFields().GetWsGlIndValS();
//                gvar.GetWsTempFields().SetWsGlIndValS(currentIndValS + gvar.GetScheduleRecord().GetSHoldingCost9());
//            }
//        }
//        /// <summary>
//        /// Implements the COBOL B10ADJUSTMENTS paragraph.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph: B10ADJUSTMENTS
//        /// This method handles adjustments processing.
//        /// </remarks>
//        /// <param name="fvar">Fvar object containing file-related variables</param>
//        /// <param name="gvar">Gvar object containing global variables</param>
//        /// <param name="ivar">Ivar object containing input variables</param>
//        public void B10Adjustments(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // The original COBOL paragraph was empty with no logic
//            // This is an empty implementation matching the original COBOL
//        }
//        /// <summary>
//        /// Implements the logic from COBOL paragraph b10.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph name: b10
//        /// Adds holding and indexation costs to temporary fields if they are not empty.
//        /// </remarks>
//        /// <param name="fvar">Fvar parameter (unused in this implementation)</param>
//        /// <param name="gvar">Gvar parameter containing all relevant data fields</param>
//        /// <param name="ivar">Ivar parameter (unused in this implementation)</param>
//        public void B10(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Check if S-HOLDING-COST-X is not empty and add S-HOLDING-COST-9 to WS-GL-BOOK-S if true
//            if (gvar.GetScheduleRecord().GetSHoldingCostX() != gvar.GetSpaces())
//            {
//                decimal currentBookValue = gvar.GetWsTempFields().GetWsGlBookS();
//                decimal holdingCost = gvar.GetScheduleRecord().GetSHoldingCost9();
//                gvar.GetWsTempFields().SetWsGlBookS(currentBookValue + holdingCost);
//            }

//            // Check if S-INDEXATION-COST-X is not empty and add S-INDEXATION-COST-9 to WS-GL-IND-VAL-S if true
//            if (gvar.GetScheduleRecord().GetSIndexationCostX() != gvar.GetSpaces())
//            {
//                decimal currentIndVal = gvar.GetWsTempFields().GetWsGlIndValS();
//                decimal indexationCost = gvar.GetScheduleRecord().GetSIndexationCost9();
//                gvar.GetWsTempFields().SetWsGlIndValS(currentIndVal + indexationCost);
//            }
//        }
//        /// <summary>
//        /// Original COBOL paragraph: b11PriorGain
//        /// </summary>
//        /// <remarks>
//        /// This method implements the COBOL B11-PRIOR-GAIN paragraph functionality.
//        /// </remarks>
//        public void B11PriorGain(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Empty method implementation since the provided COBOL paragraph was empty
//            // No logic to convert as the source paragraph contained no executable statements
//        }
//        /// <summary>
//        /// Original COBOL paragraph: b11
//        /// </summary>
//        /// <remarks>
//        /// Checks if S-HOLDING-COST-X is not spaces, then adds S-HOLDING-COST-9 to both WS-GL-BOOK-S and WS-GL-IND-VAL-S
//        /// </remarks>
//        public void B11(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Check if S-HOLDING-COST-X is not equal to spaces
//            if (gvar.GetScheduleRecord().GetSHoldingCostX() != gvar.GetSpaces())
//            {
//                // Add S-HOLDING-COST-9 to WS-GL-BOOK-S
//                gvar.GetWsTempFields().SetWsGlBookS(
//                    gvar.GetWsTempFields().GetWsGlBookS() +
//                    gvar.GetScheduleRecord().GetSHoldingCost9());

//                // Add S-HOLDING-COST-9 to WS-GL-IND-VAL-S
//                gvar.GetWsTempFields().SetWsGlIndValS(
//                    gvar.GetWsTempFields().GetWsGlIndValS() +
//                    gvar.GetScheduleRecord().GetSHoldingCost9());
//            }
//        }
//        /// <summary>
//        /// Implementation of COBOL paragraph B12PRIORINDEX
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph name: b12PriorIndex
//        /// This method handles prior index processing logic
//        /// </remarks>
//        public void B12PriorIndex(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // The original COBOL paragraph was empty
//            // No implementation needed as there was no logic in the source paragraph
//            // This is an empty method to maintain the structural match with COBOL
//        }
//        /// <summary>
//        /// Implements the COBOL paragraph b12 which subtracts holding cost from GL book value if holding cost is specified.
//        /// Original COBOL paragraph name: b12
//        /// </summary>
//        /// <remarks>
//        /// This method checks if S-HOLDING-COST-X contains a value (not spaces) and if so,
//        /// subtracts S-HOLDING-COST-9 from WS-GL-BOOK-S.
//        /// </remarks>
//        public void B12(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Check if holding cost field is not empty
//            if (gvar.GetScheduleRecord().GetSHoldingCostX() != gvar.GetSpaces())
//            {
//                // Subtract holding cost amount from GL book value
//                decimal currentValue = gvar.GetWsTempFields().GetWsGlBookS();
//                decimal subtractedValue = currentValue - gvar.GetScheduleRecord().GetSHoldingCost9();
//                gvar.GetWsTempFields().SetWsGlBookS(subtractedValue);
//            }
//        }
//        /// <summary>
//        /// Implements the C-END paragraph from COBOL which handles the closing and cleanup operations
//        /// for the capital gains tax scanning process.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph: C-END
//        /// This method performs debugging output, file closing operations, and gain/loss export processing
//        /// before finalizing the CGT scanning operation.
//        /// </remarks>
//        public void CEnd(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Debug logging 1
//            gvar.SetEqtdebugText("1 CGTSCANX - C-END");
//            XCallEqtdebug(gvar);

//            // Debug logging 2
//            gvar.SetEqtdebugText("2 CGTSCANX - C-END");
//            XCallEqtdebug(gvar);

//            // Close files
//            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.CLOSE_FILE);
//            XCallCgtfiles(gvar);

//            // Close realised export file if needed
//            if (gvar.GetEquityglobalparms().GetGgeneralparms().GetGcgtscot1format().IsCgtscot1realisedextract())
//            {
//                gvar.SetRealisedExportFile(null);
//            }

//            // Debug logging 3
//            gvar.SetEqtdebugText("3 CGTSCANX - C-END");
//            XCallEqtdebug(gvar);

//            // Debug logging 4
//            gvar.SetEqtdebugText("4 CGTSCANX - C-END");
//            XCallEqtdebug(gvar);

//            // Process gain/loss export if needed
//            if (gvar.GetEquityglobalparms().GetGgeneralparms().GetGcgtscot1format().IsCgtscot1gainlossexport())
//            {
//                // Prepare report file
//                gvar.SetReportFile("$" + gvar.GetCommonLinkage().GetLUserNo().ToString() + "5D" +
//                                  gvar.GetCgtfilesLinkage().GetLReportNo().ToString() + ".REP");

//                gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);
//                gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetReportFile());
//                XCallEqtpath(gvar);
//                gvar.SetReportFileName(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

//                // Initialize export file
//                fvar.SetGlRecord(gvar.GetSpaces());
//                ivar.GetCgt0109ALinkage().SetOutput(""); // Simplified for OPEN OUTPUT

//                // Log export creation
//                gvar.GetCgtlogLinkageArea2().SetLLogMessage("Creating Gain/Loss Export:      " + gvar.GetReportFile());
//                X4Cgtlog(gvar);

//                // Process temporary records
//                gvar.GetCgttempLinkage().SetCgttempKey(gvar.GetSpaces());
//                gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_START_NOT_LESS_THAN);
//                XCallCgttemp(gvar);

//                gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_READ);
//                XCallCgttemp(gvar);

//                while (gvar.GetCgttempLinkage().GetCgttempStatus() == "00")
//                {
//                    gvar.GetTempFields().SetTempKey(gvar.GetCgttempLinkage().GetCgttempKey());
//                    gvar.GetTempFields().SetTempDetails(gvar.GetCgttempLinkage().GetCgttempDetails());
//                    C1FormatWriteExport2(gvar, fvar, ivar);
//                    XCallCgttemp(gvar);
//                }

//                // Close temp file
//                gvar.GetCgttempLinkage().SetCgttempAction(Gvar.CGTTEMP_CLOSE);
//                XCallCgttemp(gvar);
//                gvar.SetRealisedExportFile(null);
//            }

//            // Final logging
//            gvar.GetCgtlogLinkageArea2().SetLLogMessageType("F");
//            gvar.GetCgtlogLinkageArea1().SetLLogAction(Gvar.CLOSE_FILE);
//            X4Cgtlog(gvar);
//        }
//        /// <summary>
//        /// Converts COBOL paragraph C1-FORMAT-WRITE-EXPORT-2 to C# method.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL logic:
//        /// Moves data from temporary fields to GL-RECORD2 structure and writes the record.
//        /// Handles capital gain/loss logic and flags based on input values.
//        /// </remarks>
//        public void C1FormatWriteExport2(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Initialize GL-RECORD2 with base record
//            fvar.SetGlRecord2(gvar.GetWInitialRecord2());

//            // Set fund code and SEDOL
//            fvar.GetGlRecord2().SetGlFund2(gvar.GetTempFields().GetTempKFundCode());
//            fvar.GetGlRecord2().SetGlSedol2(gvar.GetTempFields().GetTempKSedolCode());

//            // Set quantity and disposal date
//            fvar.GetGlRecord2().SetGlHolding(gvar.GetTempFields().GetTempQuantity());
//            fvar.GetGlRecord2().SetGlDisposalDate(gvar.GetTempFields().GetTempDisposalDate().ToString());

//            // Handle capital gain/loss logic
//            if (gvar.GetTempFields().GetTempGainLoss() > gvar.GetZero())
//            {
//                fvar.GetGlRecord2().SetGlCapitalGain(gvar.GetTempFields().GetTempGainLoss());
//                fvar.GetGlRecord2().SetGlCapitalLoss(gvar.GetZero());
//            }
//            else
//            {
//                fvar.GetGlRecord2().SetGlCapitalGain(gvar.GetZero());
//                fvar.GetGlRecord2().SetGlCapitalLoss(gvar.GetTempFields().GetTempGainLoss());
//            }

//            // Set holding flag if applicable
//            if (gvar.GetTempFields().GetTempHoldingFlag() == "Y")
//            {
//                fvar.GetGlRecord2().SetGlHoldingFlag(gvar.GetTempFields().GetTempHoldingFlag());
//            }

//            // Set tranche flag if applicable
//            if (gvar.GetTempFields().GetTempTrancheFlag() == "Y")
//            {
//                fvar.GetGlRecord2().SetGlTrancheFlag2(gvar.GetTempFields().GetTempTrancheFlag());
//            }

//            // Handle sign format based on configuration
//            if (gvar.GetWnewderivativeexportformat().IsConfignewderivativeexportformat())
//            {
//                fvar.GetGlRecord2().SetGlSign2(gvar.GetTempFields().GetTempSign());
//            }
//            else
//            {
//                fvar.GetGlRecord2().SetGlWithoutSign2("");
//            }

//            // Write the record - actual write operation would be performed by calling system
//            // (Note: In C# this would likely be handled by a different mechanism)
//        }
//        /// <summary>
//        /// Implements the COBOL x4Cgtlog paragraph functionality.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph name: x4Cgtlog
//        /// This method increments the message number, logs it, and handles message type conditions.
//        /// </remarks>
//        /// <param name="fvar">Fvar parameter for variable access</param>
//        /// <param name="gvar">Gvar parameter for variable access</param>
//        /// <param name="ivar">Ivar parameter for variable access</param>
//        public void X4Cgtlog(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Increment WS-MESSAGE-NO by 1
//            gvar.SetWsMessageNo((int.Parse(gvar.GetWsMessageNo()) + 1).ToString());

//            // Move WS-MESSAGE-NO to L-LOG-MESSAGE-NO
//            gvar.GetCgtlogLinkageArea2().SetLLogMessageNo(gvar.GetWsMessageNo());

//            // Call CGTLOG with both linkage areas
//            CGTLOG cgtlog = new CGTLOG();
//            cgtlog.Run(gvar.GetCgtlogLinkageArea1(), gvar.GetCgtlogLinkageArea2());

//            // Check if message type is 'Q' and set flags accordingly
//            if (gvar.GetCgtlogLinkageArea2().GetLLogMessageType() == "Q")
//            {
//                gvar.SetWsProcessFlag("Q");
//                gvar.GetCgtlogLinkageArea2().SetLLogMessageType("P");
//            }

//            // Clear the log message
//            gvar.GetCgtlogLinkageArea2().SetLLogMessage(gvar.GetSpaces());
//        }
//        /// <summary>
//        /// Executes the EQTPATH program by calling it with the EQTPATH-LINKAGE parameter.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph name: xCallEqtpath
//        /// This method performs a direct call to the EQTPATH program using the EQTPATH-LINKAGE data.
//        /// </remarks>
//        /// <param name="fvar">Field variable accessor</param>
//        /// <param name="gvar">Global variable accessor</param>
//        /// <param name="ivar">Index variable accessor</param>
//        public void XCallEqtpath(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Create new instance of external program EQTPATH
//            Eqtpath eqtpath = new Eqtpath();
//            // Call the Run method with the EQTPATH-LINKAGE parameter
//            eqtpath.Run(gvar.GetEqtpathLinkage());
//        }
//        /// <summary>
//        /// XCallEqtdebug - Calls the EQTDEBUG program with EQTDEBUG-LINKAGE as parameter.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph name: X-CALL-EQTDEBUG
//        /// This method corresponds to a COBOL CALL statement to program EQTDEBUG with EQTDEBUG-LINKAGE parameter.
//        /// </remarks>
//        /// <param name="fvar">Fvar parameter (unused in this method)</param>
//        /// <param name="gvar">Gvar parameter containing EQTDEBUG-LINKAGE</param>
//        /// <param name="ivar">Ivar parameter (unused in this method)</param>
//        public void XCallEqtdebug(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Create a new instance of the external program EQTDEBUG
//            EQTDEBUG eqtdebug = new EQTDEBUG();
//            // Call the Run method with EQTDEBUG-LINKAGE parameter
//            eqtdebug.Run(gvar.GetEqtdebugLinkage());
//        }
//        /// <summary>
//        /// Executes the CGTFILES call with the specified linkage areas.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph name: xCallCgtfiles
//        /// This method performs the equivalent of COBOL's CALL 'CGTFILES' statement,
//        /// passing the CGTFILES-LINKAGE, L-FILE-RECORD-AREA, and COMMON-LINKAGE as parameters.
//        /// </remarks>
//        /// <param name="fvar">Fvar instance containing field variables</param>
//        /// <param name="gvar">Gvar instance containing global variables</param>
//        /// <param name="ivar">Ivar instance containing internal variables</param>
//        public void XCallCgtfiles(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Create a new instance of the CGTFILES program
//            CGTFILES cgtfiles = new CGTFILES();

//            // Call the Run method with the parameters specified in the COBOL USING clause
//            cgtfiles.Run(
//                gvar.GetCgtfilesLinkage(),
//                gvar.GetLFileRecordArea(),
//                gvar.GetCommonLinkage()
//            );
//        }
//        /// <summary>
//        /// Converts the COBOL paragraph X-CALL-MF-HANDLER-FOR-CONFIG to C#.
//        /// This method handles calling the external module ELCGMIO and moving configuration values.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph: X-CALL-MF-HANDLER-FOR-CONFIG
//        /// The method:
//        /// 1. Moves GET-CONFIG-VALUE to L-ACT
//        /// 2. Calls external module ELCGMIO with linkage parameters
//        /// 3. Moves ELCGMIO-LINKAGE-2 to W-CONFIG-ITEM
//        /// </remarks>
//        /// <param name="fvar">Fvar instance</param>
//        /// <param name="gvar">Gvar instance</param>
//        /// <param name="ivar">Ivar instance</param>
//        public void XCallMfHandlerForConfig(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Move GET-CONFIG-VALUE to L-ACT (ELCGMIO-LINKAGE-1)
//            gvar.GetElcgmioLinkage1().SetLAct(Gvar.GET_CONFIG_VALUE);

//            // Call ELCGMIO module with linkage parameters
//            ELCGMIO elcgmio = new ELCGMIO();
//            elcgmio.Run(gvar.GetElcgmioLinkage1(), gvar.GetElcgmioLinkage2());

//            // Move ELCGMIO-LINKAGE-2 to W-CONFIG-ITEM
//            gvar.SetWConfigItem(gvar.GetElcgmioLinkage2());
//        }
//        /// <summary>
//        /// XCallCgttemp - Converts the COBOL CALL 'CGTTEMP' USING CGTTEMP-LINKAGE statement to C#.
//        /// </summary>
//        /// <remarks>
//        /// Original COBOL paragraph name: xCallCgttemp
//        /// This method handles the CALL statement by creating an instance of the CGTTEMP program
//        /// and executing it with the CGTTEMP-LINKAGE parameter.
//        /// </remarks>
//        public void XCallCgttemp(Fvar fvar, Gvar gvar, Ivar ivar)
//        {
//            // Create a new instance of the external program
//            Cgttemp cgttemp = new Cgttemp();
//            // Call the Run method with the CGTTEMP-LINKAGE parameter
//            cgttemp.Run(gvar.GetCgttempLinkage());
//        }

//    }
//}
