{"RootPath": "C:\\Elfonze\\Mclaren\\AugTest\\Equity.CobMod", "ProjectFileName": "Equity.CobMod.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Computations\\Cgtbalup.cs"}, {"SourceFile": "Computations\\Eqtye.cs"}, {"SourceFile": "Exports\\Cgt179Hs.cs"}, {"SourceFile": "Exports\\Cgt222Hs.cs"}, {"SourceFile": "Exports\\Cgtdaily.cs"}, {"SourceFile": "Exports\\Cgthbose.cs"}, {"SourceFile": "Exports\\Cgtscanx.cs"}, {"SourceFile": "Exports\\Cgtscot1.cs"}, {"SourceFile": "Exports\\Cgtscot2.cs"}, {"SourceFile": "Exports\\Cgtsing2.cs"}, {"SourceFile": "Exports\\Cgtsing3.cs"}, {"SourceFile": "Exports\\Cgtsingf.cs"}, {"SourceFile": "Exports\\Cgtskrl.cs"}, {"SourceFile": "Facade\\EquityFacadeClass.cs"}, {"SourceFile": "Facade\\EquityRouter.cs"}, {"SourceFile": "FIleHandlers\\AssetUsageCalendarDAL.cs"}, {"SourceFile": "FIleHandlers\\COUNTRYDAL.cs"}, {"SourceFile": "FIleHandlers\\Elcggio.cs"}, {"SourceFile": "FIleHandlers\\GetTemporaryBalancesDeleteNo.cs"}, {"SourceFile": "FIleHandlers\\GroupDAL.cs"}, {"SourceFile": "FIleHandlers\\MasterComputationalDAL.cs"}, {"SourceFile": "FIleHandlers\\MasterDAL.cs"}, {"SourceFile": "FIleHandlers\\ParametersDAL.cs"}, {"SourceFile": "FileHandlers\\PartTrancheDisposalService.cs"}, {"SourceFile": "FIleHandlers\\PriceDAL.cs"}, {"SourceFile": "FIleHandlers\\RPIDAL.cs"}, {"SourceFile": "FIleHandlers\\RunLogDAL.cs"}, {"SourceFile": "FIleHandlers\\TaperRateDAL.cs"}, {"SourceFile": "FIleHandlers\\UserFundProcessor.cs"}, {"SourceFile": "FIleHandlers\\UserFundsDAL.cs"}, {"SourceFile": "Helper\\Cgdate.cs"}, {"SourceFile": "Helper\\Cgtabort.cs"}, {"SourceFile": "Helper\\Cgtdate2.cs"}, {"SourceFile": "Helper\\Cgtdel.cs"}, {"SourceFile": "Helper\\Cgtinvrt.cs"}, {"SourceFile": "Helper\\Cgtlock.cs"}, {"SourceFile": "Helper\\Cgtname.cs"}, {"SourceFile": "Helper\\Cgtsched.cs"}, {"SourceFile": "Helper\\Cgtstat.cs"}, {"SourceFile": "Helper\\Cgttemp.cs"}, {"SourceFile": "Helper\\Cgttemp2.cs"}, {"SourceFile": "Helper\\CheckCat.cs"}, {"SourceFile": "Helper\\Cgtprice.cs"}, {"SourceFile": "Helper\\Eqtdebug.cs"}, {"SourceFile": "Helper\\Eqtenvnm.cs"}, {"SourceFile": "Helper\\Eqtlog.cs"}, {"SourceFile": "Helper\\Eqtpath.cs"}, {"SourceFile": "Helper\\Equsecal.cs"}, {"SourceFile": "Helper\\GetCurrency.cs"}, {"SourceFile": "Helper\\RealSub.cs"}, {"SourceFile": "Helper\\TempData.cs"}, {"SourceFile": "Helper\\WriteTrace.cs"}, {"SourceFile": "Models\\CGDate\\Gvar.cs"}, {"SourceFile": "Models\\CGDate\\Ivar.cs"}, {"SourceFile": "Models\\CGDate\\LValidDate.cs"}, {"SourceFile": "Models\\CGDate\\Ws2MonthTable.cs"}, {"SourceFile": "Models\\CGDate\\Ws3WorkArea.cs"}, {"SourceFile": "Models\\CGDate\\Ws4DisplayScreen.cs"}, {"SourceFile": "Models\\CGT179HS\\AcquisitionIds.cs"}, {"SourceFile": "Models\\CGT179HS\\BalanceIds.cs"}, {"SourceFile": "Models\\CGT179HS\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGT179HS\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGT179HS\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGT179HS\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGT179HS\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGT179HS\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGT179HS\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGT179HS\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGT179HS\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGT179HS\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGT179HS\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGT179HS\\CgtskanLinkage.cs"}, {"SourceFile": "Models\\CGT179HS\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGT179HS\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGT179HS\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGT179HS\\D77Record.cs"}, {"SourceFile": "Models\\CGT179HS\\DisposalIds.cs"}, {"SourceFile": "Models\\CGT179HS\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGT179HS\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGT179HS\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGT179HS\\Filler111.cs"}, {"SourceFile": "Models\\CGT179HS\\Filler112.cs"}, {"SourceFile": "Models\\CGT179HS\\Fvar.cs"}, {"SourceFile": "Models\\CGT179HS\\Gvar.cs"}, {"SourceFile": "Models\\CGT179HS\\HeaderIds.cs"}, {"SourceFile": "Models\\CGT179HS\\Ivar.cs"}, {"SourceFile": "Models\\CGT179HS\\ReportFile.cs"}, {"SourceFile": "Models\\CGT179HS\\WIds.cs"}, {"SourceFile": "Models\\CGT179HS\\WInitialisedRecord.cs"}, {"SourceFile": "Models\\CGT179HS\\WTodaysDate.cs"}, {"SourceFile": "Models\\CGT222HS\\AcquisitionIds.cs"}, {"SourceFile": "Models\\CGT222HS\\BalanceIds.cs"}, {"SourceFile": "Models\\CGT222HS\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGT222HS\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGT222HS\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGT222HS\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGT222HS\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGT222HS\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGT222HS\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGT222HS\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGT222HS\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGT222HS\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGT222HS\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGT222HS\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGT222HS\\CgtskanLinkage.cs"}, {"SourceFile": "Models\\CGT222HS\\CgttempLinkage.cs"}, {"SourceFile": "Models\\CGT222HS\\CheckCatLinkage.cs"}, {"SourceFile": "Models\\CGT222HS\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGT222HS\\D105Record.cs"}, {"SourceFile": "Models\\CGT222HS\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGT222HS\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGT222HS\\D34Record.cs"}, {"SourceFile": "Models\\CGT222HS\\DisposalIds.cs"}, {"SourceFile": "Models\\CGT222HS\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGT222HS\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGT222HS\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGT222HS\\Filler112.cs"}, {"SourceFile": "Models\\CGT222HS\\Fvar.cs"}, {"SourceFile": "Models\\CGT222HS\\Gvar.cs"}, {"SourceFile": "Models\\CGT222HS\\HeaderIds.cs"}, {"SourceFile": "Models\\CGT222HS\\Ivar.cs"}, {"SourceFile": "Models\\CGT222HS\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGT222HS\\ReportFile.cs"}, {"SourceFile": "Models\\CGT222HS\\WIds.cs"}, {"SourceFile": "Models\\CGT222HS\\WInitialisedRecord.cs"}, {"SourceFile": "Models\\CGT222HS\\WTodaysDate.cs"}, {"SourceFile": "Models\\CGT222HS\\WWorkFields.cs"}, {"SourceFile": "Models\\CGTABORT\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTABORT\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTABORT\\CgtstatLinkage.cs"}, {"SourceFile": "Models\\CGTABORT\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTABORT\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTABORT\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTABORT\\EqtdebugLinkage.cs"}, {"SourceFile": "Models\\CGTABORT\\EquityErrorMessages.cs"}, {"SourceFile": "Models\\CGTABORT\\EquityParameters.cs"}, {"SourceFile": "Models\\CGTABORT\\EquityStatusRecord.cs"}, {"SourceFile": "Models\\CGTABORT\\Filler21.cs"}, {"SourceFile": "Models\\CGTABORT\\Gvar.cs"}, {"SourceFile": "Models\\CGTABORT\\Ivar.cs"}, {"SourceFile": "Models\\CGTABORT\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTBALUP\\AcquisitionIds.cs"}, {"SourceFile": "Models\\CGTBALUP\\BalanceIds.cs"}, {"SourceFile": "Models\\CGTBALUP\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTBALUP\\Cgtbalup00.cs"}, {"SourceFile": "Models\\CGTBALUP\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTBALUP\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTBALUP\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTBALUP\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTBALUP\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTBALUP\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTBALUP\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTBALUP\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTBALUP\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTBALUP\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTBALUP\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTBALUP\\CgtlogLinkageArea1.cs"}, {"SourceFile": "Models\\CGTBALUP\\CgtlogLinkageArea2.cs"}, {"SourceFile": "Models\\CGTBALUP\\CgtmessLinkage.cs"}, {"SourceFile": "Models\\CGTBALUP\\CheckCatLinkage.cs"}, {"SourceFile": "Models\\CGTBALUP\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTBALUP\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTBALUP\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTBALUP\\D37Record.cs"}, {"SourceFile": "Models\\CGTBALUP\\D37RecordFormat2.cs"}, {"SourceFile": "Models\\CGTBALUP\\D45BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTBALUP\\D45Record.cs"}, {"SourceFile": "Models\\CGTBALUP\\D4Record.cs"}, {"SourceFile": "Models\\CGTBALUP\\DateStamp.cs"}, {"SourceFile": "Models\\CGTBALUP\\DisposalIds.cs"}, {"SourceFile": "Models\\CGTBALUP\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTBALUP\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTBALUP\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTBALUP\\EquityParameters.cs"}, {"SourceFile": "Models\\CGTBALUP\\Filler202.cs"}, {"SourceFile": "Models\\CGTBALUP\\Fvar.cs"}, {"SourceFile": "Models\\CGTBALUP\\GetCurrencyLinkage.cs"}, {"SourceFile": "Models\\CGTBALUP\\Gvar.cs"}, {"SourceFile": "Models\\CGTBALUP\\HeaderIds.cs"}, {"SourceFile": "Models\\CGTBALUP\\Ivar.cs"}, {"SourceFile": "Models\\CGTBALUP\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTBALUP\\LkFundSedol.cs"}, {"SourceFile": "Models\\CGTBALUP\\ReportColumnHeading1.cs"}, {"SourceFile": "Models\\CGTBALUP\\ReportColumnHeading2.cs"}, {"SourceFile": "Models\\CGTBALUP\\ReportDetailLine1.cs"}, {"SourceFile": "Models\\CGTBALUP\\ReportDetailLine2.cs"}, {"SourceFile": "Models\\CGTBALUP\\ReportDetailLine3.cs"}, {"SourceFile": "Models\\CGTBALUP\\ReportFile.cs"}, {"SourceFile": "Models\\CGTBALUP\\ReportHeading1.cs"}, {"SourceFile": "Models\\CGTBALUP\\ReportHeading2.cs"}, {"SourceFile": "Models\\CGTBALUP\\ReportHeading3.cs"}, {"SourceFile": "Models\\CGTBALUP\\ReportLastLine.cs"}, {"SourceFile": "Models\\CGTBALUP\\ReportOpt01.cs"}, {"SourceFile": "Models\\CGTBALUP\\ReportOptTable.cs"}, {"SourceFile": "Models\\CGTBALUP\\ReportRecord.cs"}, {"SourceFile": "Models\\CGTBALUP\\ReportRecord2.cs"}, {"SourceFile": "Models\\CGTBALUP\\TimeStamp.cs"}, {"SourceFile": "Models\\CGTBALUP\\WIds.cs"}, {"SourceFile": "Models\\CGTBALUP\\WRecordStore.cs"}, {"SourceFile": "Models\\CGTBALUP\\WsCrt.cs"}, {"SourceFile": "Models\\CGTBALUP\\WsFlags.cs"}, {"SourceFile": "Models\\CGTBALUP\\WsMessages.cs"}, {"SourceFile": "Models\\CGTBALUP\\WsSedol.cs"}, {"SourceFile": "Models\\CGTBALUP\\WStoredFundSedol.cs"}, {"SourceFile": "Models\\CGTBALUP\\WsWhenCompiled.cs"}, {"SourceFile": "Models\\CGTBALUP\\WttOutputRecord.cs"}, {"SourceFile": "Models\\CGTDAILY\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTDAILY\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTDAILY\\CgtskanLinkage.cs"}, {"SourceFile": "Models\\CGTDAILY\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTDAILY\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTDAILY\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTDAILY\\D171Record.cs"}, {"SourceFile": "Models\\CGTDAILY\\DateStamp.cs"}, {"SourceFile": "Models\\CGTDAILY\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTDAILY\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTDAILY\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTDAILY\\Fvar.cs"}, {"SourceFile": "Models\\CGTDAILY\\Gvar.cs"}, {"SourceFile": "Models\\CGTDAILY\\Ivar.cs"}, {"SourceFile": "Models\\CGTDAILY\\ReportFile.cs"}, {"SourceFile": "Models\\CGTDAILY\\WHeaderRecord.cs"}, {"SourceFile": "Models\\CGTDAILY\\WInitialisedRecord.cs"}, {"SourceFile": "Models\\CGTDate2\\Cgtdate2ParDate.cs"}, {"SourceFile": "Models\\CGTDate2\\Gvar.cs"}, {"SourceFile": "Models\\CGTDate2\\Ivar.cs"}, {"SourceFile": "Models\\CGTDel\\CgtdelLinkage.cs"}, {"SourceFile": "Models\\CGTDel\\Gvar.cs"}, {"SourceFile": "Models\\CGTDel\\Ivar.cs"}, {"SourceFile": "Models\\CGTGLREP\\CgtglrepLinkage.cs"}, {"SourceFile": "Models\\CGTGLREP\\CheckCatLinkage.cs"}, {"SourceFile": "Models\\CGTGLREP\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTGLREP\\D34Record.cs"}, {"SourceFile": "Models\\CGTGLREP\\D37Record.cs"}, {"SourceFile": "Models\\CGTGLREP\\D37RecordFormat2.cs"}, {"SourceFile": "Models\\CGTGLREP\\D94Record.cs"}, {"SourceFile": "Models\\CGTGLREP\\D95Record.cs"}, {"SourceFile": "Models\\CGTGLREP\\ElcggioLink1.cs"}, {"SourceFile": "Models\\CGTGLREP\\ElcggioLink2.cs"}, {"SourceFile": "Models\\CGTGLREP\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTGLREP\\Fvar.cs"}, {"SourceFile": "Models\\CGTGLREP\\GetCurrencyLinkage.cs"}, {"SourceFile": "Models\\CGTGLREP\\Gvar.cs"}, {"SourceFile": "Models\\CGTGLREP\\Ivar.cs"}, {"SourceFile": "Models\\CGTGLREP\\WsDate.cs"}, {"SourceFile": "Models\\CGTGLREP\\WsDescriptionTable.cs"}, {"SourceFile": "Models\\CGTGLREP\\WsIssuersNameTable.cs"}, {"SourceFile": "Models\\CGTGLREP\\WsLogMessageArea.cs"}, {"SourceFile": "Models\\CGTGLREP\\WsMessage1.cs"}, {"SourceFile": "Models\\CGTGLREP\\WsReportFile.cs"}, {"SourceFile": "Models\\CGTGLREP\\WsReportItems.cs"}, {"SourceFile": "Models\\CGTGLREP\\WsTime.cs"}, {"SourceFile": "Models\\CGTGLREP\\WsWhenCompiled.cs"}, {"SourceFile": "Models\\CGTHBOSE\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTHBOSE\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTHBOSE\\CgtskanLinkage.cs"}, {"SourceFile": "Models\\CGTHBOSE\\CgttempLinkage.cs"}, {"SourceFile": "Models\\CGTHBOSE\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTHBOSE\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTHBOSE\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTHBOSE\\D163Record.cs"}, {"SourceFile": "Models\\CGTHBOSE\\D164Record.cs"}, {"SourceFile": "Models\\CGTHBOSE\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Fvar.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Gvar.cs"}, {"SourceFile": "Models\\CGTHBOSE\\Ivar.cs"}, {"SourceFile": "Models\\CGTHBOSE\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTHBOSE\\ReportFile.cs"}, {"SourceFile": "Models\\CGTHBOSE\\WInitialisedRecord.cs"}, {"SourceFile": "Models\\CGTHBOSE\\WTempRecord.cs"}, {"SourceFile": "Models\\CGTHBOSE\\WTodaysDate.cs"}, {"SourceFile": "Models\\CGTHBOSE\\WWorkFields.cs"}, {"SourceFile": "Models\\CGTInvert\\Gvar.cs"}, {"SourceFile": "Models\\CGTInvert\\Ivar.cs"}, {"SourceFile": "Models\\CGTInvert\\LKey.cs"}, {"SourceFile": "Models\\CGTInvert\\WWorkArea.cs"}, {"SourceFile": "Models\\CGT1WinLinkage.cs"}, {"SourceFile": "Models\\CgtAbortLinkage.cs"}, {"SourceFile": "Models\\CgtinvrtLinkage.cs"}, {"SourceFile": "Models\\CGTLock\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTLock\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTLock\\CgtlockLinkage.cs"}, {"SourceFile": "Models\\CGTLock\\CgtlogLinkageArea1.cs"}, {"SourceFile": "Models\\CGTLock\\CgtlogLinkageArea2.cs"}, {"SourceFile": "Models\\CGTLock\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTLock\\D4Record.cs"}, {"SourceFile": "Models\\CGTLock\\Gvar.cs"}, {"SourceFile": "Models\\CGTLock\\Ivar.cs"}, {"SourceFile": "Models\\CGTLock\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CgtMessLinkage.cs"}, {"SourceFile": "Models\\CGTNAME\\Gvar.cs"}, {"SourceFile": "Models\\CGTNAME\\IlnameLinkage.cs"}, {"SourceFile": "Models\\CGTNAME\\Ivar.cs"}, {"SourceFile": "Models\\CGTNAME\\WWords.cs"}, {"SourceFile": "Models\\CgtPriceLinkage.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTPrice\\Cgtdate3Linkage.cs"}, {"SourceFile": "Models\\CGTPrice\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTPrice\\CgtpriceLinkage.cs"}, {"SourceFile": "Models\\CGTPrice\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTPrice\\D167Record.cs"}, {"SourceFile": "Models\\CGTPrice\\D3Record.cs"}, {"SourceFile": "Models\\CGTPrice\\D43Record.cs"}, {"SourceFile": "Models\\CGTPrice\\D4Record.cs"}, {"SourceFile": "Models\\CGTPrice\\Gvar.cs"}, {"SourceFile": "Models\\CGTPrice\\Ivar.cs"}, {"SourceFile": "Models\\CGTPrice\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTPrice\\WStorageForPricing.cs"}, {"SourceFile": "Models\\CGTRES\\BusinessUseWorkFields.cs"}, {"SourceFile": "Models\\CGTRES\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTRES\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTRES\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTRES\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTRES\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTRES\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTRES\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTRES\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTRES\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTRES\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTRES\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTRES\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTRES\\CgtinvrtLinkage.cs"}, {"SourceFile": "Models\\CGTRES\\CgtlogLinkageArea1.cs"}, {"SourceFile": "Models\\CGTRES\\CgtlogLinkageArea2.cs"}, {"SourceFile": "Models\\CGTRES\\Cgtsing3Linkage.cs"}, {"SourceFile": "Models\\CGTRES\\CgttempLinkage.cs"}, {"SourceFile": "Models\\CGTRES\\CheckCatLinkage.cs"}, {"SourceFile": "Models\\CGTRES\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTRES\\D112Record.cs"}, {"SourceFile": "Models\\CGTRES\\D37Record.cs"}, {"SourceFile": "Models\\CGTRES\\D37RecordFormat2.cs"}, {"SourceFile": "Models\\CGTRES\\D3Record.cs"}, {"SourceFile": "Models\\CGTRES\\D4Record.cs"}, {"SourceFile": "Models\\CGTRES\\D8Record.cs"}, {"SourceFile": "Models\\CGTRES\\DateStamp.cs"}, {"SourceFile": "Models\\CGTRES\\DbTaperRatesData.cs"}, {"SourceFile": "Models\\CGTRES\\DbTaperRatesTable.cs"}, {"SourceFile": "Models\\CGTRES\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTRES\\Filler178.cs"}, {"SourceFile": "Models\\CGTRES\\Gvar.cs"}, {"SourceFile": "Models\\CGTRES\\Ivar.cs"}, {"SourceFile": "Models\\CGTRES\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTRES\\OutputRecord.cs"}, {"SourceFile": "Models\\CGTRES\\ScheduleDataRecord.cs"}, {"SourceFile": "Models\\CGTRES\\Temp2Fields.cs"}, {"SourceFile": "Models\\CGTRES\\TempFields.cs"}, {"SourceFile": "Models\\CGTRES\\TimeStamp.cs"}, {"SourceFile": "Models\\CGTRES\\WMessage.cs"}, {"SourceFile": "Models\\CGTRES\\WsGeneratedRef.cs"}, {"SourceFile": "Models\\CGTRES\\WsMessages.cs"}, {"SourceFile": "Models\\CGTRES\\WsPrevRecordDetails.cs"}, {"SourceFile": "Models\\CGTRES\\WsScheduleTypesTable.cs"}, {"SourceFile": "Models\\CGTRES\\WsTaperRatesTable.cs"}, {"SourceFile": "Models\\CGTRES\\WsWhenCompiled.cs"}, {"SourceFile": "Models\\CGTRES\\WsWorkTaperDate.cs"}, {"SourceFile": "Models\\CGTRES\\WsXml.cs"}, {"SourceFile": "Models\\CGTRES\\WTransactionCodes.cs"}, {"SourceFile": "Models\\CGTRES\\WTransactionTable.cs"}, {"SourceFile": "Models\\CGTRES\\WTransactionTest.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgt0109ALinkage.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgt0109Linkage.cs"}, {"SourceFile": "Models\\CGTSCANX\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTSCANX\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTSCANX\\CgthelpLinkage.cs"}, {"SourceFile": "Models\\CGTSCANX\\CgtlogLinkageArea1.cs"}, {"SourceFile": "Models\\CGTSCANX\\CgtlogLinkageArea2.cs"}, {"SourceFile": "Models\\CGTSCANX\\CgtmessLinkage.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgtscanx00.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgtscanx00Attr.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgtscanx01.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgtscanx02.cs"}, {"SourceFile": "Models\\CGTSCANX\\Cgtscot1Linkage.cs"}, {"SourceFile": "Models\\CGTSCANX\\CgttempLinkage.cs"}, {"SourceFile": "Models\\CGTSCANX\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSCANX\\DateStamp.cs"}, {"SourceFile": "Models\\CGTSCANX\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTSCANX\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTSCANX\\EqtdebugLinkage.cs"}, {"SourceFile": "Models\\CGTSCANX\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSCANX\\Flags.cs"}, {"SourceFile": "Models\\CGTSCANX\\Fvar.cs"}, {"SourceFile": "Models\\CGTSCANX\\GlRecord.cs"}, {"SourceFile": "Models\\CGTSCANX\\GlRecord2.cs"}, {"SourceFile": "Models\\CGTSCANX\\Gvar.cs"}, {"SourceFile": "Models\\CGTSCANX\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTSCANX\\LkGetCharacter.cs"}, {"SourceFile": "Models\\CGTSCANX\\LkPanels.cs"}, {"SourceFile": "Models\\CGTSCANX\\PanelsParameterBlock.cs"}, {"SourceFile": "Models\\CGTSCANX\\ReportFile.cs"}, {"SourceFile": "Models\\CGTSCANX\\ScheduleRecord.cs"}, {"SourceFile": "Models\\CGTSCANX\\Subscripts.cs"}, {"SourceFile": "Models\\CGTSCANX\\TempFields.cs"}, {"SourceFile": "Models\\CGTSCANX\\TimeStamp.cs"}, {"SourceFile": "Models\\CGTSCANX\\WCrt.cs"}, {"SourceFile": "Models\\CGTSCANX\\WCursorPosition.cs"}, {"SourceFile": "Models\\CGTSCANX\\WFileSplit.cs"}, {"SourceFile": "Models\\CGTSCANX\\WFileTable.cs"}, {"SourceFile": "Models\\CGTSCANX\\WInitialRecord.cs"}, {"SourceFile": "Models\\CGTSCANX\\WInitialRecord2.cs"}, {"SourceFile": "Models\\CGTSCANX\\WsErrors.cs"}, {"SourceFile": "Models\\CGTSCANX\\WsMessages.cs"}, {"SourceFile": "Models\\CGTSCANX\\WsTempFields.cs"}, {"SourceFile": "Models\\CGTSCANX\\WsWhenCompiled.cs"}, {"SourceFile": "Models\\CGTSCANX\\WtDescriptionsD.cs"}, {"SourceFile": "Models\\CGTSched\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTSched\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTSched\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSched\\D100PrintRecord.cs"}, {"SourceFile": "Models\\CGTSched\\D102PrintRecord.cs"}, {"SourceFile": "Models\\CGTSched\\D1Detail.cs"}, {"SourceFile": "Models\\CGTSched\\D21PrintRecord.cs"}, {"SourceFile": "Models\\CGTSched\\D22PrintRecord.cs"}, {"SourceFile": "Models\\CGTSched\\D2Detail.cs"}, {"SourceFile": "Models\\CGTSched\\D82PrintRecord.cs"}, {"SourceFile": "Models\\CGTSched\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSched\\Filler60.cs"}, {"SourceFile": "Models\\CGTSched\\Fvar.cs"}, {"SourceFile": "Models\\CGTSched\\Gvar.cs"}, {"SourceFile": "Models\\CGTSched\\H1NHeading.cs"}, {"SourceFile": "Models\\CGTSched\\H1RHeading.cs"}, {"SourceFile": "Models\\CGTSched\\H1THeading.cs"}, {"SourceFile": "Models\\CGTSched\\H1UHeading.cs"}, {"SourceFile": "Models\\CGTSched\\H1XHeading.cs"}, {"SourceFile": "Models\\CGTSched\\H2Heading.cs"}, {"SourceFile": "Models\\CGTSched\\Ivar.cs"}, {"SourceFile": "Models\\CGTSched\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTSched\\PrintRecord.cs"}, {"SourceFile": "Models\\CGTSched\\PrintRecTable.cs"}, {"SourceFile": "Models\\CGTSCOT1\\AcquisitionIds.cs"}, {"SourceFile": "Models\\CGTSCOT1\\BalanceIds.cs"}, {"SourceFile": "Models\\CGTSCOT1\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTSCOT1\\CgtlogLinkageArea1.cs"}, {"SourceFile": "Models\\CGTSCOT1\\CgtlogLinkageArea2.cs"}, {"SourceFile": "Models\\CGTSCOT1\\Cgtscot1Linkage.cs"}, {"SourceFile": "Models\\CGTSCOT1\\D75Record.cs"}, {"SourceFile": "Models\\CGTSCOT1\\DateStamp.cs"}, {"SourceFile": "Models\\CGTSCOT1\\DisposalIds.cs"}, {"SourceFile": "Models\\CGTSCOT1\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTSCOT1\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTSCOT1\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSCOT1\\Filler72.cs"}, {"SourceFile": "Models\\CGTSCOT1\\Fvar.cs"}, {"SourceFile": "Models\\CGTSCOT1\\Gvar.cs"}, {"SourceFile": "Models\\CGTSCOT1\\HeaderIds.cs"}, {"SourceFile": "Models\\CGTSCOT1\\Ivar.cs"}, {"SourceFile": "Models\\CGTSCOT1\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTSCOT1\\ReportFile.cs"}, {"SourceFile": "Models\\CGTSCOT1\\TimeStamp.cs"}, {"SourceFile": "Models\\CGTSCOT1\\UGainsDataRecord.cs"}, {"SourceFile": "Models\\CGTSCOT1\\WIds.cs"}, {"SourceFile": "Models\\CGTSCOT1\\WInitialRecord.cs"}, {"SourceFile": "Models\\CGTSCOT1\\WsMessages.cs"}, {"SourceFile": "Models\\CGTSCOT1\\WsWhenCompiled.cs"}, {"SourceFile": "Models\\CGTSCOT2\\AcquisitionIds.cs"}, {"SourceFile": "Models\\CGTSCOT2\\BalanceIds.cs"}, {"SourceFile": "Models\\CGTSCOT2\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTSCOT2\\CgtlogLinkageArea1.cs"}, {"SourceFile": "Models\\CGTSCOT2\\CgtlogLinkageArea2.cs"}, {"SourceFile": "Models\\CGTSCOT2\\Cgtscot2Linkage.cs"}, {"SourceFile": "Models\\CGTSCOT2\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSCOT2\\DateStamp.cs"}, {"SourceFile": "Models\\CGTSCOT2\\DisposalIds.cs"}, {"SourceFile": "Models\\CGTSCOT2\\DTsbRecord.cs"}, {"SourceFile": "Models\\CGTSCOT2\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTSCOT2\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTSCOT2\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSCOT2\\Filler48.cs"}, {"SourceFile": "Models\\CGTSCOT2\\Fvar.cs"}, {"SourceFile": "Models\\CGTSCOT2\\Gvar.cs"}, {"SourceFile": "Models\\CGTSCOT2\\HeaderIds.cs"}, {"SourceFile": "Models\\CGTSCOT2\\Ivar.cs"}, {"SourceFile": "Models\\CGTSCOT2\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTSCOT2\\ReportFile.cs"}, {"SourceFile": "Models\\CGTSCOT2\\TimeStamp.cs"}, {"SourceFile": "Models\\CGTSCOT2\\UGainsDataRecord.cs"}, {"SourceFile": "Models\\CGTSCOT2\\WIds.cs"}, {"SourceFile": "Models\\CGTSCOT2\\WsMessages.cs"}, {"SourceFile": "Models\\CGTSCOT2\\WsWhenCompiled.cs"}, {"SourceFile": "Models\\CGTSING4\\BusienssUseWorkFields.cs"}, {"SourceFile": "Models\\CGTSING4\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTSING4\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTSING4\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTSING4\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTSING4\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTSING4\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTSING4\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTSING4\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTSING4\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTSING4\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTSING4\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTSING4\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTSING4\\CgtinvrtLinkage.cs"}, {"SourceFile": "Models\\CGTSING4\\CgtlogLinkageArea1.cs"}, {"SourceFile": "Models\\CGTSING4\\CgtlogLinkageArea2.cs"}, {"SourceFile": "Models\\CGTSING4\\Cgtsing3Linkage.cs"}, {"SourceFile": "Models\\CGTSING4\\CgttempLinkage.cs"}, {"SourceFile": "Models\\CGTSING4\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSING4\\D110Record.cs"}, {"SourceFile": "Models\\CGTSING4\\D111Record.cs"}, {"SourceFile": "Models\\CGTSING4\\D112Record.cs"}, {"SourceFile": "Models\\CGTSING4\\D157HeaderRecord.cs"}, {"SourceFile": "Models\\CGTSING4\\D157Record.cs"}, {"SourceFile": "Models\\CGTSING4\\D157RecordN.cs"}, {"SourceFile": "Models\\CGTSING4\\D157TrailerRecord.cs"}, {"SourceFile": "Models\\CGTSING4\\D158HeaderRecord.cs"}, {"SourceFile": "Models\\CGTSING4\\D158Record.cs"}, {"SourceFile": "Models\\CGTSING4\\D158RecordN.cs"}, {"SourceFile": "Models\\CGTSING4\\D158TrailerRecord.cs"}, {"SourceFile": "Models\\CGTSING4\\D37Record.cs"}, {"SourceFile": "Models\\CGTSING4\\D37RecordFormat2.cs"}, {"SourceFile": "Models\\CGTSING4\\D3Record.cs"}, {"SourceFile": "Models\\CGTSING4\\D8Record.cs"}, {"SourceFile": "Models\\CGTSING4\\DateStamp.cs"}, {"SourceFile": "Models\\CGTSING4\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTSING4\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTSING4\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSING4\\ExportFile.cs"}, {"SourceFile": "Models\\CGTSING4\\Filler13.cs"}, {"SourceFile": "Models\\CGTSING4\\Filler14.cs"}, {"SourceFile": "Models\\CGTSING4\\Fvar.cs"}, {"SourceFile": "Models\\CGTSING4\\Gvar.cs"}, {"SourceFile": "Models\\CGTSING4\\Ivar.cs"}, {"SourceFile": "Models\\CGTSING4\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTSING4\\LossExportFile.cs"}, {"SourceFile": "Models\\CGTSING4\\ReportColHeader1.cs"}, {"SourceFile": "Models\\CGTSING4\\ReportColHeader2.cs"}, {"SourceFile": "Models\\CGTSING4\\ReportColHeader3.cs"}, {"SourceFile": "Models\\CGTSING4\\ReportColHeader4.cs"}, {"SourceFile": "Models\\CGTSING4\\ReportConstants.cs"}, {"SourceFile": "Models\\CGTSING4\\ReportDetail.cs"}, {"SourceFile": "Models\\CGTSING4\\ReportFields.cs"}, {"SourceFile": "Models\\CGTSING4\\ReportFile.cs"}, {"SourceFile": "Models\\CGTSING4\\ReportFooter.cs"}, {"SourceFile": "Models\\CGTSING4\\ReportFooter2.cs"}, {"SourceFile": "Models\\CGTSING4\\ReportGainTotalText.cs"}, {"SourceFile": "Models\\CGTSING4\\ReportHeader1.cs"}, {"SourceFile": "Models\\CGTSING4\\ReportHeader2.cs"}, {"SourceFile": "Models\\CGTSING4\\ReportHeader3.cs"}, {"SourceFile": "Models\\CGTSING4\\ReportLossTotalText.cs"}, {"SourceFile": "Models\\CGTSING4\\ReportNetGainTotalText.cs"}, {"SourceFile": "Models\\CGTSING4\\ScheduleDataRecord.cs"}, {"SourceFile": "Models\\CGTSING4\\TempFields.cs"}, {"SourceFile": "Models\\CGTSING4\\TimeStamp.cs"}, {"SourceFile": "Models\\CGTSING4\\WsAllowancesTable.cs"}, {"SourceFile": "Models\\CGTSING4\\WsDbDisposals.cs"}, {"SourceFile": "Models\\CGTSING4\\WsDbLosses.cs"}, {"SourceFile": "Models\\CGTSING4\\WsDbLossesN.cs"}, {"SourceFile": "Models\\CGTSING4\\WsDbLossTable.cs"}, {"SourceFile": "Models\\CGTSING4\\WsLossTextTable.cs"}, {"SourceFile": "Models\\CGTSING4\\WsMessages.cs"}, {"SourceFile": "Models\\CGTSING4\\WsPrevScheduleRecDetails.cs"}, {"SourceFile": "Models\\CGTSING4\\WsTaperRatesTable.cs"}, {"SourceFile": "Models\\CGTSING4\\WsWhenCompiled.cs"}, {"SourceFile": "Models\\CGTSING4\\WsWorkAcquisitionDate.cs"}, {"SourceFile": "Models\\CGTSKAN\\AcquisitionIds.cs"}, {"SourceFile": "Models\\CGTSKAN\\BalanceIds.cs"}, {"SourceFile": "Models\\CGTSKAN\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTSKAN\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTSKAN\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTSKAN\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTSKAN\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTSKAN\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTSKAN\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTSKAN\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTSKAN\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTSKAN\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTSKAN\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTSKAN\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTSKAN\\CgtlogLinkageArea1.cs"}, {"SourceFile": "Models\\CGTSKAN\\CgtlogLinkageArea2.cs"}, {"SourceFile": "Models\\CGTSKAN\\CgtskanLinkage.cs"}, {"SourceFile": "Models\\CGTSKAN\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSKAN\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTSKAN\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTSKAN\\D37Record.cs"}, {"SourceFile": "Models\\CGTSKAN\\D37RecordFormat2.cs"}, {"SourceFile": "Models\\CGTSKAN\\DateStamp.cs"}, {"SourceFile": "Models\\CGTSKAN\\DisposalIds.cs"}, {"SourceFile": "Models\\CGTSKAN\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTSKAN\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTSKAN\\Filler115.cs"}, {"SourceFile": "Models\\CGTSKAN\\GetCurrencyLinkage.cs"}, {"SourceFile": "Models\\CGTSKAN\\Gvar.cs"}, {"SourceFile": "Models\\CGTSKAN\\HeaderIds.cs"}, {"SourceFile": "Models\\CGTSKAN\\Ivar.cs"}, {"SourceFile": "Models\\CGTSKAN\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTSKAN\\TimeStamp.cs"}, {"SourceFile": "Models\\CGTSKAN\\WIds.cs"}, {"SourceFile": "Models\\CGTSKAN\\WMasterRecordCache.cs"}, {"SourceFile": "Models\\CGTSKAN\\WsCrt.cs"}, {"SourceFile": "Models\\CGTSKAN\\WsFlags.cs"}, {"SourceFile": "Models\\CGTSKAN\\WsMessages.cs"}, {"SourceFile": "Models\\CGTSKAN\\WsTempD13Fields.cs"}, {"SourceFile": "Models\\CGTSKAN\\WsWhenCompiled.cs"}, {"SourceFile": "Models\\CGTSING2\\AcquisitionIds.cs"}, {"SourceFile": "Models\\CGTSING2\\BalanceIds.cs"}, {"SourceFile": "Models\\CGTSING2\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTSING2\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTSING2\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTSING2\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTSING2\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTSING2\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTSING2\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTSING2\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTSING2\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTSING2\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTSING2\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTSING2\\CgtskanLinkage.cs"}, {"SourceFile": "Models\\CGTSING2\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSING2\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTSING2\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTSING2\\D77Record.cs"}, {"SourceFile": "Models\\CGTSING2\\DisposalIds.cs"}, {"SourceFile": "Models\\CGTSING2\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTSING2\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTSING2\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSING2\\Filler107.cs"}, {"SourceFile": "Models\\CGTSING2\\Fvar.cs"}, {"SourceFile": "Models\\CGTSING2\\Gvar.cs"}, {"SourceFile": "Models\\CGTSING2\\HeaderIds.cs"}, {"SourceFile": "Models\\CGTSING2\\Ivar.cs"}, {"SourceFile": "Models\\CGTSING2\\ReportFile.cs"}, {"SourceFile": "Models\\CGTSING2\\WDisposalDetails.cs"}, {"SourceFile": "Models\\CGTSING2\\WIds.cs"}, {"SourceFile": "Models\\CGTSING2\\WInitialisedRecord.cs"}, {"SourceFile": "Models\\CGTSING2\\WTodaysDate.cs"}, {"SourceFile": "Models\\CGTSING2\\WTrancheDetails.cs"}, {"SourceFile": "Models\\CGTSING3\\AcquisitionIds.cs"}, {"SourceFile": "Models\\CGTSING3\\BalanceIds.cs"}, {"SourceFile": "Models\\CGTSING3\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTSING3\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTSING3\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTSING3\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTSING3\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTSING3\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTSING3\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTSING3\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTSING3\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTSING3\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTSING3\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTSING3\\CgtlogLinkageArea1.cs"}, {"SourceFile": "Models\\CGTSING3\\CgtlogLinkageArea2.cs"}, {"SourceFile": "Models\\CGTSING3\\Cgtsing3Linkage.cs"}, {"SourceFile": "Models\\CGTSING3\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSING3\\DateStamp.cs"}, {"SourceFile": "Models\\CGTSING3\\DisposalIds.cs"}, {"SourceFile": "Models\\CGTSING3\\DSf3Record.cs"}, {"SourceFile": "Models\\CGTSING3\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTSING3\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTSING3\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSING3\\Filler151.cs"}, {"SourceFile": "Models\\CGTSING3\\Fvar.cs"}, {"SourceFile": "Models\\CGTSING3\\Gvar.cs"}, {"SourceFile": "Models\\CGTSING3\\HeaderIds.cs"}, {"SourceFile": "Models\\CGTSING3\\Ivar.cs"}, {"SourceFile": "Models\\CGTSING3\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTSING3\\ReportFile.cs"}, {"SourceFile": "Models\\CGTSING3\\ScheduleDataRecord.cs"}, {"SourceFile": "Models\\CGTSING3\\TimeStamp.cs"}, {"SourceFile": "Models\\CGTSING3\\WIds.cs"}, {"SourceFile": "Models\\CGTSING3\\WsMessages.cs"}, {"SourceFile": "Models\\CGTSING3\\WsWhenCompiled.cs"}, {"SourceFile": "Models\\CGTSINGF\\AcquisitionIds.cs"}, {"SourceFile": "Models\\CGTSINGF\\BalanceIds.cs"}, {"SourceFile": "Models\\CGTSINGF\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTSINGF\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTSINGF\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTSINGF\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTSINGF\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTSINGF\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTSINGF\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTSINGF\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTSINGF\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTSINGF\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTSINGF\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTSINGF\\CgtskanLinkage.cs"}, {"SourceFile": "Models\\CGTSINGF\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSINGF\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTSINGF\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTSINGF\\D76Record.cs"}, {"SourceFile": "Models\\CGTSINGF\\DisposalIds.cs"}, {"SourceFile": "Models\\CGTSINGF\\DTsbMRecord.cs"}, {"SourceFile": "Models\\CGTSINGF\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTSINGF\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTSINGF\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSINGF\\Filler84.cs"}, {"SourceFile": "Models\\CGTSINGF\\Fvar.cs"}, {"SourceFile": "Models\\CGTSINGF\\Gvar.cs"}, {"SourceFile": "Models\\CGTSINGF\\HeaderIds.cs"}, {"SourceFile": "Models\\CGTSINGF\\Ivar.cs"}, {"SourceFile": "Models\\CGTSINGF\\ReportFile.cs"}, {"SourceFile": "Models\\CGTSINGF\\WIds.cs"}, {"SourceFile": "Models\\CGTSKR1\\AcquisitionIds.cs"}, {"SourceFile": "Models\\CGTSKR1\\BalanceIds.cs"}, {"SourceFile": "Models\\CGTSKR1\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTSKR1\\CgtskanLinkage.cs"}, {"SourceFile": "Models\\CGTSKR1\\CheckCatLinkage.cs"}, {"SourceFile": "Models\\CGTSKR1\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSKR1\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTSKR1\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTSKR1\\D72Record.cs"}, {"SourceFile": "Models\\CGTSKR1\\DateStamp.cs"}, {"SourceFile": "Models\\CGTSKR1\\DisposalIds.cs"}, {"SourceFile": "Models\\CGTSKR1\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSKR1\\Filler106.cs"}, {"SourceFile": "Models\\CGTSKR1\\Filler107.cs"}, {"SourceFile": "Models\\CGTSKR1\\Fvar.cs"}, {"SourceFile": "Models\\CGTSKR1\\Gvar.cs"}, {"SourceFile": "Models\\CGTSKR1\\HeaderIds.cs"}, {"SourceFile": "Models\\CGTSKR1\\Ivar.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportColHd.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportD13Count.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportDtl.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportDtlCount.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportFile.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportFinal.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportFund.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportHd1.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportHd2.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportHd3.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportLastLine.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportQuitLine.cs"}, {"SourceFile": "Models\\CGTSKR1\\ReportSedol.cs"}, {"SourceFile": "Models\\CGTSKR1\\WIds.cs"}, {"SourceFile": "Models\\CGTSKR2\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTSKR2\\CgtskanLinkage.cs"}, {"SourceFile": "Models\\CGTSKR2\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSKR2\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTSKR2\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTSKR2\\D73Record.cs"}, {"SourceFile": "Models\\CGTSKR2\\DateStamp.cs"}, {"SourceFile": "Models\\CGTSKR2\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSKR2\\Fvar.cs"}, {"SourceFile": "Models\\CGTSKR2\\Gvar.cs"}, {"SourceFile": "Models\\CGTSKR2\\Ivar.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportColHd.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportD13Count.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportDtlCount.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportFile.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportFinal.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportFund.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportHd1.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportHd2.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportHd2A.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportHd3.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportLastLine.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportQuitLine.cs"}, {"SourceFile": "Models\\CGTSKR2\\ReportSdl.cs"}, {"SourceFile": "Models\\CGTSKR2\\WReportFields.cs"}, {"SourceFile": "Models\\CGTSKR3\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTSKR3\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTSKR3\\CgtskanLinkage.cs"}, {"SourceFile": "Models\\CGTSKR3\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSKR3\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTSKR3\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTSKR3\\D74Record.cs"}, {"SourceFile": "Models\\CGTSKR3\\DateStamp.cs"}, {"SourceFile": "Models\\CGTSKR3\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSKR3\\Fvar.cs"}, {"SourceFile": "Models\\CGTSKR3\\Gvar.cs"}, {"SourceFile": "Models\\CGTSKR3\\Ivar.cs"}, {"SourceFile": "Models\\CGTSKR3\\ReportFile.cs"}, {"SourceFile": "Models\\CGTSKR3\\WInitialRecord.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\Cgtsplit2012.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\CgtsplitLinkage.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\D37Record.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\D37RecordFormat2.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\D45BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\D45Record.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\D4Record.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\ElcggioLink1.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\ElcggioLink2.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\Fvar.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\Gvar.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\Ivar.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\WCurentLine.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\WFundSedol.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\WHeader.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\WRedefinedFields.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\WsFlags.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\WsFundSedolKey.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\WsMessgaes.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\WsMiscellaneous.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\WsPropValues.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\WsSummFundSedolKey.cs"}, {"SourceFile": "Models\\CGTSPLIT2012\\WSummaryCode.cs"}, {"SourceFile": "Models\\CGTSPLIT\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\CGTSPLIT\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\CGTSPLIT\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\CGTSPLIT\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\CGTSPLIT\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\CGTSPLIT\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\CGTSPLIT\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\CGTSPLIT\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\CGTSPLIT\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\CGTSPLIT\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\CGTSPLIT\\Cgtsplit.cs"}, {"SourceFile": "Models\\CGTSPLIT\\CgtsplitLinkage.cs"}, {"SourceFile": "Models\\CGTSPLIT\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSPLIT\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTSPLIT\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTSPLIT\\D45BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTSPLIT\\D45Record.cs"}, {"SourceFile": "Models\\CGTSPLIT\\D4Record.cs"}, {"SourceFile": "Models\\CGTSPLIT\\ElcggioLink1.cs"}, {"SourceFile": "Models\\CGTSPLIT\\ElcggioLink2.cs"}, {"SourceFile": "Models\\CGTSPLIT\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTSPLIT\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTSPLIT\\Fvar.cs"}, {"SourceFile": "Models\\CGTSPLIT\\Gvar.cs"}, {"SourceFile": "Models\\CGTSPLIT\\Ivar.cs"}, {"SourceFile": "Models\\CGTSPLIT\\WsFlags.cs"}, {"SourceFile": "Models\\CGTSPLIT\\WsFundSedolKey.cs"}, {"SourceFile": "Models\\CGTSPLIT\\WsMessgaes.cs"}, {"SourceFile": "Models\\CGTSPLIT\\WsMiscellaneous.cs"}, {"SourceFile": "Models\\CGTSPLIT\\WsPropValues.cs"}, {"SourceFile": "Models\\CGTSPLIT\\WsSummFundSedolKey.cs"}, {"SourceFile": "Models\\CGTSPLIT\\WsTestKey.cs"}, {"SourceFile": "Models\\CGTSKRL\\AcquisitionIds.cs"}, {"SourceFile": "Models\\CGTSKRL\\BalanceIds.cs"}, {"SourceFile": "Models\\CGTSKRL\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\CGTSKRL\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\CGTSKRL\\CgtlogLinkageArea1.cs"}, {"SourceFile": "Models\\CGTSKRL\\CgtlogLinkageArea2.cs"}, {"SourceFile": "Models\\CGTSKRL\\CgtskanLinkage.cs"}, {"SourceFile": "Models\\CGTSKRL\\Cgtskrl00.cs"}, {"SourceFile": "Models\\CGTSKRL\\Cgtskrl00Attr.cs"}, {"SourceFile": "Models\\CGTSKRL\\Cgtskrl01.cs"}, {"SourceFile": "Models\\CGTSKRL\\Cgtskrl02.cs"}, {"SourceFile": "Models\\CGTSKRL\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTSKRL\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\CGTSKRL\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\CGTSKRL\\D37Record.cs"}, {"SourceFile": "Models\\CGTSKRL\\D37RecordFormat2.cs"}, {"SourceFile": "Models\\CGTSKRL\\DateStamp.cs"}, {"SourceFile": "Models\\CGTSKRL\\DisposalIds.cs"}, {"SourceFile": "Models\\CGTSKRL\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\CGTSKRL\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\CGTSKRL\\EqtdebugLinkage.cs"}, {"SourceFile": "Models\\CGTSKRL\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTSKRL\\F1Record.cs"}, {"SourceFile": "Models\\CGTSKRL\\FileHandler.cs"}, {"SourceFile": "Models\\CGTSKRL\\Filler101.cs"}, {"SourceFile": "Models\\CGTSKRL\\Filler103.cs"}, {"SourceFile": "Models\\CGTSKRL\\Fvar.cs"}, {"SourceFile": "Models\\CGTSKRL\\Gvar.cs"}, {"SourceFile": "Models\\CGTSKRL\\HeaderIds.cs"}, {"SourceFile": "Models\\CGTSKRL\\Ivar.cs"}, {"SourceFile": "Models\\CGTSKRL\\LFileRecordArea.cs"}, {"SourceFile": "Models\\CGTSKRL\\LkPanels.cs"}, {"SourceFile": "Models\\CGTSKRL\\PanelsParameterBlock.cs"}, {"SourceFile": "Models\\CGTSKRL\\TimeStamp.cs"}, {"SourceFile": "Models\\CGTSKRL\\W1Flags.cs"}, {"SourceFile": "Models\\CGTSKRL\\W2CrtControl.cs"}, {"SourceFile": "Models\\CGTSKRL\\W3File.cs"}, {"SourceFile": "Models\\CGTSKRL\\W4Subscripts.cs"}, {"SourceFile": "Models\\CGTSKRL\\W5FileFlags.cs"}, {"SourceFile": "Models\\CGTSKRL\\W6TempFields.cs"}, {"SourceFile": "Models\\CGTSKRL\\WIds.cs"}, {"SourceFile": "Models\\CGTSKRL\\WsMessages.cs"}, {"SourceFile": "Models\\CGTSKRL\\WsWhenCompiled.cs"}, {"SourceFile": "Models\\CgtstatLinkage.cs"}, {"SourceFile": "Models\\CGTStat\\CgtstatLinkage.cs"}, {"SourceFile": "Models\\CGTStat\\CommonLinkage.cs"}, {"SourceFile": "Models\\CGTStat\\FileStatus.cs"}, {"SourceFile": "Models\\CGTStat\\Gvar.cs"}, {"SourceFile": "Models\\CGTStat\\Ivar.cs"}, {"SourceFile": "Models\\CGTStat\\MessageTabAndBrew.cs"}, {"SourceFile": "Models\\CGTStat\\MessageTable.cs"}, {"SourceFile": "Models\\CGTStat\\WBinaryField.cs"}, {"SourceFile": "Models\\CGTStat\\WDefaultMessages.cs"}, {"SourceFile": "Models\\CGTStat\\WStatus.cs"}, {"SourceFile": "Models\\CGTStat\\WUnknown.cs"}, {"SourceFile": "Models\\CGTTEMP2\\CgtdelLinkage.cs"}, {"SourceFile": "Models\\CGTTEMP2\\CgttempLinkage.cs"}, {"SourceFile": "Models\\CGTTEMP2\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTTEMP2\\Fvar.cs"}, {"SourceFile": "Models\\CGTTEMP2\\Gvar.cs"}, {"SourceFile": "Models\\CGTTEMP2\\Ivar.cs"}, {"SourceFile": "Models\\CGTTEMP2\\TempFile.cs"}, {"SourceFile": "Models\\CGTTEMP2\\TempRecord.cs"}, {"SourceFile": "Models\\CGTTEMP2\\TimingLinkage.cs"}, {"SourceFile": "Models\\CGTTemp\\CgtdelLinkage.cs"}, {"SourceFile": "Models\\CGTTemp\\CgttempLinkage.cs"}, {"SourceFile": "Models\\CGTTemp\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\CGTTemp\\Fvar.cs"}, {"SourceFile": "Models\\CGTTemp\\Gvar.cs"}, {"SourceFile": "Models\\CGTTemp\\Ivar.cs"}, {"SourceFile": "Models\\CGTTemp\\TempFile.cs"}, {"SourceFile": "Models\\CGTTemp\\TempRecord.cs"}, {"SourceFile": "Models\\CGTTemp\\TimingLinkage.cs"}, {"SourceFile": "Models\\CheckCatLinkage.cs"}, {"SourceFile": "Models\\CobolConstants.cs"}, {"SourceFile": "Models\\CommonLinkage.cs"}, {"SourceFile": "Models\\Common\\CommonUtils.cs"}, {"SourceFile": "Models\\Common\\EquityGlobalParms.cs"}, {"SourceFile": "Models\\Common\\GlobalVars.cs"}, {"SourceFile": "Models\\D133Record.cs"}, {"SourceFile": "Models\\D13BalAcqDispRecord.cs"}, {"SourceFile": "Models\\D13CostElement.cs"}, {"SourceFile": "Models\\D13RecordHolder.cs"}, {"SourceFile": "Models\\D13SedolHeaderRecord.cs"}, {"SourceFile": "Models\\D37DetailRecord.cs"}, {"SourceFile": "Models\\D37HeaderRecord.cs"}, {"SourceFile": "Models\\D37Record.cs"}, {"SourceFile": "Models\\D37RecordFormat2.cs"}, {"SourceFile": "Models\\D8Record.cs"}, {"SourceFile": "Models\\D8WindowsRecord.cs"}, {"SourceFile": "Models\\ELCGGIO\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\ELCGGIO\\CommonLinkage.cs"}, {"SourceFile": "Models\\ELCGGIO\\ElcggioLink1.cs"}, {"SourceFile": "Models\\ELCGGIO\\ElcggioLink2.cs"}, {"SourceFile": "Models\\ELCGGIO\\Gvar.cs"}, {"SourceFile": "Models\\ELCGGIO\\Ivar.cs"}, {"SourceFile": "Models\\ELCGGIO\\LFileRecordArea.cs"}, {"SourceFile": "Models\\ELCGTK25\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\ELCGTK25\\CheckCatLinkage.cs"}, {"SourceFile": "Models\\ELCGTK25\\CommonLinkage.cs"}, {"SourceFile": "Models\\ELCGTK25\\D000Detail.cs"}, {"SourceFile": "Models\\ELCGTK25\\D8Record.cs"}, {"SourceFile": "Models\\ELCGTK25\\E000Detail.cs"}, {"SourceFile": "Models\\ELCGTK25\\EBondDetail.cs"}, {"SourceFile": "Models\\ELCGTK25\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\ELCGTK25\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\ELCGTK25\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\ELCGTK25\\F000Footing.cs"}, {"SourceFile": "Models\\ELCGTK25\\F200Footing.cs"}, {"SourceFile": "Models\\ELCGTK25\\Filler107.cs"}, {"SourceFile": "Models\\ELCGTK25\\Fvar.cs"}, {"SourceFile": "Models\\ELCGTK25\\G000Detail.cs"}, {"SourceFile": "Models\\ELCGTK25\\G000Group.cs"}, {"SourceFile": "Models\\ELCGTK25\\Gvar.cs"}, {"SourceFile": "Models\\ELCGTK25\\H000Heading.cs"}, {"SourceFile": "Models\\ELCGTK25\\Ivar.cs"}, {"SourceFile": "Models\\ELCGTK25\\LastDetails.cs"}, {"SourceFile": "Models\\ELCGTK25\\LFileRecordArea.cs"}, {"SourceFile": "Models\\ELCGTK25\\N000Country.cs"}, {"SourceFile": "Models\\ELCGTK25\\NewFields.cs"}, {"SourceFile": "Models\\ELCGTK25\\ParmInfo.cs"}, {"SourceFile": "Models\\ELCGTK25\\PrintRecord.cs"}, {"SourceFile": "Models\\ELCGTK25\\ReportFileRecord.cs"}, {"SourceFile": "Models\\ELCGTK25\\ScheduleRecord.cs"}, {"SourceFile": "Models\\ELCGTK25\\T000Totals.cs"}, {"SourceFile": "Models\\ELCGTK25\\WctCountryTable.cs"}, {"SourceFile": "Models\\ELCGTK25\\WftFundRecord.cs"}, {"SourceFile": "Models\\ELCGTK25\\WgtGroupTable.cs"}, {"SourceFile": "Models\\ELCGTK25\\WmtMonthTable.cs"}, {"SourceFile": "Models\\ELCGTK25\\WsCioLink.cs"}, {"SourceFile": "Models\\ELCGTK25\\WsGeneralWork.cs"}, {"SourceFile": "Models\\ELCGTK25\\WswSwitches.cs"}, {"SourceFile": "Models\\ELCGTK25\\WtsTotals.cs"}, {"SourceFile": "Models\\ELCGTK25\\WwaWorkareas.cs"}, {"SourceFile": "Models\\ELCGTK26\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\ELCGTK26\\CheckCatLinkage.cs"}, {"SourceFile": "Models\\ELCGTK26\\CommonLinkage.cs"}, {"SourceFile": "Models\\ELCGTK26\\D000Detail.cs"}, {"SourceFile": "Models\\ELCGTK26\\D100Dashes.cs"}, {"SourceFile": "Models\\ELCGTK26\\D200Dashes.cs"}, {"SourceFile": "Models\\ELCGTK26\\D37Record.cs"}, {"SourceFile": "Models\\ELCGTK26\\D37RecordFormat2.cs"}, {"SourceFile": "Models\\ELCGTK26\\D8Record.cs"}, {"SourceFile": "Models\\ELCGTK26\\Elcgtk26.cs"}, {"SourceFile": "Models\\ELCGTK26\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\ELCGTK26\\F200Footing.cs"}, {"SourceFile": "Models\\ELCGTK26\\Filler101.cs"}, {"SourceFile": "Models\\ELCGTK26\\Filler174.cs"}, {"SourceFile": "Models\\ELCGTK26\\Filler193.cs"}, {"SourceFile": "Models\\ELCGTK26\\Fvar.cs"}, {"SourceFile": "Models\\ELCGTK26\\G000Detail.cs"}, {"SourceFile": "Models\\ELCGTK26\\GetCurrencyLinkage.cs"}, {"SourceFile": "Models\\ELCGTK26\\Gvar.cs"}, {"SourceFile": "Models\\ELCGTK26\\H000Heading.cs"}, {"SourceFile": "Models\\ELCGTK26\\H100Heading.cs"}, {"SourceFile": "Models\\ELCGTK26\\H200Heading.cs"}, {"SourceFile": "Models\\ELCGTK26\\H300Heading.cs"}, {"SourceFile": "Models\\ELCGTK26\\H400Heading.cs"}, {"SourceFile": "Models\\ELCGTK26\\H500Heading.cs"}, {"SourceFile": "Models\\ELCGTK26\\H600Heading.cs"}, {"SourceFile": "Models\\ELCGTK26\\H700Heading.cs"}, {"SourceFile": "Models\\ELCGTK26\\Ivar.cs"}, {"SourceFile": "Models\\ELCGTK26\\LFileRecordArea.cs"}, {"SourceFile": "Models\\ELCGTK26\\NewFields.cs"}, {"SourceFile": "Models\\ELCGTK26\\ParmInfo.cs"}, {"SourceFile": "Models\\ELCGTK26\\PRecord.cs"}, {"SourceFile": "Models\\ELCGTK26\\PrintRecord.cs"}, {"SourceFile": "Models\\ELCGTK26\\ReportFileRecord.cs"}, {"SourceFile": "Models\\ELCGTK26\\ScheduleRecord.cs"}, {"SourceFile": "Models\\ELCGTK26\\T000Totals.cs"}, {"SourceFile": "Models\\ELCGTK26\\WdtDescriptions1.cs"}, {"SourceFile": "Models\\ELCGTK26\\WdtDescriptions2.cs"}, {"SourceFile": "Models\\ELCGTK26\\WDtpFields.cs"}, {"SourceFile": "Models\\ELCGTK26\\WftFundRecord.cs"}, {"SourceFile": "Models\\ELCGTK26\\WmtMonthTable.cs"}, {"SourceFile": "Models\\ELCGTK26\\WsCioLink.cs"}, {"SourceFile": "Models\\ELCGTK26\\WsGeneralWork.cs"}, {"SourceFile": "Models\\ELCGTK26\\WswSwitches.cs"}, {"SourceFile": "Models\\ELCGTK26\\WtsTotals.cs"}, {"SourceFile": "Models\\ELCGTK26\\WwaWorkareas.cs"}, {"SourceFile": "Models\\ELCGTK30\\AcquisitionIds.cs"}, {"SourceFile": "Models\\ELCGTK30\\BalanceIds.cs"}, {"SourceFile": "Models\\ELCGTK30\\C1DetailsTable.cs"}, {"SourceFile": "Models\\ELCGTK30\\C2DetailsTable.cs"}, {"SourceFile": "Models\\ELCGTK30\\C3DetailsTable.cs"}, {"SourceFile": "Models\\ELCGTK30\\Cgtdate2LinkageDate1.cs"}, {"SourceFile": "Models\\ELCGTK30\\Cgtdate2LinkageDate10.cs"}, {"SourceFile": "Models\\ELCGTK30\\Cgtdate2LinkageDate2.cs"}, {"SourceFile": "Models\\ELCGTK30\\Cgtdate2LinkageDate3.cs"}, {"SourceFile": "Models\\ELCGTK30\\Cgtdate2LinkageDate4.cs"}, {"SourceFile": "Models\\ELCGTK30\\Cgtdate2LinkageDate5.cs"}, {"SourceFile": "Models\\ELCGTK30\\Cgtdate2LinkageDate6.cs"}, {"SourceFile": "Models\\ELCGTK30\\Cgtdate2LinkageDate7.cs"}, {"SourceFile": "Models\\ELCGTK30\\Cgtdate2LinkageDate8.cs"}, {"SourceFile": "Models\\ELCGTK30\\Cgtdate2LinkageDate9.cs"}, {"SourceFile": "Models\\ELCGTK30\\CgtMasterRecord.cs"}, {"SourceFile": "Models\\ELCGTK30\\CountryTable.cs"}, {"SourceFile": "Models\\ELCGTK30\\CurrentProcessing.cs"}, {"SourceFile": "Models\\ELCGTK30\\DateManipulationFields.cs"}, {"SourceFile": "Models\\ELCGTK30\\DisposalIds.cs"}, {"SourceFile": "Models\\ELCGTK30\\ElcgmioLinkage1.cs"}, {"SourceFile": "Models\\ELCGTK30\\ElcgmioLinkage2.cs"}, {"SourceFile": "Models\\ELCGTK30\\Elcgtk30.cs"}, {"SourceFile": "Models\\ELCGTK30\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\ELCGTK30\\ErrorMesg.cs"}, {"SourceFile": "Models\\ELCGTK30\\Filler164.cs"}, {"SourceFile": "Models\\ELCGTK30\\Fvar.cs"}, {"SourceFile": "Models\\ELCGTK30\\Gvar.cs"}, {"SourceFile": "Models\\ELCGTK30\\HeaderIds.cs"}, {"SourceFile": "Models\\ELCGTK30\\Ivar.cs"}, {"SourceFile": "Models\\ELCGTK30\\LkK30Programs.cs"}, {"SourceFile": "Models\\ELCGTK30\\MaingroupTable.cs"}, {"SourceFile": "Models\\ELCGTK30\\MonthName.cs"}, {"SourceFile": "Models\\ELCGTK30\\MonthTable.cs"}, {"SourceFile": "Models\\ELCGTK30\\NewFields.cs"}, {"SourceFile": "Models\\ELCGTK30\\ParamTable.cs"}, {"SourceFile": "Models\\ELCGTK30\\ParmInfo.cs"}, {"SourceFile": "Models\\ELCGTK30\\SedolRecord.cs"}, {"SourceFile": "Models\\ELCGTK30\\STotalRecord.cs"}, {"SourceFile": "Models\\ELCGTK30\\W01General.cs"}, {"SourceFile": "Models\\ELCGTK30\\WIds.cs"}, {"SourceFile": "Models\\ELCGTK30\\Ws1982ValWorkFields.cs"}, {"SourceFile": "Models\\ELCGTK30\\WsCioLink.cs"}, {"SourceFile": "Models\\ELCGTK30\\WtsCgtHeaders.cs"}, {"SourceFile": "Models\\ELCGTK30\\WttCgtTransactions.cs"}, {"SourceFile": "Models\\EnvironmentSettings.cs"}, {"SourceFile": "Models\\EQTCALC\\EquityParameters.cs"}, {"SourceFile": "Models\\EQTDEBUG\\CommonLinkage.cs"}, {"SourceFile": "Models\\EQTDEBUG\\EqtdebugLinkage.cs"}, {"SourceFile": "Models\\EQTDEBUG\\Gvar.cs"}, {"SourceFile": "Models\\EQTDEBUG\\Ivar.cs"}, {"SourceFile": "Models\\EQTENVNM\\EqtEnvnmLinkage.cs"}, {"SourceFile": "Models\\EqtLog\\Gvar.cs"}, {"SourceFile": "Models\\EqtLog\\Ivar.cs"}, {"SourceFile": "Models\\EqtLog\\LinkageArea1.cs"}, {"SourceFile": "Models\\EqtLog\\LinkageArea2.cs"}, {"SourceFile": "Models\\EqtLog\\LogMessage.cs"}, {"SourceFile": "Models\\eqtpath\\EqtdebugLinkage.cs"}, {"SourceFile": "Models\\eqtpath\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\eqtpath\\Gvar.cs"}, {"SourceFile": "Models\\eqtpath\\Ivar.cs"}, {"SourceFile": "Models\\eqtpath\\SplitParams.cs"}, {"SourceFile": "Models\\EQTYE\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\EQTYE\\CgtdelLinkage.cs"}, {"SourceFile": "Models\\EQTYE\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\EQTYE\\CgtlogLinkageArea1.cs"}, {"SourceFile": "Models\\EQTYE\\CgtlogLinkageArea2.cs"}, {"SourceFile": "Models\\EQTYE\\CgtsplitLinkage.cs"}, {"SourceFile": "Models\\EQTYE\\CommonLinkage.cs"}, {"SourceFile": "Models\\EQTYE\\D8Record.cs"}, {"SourceFile": "Models\\EQTYE\\EqtdebugLinkage.cs"}, {"SourceFile": "Models\\EQTYE\\EqtpathLinkage.cs"}, {"SourceFile": "Models\\EQTYE\\EquityErrorMessages.cs"}, {"SourceFile": "Models\\EQTYE\\EquityParameters.cs"}, {"SourceFile": "Models\\EQTYE\\Filler22.cs"}, {"SourceFile": "Models\\EQTYE\\Gvar.cs"}, {"SourceFile": "Models\\EQTYE\\Ivar.cs"}, {"SourceFile": "Models\\EQTYE\\LFileRecordArea.cs"}, {"SourceFile": "Models\\EQTYE\\LkCgtMasterRecord.cs"}, {"SourceFile": "Models\\EQTYE\\ParmInfo.cs"}, {"SourceFile": "Models\\EQTYE\\WLog.cs"}, {"SourceFile": "Models\\EQTYE\\WTest.cs"}, {"SourceFile": "Models\\EquityGlobalParms.cs"}, {"SourceFile": "Models\\EquityGlobal.cs"}, {"SourceFile": "Models\\EquityParameters.cs"}, {"SourceFile": "Models\\EquityRouterLinkage.cs"}, {"SourceFile": "Models\\EQUSECAL\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\EQUSECAL\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\EQUSECAL\\CommonLinkage.cs"}, {"SourceFile": "Models\\EQUSECAL\\D153Record.cs"}, {"SourceFile": "Models\\EQUSECAL\\D4Record.cs"}, {"SourceFile": "Models\\EQUSECAL\\D8Record.cs"}, {"SourceFile": "Models\\EQUSECAL\\EqtdebugLinkage.cs"}, {"SourceFile": "Models\\EQUSECAL\\EqusecalLinkage.cs"}, {"SourceFile": "Models\\EQUSECAL\\Gvar.cs"}, {"SourceFile": "Models\\EQUSECAL\\Ivar.cs"}, {"SourceFile": "Models\\EQUSECAL\\LFileRecordArea.cs"}, {"SourceFile": "Models\\ExporterSettings.cs"}, {"SourceFile": "Models\\FileAction.cs"}, {"SourceFile": "Models\\GeneralParameters.cs"}, {"SourceFile": "Models\\GetCurrency\\CurrencyChangeStatus.cs"}, {"SourceFile": "Models\\GetCurrency\\FundType.cs"}, {"SourceFile": "Models\\GetCurrency\\GetCurrencyLinkage.cs"}, {"SourceFile": "Models\\GroupDataLinkage.cs"}, {"SourceFile": "Models\\LinkageArea.cs"}, {"SourceFile": "Models\\LinkageIDs.cs"}, {"SourceFile": "Models\\LinkageRequests.cs"}, {"SourceFile": "Models\\LkPanels.cs"}, {"SourceFile": "Models\\LogMessage.cs"}, {"SourceFile": "Models\\MasterAcquisitionDARecord.cs"}, {"SourceFile": "Models\\MasterBalanceCostDARecord.cs"}, {"SourceFile": "Models\\MasterBalanceDARecord.cs"}, {"SourceFile": "Models\\MasterDADto.cs"}, {"SourceFile": "Models\\MasterDALResult.cs"}, {"SourceFile": "Models\\MasterDisposalDARecord.cs"}, {"SourceFile": "Models\\MasterHoldingDARecord.cs"}, {"SourceFile": "Models\\PathSettings.cs"}, {"SourceFile": "Models\\PartTrancheDisposal\\CommonLinkage.cs"}, {"SourceFile": "Models\\PartTrancheDisposal\\FileAction.cs"}, {"SourceFile": "Models\\PartTrancheDisposal\\PartTrancheDisposalParameters.cs"}, {"SourceFile": "Models\\RealSub\\CgtabortLinkage.cs"}, {"SourceFile": "Models\\RealSub\\CgtfilesLinkage.cs"}, {"SourceFile": "Models\\RealSub\\CommonLinkage.cs"}, {"SourceFile": "Models\\RealSub\\D8Record.cs"}, {"SourceFile": "Models\\RealSub\\RealSubParameters.cs"}, {"SourceFile": "Models\\RealSub\\RealSubScreenData.cs"}, {"SourceFile": "Models\\TimingLinkage.cs"}, {"SourceFile": "Models\\UserFundRecord.cs"}, {"SourceFile": "Models\\UserFundsDARecord.cs"}, {"SourceFile": "Models\\UserInfoDARecord.cs"}, {"SourceFile": "Models\\UserInfoRecord.cs"}, {"SourceFile": "Models\\WriteTrace\\TraceAction.cs"}, {"SourceFile": "Models\\WriteTrace\\TraceMessageParameters.cs"}, {"SourceFile": "Models\\WTSedolHeaders.cs"}, {"SourceFile": "Models\\WTTCCosts.cs"}, {"SourceFile": "Models\\WTTransactions.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Reports\\Cgtres.cs"}, {"SourceFile": "Reports\\Cgtsing4.cs"}, {"SourceFile": "Reports\\Cgtskan.cs"}, {"SourceFile": "Reports\\Cgtglrep.cs"}, {"SourceFile": "Reports\\Cgtskr1.cs"}, {"SourceFile": "Reports\\Cgtskr2.cs"}, {"SourceFile": "Reports\\Cgtskr3.cs"}, {"SourceFile": "Reports\\Elcgtk25.cs"}, {"SourceFile": "Utils\\Comp3Converter.cs"}, {"SourceFile": "Utils\\Constants.cs"}, {"SourceFile": "Utils\\MoveCorr.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Git\\Legacy4-WK\\Equity.Net\\Sources\\Business Logic Layer\\Equity.CobolDataTransformation\\bin\\Debug\\WK.UK.CCH.Equity.CobolDataTransformation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Git\\Legacy4-WK\\Equity.Net\\Sources\\Foundation Layer\\Equity.CommonTypes\\bin\\Debug\\WK.UK.CCH.Equity.CommonTypes.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Git\\Legacy4-WK\\Equity.Net\\Sources\\Foundation Layer\\Equity.Utilities\\bin\\Debug\\WK.UK.CCH.Equity.Utilities.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Elfonze\\Mclaren\\AugTest\\Equity.CobMod\\bin\\Debug\\Legacy4.Equity.CobMod.dll", "OutputItemRelativePath": "Legacy4.Equity.CobMod.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}