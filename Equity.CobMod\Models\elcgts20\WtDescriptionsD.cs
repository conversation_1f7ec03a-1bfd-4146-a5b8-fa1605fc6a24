using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtDescriptionsD Data Structure

public class WtDescriptionsD
{
    private static int _size = 608;
    // [DEBUG] Class: WtDescriptionsD, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler409, is_external=, is_static_class=False, static_prefix=
    private string _Filler409 ="DECRIPTIONS-D===";
    
    
    
    
    // [DEBUG] Field: WtddOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtddOccurs =30;
    
    
    
    
    // [DEBUG] Field: WtddDescriptionsTable, is_external=, is_static_class=False, static_prefix=
    private WtddDescriptionsTable _WtddDescriptionsTable = new WtddDescriptionsTable();
    
    
    
    
    // [DEBUG] Field: WtddDescR, is_external=, is_static_class=False, static_prefix=
    private WtddDescR _WtddDescR = new WtddDescR();
    
    
    
    
    
    // Serialization methods
    public string GetWtDescriptionsDAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler409.PadRight(16));
        result.Append(_WtddOccurs.ToString().PadLeft(3, '0'));
        result.Append(_WtddDescriptionsTable.GetWtddDescriptionsTableAsString());
        result.Append(_WtddDescR.GetWtddDescRAsString());
        
        return result.ToString();
    }
    
    public void SetWtDescriptionsDAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller409(extracted);
        }
        offset += 16;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtddOccurs(parsedInt);
        }
        offset += 3;
        if (offset + 570 <= data.Length)
        {
            _WtddDescriptionsTable.SetWtddDescriptionsTableAsString(data.Substring(offset, 570));
        }
        else
        {
            _WtddDescriptionsTable.SetWtddDescriptionsTableAsString(data.Substring(offset));
        }
        offset += 570;
        if (offset + 19 <= data.Length)
        {
            _WtddDescR.SetWtddDescRAsString(data.Substring(offset, 19));
        }
        else
        {
            _WtddDescR.SetWtddDescRAsString(data.Substring(offset));
        }
        offset += 19;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtDescriptionsDAsString();
    }
    // Set<>String Override function
    public void SetWtDescriptionsD(string value)
    {
        SetWtDescriptionsDAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller409()
    {
        return _Filler409;
    }
    
    // Standard Setter
    public void SetFiller409(string value)
    {
        _Filler409 = value;
    }
    
    // Get<>AsString()
    public string GetFiller409AsString()
    {
        return _Filler409.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller409AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler409 = value;
    }
    
    // Standard Getter
    public int GetWtddOccurs()
    {
        return _WtddOccurs;
    }
    
    // Standard Setter
    public void SetWtddOccurs(int value)
    {
        _WtddOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtddOccursAsString()
    {
        return _WtddOccurs.ToString().PadLeft(3, '0');
    }
    
    // Set<>AsString()
    public void SetWtddOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtddOccurs = parsed;
    }
    
    // Standard Getter
    public WtddDescriptionsTable GetWtddDescriptionsTable()
    {
        return _WtddDescriptionsTable;
    }
    
    // Standard Setter
    public void SetWtddDescriptionsTable(WtddDescriptionsTable value)
    {
        _WtddDescriptionsTable = value;
    }
    
    // Get<>AsString()
    public string GetWtddDescriptionsTableAsString()
    {
        return _WtddDescriptionsTable != null ? _WtddDescriptionsTable.GetWtddDescriptionsTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtddDescriptionsTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtddDescriptionsTable == null)
        {
            _WtddDescriptionsTable = new WtddDescriptionsTable();
        }
        _WtddDescriptionsTable.SetWtddDescriptionsTableAsString(value);
    }
    
    // Standard Getter
    public WtddDescR GetWtddDescR()
    {
        return _WtddDescR;
    }
    
    // Standard Setter
    public void SetWtddDescR(WtddDescR value)
    {
        _WtddDescR = value;
    }
    
    // Get<>AsString()
    public string GetWtddDescRAsString()
    {
        return _WtddDescR != null ? _WtddDescR.GetWtddDescRAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtddDescRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtddDescR == null)
        {
            _WtddDescR = new WtddDescR();
        }
        _WtddDescR.SetWtddDescRAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtddDescriptionsTable(string value)
    {
        _WtddDescriptionsTable.SetWtddDescriptionsTableAsString(value);
    }
    // Nested Class: WtddDescriptionsTable
    public class WtddDescriptionsTable
    {
        private static int _size = 570;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler410, is_external=, is_static_class=False, static_prefix=
        private string _Filler410 ="   DISPOSAL";
        
        
        
        
        // [DEBUG] Field: Filler411, is_external=, is_static_class=False, static_prefix=
        private string _Filler411 ="SS STOCK SALE";
        
        
        
        
        // [DEBUG] Field: Filler412, is_external=, is_static_class=False, static_prefix=
        private string _Filler412 ="RS RIGHTS SALE";
        
        
        
        
        // [DEBUG] Field: Filler413, is_external=, is_static_class=False, static_prefix=
        private string _Filler413 ="CD CAPITAL DISTBTN";
        
        
        
        
        // [DEBUG] Field: Filler414, is_external=, is_static_class=False, static_prefix=
        private string _Filler414 ="RL RIGHTS LAPSE";
        
        
        
        
        // [DEBUG] Field: Filler415, is_external=, is_static_class=False, static_prefix=
        private string _Filler415 ="RF RIGHTS FRACTION";
        
        
        
        
        // [DEBUG] Field: Filler416, is_external=, is_static_class=False, static_prefix=
        private string _Filler416 ="RD REDEMPTION";
        
        
        
        
        // [DEBUG] Field: Filler417, is_external=, is_static_class=False, static_prefix=
        private string _Filler417 ="CLWCL TO";
        
        
        
        
        // [DEBUG] Field: Filler418, is_external=, is_static_class=False, static_prefix=
        private string _Filler418 ="RCWRC TO";
        
        
        
        
        // [DEBUG] Field: Filler419, is_external=, is_static_class=False, static_prefix=
        private string _Filler419 ="RRWRR TO";
        
        
        
        
        // [DEBUG] Field: Filler420, is_external=, is_static_class=False, static_prefix=
        private string _Filler420 ="TRWTR TO";
        
        
        
        
        // [DEBUG] Field: Filler421, is_external=, is_static_class=False, static_prefix=
        private string _Filler421 ="RGWRG TO";
        
        
        
        
        // [DEBUG] Field: Filler422, is_external=, is_static_class=False, static_prefix=
        private string _Filler422 ="CNWCN TO";
        
        
        
        
        // [DEBUG] Field: Filler423, is_external=, is_static_class=False, static_prefix=
        private string _Filler423 ="CSWCS TO";
        
        
        
        
        // [DEBUG] Field: Filler424, is_external=, is_static_class=False, static_prefix=
        private string _Filler424 ="TSWTS TO";
        
        
        
        
        // [DEBUG] Field: Filler425, is_external=, is_static_class=False, static_prefix=
        private string _Filler425 ="GSCGS TO";
        
        
        
        
        // [DEBUG] Field: Filler426, is_external=, is_static_class=False, static_prefix=
        private string _Filler426 ="LQ LIQUIDATION";
        
        
        
        
        // [DEBUG] Field: Filler427, is_external=, is_static_class=False, static_prefix=
        private string _Filler427 ="XXWADJT.  ";
        
        
        
        
        // [DEBUG] Field: Filler428, is_external=, is_static_class=False, static_prefix=
        private string _Filler428 ="RAWRIGHTS ";
        
        
        
        
        // [DEBUG] Field: Filler429, is_external=, is_static_class=False, static_prefix=
        private string _Filler429 ="BAWBONUS  ";
        
        
        
        
        // [DEBUG] Field: Filler430, is_external=, is_static_class=False, static_prefix=
        private string _Filler430 ="EQ EQUALISATION";
        
        
        
        
        // [DEBUG] Field: Filler431, is_external=, is_static_class=False, static_prefix=
        private string _Filler431 ="ROWRO TO";
        
        
        
        
        // [DEBUG] Field: Filler432, is_external=, is_static_class=False, static_prefix=
        private string _Filler432 ="CTXCT TO";
        
        
        
        
        // [DEBUG] Field: Filler433, is_external=, is_static_class=False, static_prefix=
        private string _Filler433 ="DD DEEMED DISPOSAL";
        
        
        
        
        // [DEBUG] Field: Filler434, is_external=, is_static_class=False, static_prefix=
        private string _Filler434 ="RV REVALUATION";
        
        
        
        
        // [DEBUG] Field: Filler435, is_external=, is_static_class=False, static_prefix=
        private string _Filler435 ="TDWTD TO";
        
        
        
        
        // [DEBUG] Field: Filler436, is_external=, is_static_class=False, static_prefix=
        private string _Filler436 ="OL OPTION LAPSE";
        
        
        
        
        // [DEBUG] Field: Filler437, is_external=, is_static_class=False, static_prefix=
        private string _Filler437 ="ECDEX xx";
        
        
        
        
        // [DEBUG] Field: Filler438, is_external=, is_static_class=False, static_prefix=
        private string _Filler438 ="EPDEX xx";
        
        
        
        
        // [DEBUG] Field: Filler439, is_external=, is_static_class=False, static_prefix=
        private string _Filler439 ="WCDEX xx";
        
        
        
        
    public WtddDescriptionsTable() {}
    
    public WtddDescriptionsTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller410(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller411(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller412(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller413(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller414(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller415(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller416(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller417(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller418(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller419(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller420(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller421(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller422(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller423(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller424(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller425(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller426(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller427(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller428(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller429(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller430(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller431(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller432(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller433(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller434(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller435(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller436(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller437(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller438(data.Substring(offset, 19).Trim());
        offset += 19;
        SetFiller439(data.Substring(offset, 19).Trim());
        offset += 19;
        
    }
    
    // Serialization methods
    public string GetWtddDescriptionsTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler410.PadRight(19));
        result.Append(_Filler411.PadRight(19));
        result.Append(_Filler412.PadRight(19));
        result.Append(_Filler413.PadRight(19));
        result.Append(_Filler414.PadRight(19));
        result.Append(_Filler415.PadRight(19));
        result.Append(_Filler416.PadRight(19));
        result.Append(_Filler417.PadRight(19));
        result.Append(_Filler418.PadRight(19));
        result.Append(_Filler419.PadRight(19));
        result.Append(_Filler420.PadRight(19));
        result.Append(_Filler421.PadRight(19));
        result.Append(_Filler422.PadRight(19));
        result.Append(_Filler423.PadRight(19));
        result.Append(_Filler424.PadRight(19));
        result.Append(_Filler425.PadRight(19));
        result.Append(_Filler426.PadRight(19));
        result.Append(_Filler427.PadRight(19));
        result.Append(_Filler428.PadRight(19));
        result.Append(_Filler429.PadRight(19));
        result.Append(_Filler430.PadRight(19));
        result.Append(_Filler431.PadRight(19));
        result.Append(_Filler432.PadRight(19));
        result.Append(_Filler433.PadRight(19));
        result.Append(_Filler434.PadRight(19));
        result.Append(_Filler435.PadRight(19));
        result.Append(_Filler436.PadRight(19));
        result.Append(_Filler437.PadRight(19));
        result.Append(_Filler438.PadRight(19));
        result.Append(_Filler439.PadRight(19));
        
        return result.ToString();
    }
    
    public void SetWtddDescriptionsTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller410(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller411(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller412(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller413(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller414(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller415(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller416(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller417(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller418(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller419(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller420(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller421(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller422(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller423(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller424(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller425(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller426(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller427(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller428(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller429(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller430(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller431(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller432(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller433(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller434(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller435(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller436(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller437(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller438(extracted);
        }
        offset += 19;
        if (offset + 19 <= data.Length)
        {
            string extracted = data.Substring(offset, 19).Trim();
            SetFiller439(extracted);
        }
        offset += 19;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller410()
    {
        return _Filler410;
    }
    
    // Standard Setter
    public void SetFiller410(string value)
    {
        _Filler410 = value;
    }
    
    // Get<>AsString()
    public string GetFiller410AsString()
    {
        return _Filler410.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller410AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler410 = value;
    }
    
    // Standard Getter
    public string GetFiller411()
    {
        return _Filler411;
    }
    
    // Standard Setter
    public void SetFiller411(string value)
    {
        _Filler411 = value;
    }
    
    // Get<>AsString()
    public string GetFiller411AsString()
    {
        return _Filler411.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller411AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler411 = value;
    }
    
    // Standard Getter
    public string GetFiller412()
    {
        return _Filler412;
    }
    
    // Standard Setter
    public void SetFiller412(string value)
    {
        _Filler412 = value;
    }
    
    // Get<>AsString()
    public string GetFiller412AsString()
    {
        return _Filler412.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller412AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler412 = value;
    }
    
    // Standard Getter
    public string GetFiller413()
    {
        return _Filler413;
    }
    
    // Standard Setter
    public void SetFiller413(string value)
    {
        _Filler413 = value;
    }
    
    // Get<>AsString()
    public string GetFiller413AsString()
    {
        return _Filler413.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller413AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler413 = value;
    }
    
    // Standard Getter
    public string GetFiller414()
    {
        return _Filler414;
    }
    
    // Standard Setter
    public void SetFiller414(string value)
    {
        _Filler414 = value;
    }
    
    // Get<>AsString()
    public string GetFiller414AsString()
    {
        return _Filler414.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller414AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler414 = value;
    }
    
    // Standard Getter
    public string GetFiller415()
    {
        return _Filler415;
    }
    
    // Standard Setter
    public void SetFiller415(string value)
    {
        _Filler415 = value;
    }
    
    // Get<>AsString()
    public string GetFiller415AsString()
    {
        return _Filler415.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller415AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler415 = value;
    }
    
    // Standard Getter
    public string GetFiller416()
    {
        return _Filler416;
    }
    
    // Standard Setter
    public void SetFiller416(string value)
    {
        _Filler416 = value;
    }
    
    // Get<>AsString()
    public string GetFiller416AsString()
    {
        return _Filler416.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller416AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler416 = value;
    }
    
    // Standard Getter
    public string GetFiller417()
    {
        return _Filler417;
    }
    
    // Standard Setter
    public void SetFiller417(string value)
    {
        _Filler417 = value;
    }
    
    // Get<>AsString()
    public string GetFiller417AsString()
    {
        return _Filler417.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller417AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler417 = value;
    }
    
    // Standard Getter
    public string GetFiller418()
    {
        return _Filler418;
    }
    
    // Standard Setter
    public void SetFiller418(string value)
    {
        _Filler418 = value;
    }
    
    // Get<>AsString()
    public string GetFiller418AsString()
    {
        return _Filler418.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller418AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler418 = value;
    }
    
    // Standard Getter
    public string GetFiller419()
    {
        return _Filler419;
    }
    
    // Standard Setter
    public void SetFiller419(string value)
    {
        _Filler419 = value;
    }
    
    // Get<>AsString()
    public string GetFiller419AsString()
    {
        return _Filler419.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller419AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler419 = value;
    }
    
    // Standard Getter
    public string GetFiller420()
    {
        return _Filler420;
    }
    
    // Standard Setter
    public void SetFiller420(string value)
    {
        _Filler420 = value;
    }
    
    // Get<>AsString()
    public string GetFiller420AsString()
    {
        return _Filler420.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller420AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler420 = value;
    }
    
    // Standard Getter
    public string GetFiller421()
    {
        return _Filler421;
    }
    
    // Standard Setter
    public void SetFiller421(string value)
    {
        _Filler421 = value;
    }
    
    // Get<>AsString()
    public string GetFiller421AsString()
    {
        return _Filler421.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller421AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler421 = value;
    }
    
    // Standard Getter
    public string GetFiller422()
    {
        return _Filler422;
    }
    
    // Standard Setter
    public void SetFiller422(string value)
    {
        _Filler422 = value;
    }
    
    // Get<>AsString()
    public string GetFiller422AsString()
    {
        return _Filler422.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller422AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler422 = value;
    }
    
    // Standard Getter
    public string GetFiller423()
    {
        return _Filler423;
    }
    
    // Standard Setter
    public void SetFiller423(string value)
    {
        _Filler423 = value;
    }
    
    // Get<>AsString()
    public string GetFiller423AsString()
    {
        return _Filler423.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller423AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler423 = value;
    }
    
    // Standard Getter
    public string GetFiller424()
    {
        return _Filler424;
    }
    
    // Standard Setter
    public void SetFiller424(string value)
    {
        _Filler424 = value;
    }
    
    // Get<>AsString()
    public string GetFiller424AsString()
    {
        return _Filler424.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller424AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler424 = value;
    }
    
    // Standard Getter
    public string GetFiller425()
    {
        return _Filler425;
    }
    
    // Standard Setter
    public void SetFiller425(string value)
    {
        _Filler425 = value;
    }
    
    // Get<>AsString()
    public string GetFiller425AsString()
    {
        return _Filler425.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller425AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler425 = value;
    }
    
    // Standard Getter
    public string GetFiller426()
    {
        return _Filler426;
    }
    
    // Standard Setter
    public void SetFiller426(string value)
    {
        _Filler426 = value;
    }
    
    // Get<>AsString()
    public string GetFiller426AsString()
    {
        return _Filler426.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller426AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler426 = value;
    }
    
    // Standard Getter
    public string GetFiller427()
    {
        return _Filler427;
    }
    
    // Standard Setter
    public void SetFiller427(string value)
    {
        _Filler427 = value;
    }
    
    // Get<>AsString()
    public string GetFiller427AsString()
    {
        return _Filler427.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller427AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler427 = value;
    }
    
    // Standard Getter
    public string GetFiller428()
    {
        return _Filler428;
    }
    
    // Standard Setter
    public void SetFiller428(string value)
    {
        _Filler428 = value;
    }
    
    // Get<>AsString()
    public string GetFiller428AsString()
    {
        return _Filler428.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller428AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler428 = value;
    }
    
    // Standard Getter
    public string GetFiller429()
    {
        return _Filler429;
    }
    
    // Standard Setter
    public void SetFiller429(string value)
    {
        _Filler429 = value;
    }
    
    // Get<>AsString()
    public string GetFiller429AsString()
    {
        return _Filler429.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller429AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler429 = value;
    }
    
    // Standard Getter
    public string GetFiller430()
    {
        return _Filler430;
    }
    
    // Standard Setter
    public void SetFiller430(string value)
    {
        _Filler430 = value;
    }
    
    // Get<>AsString()
    public string GetFiller430AsString()
    {
        return _Filler430.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller430AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler430 = value;
    }
    
    // Standard Getter
    public string GetFiller431()
    {
        return _Filler431;
    }
    
    // Standard Setter
    public void SetFiller431(string value)
    {
        _Filler431 = value;
    }
    
    // Get<>AsString()
    public string GetFiller431AsString()
    {
        return _Filler431.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller431AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler431 = value;
    }
    
    // Standard Getter
    public string GetFiller432()
    {
        return _Filler432;
    }
    
    // Standard Setter
    public void SetFiller432(string value)
    {
        _Filler432 = value;
    }
    
    // Get<>AsString()
    public string GetFiller432AsString()
    {
        return _Filler432.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller432AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler432 = value;
    }
    
    // Standard Getter
    public string GetFiller433()
    {
        return _Filler433;
    }
    
    // Standard Setter
    public void SetFiller433(string value)
    {
        _Filler433 = value;
    }
    
    // Get<>AsString()
    public string GetFiller433AsString()
    {
        return _Filler433.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller433AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler433 = value;
    }
    
    // Standard Getter
    public string GetFiller434()
    {
        return _Filler434;
    }
    
    // Standard Setter
    public void SetFiller434(string value)
    {
        _Filler434 = value;
    }
    
    // Get<>AsString()
    public string GetFiller434AsString()
    {
        return _Filler434.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller434AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler434 = value;
    }
    
    // Standard Getter
    public string GetFiller435()
    {
        return _Filler435;
    }
    
    // Standard Setter
    public void SetFiller435(string value)
    {
        _Filler435 = value;
    }
    
    // Get<>AsString()
    public string GetFiller435AsString()
    {
        return _Filler435.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller435AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler435 = value;
    }
    
    // Standard Getter
    public string GetFiller436()
    {
        return _Filler436;
    }
    
    // Standard Setter
    public void SetFiller436(string value)
    {
        _Filler436 = value;
    }
    
    // Get<>AsString()
    public string GetFiller436AsString()
    {
        return _Filler436.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller436AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler436 = value;
    }
    
    // Standard Getter
    public string GetFiller437()
    {
        return _Filler437;
    }
    
    // Standard Setter
    public void SetFiller437(string value)
    {
        _Filler437 = value;
    }
    
    // Get<>AsString()
    public string GetFiller437AsString()
    {
        return _Filler437.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller437AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler437 = value;
    }
    
    // Standard Getter
    public string GetFiller438()
    {
        return _Filler438;
    }
    
    // Standard Setter
    public void SetFiller438(string value)
    {
        _Filler438 = value;
    }
    
    // Get<>AsString()
    public string GetFiller438AsString()
    {
        return _Filler438.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller438AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler438 = value;
    }
    
    // Standard Getter
    public string GetFiller439()
    {
        return _Filler439;
    }
    
    // Standard Setter
    public void SetFiller439(string value)
    {
        _Filler439 = value;
    }
    
    // Get<>AsString()
    public string GetFiller439AsString()
    {
        return _Filler439.PadRight(19);
    }
    
    // Set<>AsString()
    public void SetFiller439AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler439 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Set<>String Override function (Nested)
public void SetWtddDescR(string value)
{
    _WtddDescR.SetWtddDescRAsString(value);
}
// Nested Class: WtddDescR
public class WtddDescR
{
    private static int _size = 19;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtddElement, is_external=, is_static_class=False, static_prefix=
    private WtddDescR.WtddElement[] _WtddElement = new WtddDescR.WtddElement[30];
    
    public void InitializeWtddElementArray()
    {
        for (int i = 0; i < 30; i++)
        {
            _WtddElement[i] = new WtddDescR.WtddElement();
        }
    }
    
    
    
public WtddDescR() {}

public WtddDescR(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    InitializeWtddElementArray();
    for (int i = 0; i < 30; i++)
    {
        _WtddElement[i].SetWtddElementAsString(data.Substring(offset, 19));
        offset += 19;
    }
    
}

// Serialization methods
public string GetWtddDescRAsString()
{
    StringBuilder result = new StringBuilder();
    
    for (int i = 0; i < 30; i++)
    {
        result.Append(_WtddElement[i].GetWtddElementAsString());
    }
    
    return result.ToString();
}

public void SetWtddDescRAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    for (int i = 0; i < 30; i++)
    {
        if (offset + 19 > data.Length) break;
        string val = data.Substring(offset, 19);
        
        _WtddElement[i].SetWtddElementAsString(val);
        offset += 19;
    }
}

// Getter and Setter methods

// Array Accessors for WtddElement
public WtddElement GetWtddElementAt(int index)
{
    return _WtddElement[index];
}

public void SetWtddElementAt(int index, WtddElement value)
{
    _WtddElement[index] = value;
}

// Flattened accessors (index 0)
public WtddElement GetWtddElement()
{
    return _WtddElement != null && _WtddElement.Length > 0
    ? _WtddElement[0]
    : new WtddElement();
}

public void SetWtddElement(WtddElement value)
{
    if (_WtddElement == null || _WtddElement.Length == 0)
    _WtddElement = new WtddElement[1];
    _WtddElement[0] = value;
}





public static int GetSize()
{
    return _size;
}

// Nested Class: WtddElement
public class WtddElement
{
    private static int _size = 19;
    
    // Fields in the class
    
    
    // [DEBUG] Field: WtddTransCat, is_external=, is_static_class=False, static_prefix=
    private string _WtddTransCat ="";
    
    
    
    
    // [DEBUG] Field: WtddSedolType, is_external=, is_static_class=False, static_prefix=
    private string _WtddSedolType ="";
    
    
    
    
    // [DEBUG] Field: WtddDescription, is_external=, is_static_class=False, static_prefix=
    private WtddElement.WtddDescription _WtddDescription = new WtddElement.WtddDescription();
    
    
    
    
public WtddElement() {}

public WtddElement(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetWtddTransCat(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWtddSedolType(data.Substring(offset, 1).Trim());
    offset += 1;
    _WtddDescription.SetWtddDescriptionAsString(data.Substring(offset, WtddDescription.GetSize()));
    offset += 16;
    
}

// Serialization methods
public string GetWtddElementAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_WtddTransCat.PadRight(2));
    result.Append(_WtddSedolType.PadRight(1));
    result.Append(_WtddDescription.GetWtddDescriptionAsString());
    
    return result.ToString();
}

public void SetWtddElementAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWtddTransCat(extracted);
    }
    offset += 2;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetWtddSedolType(extracted);
    }
    offset += 1;
    if (offset + 16 <= data.Length)
    {
        _WtddDescription.SetWtddDescriptionAsString(data.Substring(offset, 16));
    }
    else
    {
        _WtddDescription.SetWtddDescriptionAsString(data.Substring(offset));
    }
    offset += 16;
}

// Getter and Setter methods

// Standard Getter
public string GetWtddTransCat()
{
    return _WtddTransCat;
}

// Standard Setter
public void SetWtddTransCat(string value)
{
    _WtddTransCat = value;
}

// Get<>AsString()
public string GetWtddTransCatAsString()
{
    return _WtddTransCat.PadRight(2);
}

// Set<>AsString()
public void SetWtddTransCatAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtddTransCat = value;
}

// Standard Getter
public string GetWtddSedolType()
{
    return _WtddSedolType;
}

// Standard Setter
public void SetWtddSedolType(string value)
{
    _WtddSedolType = value;
}

// Get<>AsString()
public string GetWtddSedolTypeAsString()
{
    return _WtddSedolType.PadRight(1);
}

// Set<>AsString()
public void SetWtddSedolTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtddSedolType = value;
}

// Standard Getter
public WtddDescription GetWtddDescription()
{
    return _WtddDescription;
}

// Standard Setter
public void SetWtddDescription(WtddDescription value)
{
    _WtddDescription = value;
}

// Get<>AsString()
public string GetWtddDescriptionAsString()
{
    return _WtddDescription != null ? _WtddDescription.GetWtddDescriptionAsString() : "";
}

// Set<>AsString()
public void SetWtddDescriptionAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    if (_WtddDescription == null)
    {
        _WtddDescription = new WtddDescription();
    }
    _WtddDescription.SetWtddDescriptionAsString(value);
}



public static int GetSize()
{
    return _size;
}

// Nested Class: WtddDescription
public class WtddDescription
{
    private static int _size = 16;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler440, is_external=, is_static_class=False, static_prefix=
    private string _Filler440 ="";
    
    
    
    
    // [DEBUG] Field: WtddToFrom, is_external=, is_static_class=False, static_prefix=
    private string _WtddToFrom ="";
    
    
    
    
    // [DEBUG] Field: Filler441, is_external=, is_static_class=False, static_prefix=
    private string _Filler441 ="";
    
    
    
    
    // [DEBUG] Field: WtddSedol, is_external=, is_static_class=False, static_prefix=
    private string _WtddSedol ="";
    
    
    
    
public WtddDescription() {}

public WtddDescription(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller440(data.Substring(offset, 3).Trim());
    offset += 3;
    SetWtddToFrom(data.Substring(offset, 2).Trim());
    offset += 2;
    SetFiller441(data.Substring(offset, 2).Trim());
    offset += 2;
    SetWtddSedol(data.Substring(offset, 9).Trim());
    offset += 9;
    
}

// Serialization methods
public string GetWtddDescriptionAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler440.PadRight(3));
    result.Append(_WtddToFrom.PadRight(2));
    result.Append(_Filler441.PadRight(2));
    result.Append(_WtddSedol.PadRight(9));
    
    return result.ToString();
}

public void SetWtddDescriptionAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 3 <= data.Length)
    {
        string extracted = data.Substring(offset, 3).Trim();
        SetFiller440(extracted);
    }
    offset += 3;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetWtddToFrom(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller441(extracted);
    }
    offset += 2;
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        SetWtddSedol(extracted);
    }
    offset += 9;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller440()
{
    return _Filler440;
}

// Standard Setter
public void SetFiller440(string value)
{
    _Filler440 = value;
}

// Get<>AsString()
public string GetFiller440AsString()
{
    return _Filler440.PadRight(3);
}

// Set<>AsString()
public void SetFiller440AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler440 = value;
}

// Standard Getter
public string GetWtddToFrom()
{
    return _WtddToFrom;
}

// Standard Setter
public void SetWtddToFrom(string value)
{
    _WtddToFrom = value;
}

// Get<>AsString()
public string GetWtddToFromAsString()
{
    return _WtddToFrom.PadRight(2);
}

// Set<>AsString()
public void SetWtddToFromAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtddToFrom = value;
}

// Standard Getter
public string GetFiller441()
{
    return _Filler441;
}

// Standard Setter
public void SetFiller441(string value)
{
    _Filler441 = value;
}

// Get<>AsString()
public string GetFiller441AsString()
{
    return _Filler441.PadRight(2);
}

// Set<>AsString()
public void SetFiller441AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler441 = value;
}

// Standard Getter
public string GetWtddSedol()
{
    return _WtddSedol;
}

// Standard Setter
public void SetWtddSedol(string value)
{
    _WtddSedol = value;
}

// Get<>AsString()
public string GetWtddSedolAsString()
{
    return _WtddSedol.PadRight(9);
}

// Set<>AsString()
public void SetWtddSedolAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _WtddSedol = value;
}



public static int GetSize()
{
    return _size;
}

}
}
}

}}
