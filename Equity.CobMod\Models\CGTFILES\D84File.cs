using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D84File Data Structure

public class D84File
{
    private static int _size = 12;
    // [DEBUG] Class: D84File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler258, is_external=, is_static_class=False, static_prefix=
    private string _Filler258 ="$";
    
    
    
    
    // [DEBUG] Field: D84UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D84UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler259, is_external=, is_static_class=False, static_prefix=
    private string _Filler259 ="BLD.REP";
    
    
    
    
    
    // Serialization methods
    public string GetD84FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler258.PadRight(1));
        result.Append(_D84UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler259.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD84FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller258(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD84UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller259(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD84FileAsString();
    }
    // Set<>String Override function
    public void SetD84File(string value)
    {
        SetD84FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller258()
    {
        return _Filler258;
    }
    
    // Standard Setter
    public void SetFiller258(string value)
    {
        _Filler258 = value;
    }
    
    // Get<>AsString()
    public string GetFiller258AsString()
    {
        return _Filler258.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller258AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler258 = value;
    }
    
    // Standard Getter
    public int GetD84UserNo()
    {
        return _D84UserNo;
    }
    
    // Standard Setter
    public void SetD84UserNo(int value)
    {
        _D84UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD84UserNoAsString()
    {
        return _D84UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD84UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D84UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller259()
    {
        return _Filler259;
    }
    
    // Standard Setter
    public void SetFiller259(string value)
    {
        _Filler259 = value;
    }
    
    // Get<>AsString()
    public string GetFiller259AsString()
    {
        return _Filler259.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller259AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler259 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}