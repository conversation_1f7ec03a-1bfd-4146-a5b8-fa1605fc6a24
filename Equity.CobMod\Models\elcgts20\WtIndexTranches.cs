using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WtIndexTranches Data Structure

public class WtIndexTranches
{
    private static int _size = 45;
    // [DEBUG] Class: WtIndexTranches, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler442, is_external=, is_static_class=False, static_prefix=
    private string _Filler442 ="INDEX-TRANCHES==";
    
    
    
    
    // [DEBUG] Field: WtitMaxTableSize, is_external=, is_static_class=False, static_prefix=
    private int _WtitMaxTableSize =4000;
    
    
    
    
    // [DEBUG] Field: WtitOccurs, is_external=, is_static_class=False, static_prefix=
    private int _WtitOccurs =4000;
    
    
    
    
    // [DEBUG] Field: WtitTable, is_external=, is_static_class=False, static_prefix=
    private WtitTable _WtitTable = new WtitTable();
    
    
    
    
    
    // Serialization methods
    public string GetWtIndexTranchesAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler442.PadRight(16));
        result.Append(_WtitMaxTableSize.ToString().PadLeft(4, '0'));
        result.Append(_WtitOccurs.ToString().PadLeft(4, '0'));
        result.Append(_WtitTable.GetWtitTableAsString());
        
        return result.ToString();
    }
    
    public void SetWtIndexTranchesAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller442(extracted);
        }
        offset += 16;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtitMaxTableSize(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWtitOccurs(parsedInt);
        }
        offset += 4;
        if (offset + 21 <= data.Length)
        {
            _WtitTable.SetWtitTableAsString(data.Substring(offset, 21));
        }
        else
        {
            _WtitTable.SetWtitTableAsString(data.Substring(offset));
        }
        offset += 21;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWtIndexTranchesAsString();
    }
    // Set<>String Override function
    public void SetWtIndexTranches(string value)
    {
        SetWtIndexTranchesAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller442()
    {
        return _Filler442;
    }
    
    // Standard Setter
    public void SetFiller442(string value)
    {
        _Filler442 = value;
    }
    
    // Get<>AsString()
    public string GetFiller442AsString()
    {
        return _Filler442.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller442AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler442 = value;
    }
    
    // Standard Getter
    public int GetWtitMaxTableSize()
    {
        return _WtitMaxTableSize;
    }
    
    // Standard Setter
    public void SetWtitMaxTableSize(int value)
    {
        _WtitMaxTableSize = value;
    }
    
    // Get<>AsString()
    public string GetWtitMaxTableSizeAsString()
    {
        return _WtitMaxTableSize.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtitMaxTableSizeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtitMaxTableSize = parsed;
    }
    
    // Standard Getter
    public int GetWtitOccurs()
    {
        return _WtitOccurs;
    }
    
    // Standard Setter
    public void SetWtitOccurs(int value)
    {
        _WtitOccurs = value;
    }
    
    // Get<>AsString()
    public string GetWtitOccursAsString()
    {
        return _WtitOccurs.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetWtitOccursAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WtitOccurs = parsed;
    }
    
    // Standard Getter
    public WtitTable GetWtitTable()
    {
        return _WtitTable;
    }
    
    // Standard Setter
    public void SetWtitTable(WtitTable value)
    {
        _WtitTable = value;
    }
    
    // Get<>AsString()
    public string GetWtitTableAsString()
    {
        return _WtitTable != null ? _WtitTable.GetWtitTableAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWtitTableAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WtitTable == null)
        {
            _WtitTable = new WtitTable();
        }
        _WtitTable.SetWtitTableAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWtitTable(string value)
    {
        _WtitTable.SetWtitTableAsString(value);
    }
    // Nested Class: WtitTable
    public class WtitTable
    {
        private static int _size = 21;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtitElement, is_external=, is_static_class=False, static_prefix=
        private WtitTable.WtitElement[] _WtitElement = new WtitTable.WtitElement[4000];
        
        public void InitializeWtitElementArray()
        {
            for (int i = 0; i < 4000; i++)
            {
                _WtitElement[i] = new WtitTable.WtitElement();
            }
        }
        
        
        
    public WtitTable() {}
    
    public WtitTable(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        InitializeWtitElementArray();
        for (int i = 0; i < 4000; i++)
        {
            _WtitElement[i].SetWtitElementAsString(data.Substring(offset, 21));
            offset += 21;
        }
        
    }
    
    // Serialization methods
    public string GetWtitTableAsString()
    {
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < 4000; i++)
        {
            result.Append(_WtitElement[i].GetWtitElementAsString());
        }
        
        return result.ToString();
    }
    
    public void SetWtitTableAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        for (int i = 0; i < 4000; i++)
        {
            if (offset + 21 > data.Length) break;
            string val = data.Substring(offset, 21);
            
            _WtitElement[i].SetWtitElementAsString(val);
            offset += 21;
        }
    }
    
    // Getter and Setter methods
    
    // Array Accessors for WtitElement
    public WtitElement GetWtitElementAt(int index)
    {
        return _WtitElement[index];
    }
    
    public void SetWtitElementAt(int index, WtitElement value)
    {
        _WtitElement[index] = value;
    }
    
    // Flattened accessors (index 0)
    public WtitElement GetWtitElement()
    {
        return _WtitElement != null && _WtitElement.Length > 0
        ? _WtitElement[0]
        : new WtitElement();
    }
    
    public void SetWtitElement(WtitElement value)
    {
        if (_WtitElement == null || _WtitElement.Length == 0)
        _WtitElement = new WtitElement[1];
        _WtitElement[0] = value;
    }
    
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WtitElement
    public class WtitElement
    {
        private static int _size = 21;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WtitIndex, is_external=, is_static_class=False, static_prefix=
        private string _WtitIndex ="";
        
        
        
        
        // [DEBUG] Field: WtitUnits, is_external=, is_static_class=False, static_prefix=
        private decimal _WtitUnits =0;
        
        
        
        
        // [DEBUG] Field: WtitIndex2, is_external=, is_static_class=False, static_prefix=
        private string _WtitIndex2 ="";
        
        
        
        
    public WtitElement() {}
    
    public WtitElement(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWtitIndex(data.Substring(offset, 4).Trim());
        offset += 4;
        SetWtitUnits(PackedDecimalConverter.ToDecimal(data.Substring(offset, 13)));
        offset += 13;
        SetWtitIndex2(data.Substring(offset, 4).Trim());
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetWtitElementAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WtitIndex.PadRight(4));
        result.Append(_WtitUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WtitIndex2.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetWtitElementAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtitIndex(extracted);
        }
        offset += 4;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWtitUnits(parsedDec);
        }
        offset += 13;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWtitIndex2(extracted);
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWtitIndex()
    {
        return _WtitIndex;
    }
    
    // Standard Setter
    public void SetWtitIndex(string value)
    {
        _WtitIndex = value;
    }
    
    // Get<>AsString()
    public string GetWtitIndexAsString()
    {
        return _WtitIndex.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtitIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtitIndex = value;
    }
    
    // Standard Getter
    public decimal GetWtitUnits()
    {
        return _WtitUnits;
    }
    
    // Standard Setter
    public void SetWtitUnits(decimal value)
    {
        _WtitUnits = value;
    }
    
    // Get<>AsString()
    public string GetWtitUnitsAsString()
    {
        return _WtitUnits.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWtitUnitsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WtitUnits = parsed;
    }
    
    // Standard Getter
    public string GetWtitIndex2()
    {
        return _WtitIndex2;
    }
    
    // Standard Setter
    public void SetWtitIndex2(string value)
    {
        _WtitIndex2 = value;
    }
    
    // Get<>AsString()
    public string GetWtitIndex2AsString()
    {
        return _WtitIndex2.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWtitIndex2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WtitIndex2 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}

}}
