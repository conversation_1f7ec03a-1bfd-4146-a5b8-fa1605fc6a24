using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D91HeaderRecord Data Structure

public class D91HeaderRecord
{
    private static int _size = 37;
    // [DEBUG] Class: D91HeaderRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler127, is_external=, is_static_class=False, static_prefix=
    private string _Filler127 ="";
    
    
    // 88-level condition checks for Filler127
    public bool IsD91HeaderFound()
    {
        if (this._Filler127 == "'   EQUITY COUNTRY HEADER '") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D91ExtractDate, is_external=, is_static_class=False, static_prefix=
    private D91ExtractDate _D91ExtractDate = new D91ExtractDate();
    
    
    
    
    // [DEBUG] Field: Filler128, is_external=, is_static_class=False, static_prefix=
    private string _Filler128 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD91HeaderRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler127.PadRight(25));
        result.Append(_D91ExtractDate.GetD91ExtractDateAsString());
        result.Append(_Filler128.PadRight(12));
        
        return result.ToString();
    }
    
    public void SetD91HeaderRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 25 <= data.Length)
        {
            string extracted = data.Substring(offset, 25).Trim();
            SetFiller127(extracted);
        }
        offset += 25;
        if (offset + 0 <= data.Length)
        {
            _D91ExtractDate.SetD91ExtractDateAsString(data.Substring(offset, 0));
        }
        else
        {
            _D91ExtractDate.SetD91ExtractDateAsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 12 <= data.Length)
        {
            string extracted = data.Substring(offset, 12).Trim();
            SetFiller128(extracted);
        }
        offset += 12;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD91HeaderRecordAsString();
    }
    // Set<>String Override function
    public void SetD91HeaderRecord(string value)
    {
        SetD91HeaderRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller127()
    {
        return _Filler127;
    }
    
    // Standard Setter
    public void SetFiller127(string value)
    {
        _Filler127 = value;
    }
    
    // Get<>AsString()
    public string GetFiller127AsString()
    {
        return _Filler127.PadRight(25);
    }
    
    // Set<>AsString()
    public void SetFiller127AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler127 = value;
    }
    
    // Standard Getter
    public D91ExtractDate GetD91ExtractDate()
    {
        return _D91ExtractDate;
    }
    
    // Standard Setter
    public void SetD91ExtractDate(D91ExtractDate value)
    {
        _D91ExtractDate = value;
    }
    
    // Get<>AsString()
    public string GetD91ExtractDateAsString()
    {
        return _D91ExtractDate != null ? _D91ExtractDate.GetD91ExtractDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD91ExtractDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D91ExtractDate == null)
        {
            _D91ExtractDate = new D91ExtractDate();
        }
        _D91ExtractDate.SetD91ExtractDateAsString(value);
    }
    
    // Standard Getter
    public string GetFiller128()
    {
        return _Filler128;
    }
    
    // Standard Setter
    public void SetFiller128(string value)
    {
        _Filler128 = value;
    }
    
    // Get<>AsString()
    public string GetFiller128AsString()
    {
        return _Filler128.PadRight(12);
    }
    
    // Set<>AsString()
    public void SetFiller128AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler128 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD91ExtractDate(string value)
    {
        _D91ExtractDate.SetD91ExtractDateAsString(value);
    }
    // Nested Class: D91ExtractDate
    public class D91ExtractDate
    {
        private static int _size = 0;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D91ExtractDateYy, is_external=, is_static_class=False, static_prefix=
        private string _D91ExtractDateYy ="";
        
        
        
        
        // [DEBUG] Field: D91ExtractDateMm, is_external=, is_static_class=False, static_prefix=
        private string _D91ExtractDateMm ="";
        
        
        
        
        // [DEBUG] Field: D91ExtractDateDd, is_external=, is_static_class=False, static_prefix=
        private string _D91ExtractDateDd ="";
        
        
        
        
    public D91ExtractDate() {}
    
    public D91ExtractDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD91ExtractDateYy(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD91ExtractDateMm(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD91ExtractDateDd(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD91ExtractDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D91ExtractDateYy.PadRight(0));
        result.Append(_D91ExtractDateMm.PadRight(0));
        result.Append(_D91ExtractDateDd.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD91ExtractDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD91ExtractDateYy(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD91ExtractDateMm(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD91ExtractDateDd(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD91ExtractDateYy()
    {
        return _D91ExtractDateYy;
    }
    
    // Standard Setter
    public void SetD91ExtractDateYy(string value)
    {
        _D91ExtractDateYy = value;
    }
    
    // Get<>AsString()
    public string GetD91ExtractDateYyAsString()
    {
        return _D91ExtractDateYy.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD91ExtractDateYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D91ExtractDateYy = value;
    }
    
    // Standard Getter
    public string GetD91ExtractDateMm()
    {
        return _D91ExtractDateMm;
    }
    
    // Standard Setter
    public void SetD91ExtractDateMm(string value)
    {
        _D91ExtractDateMm = value;
    }
    
    // Get<>AsString()
    public string GetD91ExtractDateMmAsString()
    {
        return _D91ExtractDateMm.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD91ExtractDateMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D91ExtractDateMm = value;
    }
    
    // Standard Getter
    public string GetD91ExtractDateDd()
    {
        return _D91ExtractDateDd;
    }
    
    // Standard Setter
    public void SetD91ExtractDateDd(string value)
    {
        _D91ExtractDateDd = value;
    }
    
    // Get<>AsString()
    public string GetD91ExtractDateDdAsString()
    {
        return _D91ExtractDateDd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD91ExtractDateDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D91ExtractDateDd = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}