using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt179HsDTO
{// DTO class representing CgtskanLinkage Data Structure

public class CgtskanLinkage
{
    private static int _size = 340;
    // [DEBUG] Class: CgtskanLinkage, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: CgtskanMasterRecord, is_external=, is_static_class=False, static_prefix=
    private CgtskanMasterRecord _CgtskanMasterRecord = new CgtskanMasterRecord();
    
    
    
    
    // [DEBUG] Field: CgtskanFundCode, is_external=, is_static_class=False, static_prefix=
    private string _CgtskanFundCode ="";
    
    
    
    
    // [DEBUG] Field: CgtskanFundPeriodEndDate, is_external=, is_static_class=False, static_prefix=
    private string _CgtskanFundPeriodEndDate ="";
    
    
    
    
    // [DEBUG] Field: CgtskanReportReference, is_external=, is_static_class=False, static_prefix=
    private string _CgtskanReportReference ="";
    
    
    
    
    // [DEBUG] Field: CgtskanReportGenerationNo, is_external=, is_static_class=False, static_prefix=
    private string _CgtskanReportGenerationNo ="";
    
    
    
    
    // [DEBUG] Field: CgtskanReportProgram, is_external=, is_static_class=False, static_prefix=
    private string _CgtskanReportProgram ="";
    
    
    
    
    // [DEBUG] Field: CgtskanRecordsRead, is_external=, is_static_class=False, static_prefix=
    private int _CgtskanRecordsRead =0;
    
    
    
    
    // [DEBUG] Field: CgtskanAction, is_external=, is_static_class=False, static_prefix=
    private string _CgtskanAction ="";
    
    
    // 88-level condition checks for CgtskanAction
    public bool IsCgtskanCloseReport()
    {
        if (this._CgtskanAction == "'C'") return true;
        return false;
    }
    public bool IsCgtskanFundTotals()
    {
        if (this._CgtskanAction == "'F'") return true;
        return false;
    }
    public bool IsCgtskanGrandTotals()
    {
        if (this._CgtskanAction == "'G'") return true;
        return false;
    }
    public bool IsCgtskanInitialiseProgram()
    {
        if (this._CgtskanAction == "'I'") return true;
        return false;
    }
    public bool IsCgtskanQuitProgram()
    {
        if (this._CgtskanAction == "'Q'") return true;
        return false;
    }
    public bool IsCgtskanReportDetail()
    {
        if (this._CgtskanAction == "'R'") return true;
        return false;
    }
    public bool IsCgtskanSedolTotals()
    {
        if (this._CgtskanAction == "'S'") return true;
        return false;
    }
    public bool IsCgtskanTsbExport()
    {
        if (this._CgtskanAction == "'T'") return true;
        return false;
    }
    public bool IsCgtskanZeroiseTotals()
    {
        if (this._CgtskanAction == "'Z'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: CgtskanCurrencyPrintString, is_external=, is_static_class=False, static_prefix=
    private string _CgtskanCurrencyPrintString ="";
    
    
    
    
    // [DEBUG] Field: CgtskanSecurityType, is_external=, is_static_class=False, static_prefix=
    private string _CgtskanSecurityType ="";
    
    
    // 88-level condition checks for CgtskanSecurityType
    public bool IsCgtskanShortWrittenDeriv()
    {
        if (this._CgtskanSecurityType == "'G'") return true;
        if (this._CgtskanSecurityType == "'K'") return true;
        if (this._CgtskanSecurityType == "'L'") return true;
        return false;
    }
    public bool IsCgtskanOptions()
    {
        if (this._CgtskanSecurityType == "'I'") return true;
        if (this._CgtskanSecurityType == "'J'") return true;
        if (this._CgtskanSecurityType == "'K'") return true;
        if (this._CgtskanSecurityType == "'L'") return true;
        return false;
    }
    public bool IsCgtskanBonds()
    {
        if (this._CgtskanSecurityType == "'B'") return true;
        if (this._CgtskanSecurityType == "'C'") return true;
        if (this._CgtskanSecurityType == "'F'") return true;
        return false;
    }
    
    
    
    // Serialization methods
    public string GetCgtskanLinkageAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_CgtskanMasterRecord.GetCgtskanMasterRecordAsString());
        result.Append(_CgtskanFundCode.PadRight(4));
        result.Append(_CgtskanFundPeriodEndDate.PadRight(6));
        result.Append(_CgtskanReportReference.PadRight(30));
        result.Append(_CgtskanReportGenerationNo.PadRight(1));
        result.Append(_CgtskanReportProgram.PadRight(8));
        result.Append(_CgtskanRecordsRead.ToString().PadLeft(9, '0'));
        result.Append(_CgtskanAction.PadRight(1));
        result.Append(_CgtskanCurrencyPrintString.PadRight(10));
        result.Append(_CgtskanSecurityType.PadRight(1));
        
        return result.ToString();
    }
    
    public void SetCgtskanLinkageAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 270 <= data.Length)
        {
            _CgtskanMasterRecord.SetCgtskanMasterRecordAsString(data.Substring(offset, 270));
        }
        else
        {
            _CgtskanMasterRecord.SetCgtskanMasterRecordAsString(data.Substring(offset));
        }
        offset += 270;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetCgtskanFundCode(extracted);
        }
        offset += 4;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetCgtskanFundPeriodEndDate(extracted);
        }
        offset += 6;
        if (offset + 30 <= data.Length)
        {
            string extracted = data.Substring(offset, 30).Trim();
            SetCgtskanReportReference(extracted);
        }
        offset += 30;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetCgtskanReportGenerationNo(extracted);
        }
        offset += 1;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetCgtskanReportProgram(extracted);
        }
        offset += 8;
        if (offset + 9 <= data.Length)
        {
            string extracted = data.Substring(offset, 9).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtskanRecordsRead(parsedInt);
        }
        offset += 9;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetCgtskanAction(extracted);
        }
        offset += 1;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetCgtskanCurrencyPrintString(extracted);
        }
        offset += 10;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetCgtskanSecurityType(extracted);
        }
        offset += 1;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtskanLinkageAsString();
    }
    // Set<>String Override function
    public void SetCgtskanLinkage(string value)
    {
        SetCgtskanLinkageAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public CgtskanMasterRecord GetCgtskanMasterRecord()
    {
        return _CgtskanMasterRecord;
    }
    
    // Standard Setter
    public void SetCgtskanMasterRecord(CgtskanMasterRecord value)
    {
        _CgtskanMasterRecord = value;
    }
    
    // Get<>AsString()
    public string GetCgtskanMasterRecordAsString()
    {
        return _CgtskanMasterRecord != null ? _CgtskanMasterRecord.GetCgtskanMasterRecordAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtskanMasterRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CgtskanMasterRecord == null)
        {
            _CgtskanMasterRecord = new CgtskanMasterRecord();
        }
        _CgtskanMasterRecord.SetCgtskanMasterRecordAsString(value);
    }
    
    // Standard Getter
    public string GetCgtskanFundCode()
    {
        return _CgtskanFundCode;
    }
    
    // Standard Setter
    public void SetCgtskanFundCode(string value)
    {
        _CgtskanFundCode = value;
    }
    
    // Get<>AsString()
    public string GetCgtskanFundCodeAsString()
    {
        return _CgtskanFundCode.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetCgtskanFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtskanFundCode = value;
    }
    
    // Standard Getter
    public string GetCgtskanFundPeriodEndDate()
    {
        return _CgtskanFundPeriodEndDate;
    }
    
    // Standard Setter
    public void SetCgtskanFundPeriodEndDate(string value)
    {
        _CgtskanFundPeriodEndDate = value;
    }
    
    // Get<>AsString()
    public string GetCgtskanFundPeriodEndDateAsString()
    {
        return _CgtskanFundPeriodEndDate.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetCgtskanFundPeriodEndDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtskanFundPeriodEndDate = value;
    }
    
    // Standard Getter
    public string GetCgtskanReportReference()
    {
        return _CgtskanReportReference;
    }
    
    // Standard Setter
    public void SetCgtskanReportReference(string value)
    {
        _CgtskanReportReference = value;
    }
    
    // Get<>AsString()
    public string GetCgtskanReportReferenceAsString()
    {
        return _CgtskanReportReference.PadRight(30);
    }
    
    // Set<>AsString()
    public void SetCgtskanReportReferenceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtskanReportReference = value;
    }
    
    // Standard Getter
    public string GetCgtskanReportGenerationNo()
    {
        return _CgtskanReportGenerationNo;
    }
    
    // Standard Setter
    public void SetCgtskanReportGenerationNo(string value)
    {
        _CgtskanReportGenerationNo = value;
    }
    
    // Get<>AsString()
    public string GetCgtskanReportGenerationNoAsString()
    {
        return _CgtskanReportGenerationNo.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetCgtskanReportGenerationNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtskanReportGenerationNo = value;
    }
    
    // Standard Getter
    public string GetCgtskanReportProgram()
    {
        return _CgtskanReportProgram;
    }
    
    // Standard Setter
    public void SetCgtskanReportProgram(string value)
    {
        _CgtskanReportProgram = value;
    }
    
    // Get<>AsString()
    public string GetCgtskanReportProgramAsString()
    {
        return _CgtskanReportProgram.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetCgtskanReportProgramAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtskanReportProgram = value;
    }
    
    // Standard Getter
    public int GetCgtskanRecordsRead()
    {
        return _CgtskanRecordsRead;
    }
    
    // Standard Setter
    public void SetCgtskanRecordsRead(int value)
    {
        _CgtskanRecordsRead = value;
    }
    
    // Get<>AsString()
    public string GetCgtskanRecordsReadAsString()
    {
        return _CgtskanRecordsRead.ToString().PadLeft(9, '0');
    }
    
    // Set<>AsString()
    public void SetCgtskanRecordsReadAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _CgtskanRecordsRead = parsed;
    }
    
    // Standard Getter
    public string GetCgtskanAction()
    {
        return _CgtskanAction;
    }
    
    // Standard Setter
    public void SetCgtskanAction(string value)
    {
        _CgtskanAction = value;
    }
    
    // Get<>AsString()
    public string GetCgtskanActionAsString()
    {
        return _CgtskanAction.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetCgtskanActionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtskanAction = value;
    }
    
    // Standard Getter
    public string GetCgtskanCurrencyPrintString()
    {
        return _CgtskanCurrencyPrintString;
    }
    
    // Standard Setter
    public void SetCgtskanCurrencyPrintString(string value)
    {
        _CgtskanCurrencyPrintString = value;
    }
    
    // Get<>AsString()
    public string GetCgtskanCurrencyPrintStringAsString()
    {
        return _CgtskanCurrencyPrintString.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetCgtskanCurrencyPrintStringAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtskanCurrencyPrintString = value;
    }
    
    // Standard Getter
    public string GetCgtskanSecurityType()
    {
        return _CgtskanSecurityType;
    }
    
    // Standard Setter
    public void SetCgtskanSecurityType(string value)
    {
        _CgtskanSecurityType = value;
    }
    
    // Get<>AsString()
    public string GetCgtskanSecurityTypeAsString()
    {
        return _CgtskanSecurityType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetCgtskanSecurityTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CgtskanSecurityType = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetCgtskanMasterRecord(string value)
    {
        _CgtskanMasterRecord.SetCgtskanMasterRecordAsString(value);
    }
    // Nested Class: CgtskanMasterRecord
    public class CgtskanMasterRecord
    {
        private static int _size = 270;
        
        // Fields in the class
        
        
        // [DEBUG] Field: FixedPortion, is_external=, is_static_class=False, static_prefix=
        private string _FixedPortion ="";
        
        
        
        
        // [DEBUG] Field: Filler122, is_external=, is_static_class=False, static_prefix=
        private string _Filler122 ="";
        
        
        
        
        // [DEBUG] Field: BalanceCosts, is_external=, is_static_class=False, static_prefix=
        private string[] _BalanceCosts = new string[200];
        
        
        
        
    public CgtskanMasterRecord() {}
    
    public CgtskanMasterRecord(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFixedPortion(data.Substring(offset, 270).Trim());
        offset += 270;
        SetFiller122(data.Substring(offset, 0).Trim());
        offset += 0;
        for (int i = 0; i < 200; i++)
        {
            string value = data.Substring(offset, 0);
            _BalanceCosts[i] = value.Trim();
            offset += 0;
        }
        
    }
    
    // Serialization methods
    public string GetCgtskanMasterRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_FixedPortion.PadRight(270));
        result.Append(_Filler122.PadRight(0));
        for (int i = 0; i < 200; i++)
        {
            result.Append(_BalanceCosts[i].PadRight(0));
        }
        
        return result.ToString();
    }
    
    public void SetCgtskanMasterRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 270 <= data.Length)
        {
            string extracted = data.Substring(offset, 270).Trim();
            SetFixedPortion(extracted);
        }
        offset += 270;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller122(extracted);
        }
        offset += 0;
        for (int i = 0; i < 200; i++)
        {
            if (offset + 0 > data.Length) break;
            string val = data.Substring(offset, 0);
            
            _BalanceCosts[i] = val.Trim();
            offset += 0;
        }
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFixedPortion()
    {
        return _FixedPortion;
    }
    
    // Standard Setter
    public void SetFixedPortion(string value)
    {
        _FixedPortion = value;
    }
    
    // Get<>AsString()
    public string GetFixedPortionAsString()
    {
        return _FixedPortion.PadRight(270);
    }
    
    // Set<>AsString()
    public void SetFixedPortionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _FixedPortion = value;
    }
    
    // Standard Getter
    public string GetFiller122()
    {
        return _Filler122;
    }
    
    // Standard Setter
    public void SetFiller122(string value)
    {
        _Filler122 = value;
    }
    
    // Get<>AsString()
    public string GetFiller122AsString()
    {
        return _Filler122.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller122AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler122 = value;
    }
    
    // Array Accessors for BalanceCosts
    public string GetBalanceCostsAt(int index)
    {
        return _BalanceCosts[index];
    }
    
    public void SetBalanceCostsAt(int index, string value)
    {
        _BalanceCosts[index] = value;
    }
    
    public string GetBalanceCostsAsStringAt(int index)
    {
        return _BalanceCosts[index].PadRight(0);
    }
    
    public void SetBalanceCostsAsStringAt(int index, string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        _BalanceCosts[index] = value;
    }
    
    // Flattened accessors (index 0)
    public string GetBalanceCosts()
    {
        return _BalanceCosts != null && _BalanceCosts.Length > 0
        ? _BalanceCosts[0]
        : default(string);
    }
    
    public void SetBalanceCosts(string value)
    {
        if (_BalanceCosts == null || _BalanceCosts.Length == 0)
        _BalanceCosts = new string[1];
        _BalanceCosts[0] = value;
    }
    
    public string GetBalanceCostsAsString()
    {
        return _BalanceCosts != null && _BalanceCosts.Length > 0
        ? _BalanceCosts[0].ToString()
        : string.Empty;
    }
    
    public void SetBalanceCostsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        if (_BalanceCosts == null || _BalanceCosts.Length == 0)
        _BalanceCosts = new string[1];
        
        _BalanceCosts[0] = value;
    }
    
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}