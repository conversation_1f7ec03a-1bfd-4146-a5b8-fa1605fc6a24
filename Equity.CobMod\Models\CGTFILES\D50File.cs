using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D50File Data Structure

public class D50File
{
    private static int _size = 12;
    // [DEBUG] Class: D50File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler217, is_external=, is_static_class=False, static_prefix=
    private string _Filler217 ="$";
    
    
    
    
    // [DEBUG] Field: D50UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D50UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler218, is_external=, is_static_class=False, static_prefix=
    private string _Filler218 ="STL.REP";
    
    
    
    
    
    // Serialization methods
    public string GetD50FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler217.PadRight(1));
        result.Append(_D50UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler218.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD50FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller217(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD50UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller218(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD50FileAsString();
    }
    // Set<>String Override function
    public void SetD50File(string value)
    {
        SetD50FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller217()
    {
        return _Filler217;
    }
    
    // Standard Setter
    public void SetFiller217(string value)
    {
        _Filler217 = value;
    }
    
    // Get<>AsString()
    public string GetFiller217AsString()
    {
        return _Filler217.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller217AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler217 = value;
    }
    
    // Standard Getter
    public int GetD50UserNo()
    {
        return _D50UserNo;
    }
    
    // Standard Setter
    public void SetD50UserNo(int value)
    {
        _D50UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD50UserNoAsString()
    {
        return _D50UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD50UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D50UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller218()
    {
        return _Filler218;
    }
    
    // Standard Setter
    public void SetFiller218(string value)
    {
        _Filler218 = value;
    }
    
    // Get<>AsString()
    public string GetFiller218AsString()
    {
        return _Filler218.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller218AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler218 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}