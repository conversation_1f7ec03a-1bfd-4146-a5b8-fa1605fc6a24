using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing ErrRec Data Structure

public class ErrRec
{
    private static int _size = 484;
    // [DEBUG] Class: ErrRec, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: ErrorNo, is_external=, is_static_class=False, static_prefix=
    private int _ErrorNo =0;
    
    
    
    
    // [DEBUG] Field: ErrorTableNo, is_external=, is_static_class=False, static_prefix=
    private int _ErrorTableNo =0;
    
    
    
    
    // [DEBUG] Field: ErrorFiller, is_external=, is_static_class=False, static_prefix=
    private string _ErrorFiller =" ";
    
    
    
    
    // [DEBUG] Field: ErrorRecArea, is_external=, is_static_class=False, static_prefix=
    private ErrorRecArea _ErrorRecArea = new ErrorRecArea();
    
    
    
    
    
    // Serialization methods
    public string GetErrRecAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_ErrorNo.ToString().PadLeft(2, '0'));
        result.Append(_ErrorTableNo.ToString().PadLeft(4, '0'));
        result.Append(_ErrorFiller.PadRight(8));
        result.Append(_ErrorRecArea.GetErrorRecAreaAsString());
        
        return result.ToString();
    }
    
    public void SetErrRecAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetErrorNo(parsedInt);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetErrorTableNo(parsedInt);
        }
        offset += 4;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetErrorFiller(extracted);
        }
        offset += 8;
        if (offset + 470 <= data.Length)
        {
            _ErrorRecArea.SetErrorRecAreaAsString(data.Substring(offset, 470));
        }
        else
        {
            _ErrorRecArea.SetErrorRecAreaAsString(data.Substring(offset));
        }
        offset += 470;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetErrRecAsString();
    }
    // Set<>String Override function
    public void SetErrRec(string value)
    {
        SetErrRecAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetErrorNo()
    {
        return _ErrorNo;
    }
    
    // Standard Setter
    public void SetErrorNo(int value)
    {
        _ErrorNo = value;
    }
    
    // Get<>AsString()
    public string GetErrorNoAsString()
    {
        return _ErrorNo.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetErrorNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _ErrorNo = parsed;
    }
    
    // Standard Getter
    public int GetErrorTableNo()
    {
        return _ErrorTableNo;
    }
    
    // Standard Setter
    public void SetErrorTableNo(int value)
    {
        _ErrorTableNo = value;
    }
    
    // Get<>AsString()
    public string GetErrorTableNoAsString()
    {
        return _ErrorTableNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetErrorTableNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _ErrorTableNo = parsed;
    }
    
    // Standard Getter
    public string GetErrorFiller()
    {
        return _ErrorFiller;
    }
    
    // Standard Setter
    public void SetErrorFiller(string value)
    {
        _ErrorFiller = value;
    }
    
    // Get<>AsString()
    public string GetErrorFillerAsString()
    {
        return _ErrorFiller.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetErrorFillerAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ErrorFiller = value;
    }
    
    // Standard Getter
    public ErrorRecArea GetErrorRecArea()
    {
        return _ErrorRecArea;
    }
    
    // Standard Setter
    public void SetErrorRecArea(ErrorRecArea value)
    {
        _ErrorRecArea = value;
    }
    
    // Get<>AsString()
    public string GetErrorRecAreaAsString()
    {
        return _ErrorRecArea != null ? _ErrorRecArea.GetErrorRecAreaAsString() : "";
    }
    
    // Set<>AsString()
    public void SetErrorRecAreaAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_ErrorRecArea == null)
        {
            _ErrorRecArea = new ErrorRecArea();
        }
        _ErrorRecArea.SetErrorRecAreaAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetErrorRecArea(string value)
    {
        _ErrorRecArea.SetErrorRecAreaAsString(value);
    }
    // Nested Class: ErrorRecArea
    public class ErrorRecArea
    {
        private static int _size = 470;
        
        // Fields in the class
        
        
        // [DEBUG] Field: ErrorRecCf, is_external=, is_static_class=False, static_prefix=
        private string _ErrorRecCf ="";
        
        
        
        
        // [DEBUG] Field: ErrorRecNow, is_external=, is_static_class=False, static_prefix=
        private string _ErrorRecNow ="";
        
        
        
        
        // [DEBUG] Field: Filler110, is_external=, is_static_class=False, static_prefix=
        private string _Filler110 ="";
        
        
        
        
    public ErrorRecArea() {}
    
    public ErrorRecArea(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetErrorRecCf(data.Substring(offset, 80).Trim());
        offset += 80;
        SetErrorRecNow(data.Substring(offset, 14).Trim());
        offset += 14;
        SetFiller110(data.Substring(offset, 376).Trim());
        offset += 376;
        
    }
    
    // Serialization methods
    public string GetErrorRecAreaAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_ErrorRecCf.PadRight(80));
        result.Append(_ErrorRecNow.PadRight(14));
        result.Append(_Filler110.PadRight(376));
        
        return result.ToString();
    }
    
    public void SetErrorRecAreaAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 80 <= data.Length)
        {
            string extracted = data.Substring(offset, 80).Trim();
            SetErrorRecCf(extracted);
        }
        offset += 80;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            SetErrorRecNow(extracted);
        }
        offset += 14;
        if (offset + 376 <= data.Length)
        {
            string extracted = data.Substring(offset, 376).Trim();
            SetFiller110(extracted);
        }
        offset += 376;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetErrorRecCf()
    {
        return _ErrorRecCf;
    }
    
    // Standard Setter
    public void SetErrorRecCf(string value)
    {
        _ErrorRecCf = value;
    }
    
    // Get<>AsString()
    public string GetErrorRecCfAsString()
    {
        return _ErrorRecCf.PadRight(80);
    }
    
    // Set<>AsString()
    public void SetErrorRecCfAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ErrorRecCf = value;
    }
    
    // Standard Getter
    public string GetErrorRecNow()
    {
        return _ErrorRecNow;
    }
    
    // Standard Setter
    public void SetErrorRecNow(string value)
    {
        _ErrorRecNow = value;
    }
    
    // Get<>AsString()
    public string GetErrorRecNowAsString()
    {
        return _ErrorRecNow.PadRight(14);
    }
    
    // Set<>AsString()
    public void SetErrorRecNowAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ErrorRecNow = value;
    }
    
    // Standard Getter
    public string GetFiller110()
    {
        return _Filler110;
    }
    
    // Standard Setter
    public void SetFiller110(string value)
    {
        _Filler110 = value;
    }
    
    // Get<>AsString()
    public string GetFiller110AsString()
    {
        return _Filler110.PadRight(376);
    }
    
    // Set<>AsString()
    public void SetFiller110AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler110 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}
