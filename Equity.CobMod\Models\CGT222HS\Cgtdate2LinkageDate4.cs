using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// DTO class representing Cgtdate2LinkageDate4 Data Structure

public class Cgtdate2LinkageDate4
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate4, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd4 =0;
    
    
    
    
    // [DEBUG] Field: Filler53, is_external=, is_static_class=False, static_prefix=
    private Filler53 _Filler53 = new Filler53();
    
    
    
    
    // [DEBUG] Field: Filler54, is_external=, is_static_class=False, static_prefix=
    private Filler54 _Filler54 = new Filler54();
    
    
    
    
    // [DEBUG] Field: Filler55, is_external=, is_static_class=False, static_prefix=
    private Filler55 _Filler55 = new Filler55();
    
    
    
    
    // [DEBUG] Field: Filler56, is_external=, is_static_class=False, static_prefix=
    private Filler56 _Filler56 = new Filler56();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate4AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd4.ToString().PadLeft(8, '0'));
        result.Append(_Filler53.GetFiller53AsString());
        result.Append(_Filler54.GetFiller54AsString());
        result.Append(_Filler55.GetFiller55AsString());
        result.Append(_Filler56.GetFiller56AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate4AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd4(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler53.SetFiller53AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler53.SetFiller53AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler54.SetFiller54AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler54.SetFiller54AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler55.SetFiller55AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler55.SetFiller55AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler56.SetFiller56AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler56.SetFiller56AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate4AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate4(string value)
    {
        SetCgtdate2LinkageDate4AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd4()
    {
        return _Cgtdate2Ccyymmdd4;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd4(int value)
    {
        _Cgtdate2Ccyymmdd4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd4AsString()
    {
        return _Cgtdate2Ccyymmdd4.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd4 = parsed;
    }
    
    // Standard Getter
    public Filler53 GetFiller53()
    {
        return _Filler53;
    }
    
    // Standard Setter
    public void SetFiller53(Filler53 value)
    {
        _Filler53 = value;
    }
    
    // Get<>AsString()
    public string GetFiller53AsString()
    {
        return _Filler53 != null ? _Filler53.GetFiller53AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller53AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler53 == null)
        {
            _Filler53 = new Filler53();
        }
        _Filler53.SetFiller53AsString(value);
    }
    
    // Standard Getter
    public Filler54 GetFiller54()
    {
        return _Filler54;
    }
    
    // Standard Setter
    public void SetFiller54(Filler54 value)
    {
        _Filler54 = value;
    }
    
    // Get<>AsString()
    public string GetFiller54AsString()
    {
        return _Filler54 != null ? _Filler54.GetFiller54AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller54AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler54 == null)
        {
            _Filler54 = new Filler54();
        }
        _Filler54.SetFiller54AsString(value);
    }
    
    // Standard Getter
    public Filler55 GetFiller55()
    {
        return _Filler55;
    }
    
    // Standard Setter
    public void SetFiller55(Filler55 value)
    {
        _Filler55 = value;
    }
    
    // Get<>AsString()
    public string GetFiller55AsString()
    {
        return _Filler55 != null ? _Filler55.GetFiller55AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller55AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler55 == null)
        {
            _Filler55 = new Filler55();
        }
        _Filler55.SetFiller55AsString(value);
    }
    
    // Standard Getter
    public Filler56 GetFiller56()
    {
        return _Filler56;
    }
    
    // Standard Setter
    public void SetFiller56(Filler56 value)
    {
        _Filler56 = value;
    }
    
    // Get<>AsString()
    public string GetFiller56AsString()
    {
        return _Filler56 != null ? _Filler56.GetFiller56AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller56AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler56 == null)
        {
            _Filler56 = new Filler56();
        }
        _Filler56.SetFiller56AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller53(string value)
    {
        _Filler53.SetFiller53AsString(value);
    }
    // Nested Class: Filler53
    public class Filler53
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc4, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc4 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd4, is_external=, is_static_class=False, static_prefix=
        private Filler53.Cgtdate2Yymmdd4 _Cgtdate2Yymmdd4 = new Filler53.Cgtdate2Yymmdd4();
        
        
        
        
    public Filler53() {}
    
    public Filler53(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc4(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd4.SetCgtdate2Yymmdd4AsString(data.Substring(offset, Cgtdate2Yymmdd4.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller53AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc4.PadRight(2));
        result.Append(_Cgtdate2Yymmdd4.GetCgtdate2Yymmdd4AsString());
        
        return result.ToString();
    }
    
    public void SetFiller53AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc4(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd4.SetCgtdate2Yymmdd4AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd4.SetCgtdate2Yymmdd4AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc4()
    {
        return _Cgtdate2Cc4;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc4(string value)
    {
        _Cgtdate2Cc4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc4AsString()
    {
        return _Cgtdate2Cc4.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc4 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd4 GetCgtdate2Yymmdd4()
    {
        return _Cgtdate2Yymmdd4;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd4(Cgtdate2Yymmdd4 value)
    {
        _Cgtdate2Yymmdd4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd4AsString()
    {
        return _Cgtdate2Yymmdd4 != null ? _Cgtdate2Yymmdd4.GetCgtdate2Yymmdd4AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd4 == null)
        {
            _Cgtdate2Yymmdd4 = new Cgtdate2Yymmdd4();
        }
        _Cgtdate2Yymmdd4.SetCgtdate2Yymmdd4AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd4
    public class Cgtdate2Yymmdd4
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy4, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy4 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd4, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd4.Cgtdate2Mmdd4 _Cgtdate2Mmdd4 = new Cgtdate2Yymmdd4.Cgtdate2Mmdd4();
        
        
        
        
    public Cgtdate2Yymmdd4() {}
    
    public Cgtdate2Yymmdd4(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy4(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd4.SetCgtdate2Mmdd4AsString(data.Substring(offset, Cgtdate2Mmdd4.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd4AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy4.PadRight(2));
        result.Append(_Cgtdate2Mmdd4.GetCgtdate2Mmdd4AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd4AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy4(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd4.SetCgtdate2Mmdd4AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd4.SetCgtdate2Mmdd4AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy4()
    {
        return _Cgtdate2Yy4;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy4(string value)
    {
        _Cgtdate2Yy4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy4AsString()
    {
        return _Cgtdate2Yy4.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy4 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd4 GetCgtdate2Mmdd4()
    {
        return _Cgtdate2Mmdd4;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd4(Cgtdate2Mmdd4 value)
    {
        _Cgtdate2Mmdd4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd4AsString()
    {
        return _Cgtdate2Mmdd4 != null ? _Cgtdate2Mmdd4.GetCgtdate2Mmdd4AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd4 == null)
        {
            _Cgtdate2Mmdd4 = new Cgtdate2Mmdd4();
        }
        _Cgtdate2Mmdd4.SetCgtdate2Mmdd4AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd4
    public class Cgtdate2Mmdd4
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm4, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm4 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd4, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd4 ="";
        
        
        
        
    public Cgtdate2Mmdd4() {}
    
    public Cgtdate2Mmdd4(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm4(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd4(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd4AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm4.PadRight(2));
        result.Append(_Cgtdate2Dd4.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd4AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm4(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd4(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm4()
    {
        return _Cgtdate2Mm4;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm4(string value)
    {
        _Cgtdate2Mm4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm4AsString()
    {
        return _Cgtdate2Mm4.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm4 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd4()
    {
        return _Cgtdate2Dd4;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd4(string value)
    {
        _Cgtdate2Dd4 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd4AsString()
    {
        return _Cgtdate2Dd4.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd4 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller54(string value)
{
    _Filler54.SetFiller54AsString(value);
}
// Nested Class: Filler54
public class Filler54
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy4 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd4 =0;
    
    
    
    
public Filler54() {}

public Filler54(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy4(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd4(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller54AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy4.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd4.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller54AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy4(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd4(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy4()
{
    return _Cgtdate2CCcyy4;
}

// Standard Setter
public void SetCgtdate2CCcyy4(int value)
{
    _Cgtdate2CCcyy4 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy4AsString()
{
    return _Cgtdate2CCcyy4.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy4 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd4()
{
    return _Cgtdate2CMmdd4;
}

// Standard Setter
public void SetCgtdate2CMmdd4(int value)
{
    _Cgtdate2CMmdd4 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd4AsString()
{
    return _Cgtdate2CMmdd4.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd4 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller55(string value)
{
    _Filler55.SetFiller55AsString(value);
}
// Nested Class: Filler55
public class Filler55
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc4 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy4 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm4 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd4 =0;
    
    
    
    
public Filler55() {}

public Filler55(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc4(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy4(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm4(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd4(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller55AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc4.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy4.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm4.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd4.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller55AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc4(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy4(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm4(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd4(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc4()
{
    return _Cgtdate2CCc4;
}

// Standard Setter
public void SetCgtdate2CCc4(int value)
{
    _Cgtdate2CCc4 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc4AsString()
{
    return _Cgtdate2CCc4.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc4 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy4()
{
    return _Cgtdate2CYy4;
}

// Standard Setter
public void SetCgtdate2CYy4(int value)
{
    _Cgtdate2CYy4 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy4AsString()
{
    return _Cgtdate2CYy4.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy4 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm4()
{
    return _Cgtdate2CMm4;
}

// Standard Setter
public void SetCgtdate2CMm4(int value)
{
    _Cgtdate2CMm4 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm4AsString()
{
    return _Cgtdate2CMm4.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm4 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd4()
{
    return _Cgtdate2CDd4;
}

// Standard Setter
public void SetCgtdate2CDd4(int value)
{
    _Cgtdate2CDd4 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd4AsString()
{
    return _Cgtdate2CDd4.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd4 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller56(string value)
{
    _Filler56.SetFiller56AsString(value);
}
// Nested Class: Filler56
public class Filler56
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm4, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm4 =0;
    
    
    
    
    // [DEBUG] Field: Filler57, is_external=, is_static_class=False, static_prefix=
    private string _Filler57 ="";
    
    
    
    
public Filler56() {}

public Filler56(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm4(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller57(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller56AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm4.ToString().PadLeft(6, '0'));
    result.Append(_Filler57.PadRight(2));
    
    return result.ToString();
}

public void SetFiller56AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm4(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller57(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm4()
{
    return _Cgtdate2CCcyymm4;
}

// Standard Setter
public void SetCgtdate2CCcyymm4(int value)
{
    _Cgtdate2CCcyymm4 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm4AsString()
{
    return _Cgtdate2CCcyymm4.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm4AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm4 = parsed;
}

// Standard Getter
public string GetFiller57()
{
    return _Filler57;
}

// Standard Setter
public void SetFiller57(string value)
{
    _Filler57 = value;
}

// Get<>AsString()
public string GetFiller57AsString()
{
    return _Filler57.PadRight(2);
}

// Set<>AsString()
public void SetFiller57AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler57 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}