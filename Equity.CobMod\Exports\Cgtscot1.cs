using System;
using System.Text;
using EquityProject.Cgtscot1DTO;
using EquityProject.EqtlogPGM;
using EquityProject.EqtpathPGM;
using EquityProject.EquityGlobalParmsDTO;
namespace EquityProject.Cgtscot1PGM
{
    // Cgtscot1 Class Definition

    //Cgtscot1 Class Constructor
    public class Cgtscot1
    {
        // Declare Cgtscot1 Class private variables
        private Fvar _fvar = new Fvar();
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();
        private EquityGlobalParms equityGlobalParms = new EquityGlobalParms();

        // Declare {program_name} Class getters setters
        public Fvar GetFvar() { return _fvar; }
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }

        /// <summary>
        /// Helper method to call external subroutines
        /// </summary>
        /// <param name="ivar">The Ivar parameter to pass to the subroutine</param>
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
        }


        // Run() method
        public void Run(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Call the main entry point
            Mainline(fvar, gvar, ivar);
        }

        // Methods representing paragraphs under procedure division
        /// <summary>
        /// This method executes the main logic of the program based on the COBOL paragraph 'mainline'.
        /// It initializes the process, reads and accumulates data until a condition is met, and then ends the process.
        /// </summary>
        /// <param name="fvar">The fvar parameter represents the function variables.</param>
        /// <param name="gvar">The gvar parameter represents the global variables.</param>
        /// <param name="ivar">The ivar parameter represents the input variables.</param>
        /// <remarks>
        /// This method translates the COBOL paragraph 'mainline' to C#.
        /// It includes the logic to initialize, read, accumulate, and end the process.
        /// </remarks>
        public void Mainline(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Mainline paragraph logic
            // PERFORM   A-INITIALISE
            AInitialise(fvar, gvar, ivar);

            // PERFORM   B-READ-ACCUMULATE   UNTIL   NOT   SUCCESSFUL OR QUIT-PROCESS
            do
            {
                BReadAccumulate(fvar, gvar, ivar);

                // Check the exit condition
            } while (gvar.GetCgtfilesLinkage().IsSuccessful() && !gvar.IsQuitProcess());

            // PERFORM   C-END
            CEnd(fvar, gvar, ivar);
        }
        /// <summary>
        /// COBOL Paragraph: A-INITIALISE
        /// </summary>
        /// <remarks>
        /// Implements the COBOL paragraph A-INITIALISE.
        /// This paragraph handles initial setup tasks, including:
        /// - Setting up user and report numbers.
        /// - Determining file paths using EQTPATH.
        /// - Opening the unrealised data file for input.
        /// - Opening the unrealised export file for output.
        /// - Handling configuration for derivative export format.
        /// - Logging program start and version information.
        /// </remarks>
        /// <param name="fvar">File variables, providing access to file record structures.</param>
        /// <param name="gvar">Global variables, holding application-wide data and linkage areas.</param>
        /// <param name="ivar">Input/Output variables, typically used for passing parameters to the program.</param>
        public void AInitialise(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // MOVE CGTSCOT1-USER-NO TO L-USER-NO
            gvar.GetLFileRecordArea().SetLUserNo(ivar.GetCgtscot1Linkage().GetCgtscot1UserNo());
            // REPORT-USER-NO (This is a label in COBOL, not an executable statement)
            // MOVE   CGTSCOT1-REPORT-NUMBER   TO   L-REPORT-NO
            gvar.GetCgtfilesLinkage().SetLReportNo(ivar.GetCgtscot1Linkage().GetCgtscot1ReportNumber().ToString());
            // REPORT-GEN-NO (This is a label in COBOL, not an executable statement)

            // MOVE   USER-DATA-PATH   TO   EQTPATH-PATH-ENV-VARIABLE
            gvar.GetEqtpathLinkage().SetEqtpathPathEnvVariable(Gvar.USER_DATA_PATH);

            // MOVE   REPORT-FILE      TO   EQTPATH-FILE-NAME
            gvar.GetEqtpathLinkage().SetEqtpathFileName(gvar.GetReportFile().ToString());

            // PERFORM   X-CALL-EQTPATH
            XCallEqtpath(fvar, gvar, ivar);

            // MOVE   EQTPATH-PATH-FILE-NAME   TO   REPORT-FILE-NAME
            gvar.SetReportFileName(gvar.GetEqtpathLinkage().GetEqtpathPathFileName());

            // MOVE   UNREALISED-DATA-FILE     TO   L-FILE-NAME
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.UNREALISED_DATA_FILE);

            // MOVE   OPEN-INPUT               TO   L-FILE-ACTION
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.OPEN_INPUT);

            // PERFORM   X-CALL-CGTFILES
            XCallCgtfiles(fvar, gvar, ivar);

            // MOVE   READ-NEXT                TO   L-FILE-ACTION.
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_NEXT);

            // OPEN   OUTPUT   UNREALISED-EXPORT-FILE.
            // Assuming this is handled by X-CALL-CGTFILES similar to OPEN-INPUT
            gvar.GetCgtfilesLinkage().SetLFileName("UNREALISED-EXPORT-FILE");
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.OPEN_OUTPUT);
            XCallCgtfiles(fvar, gvar, ivar);

            // MOVE   NEW-DERIVATIVE-EXPORT-FORMAT   TO   ELCGMIO-LINKAGE-2
            gvar.SetElcgmioLinkage2(new ElcgmioLinkage2());

            // PERFORM   X-CALL-MF-HANDLER-FOR-CONFIG
            XCallMfHandlerForConfig(fvar, gvar, ivar);

            // MOVE   W-CONFIG-ITEM    TO   W-NEW-DERIVATIVE-EXPORT-FORMAT
            gvar.SetWNewDerivativeExportFormat(gvar.GetWConfigItem());

            // IF   CONFIG-NEW-DERIVATIVE-EXPORT-FORMAT
            if (gvar.IsConfigNewDerivativeExportFormat())
            {
                // MOVE   W-NEW-INITIALISED-RECORD     TO   D75-RECORD
                // Note: This is a record-to-record move, need to copy fields individually
                // fvar.SetD75Record(gvar.GetWInitialRecord().GetWNewInitialisedRecord());
            }
            else
            {
                // MOVE   W-OLD-INITIALISED-RECORD     TO   D75-RECORD
                // Note: This is a record-to-record move, need to copy fields individually
                // fvar.SetD75Record(gvar.GetWInitialRecord().GetWOldInitialisedRecord());
            }
            // END-IF

            // MOVE   PROGRAM-NAME   TO   L-LOG-PROGRAM
            gvar.GetCgtlogLinkageArea1().SetLLogProgram(gvar.GetProgramName());

            // MOVE   OPEN-OUTPUT    TO   L-LOG-ACTION
            gvar.GetCgtlogLinkageArea1().SetLLogAction(Gvar.OPEN_OUTPUT);

            // MOVE   SPACES         TO   L-LOG-FILE-NAME
            gvar.GetCgtlogLinkageArea1().SetLLogFileName("");

            // PERFORM   X4-CGTLOG
            X4Cgtlog(fvar, gvar, ivar);

            // MOVE       ZERO       TO   WS-MESSAGE-NO
            gvar.SetWsMessageNo(0);

            // MOVE        'I'       TO   L-LOG-MESSAGE-TYPE
            gvar.GetCgtlogLinkageArea2().SetLLogMessageType("I");

            // MOVE   WRITE-RECORD   TO   L-LOG-ACTION
            gvar.GetCgtlogLinkageArea1().SetLLogAction(Gvar.WRITE_RECORD);

            // ACCEPT   TIME-STAMP   FROM   TIME
            gvar.SetTimeStampAsString(DateTime.Now.ToString("HHmmss"));

            // ACCEPT   DATE-STAMP   FROM   DATE
            gvar.SetDateStampAsString(DateTime.Now.ToString("yyMMdd"));

            // MOVE   WS-DD   TO   WS-MESS-DD
            gvar.GetWsMessages().GetWsMessage2().SetWsMessDd(int.Parse(DateTime.Now.ToString("dd")));

            // MOVE   WS-MM   TO   WS-MESS-MM
            gvar.GetWsMessages().GetWsMessage2().SetWsMessMm(int.Parse(DateTime.Now.ToString("MM")));

            // MOVE   WS-YY   TO   WS-MESS-YY
            gvar.GetWsMessages().GetWsMessage2().SetWsMessYy(int.Parse(DateTime.Now.ToString("yy")));

            // MOVE   WS-HH   TO   WS-MESS-HH
            gvar.GetWsMessages().GetWsMessage2().SetWsMessHh(int.Parse(DateTime.Now.ToString("HH")));

            // MOVE   WS-NN   TO   WS-MESS-NN
            gvar.GetWsMessages().GetWsMessage2().SetWsMessNn(int.Parse(DateTime.Now.ToString("mm")));

            // MOVE   WHEN-COMPILED          TO   WS-WHEN-COMPILED
            gvar.SetWsWhenCompiledAsString(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

            // STRING    PROGRAM-NAME   ': Version '   VERSION-NUMBER   ' '
            // WS-COMP-DATE   ' '   WS-COMP-TIME   '; '   WS-MESSAGE-2
            // DELIMITED   BY   SIZE
            // INTO   WS-MESSAGE-1
            gvar.GetWsMessages().SetWsMessage1(string.Concat(
                gvar.GetProgramName(),
                ": Version ",
                gvar.GetVersionNumber(),
                " ",
                gvar.GetWsWhenCompiled().GetWsCompDate(),
                " ",
                gvar.GetWsWhenCompiled().GetWsCompTime(),
                "; ",
                gvar.GetWsMessages().GetWsMessage2()
            ));

            // MOVE   WS-MESSAGE-1   TO   L-LOG-MESSAGE
            gvar.GetCgtlogLinkageArea2().SetLLogMessage(gvar.GetWsMessages().GetWsMessage1());

            // PERFORM   X4-CGTLOG.
            X4Cgtlog(fvar, gvar, ivar);
        }
        /// <summary>
        /// COBOL paragraph: bReadAccumulate
        ///
        /// This paragraph is designed to process files, evaluate records, and perform actions
        /// based on specific conditions. It handles different types of records and updates
        /// fields in the data structure accordingly.
        /// </summary>
        ///
        /// <param name="fvar">Parameter to access COBOL variables</param>
        /// <param name="gvar">Parameter to access COBOL variables</param>
        /// <param name="ivar">Parameter to access COBOL variables</param>
        ///
        /// <remarks>
        /// The COBOL paragraph 'BREADACCUMULATE' performs a series of operations based on the
        /// type of record being processed. It evaluates the record type and performs
        /// different actions based on the conditions specified. Variables are checked,
        /// and values are updated accordingly.
        /// </remarks>

        public void BReadAccumulate(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Perform X-CALL-CGTFILES (assuming this is a method call)
            XCallCgtfiles(fvar, gvar, ivar);
            // Check if the operation was successful
            if (gvar.GetCgtfilesLinkage().IsSuccessful())
            {
                // Move L-FILE-RECORD-AREA to U-GAINS-DATA-RECORD
                gvar.GetUGainsDataRecord().SetUGainsDataRecord(gvar.GetLFileRecordArea().ToString());

                // Initial value
                string uRecordTypeValue = gvar.GetUGainsDataRecord().GetURecordType();
                // Evaluate U-RECORD-TYPE
                switch (uRecordTypeValue)
                {
                    // Case '1'
                    case "1":
                        // Check conditions for D75-FUND-CODE and D75-DATASTREAM-STOCK-ID
                        if (string.IsNullOrEmpty(fvar.GetD75Record().GetFiller1().GetD75FundCode()))
                        {
                            // Set fvar D75-Fund-Code to spaces
                            fvar.GetD75Record().GetFiller1().SetD75FundCode("");
                        }

                        if (string.IsNullOrEmpty(fvar.GetD75Record().GetFiller1().GetD75DatastreamStockId()))
                        {
                            // Set fvar D75-DATASTREAM-STOCK-ID to spaces
                            fvar.GetD75Record().GetFiller1().SetD75DatastreamStockId("");
                        }

                        // Case D75-FUND-CODE not equal U-FUND-CODE AND D75-DATASTREAM-STOCK-ID not equal U-SEDOL-NUMBER
                        if (fvar.GetD75Record().GetFiller1().GetD75FundCode() != gvar.GetUGainsDataRecord().GetUFundCode()
                            && fvar.GetD75Record().GetFiller1().GetD75DatastreamStockId() != gvar.GetUGainsDataRecord().GetUHeaderRecordLayout().GetUSedolNumber())
                        {
                            // Perform B1-NEXT-FUND-SEDOL
                            B1NextFundSedol(fvar, gvar, ivar);
                        }
                        break;

                    // Case '2'
                    case "2":
                        // Check if U-PROCEEDS-X and U-GAINLOSS-X are not spaces
                        if (!string.IsNullOrEmpty(gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUProceedsX()) && !string.IsNullOrEmpty(gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUGainlossX())
                            // Check if CONFIG-NEW-DERIVATIVE-EXPORT-FORMAT is true
                            && gvar.IsConfigNewDerivativeExportFormat()
                        )
                        {
                            // Check if U-SHORT-WRITTEN-DERIVATIVE is true
                            if (gvar.GetUGainsDataRecord().IsUShortWrittenDerivative())
                            {
                                // Set D75-HOLDING-SIGN to '+' if U-SHORT-WRITTEN-DERIVATIVE is true
                                fvar.GetD75Record().GetFiller1().SetD75HoldingSign("+");
                            }
                            else
                            {
                                // Set D75-HOLDING-SIGN to '-' if U-SHORT-WRITTEN-DERIVATIVE is false
                                fvar.GetD75Record().GetFiller1().SetD75HoldingSign("-");
                            }
                        }

                        // Compute D75-MICROCGT-BOOK-COST
                        fvar.GetD75Record().GetFiller1().SetD75MicrocgtBookCost(fvar.GetD75Record().GetFiller1().GetD75MicrocgtBookCost() +
                                                                            gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUProceeds() -
                                                                            gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUGainloss());

                        // Add U-GAINLOSS to WS-GAIN-LOSS
                        gvar.SetWsGainLoss(gvar.GetWsGainLoss() + gvar.GetUGainsDataRecord().GetUDetailRecordLayout().GetUGainloss());
                        break;

                    // Case OTHER
                    case "OTHER":
                        // CONTINUE is implied in COBOL, no action needed in C#
                        break;
                }
            }
        }
        /// <summary>
        /// This is a conversion of the "B1-NEXT-FUND-SEDOL" COBOL paragraph.
        /// </summary>
        /// <param name="fvar">Fvar object</param>
        /// <param name="gvar">Gvar object</param>
        /// <param name="ivar">Ivar object</param>
        /// <remarks>
        /// This method checks if U-FUND-CODE is not equal to D75-FUND-CODE. If so, it moves U-FUND-CODE to WS-MESS-11-FUND, moves WS-MESSAGE-11 to L-LOG-MESSAGE,
        /// and then moves U-FUND-CODE to D75-FUND-CODE. It then moves U-SEDOL-NUMBER to D75-DATASTREAM-STOCK-ID, U-SECURITY-TYPE to D75-SECURITY-TYPE,
        /// and zeros out D75-MICROCGT-BOOK-COST and WS-GAIN-LOSS.
        ///
        /// Converted from the COBOL paragraph "B1-NEXT-FUND-SEDOL".
        /// </remarks>
        public void B1NextFundSedol(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move data from UGAINS-DATA-RECORD to D75-RECORD only if U-FUND-CODE does not equal D75-FUND-CODE
            string notDigit = "00";
            string notEqual = "01";
            string ugainsUFundCode = gvar.GetUGainsDataRecord().GetUFundCode().Trim();
            string d75FundCode = fvar.GetD75Record().GetFiller1().GetD75FundCode().Trim();

            if (!ugainsUFundCode.Equals(d75FundCode))
            {
                // If U-FUND-CODE is not equal to D75-FUND-CODE
                var wsMessages = gvar.GetWsMessages();
                wsMessages.GetWsMessage11().SetWsMess11Fund(ugainsUFundCode);
                gvar.SetWsMessages(wsMessages);

                var cgtlogLinkageArea2 = gvar.GetCgtlogLinkageArea2();
                cgtlogLinkageArea2.SetLLogMessage(gvar.GetWsMessages().GetWsMessage11AsString());

                var d75Record = fvar.GetD75Record();
                d75Record.GetFiller1().SetD75FundCode(ugainsUFundCode);
                fvar.SetD75Record(d75Record);
            }

            // Move U-SEDOL-NUMBER to D75-DATASTREAM-STOCK-ID
            var d75RecordChild = fvar.GetD75Record();
            d75RecordChild.GetFiller1().SetD75DatastreamStockId(gvar.GetUGainsDataRecord().GetUHeaderRecordLayout().GetUSedolNumber());
            fvar.SetD75Record(d75RecordChild);

            // Move U-SECURITY-TYPE to D75-SECURITY-TYPE
            var d75Record_Grand = fvar.GetD75Record();
            d75Record_Grand.GetFiller1().SetD75SecurityType(gvar.GetUGainsDataRecord().GetUSecurityType());
            fvar.SetD75Record(d75Record_Grand);

            // Set D75-MICROCGT-BOOK-COST and WS-GAIN-LOSS to zero
            var d75Record_GrandChild = fvar.GetD75Record();
            d75Record_GrandChild.GetFiller1().SetD75MicrocgtBookCost(0);  // Set to zero
            fvar.SetD75Record(d75Record_GrandChild);

            gvar.SetWsGainLoss(0);  // Set to zero
        }
        /// <summary>
        /// B2WRITE-RECORD.
        /// </summary>
        /// <param name="fvar">The parameters</param>
        /// <param name="gvar">The parameters</param>
        /// <param name="ivar">The parameters</param>
        /// <remarks>
        /// COBOL Code:
        /// EVALUATE   TRUE
        /// WHEN   WS-GAIN-LOSS   >   0
        /// MOVE   WS-GAIN-LOSS   TO   D75-UN-GAIN
        /// MOVE   ZERO           TO   D75-UN-LOSS
        /// WHEN   WS-GAIN-LOSS   <   0
        /// MOVE   WS-GAIN-LOSS   TO   D75-UN-LOSS
        /// MOVE   ZERO           TO   D75-UN-GAIN
        /// WHEN   OTHER
        /// MOVE   ZERO           TO   D75-UN-GAIN
        /// D75-UN-LOSS
        /// END-EVALUATE
        /// WRITE   D75-RECORD.
        /// </remarks>
        public void B2WriteRecord(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // COBOL EVALUATE statement converted to C# if-else
            // WHEN WS-GAIN-LOSS > 0
            if (gvar.GetWsGainLoss() > 0)
            {
                // MOVE WS-GAIN-LOSS TO D75-UN-GAIN
                fvar.GetD75Record().GetFiller1().SetD75UnGain(gvar.GetWsGainLoss());

                // MOVE ZERO TO D75-UN-LOSS
                fvar.GetD75Record().GetFiller1().SetD75UnLoss(0);
            }
            // WHEN WS-GAIN-LOSS < 0
            else if (gvar.GetWsGainLoss() < 0)
            {
                // MOVE WS-GAIN-LOSS TO D75-UN-LOSS
                fvar.GetD75Record().GetFiller1().SetD75UnLoss(gvar.GetWsGainLoss());

                // MOVE ZERO TO D75-UN-GAIN
                fvar.GetD75Record().GetFiller1().SetD75UnGain(0);
            }
            // WHEN OTHER
            else
            {
                // MOVE ZERO TO D75-UN-GAIN
                fvar.GetD75Record().GetFiller1().SetD75UnGain(0);

                // MOVE ZERO TO D75-UN-LOSS
                fvar.GetD75Record().GetFiller1().SetD75UnLoss(0);
            }

            // WRITE D75-RECORD - This should be a file write operation
            // For now, we'll comment this out as it needs proper file handling
            // WriteRecord(fvar, gvar, ivar);
        }
        /// <summary>
        /// CEnd paragraph performs several actions:
        /// 1. Calls the B2WriteRecord method.
        /// 2. Updates L-LOG-MESSAGE-TYPE to 'F'.
        /// 3. Updates L-LOG-ACTION to Gvar.CLOSE_FILE.
        /// 4. Calls the X4Cgtlog method.
        /// 5. Updates L-FILE-ACTION to Gvar.CLOSE_FILE.
        /// 6. Calls the XCallCgtfiles method.
        /// 7. Closes the UNREALISED-EXPORT-FILE.
        ///
        /// COBOL Paragraph: cEnd
        /// </summary>
        ///
        /// <param name="fvar">Fvar parameter</param>
        /// <param name="gvar">Gvar parameter</param>
        /// <param name="ivar">Ivar parameter</param>
        ///
        /// <remarks>
        /// Conversion from COBOL to C#:
        /// 1. PERFORM B2-WRITE-RECORD -> B2WriteRecord(fvar, gvar, ivar)
        /// 2. MOVE 'F' TO L-LOG-MESSAGE-TYPE -> gvar.SetCgtlogLinkageArea2(newValue)
        ///     where newValue = "F"
        /// 3. MOVE CLOSE-FILE TO L-LOG-ACTION -> gvar.GetCgtlogLinkageArea1().SetLLogAction("CLSE");
        /// 4. PERFORM X4-CGTLOG -> X4Cgtlog(fvar, gvar, ivar)
        /// 5. MOVE CLOSE-FILE TO L-FILE-ACTION -> gvar.GetCgtfilesLinkage().SetLFileAction("CLSE");
        /// 6. PERFORM X-CALL-CGTFILES -> XCallCgtfiles(fvar, gvar, ivar)
        /// 7. CLOSE UNREALISED-EXPORT-FILE -> gvar.GetUnrealisedExportFile().Close()
        /// </remarks>
        public void CEnd(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // PERFORM B2-WRITE-RECORD
            B2WriteRecord(fvar, gvar, ivar);

            // MOVE 'F' TO L-LOG-MESSAGE-TYPE
            gvar.GetCgtlogLinkageArea2().SetLLogMessageType("F");

            // MOVE CORRECT CLOSE-FILE constant to L-LOG-ACTION
            gvar.GetCgtlogLinkageArea1().SetLLogAction("CLSE");

            // PERFORM X4-CGTLOG
            X4Cgtlog(fvar, gvar, ivar);

            // CORRECT the logic to pass actual constant value to L-FILE-ACTION since COBOL constant CLSE is being mapped to CLOSE-FILE (78 level) reference
            gvar.GetCgtfilesLinkage().SetLFileAction("CLSE"); // Closure File Action -> CLSE in code

            // PERFORM X-CALL-CGTFILES
            XCallCgtfiles(fvar, gvar, ivar);

            // CLOSE UNREALISED-EXPORT-FILE
            // This should be handled by file I/O operations
            // gvar.GetUnrealisedExportFile().Close();
        }
        /// <summary>
        /// X4Cgtlog paragraph as described in the COBOL code.
        /// The COBOL code performs the following actions:
        /// 1. Adds 1 to WS-MESSAGE-NO.
        /// 2. Moves WS-MESSAGE-NO to L-LOG-MESSAGE-NO.
        /// 3. Calls the 'CGTLOG' program using CGTLOG-LINKAGE-AREA-1.
        /// 4. Checks if L-LOG-MESSAGE-TYPE is 'Q'.
        ///    - If true, moves 'Q' to WS-PROCESS-FLAG and 'P' to L-LOG-MESSAGE-TYPE.
        /// </summary>
        /// <param name="fvar">The fvar parameter to access COBOL variables.</param>
        /// <param name="gvar">The gvar parameter to access COBOL variables.</param>
        /// <param name="ivar">The ivar parameter to access COBOL variables.</param>
        /// <remarks>
        /// 1. Adds 1 to WS-MESSAGE-NO.
        /// 2. Sets L-LOG-MESSAGE-NO to the current value of WS-MESSAGE-NO
        /// 3. Call to external program "Cgtlog".
        /// 4. Conditional statements to move 'Q' to WS-PROCESS-FLAG and
        ///    'P' to L-LOG-MESSAGE-TYPE if L-LOG-MESSAGE-TYPE is 'Q'.
        /// </remarks>
        public void X4Cgtlog(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Add 1 to WS-MESSAGE-NO
            var wsMessageNo = gvar.GetWsMessageNo() + 1;
            gvar.SetWsMessageNo(wsMessageNo);

            // Move WS-MESSAGE-NO to L-LOG-MESSAGE-NO
            var linkageArea = gvar.GetCgtlogLinkageArea2();
            linkageArea.SetLLogMessageNo(wsMessageNo.ToString());
            gvar.SetCgtlogLinkageArea2(linkageArea);

            // Call external program 'CGTLOG' using CGTLOG-LINKAGE-AREA-1 as USING clause
            Eqtlog eqtlog = new Eqtlog();
            eqtlog.GetIvar().SetLinkageArea1AsString(gvar.GetCgtlogLinkageArea1AsString());
            eqtlog.Run(eqtlog.GetGvar(), eqtlog.GetIvar());

            // Check if L-LOG-MESSAGE-TYPE is 'Q'
            if (gvar.GetCgtlogLinkageArea2().GetLLogMessageType() == "Q")
            {
                // Move 'Q' to WS-PROCESS-FLAG
                gvar.SetWsProcessFlag("Q");
                // Move 'P' to L-LOG-MESSAGE-TYPE
                var newLinkageArea = gvar.GetCgtlogLinkageArea2();
                newLinkageArea.SetLLogMessageType("P");
                gvar.SetCgtlogLinkageArea2(newLinkageArea);
            }
        }
        /// <summary>
        /// CALL 'EQTPATH' USING EQTPATH-LINKAGE.
        /// </summary>
        /// <param name="fvar">A variable to hold function-defined variables.</param>
        /// <param name="gvar">A variable to hold global variables.</param>
        /// <param name="ivar">A variable to hold input variables.</param>
        /// <remarks>
        /// For the CALL statement, create a new instance of the external program and call its Run method with ONLY the parameters specified in the USING clause.
        /// Example:
        ///     EQTPATH eqtpath = new EQTPATH();
        ///     eqtpath.Run(gvar.GetPathLinkage());
        /// </remarks>

        public void XCallEqtpath(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Create an instance of the EQTPATH class
            Eqtpath eqtPath = new Eqtpath();

            /*
             * In COBOL:
             * CALL   'EQTPATH'   USING   EQTPATH-LINKAGE.
             *
             * Converting this to C#:
             * - Create an instance of the EQTPATH class
             * - Call the Run method with the EQTPATH-LINKAGE parameter
             */

            // Get the EQTPATH-LINKAGE value using the provided access method
            eqtPath.GetIvar().SetEqtpathLinkageAsString(gvar.GetEqtpathLinkageAsString());

            // Call the Run method with the EQTPATH-LINKAGE parameter
            eqtPath.Run(eqtPath.GetGvar(), eqtPath.GetIvar(), equityGlobalParms);
        }
        ///<summary>
        /// This method implements the COBOL paragraph xCallMfHandlerForConfig.

        /// <param name="fvar">used for accessing COBOL file variables</param>
        /// <param name="gvar">used for accessing COBOL global variables</param>
        /// <param name="ivar">used for accessing COBOL input variables</param>
        /// <remarks>
        /// This method performs the following steps:
        /// 1. Moves the value of GET-CONFIG-VALUE to L-ACT.
        /// 2. Calls the external program 'ELCGMIO' with the specified parameters.
        /// 3. Moves the result of ELCGMIO-LINKAGE-2 to W-CONFIG-ITEM.
        /// </remarks>
        public void XCallMfHandlerForConfig(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Move the value of GET-CONFIG-VALUE to L-ACT
            gvar.GetElcgmioLinkage1().SetLAct(Gvar.GET_CONFIG_VALUE);

            // Call the external program 'ELCGMIO' with the specified parameters
            /* must uncomment
            Elcgmio elcgmio = new Elcgmio();
            elcgmio.Run(gvar.GetElcgmioLinkage1());

            // Move the result of ELCGMIO-LINKAGE-2 to W-CONFIG-ITEM
            gvar.SetWConfigItem(gvar.GetElcgmioLinkage2());*/
        }
        /// <summary>
        /// Calls the external program 'CGTFILES' using the specified linkage and records.
        /// </summary>
        /// <param name="fvar">Fvar parameter containing function-specific variables.</param>
        /// <param name="gvar">Gvar parameter containing global variables.</param>
        /// <param name="ivar">Ivar parameter containing input-specific variables.</param>
        /// <remarks>
        /// Converts the COBOL paragraph 'xCallCgtfiles' to a C# method.
        /// This method calls the external program 'CGTFILES' with the provided linkage and records.
        /// </remarks>

        public void XCallCgtfiles(Fvar fvar, Gvar gvar, Ivar ivar)
        {
            // Create a new instance of the external program
            /* must uncomment
            Cgtfiles cgtfiles = new Cgtfiles();
            // Call the Run method with the parameters specified in the USING clause of COBOL
            // Map the COBOL USING clause variables to C# using the provided gvar and ivar parameters
            cgtfiles.Run(
                cgtfilesLinkage: gvar.GetCgtfilesLinkage(),
                lFileRecordArea: gvar.GetLFileRecordArea(),
                commonLinkage: ivar.GetCgtscot1Linkage()
            );*/
        }
    }
}