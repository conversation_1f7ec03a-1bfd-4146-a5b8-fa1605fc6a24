using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D89HeaderRecord Data Structure

public class D89HeaderRecord
{
    private static int _size = 36;
    // [DEBUG] Class: D89HeaderRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler123, is_external=, is_static_class=False, static_prefix=
    private string _Filler123 ="";
    
    
    // 88-level condition checks for Filler123
    public bool IsD89HeaderFound()
    {
        if (this._Filler123 == "'  EQUITY GROUP HEADER '") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D89ExtractDate, is_external=, is_static_class=False, static_prefix=
    private D89ExtractDate _D89ExtractDate = new D89ExtractDate();
    
    
    
    
    // [DEBUG] Field: Filler124, is_external=, is_static_class=False, static_prefix=
    private string _Filler124 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD89HeaderRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler123.PadRight(22));
        result.Append(_D89ExtractDate.GetD89ExtractDateAsString());
        result.Append(_Filler124.PadRight(14));
        
        return result.ToString();
    }
    
    public void SetD89HeaderRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 22 <= data.Length)
        {
            string extracted = data.Substring(offset, 22).Trim();
            SetFiller123(extracted);
        }
        offset += 22;
        if (offset + 0 <= data.Length)
        {
            _D89ExtractDate.SetD89ExtractDateAsString(data.Substring(offset, 0));
        }
        else
        {
            _D89ExtractDate.SetD89ExtractDateAsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            SetFiller124(extracted);
        }
        offset += 14;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD89HeaderRecordAsString();
    }
    // Set<>String Override function
    public void SetD89HeaderRecord(string value)
    {
        SetD89HeaderRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller123()
    {
        return _Filler123;
    }
    
    // Standard Setter
    public void SetFiller123(string value)
    {
        _Filler123 = value;
    }
    
    // Get<>AsString()
    public string GetFiller123AsString()
    {
        return _Filler123.PadRight(22);
    }
    
    // Set<>AsString()
    public void SetFiller123AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler123 = value;
    }
    
    // Standard Getter
    public D89ExtractDate GetD89ExtractDate()
    {
        return _D89ExtractDate;
    }
    
    // Standard Setter
    public void SetD89ExtractDate(D89ExtractDate value)
    {
        _D89ExtractDate = value;
    }
    
    // Get<>AsString()
    public string GetD89ExtractDateAsString()
    {
        return _D89ExtractDate != null ? _D89ExtractDate.GetD89ExtractDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD89ExtractDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D89ExtractDate == null)
        {
            _D89ExtractDate = new D89ExtractDate();
        }
        _D89ExtractDate.SetD89ExtractDateAsString(value);
    }
    
    // Standard Getter
    public string GetFiller124()
    {
        return _Filler124;
    }
    
    // Standard Setter
    public void SetFiller124(string value)
    {
        _Filler124 = value;
    }
    
    // Get<>AsString()
    public string GetFiller124AsString()
    {
        return _Filler124.PadRight(14);
    }
    
    // Set<>AsString()
    public void SetFiller124AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler124 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD89ExtractDate(string value)
    {
        _D89ExtractDate.SetD89ExtractDateAsString(value);
    }
    // Nested Class: D89ExtractDate
    public class D89ExtractDate
    {
        private static int _size = 0;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D89ExtractDateYy, is_external=, is_static_class=False, static_prefix=
        private string _D89ExtractDateYy ="";
        
        
        
        
        // [DEBUG] Field: D89ExtractDateMm, is_external=, is_static_class=False, static_prefix=
        private string _D89ExtractDateMm ="";
        
        
        
        
        // [DEBUG] Field: D89ExtractDateDd, is_external=, is_static_class=False, static_prefix=
        private string _D89ExtractDateDd ="";
        
        
        
        
    public D89ExtractDate() {}
    
    public D89ExtractDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD89ExtractDateYy(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD89ExtractDateMm(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD89ExtractDateDd(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD89ExtractDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D89ExtractDateYy.PadRight(0));
        result.Append(_D89ExtractDateMm.PadRight(0));
        result.Append(_D89ExtractDateDd.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD89ExtractDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD89ExtractDateYy(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD89ExtractDateMm(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD89ExtractDateDd(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD89ExtractDateYy()
    {
        return _D89ExtractDateYy;
    }
    
    // Standard Setter
    public void SetD89ExtractDateYy(string value)
    {
        _D89ExtractDateYy = value;
    }
    
    // Get<>AsString()
    public string GetD89ExtractDateYyAsString()
    {
        return _D89ExtractDateYy.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD89ExtractDateYyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D89ExtractDateYy = value;
    }
    
    // Standard Getter
    public string GetD89ExtractDateMm()
    {
        return _D89ExtractDateMm;
    }
    
    // Standard Setter
    public void SetD89ExtractDateMm(string value)
    {
        _D89ExtractDateMm = value;
    }
    
    // Get<>AsString()
    public string GetD89ExtractDateMmAsString()
    {
        return _D89ExtractDateMm.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD89ExtractDateMmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D89ExtractDateMm = value;
    }
    
    // Standard Getter
    public string GetD89ExtractDateDd()
    {
        return _D89ExtractDateDd;
    }
    
    // Standard Setter
    public void SetD89ExtractDateDd(string value)
    {
        _D89ExtractDateDd = value;
    }
    
    // Get<>AsString()
    public string GetD89ExtractDateDdAsString()
    {
        return _D89ExtractDateDd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD89ExtractDateDdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D89ExtractDateDd = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}