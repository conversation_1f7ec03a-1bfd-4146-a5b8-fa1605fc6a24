using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// DTO class representing D105Record Data Structure

public class D105Record
{
    private static int _size = 112;
    // [DEBUG] Class: D105Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler1, is_external=, is_static_class=False, static_prefix=
    private string _Filler1 ="";
    
    
    
    
    // [DEBUG] Field: D105FundCode, is_external=, is_static_class=False, static_prefix=
    private string _D105FundCode ="";
    
    
    
    
    // [DEBUG] Field: Filler2, is_external=, is_static_class=False, static_prefix=
    private string _Filler2 ="";
    
    
    
    
    // [DEBUG] Field: D105SedolCode, is_external=, is_static_class=False, static_prefix=
    private string _D105SedolCode ="";
    
    
    
    
    // [DEBUG] Field: Filler3, is_external=, is_static_class=False, static_prefix=
    private string _Filler3 ="";
    
    
    
    
    // [DEBUG] Field: D105TransReference, is_external=, is_static_class=False, static_prefix=
    private string _D105TransReference ="";
    
    
    
    
    // [DEBUG] Field: Filler4, is_external=, is_static_class=False, static_prefix=
    private string _Filler4 ="";
    
    
    
    
    // [DEBUG] Field: D105BargainDate, is_external=, is_static_class=False, static_prefix=
    private string _D105BargainDate ="";
    
    
    
    
    // [DEBUG] Field: Filler5, is_external=, is_static_class=False, static_prefix=
    private string _Filler5 ="";
    
    
    
    
    // [DEBUG] Field: D105Holding, is_external=, is_static_class=False, static_prefix=
    private decimal _D105Holding =0;
    
    
    
    
    // [DEBUG] Field: D105HoldingX, is_external=, is_static_class=False, static_prefix=
    private string _D105HoldingX ="";
    
    
    
    
    // [DEBUG] Field: Filler6, is_external=, is_static_class=False, static_prefix=
    private string _Filler6 ="";
    
    
    
    
    // [DEBUG] Field: D105MovementDesc, is_external=, is_static_class=False, static_prefix=
    private string _D105MovementDesc ="";
    
    
    
    
    // [DEBUG] Field: Filler7, is_external=, is_static_class=False, static_prefix=
    private string _Filler7 ="";
    
    
    
    
    // [DEBUG] Field: D105Cost, is_external=, is_static_class=False, static_prefix=
    private decimal _D105Cost =0;
    
    
    
    
    // [DEBUG] Field: Filler8, is_external=, is_static_class=False, static_prefix=
    private string _Filler8 ="";
    
    
    
    
    // [DEBUG] Field: D105Balance, is_external=, is_static_class=False, static_prefix=
    private decimal _D105Balance =0;
    
    
    
    
    // [DEBUG] Field: Filler9, is_external=, is_static_class=False, static_prefix=
    private string _Filler9 ="";
    
    
    
    
    // [DEBUG] Field: D105HoldingFlag, is_external=, is_static_class=False, static_prefix=
    private string _D105HoldingFlag ="";
    
    
    
    
    // [DEBUG] Field: Filler10, is_external=, is_static_class=False, static_prefix=
    private string _Filler10 ="";
    
    
    
    
    // [DEBUG] Field: D105HoldingSign, is_external=, is_static_class=False, static_prefix=
    private string _D105HoldingSign ="";
    
    
    
    
    // [DEBUG] Field: Filler11, is_external=, is_static_class=False, static_prefix=
    private string _Filler11 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD105RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler1.PadRight(0));
        result.Append(_D105FundCode.PadRight(0));
        result.Append(_Filler2.PadRight(0));
        result.Append(_D105SedolCode.PadRight(0));
        result.Append(_Filler3.PadRight(0));
        result.Append(_D105TransReference.PadRight(10));
        result.Append(_Filler4.PadRight(0));
        result.Append(_D105BargainDate.PadRight(0));
        result.Append(_Filler5.PadRight(0));
        result.Append(_D105Holding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D105HoldingX.PadRight(18));
        result.Append(_Filler6.PadRight(0));
        result.Append(_D105MovementDesc.PadRight(30));
        result.Append(_Filler7.PadRight(0));
        result.Append(_D105Cost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler8.PadRight(0));
        result.Append(_D105Balance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler9.PadRight(0));
        result.Append(_D105HoldingFlag.PadRight(0));
        result.Append(_Filler10.PadRight(0));
        result.Append(_D105HoldingSign.PadRight(0));
        result.Append(_Filler11.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD105RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller1(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD105FundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller2(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD105SedolCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller3(extracted);
        }
        offset += 0;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetD105TransReference(extracted);
        }
        offset += 10;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller4(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD105BargainDate(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller5(extracted);
        }
        offset += 0;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD105Holding(parsedDec);
        }
        offset += 18;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            SetD105HoldingX(extracted);
        }
        offset += 18;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller6(extracted);
        }
        offset += 0;
        if (offset + 30 <= data.Length)
        {
            string extracted = data.Substring(offset, 30).Trim();
            SetD105MovementDesc(extracted);
        }
        offset += 30;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller7(extracted);
        }
        offset += 0;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD105Cost(parsedDec);
        }
        offset += 18;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller8(extracted);
        }
        offset += 0;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD105Balance(parsedDec);
        }
        offset += 18;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller9(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD105HoldingFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller10(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD105HoldingSign(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller11(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD105RecordAsString();
    }
    // Set<>String Override function
    public void SetD105Record(string value)
    {
        SetD105RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller1()
    {
        return _Filler1;
    }
    
    // Standard Setter
    public void SetFiller1(string value)
    {
        _Filler1 = value;
    }
    
    // Get<>AsString()
    public string GetFiller1AsString()
    {
        return _Filler1.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller1AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler1 = value;
    }
    
    // Standard Getter
    public string GetD105FundCode()
    {
        return _D105FundCode;
    }
    
    // Standard Setter
    public void SetD105FundCode(string value)
    {
        _D105FundCode = value;
    }
    
    // Get<>AsString()
    public string GetD105FundCodeAsString()
    {
        return _D105FundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD105FundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D105FundCode = value;
    }
    
    // Standard Getter
    public string GetFiller2()
    {
        return _Filler2;
    }
    
    // Standard Setter
    public void SetFiller2(string value)
    {
        _Filler2 = value;
    }
    
    // Get<>AsString()
    public string GetFiller2AsString()
    {
        return _Filler2.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler2 = value;
    }
    
    // Standard Getter
    public string GetD105SedolCode()
    {
        return _D105SedolCode;
    }
    
    // Standard Setter
    public void SetD105SedolCode(string value)
    {
        _D105SedolCode = value;
    }
    
    // Get<>AsString()
    public string GetD105SedolCodeAsString()
    {
        return _D105SedolCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD105SedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D105SedolCode = value;
    }
    
    // Standard Getter
    public string GetFiller3()
    {
        return _Filler3;
    }
    
    // Standard Setter
    public void SetFiller3(string value)
    {
        _Filler3 = value;
    }
    
    // Get<>AsString()
    public string GetFiller3AsString()
    {
        return _Filler3.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler3 = value;
    }
    
    // Standard Getter
    public string GetD105TransReference()
    {
        return _D105TransReference;
    }
    
    // Standard Setter
    public void SetD105TransReference(string value)
    {
        _D105TransReference = value;
    }
    
    // Get<>AsString()
    public string GetD105TransReferenceAsString()
    {
        return _D105TransReference.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetD105TransReferenceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D105TransReference = value;
    }
    
    // Standard Getter
    public string GetFiller4()
    {
        return _Filler4;
    }
    
    // Standard Setter
    public void SetFiller4(string value)
    {
        _Filler4 = value;
    }
    
    // Get<>AsString()
    public string GetFiller4AsString()
    {
        return _Filler4.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller4AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler4 = value;
    }
    
    // Standard Getter
    public string GetD105BargainDate()
    {
        return _D105BargainDate;
    }
    
    // Standard Setter
    public void SetD105BargainDate(string value)
    {
        _D105BargainDate = value;
    }
    
    // Get<>AsString()
    public string GetD105BargainDateAsString()
    {
        return _D105BargainDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD105BargainDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D105BargainDate = value;
    }
    
    // Standard Getter
    public string GetFiller5()
    {
        return _Filler5;
    }
    
    // Standard Setter
    public void SetFiller5(string value)
    {
        _Filler5 = value;
    }
    
    // Get<>AsString()
    public string GetFiller5AsString()
    {
        return _Filler5.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler5 = value;
    }
    
    // Standard Getter
    public decimal GetD105Holding()
    {
        return _D105Holding;
    }
    
    // Standard Setter
    public void SetD105Holding(decimal value)
    {
        _D105Holding = value;
    }
    
    // Get<>AsString()
    public string GetD105HoldingAsString()
    {
        return _D105Holding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD105HoldingAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D105Holding = parsed;
    }
    
    // Standard Getter
    public string GetD105HoldingX()
    {
        return _D105HoldingX;
    }
    
    // Standard Setter
    public void SetD105HoldingX(string value)
    {
        _D105HoldingX = value;
    }
    
    // Get<>AsString()
    public string GetD105HoldingXAsString()
    {
        return _D105HoldingX.PadRight(18);
    }
    
    // Set<>AsString()
    public void SetD105HoldingXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D105HoldingX = value;
    }
    
    // Standard Getter
    public string GetFiller6()
    {
        return _Filler6;
    }
    
    // Standard Setter
    public void SetFiller6(string value)
    {
        _Filler6 = value;
    }
    
    // Get<>AsString()
    public string GetFiller6AsString()
    {
        return _Filler6.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler6 = value;
    }
    
    // Standard Getter
    public string GetD105MovementDesc()
    {
        return _D105MovementDesc;
    }
    
    // Standard Setter
    public void SetD105MovementDesc(string value)
    {
        _D105MovementDesc = value;
    }
    
    // Get<>AsString()
    public string GetD105MovementDescAsString()
    {
        return _D105MovementDesc.PadRight(30);
    }
    
    // Set<>AsString()
    public void SetD105MovementDescAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D105MovementDesc = value;
    }
    
    // Standard Getter
    public string GetFiller7()
    {
        return _Filler7;
    }
    
    // Standard Setter
    public void SetFiller7(string value)
    {
        _Filler7 = value;
    }
    
    // Get<>AsString()
    public string GetFiller7AsString()
    {
        return _Filler7.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler7 = value;
    }
    
    // Standard Getter
    public decimal GetD105Cost()
    {
        return _D105Cost;
    }
    
    // Standard Setter
    public void SetD105Cost(decimal value)
    {
        _D105Cost = value;
    }
    
    // Get<>AsString()
    public string GetD105CostAsString()
    {
        return _D105Cost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD105CostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D105Cost = parsed;
    }
    
    // Standard Getter
    public string GetFiller8()
    {
        return _Filler8;
    }
    
    // Standard Setter
    public void SetFiller8(string value)
    {
        _Filler8 = value;
    }
    
    // Get<>AsString()
    public string GetFiller8AsString()
    {
        return _Filler8.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler8 = value;
    }
    
    // Standard Getter
    public decimal GetD105Balance()
    {
        return _D105Balance;
    }
    
    // Standard Setter
    public void SetD105Balance(decimal value)
    {
        _D105Balance = value;
    }
    
    // Get<>AsString()
    public string GetD105BalanceAsString()
    {
        return _D105Balance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD105BalanceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D105Balance = parsed;
    }
    
    // Standard Getter
    public string GetFiller9()
    {
        return _Filler9;
    }
    
    // Standard Setter
    public void SetFiller9(string value)
    {
        _Filler9 = value;
    }
    
    // Get<>AsString()
    public string GetFiller9AsString()
    {
        return _Filler9.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler9 = value;
    }
    
    // Standard Getter
    public string GetD105HoldingFlag()
    {
        return _D105HoldingFlag;
    }
    
    // Standard Setter
    public void SetD105HoldingFlag(string value)
    {
        _D105HoldingFlag = value;
    }
    
    // Get<>AsString()
    public string GetD105HoldingFlagAsString()
    {
        return _D105HoldingFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD105HoldingFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D105HoldingFlag = value;
    }
    
    // Standard Getter
    public string GetFiller10()
    {
        return _Filler10;
    }
    
    // Standard Setter
    public void SetFiller10(string value)
    {
        _Filler10 = value;
    }
    
    // Get<>AsString()
    public string GetFiller10AsString()
    {
        return _Filler10.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller10AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler10 = value;
    }
    
    // Standard Getter
    public string GetD105HoldingSign()
    {
        return _D105HoldingSign;
    }
    
    // Standard Setter
    public void SetD105HoldingSign(string value)
    {
        _D105HoldingSign = value;
    }
    
    // Get<>AsString()
    public string GetD105HoldingSignAsString()
    {
        return _D105HoldingSign.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD105HoldingSignAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D105HoldingSign = value;
    }
    
    // Standard Getter
    public string GetFiller11()
    {
        return _Filler11;
    }
    
    // Standard Setter
    public void SetFiller11(string value)
    {
        _Filler11 = value;
    }
    
    // Get<>AsString()
    public string GetFiller11AsString()
    {
        return _Filler11.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller11AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler11 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}