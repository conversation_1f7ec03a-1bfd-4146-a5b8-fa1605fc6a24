using System;
using System.Text;
using EquityProject.CgtabortPGM;
using EquityProject.EqusecalDTO;
namespace EquityProject.EqusecalPGM
{
    // Equsecal Class Definition

    //Equsecal Class Constructor
    public class Equsecal
    {
        // Declare Equsecal Class private variables
        private Gvar _gvar = new Gvar();
        private Ivar _ivar = new Ivar();

        // Declare Equsecal Class getters setters
        public Gvar GetGvar() { return _gvar; }
        public Ivar GetIvar() { return _ivar; }



        // Run() method
        public void Run(Gvar gvar, Ivar ivar)
        {
            // Main entry point - calls AMain paragraph
            AMain(gvar, ivar);
        }

        // CallSub() method
        public void CallSub(Ivar ivar)
        {
            // Implement subroutine call logic here
            // This method can be used for external subroutine calls if needed
        }

        /// <summary>
        /// COBOL paragraph: A-MAIN
        /// This method sets the EQUSECAL-CALENDAR-NOT-IN-USE flag to TRUE, processes
        /// the program by performing the steps in B-Process and C-Terminate,
        /// and then stops the program run.
        /// </summary>
        /// <param name="gvar">Global variables object</param>
        /// <param name="ivar">Input variables object</param>
        public void AMain(Gvar gvar, Ivar ivar)
        {
            // SET EQUSECAL-CALENDAR-NOT-IN-USE TO TRUE
            ivar.GetEqusecalLinkage().GetEqusecalParametersOut().SetEqusecalReturnCode(1); // Calendar not in use

            // PERFORM B-PROCESS
            BProcess(gvar, ivar);

            // PERFORM C-TERMINATE
            CTerminate(gvar, ivar);

            // EXIT PROGRAM (method naturally exits)
        }

        /// <summary>
        /// COBOL paragraph B-PROCESS converted to C# method. This method processes the validation and file operations
        /// based on the given parameters and sets appropriate flags and fields.
        /// </summary>
        /// <param name="gvar">Global variables container</param>
        /// <param name="ivar">Input variables container</param>
        public void BProcess(Gvar gvar, Ivar ivar)
        {
            // Set EQUSECAL-INVALID-PARAMETERS to TRUE
            ivar.GetEqusecalLinkage().GetEqusecalParametersOut().SetEqusecalReturnCode(2); // Invalid parameters

            // Move SPACES to D4-RECORD, D8-RECORD, D153-RECORD
            gvar.GetD4Record().SetD4RecordAsString("");
            gvar.GetD8Record().SetD8RecordAsString("");
            ivar.GetD153Record().SetD153RecordAsString("");

            // Validate IF conditions
            if (string.IsNullOrEmpty(ivar.GetEqusecalLinkage().GetEqusecalParametersIn().GetEqusecalFundCode()) ||
                ivar.GetEqusecalLinkage().GetEqusecalParametersIn().GetEqusecalMasterFileYearN() < Gvar.FIRST_MASTER_YEAR ||
                ivar.GetEqusecalLinkage().GetEqusecalParametersIn().GetEqusecalMasterFileYearN() > Gvar.LAST_MASTER_YEAR)
            {
                // GO TO B-EXIT
                return;
            }

            // Move FUND-FILE to L-FILE-NAME
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.FUND_FILE);

            // Move OPEN-I-O to L-FILE-ACTION
            gvar.GetCgtfilesLinkage().SetLFileAction("OPEN-I-O");

            // PERFORM X-CALL-CGTFILES
            XCallCgtfiles(gvar, ivar);

            // EVALUATE file operation result
            string returnCode = gvar.GetCgtfilesLinkage().GetLFileReturnCode();
            if (returnCode == "00" || returnCode == "SUCCESSFUL")
            {
                // SET FUND-CLOSE-NEEDED TO TRUE
                gvar.SetWFundCloseNeeded("Y");
            }
            else if (returnCode == "OPEN-CREATED-NEW-FILE")
            {
                // SET FUND-CLOSE-NEEDED TO TRUE
                gvar.SetWFundCloseNeeded("Y");
            }
            else if (returnCode == "FILE-WAS-ALREADY-OPEN")
            {
                // SET FUND-CLOSE-NOT-NEEDED TO TRUE
                gvar.SetWFundCloseNeeded("N");
            }
            else
            {
                // PERFORM X-CALL-CGTABORT
                XCallCgtabort(gvar, ivar);
                return; // GO TO B-EXIT
            }

            // Move EQUSECAL-FUND-CODE to L-FILE-RECORD-AREA
            gvar.GetLFileRecordArea().SetLFileRecordAreaAsString(ivar.GetEqusecalLinkage().GetEqusecalParametersIn().GetEqusecalFundCode());

            // Move READ-RECORD to L-FILE-ACTION
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_RECORD);

            // PERFORM X-CALL-CGTFILES
            XCallCgtfiles(gvar, ivar);

            // IF SUCCESSFUL
            if (gvar.GetCgtfilesLinkage().GetLFileReturnCode() == "00")
            {
                // Move L-FILE-RECORD-AREA to D4-RECORD
                gvar.GetD4Record().SetD4RecordAsString(gvar.GetLFileRecordArea().GetLFileRecordAreaAsString());
            }
            else
            {
                // GO TO B-EXIT
                return;
            }

            // Move PARAMETER-FILE to L-FILE-NAME
            gvar.GetCgtfilesLinkage().SetLFileName(Gvar.PARAMETER_FILE);

            // Move OPEN-I-O to L-FILE-ACTION
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.OPEN_I_O);

            // PERFORM X-CALL-CGTFILES
            XCallCgtfiles(gvar, ivar);

            // EVALUATE file operation result
            returnCode = gvar.GetCgtfilesLinkage().GetLFileReturnCode();
            if (returnCode == "00" || returnCode == "SUCCESSFUL")
            {
                // SET PARM-CLOSE-NEEDED TO TRUE
                gvar.SetWParmCloseNeeded("'Y'");
            }
            else if (returnCode == "OPEN-CREATED-NEW-FILE")
            {
                // SET PARM-CLOSE-NEEDED TO TRUE
                gvar.SetWParmCloseNeeded("'Y'");
            }
            else if (returnCode == "FILE-WAS-ALREADY-OPEN")
            {
                // SET PARM-CLOSE-NOT-NEEDED TO TRUE
                gvar.SetWParmCloseNeeded("'N'");
            }
            else
            {
                // PERFORM X-CALL-CGTABORT
                XCallCgtabort(gvar, ivar);
                return;
            }

            // Move READ-RECORD to L-FILE-ACTION
            gvar.GetCgtfilesLinkage().SetLFileAction("read-RECORD");

            // Move 'CGT' to L-FILE-RECORD-AREA
            gvar.GetLFileRecordArea().SetLFileRecordAreaAsString("CGT");

            // PERFORM X-CALL-CGTFILES
            XCallCgtfiles(gvar, ivar);

            // IF SUCCESSFUL
            if (gvar.GetCgtfilesLinkage().GetLFileReturnCode() == "00")
            {
                // Move L-FILE-RECORD-AREA to D8-RECORD
                gvar.GetD8Record().SetD8RecordAsString(gvar.GetLFileRecordArea().GetLFileRecordAreaAsString());
            }
            else
            {
                // PERFORM X-CALL-CGTABORT
                XCallCgtabort(gvar, ivar);
                return; // GO TO B-EXIT
            }

            // IF D8-USE-PERIOD-END-CALENDARS = 'Y' AND D4-CALENDAR-ID <> SPACES
            if (gvar.GetD8Record().GetD8UsePeriodEndCalendars() == "Y" &&
                !string.IsNullOrEmpty(gvar.GetD4Record().GetD4CalendarId()))
            {
                // Move PERIOD-END-CALENDAR-FILE to L-FILE-NAME
                gvar.GetCgtfilesLinkage().SetLFileName(Gvar.PERIOD_END_CALENDAR_FILE);

                // Move OPEN-I-O to L-FILE-ACTION
                gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.OPEN_I_O);

                // PERFORM X-CALL-CGTFILES
                XCallCgtfiles(gvar, ivar);

                // EVALUATE file operation result
                returnCode = gvar.GetCgtfilesLinkage().GetLFileReturnCode();
                if (returnCode == "00" || returnCode == "SUCCESSFUL")
                {
                    // SET CALS-CLOSE-NEEDED TO TRUE
                    gvar.SetWCalsCloseNeeded("'Y'");
                }
                else if (returnCode == "OPEN-CREATED-NEW-FILE")
                {
                    // SET CALS-CLOSE-NEEDED TO TRUE
                    gvar.SetWCalsCloseNeeded("'Y'");
                }
                else if (returnCode == "FILE-WAS-ALREADY-OPEN")
                {
                    // SET CALS-CLOSE-NOT-NEEDED TO TRUE
                    gvar.SetWCalsCloseNeeded("'N'");
                }
                else
                {
                    // PERFORM X-CALL-CGTABORT
                    XCallCgtabort(gvar, ivar);
                    return;
                }

                // Move D4-CALENDAR-ID to L-FILE-RECORD-AREA
                gvar.GetLFileRecordArea().SetLFileRecordAreaAsString(gvar.GetD4Record().GetD4CalendarId());

                // Move READ-RECORD to L-FILE-ACTION
                gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.READ_RECORD);

                // PERFORM X-CALL-CGTFILES
                XCallCgtfiles(gvar, ivar);

                // IF SUCCESSFUL
                if (gvar.GetCgtfilesLinkage().GetLFileReturnCode() == "00")
                {
                    // Move L-FILE-RECORD-AREA to D153-RECORD
                    ivar.GetD153Record().SetD153RecordAsString(gvar.GetLFileRecordArea().GetLFileRecordAreaAsString());
                }
                else
                {
                    // PERFORM X-CALL-CGTABORT
                    XCallCgtabort(gvar, ivar);
                    return;
                }
            }

            // Final calendar check
            if (gvar.GetD8Record().GetD8UsePeriodEndCalendars() == "Y" &&
                !string.IsNullOrEmpty(gvar.GetD4Record().GetD4CalendarId()) &&
                ivar.GetEqusecalLinkage().GetEqusecalParametersIn().GetEqusecalMasterFileYearN() >= ivar.GetD153Record().GetD153StartYear())
            {
                // SET EQUSECAL-CALENDAR-IN-USE TO TRUE
                ivar.GetEqusecalLinkage().GetEqusecalParametersOut().SetEqusecalReturnCode(0); // Calendar in use
            }
            else
            {
                // SET EQUSECAL-CALENDAR-NOT-IN-USE TO TRUE
                ivar.GetEqusecalLinkage().GetEqusecalParametersOut().SetEqusecalReturnCode(1); // Calendar not in use
            }
        }

        /// <summary>
        /// COBOL paragraph C-TERMINATE - Terminalization Process
        /// This method handles the terminalization process by setting file actions and performing
        /// necessary file closures based on the flags FUND-CLOSE-NEEDED, PARM-CLOSE-NEEDED,
        /// and CALS-CLOSE-NEEDED.
        /// </summary>
        /// <param name="gvar">Global variables including file names and close flags.</param>
        /// <param name="ivar">Other input variables</param>
        public void CTerminate(Gvar gvar, Ivar ivar)
        {
            // Move CLOSE-FILE to L-FILE-ACTION
            gvar.GetCgtfilesLinkage().SetLFileAction(Gvar.CLOSE_FILE);

            // Check if FUND-CLOSE-NEEDED is true
            if (gvar.IsFundCloseNeeded())
            {
                // Move FUND-FILE to L-FILE-NAME
                gvar.GetCgtfilesLinkage().SetLFileName(Gvar.FUND_FILE);

                // PERFORM X-CALL-CGTFILES
                XCallCgtfiles(gvar, ivar);
            }

            // Check if PARM-CLOSE-NEEDED is true
            if (gvar.IsParmCloseNeeded())
            {
                // Move PARAMETER-FILE to L-FILE-NAME
                gvar.GetCgtfilesLinkage().SetLFileName(Gvar.PARAMETER_FILE);

                // PERFORM X-CALL-CGTFILES
                XCallCgtfiles(gvar, ivar);
            }

            // Check if CALS-CLOSE-NEEDED is true
            if (gvar.IsCalsCloseNeeded())
            {
                // Move PERIOD-END-CALENDAR-FILE to L-FILE-NAME
                gvar.GetCgtfilesLinkage().SetLFileName(Gvar.PERIOD_END_CALENDAR_FILE);

                // PERFORM X-CALL-CGTFILES
                XCallCgtfiles(gvar, ivar);
            }
        }

        /// <summary>
        /// The XCallCgtfiles method is equivalent to the COBOL paragraph xCallCgtfiles.
        /// This method calls the 'CGTFILES' external program with the specified linkage
        /// and common linkage areas.
        /// </summary>
        /// <param name="gvar">Global variables container.</param>
        /// <param name="ivar">Input variables container (not used in this method).</param>
        public void XCallCgtfiles(Gvar gvar, Ivar ivar)
        {
            // must uncomment 
            // Create an instance of the external program
            //var cgtfiles = new Cgtfiles();

            // Call the Run method with the parameters specified in the COBOL USING clause
            // CALL 'CGTFILES' USING CGTFILES-LINKAGE L-FILE-RECORD-AREA COMMON-LINKAGE
            //cgtfiles.Run(gvar.GetCgtfilesLinkage(), gvar.GetLFileRecordArea(), gvar.GetCommonLinkage());
        }

        /// <summary>
        /// Executes the CGTABORT program with the specified linkage parameters.
        /// Converted from COBOL paragraph: xCallCgtabort
        /// </summary>
        /// <param name="gvar">Global variables.</param>
        /// <param name="ivar">Input variables.</param>
        public void XCallCgtabort(Gvar gvar, Ivar ivar)
        {
            // Move L-FILE-RETURN-CODE to L-ABORT-FILE-STATUS
            gvar.GetCgtabortLinkage().SetLAbortFileStatus(gvar.GetCgtfilesLinkage().GetLFileReturnCode());

            // Move W-PROGRAM-NAME to L-ABORT-PROGRAM-NAME
            gvar.GetCgtabortLinkage().SetLAbortProgramName(gvar.GetWProgramName());

            // Call the CGTABORT program
            // CALL 'CGTABORT' USING COMMON-LINKAGE CGTABORT-LINKAGE
            Cgtabort cgtabort = new Cgtabort();
            cgtabort.GetIvar().SetCgtabortLinkageAsString(gvar.GetCgtabortLinkageAsString());
            cgtabort.Run(cgtabort.GetGvar(), cgtabort.GetIvar());
        }
    }
}
