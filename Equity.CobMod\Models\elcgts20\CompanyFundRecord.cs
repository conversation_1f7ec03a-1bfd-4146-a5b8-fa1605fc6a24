using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing CompanyFundRecord Data Structure

public class CompanyFundRecord
{
    private static int _size = 98;
    // [DEBUG] Class: CompanyFundRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: CfCoAcLk, is_external=, is_static_class=False, static_prefix=
    private string _CfCoAcLk ="";
    
    
    
    
    // [DEBUG] Field: CfCompanyFundName, is_external=, is_static_class=False, static_prefix=
    private CfCompanyFundName _CfCompanyFundName = new CfCompanyFundName();
    
    
    
    
    // [DEBUG] Field: CfCgtPeriodStartDate, is_external=, is_static_class=False, static_prefix=
    private int _CfCgtPeriodStartDate =0;
    
    
    
    
    // [DEBUG] Field: CfCgtPeriodStartDateR, is_external=, is_static_class=False, static_prefix=
    private CfCgtPeriodStartDateR _CfCgtPeriodStartDateR = new CfCgtPeriodStartDateR();
    
    
    
    
    // [DEBUG] Field: CfCgtPeriodEndDate, is_external=, is_static_class=False, static_prefix=
    private int _CfCgtPeriodEndDate =0;
    
    
    
    
    // [DEBUG] Field: CfCgtPeriodEndDateRed, is_external=, is_static_class=False, static_prefix=
    private CfCgtPeriodEndDateRed _CfCgtPeriodEndDateRed = new CfCgtPeriodEndDateRed();
    
    
    
    
    // [DEBUG] Field: CfPre65FixedInterest, is_external=, is_static_class=False, static_prefix=
    private int _CfPre65FixedInterest =0;
    
    
    
    
    // [DEBUG] Field: CfPre65Ordinary, is_external=, is_static_class=False, static_prefix=
    private int _CfPre65Ordinary =0;
    
    
    
    
    // [DEBUG] Field: CfParallelPooling, is_external=, is_static_class=False, static_prefix=
    private int _CfParallelPooling =0;
    
    
    
    
    // [DEBUG] Field: CfCalculationRequest, is_external=, is_static_class=False, static_prefix=
    private int _CfCalculationRequest =0;
    
    
    
    
    // [DEBUG] Field: Filler9, is_external=, is_static_class=False, static_prefix=
    private string _Filler9 ="";
    
    
    
    
    // [DEBUG] Field: CfDeemedDisposalState, is_external=, is_static_class=False, static_prefix=
    private int _CfDeemedDisposalState =0;
    
    
    
    
    // [DEBUG] Field: CfCalendarCode, is_external=, is_static_class=False, static_prefix=
    private string _CfCalendarCode ="";
    
    
    
    
    // [DEBUG] Field: CfPriceTypeId, is_external=, is_static_class=False, static_prefix=
    private string _CfPriceTypeId ="";
    
    
    
    
    // [DEBUG] Field: CfUseEarlierPrice, is_external=, is_static_class=False, static_prefix=
    private string _CfUseEarlierPrice ="";
    
    
    
    
    // [DEBUG] Field: CfFundId, is_external=, is_static_class=False, static_prefix=
    private string _CfFundId ="";
    
    
    
    
    // [DEBUG] Field: CfOlabFund, is_external=, is_static_class=False, static_prefix=
    private string _CfOlabFund ="";
    
    
    
    
    // [DEBUG] Field: CfLifeSummaryFund, is_external=, is_static_class=False, static_prefix=
    private string _CfLifeSummaryFund ="";
    
    
    
    
    // [DEBUG] Field: CfUseLossesFlag, is_external=, is_static_class=False, static_prefix=
    private string _CfUseLossesFlag ="";
    
    
    
    
    // [DEBUG] Field: CfFa2012ReIndexation, is_external=, is_static_class=False, static_prefix=
    private string _CfFa2012ReIndexation ="";
    
    
    
    
    // [DEBUG] Field: CfOldSummaryFundCode, is_external=, is_static_class=False, static_prefix=
    private string _CfOldSummaryFundCode ="";
    
    
    
    
    // [DEBUG] Field: CfSummaryFundCode, is_external=, is_static_class=False, static_prefix=
    private string _CfSummaryFundCode ="";
    
    
    
    
    // [DEBUG] Field: CfPost2012SummaryFundCode, is_external=, is_static_class=False, static_prefix=
    private string _CfPost2012SummaryFundCode ="";
    
    
    
    
    
    // Serialization methods
    public string GetCompanyFundRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_CfCoAcLk.PadRight(4));
        result.Append(_CfCompanyFundName.GetCfCompanyFundNameAsString());
        result.Append(_CfCgtPeriodStartDate.ToString().PadLeft(6, '0'));
        result.Append(_CfCgtPeriodStartDateR.GetCfCgtPeriodStartDateRAsString());
        result.Append(_CfCgtPeriodEndDate.ToString().PadLeft(6, '0'));
        result.Append(_CfCgtPeriodEndDateRed.GetCfCgtPeriodEndDateRedAsString());
        result.Append(_CfPre65FixedInterest.ToString().PadLeft(1, '0'));
        result.Append(_CfPre65Ordinary.ToString().PadLeft(1, '0'));
        result.Append(_CfParallelPooling.ToString().PadLeft(1, '0'));
        result.Append(_CfCalculationRequest.ToString().PadLeft(1, '0'));
        result.Append(_Filler9.PadRight(4));
        result.Append(_CfDeemedDisposalState.ToString().PadLeft(1, '0'));
        result.Append(_CfCalendarCode.PadRight(4));
        result.Append(_CfPriceTypeId.PadRight(0));
        result.Append(_CfUseEarlierPrice.PadRight(0));
        result.Append(_CfFundId.PadRight(0));
        result.Append(_CfOlabFund.PadRight(0));
        result.Append(_CfLifeSummaryFund.PadRight(0));
        result.Append(_CfUseLossesFlag.PadRight(0));
        result.Append(_CfFa2012ReIndexation.PadRight(1));
        result.Append(_CfOldSummaryFundCode.PadRight(0));
        result.Append(_CfSummaryFundCode.PadRight(0));
        result.Append(_CfPost2012SummaryFundCode.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetCompanyFundRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetCfCoAcLk(extracted);
        }
        offset += 4;
        if (offset + 60 <= data.Length)
        {
            _CfCompanyFundName.SetCfCompanyFundNameAsString(data.Substring(offset, 60));
        }
        else
        {
            _CfCompanyFundName.SetCfCompanyFundNameAsString(data.Substring(offset));
        }
        offset += 60;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCfCgtPeriodStartDate(parsedInt);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            _CfCgtPeriodStartDateR.SetCfCgtPeriodStartDateRAsString(data.Substring(offset, 6));
        }
        else
        {
            _CfCgtPeriodStartDateR.SetCfCgtPeriodStartDateRAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCfCgtPeriodEndDate(parsedInt);
        }
        offset += 6;
        if (offset + 2 <= data.Length)
        {
            _CfCgtPeriodEndDateRed.SetCfCgtPeriodEndDateRedAsString(data.Substring(offset, 2));
        }
        else
        {
            _CfCgtPeriodEndDateRed.SetCfCgtPeriodEndDateRedAsString(data.Substring(offset));
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCfPre65FixedInterest(parsedInt);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCfPre65Ordinary(parsedInt);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCfParallelPooling(parsedInt);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCfCalculationRequest(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller9(extracted);
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCfDeemedDisposalState(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetCfCalendarCode(extracted);
        }
        offset += 4;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCfPriceTypeId(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCfUseEarlierPrice(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCfFundId(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCfOlabFund(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCfLifeSummaryFund(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCfUseLossesFlag(extracted);
        }
        offset += 0;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetCfFa2012ReIndexation(extracted);
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCfOldSummaryFundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCfSummaryFundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCfPost2012SummaryFundCode(extracted);
        }
        offset += 0;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCompanyFundRecordAsString();
    }
    // Set<>String Override function
    public void SetCompanyFundRecord(string value)
    {
        SetCompanyFundRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCfCoAcLk()
    {
        return _CfCoAcLk;
    }
    
    // Standard Setter
    public void SetCfCoAcLk(string value)
    {
        _CfCoAcLk = value;
    }
    
    // Get<>AsString()
    public string GetCfCoAcLkAsString()
    {
        return _CfCoAcLk.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetCfCoAcLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CfCoAcLk = value;
    }
    
    // Standard Getter
    public CfCompanyFundName GetCfCompanyFundName()
    {
        return _CfCompanyFundName;
    }
    
    // Standard Setter
    public void SetCfCompanyFundName(CfCompanyFundName value)
    {
        _CfCompanyFundName = value;
    }
    
    // Get<>AsString()
    public string GetCfCompanyFundNameAsString()
    {
        return _CfCompanyFundName != null ? _CfCompanyFundName.GetCfCompanyFundNameAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCfCompanyFundNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CfCompanyFundName == null)
        {
            _CfCompanyFundName = new CfCompanyFundName();
        }
        _CfCompanyFundName.SetCfCompanyFundNameAsString(value);
    }
    
    // Standard Getter
    public int GetCfCgtPeriodStartDate()
    {
        return _CfCgtPeriodStartDate;
    }
    
    // Standard Setter
    public void SetCfCgtPeriodStartDate(int value)
    {
        _CfCgtPeriodStartDate = value;
    }
    
    // Get<>AsString()
    public string GetCfCgtPeriodStartDateAsString()
    {
        return _CfCgtPeriodStartDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetCfCgtPeriodStartDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _CfCgtPeriodStartDate = parsed;
    }
    
    // Standard Getter
    public CfCgtPeriodStartDateR GetCfCgtPeriodStartDateR()
    {
        return _CfCgtPeriodStartDateR;
    }
    
    // Standard Setter
    public void SetCfCgtPeriodStartDateR(CfCgtPeriodStartDateR value)
    {
        _CfCgtPeriodStartDateR = value;
    }
    
    // Get<>AsString()
    public string GetCfCgtPeriodStartDateRAsString()
    {
        return _CfCgtPeriodStartDateR != null ? _CfCgtPeriodStartDateR.GetCfCgtPeriodStartDateRAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCfCgtPeriodStartDateRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CfCgtPeriodStartDateR == null)
        {
            _CfCgtPeriodStartDateR = new CfCgtPeriodStartDateR();
        }
        _CfCgtPeriodStartDateR.SetCfCgtPeriodStartDateRAsString(value);
    }
    
    // Standard Getter
    public int GetCfCgtPeriodEndDate()
    {
        return _CfCgtPeriodEndDate;
    }
    
    // Standard Setter
    public void SetCfCgtPeriodEndDate(int value)
    {
        _CfCgtPeriodEndDate = value;
    }
    
    // Get<>AsString()
    public string GetCfCgtPeriodEndDateAsString()
    {
        return _CfCgtPeriodEndDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetCfCgtPeriodEndDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _CfCgtPeriodEndDate = parsed;
    }
    
    // Standard Getter
    public CfCgtPeriodEndDateRed GetCfCgtPeriodEndDateRed()
    {
        return _CfCgtPeriodEndDateRed;
    }
    
    // Standard Setter
    public void SetCfCgtPeriodEndDateRed(CfCgtPeriodEndDateRed value)
    {
        _CfCgtPeriodEndDateRed = value;
    }
    
    // Get<>AsString()
    public string GetCfCgtPeriodEndDateRedAsString()
    {
        return _CfCgtPeriodEndDateRed != null ? _CfCgtPeriodEndDateRed.GetCfCgtPeriodEndDateRedAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCfCgtPeriodEndDateRedAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CfCgtPeriodEndDateRed == null)
        {
            _CfCgtPeriodEndDateRed = new CfCgtPeriodEndDateRed();
        }
        _CfCgtPeriodEndDateRed.SetCfCgtPeriodEndDateRedAsString(value);
    }
    
    // Standard Getter
    public int GetCfPre65FixedInterest()
    {
        return _CfPre65FixedInterest;
    }
    
    // Standard Setter
    public void SetCfPre65FixedInterest(int value)
    {
        _CfPre65FixedInterest = value;
    }
    
    // Get<>AsString()
    public string GetCfPre65FixedInterestAsString()
    {
        return _CfPre65FixedInterest.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetCfPre65FixedInterestAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _CfPre65FixedInterest = parsed;
    }
    
    // Standard Getter
    public int GetCfPre65Ordinary()
    {
        return _CfPre65Ordinary;
    }
    
    // Standard Setter
    public void SetCfPre65Ordinary(int value)
    {
        _CfPre65Ordinary = value;
    }
    
    // Get<>AsString()
    public string GetCfPre65OrdinaryAsString()
    {
        return _CfPre65Ordinary.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetCfPre65OrdinaryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _CfPre65Ordinary = parsed;
    }
    
    // Standard Getter
    public int GetCfParallelPooling()
    {
        return _CfParallelPooling;
    }
    
    // Standard Setter
    public void SetCfParallelPooling(int value)
    {
        _CfParallelPooling = value;
    }
    
    // Get<>AsString()
    public string GetCfParallelPoolingAsString()
    {
        return _CfParallelPooling.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetCfParallelPoolingAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _CfParallelPooling = parsed;
    }
    
    // Standard Getter
    public int GetCfCalculationRequest()
    {
        return _CfCalculationRequest;
    }
    
    // Standard Setter
    public void SetCfCalculationRequest(int value)
    {
        _CfCalculationRequest = value;
    }
    
    // Get<>AsString()
    public string GetCfCalculationRequestAsString()
    {
        return _CfCalculationRequest.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetCfCalculationRequestAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _CfCalculationRequest = parsed;
    }
    
    // Standard Getter
    public string GetFiller9()
    {
        return _Filler9;
    }
    
    // Standard Setter
    public void SetFiller9(string value)
    {
        _Filler9 = value;
    }
    
    // Get<>AsString()
    public string GetFiller9AsString()
    {
        return _Filler9.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller9AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler9 = value;
    }
    
    // Standard Getter
    public int GetCfDeemedDisposalState()
    {
        return _CfDeemedDisposalState;
    }
    
    // Standard Setter
    public void SetCfDeemedDisposalState(int value)
    {
        _CfDeemedDisposalState = value;
    }
    
    // Get<>AsString()
    public string GetCfDeemedDisposalStateAsString()
    {
        return _CfDeemedDisposalState.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetCfDeemedDisposalStateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _CfDeemedDisposalState = parsed;
    }
    
    // Standard Getter
    public string GetCfCalendarCode()
    {
        return _CfCalendarCode;
    }
    
    // Standard Setter
    public void SetCfCalendarCode(string value)
    {
        _CfCalendarCode = value;
    }
    
    // Get<>AsString()
    public string GetCfCalendarCodeAsString()
    {
        return _CfCalendarCode.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetCfCalendarCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CfCalendarCode = value;
    }
    
    // Standard Getter
    public string GetCfPriceTypeId()
    {
        return _CfPriceTypeId;
    }
    
    // Standard Setter
    public void SetCfPriceTypeId(string value)
    {
        _CfPriceTypeId = value;
    }
    
    // Get<>AsString()
    public string GetCfPriceTypeIdAsString()
    {
        return _CfPriceTypeId.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCfPriceTypeIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CfPriceTypeId = value;
    }
    
    // Standard Getter
    public string GetCfUseEarlierPrice()
    {
        return _CfUseEarlierPrice;
    }
    
    // Standard Setter
    public void SetCfUseEarlierPrice(string value)
    {
        _CfUseEarlierPrice = value;
    }
    
    // Get<>AsString()
    public string GetCfUseEarlierPriceAsString()
    {
        return _CfUseEarlierPrice.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCfUseEarlierPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CfUseEarlierPrice = value;
    }
    
    // Standard Getter
    public string GetCfFundId()
    {
        return _CfFundId;
    }
    
    // Standard Setter
    public void SetCfFundId(string value)
    {
        _CfFundId = value;
    }
    
    // Get<>AsString()
    public string GetCfFundIdAsString()
    {
        return _CfFundId.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCfFundIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CfFundId = value;
    }
    
    // Standard Getter
    public string GetCfOlabFund()
    {
        return _CfOlabFund;
    }
    
    // Standard Setter
    public void SetCfOlabFund(string value)
    {
        _CfOlabFund = value;
    }
    
    // Get<>AsString()
    public string GetCfOlabFundAsString()
    {
        return _CfOlabFund.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCfOlabFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CfOlabFund = value;
    }
    
    // Standard Getter
    public string GetCfLifeSummaryFund()
    {
        return _CfLifeSummaryFund;
    }
    
    // Standard Setter
    public void SetCfLifeSummaryFund(string value)
    {
        _CfLifeSummaryFund = value;
    }
    
    // Get<>AsString()
    public string GetCfLifeSummaryFundAsString()
    {
        return _CfLifeSummaryFund.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCfLifeSummaryFundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CfLifeSummaryFund = value;
    }
    
    // Standard Getter
    public string GetCfUseLossesFlag()
    {
        return _CfUseLossesFlag;
    }
    
    // Standard Setter
    public void SetCfUseLossesFlag(string value)
    {
        _CfUseLossesFlag = value;
    }
    
    // Get<>AsString()
    public string GetCfUseLossesFlagAsString()
    {
        return _CfUseLossesFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCfUseLossesFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CfUseLossesFlag = value;
    }
    
    // Standard Getter
    public string GetCfFa2012ReIndexation()
    {
        return _CfFa2012ReIndexation;
    }
    
    // Standard Setter
    public void SetCfFa2012ReIndexation(string value)
    {
        _CfFa2012ReIndexation = value;
    }
    
    // Get<>AsString()
    public string GetCfFa2012ReIndexationAsString()
    {
        return _CfFa2012ReIndexation.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetCfFa2012ReIndexationAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CfFa2012ReIndexation = value;
    }
    
    // Standard Getter
    public string GetCfOldSummaryFundCode()
    {
        return _CfOldSummaryFundCode;
    }
    
    // Standard Setter
    public void SetCfOldSummaryFundCode(string value)
    {
        _CfOldSummaryFundCode = value;
    }
    
    // Get<>AsString()
    public string GetCfOldSummaryFundCodeAsString()
    {
        return _CfOldSummaryFundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCfOldSummaryFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CfOldSummaryFundCode = value;
    }
    
    // Standard Getter
    public string GetCfSummaryFundCode()
    {
        return _CfSummaryFundCode;
    }
    
    // Standard Setter
    public void SetCfSummaryFundCode(string value)
    {
        _CfSummaryFundCode = value;
    }
    
    // Get<>AsString()
    public string GetCfSummaryFundCodeAsString()
    {
        return _CfSummaryFundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCfSummaryFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CfSummaryFundCode = value;
    }
    
    // Standard Getter
    public string GetCfPost2012SummaryFundCode()
    {
        return _CfPost2012SummaryFundCode;
    }
    
    // Standard Setter
    public void SetCfPost2012SummaryFundCode(string value)
    {
        _CfPost2012SummaryFundCode = value;
    }
    
    // Get<>AsString()
    public string GetCfPost2012SummaryFundCodeAsString()
    {
        return _CfPost2012SummaryFundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCfPost2012SummaryFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CfPost2012SummaryFundCode = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetCfCompanyFundName(string value)
    {
        _CfCompanyFundName.SetCfCompanyFundNameAsString(value);
    }
    // Nested Class: CfCompanyFundName
    public class CfCompanyFundName
    {
        private static int _size = 60;
        
        // Fields in the class
        
        
        // [DEBUG] Field: CfFundName, is_external=, is_static_class=False, static_prefix=
        private CfCompanyFundName.CfFundName _CfFundName = new CfCompanyFundName.CfFundName();
        
        
        
        
        // [DEBUG] Field: CfType, is_external=, is_static_class=False, static_prefix=
        private CfCompanyFundName.CfType _CfType = new CfCompanyFundName.CfType();
        
        
        
        
        // [DEBUG] Field: Filler6, is_external=, is_static_class=False, static_prefix=
        private CfCompanyFundName.Filler6 _Filler6 = new CfCompanyFundName.Filler6();
        
        
        
        
    public CfCompanyFundName() {}
    
    public CfCompanyFundName(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _CfFundName.SetCfFundNameAsString(data.Substring(offset, CfFundName.GetSize()));
        offset += 50;
        _CfType.SetCfTypeAsString(data.Substring(offset, CfType.GetSize()));
        offset += 1;
        _Filler6.SetFiller6AsString(data.Substring(offset, Filler6.GetSize()));
        offset += 9;
        
    }
    
    // Serialization methods
    public string GetCfCompanyFundNameAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_CfFundName.GetCfFundNameAsString());
        result.Append(_CfType.GetCfTypeAsString());
        result.Append(_Filler6.GetFiller6AsString());
        
        return result.ToString();
    }
    
    public void SetCfCompanyFundNameAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 50 <= data.Length)
        {
            _CfFundName.SetCfFundNameAsString(data.Substring(offset, 50));
        }
        else
        {
            _CfFundName.SetCfFundNameAsString(data.Substring(offset));
        }
        offset += 50;
        if (offset + 1 <= data.Length)
        {
            _CfType.SetCfTypeAsString(data.Substring(offset, 1));
        }
        else
        {
            _CfType.SetCfTypeAsString(data.Substring(offset));
        }
        offset += 1;
        if (offset + 9 <= data.Length)
        {
            _Filler6.SetFiller6AsString(data.Substring(offset, 9));
        }
        else
        {
            _Filler6.SetFiller6AsString(data.Substring(offset));
        }
        offset += 9;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public CfFundName GetCfFundName()
    {
        return _CfFundName;
    }
    
    // Standard Setter
    public void SetCfFundName(CfFundName value)
    {
        _CfFundName = value;
    }
    
    // Get<>AsString()
    public string GetCfFundNameAsString()
    {
        return _CfFundName != null ? _CfFundName.GetCfFundNameAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCfFundNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CfFundName == null)
        {
            _CfFundName = new CfFundName();
        }
        _CfFundName.SetCfFundNameAsString(value);
    }
    
    // Standard Getter
    public CfType GetCfType()
    {
        return _CfType;
    }
    
    // Standard Setter
    public void SetCfType(CfType value)
    {
        _CfType = value;
    }
    
    // Get<>AsString()
    public string GetCfTypeAsString()
    {
        return _CfType != null ? _CfType.GetCfTypeAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCfTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CfType == null)
        {
            _CfType = new CfType();
        }
        _CfType.SetCfTypeAsString(value);
    }
    
    // Standard Getter
    public Filler6 GetFiller6()
    {
        return _Filler6;
    }
    
    // Standard Setter
    public void SetFiller6(Filler6 value)
    {
        _Filler6 = value;
    }
    
    // Get<>AsString()
    public string GetFiller6AsString()
    {
        return _Filler6 != null ? _Filler6.GetFiller6AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller6AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler6 == null)
        {
            _Filler6 = new Filler6();
        }
        _Filler6.SetFiller6AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: CfFundName
    public class CfFundName
    {
        private static int _size = 50;
        
        // Fields in the class
        
        
        // [DEBUG] Field: CfSummaryWord, is_external=, is_static_class=False, static_prefix=
        private string _CfSummaryWord ="";
        
        
        // 88-level condition checks for CfSummaryWord
        public bool IsCfSummaryFund()
        {
            if (this._CfSummaryWord == "'SUMMARY'") return true;
            return false;
        }
        
        
        // [DEBUG] Field: Filler5, is_external=, is_static_class=False, static_prefix=
        private string _Filler5 ="";
        
        
        
        
    public CfFundName() {}
    
    public CfFundName(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCfSummaryWord(data.Substring(offset, 7).Trim());
        offset += 7;
        SetFiller5(data.Substring(offset, 43).Trim());
        offset += 43;
        
    }
    
    // Serialization methods
    public string GetCfFundNameAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_CfSummaryWord.PadRight(7));
        result.Append(_Filler5.PadRight(43));
        
        return result.ToString();
    }
    
    public void SetCfFundNameAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetCfSummaryWord(extracted);
        }
        offset += 7;
        if (offset + 43 <= data.Length)
        {
            string extracted = data.Substring(offset, 43).Trim();
            SetFiller5(extracted);
        }
        offset += 43;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCfSummaryWord()
    {
        return _CfSummaryWord;
    }
    
    // Standard Setter
    public void SetCfSummaryWord(string value)
    {
        _CfSummaryWord = value;
    }
    
    // Get<>AsString()
    public string GetCfSummaryWordAsString()
    {
        return _CfSummaryWord.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetCfSummaryWordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CfSummaryWord = value;
    }
    
    // Standard Getter
    public string GetFiller5()
    {
        return _Filler5;
    }
    
    // Standard Setter
    public void SetFiller5(string value)
    {
        _Filler5 = value;
    }
    
    // Get<>AsString()
    public string GetFiller5AsString()
    {
        return _Filler5.PadRight(43);
    }
    
    // Set<>AsString()
    public void SetFiller5AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler5 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: CfType
public class CfType
{
    private static int _size = 1;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CfFundType, is_external=, is_static_class=False, static_prefix=
    private string _CfFundType ="";
    
    
    // 88-level condition checks for CfFundType
    public bool IsLifeFund()
    {
        if (this._CfFundType == "'L'") return true;
        return false;
    }
    public bool IsCompanyFund()
    {
        if (this._CfFundType == "'C'") return true;
        if (this._CfFundType == "'L'") return true;
        return false;
    }
    public bool IsIndividualFund()
    {
        if (this._CfFundType == "'I'") return true;
        if (this._CfFundType == "'J'") return true;
        if (this._CfFundType == "'Z'") return true;
        if (this._CfFundType == "'T'") return true;
        return false;
    }
    public bool IsIrishLifeFund()
    {
        if (this._CfFundType == "'X'") return true;
        return false;
    }
    
    
public CfType() {}

public CfType(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCfFundType(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetCfTypeAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CfFundType.PadRight(1));
    
    return result.ToString();
}

public void SetCfTypeAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetCfFundType(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetCfFundType()
{
    return _CfFundType;
}

// Standard Setter
public void SetCfFundType(string value)
{
    _CfFundType = value;
}

// Get<>AsString()
public string GetCfFundTypeAsString()
{
    return _CfFundType.PadRight(1);
}

// Set<>AsString()
public void SetCfFundTypeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _CfFundType = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: Filler6
public class Filler6
{
    private static int _size = 9;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler7, is_external=, is_static_class=False, static_prefix=
    private string _Filler7 ="";
    
    
    
    
public Filler6() {}

public Filler6(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller7(data.Substring(offset, 9).Trim());
    offset += 9;
    
}

// Serialization methods
public string GetFiller6AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler7.PadRight(9));
    
    return result.ToString();
}

public void SetFiller6AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 9 <= data.Length)
    {
        string extracted = data.Substring(offset, 9).Trim();
        SetFiller7(extracted);
    }
    offset += 9;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller7()
{
    return _Filler7;
}

// Standard Setter
public void SetFiller7(string value)
{
    _Filler7 = value;
}

// Get<>AsString()
public string GetFiller7AsString()
{
    return _Filler7.PadRight(9);
}

// Set<>AsString()
public void SetFiller7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler7 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetCfCgtPeriodStartDateR(string value)
{
    _CfCgtPeriodStartDateR.SetCfCgtPeriodStartDateRAsString(value);
}
// Nested Class: CfCgtPeriodStartDateR
public class CfCgtPeriodStartDateR
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CfStartDateYy, is_external=, is_static_class=False, static_prefix=
    private int _CfStartDateYy =0;
    
    
    
    
    // [DEBUG] Field: CfStartDateMm, is_external=, is_static_class=False, static_prefix=
    private int _CfStartDateMm =0;
    
    
    
    
    // [DEBUG] Field: CfStartDateDd, is_external=, is_static_class=False, static_prefix=
    private int _CfStartDateDd =0;
    
    
    
    
public CfCgtPeriodStartDateR() {}

public CfCgtPeriodStartDateR(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCfStartDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCfStartDateMm(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCfStartDateDd(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetCfCgtPeriodStartDateRAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CfStartDateYy.ToString().PadLeft(2, '0'));
    result.Append(_CfStartDateMm.ToString().PadLeft(2, '0'));
    result.Append(_CfStartDateDd.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetCfCgtPeriodStartDateRAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCfStartDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCfStartDateMm(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCfStartDateDd(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCfStartDateYy()
{
    return _CfStartDateYy;
}

// Standard Setter
public void SetCfStartDateYy(int value)
{
    _CfStartDateYy = value;
}

// Get<>AsString()
public string GetCfStartDateYyAsString()
{
    return _CfStartDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCfStartDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _CfStartDateYy = parsed;
}

// Standard Getter
public int GetCfStartDateMm()
{
    return _CfStartDateMm;
}

// Standard Setter
public void SetCfStartDateMm(int value)
{
    _CfStartDateMm = value;
}

// Get<>AsString()
public string GetCfStartDateMmAsString()
{
    return _CfStartDateMm.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCfStartDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _CfStartDateMm = parsed;
}

// Standard Getter
public int GetCfStartDateDd()
{
    return _CfStartDateDd;
}

// Standard Setter
public void SetCfStartDateDd(int value)
{
    _CfStartDateDd = value;
}

// Get<>AsString()
public string GetCfStartDateDdAsString()
{
    return _CfStartDateDd.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCfStartDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _CfStartDateDd = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetCfCgtPeriodEndDateRed(string value)
{
    _CfCgtPeriodEndDateRed.SetCfCgtPeriodEndDateRedAsString(value);
}
// Nested Class: CfCgtPeriodEndDateRed
public class CfCgtPeriodEndDateRed
{
    private static int _size = 2;
    
    // Fields in the class
    
    
    // [DEBUG] Field: CfCgtPeriodEndDateYy, is_external=, is_static_class=False, static_prefix=
    private int _CfCgtPeriodEndDateYy =0;
    
    
    
    
    // [DEBUG] Field: Filler8, is_external=, is_static_class=False, static_prefix=
    private string _Filler8 ="";
    
    
    
    
public CfCgtPeriodEndDateRed() {}

public CfCgtPeriodEndDateRed(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCfCgtPeriodEndDateYy(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetFiller8(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetCfCgtPeriodEndDateRedAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_CfCgtPeriodEndDateYy.ToString().PadLeft(2, '0'));
    result.Append(_Filler8.PadRight(0));
    
    return result.ToString();
}

public void SetCfCgtPeriodEndDateRedAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCfCgtPeriodEndDateYy(parsedInt);
    }
    offset += 2;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller8(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public int GetCfCgtPeriodEndDateYy()
{
    return _CfCgtPeriodEndDateYy;
}

// Standard Setter
public void SetCfCgtPeriodEndDateYy(int value)
{
    _CfCgtPeriodEndDateYy = value;
}

// Get<>AsString()
public string GetCfCgtPeriodEndDateYyAsString()
{
    return _CfCgtPeriodEndDateYy.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCfCgtPeriodEndDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _CfCgtPeriodEndDateYy = parsed;
}

// Standard Getter
public string GetFiller8()
{
    return _Filler8;
}

// Standard Setter
public void SetFiller8(string value)
{
    _Filler8 = value;
}

// Get<>AsString()
public string GetFiller8AsString()
{
    return _Filler8.PadRight(0);
}

// Set<>AsString()
public void SetFiller8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler8 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}