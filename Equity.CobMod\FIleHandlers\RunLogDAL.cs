﻿using System;
using WK.UK.CCH.Equity.Utilities;

namespace Legacy4.Equity.CobMod.FIleHandlers
{
    public class RunLogDAL
    {
        // Constants for log actions (assuming these values based on typical usage)
        // Verify these against your COBOL definitions (e.g., 78 levels in copybooks).
        private const string OPEN_OUTPUT = "OPN";
        private const string WRITE_RECORD = "WRT";
        private const string CLOSE_FILE = "CLS";
        private const string CLEAR_RUN_LOG = "CRL"; // Assuming this is the correct value for CLEAR-RUN-LOG
                                                    // CLEAR_RUN_LOG ("CRL") is defined in LogMessageStructure

        // Instance field to hold the RunLogManager reference.
        // See Assumption #5 regarding lifetime and potential need for static/thread-safe handling.
        private RunLogManager _runLogManager = null; // Corresponds to oRunLogManager

        /// <summary>
        /// Processes a log request using the injected RunLogManager.
        /// Equivalent to the PROCEDURE DIVISION of RunLogDAL.cbl.
        /// </summary>
        /// <param name="logMessage">Input parameters corresponding to LINKAGE-AREA-1.</param>
        /// <param name="userNo">The user number, corresponding to G-USER-NO passed via W-USER-NO.</param>
        public void ProcessLogRequest(LogMessage logMessage) // USING LINKAGE-AREA-1 and G-USER-NO
        {
            // Corresponds to: move G-USER-NO to W-USER-NO
            // The userNo parameter directly represents W-USER-NO here.
            EquityGlobal.GGeneralParms gGeneralParms = EquityGlobal.GetGGeneralParms();
            try
            {
                // if oRunLogManager = null
                if (_runLogManager == null)
                {
                    // invoke RunLogManager::"GetRunLog"(W-USER-NO) returning oRunLogManager
                    _runLogManager = RunLogManager.GetRunLog(Int32.Parse(gGeneralParms.gUserNo));
                }

                // Ensure we have a logger instance (GetRunLog might return null or throw)
                if (_runLogManager == null)
                {
                    // Log an error - cannot proceed without a logger instance
                    Console.Error.WriteLine($"Error: Failed to obtain RunLogManager for user {gGeneralParms.gUserNo}.");
                    // Depending on requirements, you might throw an exception here.
                    return; // Exit processing if logger is unavailable
                }


                // evaluate log-action
                switch (logMessage.GetLogAction())
                {
                    case OPEN_OUTPUT: // when OPEN-OUTPUT
                                      // invoke oRunLogManager::"AddStartLine"(LOG-CODE)
                        _runLogManager.AddStartLine(logMessage.GetHashCode().ToString());
                        break;

                    case WRITE_RECORD: // when WRITE-RECORD
                                       // invoke oRunLogManager::"AddLine"(LOG-STRING)
                        _runLogManager.AddLine(logMessage.GetLogString());
                        break;

                    case CLOSE_FILE: // when CLOSE-FILE
                                     // invoke oRunLogManager::"AddEndLine"()
                        _runLogManager.AddEndLine();
                        break;

                    case CLEAR_RUN_LOG: // when CLEAR-RUN-LOG ("CRL")
                                        // invoke oRunLogManager::"Clear"()
                        _runLogManager.Clear();
                        break;

                    default: // when other
                             // STRING 'Invalid log action: ' LOG-ACTION DELIMITED BY SIZE INTO LOG-STRING
                             // Note: This modifies the input logMessage object's LogString property.
                        logMessage.SetLogString($"Invalid log action: {logMessage.GetLogAction()})");

                        // invoke oRunLogManager::"AddLine"(LOG-STRING)
                        _runLogManager.AddLine(logMessage.GetLogString());
                        break;
                }
            }
            catch (Exception ex)
            {
                // Basic error handling for any exceptions during RunLogManager interaction
                Console.Error.WriteLine($"Error during RunLogDAL processing for user {gGeneralParms.gUserNo}, Action={logMessage.GetLogAction()}: {ex.Message}");
                // Consider more specific error handling or logging
                // Depending on requirements, you might re-throw the exception: throw;
            }

            // goback (End of method acts as GOBACK)
        }

        /*public class RunLogDAL
        {
            private RunLogManager _oRunLogManager;
            private int _wUserNo;
            public RunLogDAL() { }
            public RunLogDAL(CommonLinkage commonLinkage, EquityGlobalParms equityGlobalParms, string logCode, string logAction, string logString)
            {
                _wUserNo = int.Parse(equityGlobalParms.GeneralParms.UserNo.Trim());

                if (_oRunLogManager == null)
                {
                    _oRunLogManager = RunLogManager.GetRunLog(_wUserNo);
                }

                ProcessLogAction(logAction, logCode, logString);
            }

            public void ProcessLogAction(string logAction, string logCode, string logString)
            {
                switch (logAction)
                {
                    case "OPEN-OUTPUT":
                        _oRunLogManager.AddStartLine(logCode);
                        break;
                    case "WRITE-RECORD":
                        _oRunLogManager.AddLine(logString);
                        break;
                    case "CLOSE-FILE":
                        _oRunLogManager.AddEndLine();
                        break;
                    case "CRL": // CLEAR-RUN-LOG
                        _oRunLogManager.Clear();
                        break;
                    default:
                        string invalidLogMessage = $"Invalid log action: {logAction}";
                        _oRunLogManager.AddLine(invalidLogMessage);
                        break;
                }
            }
        }*/
    }
}
