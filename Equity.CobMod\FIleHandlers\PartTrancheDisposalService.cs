﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using EquityProject.PartTrancheDisposalDTO;
using WK.UK.CCH.Equity.CobolDataTransformation;

namespace EquityProject.PartTrancheDisposalPGM
{
    /// <summary>
    /// Provides data access operations for Part Tranche Disposal,
    /// mimicking the COBOL PartTrancheDisposalDAL program.
    /// This class manages the lifecycle of the underlying C# DA object.
    /// </summary>
    public class PartTrancheDisposalService
    {
        // Corresponds to COBOL's oPartTrancheDisposalDA Object Reference PartTrancheDisposalDA.
        // This instance persists across calls to *this instance* of the service.
        private PartTrancheDisposalDA _partTrancheDisposalDA;

        // Corresponds to COBOL's sRecordArea and sMessage.
        // These are temporary variables, so we'll use local strings in the method.

        // Corresponds to COBOL's W-ReturnCode. Not used in the provided snippet, so omitted here.

        /// <summary>
        /// Initializes a new instance of the <see cref="PartTrancheDisposalService"/> class.
        /// The underlying DA object is null until an "Open" action is performed.
        /// </summary>
        public PartTrancheDisposalService()
        {
            _partTrancheDisposalDA = null; // Initial state, not yet instantiated
        }

        /// <summary>
        /// Executes a data access operation based on the provided parameters,
        /// mimicking the PROCEDURE DIVISION of the COBOL PartTrancheDisposalDAL program.
        /// </summary>
        /// <param name="parameters">
        /// An instance of <see cref="PartTrancheDisposalParameters"/> containing
        /// the action, record data, and receiving any messages.
        /// </param>
        public void ExecuteDataAccess(PartTrancheDisposalParameters parameters)
        {
            // COBOL: evaluate L-FILE-ACTION
            switch (parameters.InterpretedFileAction)
            {
                // COBOL: when OPEN-INPUT
                case FileAction.OpenInput:
                    // COBOL: Set oPartTrancheDisposalDA to PartTrancheDisposalDA::"New"
                    if (_partTrancheDisposalDA == null)
                    {
                        _partTrancheDisposalDA = new PartTrancheDisposalDA();
                        Console.WriteLine("Service: PartTrancheDisposalDA instance created (OpenInput).");
                    }
                    else
                    {
                        Console.WriteLine("Service: PartTrancheDisposalDA already open. Re-opening not explicitly handled by COBOL snippet.");
                        // Depending on the DA's behavior, you might want to close and re-open,
                        // or just assume it's ready. The mock assumes 'New' always creates a ready state.
                    }
                    break;

                // COBOL: when WRITE-RECORD
                case FileAction.WriteRecord:
                    // COBOL: move SPACES to L-Message
                    parameters.Message = new string(' ', 690); // Reset message buffer

                    // COBOL: set sRecordArea to L-FILE-RECORD-AREA
                    string sRecordArea = parameters.FileRecordArea; // C# strings are reference types, so this is a shallow copy.

                    // COBOL: INVOKE oPartTrancheDisposalDA "CreateNewPartTrancheDisposal" using sRecordArea returning sMessage
                    string sMessage = null;
                    if (_partTrancheDisposalDA != null)
                    {
                        sMessage = _partTrancheDisposalDA.CreateNewPartTrancheDisposal(sRecordArea);
                    }
                    else
                    {
                        sMessage = "Error: DAL not initialized. Call OpenInput first.";
                        Console.Error.WriteLine(sMessage);
                    }

                    // COBOL: if not string::"IsNullOrEmpty"(sMessage)
                    // COBOL: set L-Message to sMessage
                    if (!string.IsNullOrEmpty(sMessage))
                    {
                        // Ensure the message fits PIC X(690)
                        if (sMessage.Length > 690)
                        {
                            parameters.Message = sMessage.Substring(0, 690);
                        }
                        else
                        {
                            parameters.Message = sMessage.PadRight(690, ' ');
                        }
                        Console.WriteLine($"Service: Message returned from DA: '{sMessage.TrimEnd()}'");
                    }
                    else
                    {
                        Console.WriteLine("Service: CreateNewPartTrancheDisposal successful (no message returned).");
                    }
                    break;

                // COBOL: when CLOSE-FILE
                case FileAction.CloseFile:
                    // COBOL: INVOKE oPartTrancheDisposalDA::"Close" returning sRecordArea
                    // The COBOL returns sRecordArea, which is unusual for a close.
                    // The mock DA returns a string, which we'll just log.
                    if (_partTrancheDisposalDA != null)
                    {
                        string closeResult = _partTrancheDisposalDA.Close();
                        Console.WriteLine($"Service: PartTrancheDisposalDA closed. Result: {closeResult}");
                        _partTrancheDisposalDA = null; // Clear the reference after closing
                    }
                    else
                    {
                        Console.WriteLine("Service: PartTrancheDisposalDA was not open (CloseFile).");
                    }
                    break;

                // COBOL: when other
                case FileAction.Unknown:
                default:
                    string errorMessage = $"Error: Unknown or unsupported file action: '{parameters.RawFileAction}'.";
                    Console.Error.WriteLine(errorMessage);
                    // Populate L-Message with the error
                    if (errorMessage.Length > 690)
                    {
                        parameters.Message = errorMessage.Substring(0, 690);
                    }
                    else
                    {
                        parameters.Message = errorMessage.PadRight(690, ' ');
                    }
                    break;
            }

            // COBOL: goback. (Method simply returns)
        }
    }
}
