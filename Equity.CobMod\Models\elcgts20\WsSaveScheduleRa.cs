using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WsSaveScheduleRa Data Structure

public class WsSaveScheduleRa
{
    private static int _size = 16;
    // [DEBUG] Class: WsSaveScheduleRa, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsSaveScheduleDate, is_external=, is_static_class=False, static_prefix=
    private string _WsSaveScheduleDate =" ";
    
    
    
    
    // [DEBUG] Field: WsSaveScheduleDescription, is_external=, is_static_class=False, static_prefix=
    private string _WsSaveScheduleDescription =" ";
    
    
    
    
    
    // Serialization methods
    public string GetWsSaveScheduleRaAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsSaveScheduleDate.PadRight(0));
        result.Append(_WsSaveScheduleDescription.PadRight(16));
        
        return result.ToString();
    }
    
    public void SetWsSaveScheduleRaAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWsSaveScheduleDate(extracted);
        }
        offset += 0;
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetWsSaveScheduleDescription(extracted);
        }
        offset += 16;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsSaveScheduleRaAsString();
    }
    // Set<>String Override function
    public void SetWsSaveScheduleRa(string value)
    {
        SetWsSaveScheduleRaAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsSaveScheduleDate()
    {
        return _WsSaveScheduleDate;
    }
    
    // Standard Setter
    public void SetWsSaveScheduleDate(string value)
    {
        _WsSaveScheduleDate = value;
    }
    
    // Get<>AsString()
    public string GetWsSaveScheduleDateAsString()
    {
        return _WsSaveScheduleDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWsSaveScheduleDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsSaveScheduleDate = value;
    }
    
    // Standard Getter
    public string GetWsSaveScheduleDescription()
    {
        return _WsSaveScheduleDescription;
    }
    
    // Standard Setter
    public void SetWsSaveScheduleDescription(string value)
    {
        _WsSaveScheduleDescription = value;
    }
    
    // Get<>AsString()
    public string GetWsSaveScheduleDescriptionAsString()
    {
        return _WsSaveScheduleDescription.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetWsSaveScheduleDescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsSaveScheduleDescription = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}
