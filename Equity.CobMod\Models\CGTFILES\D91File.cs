using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D91File Data Structure

public class D91File
{
    private static int _size = 128;
    // [DEBUG] Class: D91File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler269, is_external=, is_static_class=False, static_prefix=
    private string _Filler269 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD91FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler269.PadRight(128));
        
        return result.ToString();
    }
    
    public void SetD91FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 128 <= data.Length)
        {
            string extracted = data.Substring(offset, 128).Trim();
            SetFiller269(extracted);
        }
        offset += 128;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD91FileAsString();
    }
    // Set<>String Override function
    public void SetD91File(string value)
    {
        SetD91FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller269()
    {
        return _Filler269;
    }
    
    // Standard Setter
    public void SetFiller269(string value)
    {
        _Filler269 = value;
    }
    
    // Get<>AsString()
    public string GetFiller269AsString()
    {
        return _Filler269.PadRight(128);
    }
    
    // Set<>AsString()
    public void SetFiller269AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler269 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}