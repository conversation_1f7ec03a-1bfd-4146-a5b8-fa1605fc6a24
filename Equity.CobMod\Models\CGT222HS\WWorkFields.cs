using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// DTO class representing WWorkFields Data Structure

public class WWorkFields
{
    private static int _size = 58;
    // [DEBUG] Class: WWorkFields, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WBalance, is_external=, is_static_class=False, static_prefix=
    private decimal _WBalance =0M;
    
    
    
    
    // [DEBUG] Field: WSub, is_external=, is_static_class=False, static_prefix=
    private int _WSub =0;
    
    
    
    
    // [DEBUG] Field: WLastCall, is_external=, is_static_class=False, static_prefix=
    private string _WLastCall ="";
    
    
    
    
    // [DEBUG] Field: WLastFundCode, is_external=, is_static_class=False, static_prefix=
    private string _WLastFundCode ="";
    
    
    
    
    // [DEBUG] Field: WLastSedolCode, is_external=, is_static_class=False, static_prefix=
    private string _WLastSedolCode ="";
    
    
    
    
    // [DEBUG] Field: WRecordNumber, is_external=, is_static_class=False, static_prefix=
    private int _WRecordNumber =0;
    
    
    
    
    
    // Serialization methods
    public string GetWWorkFieldsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WSub.ToString().PadLeft(18, '0'));
        result.Append(_WLastCall.PadRight(1));
        result.Append(_WLastFundCode.PadRight(4));
        result.Append(_WLastSedolCode.PadRight(7));
        result.Append(_WRecordNumber.ToString().PadLeft(10, '0'));
        
        return result.ToString();
    }
    
    public void SetWWorkFieldsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWBalance(parsedDec);
        }
        offset += 18;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWSub(parsedInt);
        }
        offset += 18;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWLastCall(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWLastFundCode(extracted);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetWLastSedolCode(extracted);
        }
        offset += 7;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWRecordNumber(parsedInt);
        }
        offset += 10;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWWorkFieldsAsString();
    }
    // Set<>String Override function
    public void SetWWorkFields(string value)
    {
        SetWWorkFieldsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public decimal GetWBalance()
    {
        return _WBalance;
    }
    
    // Standard Setter
    public void SetWBalance(decimal value)
    {
        _WBalance = value;
    }
    
    // Get<>AsString()
    public string GetWBalanceAsString()
    {
        return _WBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWBalanceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WBalance = parsed;
    }
    
    // Standard Getter
    public int GetWSub()
    {
        return _WSub;
    }
    
    // Standard Setter
    public void SetWSub(int value)
    {
        _WSub = value;
    }
    
    // Get<>AsString()
    public string GetWSubAsString()
    {
        return _WSub.ToString().PadLeft(18, '0');
    }
    
    // Set<>AsString()
    public void SetWSubAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WSub = parsed;
    }
    
    // Standard Getter
    public string GetWLastCall()
    {
        return _WLastCall;
    }
    
    // Standard Setter
    public void SetWLastCall(string value)
    {
        _WLastCall = value;
    }
    
    // Get<>AsString()
    public string GetWLastCallAsString()
    {
        return _WLastCall.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWLastCallAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WLastCall = value;
    }
    
    // Standard Getter
    public string GetWLastFundCode()
    {
        return _WLastFundCode;
    }
    
    // Standard Setter
    public void SetWLastFundCode(string value)
    {
        _WLastFundCode = value;
    }
    
    // Get<>AsString()
    public string GetWLastFundCodeAsString()
    {
        return _WLastFundCode.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWLastFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WLastFundCode = value;
    }
    
    // Standard Getter
    public string GetWLastSedolCode()
    {
        return _WLastSedolCode;
    }
    
    // Standard Setter
    public void SetWLastSedolCode(string value)
    {
        _WLastSedolCode = value;
    }
    
    // Get<>AsString()
    public string GetWLastSedolCodeAsString()
    {
        return _WLastSedolCode.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetWLastSedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WLastSedolCode = value;
    }
    
    // Standard Getter
    public int GetWRecordNumber()
    {
        return _WRecordNumber;
    }
    
    // Standard Setter
    public void SetWRecordNumber(int value)
    {
        _WRecordNumber = value;
    }
    
    // Get<>AsString()
    public string GetWRecordNumberAsString()
    {
        return _WRecordNumber.ToString().PadLeft(10, '0');
    }
    
    // Set<>AsString()
    public void SetWRecordNumberAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WRecordNumber = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}