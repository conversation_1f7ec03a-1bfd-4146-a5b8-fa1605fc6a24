using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WsIrishCgtFields Data Structure

public class WsIrishCgtFields
{
    private static int _size = 52;
    // [DEBUG] Class: WsIrishCgtFields, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WsNdlGroupsFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsNdlGroupsFlag =" ";
    
    
    // 88-level condition checks for WsNdlGroupsFlag
    public bool IsNdlGroupsFound()
    {
        if (this._WsNdlGroupsFlag == "'Y'") return true;
        return false;
    }
    public bool IsNoNdlGroups()
    {
        if (this._WsNdlGroupsFlag == "'N'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsIrishTrialCompFlag, is_external=, is_static_class=False, static_prefix=
    private string _WsIrishTrialCompFlag =" ";
    
    
    // 88-level condition checks for WsIrishTrialCompFlag
    public bool IsIrishTrialComp()
    {
        if (this._WsIrishTrialCompFlag == "'Y'") return true;
        return false;
    }
    public bool IsNotIrishTrialComp()
    {
        if (this._WsIrishTrialCompFlag == "'N'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: WsNdlUnitsToAllocate, is_external=, is_static_class=False, static_prefix=
    private decimal _WsNdlUnitsToAllocate =0;
    
    
    
    
    // [DEBUG] Field: WsNdlLossToAllocate, is_external=, is_static_class=False, static_prefix=
    private decimal _WsNdlLossToAllocate =0;
    
    
    
    
    // [DEBUG] Field: WsPurchaseMinus28DaysDate, is_external=, is_static_class=False, static_prefix=
    private string _WsPurchaseMinus28DaysDate ="";
    
    
    
    
    // [DEBUG] Field: WiSpIndex, is_external=, is_static_class=False, static_prefix=
    private string _WiSpIndex ="0";
    
    
    
    
    // [DEBUG] Field: WiSsIndex, is_external=, is_static_class=False, static_prefix=
    private string _WiSsIndex ="0";
    
    
    
    
    // [DEBUG] Field: WiStoreWiTc, is_external=, is_static_class=False, static_prefix=
    private string _WiStoreWiTc ="0";
    
    
    
    
    // [DEBUG] Field: WiStoreWiWttc, is_external=, is_static_class=False, static_prefix=
    private string _WiStoreWiWttc ="0";
    
    
    
    
    // [DEBUG] Field: WiStoreWiWttcCPrev, is_external=, is_static_class=False, static_prefix=
    private string _WiStoreWiWttcCPrev ="0";
    
    
    
    
    
    // Serialization methods
    public string GetWsIrishCgtFieldsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WsNdlGroupsFlag.PadRight(1));
        result.Append(_WsIrishTrialCompFlag.PadRight(1));
        result.Append(_WsNdlUnitsToAllocate.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsNdlLossToAllocate.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_WsPurchaseMinus28DaysDate.PadRight(0));
        result.Append(_WiSpIndex.PadRight(4));
        result.Append(_WiSsIndex.PadRight(4));
        result.Append(_WiStoreWiTc.PadRight(4));
        result.Append(_WiStoreWiWttc.PadRight(4));
        result.Append(_WiStoreWiWttcCPrev.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetWsIrishCgtFieldsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsNdlGroupsFlag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWsIrishTrialCompFlag(extracted);
        }
        offset += 1;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsNdlUnitsToAllocate(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetWsNdlLossToAllocate(parsedDec);
        }
        offset += 15;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetWsPurchaseMinus28DaysDate(extracted);
        }
        offset += 0;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWiSpIndex(extracted);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWiSsIndex(extracted);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWiStoreWiTc(extracted);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWiStoreWiWttc(extracted);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetWiStoreWiWttcCPrev(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWsIrishCgtFieldsAsString();
    }
    // Set<>String Override function
    public void SetWsIrishCgtFields(string value)
    {
        SetWsIrishCgtFieldsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWsNdlGroupsFlag()
    {
        return _WsNdlGroupsFlag;
    }
    
    // Standard Setter
    public void SetWsNdlGroupsFlag(string value)
    {
        _WsNdlGroupsFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsNdlGroupsFlagAsString()
    {
        return _WsNdlGroupsFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsNdlGroupsFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsNdlGroupsFlag = value;
    }
    
    // Standard Getter
    public string GetWsIrishTrialCompFlag()
    {
        return _WsIrishTrialCompFlag;
    }
    
    // Standard Setter
    public void SetWsIrishTrialCompFlag(string value)
    {
        _WsIrishTrialCompFlag = value;
    }
    
    // Get<>AsString()
    public string GetWsIrishTrialCompFlagAsString()
    {
        return _WsIrishTrialCompFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWsIrishTrialCompFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsIrishTrialCompFlag = value;
    }
    
    // Standard Getter
    public decimal GetWsNdlUnitsToAllocate()
    {
        return _WsNdlUnitsToAllocate;
    }
    
    // Standard Setter
    public void SetWsNdlUnitsToAllocate(decimal value)
    {
        _WsNdlUnitsToAllocate = value;
    }
    
    // Get<>AsString()
    public string GetWsNdlUnitsToAllocateAsString()
    {
        return _WsNdlUnitsToAllocate.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsNdlUnitsToAllocateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsNdlUnitsToAllocate = parsed;
    }
    
    // Standard Getter
    public decimal GetWsNdlLossToAllocate()
    {
        return _WsNdlLossToAllocate;
    }
    
    // Standard Setter
    public void SetWsNdlLossToAllocate(decimal value)
    {
        _WsNdlLossToAllocate = value;
    }
    
    // Get<>AsString()
    public string GetWsNdlLossToAllocateAsString()
    {
        return _WsNdlLossToAllocate.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetWsNdlLossToAllocateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _WsNdlLossToAllocate = parsed;
    }
    
    // Standard Getter
    public string GetWsPurchaseMinus28DaysDate()
    {
        return _WsPurchaseMinus28DaysDate;
    }
    
    // Standard Setter
    public void SetWsPurchaseMinus28DaysDate(string value)
    {
        _WsPurchaseMinus28DaysDate = value;
    }
    
    // Get<>AsString()
    public string GetWsPurchaseMinus28DaysDateAsString()
    {
        return _WsPurchaseMinus28DaysDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetWsPurchaseMinus28DaysDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WsPurchaseMinus28DaysDate = value;
    }
    
    // Standard Getter
    public string GetWiSpIndex()
    {
        return _WiSpIndex;
    }
    
    // Standard Setter
    public void SetWiSpIndex(string value)
    {
        _WiSpIndex = value;
    }
    
    // Get<>AsString()
    public string GetWiSpIndexAsString()
    {
        return _WiSpIndex.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWiSpIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WiSpIndex = value;
    }
    
    // Standard Getter
    public string GetWiSsIndex()
    {
        return _WiSsIndex;
    }
    
    // Standard Setter
    public void SetWiSsIndex(string value)
    {
        _WiSsIndex = value;
    }
    
    // Get<>AsString()
    public string GetWiSsIndexAsString()
    {
        return _WiSsIndex.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWiSsIndexAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WiSsIndex = value;
    }
    
    // Standard Getter
    public string GetWiStoreWiTc()
    {
        return _WiStoreWiTc;
    }
    
    // Standard Setter
    public void SetWiStoreWiTc(string value)
    {
        _WiStoreWiTc = value;
    }
    
    // Get<>AsString()
    public string GetWiStoreWiTcAsString()
    {
        return _WiStoreWiTc.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWiStoreWiTcAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WiStoreWiTc = value;
    }
    
    // Standard Getter
    public string GetWiStoreWiWttc()
    {
        return _WiStoreWiWttc;
    }
    
    // Standard Setter
    public void SetWiStoreWiWttc(string value)
    {
        _WiStoreWiWttc = value;
    }
    
    // Get<>AsString()
    public string GetWiStoreWiWttcAsString()
    {
        return _WiStoreWiWttc.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWiStoreWiWttcAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WiStoreWiWttc = value;
    }
    
    // Standard Getter
    public string GetWiStoreWiWttcCPrev()
    {
        return _WiStoreWiWttcCPrev;
    }
    
    // Standard Setter
    public void SetWiStoreWiWttcCPrev(string value)
    {
        _WiStoreWiWttcCPrev = value;
    }
    
    // Get<>AsString()
    public string GetWiStoreWiWttcCPrevAsString()
    {
        return _WiStoreWiWttcCPrev.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetWiStoreWiWttcCPrevAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WiStoreWiWttcCPrev = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}