using System;
using System.IO;
using Equity.CobMod.Models;
using Legacy4.Equity.CobMod.Models;
using WK.UK.CCH.Equity.CobolDataTransformation;
namespace Legacy4.Equity.CobMod.Helper
{
    public class TaperRateDAL
    {
        private string _fileAction;
        private string _recordArea;
        private TaperRateDA _taperRateDA;

        public TaperRateDAL()
        {
            _taperRateDA = new TaperRateDA();
            _recordArea = new string(' ', 1870); // Initialize with spaces
        }

        public void ProcessFileAction(string fileAction, ref string recordArea)
        {
            _fileAction = fileAction;
            
            // Simulate timing start
            Console.WriteLine("Timing section data trans: taper rate");

            switch (_fileAction)
            {
                case  CommonLinkage.OPEN_IO:
                    _taperRateDA = new TaperRateDA();
                    break;
                
                case CommonLinkage.READ_NEXT:
                    _recordArea = _taperRateDA.ReadNext();
                    break;
                
                case CommonLinkage.CLOSE_FILE:
                    // No specific action required
                    break;
            }
            
            // Set the output record area
            recordArea = _recordArea;

            // Simulate timing end
            Console.WriteLine("Timing end section");
        }
    }
}
