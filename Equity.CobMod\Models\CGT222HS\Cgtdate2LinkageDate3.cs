using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// DTO class representing Cgtdate2LinkageDate3 Data Structure

public class Cgtdate2LinkageDate3
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate3, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd3, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd3 =0;
    
    
    
    
    // [DEBUG] Field: Filler48, is_external=, is_static_class=False, static_prefix=
    private Filler48 _Filler48 = new Filler48();
    
    
    
    
    // [DEBUG] Field: Filler49, is_external=, is_static_class=False, static_prefix=
    private Filler49 _Filler49 = new Filler49();
    
    
    
    
    // [DEBUG] Field: Filler50, is_external=, is_static_class=False, static_prefix=
    private Filler50 _Filler50 = new Filler50();
    
    
    
    
    // [DEBUG] Field: Filler51, is_external=, is_static_class=False, static_prefix=
    private Filler51 _Filler51 = new Filler51();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate3AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd3.ToString().PadLeft(8, '0'));
        result.Append(_Filler48.GetFiller48AsString());
        result.Append(_Filler49.GetFiller49AsString());
        result.Append(_Filler50.GetFiller50AsString());
        result.Append(_Filler51.GetFiller51AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate3AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd3(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler48.SetFiller48AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler48.SetFiller48AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler49.SetFiller49AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler49.SetFiller49AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler50.SetFiller50AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler50.SetFiller50AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler51.SetFiller51AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler51.SetFiller51AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate3AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate3(string value)
    {
        SetCgtdate2LinkageDate3AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd3()
    {
        return _Cgtdate2Ccyymmdd3;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd3(int value)
    {
        _Cgtdate2Ccyymmdd3 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd3AsString()
    {
        return _Cgtdate2Ccyymmdd3.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd3 = parsed;
    }
    
    // Standard Getter
    public Filler48 GetFiller48()
    {
        return _Filler48;
    }
    
    // Standard Setter
    public void SetFiller48(Filler48 value)
    {
        _Filler48 = value;
    }
    
    // Get<>AsString()
    public string GetFiller48AsString()
    {
        return _Filler48 != null ? _Filler48.GetFiller48AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller48AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler48 == null)
        {
            _Filler48 = new Filler48();
        }
        _Filler48.SetFiller48AsString(value);
    }
    
    // Standard Getter
    public Filler49 GetFiller49()
    {
        return _Filler49;
    }
    
    // Standard Setter
    public void SetFiller49(Filler49 value)
    {
        _Filler49 = value;
    }
    
    // Get<>AsString()
    public string GetFiller49AsString()
    {
        return _Filler49 != null ? _Filler49.GetFiller49AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller49AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler49 == null)
        {
            _Filler49 = new Filler49();
        }
        _Filler49.SetFiller49AsString(value);
    }
    
    // Standard Getter
    public Filler50 GetFiller50()
    {
        return _Filler50;
    }
    
    // Standard Setter
    public void SetFiller50(Filler50 value)
    {
        _Filler50 = value;
    }
    
    // Get<>AsString()
    public string GetFiller50AsString()
    {
        return _Filler50 != null ? _Filler50.GetFiller50AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller50AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler50 == null)
        {
            _Filler50 = new Filler50();
        }
        _Filler50.SetFiller50AsString(value);
    }
    
    // Standard Getter
    public Filler51 GetFiller51()
    {
        return _Filler51;
    }
    
    // Standard Setter
    public void SetFiller51(Filler51 value)
    {
        _Filler51 = value;
    }
    
    // Get<>AsString()
    public string GetFiller51AsString()
    {
        return _Filler51 != null ? _Filler51.GetFiller51AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller51AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler51 == null)
        {
            _Filler51 = new Filler51();
        }
        _Filler51.SetFiller51AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller48(string value)
    {
        _Filler48.SetFiller48AsString(value);
    }
    // Nested Class: Filler48
    public class Filler48
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc3, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc3 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd3, is_external=, is_static_class=False, static_prefix=
        private Filler48.Cgtdate2Yymmdd3 _Cgtdate2Yymmdd3 = new Filler48.Cgtdate2Yymmdd3();
        
        
        
        
    public Filler48() {}
    
    public Filler48(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc3(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd3.SetCgtdate2Yymmdd3AsString(data.Substring(offset, Cgtdate2Yymmdd3.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller48AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc3.PadRight(2));
        result.Append(_Cgtdate2Yymmdd3.GetCgtdate2Yymmdd3AsString());
        
        return result.ToString();
    }
    
    public void SetFiller48AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc3(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd3.SetCgtdate2Yymmdd3AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd3.SetCgtdate2Yymmdd3AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc3()
    {
        return _Cgtdate2Cc3;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc3(string value)
    {
        _Cgtdate2Cc3 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc3AsString()
    {
        return _Cgtdate2Cc3.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc3 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd3 GetCgtdate2Yymmdd3()
    {
        return _Cgtdate2Yymmdd3;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd3(Cgtdate2Yymmdd3 value)
    {
        _Cgtdate2Yymmdd3 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd3AsString()
    {
        return _Cgtdate2Yymmdd3 != null ? _Cgtdate2Yymmdd3.GetCgtdate2Yymmdd3AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd3 == null)
        {
            _Cgtdate2Yymmdd3 = new Cgtdate2Yymmdd3();
        }
        _Cgtdate2Yymmdd3.SetCgtdate2Yymmdd3AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd3
    public class Cgtdate2Yymmdd3
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy3, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy3 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd3, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd3.Cgtdate2Mmdd3 _Cgtdate2Mmdd3 = new Cgtdate2Yymmdd3.Cgtdate2Mmdd3();
        
        
        
        
    public Cgtdate2Yymmdd3() {}
    
    public Cgtdate2Yymmdd3(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy3(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd3.SetCgtdate2Mmdd3AsString(data.Substring(offset, Cgtdate2Mmdd3.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd3AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy3.PadRight(2));
        result.Append(_Cgtdate2Mmdd3.GetCgtdate2Mmdd3AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd3AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy3(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd3.SetCgtdate2Mmdd3AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd3.SetCgtdate2Mmdd3AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy3()
    {
        return _Cgtdate2Yy3;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy3(string value)
    {
        _Cgtdate2Yy3 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy3AsString()
    {
        return _Cgtdate2Yy3.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy3 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd3 GetCgtdate2Mmdd3()
    {
        return _Cgtdate2Mmdd3;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd3(Cgtdate2Mmdd3 value)
    {
        _Cgtdate2Mmdd3 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd3AsString()
    {
        return _Cgtdate2Mmdd3 != null ? _Cgtdate2Mmdd3.GetCgtdate2Mmdd3AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd3 == null)
        {
            _Cgtdate2Mmdd3 = new Cgtdate2Mmdd3();
        }
        _Cgtdate2Mmdd3.SetCgtdate2Mmdd3AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd3
    public class Cgtdate2Mmdd3
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm3, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm3 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd3, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd3 ="";
        
        
        
        
    public Cgtdate2Mmdd3() {}
    
    public Cgtdate2Mmdd3(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm3(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd3(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd3AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm3.PadRight(2));
        result.Append(_Cgtdate2Dd3.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd3AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm3(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd3(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm3()
    {
        return _Cgtdate2Mm3;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm3(string value)
    {
        _Cgtdate2Mm3 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm3AsString()
    {
        return _Cgtdate2Mm3.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm3 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd3()
    {
        return _Cgtdate2Dd3;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd3(string value)
    {
        _Cgtdate2Dd3 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd3AsString()
    {
        return _Cgtdate2Dd3.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd3 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller49(string value)
{
    _Filler49.SetFiller49AsString(value);
}
// Nested Class: Filler49
public class Filler49
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy3, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy3 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd3, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd3 =0;
    
    
    
    
public Filler49() {}

public Filler49(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy3(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd3(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller49AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy3.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd3.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller49AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy3(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd3(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy3()
{
    return _Cgtdate2CCcyy3;
}

// Standard Setter
public void SetCgtdate2CCcyy3(int value)
{
    _Cgtdate2CCcyy3 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy3AsString()
{
    return _Cgtdate2CCcyy3.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy3 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd3()
{
    return _Cgtdate2CMmdd3;
}

// Standard Setter
public void SetCgtdate2CMmdd3(int value)
{
    _Cgtdate2CMmdd3 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd3AsString()
{
    return _Cgtdate2CMmdd3.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd3 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller50(string value)
{
    _Filler50.SetFiller50AsString(value);
}
// Nested Class: Filler50
public class Filler50
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc3, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc3 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy3, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy3 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm3, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm3 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd3, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd3 =0;
    
    
    
    
public Filler50() {}

public Filler50(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc3(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy3(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm3(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd3(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller50AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc3.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy3.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm3.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd3.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller50AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc3(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy3(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm3(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd3(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc3()
{
    return _Cgtdate2CCc3;
}

// Standard Setter
public void SetCgtdate2CCc3(int value)
{
    _Cgtdate2CCc3 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc3AsString()
{
    return _Cgtdate2CCc3.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc3 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy3()
{
    return _Cgtdate2CYy3;
}

// Standard Setter
public void SetCgtdate2CYy3(int value)
{
    _Cgtdate2CYy3 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy3AsString()
{
    return _Cgtdate2CYy3.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy3 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm3()
{
    return _Cgtdate2CMm3;
}

// Standard Setter
public void SetCgtdate2CMm3(int value)
{
    _Cgtdate2CMm3 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm3AsString()
{
    return _Cgtdate2CMm3.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm3 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd3()
{
    return _Cgtdate2CDd3;
}

// Standard Setter
public void SetCgtdate2CDd3(int value)
{
    _Cgtdate2CDd3 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd3AsString()
{
    return _Cgtdate2CDd3.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd3 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller51(string value)
{
    _Filler51.SetFiller51AsString(value);
}
// Nested Class: Filler51
public class Filler51
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm3, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm3 =0;
    
    
    
    
    // [DEBUG] Field: Filler52, is_external=, is_static_class=False, static_prefix=
    private string _Filler52 ="";
    
    
    
    
public Filler51() {}

public Filler51(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm3(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller52(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller51AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm3.ToString().PadLeft(6, '0'));
    result.Append(_Filler52.PadRight(2));
    
    return result.ToString();
}

public void SetFiller51AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm3(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller52(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm3()
{
    return _Cgtdate2CCcyymm3;
}

// Standard Setter
public void SetCgtdate2CCcyymm3(int value)
{
    _Cgtdate2CCcyymm3 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm3AsString()
{
    return _Cgtdate2CCcyymm3.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm3AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm3 = parsed;
}

// Standard Getter
public string GetFiller52()
{
    return _Filler52;
}

// Standard Setter
public void SetFiller52(string value)
{
    _Filler52 = value;
}

// Get<>AsString()
public string GetFiller52AsString()
{
    return _Filler52.PadRight(2);
}

// Set<>AsString()
public void SetFiller52AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler52 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}