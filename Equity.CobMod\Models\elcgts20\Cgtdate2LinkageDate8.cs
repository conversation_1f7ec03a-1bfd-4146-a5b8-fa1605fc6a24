using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing Cgtdate2LinkageDate8 Data Structure

public class Cgtdate2LinkageDate8
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate8, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd8, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd8 =0;
    
    
    
    
    // [DEBUG] Field: Filler45, is_external=, is_static_class=False, static_prefix=
    private Filler45 _Filler45 = new Filler45();
    
    
    
    
    // [DEBUG] Field: Filler46, is_external=, is_static_class=False, static_prefix=
    private Filler46 _Filler46 = new Filler46();
    
    
    
    
    // [DEBUG] Field: Filler47, is_external=, is_static_class=False, static_prefix=
    private Filler47 _Filler47 = new Filler47();
    
    
    
    
    // [DEBUG] Field: Filler48, is_external=, is_static_class=False, static_prefix=
    private Filler48 _Filler48 = new Filler48();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate8AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd8.ToString().PadLeft(8, '0'));
        result.Append(_Filler45.GetFiller45AsString());
        result.Append(_Filler46.GetFiller46AsString());
        result.Append(_Filler47.GetFiller47AsString());
        result.Append(_Filler48.GetFiller48AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate8AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd8(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler45.SetFiller45AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler45.SetFiller45AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler46.SetFiller46AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler46.SetFiller46AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler47.SetFiller47AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler47.SetFiller47AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler48.SetFiller48AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler48.SetFiller48AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate8AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate8(string value)
    {
        SetCgtdate2LinkageDate8AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd8()
    {
        return _Cgtdate2Ccyymmdd8;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd8(int value)
    {
        _Cgtdate2Ccyymmdd8 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd8AsString()
    {
        return _Cgtdate2Ccyymmdd8.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd8 = parsed;
    }
    
    // Standard Getter
    public Filler45 GetFiller45()
    {
        return _Filler45;
    }
    
    // Standard Setter
    public void SetFiller45(Filler45 value)
    {
        _Filler45 = value;
    }
    
    // Get<>AsString()
    public string GetFiller45AsString()
    {
        return _Filler45 != null ? _Filler45.GetFiller45AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller45AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler45 == null)
        {
            _Filler45 = new Filler45();
        }
        _Filler45.SetFiller45AsString(value);
    }
    
    // Standard Getter
    public Filler46 GetFiller46()
    {
        return _Filler46;
    }
    
    // Standard Setter
    public void SetFiller46(Filler46 value)
    {
        _Filler46 = value;
    }
    
    // Get<>AsString()
    public string GetFiller46AsString()
    {
        return _Filler46 != null ? _Filler46.GetFiller46AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller46AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler46 == null)
        {
            _Filler46 = new Filler46();
        }
        _Filler46.SetFiller46AsString(value);
    }
    
    // Standard Getter
    public Filler47 GetFiller47()
    {
        return _Filler47;
    }
    
    // Standard Setter
    public void SetFiller47(Filler47 value)
    {
        _Filler47 = value;
    }
    
    // Get<>AsString()
    public string GetFiller47AsString()
    {
        return _Filler47 != null ? _Filler47.GetFiller47AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller47AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler47 == null)
        {
            _Filler47 = new Filler47();
        }
        _Filler47.SetFiller47AsString(value);
    }
    
    // Standard Getter
    public Filler48 GetFiller48()
    {
        return _Filler48;
    }
    
    // Standard Setter
    public void SetFiller48(Filler48 value)
    {
        _Filler48 = value;
    }
    
    // Get<>AsString()
    public string GetFiller48AsString()
    {
        return _Filler48 != null ? _Filler48.GetFiller48AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller48AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler48 == null)
        {
            _Filler48 = new Filler48();
        }
        _Filler48.SetFiller48AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller45(string value)
    {
        _Filler45.SetFiller45AsString(value);
    }
    // Nested Class: Filler45
    public class Filler45
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc8, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc8 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd8, is_external=, is_static_class=False, static_prefix=
        private Filler45.Cgtdate2Yymmdd8 _Cgtdate2Yymmdd8 = new Filler45.Cgtdate2Yymmdd8();
        
        
        
        
    public Filler45() {}
    
    public Filler45(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc8(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd8.SetCgtdate2Yymmdd8AsString(data.Substring(offset, Cgtdate2Yymmdd8.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller45AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc8.PadRight(2));
        result.Append(_Cgtdate2Yymmdd8.GetCgtdate2Yymmdd8AsString());
        
        return result.ToString();
    }
    
    public void SetFiller45AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc8(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd8.SetCgtdate2Yymmdd8AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd8.SetCgtdate2Yymmdd8AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc8()
    {
        return _Cgtdate2Cc8;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc8(string value)
    {
        _Cgtdate2Cc8 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc8AsString()
    {
        return _Cgtdate2Cc8.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc8 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd8 GetCgtdate2Yymmdd8()
    {
        return _Cgtdate2Yymmdd8;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd8(Cgtdate2Yymmdd8 value)
    {
        _Cgtdate2Yymmdd8 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd8AsString()
    {
        return _Cgtdate2Yymmdd8 != null ? _Cgtdate2Yymmdd8.GetCgtdate2Yymmdd8AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd8 == null)
        {
            _Cgtdate2Yymmdd8 = new Cgtdate2Yymmdd8();
        }
        _Cgtdate2Yymmdd8.SetCgtdate2Yymmdd8AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd8
    public class Cgtdate2Yymmdd8
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy8, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy8 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd8, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd8.Cgtdate2Mmdd8 _Cgtdate2Mmdd8 = new Cgtdate2Yymmdd8.Cgtdate2Mmdd8();
        
        
        
        
    public Cgtdate2Yymmdd8() {}
    
    public Cgtdate2Yymmdd8(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy8(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd8.SetCgtdate2Mmdd8AsString(data.Substring(offset, Cgtdate2Mmdd8.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd8AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy8.PadRight(2));
        result.Append(_Cgtdate2Mmdd8.GetCgtdate2Mmdd8AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd8AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy8(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd8.SetCgtdate2Mmdd8AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd8.SetCgtdate2Mmdd8AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy8()
    {
        return _Cgtdate2Yy8;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy8(string value)
    {
        _Cgtdate2Yy8 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy8AsString()
    {
        return _Cgtdate2Yy8.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy8 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd8 GetCgtdate2Mmdd8()
    {
        return _Cgtdate2Mmdd8;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd8(Cgtdate2Mmdd8 value)
    {
        _Cgtdate2Mmdd8 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd8AsString()
    {
        return _Cgtdate2Mmdd8 != null ? _Cgtdate2Mmdd8.GetCgtdate2Mmdd8AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd8 == null)
        {
            _Cgtdate2Mmdd8 = new Cgtdate2Mmdd8();
        }
        _Cgtdate2Mmdd8.SetCgtdate2Mmdd8AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd8
    public class Cgtdate2Mmdd8
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm8, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm8 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd8, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd8 ="";
        
        
        
        
    public Cgtdate2Mmdd8() {}
    
    public Cgtdate2Mmdd8(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm8(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd8(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd8AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm8.PadRight(2));
        result.Append(_Cgtdate2Dd8.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd8AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm8(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd8(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm8()
    {
        return _Cgtdate2Mm8;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm8(string value)
    {
        _Cgtdate2Mm8 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm8AsString()
    {
        return _Cgtdate2Mm8.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm8 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd8()
    {
        return _Cgtdate2Dd8;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd8(string value)
    {
        _Cgtdate2Dd8 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd8AsString()
    {
        return _Cgtdate2Dd8.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd8AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd8 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller46(string value)
{
    _Filler46.SetFiller46AsString(value);
}
// Nested Class: Filler46
public class Filler46
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy8, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy8 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd8, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd8 =0;
    
    
    
    
public Filler46() {}

public Filler46(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy8(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd8(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller46AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy8.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd8.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller46AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy8(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd8(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy8()
{
    return _Cgtdate2CCcyy8;
}

// Standard Setter
public void SetCgtdate2CCcyy8(int value)
{
    _Cgtdate2CCcyy8 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy8AsString()
{
    return _Cgtdate2CCcyy8.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy8 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd8()
{
    return _Cgtdate2CMmdd8;
}

// Standard Setter
public void SetCgtdate2CMmdd8(int value)
{
    _Cgtdate2CMmdd8 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd8AsString()
{
    return _Cgtdate2CMmdd8.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd8 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller47(string value)
{
    _Filler47.SetFiller47AsString(value);
}
// Nested Class: Filler47
public class Filler47
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc8, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc8 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy8, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy8 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm8, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm8 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd8, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd8 =0;
    
    
    
    
public Filler47() {}

public Filler47(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc8(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy8(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm8(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd8(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller47AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc8.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy8.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm8.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd8.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller47AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc8(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy8(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm8(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd8(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc8()
{
    return _Cgtdate2CCc8;
}

// Standard Setter
public void SetCgtdate2CCc8(int value)
{
    _Cgtdate2CCc8 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc8AsString()
{
    return _Cgtdate2CCc8.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc8 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy8()
{
    return _Cgtdate2CYy8;
}

// Standard Setter
public void SetCgtdate2CYy8(int value)
{
    _Cgtdate2CYy8 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy8AsString()
{
    return _Cgtdate2CYy8.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy8 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm8()
{
    return _Cgtdate2CMm8;
}

// Standard Setter
public void SetCgtdate2CMm8(int value)
{
    _Cgtdate2CMm8 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm8AsString()
{
    return _Cgtdate2CMm8.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm8 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd8()
{
    return _Cgtdate2CDd8;
}

// Standard Setter
public void SetCgtdate2CDd8(int value)
{
    _Cgtdate2CDd8 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd8AsString()
{
    return _Cgtdate2CDd8.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd8 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller48(string value)
{
    _Filler48.SetFiller48AsString(value);
}
// Nested Class: Filler48
public class Filler48
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm8, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm8 =0;
    
    
    
    
    // [DEBUG] Field: Filler49, is_external=, is_static_class=False, static_prefix=
    private string _Filler49 ="";
    
    
    
    
public Filler48() {}

public Filler48(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm8(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller49(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller48AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm8.ToString().PadLeft(6, '0'));
    result.Append(_Filler49.PadRight(2));
    
    return result.ToString();
}

public void SetFiller48AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm8(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller49(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm8()
{
    return _Cgtdate2CCcyymm8;
}

// Standard Setter
public void SetCgtdate2CCcyymm8(int value)
{
    _Cgtdate2CCcyymm8 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm8AsString()
{
    return _Cgtdate2CCcyymm8.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm8AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm8 = parsed;
}

// Standard Getter
public string GetFiller49()
{
    return _Filler49;
}

// Standard Setter
public void SetFiller49(string value)
{
    _Filler49 = value;
}

// Get<>AsString()
public string GetFiller49AsString()
{
    return _Filler49.PadRight(2);
}

// Set<>AsString()
public void SetFiller49AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler49 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}
