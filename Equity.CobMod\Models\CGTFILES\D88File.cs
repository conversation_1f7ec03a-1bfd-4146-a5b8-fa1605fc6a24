using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D88File Data Structure

public class D88File
{
    private static int _size = 12;
    // [DEBUG] Class: D88File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler264, is_external=, is_static_class=False, static_prefix=
    private string _Filler264 ="$";
    
    
    
    
    // [DEBUG] Field: D88UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D88UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler265, is_external=, is_static_class=False, static_prefix=
    private string _Filler265 ="GLD.REP";
    
    
    
    
    
    // Serialization methods
    public string GetD88FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler264.PadRight(1));
        result.Append(_D88UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler265.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD88FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller264(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD88UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller265(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD88FileAsString();
    }
    // Set<>String Override function
    public void SetD88File(string value)
    {
        SetD88FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller264()
    {
        return _Filler264;
    }
    
    // Standard Setter
    public void SetFiller264(string value)
    {
        _Filler264 = value;
    }
    
    // Get<>AsString()
    public string GetFiller264AsString()
    {
        return _Filler264.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller264AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler264 = value;
    }
    
    // Standard Getter
    public int GetD88UserNo()
    {
        return _D88UserNo;
    }
    
    // Standard Setter
    public void SetD88UserNo(int value)
    {
        _D88UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD88UserNoAsString()
    {
        return _D88UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD88UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D88UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller265()
    {
        return _Filler265;
    }
    
    // Standard Setter
    public void SetFiller265(string value)
    {
        _Filler265 = value;
    }
    
    // Get<>AsString()
    public string GetFiller265AsString()
    {
        return _Filler265.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller265AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler265 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}