using System;
using System.Text;
namespace EquityProject.CommonDTO
{// DTO class representing GlobalVars Data Structure

public class GlobalVars
{
    private static int _size = 0;
    // [DEBUG] Class: GlobalVars, IsStaticClass: True
    // Fields in the class
    
    
    // [DEBUG] Field: EquityGlobalParms, is_external=True, is_static_class=True, static_prefix=static
    static private EquityGlobalParms _EquityGlobalParms = new EquityGlobalParms();
    
    
    
    
    
    // Serialization methods
    public static string GetGlobalVarsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_EquityGlobalParms.GetEquityGlobalParmsAsString());
        
        return result.ToString();
    }
    
    public static void SetGlobalVarsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1025 <= data.Length)
        {
            _EquityGlobalParms.SetEquityGlobalParmsAsString(data.Substring(offset, 1025));
        }
        else
        {
            _EquityGlobalParms.SetEquityGlobalParmsAsString(data.Substring(offset));
        }
        offset += 1025;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetGlobalVarsAsString();
    }
    // Set<>String Override function
    public void SetGlobalVars(string value)
    {
        SetGlobalVarsAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public static EquityGlobalParms GetEquityGlobalParms()
    {
        return _EquityGlobalParms;
    }
    
    // Standard Setter
    public static void SetEquityGlobalParms(EquityGlobalParms value)
    {
        _EquityGlobalParms = value;
    }
    
    // Get<>AsString()
    public static string GetEquityGlobalParmsAsString()
    {
        return _EquityGlobalParms != null ? _EquityGlobalParms.GetEquityGlobalParmsAsString() : "";
    }
    
    // Set<>AsString()
    public static void SetEquityGlobalParmsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_EquityGlobalParms == null)
        {
            _EquityGlobalParms = new EquityGlobalParms();
        }
        _EquityGlobalParms.SetEquityGlobalParmsAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetEquityGlobalParms(string value)
    {
        _EquityGlobalParms.SetEquityGlobalParmsAsString(value);
    }
    // Nested Class: EquityGlobalParms
    public class EquityGlobalParms
    {
        private static int _size = 1025;
        
        // Fields in the class
        
        
        // [DEBUG] Field: GEnvironment, is_external=True, is_static_class=False, static_prefix=
        private EquityGlobalParms.GEnvironment _GEnvironment = new EquityGlobalParms.GEnvironment();
        
        
        
        
        // [DEBUG] Field: GPaths, is_external=True, is_static_class=False, static_prefix=
        private EquityGlobalParms.GPaths _GPaths = new EquityGlobalParms.GPaths();
        
        
        
        
        // [DEBUG] Field: GGeneralParms, is_external=True, is_static_class=False, static_prefix=
        private EquityGlobalParms.GGeneralParms _GGeneralParms = new EquityGlobalParms.GGeneralParms();
        
        
        
        
    public EquityGlobalParms() {}
    
    public EquityGlobalParms(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _GEnvironment.SetGEnvironmentAsString(data.Substring(offset, GEnvironment.GetSize()));
        offset += 1;
        _GPaths.SetGPathsAsString(data.Substring(offset, GPaths.GetSize()));
        offset += 768;
        _GGeneralParms.SetGGeneralParmsAsString(data.Substring(offset, GGeneralParms.GetSize()));
        offset += 256;
        
    }
    
    // Serialization methods
    public string GetEquityGlobalParmsAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_GEnvironment.GetGEnvironmentAsString());
        result.Append(_GPaths.GetGPathsAsString());
        result.Append(_GGeneralParms.GetGGeneralParmsAsString());
        
        return result.ToString();
    }
    
    public void SetEquityGlobalParmsAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            _GEnvironment.SetGEnvironmentAsString(data.Substring(offset, 1));
        }
        else
        {
            _GEnvironment.SetGEnvironmentAsString(data.Substring(offset));
        }
        offset += 1;
        if (offset + 768 <= data.Length)
        {
            _GPaths.SetGPathsAsString(data.Substring(offset, 768));
        }
        else
        {
            _GPaths.SetGPathsAsString(data.Substring(offset));
        }
        offset += 768;
        if (offset + 256 <= data.Length)
        {
            _GGeneralParms.SetGGeneralParmsAsString(data.Substring(offset, 256));
        }
        else
        {
            _GGeneralParms.SetGGeneralParmsAsString(data.Substring(offset));
        }
        offset += 256;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public GEnvironment GetGEnvironment()
    {
        return _GEnvironment;
    }
    
    // Standard Setter
    public void SetGEnvironment(GEnvironment value)
    {
        _GEnvironment = value;
    }
    
    // Get<>AsString()
    public string GetGEnvironmentAsString()
    {
        return _GEnvironment != null ? _GEnvironment.GetGEnvironmentAsString() : "";
    }
    
    // Set<>AsString()
    public void SetGEnvironmentAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GEnvironment == null)
        {
            _GEnvironment = new GEnvironment();
        }
        _GEnvironment.SetGEnvironmentAsString(value);
    }
    
    // Standard Getter
    public GPaths GetGPaths()
    {
        return _GPaths;
    }
    
    // Standard Setter
    public void SetGPaths(GPaths value)
    {
        _GPaths = value;
    }
    
    // Get<>AsString()
    public string GetGPathsAsString()
    {
        return _GPaths != null ? _GPaths.GetGPathsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetGPathsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GPaths == null)
        {
            _GPaths = new GPaths();
        }
        _GPaths.SetGPathsAsString(value);
    }
    
    // Standard Getter
    public GGeneralParms GetGGeneralParms()
    {
        return _GGeneralParms;
    }
    
    // Standard Setter
    public void SetGGeneralParms(GGeneralParms value)
    {
        _GGeneralParms = value;
    }
    
    // Get<>AsString()
    public string GetGGeneralParmsAsString()
    {
        return _GGeneralParms != null ? _GGeneralParms.GetGGeneralParmsAsString() : "";
    }
    
    // Set<>AsString()
    public void SetGGeneralParmsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_GGeneralParms == null)
        {
            _GGeneralParms = new GGeneralParms();
        }
        _GGeneralParms.SetGGeneralParmsAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: GEnvironment
    public class GEnvironment
    {
        private static int _size = 1;
        
        // Fields in the class
        
        
        // [DEBUG] Field: GOs, is_external=, is_static_class=False, static_prefix=
        private int _GOs =0;
        
        
        // 88-level condition checks for GOs
        public bool IsGInDos()
        {
            if (this._GOs == 0) return true;
            return false;
        }
        public bool IsGInWindows()
        {
            if (this._GOs == 1) return true;
            return false;
        }
        
        
    public GEnvironment() {}
    
    public GEnvironment(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetGOs(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        
    }
    
    // Serialization methods
    public string GetGEnvironmentAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_GOs.ToString().PadLeft(1, '0'));
        
        return result.ToString();
    }
    
    public void SetGEnvironmentAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetGOs(parsedInt);
        }
        offset += 1;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetGOs()
    {
        return _GOs;
    }
    
    // Standard Setter
    public void SetGOs(int value)
    {
        _GOs = value;
    }
    
    // Get<>AsString()
    public string GetGOsAsString()
    {
        return _GOs.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetGOsAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _GOs = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: GPaths
public class GPaths
{
    private static int _size = 768;
    
    // Fields in the class
    
    
    // [DEBUG] Field: GMasterDataPath, is_external=, is_static_class=False, static_prefix=
    private string _GMasterDataPath ="";
    
    
    
    
    // [DEBUG] Field: GUserDataPath, is_external=, is_static_class=False, static_prefix=
    private string _GUserDataPath ="";
    
    
    
    
    // [DEBUG] Field: GAdminDataPath, is_external=, is_static_class=False, static_prefix=
    private string _GAdminDataPath ="";
    
    
    
    
public GPaths() {}

public GPaths(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetGMasterDataPath(data.Substring(offset, 256).Trim());
    offset += 256;
    SetGUserDataPath(data.Substring(offset, 256).Trim());
    offset += 256;
    SetGAdminDataPath(data.Substring(offset, 256).Trim());
    offset += 256;
    
}

// Serialization methods
public string GetGPathsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_GMasterDataPath.PadRight(256));
    result.Append(_GUserDataPath.PadRight(256));
    result.Append(_GAdminDataPath.PadRight(256));
    
    return result.ToString();
}

public void SetGPathsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 256 <= data.Length)
    {
        string extracted = data.Substring(offset, 256).Trim();
        SetGMasterDataPath(extracted);
    }
    offset += 256;
    if (offset + 256 <= data.Length)
    {
        string extracted = data.Substring(offset, 256).Trim();
        SetGUserDataPath(extracted);
    }
    offset += 256;
    if (offset + 256 <= data.Length)
    {
        string extracted = data.Substring(offset, 256).Trim();
        SetGAdminDataPath(extracted);
    }
    offset += 256;
}

// Getter and Setter methods

// Standard Getter
public string GetGMasterDataPath()
{
    return _GMasterDataPath;
}

// Standard Setter
public void SetGMasterDataPath(string value)
{
    _GMasterDataPath = value;
}

// Get<>AsString()
public string GetGMasterDataPathAsString()
{
    return _GMasterDataPath.PadRight(256);
}

// Set<>AsString()
public void SetGMasterDataPathAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GMasterDataPath = value;
}

// Standard Getter
public string GetGUserDataPath()
{
    return _GUserDataPath;
}

// Standard Setter
public void SetGUserDataPath(string value)
{
    _GUserDataPath = value;
}

// Get<>AsString()
public string GetGUserDataPathAsString()
{
    return _GUserDataPath.PadRight(256);
}

// Set<>AsString()
public void SetGUserDataPathAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GUserDataPath = value;
}

// Standard Getter
public string GetGAdminDataPath()
{
    return _GAdminDataPath;
}

// Standard Setter
public void SetGAdminDataPath(string value)
{
    _GAdminDataPath = value;
}

// Get<>AsString()
public string GetGAdminDataPathAsString()
{
    return _GAdminDataPath.PadRight(256);
}

// Set<>AsString()
public void SetGAdminDataPathAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GAdminDataPath = value;
}



public static int GetSize()
{
    return _size;
}

}
// Nested Class: GGeneralParms
public class GGeneralParms
{
    private static int _size = 256;
    
    // Fields in the class
    
    
    // [DEBUG] Field: GUserNo, is_external=, is_static_class=False, static_prefix=
    private string _GUserNo ="";
    
    
    
    
    // [DEBUG] Field: GYear, is_external=, is_static_class=False, static_prefix=
    private string _GYear ="";
    
    
    
    
    // [DEBUG] Field: GFileName, is_external=, is_static_class=False, static_prefix=
    private string _GFileName ="";
    
    
    
    
    // [DEBUG] Field: GReplaceRecords, is_external=, is_static_class=False, static_prefix=
    private string _GReplaceRecords ="";
    
    
    // 88-level condition checks for GReplaceRecords
    public bool IsReplaceRecords()
    {
        if (this._GReplaceRecords == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: GAllFunds, is_external=, is_static_class=False, static_prefix=
    private string _GAllFunds ="";
    
    
    
    
    // [DEBUG] Field: GStartDate, is_external=, is_static_class=False, static_prefix=
    private string _GStartDate ="";
    
    
    
    
    // [DEBUG] Field: GEndDate, is_external=, is_static_class=False, static_prefix=
    private string _GEndDate ="";
    
    
    
    
    // [DEBUG] Field: GFullReport, is_external=, is_static_class=False, static_prefix=
    private string _GFullReport ="";
    
    
    // 88-level condition checks for GFullReport
    public bool IsFullReport()
    {
        if (this._GFullReport == "'F'") return true;
        return false;
    }
    public bool IsRejectsOnly()
    {
        if (this._GFullReport == "'R'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: GSuppressCgtr04, is_external=, is_static_class=False, static_prefix=
    private string _GSuppressCgtr04 ="";
    
    
    // 88-level condition checks for GSuppressCgtr04
    public bool IsSuppressCgtr04()
    {
        if (this._GSuppressCgtr04 == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: GLogSwitch, is_external=, is_static_class=False, static_prefix=
    private string _GLogSwitch ="";
    
    
    // 88-level condition checks for GLogSwitch
    public bool IsDisplayModeScroll()
    {
        if (this._GLogSwitch == "'D'") return true;
        return false;
    }
    public bool IsDisplayModePause()
    {
        if (this._GLogSwitch == "'P'") return true;
        return false;
    }
    public bool IsLogMode()
    {
        if (this._GLogSwitch == "'L'") return true;
        return false;
    }
    public bool IsOffMode()
    {
        if (this._GLogSwitch == "'O'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: GReportNo, is_external=, is_static_class=False, static_prefix=
    private string _GReportNo ="";
    
    
    
    
    // [DEBUG] Field: GFromYear, is_external=, is_static_class=False, static_prefix=
    private string _GFromYear ="";
    
    
    
    
    // [DEBUG] Field: GToYear, is_external=, is_static_class=False, static_prefix=
    private string _GToYear ="";
    
    
    
    
    // [DEBUG] Field: GCountryExporter, is_external=, is_static_class=False, static_prefix=
    private string _GCountryExporter ="";
    
    
    
    
    // [DEBUG] Field: GGroupExporter, is_external=, is_static_class=False, static_prefix=
    private string _GGroupExporter ="";
    
    
    
    
    // [DEBUG] Field: GFundExporter, is_external=, is_static_class=False, static_prefix=
    private string _GFundExporter ="";
    
    
    
    
    // [DEBUG] Field: GStockExporter, is_external=, is_static_class=False, static_prefix=
    private string _GStockExporter ="";
    
    
    
    
    // [DEBUG] Field: GPriceExporter, is_external=, is_static_class=False, static_prefix=
    private string _GPriceExporter ="";
    
    
    
    
    // [DEBUG] Field: GCgtscot1Format, is_external=, is_static_class=False, static_prefix=
    private string _GCgtscot1Format ="";
    
    
    // 88-level condition checks for GCgtscot1Format
    public bool IsCgtscot1RealisedExtract()
    {
        if (this._GCgtscot1Format == "'1'") return true;
        if (this._GCgtscot1Format == "'3'") return true;
        return false;
    }
    public bool IsCgtscot1GainLossExport()
    {
        if (this._GCgtscot1Format == "'2'") return true;
        if (this._GCgtscot1Format == "'3'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: GPlOnDistributions, is_external=, is_static_class=False, static_prefix=
    private string _GPlOnDistributions ="";
    
    
    // 88-level condition checks for GPlOnDistributions
    public bool IsPlOnDistributions()
    {
        if (this._GPlOnDistributions == "'Y'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: GFullPricingExport, is_external=, is_static_class=False, static_prefix=
    private string _GFullPricingExport ="";
    
    
    // 88-level condition checks for GFullPricingExport
    public bool IsFullPricingExport()
    {
        if (this._GFullPricingExport == "'Y'") return true;
        return false;
    }
    
    
public GGeneralParms() {}

public GGeneralParms(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetGUserNo(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGYear(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGFileName(data.Substring(offset, 256).Trim());
    offset += 256;
    SetGReplaceRecords(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGAllFunds(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGStartDate(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGEndDate(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGFullReport(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGSuppressCgtr04(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGLogSwitch(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGReportNo(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGFromYear(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGToYear(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGCountryExporter(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGGroupExporter(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGFundExporter(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGStockExporter(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGPriceExporter(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGCgtscot1Format(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGPlOnDistributions(data.Substring(offset, 0).Trim());
    offset += 0;
    SetGFullPricingExport(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetGGeneralParmsAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_GUserNo.PadRight(0));
    result.Append(_GYear.PadRight(0));
    result.Append(_GFileName.PadRight(256));
    result.Append(_GReplaceRecords.PadRight(0));
    result.Append(_GAllFunds.PadRight(0));
    result.Append(_GStartDate.PadRight(0));
    result.Append(_GEndDate.PadRight(0));
    result.Append(_GFullReport.PadRight(0));
    result.Append(_GSuppressCgtr04.PadRight(0));
    result.Append(_GLogSwitch.PadRight(0));
    result.Append(_GReportNo.PadRight(0));
    result.Append(_GFromYear.PadRight(0));
    result.Append(_GToYear.PadRight(0));
    result.Append(_GCountryExporter.PadRight(0));
    result.Append(_GGroupExporter.PadRight(0));
    result.Append(_GFundExporter.PadRight(0));
    result.Append(_GStockExporter.PadRight(0));
    result.Append(_GPriceExporter.PadRight(0));
    result.Append(_GCgtscot1Format.PadRight(0));
    result.Append(_GPlOnDistributions.PadRight(0));
    result.Append(_GFullPricingExport.PadRight(0));
    
    return result.ToString();
}

public void SetGGeneralParmsAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGUserNo(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGYear(extracted);
    }
    offset += 0;
    if (offset + 256 <= data.Length)
    {
        string extracted = data.Substring(offset, 256).Trim();
        SetGFileName(extracted);
    }
    offset += 256;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGReplaceRecords(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGAllFunds(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGStartDate(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGEndDate(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGFullReport(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGSuppressCgtr04(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGLogSwitch(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGReportNo(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGFromYear(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGToYear(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGCountryExporter(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGGroupExporter(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGFundExporter(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGStockExporter(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGPriceExporter(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGCgtscot1Format(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGPlOnDistributions(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetGFullPricingExport(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetGUserNo()
{
    return _GUserNo;
}

// Standard Setter
public void SetGUserNo(string value)
{
    _GUserNo = value;
}

// Get<>AsString()
public string GetGUserNoAsString()
{
    return _GUserNo.PadRight(0);
}

// Set<>AsString()
public void SetGUserNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GUserNo = value;
}

// Standard Getter
public string GetGYear()
{
    return _GYear;
}

// Standard Setter
public void SetGYear(string value)
{
    _GYear = value;
}

// Get<>AsString()
public string GetGYearAsString()
{
    return _GYear.PadRight(0);
}

// Set<>AsString()
public void SetGYearAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GYear = value;
}

// Standard Getter
public string GetGFileName()
{
    return _GFileName;
}

// Standard Setter
public void SetGFileName(string value)
{
    _GFileName = value;
}

// Get<>AsString()
public string GetGFileNameAsString()
{
    return _GFileName.PadRight(256);
}

// Set<>AsString()
public void SetGFileNameAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GFileName = value;
}

// Standard Getter
public string GetGReplaceRecords()
{
    return _GReplaceRecords;
}

// Standard Setter
public void SetGReplaceRecords(string value)
{
    _GReplaceRecords = value;
}

// Get<>AsString()
public string GetGReplaceRecordsAsString()
{
    return _GReplaceRecords.PadRight(0);
}

// Set<>AsString()
public void SetGReplaceRecordsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GReplaceRecords = value;
}

// Standard Getter
public string GetGAllFunds()
{
    return _GAllFunds;
}

// Standard Setter
public void SetGAllFunds(string value)
{
    _GAllFunds = value;
}

// Get<>AsString()
public string GetGAllFundsAsString()
{
    return _GAllFunds.PadRight(0);
}

// Set<>AsString()
public void SetGAllFundsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GAllFunds = value;
}

// Standard Getter
public string GetGStartDate()
{
    return _GStartDate;
}

// Standard Setter
public void SetGStartDate(string value)
{
    _GStartDate = value;
}

// Get<>AsString()
public string GetGStartDateAsString()
{
    return _GStartDate.PadRight(0);
}

// Set<>AsString()
public void SetGStartDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GStartDate = value;
}

// Standard Getter
public string GetGEndDate()
{
    return _GEndDate;
}

// Standard Setter
public void SetGEndDate(string value)
{
    _GEndDate = value;
}

// Get<>AsString()
public string GetGEndDateAsString()
{
    return _GEndDate.PadRight(0);
}

// Set<>AsString()
public void SetGEndDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GEndDate = value;
}

// Standard Getter
public string GetGFullReport()
{
    return _GFullReport;
}

// Standard Setter
public void SetGFullReport(string value)
{
    _GFullReport = value;
}

// Get<>AsString()
public string GetGFullReportAsString()
{
    return _GFullReport.PadRight(0);
}

// Set<>AsString()
public void SetGFullReportAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GFullReport = value;
}

// Standard Getter
public string GetGSuppressCgtr04()
{
    return _GSuppressCgtr04;
}

// Standard Setter
public void SetGSuppressCgtr04(string value)
{
    _GSuppressCgtr04 = value;
}

// Get<>AsString()
public string GetGSuppressCgtr04AsString()
{
    return _GSuppressCgtr04.PadRight(0);
}

// Set<>AsString()
public void SetGSuppressCgtr04AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GSuppressCgtr04 = value;
}

// Standard Getter
public string GetGLogSwitch()
{
    return _GLogSwitch;
}

// Standard Setter
public void SetGLogSwitch(string value)
{
    _GLogSwitch = value;
}

// Get<>AsString()
public string GetGLogSwitchAsString()
{
    return _GLogSwitch.PadRight(0);
}

// Set<>AsString()
public void SetGLogSwitchAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GLogSwitch = value;
}

// Standard Getter
public string GetGReportNo()
{
    return _GReportNo;
}

// Standard Setter
public void SetGReportNo(string value)
{
    _GReportNo = value;
}

// Get<>AsString()
public string GetGReportNoAsString()
{
    return _GReportNo.PadRight(0);
}

// Set<>AsString()
public void SetGReportNoAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GReportNo = value;
}

// Standard Getter
public string GetGFromYear()
{
    return _GFromYear;
}

// Standard Setter
public void SetGFromYear(string value)
{
    _GFromYear = value;
}

// Get<>AsString()
public string GetGFromYearAsString()
{
    return _GFromYear.PadRight(0);
}

// Set<>AsString()
public void SetGFromYearAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GFromYear = value;
}

// Standard Getter
public string GetGToYear()
{
    return _GToYear;
}

// Standard Setter
public void SetGToYear(string value)
{
    _GToYear = value;
}

// Get<>AsString()
public string GetGToYearAsString()
{
    return _GToYear.PadRight(0);
}

// Set<>AsString()
public void SetGToYearAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GToYear = value;
}

// Standard Getter
public string GetGCountryExporter()
{
    return _GCountryExporter;
}

// Standard Setter
public void SetGCountryExporter(string value)
{
    _GCountryExporter = value;
}

// Get<>AsString()
public string GetGCountryExporterAsString()
{
    return _GCountryExporter.PadRight(0);
}

// Set<>AsString()
public void SetGCountryExporterAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GCountryExporter = value;
}

// Standard Getter
public string GetGGroupExporter()
{
    return _GGroupExporter;
}

// Standard Setter
public void SetGGroupExporter(string value)
{
    _GGroupExporter = value;
}

// Get<>AsString()
public string GetGGroupExporterAsString()
{
    return _GGroupExporter.PadRight(0);
}

// Set<>AsString()
public void SetGGroupExporterAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GGroupExporter = value;
}

// Standard Getter
public string GetGFundExporter()
{
    return _GFundExporter;
}

// Standard Setter
public void SetGFundExporter(string value)
{
    _GFundExporter = value;
}

// Get<>AsString()
public string GetGFundExporterAsString()
{
    return _GFundExporter.PadRight(0);
}

// Set<>AsString()
public void SetGFundExporterAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GFundExporter = value;
}

// Standard Getter
public string GetGStockExporter()
{
    return _GStockExporter;
}

// Standard Setter
public void SetGStockExporter(string value)
{
    _GStockExporter = value;
}

// Get<>AsString()
public string GetGStockExporterAsString()
{
    return _GStockExporter.PadRight(0);
}

// Set<>AsString()
public void SetGStockExporterAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GStockExporter = value;
}

// Standard Getter
public string GetGPriceExporter()
{
    return _GPriceExporter;
}

// Standard Setter
public void SetGPriceExporter(string value)
{
    _GPriceExporter = value;
}

// Get<>AsString()
public string GetGPriceExporterAsString()
{
    return _GPriceExporter.PadRight(0);
}

// Set<>AsString()
public void SetGPriceExporterAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GPriceExporter = value;
}

// Standard Getter
public string GetGCgtscot1Format()
{
    return _GCgtscot1Format;
}

// Standard Setter
public void SetGCgtscot1Format(string value)
{
    _GCgtscot1Format = value;
}

// Get<>AsString()
public string GetGCgtscot1FormatAsString()
{
    return _GCgtscot1Format.PadRight(0);
}

// Set<>AsString()
public void SetGCgtscot1FormatAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GCgtscot1Format = value;
}

// Standard Getter
public string GetGPlOnDistributions()
{
    return _GPlOnDistributions;
}

// Standard Setter
public void SetGPlOnDistributions(string value)
{
    _GPlOnDistributions = value;
}

// Get<>AsString()
public string GetGPlOnDistributionsAsString()
{
    return _GPlOnDistributions.PadRight(0);
}

// Set<>AsString()
public void SetGPlOnDistributionsAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GPlOnDistributions = value;
}

// Standard Getter
public string GetGFullPricingExport()
{
    return _GFullPricingExport;
}

// Standard Setter
public void SetGFullPricingExport(string value)
{
    _GFullPricingExport = value;
}

// Get<>AsString()
public string GetGFullPricingExportAsString()
{
    return _GFullPricingExport.PadRight(0);
}

// Set<>AsString()
public void SetGFullPricingExportAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _GFullPricingExport = value;
}



public static int GetSize()
{
    return _size;
}

}
}

}}
