using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing Cgtdate2LinkageDate7 Data Structure

public class Cgtdate2LinkageDate7
{
    private static int _size = 40;
    // [DEBUG] Class: Cgtdate2LinkageDate7, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2Ccyymmdd7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2Ccyymmdd7 =0;
    
    
    
    
    // [DEBUG] Field: Filler40, is_external=, is_static_class=False, static_prefix=
    private Filler40 _Filler40 = new Filler40();
    
    
    
    
    // [DEBUG] Field: Filler41, is_external=, is_static_class=False, static_prefix=
    private Filler41 _Filler41 = new Filler41();
    
    
    
    
    // [DEBUG] Field: Filler42, is_external=, is_static_class=False, static_prefix=
    private Filler42 _Filler42 = new Filler42();
    
    
    
    
    // [DEBUG] Field: Filler43, is_external=, is_static_class=False, static_prefix=
    private Filler43 _Filler43 = new Filler43();
    
    
    
    
    
    // Serialization methods
    public string GetCgtdate2LinkageDate7AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Ccyymmdd7.ToString().PadLeft(8, '0'));
        result.Append(_Filler40.GetFiller40AsString());
        result.Append(_Filler41.GetFiller41AsString());
        result.Append(_Filler42.GetFiller42AsString());
        result.Append(_Filler43.GetFiller43AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2LinkageDate7AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCgtdate2Ccyymmdd7(parsedInt);
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler40.SetFiller40AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler40.SetFiller40AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler41.SetFiller41AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler41.SetFiller41AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler42.SetFiller42AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler42.SetFiller42AsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            _Filler43.SetFiller43AsString(data.Substring(offset, 8));
        }
        else
        {
            _Filler43.SetFiller43AsString(data.Substring(offset));
        }
        offset += 8;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCgtdate2LinkageDate7AsString();
    }
    // Set<>String Override function
    public void SetCgtdate2LinkageDate7(string value)
    {
        SetCgtdate2LinkageDate7AsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCgtdate2Ccyymmdd7()
    {
        return _Cgtdate2Ccyymmdd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Ccyymmdd7(int value)
    {
        _Cgtdate2Ccyymmdd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Ccyymmdd7AsString()
    {
        return _Cgtdate2Ccyymmdd7.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetCgtdate2Ccyymmdd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2Ccyymmdd7 = parsed;
    }
    
    // Standard Getter
    public Filler40 GetFiller40()
    {
        return _Filler40;
    }
    
    // Standard Setter
    public void SetFiller40(Filler40 value)
    {
        _Filler40 = value;
    }
    
    // Get<>AsString()
    public string GetFiller40AsString()
    {
        return _Filler40 != null ? _Filler40.GetFiller40AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller40AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler40 == null)
        {
            _Filler40 = new Filler40();
        }
        _Filler40.SetFiller40AsString(value);
    }
    
    // Standard Getter
    public Filler41 GetFiller41()
    {
        return _Filler41;
    }
    
    // Standard Setter
    public void SetFiller41(Filler41 value)
    {
        _Filler41 = value;
    }
    
    // Get<>AsString()
    public string GetFiller41AsString()
    {
        return _Filler41 != null ? _Filler41.GetFiller41AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller41AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler41 == null)
        {
            _Filler41 = new Filler41();
        }
        _Filler41.SetFiller41AsString(value);
    }
    
    // Standard Getter
    public Filler42 GetFiller42()
    {
        return _Filler42;
    }
    
    // Standard Setter
    public void SetFiller42(Filler42 value)
    {
        _Filler42 = value;
    }
    
    // Get<>AsString()
    public string GetFiller42AsString()
    {
        return _Filler42 != null ? _Filler42.GetFiller42AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller42AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler42 == null)
        {
            _Filler42 = new Filler42();
        }
        _Filler42.SetFiller42AsString(value);
    }
    
    // Standard Getter
    public Filler43 GetFiller43()
    {
        return _Filler43;
    }
    
    // Standard Setter
    public void SetFiller43(Filler43 value)
    {
        _Filler43 = value;
    }
    
    // Get<>AsString()
    public string GetFiller43AsString()
    {
        return _Filler43 != null ? _Filler43.GetFiller43AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller43AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler43 == null)
        {
            _Filler43 = new Filler43();
        }
        _Filler43.SetFiller43AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetFiller40(string value)
    {
        _Filler40.SetFiller40AsString(value);
    }
    // Nested Class: Filler40
    public class Filler40
    {
        private static int _size = 8;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Cc7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Cc7 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Yymmdd7, is_external=, is_static_class=False, static_prefix=
        private Filler40.Cgtdate2Yymmdd7 _Cgtdate2Yymmdd7 = new Filler40.Cgtdate2Yymmdd7();
        
        
        
        
    public Filler40() {}
    
    public Filler40(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Cc7(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(data.Substring(offset, Cgtdate2Yymmdd7.GetSize()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetFiller40AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Cc7.PadRight(2));
        result.Append(_Cgtdate2Yymmdd7.GetCgtdate2Yymmdd7AsString());
        
        return result.ToString();
    }
    
    public void SetFiller40AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Cc7(extracted);
        }
        offset += 2;
        if (offset + 6 <= data.Length)
        {
            _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(data.Substring(offset, 6));
        }
        else
        {
            _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(data.Substring(offset));
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Cc7()
    {
        return _Cgtdate2Cc7;
    }
    
    // Standard Setter
    public void SetCgtdate2Cc7(string value)
    {
        _Cgtdate2Cc7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Cc7AsString()
    {
        return _Cgtdate2Cc7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Cc7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Cc7 = value;
    }
    
    // Standard Getter
    public Cgtdate2Yymmdd7 GetCgtdate2Yymmdd7()
    {
        return _Cgtdate2Yymmdd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Yymmdd7(Cgtdate2Yymmdd7 value)
    {
        _Cgtdate2Yymmdd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yymmdd7AsString()
    {
        return _Cgtdate2Yymmdd7 != null ? _Cgtdate2Yymmdd7.GetCgtdate2Yymmdd7AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yymmdd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Yymmdd7 == null)
        {
            _Cgtdate2Yymmdd7 = new Cgtdate2Yymmdd7();
        }
        _Cgtdate2Yymmdd7.SetCgtdate2Yymmdd7AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Yymmdd7
    public class Cgtdate2Yymmdd7
    {
        private static int _size = 6;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Yy7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Yy7 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Mmdd7, is_external=, is_static_class=False, static_prefix=
        private Cgtdate2Yymmdd7.Cgtdate2Mmdd7 _Cgtdate2Mmdd7 = new Cgtdate2Yymmdd7.Cgtdate2Mmdd7();
        
        
        
        
    public Cgtdate2Yymmdd7() {}
    
    public Cgtdate2Yymmdd7(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Yy7(data.Substring(offset, 2).Trim());
        offset += 2;
        _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(data.Substring(offset, Cgtdate2Mmdd7.GetSize()));
        offset += 4;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Yymmdd7AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Yy7.PadRight(2));
        result.Append(_Cgtdate2Mmdd7.GetCgtdate2Mmdd7AsString());
        
        return result.ToString();
    }
    
    public void SetCgtdate2Yymmdd7AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Yy7(extracted);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(data.Substring(offset, 4));
        }
        else
        {
            _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(data.Substring(offset));
        }
        offset += 4;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Yy7()
    {
        return _Cgtdate2Yy7;
    }
    
    // Standard Setter
    public void SetCgtdate2Yy7(string value)
    {
        _Cgtdate2Yy7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Yy7AsString()
    {
        return _Cgtdate2Yy7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Yy7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Yy7 = value;
    }
    
    // Standard Getter
    public Cgtdate2Mmdd7 GetCgtdate2Mmdd7()
    {
        return _Cgtdate2Mmdd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Mmdd7(Cgtdate2Mmdd7 value)
    {
        _Cgtdate2Mmdd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mmdd7AsString()
    {
        return _Cgtdate2Mmdd7 != null ? _Cgtdate2Mmdd7.GetCgtdate2Mmdd7AsString() : "";
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mmdd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Cgtdate2Mmdd7 == null)
        {
            _Cgtdate2Mmdd7 = new Cgtdate2Mmdd7();
        }
        _Cgtdate2Mmdd7.SetCgtdate2Mmdd7AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: Cgtdate2Mmdd7
    public class Cgtdate2Mmdd7
    {
        private static int _size = 4;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Cgtdate2Mm7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Mm7 ="";
        
        
        
        
        // [DEBUG] Field: Cgtdate2Dd7, is_external=, is_static_class=False, static_prefix=
        private string _Cgtdate2Dd7 ="";
        
        
        
        
    public Cgtdate2Mmdd7() {}
    
    public Cgtdate2Mmdd7(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCgtdate2Mm7(data.Substring(offset, 2).Trim());
        offset += 2;
        SetCgtdate2Dd7(data.Substring(offset, 2).Trim());
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetCgtdate2Mmdd7AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Cgtdate2Mm7.PadRight(2));
        result.Append(_Cgtdate2Dd7.PadRight(2));
        
        return result.ToString();
    }
    
    public void SetCgtdate2Mmdd7AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Mm7(extracted);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetCgtdate2Dd7(extracted);
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetCgtdate2Mm7()
    {
        return _Cgtdate2Mm7;
    }
    
    // Standard Setter
    public void SetCgtdate2Mm7(string value)
    {
        _Cgtdate2Mm7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Mm7AsString()
    {
        return _Cgtdate2Mm7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Mm7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Mm7 = value;
    }
    
    // Standard Getter
    public string GetCgtdate2Dd7()
    {
        return _Cgtdate2Dd7;
    }
    
    // Standard Setter
    public void SetCgtdate2Dd7(string value)
    {
        _Cgtdate2Dd7 = value;
    }
    
    // Get<>AsString()
    public string GetCgtdate2Dd7AsString()
    {
        return _Cgtdate2Dd7.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetCgtdate2Dd7AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Cgtdate2Dd7 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetFiller41(string value)
{
    _Filler41.SetFiller41AsString(value);
}
// Nested Class: Filler41
public class Filler41
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyy7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyy7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMmdd7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMmdd7 =0;
    
    
    
    
public Filler41() {}

public Filler41(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyy7(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    SetCgtdate2CMmdd7(int.Parse(data.Substring(offset, 4).Trim()));
    offset += 4;
    
}

// Serialization methods
public string GetFiller41AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyy7.ToString().PadLeft(4, '0'));
    result.Append(_Cgtdate2CMmdd7.ToString().PadLeft(4, '0'));
    
    return result.ToString();
}

public void SetFiller41AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyy7(parsedInt);
    }
    offset += 4;
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMmdd7(parsedInt);
    }
    offset += 4;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyy7()
{
    return _Cgtdate2CCcyy7;
}

// Standard Setter
public void SetCgtdate2CCcyy7(int value)
{
    _Cgtdate2CCcyy7 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyy7AsString()
{
    return _Cgtdate2CCcyy7.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyy7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyy7 = parsed;
}

// Standard Getter
public int GetCgtdate2CMmdd7()
{
    return _Cgtdate2CMmdd7;
}

// Standard Setter
public void SetCgtdate2CMmdd7(int value)
{
    _Cgtdate2CMmdd7 = value;
}

// Get<>AsString()
public string GetCgtdate2CMmdd7AsString()
{
    return _Cgtdate2CMmdd7.ToString().PadLeft(4, '0');
}

// Set<>AsString()
public void SetCgtdate2CMmdd7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMmdd7 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller42(string value)
{
    _Filler42.SetFiller42AsString(value);
}
// Nested Class: Filler42
public class Filler42
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCc7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCc7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CYy7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CYy7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CMm7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CMm7 =0;
    
    
    
    
    // [DEBUG] Field: Cgtdate2CDd7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CDd7 =0;
    
    
    
    
public Filler42() {}

public Filler42(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCc7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CYy7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CMm7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    SetCgtdate2CDd7(int.Parse(data.Substring(offset, 2).Trim()));
    offset += 2;
    
}

// Serialization methods
public string GetFiller42AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCc7.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CYy7.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CMm7.ToString().PadLeft(2, '0'));
    result.Append(_Cgtdate2CDd7.ToString().PadLeft(2, '0'));
    
    return result.ToString();
}

public void SetFiller42AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCc7(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CYy7(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CMm7(parsedInt);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CDd7(parsedInt);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCc7()
{
    return _Cgtdate2CCc7;
}

// Standard Setter
public void SetCgtdate2CCc7(int value)
{
    _Cgtdate2CCc7 = value;
}

// Get<>AsString()
public string GetCgtdate2CCc7AsString()
{
    return _Cgtdate2CCc7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CCc7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCc7 = parsed;
}

// Standard Getter
public int GetCgtdate2CYy7()
{
    return _Cgtdate2CYy7;
}

// Standard Setter
public void SetCgtdate2CYy7(int value)
{
    _Cgtdate2CYy7 = value;
}

// Get<>AsString()
public string GetCgtdate2CYy7AsString()
{
    return _Cgtdate2CYy7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CYy7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CYy7 = parsed;
}

// Standard Getter
public int GetCgtdate2CMm7()
{
    return _Cgtdate2CMm7;
}

// Standard Setter
public void SetCgtdate2CMm7(int value)
{
    _Cgtdate2CMm7 = value;
}

// Get<>AsString()
public string GetCgtdate2CMm7AsString()
{
    return _Cgtdate2CMm7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CMm7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CMm7 = parsed;
}

// Standard Getter
public int GetCgtdate2CDd7()
{
    return _Cgtdate2CDd7;
}

// Standard Setter
public void SetCgtdate2CDd7(int value)
{
    _Cgtdate2CDd7 = value;
}

// Get<>AsString()
public string GetCgtdate2CDd7AsString()
{
    return _Cgtdate2CDd7.ToString().PadLeft(2, '0');
}

// Set<>AsString()
public void SetCgtdate2CDd7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CDd7 = parsed;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller43(string value)
{
    _Filler43.SetFiller43AsString(value);
}
// Nested Class: Filler43
public class Filler43
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Cgtdate2CCcyymm7, is_external=, is_static_class=False, static_prefix=
    private int _Cgtdate2CCcyymm7 =0;
    
    
    
    
    // [DEBUG] Field: Filler44, is_external=, is_static_class=False, static_prefix=
    private string _Filler44 ="";
    
    
    
    
public Filler43() {}

public Filler43(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetCgtdate2CCcyymm7(int.Parse(data.Substring(offset, 6).Trim()));
    offset += 6;
    SetFiller44(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetFiller43AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Cgtdate2CCcyymm7.ToString().PadLeft(6, '0'));
    result.Append(_Filler44.PadRight(2));
    
    return result.ToString();
}

public void SetFiller43AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        int parsedInt;
        if (int.TryParse(extracted, out parsedInt)) SetCgtdate2CCcyymm7(parsedInt);
    }
    offset += 6;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetFiller44(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public int GetCgtdate2CCcyymm7()
{
    return _Cgtdate2CCcyymm7;
}

// Standard Setter
public void SetCgtdate2CCcyymm7(int value)
{
    _Cgtdate2CCcyymm7 = value;
}

// Get<>AsString()
public string GetCgtdate2CCcyymm7AsString()
{
    return _Cgtdate2CCcyymm7.ToString().PadLeft(6, '0');
}

// Set<>AsString()
public void SetCgtdate2CCcyymm7AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    int parsed;
    if (int.TryParse(value.Trim(), out parsed)) _Cgtdate2CCcyymm7 = parsed;
}

// Standard Getter
public string GetFiller44()
{
    return _Filler44;
}

// Standard Setter
public void SetFiller44(string value)
{
    _Filler44 = value;
}

// Get<>AsString()
public string GetFiller44AsString()
{
    return _Filler44.PadRight(2);
}

// Set<>AsString()
public void SetFiller44AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler44 = value;
}



public static int GetSize()
{
    return _size;
}

}

}}