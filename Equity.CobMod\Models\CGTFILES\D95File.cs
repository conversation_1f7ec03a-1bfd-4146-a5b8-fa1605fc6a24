using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D95File Data Structure

public class D95File
{
    private static int _size = 12;
    // [DEBUG] Class: D95File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler276, is_external=, is_static_class=False, static_prefix=
    private string _Filler276 ="$";
    
    
    
    
    // [DEBUG] Field: D95UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D95UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler277, is_external=, is_static_class=False, static_prefix=
    private string _Filler277 ="RL";
    
    
    
    
    // [DEBUG] Field: D95ReportNo, is_external=, is_static_class=False, static_prefix=
    private int _D95ReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler278, is_external=, is_static_class=False, static_prefix=
    private string _Filler278 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetD95FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler276.PadRight(1));
        result.Append(_D95UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler277.PadRight(2));
        result.Append(_D95ReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler278.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetD95FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller276(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD95UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller277(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD95ReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller278(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD95FileAsString();
    }
    // Set<>String Override function
    public void SetD95File(string value)
    {
        SetD95FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller276()
    {
        return _Filler276;
    }
    
    // Standard Setter
    public void SetFiller276(string value)
    {
        _Filler276 = value;
    }
    
    // Get<>AsString()
    public string GetFiller276AsString()
    {
        return _Filler276.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller276AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler276 = value;
    }
    
    // Standard Getter
    public int GetD95UserNo()
    {
        return _D95UserNo;
    }
    
    // Standard Setter
    public void SetD95UserNo(int value)
    {
        _D95UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD95UserNoAsString()
    {
        return _D95UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD95UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D95UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller277()
    {
        return _Filler277;
    }
    
    // Standard Setter
    public void SetFiller277(string value)
    {
        _Filler277 = value;
    }
    
    // Get<>AsString()
    public string GetFiller277AsString()
    {
        return _Filler277.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller277AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler277 = value;
    }
    
    // Standard Getter
    public int GetD95ReportNo()
    {
        return _D95ReportNo;
    }
    
    // Standard Setter
    public void SetD95ReportNo(int value)
    {
        _D95ReportNo = value;
    }
    
    // Get<>AsString()
    public string GetD95ReportNoAsString()
    {
        return _D95ReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD95ReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D95ReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller278()
    {
        return _Filler278;
    }
    
    // Standard Setter
    public void SetFiller278(string value)
    {
        _Filler278 = value;
    }
    
    // Get<>AsString()
    public string GetFiller278AsString()
    {
        return _Filler278.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller278AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler278 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}