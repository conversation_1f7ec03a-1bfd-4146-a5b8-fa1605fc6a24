using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing IlosuReportFile Data Structure

public class IlosuReportFile
{
    private static int _size = 12;
    // [DEBUG] Class: IlosuReportFile, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: IlosuUserNo, is_external=, is_static_class=False, static_prefix=
    private string _IlosuUserNo ="";
    
    
    
    
    // [DEBUG] Field: Filler108, is_external=, is_static_class=False, static_prefix=
    private string _Filler108 ="RN";
    
    
    
    
    // [DEBUG] Field: IlosuReportNo, is_external=, is_static_class=False, static_prefix=
    private int _IlosuReportNo =0;
    
    
    
    
    // [DEBUG] Field: Filler109, is_external=, is_static_class=False, static_prefix=
    private string _Filler109 =".REP";
    
    
    
    
    
    // Serialization methods
    public string GetIlosuReportFileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_IlosuUserNo.PadRight(5));
        result.Append(_Filler108.PadRight(2));
        result.Append(_IlosuReportNo.ToString().PadLeft(1, '0'));
        result.Append(_Filler109.PadRight(4));
        
        return result.ToString();
    }
    
    public void SetIlosuReportFileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 5 <= data.Length)
        {
            string extracted = data.Substring(offset, 5).Trim();
            SetIlosuUserNo(extracted);
        }
        offset += 5;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetFiller108(extracted);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetIlosuReportNo(parsedInt);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetFiller109(extracted);
        }
        offset += 4;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetIlosuReportFileAsString();
    }
    // Set<>String Override function
    public void SetIlosuReportFile(string value)
    {
        SetIlosuReportFileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetIlosuUserNo()
    {
        return _IlosuUserNo;
    }
    
    // Standard Setter
    public void SetIlosuUserNo(string value)
    {
        _IlosuUserNo = value;
    }
    
    // Get<>AsString()
    public string GetIlosuUserNoAsString()
    {
        return _IlosuUserNo.PadRight(5);
    }
    
    // Set<>AsString()
    public void SetIlosuUserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _IlosuUserNo = value;
    }
    
    // Standard Getter
    public string GetFiller108()
    {
        return _Filler108;
    }
    
    // Standard Setter
    public void SetFiller108(string value)
    {
        _Filler108 = value;
    }
    
    // Get<>AsString()
    public string GetFiller108AsString()
    {
        return _Filler108.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetFiller108AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler108 = value;
    }
    
    // Standard Getter
    public int GetIlosuReportNo()
    {
        return _IlosuReportNo;
    }
    
    // Standard Setter
    public void SetIlosuReportNo(int value)
    {
        _IlosuReportNo = value;
    }
    
    // Get<>AsString()
    public string GetIlosuReportNoAsString()
    {
        return _IlosuReportNo.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetIlosuReportNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _IlosuReportNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller109()
    {
        return _Filler109;
    }
    
    // Standard Setter
    public void SetFiller109(string value)
    {
        _Filler109 = value;
    }
    
    // Get<>AsString()
    public string GetFiller109AsString()
    {
        return _Filler109.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetFiller109AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler109 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}