using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D5File Data Structure

public class D5File
{
    private static int _size = 12;
    // [DEBUG] Class: D5File, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler159, is_external=, is_static_class=False, static_prefix=
    private string _Filler159 ="$";
    
    
    
    
    // [DEBUG] Field: D5UserNo, is_external=, is_static_class=False, static_prefix=
    private int _D5UserNo =0;
    
    
    
    
    // [DEBUG] Field: Filler160, is_external=, is_static_class=False, static_prefix=
    private string _Filler160 ="R04.REP";
    
    
    
    
    
    // Serialization methods
    public string GetD5FileAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler159.PadRight(1));
        result.Append(_D5UserNo.ToString().PadLeft(4, '0'));
        result.Append(_Filler160.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD5FileAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetFiller159(extracted);
        }
        offset += 1;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD5UserNo(parsedInt);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetFiller160(extracted);
        }
        offset += 7;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD5FileAsString();
    }
    // Set<>String Override function
    public void SetD5File(string value)
    {
        SetD5FileAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller159()
    {
        return _Filler159;
    }
    
    // Standard Setter
    public void SetFiller159(string value)
    {
        _Filler159 = value;
    }
    
    // Get<>AsString()
    public string GetFiller159AsString()
    {
        return _Filler159.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetFiller159AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler159 = value;
    }
    
    // Standard Getter
    public int GetD5UserNo()
    {
        return _D5UserNo;
    }
    
    // Standard Setter
    public void SetD5UserNo(int value)
    {
        _D5UserNo = value;
    }
    
    // Get<>AsString()
    public string GetD5UserNoAsString()
    {
        return _D5UserNo.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD5UserNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D5UserNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller160()
    {
        return _Filler160;
    }
    
    // Standard Setter
    public void SetFiller160(string value)
    {
        _Filler160 = value;
    }
    
    // Get<>AsString()
    public string GetFiller160AsString()
    {
        return _Filler160.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetFiller160AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler160 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}