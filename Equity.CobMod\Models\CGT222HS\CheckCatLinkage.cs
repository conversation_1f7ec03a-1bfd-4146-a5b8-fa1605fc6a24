using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// DTO class representing CheckCatLinkage Data Structure

public class CheckCatLinkage
{
    private static int _size = 50;
    // [DEBUG] Class: CheckCatLinkage, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: CheckCatTransactionId, is_external=, is_static_class=False, static_prefix=
    private int _CheckCatTransactionId =0;
    
    
    
    
    // [DEBUG] Field: CheckCatString, is_external=, is_static_class=False, static_prefix=
    private CheckCatString _CheckCatString = new CheckCatString();
    
    
    
    
    
    // Serialization methods
    public string GetCheckCatLinkageAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_CheckCatTransactionId.ToString().PadLeft(9, '0'));
        result.Append(_CheckCatString.GetCheckCatStringAsString());
        
        return result.ToString();
    }
    
    public void SetCheckCatLinkageAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 9 <= data.Length)
        {
            string extracted = data.Substring(offset, 9).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCheckCatTransactionId(parsedInt);
        }
        offset += 9;
        if (offset + 41 <= data.Length)
        {
            _CheckCatString.SetCheckCatStringAsString(data.Substring(offset, 41));
        }
        else
        {
            _CheckCatString.SetCheckCatStringAsString(data.Substring(offset));
        }
        offset += 41;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetCheckCatLinkageAsString();
    }
    // Set<>String Override function
    public void SetCheckCatLinkage(string value)
    {
        SetCheckCatLinkageAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCheckCatTransactionId()
    {
        return _CheckCatTransactionId;
    }
    
    // Standard Setter
    public void SetCheckCatTransactionId(int value)
    {
        _CheckCatTransactionId = value;
    }
    
    // Get<>AsString()
    public string GetCheckCatTransactionIdAsString()
    {
        return _CheckCatTransactionId.ToString().PadLeft(9, '0');
    }
    
    // Set<>AsString()
    public void SetCheckCatTransactionIdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _CheckCatTransactionId = parsed;
    }
    
    // Standard Getter
    public CheckCatString GetCheckCatString()
    {
        return _CheckCatString;
    }
    
    // Standard Setter
    public void SetCheckCatString(CheckCatString value)
    {
        _CheckCatString = value;
    }
    
    // Get<>AsString()
    public string GetCheckCatStringAsString()
    {
        return _CheckCatString != null ? _CheckCatString.GetCheckCatStringAsString() : "";
    }
    
    // Set<>AsString()
    public void SetCheckCatStringAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_CheckCatString == null)
        {
            _CheckCatString = new CheckCatString();
        }
        _CheckCatString.SetCheckCatStringAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetCheckCatString(string value)
    {
        _CheckCatString.SetCheckCatStringAsString(value);
    }
    // Nested Class: CheckCatString
    public class CheckCatString
    {
        private static int _size = 41;
        
        // Fields in the class
        
        
        // [DEBUG] Field: CheckCatStatus, is_external=, is_static_class=False, static_prefix=
        private int _CheckCatStatus =0;
        
        
        // 88-level condition checks for CheckCatStatus
        public bool IsCheckCatNotUserDefined()
        {
            if (this._CheckCatStatus == 0) return true;
            return false;
        }
        public bool IsCheckCatUserDefined()
        {
            if (this._CheckCatStatus == 1) return true;
            return false;
        }
        public bool IsCheckCatToGetDscr()
        {
            if (this._CheckCatStatus == 2) return true;
            return false;
        }
        
        
        // [DEBUG] Field: CheckCatCategoryCode, is_external=, is_static_class=False, static_prefix=
        private string _CheckCatCategoryCode ="";
        
        
        
        
        // [DEBUG] Field: CheckCatCategoryDesc, is_external=, is_static_class=False, static_prefix=
        private string _CheckCatCategoryDesc ="";
        
        
        
        
        // [DEBUG] Field: CheckCatParentCategoryCode, is_external=, is_static_class=False, static_prefix=
        private string _CheckCatParentCategoryCode ="";
        
        
        
        
    public CheckCatString() {}
    
    public CheckCatString(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetCheckCatStatus(int.Parse(data.Substring(offset, 1).Trim()));
        offset += 1;
        SetCheckCatCategoryCode(data.Substring(offset, 0).Trim());
        offset += 0;
        SetCheckCatCategoryDesc(data.Substring(offset, 40).Trim());
        offset += 40;
        SetCheckCatParentCategoryCode(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetCheckCatStringAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_CheckCatStatus.ToString().PadLeft(1, '0'));
        result.Append(_CheckCatCategoryCode.PadRight(0));
        result.Append(_CheckCatCategoryDesc.PadRight(40));
        result.Append(_CheckCatParentCategoryCode.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetCheckCatStringAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetCheckCatStatus(parsedInt);
        }
        offset += 1;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCheckCatCategoryCode(extracted);
        }
        offset += 0;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetCheckCatCategoryDesc(extracted);
        }
        offset += 40;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetCheckCatParentCategoryCode(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetCheckCatStatus()
    {
        return _CheckCatStatus;
    }
    
    // Standard Setter
    public void SetCheckCatStatus(int value)
    {
        _CheckCatStatus = value;
    }
    
    // Get<>AsString()
    public string GetCheckCatStatusAsString()
    {
        return _CheckCatStatus.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetCheckCatStatusAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _CheckCatStatus = parsed;
    }
    
    // Standard Getter
    public string GetCheckCatCategoryCode()
    {
        return _CheckCatCategoryCode;
    }
    
    // Standard Setter
    public void SetCheckCatCategoryCode(string value)
    {
        _CheckCatCategoryCode = value;
    }
    
    // Get<>AsString()
    public string GetCheckCatCategoryCodeAsString()
    {
        return _CheckCatCategoryCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCheckCatCategoryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CheckCatCategoryCode = value;
    }
    
    // Standard Getter
    public string GetCheckCatCategoryDesc()
    {
        return _CheckCatCategoryDesc;
    }
    
    // Standard Setter
    public void SetCheckCatCategoryDesc(string value)
    {
        _CheckCatCategoryDesc = value;
    }
    
    // Get<>AsString()
    public string GetCheckCatCategoryDescAsString()
    {
        return _CheckCatCategoryDesc.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetCheckCatCategoryDescAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CheckCatCategoryDesc = value;
    }
    
    // Standard Getter
    public string GetCheckCatParentCategoryCode()
    {
        return _CheckCatParentCategoryCode;
    }
    
    // Standard Setter
    public void SetCheckCatParentCategoryCode(string value)
    {
        _CheckCatParentCategoryCode = value;
    }
    
    // Get<>AsString()
    public string GetCheckCatParentCategoryCodeAsString()
    {
        return _CheckCatParentCategoryCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetCheckCatParentCategoryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _CheckCatParentCategoryCode = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}