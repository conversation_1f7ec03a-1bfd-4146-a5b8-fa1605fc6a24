using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing WScheduleCounters Data Structure

public class WScheduleCounters
{
    private static int _size = 45;
    // [DEBUG] Class: WScheduleCounters, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WD19Count, is_external=, is_static_class=False, static_prefix=
    private int _WD19Count =0;
    
    
    
    
    // [DEBUG] Field: WD20Count, is_external=, is_static_class=False, static_prefix=
    private int _WD20Count =0;
    
    
    
    
    // [DEBUG] Field: WD81Count, is_external=, is_static_class=False, static_prefix=
    private int _WD81Count =0;
    
    
    
    
    // [DEBUG] Field: WD98Count, is_external=, is_static_class=False, static_prefix=
    private int _WD98Count =0;
    
    
    
    
    // [DEBUG] Field: WD101Count, is_external=, is_static_class=False, static_prefix=
    private int _WD101Count =0;
    
    
    
    
    
    // Serialization methods
    public string GetWScheduleCountersAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WD19Count.ToString().PadLeft(9, '0'));
        result.Append(_WD20Count.ToString().PadLeft(9, '0'));
        result.Append(_WD81Count.ToString().PadLeft(9, '0'));
        result.Append(_WD98Count.ToString().PadLeft(9, '0'));
        result.Append(_WD101Count.ToString().PadLeft(9, '0'));
        
        return result.ToString();
    }
    
    public void SetWScheduleCountersAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 9 <= data.Length)
        {
            string extracted = data.Substring(offset, 9).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWd19Count(parsedInt);
        }
        offset += 9;
        if (offset + 9 <= data.Length)
        {
            string extracted = data.Substring(offset, 9).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWd20Count(parsedInt);
        }
        offset += 9;
        if (offset + 9 <= data.Length)
        {
            string extracted = data.Substring(offset, 9).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWd81Count(parsedInt);
        }
        offset += 9;
        if (offset + 9 <= data.Length)
        {
            string extracted = data.Substring(offset, 9).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWd98Count(parsedInt);
        }
        offset += 9;
        if (offset + 9 <= data.Length)
        {
            string extracted = data.Substring(offset, 9).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWd101Count(parsedInt);
        }
        offset += 9;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWScheduleCountersAsString();
    }
    // Set<>String Override function
    public void SetWScheduleCounters(string value)
    {
        SetWScheduleCountersAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetWd19Count()
    {
        return _WD19Count;
    }
    
    // Standard Setter
    public void SetWd19Count(int value)
    {
        _WD19Count = value;
    }
    
    // Get<>AsString()
    public string GetWd19CountAsString()
    {
        return _WD19Count.ToString().PadLeft(9, '0');
    }
    
    // Set<>AsString()
    public void SetWd19CountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WD19Count = parsed;
    }
    
    // Standard Getter
    public int GetWd20Count()
    {
        return _WD20Count;
    }
    
    // Standard Setter
    public void SetWd20Count(int value)
    {
        _WD20Count = value;
    }
    
    // Get<>AsString()
    public string GetWd20CountAsString()
    {
        return _WD20Count.ToString().PadLeft(9, '0');
    }
    
    // Set<>AsString()
    public void SetWd20CountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WD20Count = parsed;
    }
    
    // Standard Getter
    public int GetWd81Count()
    {
        return _WD81Count;
    }
    
    // Standard Setter
    public void SetWd81Count(int value)
    {
        _WD81Count = value;
    }
    
    // Get<>AsString()
    public string GetWd81CountAsString()
    {
        return _WD81Count.ToString().PadLeft(9, '0');
    }
    
    // Set<>AsString()
    public void SetWd81CountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WD81Count = parsed;
    }
    
    // Standard Getter
    public int GetWd98Count()
    {
        return _WD98Count;
    }
    
    // Standard Setter
    public void SetWd98Count(int value)
    {
        _WD98Count = value;
    }
    
    // Get<>AsString()
    public string GetWd98CountAsString()
    {
        return _WD98Count.ToString().PadLeft(9, '0');
    }
    
    // Set<>AsString()
    public void SetWd98CountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WD98Count = parsed;
    }
    
    // Standard Getter
    public int GetWd101Count()
    {
        return _WD101Count;
    }
    
    // Standard Setter
    public void SetWd101Count(int value)
    {
        _WD101Count = value;
    }
    
    // Get<>AsString()
    public string GetWd101CountAsString()
    {
        return _WD101Count.ToString().PadLeft(9, '0');
    }
    
    // Set<>AsString()
    public void SetWd101CountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WD101Count = parsed;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}