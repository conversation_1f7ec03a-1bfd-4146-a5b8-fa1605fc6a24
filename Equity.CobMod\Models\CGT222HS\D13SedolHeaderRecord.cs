using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// DTO class representing D13SedolHeaderRecord Data Structure

public class D13SedolHeaderRecord
{
    private static int _size = 495;
    // [DEBUG] Class: D13SedolHeaderRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D131Key, is_external=, is_static_class=False, static_prefix=
    private D131Key _D131Key = new D131Key();
    
    
    
    
    // [DEBUG] Field: D131TransactionCategory, is_external=, is_static_class=False, static_prefix=
    private string _D131TransactionCategory ="";
    
    
    
    
    // [DEBUG] Field: D131DateTimeStamp, is_external=, is_static_class=False, static_prefix=
    private D131DateTimeStamp _D131DateTimeStamp = new D131DateTimeStamp();
    
    
    
    
    // [DEBUG] Field: Filler28, is_external=, is_static_class=False, static_prefix=
    private Filler28 _Filler28 = new Filler28();
    
    
    
    
    // [DEBUG] Field: D131CalcFlag, is_external=, is_static_class=False, static_prefix=
    private string _D131CalcFlag ="";
    
    
    
    
    // [DEBUG] Field: D131RCalcPeriodEndDate, is_external=, is_static_class=False, static_prefix=
    private D131RCalcPeriodEndDate _D131RCalcPeriodEndDate = new D131RCalcPeriodEndDate();
    
    
    
    
    // [DEBUG] Field: D131UCalcPeriodEndDate, is_external=, is_static_class=False, static_prefix=
    private D131UCalcPeriodEndDate _D131UCalcPeriodEndDate = new D131UCalcPeriodEndDate();
    
    
    
    
    // [DEBUG] Field: D131SpareDate, is_external=, is_static_class=False, static_prefix=
    private D131SpareDate _D131SpareDate = new D131SpareDate();
    
    
    
    
    // [DEBUG] Field: D131SecuritySortCode, is_external=, is_static_class=False, static_prefix=
    private string _D131SecuritySortCode ="";
    
    
    
    
    // [DEBUG] Field: D131CountryCode, is_external=, is_static_class=False, static_prefix=
    private string _D131CountryCode ="";
    
    
    
    
    // [DEBUG] Field: D131MainGroupCode, is_external=, is_static_class=False, static_prefix=
    private D131MainGroupCode _D131MainGroupCode = new D131MainGroupCode();
    
    
    
    
    // [DEBUG] Field: D131IndustrialClass, is_external=, is_static_class=False, static_prefix=
    private int _D131IndustrialClass =0;
    
    
    
    
    // [DEBUG] Field: D131SecurityType, is_external=, is_static_class=False, static_prefix=
    private string _D131SecurityType ="";
    
    
    // 88-level condition checks for D131SecurityType
    public bool IsD131SecurityOther()
    {
        if (this._D131SecurityType == "'A'") return true;
        return false;
    }
    public bool IsD131SecurityGilts()
    {
        if (this._D131SecurityType == "'B'") return true;
        return false;
    }
    public bool IsD131CorporateBonds()
    {
        if (this._D131SecurityType == "'C'") return true;
        return false;
    }
    public bool IsD131FixedInterest()
    {
        if (this._D131SecurityType == "'F'") return true;
        return false;
    }
    public bool IsD131Privatisation()
    {
        if (this._D131SecurityType == "'P'") return true;
        return false;
    }
    public bool IsD131SecurityTaxExemptGilt()
    {
        if (this._D131SecurityType == "'X'") return true;
        return false;
    }
    public bool IsD131RelevantSecurities()
    {
        if (this._D131SecurityType == "'B'") return true;
        if (this._D131SecurityType == "'C'") return true;
        if (this._D131SecurityType == "'X'") return true;
        if (this._D131SecurityType == "'F'") return true;
        return false;
    }
    public bool IsD131UntaxableFa89()
    {
        if (this._D131SecurityType == "'E'") return true;
        return false;
    }
    public bool IsD131Gilts()
    {
        if (this._D131SecurityType == "'B'") return true;
        if (this._D131SecurityType == "'X'") return true;
        return false;
    }
    public bool IsD131IntegralUnits()
    {
        if (this._D131SecurityType == "'A'") return true;
        if (this._D131SecurityType == "'P'") return true;
        return false;
    }
    public bool IsD131RelevantFa90()
    {
        if (this._D131SecurityType == "'U'") return true;
        if (this._D131SecurityType == "'O'") return true;
        return false;
    }
    public bool IsD131OffshoreFund()
    {
        if (this._D131SecurityType == "'O'") return true;
        return false;
    }
    public bool IsD131Bond()
    {
        if (this._D131SecurityType == "'B'") return true;
        if (this._D131SecurityType == "'C'") return true;
        if (this._D131SecurityType == "'E'") return true;
        if (this._D131SecurityType == "'F'") return true;
        if (this._D131SecurityType == "'O'") return true;
        if (this._D131SecurityType == "'U'") return true;
        if (this._D131SecurityType == "'X'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D131CurrentMarketPrice, is_external=, is_static_class=False, static_prefix=
    private decimal _D131CurrentMarketPrice =0;
    
    
    
    
    // [DEBUG] Field: D131PricePctIndicator, is_external=, is_static_class=False, static_prefix=
    private string _D131PricePctIndicator ="";
    
    
    
    
    // [DEBUG] Field: D131BfAccountingValue, is_external=, is_static_class=False, static_prefix=
    private decimal _D131BfAccountingValue =0;
    
    
    
    
    // [DEBUG] Field: D131AccountingValueYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D131AccountingValueYtd =0;
    
    
    
    
    // [DEBUG] Field: D131ProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D131ProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: D131IssuersName, is_external=, is_static_class=False, static_prefix=
    private string _D131IssuersName ="";
    
    
    
    
    // [DEBUG] Field: D131StockDescription, is_external=, is_static_class=False, static_prefix=
    private string _D131StockDescription ="";
    
    
    
    
    // [DEBUG] Field: D131IssuedCapital, is_external=, is_static_class=False, static_prefix=
    private decimal _D131IssuedCapital =0;
    
    
    
    
    // [DEBUG] Field: D131MovementIndicator, is_external=, is_static_class=False, static_prefix=
    private int _D131MovementIndicator =0;
    
    
    
    
    // [DEBUG] Field: D131Bf2PctHoldingDateR, is_external=, is_static_class=False, static_prefix=
    private D131Bf2PctHoldingDateR _D131Bf2PctHoldingDateR = new D131Bf2PctHoldingDateR();
    
    
    
    
    // [DEBUG] Field: D131Bf2PctHoldingDate, is_external=, is_static_class=False, static_prefix=
    private int _D131Bf2PctHoldingDate =0;
    
    
    
    
    // [DEBUG] Field: D1312PctHoldingDateYtdR, is_external=, is_static_class=False, static_prefix=
    private D1312PctHoldingDateYtdR _D1312PctHoldingDateYtdR = new D1312PctHoldingDateYtdR();
    
    
    
    
    // [DEBUG] Field: D1312PctHoldingDateYtd, is_external=, is_static_class=False, static_prefix=
    private int _D1312PctHoldingDateYtd =0;
    
    
    // 88-level condition checks for D1312PctHoldingDateYtd
    public bool IsD1312PctDateYtdNotSet()
    {
        if (this._D1312PctHoldingDateYtd == 999999) return true;
        return false;
    }
    public bool IsD1312PctDateYtdSet()
    {
        if (this._D1312PctHoldingDateYtd >= 101 && this._D1312PctHoldingDateYtd <= 991231) return true;
        return false;
    }
    
    
    // [DEBUG] Field: D131SecurityIndicator, is_external=, is_static_class=False, static_prefix=
    private string _D131SecurityIndicator ="";
    
    
    // 88-level condition checks for D131SecurityIndicator
    public bool IsD131QuotedSecurity()
    {
        if (this._D131SecurityIndicator == "'0'") return true;
        if (this._D131SecurityIndicator == "LOW-VALUES") return true;
        return false;
    }
    public bool IsD131UnquotedSecurity()
    {
        if (this._D131SecurityIndicator == "'1'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D131TotalUnitsYtd, is_external=, is_static_class=False, static_prefix=
    private decimal _D131TotalUnitsYtd =0;
    
    
    
    
    // [DEBUG] Field: D131CapitalGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D131CapitalGainLoss =0;
    
    
    
    
    // [DEBUG] Field: D131UnrealGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D131UnrealGainLoss =0;
    
    
    
    
    // [DEBUG] Field: D131UnrealProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D131UnrealProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: D131OffshoreClass, is_external=, is_static_class=False, static_prefix=
    private string _D131OffshoreClass ="";
    
    
    
    
    // [DEBUG] Field: D131OverRideFlag, is_external=, is_static_class=False, static_prefix=
    private string _D131OverRideFlag ="";
    
    
    
    
    // [DEBUG] Field: D131DateOfIssueR, is_external=, is_static_class=False, static_prefix=
    private D131DateOfIssueR _D131DateOfIssueR = new D131DateOfIssueR();
    
    
    
    
    // [DEBUG] Field: D131DateOfIssue, is_external=, is_static_class=False, static_prefix=
    private int _D131DateOfIssue =0;
    
    
    
    
    // [DEBUG] Field: D131IndexFromIssue, is_external=, is_static_class=False, static_prefix=
    private string _D131IndexFromIssue ="";
    
    
    
    
    // [DEBUG] Field: D131DeemedGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D131DeemedGainLoss =0;
    
    
    
    
    // [DEBUG] Field: D131DeemedProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D131DeemedProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: D131HoldingFlag, is_external=, is_static_class=False, static_prefix=
    private string _D131HoldingFlag ="";
    
    
    
    
    // [DEBUG] Field: D131BondOverride, is_external=, is_static_class=False, static_prefix=
    private string _D131BondOverride ="";
    
    
    
    
    // [DEBUG] Field: D131LrBasis, is_external=, is_static_class=False, static_prefix=
    private string _D131LrBasis ="";
    
    
    // 88-level condition checks for D131LrBasis
    public bool IsD131BondProcessing()
    {
        if (this._D131LrBasis == "'A'") return true;
        if (this._D131LrBasis == "'M'") return true;
        return false;
    }
    public bool IsD131MarkToMarket()
    {
        if (this._D131LrBasis == "'M'") return true;
        return false;
    }
    public bool IsD131Accrual()
    {
        if (this._D131LrBasis == "'A'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D131BondMaturityDateR, is_external=, is_static_class=False, static_prefix=
    private D131BondMaturityDateR _D131BondMaturityDateR = new D131BondMaturityDateR();
    
    
    
    
    // [DEBUG] Field: D131BondMaturityDate, is_external=, is_static_class=False, static_prefix=
    private string _D131BondMaturityDate ="";
    
    
    
    
    // [DEBUG] Field: D131BondParValueX, is_external=, is_static_class=False, static_prefix=
    private string _D131BondParValueX ="";
    
    
    
    
    // [DEBUG] Field: D131BondParValue, is_external=, is_static_class=False, static_prefix=
    private decimal _D131BondParValue =0;
    
    
    
    
    // [DEBUG] Field: D131BondGainOverride, is_external=, is_static_class=False, static_prefix=
    private string _D131BondGainOverride ="";
    
    
    // 88-level condition checks for D131BondGainOverride
    public bool IsD131GainRealised()
    {
        if (this._D131BondGainOverride == "'R'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D131UnrealBondGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D131UnrealBondGainLoss =0;
    
    
    
    
    // [DEBUG] Field: D131UnrealBondProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D131UnrealBondProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: D131AssetUsage, is_external=, is_static_class=False, static_prefix=
    private string _D131AssetUsage ="";
    
    
    
    
    // [DEBUG] Field: D131RealBondGainLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D131RealBondGainLoss =0;
    
    
    
    
    // [DEBUG] Field: D131RealBondProfitLoss, is_external=, is_static_class=False, static_prefix=
    private decimal _D131RealBondProfitLoss =0;
    
    
    
    
    // [DEBUG] Field: Filler29, is_external=, is_static_class=False, static_prefix=
    private string _Filler29 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD13SedolHeaderRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D131Key.GetD131KeyAsString());
        result.Append(_D131TransactionCategory.PadRight(2));
        result.Append(_D131DateTimeStamp.GetD131DateTimeStampAsString());
        result.Append(_Filler28.GetFiller28AsString());
        result.Append(_D131CalcFlag.PadRight(4));
        result.Append(_D131RCalcPeriodEndDate.GetD131RCalcPeriodEndDateAsString());
        result.Append(_D131UCalcPeriodEndDate.GetD131UCalcPeriodEndDateAsString());
        result.Append(_D131SpareDate.GetD131SpareDateAsString());
        result.Append(_D131SecuritySortCode.PadRight(15));
        result.Append(_D131CountryCode.PadRight(3));
        result.Append(_D131MainGroupCode.GetD131MainGroupCodeAsString());
        result.Append(_D131IndustrialClass.ToString().PadLeft(2, '0'));
        result.Append(_D131SecurityType.PadRight(1));
        result.Append(_D131CurrentMarketPrice.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D131PricePctIndicator.PadRight(1));
        result.Append(_D131BfAccountingValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D131AccountingValueYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D131ProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D131IssuersName.PadRight(35));
        result.Append(_D131StockDescription.PadRight(40));
        result.Append(_D131IssuedCapital.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D131MovementIndicator.ToString().PadLeft(1, '0'));
        result.Append(_D131Bf2PctHoldingDateR.GetD131Bf2PctHoldingDateRAsString());
        result.Append(_D131Bf2PctHoldingDate.ToString().PadLeft(6, '0'));
        result.Append(_D1312PctHoldingDateYtdR.GetD1312PctHoldingDateYtdRAsString());
        result.Append(_D1312PctHoldingDateYtd.ToString().PadLeft(6, '0'));
        result.Append(_D131SecurityIndicator.PadRight(1));
        result.Append(_D131TotalUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D131CapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D131UnrealGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D131UnrealProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D131OffshoreClass.PadRight(1));
        result.Append(_D131OverRideFlag.PadRight(1));
        result.Append(_D131DateOfIssueR.GetD131DateOfIssueRAsString());
        result.Append(_D131DateOfIssue.ToString().PadLeft(6, '0'));
        result.Append(_D131IndexFromIssue.PadRight(1));
        result.Append(_D131DeemedGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D131DeemedProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D131HoldingFlag.PadRight(1));
        result.Append(_D131BondOverride.PadRight(1));
        result.Append(_D131LrBasis.PadRight(1));
        result.Append(_D131BondMaturityDateR.GetD131BondMaturityDateRAsString());
        result.Append(_D131BondMaturityDate.PadRight(8));
        result.Append(_D131BondParValueX.PadRight(6));
        result.Append(_D131BondParValue.ToString("F6", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D131BondGainOverride.PadRight(1));
        result.Append(_D131UnrealBondGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D131UnrealBondProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D131AssetUsage.PadRight(0));
        result.Append(_D131RealBondGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_D131RealBondProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler29.PadRight(13));
        
        return result.ToString();
    }
    
    public void SetD13SedolHeaderRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 25 <= data.Length)
        {
            _D131Key.SetD131KeyAsString(data.Substring(offset, 25));
        }
        else
        {
            _D131Key.SetD131KeyAsString(data.Substring(offset));
        }
        offset += 25;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            SetD131TransactionCategory(extracted);
        }
        offset += 2;
        if (offset + 14 <= data.Length)
        {
            _D131DateTimeStamp.SetD131DateTimeStampAsString(data.Substring(offset, 14));
        }
        else
        {
            _D131DateTimeStamp.SetD131DateTimeStampAsString(data.Substring(offset));
        }
        offset += 14;
        if (offset + 14 <= data.Length)
        {
            _Filler28.SetFiller28AsString(data.Substring(offset, 14));
        }
        else
        {
            _Filler28.SetFiller28AsString(data.Substring(offset));
        }
        offset += 14;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD131CalcFlag(extracted);
        }
        offset += 4;
        if (offset + 6 <= data.Length)
        {
            _D131RCalcPeriodEndDate.SetD131RCalcPeriodEndDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _D131RCalcPeriodEndDate.SetD131RCalcPeriodEndDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            _D131UCalcPeriodEndDate.SetD131UCalcPeriodEndDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _D131UCalcPeriodEndDate.SetD131UCalcPeriodEndDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            _D131SpareDate.SetD131SpareDateAsString(data.Substring(offset, 6));
        }
        else
        {
            _D131SpareDate.SetD131SpareDateAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            SetD131SecuritySortCode(extracted);
        }
        offset += 15;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD131CountryCode(extracted);
        }
        offset += 3;
        if (offset + 3 <= data.Length)
        {
            _D131MainGroupCode.SetD131MainGroupCodeAsString(data.Substring(offset, 3));
        }
        else
        {
            _D131MainGroupCode.SetD131MainGroupCodeAsString(data.Substring(offset));
        }
        offset += 3;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD131IndustrialClass(parsedInt);
        }
        offset += 2;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD131SecurityType(extracted);
        }
        offset += 1;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD131CurrentMarketPrice(parsedDec);
        }
        offset += 15;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD131PricePctIndicator(extracted);
        }
        offset += 1;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD131BfAccountingValue(parsedDec);
        }
        offset += 17;
        if (offset + 17 <= data.Length)
        {
            string extracted = data.Substring(offset, 17).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD131AccountingValueYtd(parsedDec);
        }
        offset += 17;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD131ProfitLoss(parsedDec);
        }
        offset += 15;
        if (offset + 35 <= data.Length)
        {
            string extracted = data.Substring(offset, 35).Trim();
            SetD131IssuersName(extracted);
        }
        offset += 35;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD131StockDescription(extracted);
        }
        offset += 40;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD131IssuedCapital(parsedDec);
        }
        offset += 15;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD131MovementIndicator(parsedInt);
        }
        offset += 1;
        if (offset + 6 <= data.Length)
        {
            _D131Bf2PctHoldingDateR.SetD131Bf2PctHoldingDateRAsString(data.Substring(offset, 6));
        }
        else
        {
            _D131Bf2PctHoldingDateR.SetD131Bf2PctHoldingDateRAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD131Bf2PctHoldingDate(parsedInt);
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            _D1312PctHoldingDateYtdR.SetD1312PctHoldingDateYtdRAsString(data.Substring(offset, 6));
        }
        else
        {
            _D1312PctHoldingDateYtdR.SetD1312PctHoldingDateYtdRAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD1312PctHoldingDateYtd(parsedInt);
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD131SecurityIndicator(extracted);
        }
        offset += 1;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD131TotalUnitsYtd(parsedDec);
        }
        offset += 13;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD131CapitalGainLoss(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD131UnrealGainLoss(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD131UnrealProfitLoss(parsedDec);
        }
        offset += 15;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD131OffshoreClass(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD131OverRideFlag(extracted);
        }
        offset += 1;
        if (offset + 6 <= data.Length)
        {
            _D131DateOfIssueR.SetD131DateOfIssueRAsString(data.Substring(offset, 6));
        }
        else
        {
            _D131DateOfIssueR.SetD131DateOfIssueRAsString(data.Substring(offset));
        }
        offset += 6;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD131DateOfIssue(parsedInt);
        }
        offset += 6;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD131IndexFromIssue(extracted);
        }
        offset += 1;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD131DeemedGainLoss(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD131DeemedProfitLoss(parsedDec);
        }
        offset += 15;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD131HoldingFlag(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD131BondOverride(extracted);
        }
        offset += 1;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD131LrBasis(extracted);
        }
        offset += 1;
        if (offset + 8 <= data.Length)
        {
            _D131BondMaturityDateR.SetD131BondMaturityDateRAsString(data.Substring(offset, 8));
        }
        else
        {
            _D131BondMaturityDateR.SetD131BondMaturityDateRAsString(data.Substring(offset));
        }
        offset += 8;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            SetD131BondMaturityDate(extracted);
        }
        offset += 8;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetD131BondParValueX(extracted);
        }
        offset += 6;
        if (offset + 11 <= data.Length)
        {
            string extracted = data.Substring(offset, 11).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD131BondParValue(parsedDec);
        }
        offset += 11;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD131BondGainOverride(extracted);
        }
        offset += 1;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD131UnrealBondGainLoss(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD131UnrealBondProfitLoss(parsedDec);
        }
        offset += 15;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD131AssetUsage(extracted);
        }
        offset += 0;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD131RealBondGainLoss(parsedDec);
        }
        offset += 15;
        if (offset + 15 <= data.Length)
        {
            string extracted = data.Substring(offset, 15).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetD131RealBondProfitLoss(parsedDec);
        }
        offset += 15;
        if (offset + 13 <= data.Length)
        {
            string extracted = data.Substring(offset, 13).Trim();
            SetFiller29(extracted);
        }
        offset += 13;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD13SedolHeaderRecordAsString();
    }
    // Set<>String Override function
    public void SetD13SedolHeaderRecord(string value)
    {
        SetD13SedolHeaderRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D131Key GetD131Key()
    {
        return _D131Key;
    }
    
    // Standard Setter
    public void SetD131Key(D131Key value)
    {
        _D131Key = value;
    }
    
    // Get<>AsString()
    public string GetD131KeyAsString()
    {
        return _D131Key != null ? _D131Key.GetD131KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD131KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D131Key == null)
        {
            _D131Key = new D131Key();
        }
        _D131Key.SetD131KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD131TransactionCategory()
    {
        return _D131TransactionCategory;
    }
    
    // Standard Setter
    public void SetD131TransactionCategory(string value)
    {
        _D131TransactionCategory = value;
    }
    
    // Get<>AsString()
    public string GetD131TransactionCategoryAsString()
    {
        return _D131TransactionCategory.PadRight(2);
    }
    
    // Set<>AsString()
    public void SetD131TransactionCategoryAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131TransactionCategory = value;
    }
    
    // Standard Getter
    public D131DateTimeStamp GetD131DateTimeStamp()
    {
        return _D131DateTimeStamp;
    }
    
    // Standard Setter
    public void SetD131DateTimeStamp(D131DateTimeStamp value)
    {
        _D131DateTimeStamp = value;
    }
    
    // Get<>AsString()
    public string GetD131DateTimeStampAsString()
    {
        return _D131DateTimeStamp != null ? _D131DateTimeStamp.GetD131DateTimeStampAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD131DateTimeStampAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D131DateTimeStamp == null)
        {
            _D131DateTimeStamp = new D131DateTimeStamp();
        }
        _D131DateTimeStamp.SetD131DateTimeStampAsString(value);
    }
    
    // Standard Getter
    public Filler28 GetFiller28()
    {
        return _Filler28;
    }
    
    // Standard Setter
    public void SetFiller28(Filler28 value)
    {
        _Filler28 = value;
    }
    
    // Get<>AsString()
    public string GetFiller28AsString()
    {
        return _Filler28 != null ? _Filler28.GetFiller28AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller28AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler28 == null)
        {
            _Filler28 = new Filler28();
        }
        _Filler28.SetFiller28AsString(value);
    }
    
    // Standard Getter
    public string GetD131CalcFlag()
    {
        return _D131CalcFlag;
    }
    
    // Standard Setter
    public void SetD131CalcFlag(string value)
    {
        _D131CalcFlag = value;
    }
    
    // Get<>AsString()
    public string GetD131CalcFlagAsString()
    {
        return _D131CalcFlag.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD131CalcFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131CalcFlag = value;
    }
    
    // Standard Getter
    public D131RCalcPeriodEndDate GetD131RCalcPeriodEndDate()
    {
        return _D131RCalcPeriodEndDate;
    }
    
    // Standard Setter
    public void SetD131RCalcPeriodEndDate(D131RCalcPeriodEndDate value)
    {
        _D131RCalcPeriodEndDate = value;
    }
    
    // Get<>AsString()
    public string GetD131RCalcPeriodEndDateAsString()
    {
        return _D131RCalcPeriodEndDate != null ? _D131RCalcPeriodEndDate.GetD131RCalcPeriodEndDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD131RCalcPeriodEndDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D131RCalcPeriodEndDate == null)
        {
            _D131RCalcPeriodEndDate = new D131RCalcPeriodEndDate();
        }
        _D131RCalcPeriodEndDate.SetD131RCalcPeriodEndDateAsString(value);
    }
    
    // Standard Getter
    public D131UCalcPeriodEndDate GetD131UCalcPeriodEndDate()
    {
        return _D131UCalcPeriodEndDate;
    }
    
    // Standard Setter
    public void SetD131UCalcPeriodEndDate(D131UCalcPeriodEndDate value)
    {
        _D131UCalcPeriodEndDate = value;
    }
    
    // Get<>AsString()
    public string GetD131UCalcPeriodEndDateAsString()
    {
        return _D131UCalcPeriodEndDate != null ? _D131UCalcPeriodEndDate.GetD131UCalcPeriodEndDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD131UCalcPeriodEndDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D131UCalcPeriodEndDate == null)
        {
            _D131UCalcPeriodEndDate = new D131UCalcPeriodEndDate();
        }
        _D131UCalcPeriodEndDate.SetD131UCalcPeriodEndDateAsString(value);
    }
    
    // Standard Getter
    public D131SpareDate GetD131SpareDate()
    {
        return _D131SpareDate;
    }
    
    // Standard Setter
    public void SetD131SpareDate(D131SpareDate value)
    {
        _D131SpareDate = value;
    }
    
    // Get<>AsString()
    public string GetD131SpareDateAsString()
    {
        return _D131SpareDate != null ? _D131SpareDate.GetD131SpareDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD131SpareDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D131SpareDate == null)
        {
            _D131SpareDate = new D131SpareDate();
        }
        _D131SpareDate.SetD131SpareDateAsString(value);
    }
    
    // Standard Getter
    public string GetD131SecuritySortCode()
    {
        return _D131SecuritySortCode;
    }
    
    // Standard Setter
    public void SetD131SecuritySortCode(string value)
    {
        _D131SecuritySortCode = value;
    }
    
    // Get<>AsString()
    public string GetD131SecuritySortCodeAsString()
    {
        return _D131SecuritySortCode.PadRight(15);
    }
    
    // Set<>AsString()
    public void SetD131SecuritySortCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131SecuritySortCode = value;
    }
    
    // Standard Getter
    public string GetD131CountryCode()
    {
        return _D131CountryCode;
    }
    
    // Standard Setter
    public void SetD131CountryCode(string value)
    {
        _D131CountryCode = value;
    }
    
    // Get<>AsString()
    public string GetD131CountryCodeAsString()
    {
        return _D131CountryCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD131CountryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131CountryCode = value;
    }
    
    // Standard Getter
    public D131MainGroupCode GetD131MainGroupCode()
    {
        return _D131MainGroupCode;
    }
    
    // Standard Setter
    public void SetD131MainGroupCode(D131MainGroupCode value)
    {
        _D131MainGroupCode = value;
    }
    
    // Get<>AsString()
    public string GetD131MainGroupCodeAsString()
    {
        return _D131MainGroupCode != null ? _D131MainGroupCode.GetD131MainGroupCodeAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD131MainGroupCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D131MainGroupCode == null)
        {
            _D131MainGroupCode = new D131MainGroupCode();
        }
        _D131MainGroupCode.SetD131MainGroupCodeAsString(value);
    }
    
    // Standard Getter
    public int GetD131IndustrialClass()
    {
        return _D131IndustrialClass;
    }
    
    // Standard Setter
    public void SetD131IndustrialClass(int value)
    {
        _D131IndustrialClass = value;
    }
    
    // Get<>AsString()
    public string GetD131IndustrialClassAsString()
    {
        return _D131IndustrialClass.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD131IndustrialClassAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D131IndustrialClass = parsed;
    }
    
    // Standard Getter
    public string GetD131SecurityType()
    {
        return _D131SecurityType;
    }
    
    // Standard Setter
    public void SetD131SecurityType(string value)
    {
        _D131SecurityType = value;
    }
    
    // Get<>AsString()
    public string GetD131SecurityTypeAsString()
    {
        return _D131SecurityType.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD131SecurityTypeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131SecurityType = value;
    }
    
    // Standard Getter
    public decimal GetD131CurrentMarketPrice()
    {
        return _D131CurrentMarketPrice;
    }
    
    // Standard Setter
    public void SetD131CurrentMarketPrice(decimal value)
    {
        _D131CurrentMarketPrice = value;
    }
    
    // Get<>AsString()
    public string GetD131CurrentMarketPriceAsString()
    {
        return _D131CurrentMarketPrice.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD131CurrentMarketPriceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D131CurrentMarketPrice = parsed;
    }
    
    // Standard Getter
    public string GetD131PricePctIndicator()
    {
        return _D131PricePctIndicator;
    }
    
    // Standard Setter
    public void SetD131PricePctIndicator(string value)
    {
        _D131PricePctIndicator = value;
    }
    
    // Get<>AsString()
    public string GetD131PricePctIndicatorAsString()
    {
        return _D131PricePctIndicator.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD131PricePctIndicatorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131PricePctIndicator = value;
    }
    
    // Standard Getter
    public decimal GetD131BfAccountingValue()
    {
        return _D131BfAccountingValue;
    }
    
    // Standard Setter
    public void SetD131BfAccountingValue(decimal value)
    {
        _D131BfAccountingValue = value;
    }
    
    // Get<>AsString()
    public string GetD131BfAccountingValueAsString()
    {
        return _D131BfAccountingValue.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD131BfAccountingValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D131BfAccountingValue = parsed;
    }
    
    // Standard Getter
    public decimal GetD131AccountingValueYtd()
    {
        return _D131AccountingValueYtd;
    }
    
    // Standard Setter
    public void SetD131AccountingValueYtd(decimal value)
    {
        _D131AccountingValueYtd = value;
    }
    
    // Get<>AsString()
    public string GetD131AccountingValueYtdAsString()
    {
        return _D131AccountingValueYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD131AccountingValueYtdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D131AccountingValueYtd = parsed;
    }
    
    // Standard Getter
    public decimal GetD131ProfitLoss()
    {
        return _D131ProfitLoss;
    }
    
    // Standard Setter
    public void SetD131ProfitLoss(decimal value)
    {
        _D131ProfitLoss = value;
    }
    
    // Get<>AsString()
    public string GetD131ProfitLossAsString()
    {
        return _D131ProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD131ProfitLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D131ProfitLoss = parsed;
    }
    
    // Standard Getter
    public string GetD131IssuersName()
    {
        return _D131IssuersName;
    }
    
    // Standard Setter
    public void SetD131IssuersName(string value)
    {
        _D131IssuersName = value;
    }
    
    // Get<>AsString()
    public string GetD131IssuersNameAsString()
    {
        return _D131IssuersName.PadRight(35);
    }
    
    // Set<>AsString()
    public void SetD131IssuersNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131IssuersName = value;
    }
    
    // Standard Getter
    public string GetD131StockDescription()
    {
        return _D131StockDescription;
    }
    
    // Standard Setter
    public void SetD131StockDescription(string value)
    {
        _D131StockDescription = value;
    }
    
    // Get<>AsString()
    public string GetD131StockDescriptionAsString()
    {
        return _D131StockDescription.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD131StockDescriptionAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131StockDescription = value;
    }
    
    // Standard Getter
    public decimal GetD131IssuedCapital()
    {
        return _D131IssuedCapital;
    }
    
    // Standard Setter
    public void SetD131IssuedCapital(decimal value)
    {
        _D131IssuedCapital = value;
    }
    
    // Get<>AsString()
    public string GetD131IssuedCapitalAsString()
    {
        return _D131IssuedCapital.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD131IssuedCapitalAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D131IssuedCapital = parsed;
    }
    
    // Standard Getter
    public int GetD131MovementIndicator()
    {
        return _D131MovementIndicator;
    }
    
    // Standard Setter
    public void SetD131MovementIndicator(int value)
    {
        _D131MovementIndicator = value;
    }
    
    // Get<>AsString()
    public string GetD131MovementIndicatorAsString()
    {
        return _D131MovementIndicator.ToString().PadLeft(1, '0');
    }
    
    // Set<>AsString()
    public void SetD131MovementIndicatorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D131MovementIndicator = parsed;
    }
    
    // Standard Getter
    public D131Bf2PctHoldingDateR GetD131Bf2PctHoldingDateR()
    {
        return _D131Bf2PctHoldingDateR;
    }
    
    // Standard Setter
    public void SetD131Bf2PctHoldingDateR(D131Bf2PctHoldingDateR value)
    {
        _D131Bf2PctHoldingDateR = value;
    }
    
    // Get<>AsString()
    public string GetD131Bf2PctHoldingDateRAsString()
    {
        return _D131Bf2PctHoldingDateR != null ? _D131Bf2PctHoldingDateR.GetD131Bf2PctHoldingDateRAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD131Bf2PctHoldingDateRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D131Bf2PctHoldingDateR == null)
        {
            _D131Bf2PctHoldingDateR = new D131Bf2PctHoldingDateR();
        }
        _D131Bf2PctHoldingDateR.SetD131Bf2PctHoldingDateRAsString(value);
    }
    
    // Standard Getter
    public int GetD131Bf2PctHoldingDate()
    {
        return _D131Bf2PctHoldingDate;
    }
    
    // Standard Setter
    public void SetD131Bf2PctHoldingDate(int value)
    {
        _D131Bf2PctHoldingDate = value;
    }
    
    // Get<>AsString()
    public string GetD131Bf2PctHoldingDateAsString()
    {
        return _D131Bf2PctHoldingDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD131Bf2PctHoldingDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D131Bf2PctHoldingDate = parsed;
    }
    
    // Standard Getter
    public D1312PctHoldingDateYtdR GetD1312PctHoldingDateYtdR()
    {
        return _D1312PctHoldingDateYtdR;
    }
    
    // Standard Setter
    public void SetD1312PctHoldingDateYtdR(D1312PctHoldingDateYtdR value)
    {
        _D1312PctHoldingDateYtdR = value;
    }
    
    // Get<>AsString()
    public string GetD1312PctHoldingDateYtdRAsString()
    {
        return _D1312PctHoldingDateYtdR != null ? _D1312PctHoldingDateYtdR.GetD1312PctHoldingDateYtdRAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD1312PctHoldingDateYtdRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D1312PctHoldingDateYtdR == null)
        {
            _D1312PctHoldingDateYtdR = new D1312PctHoldingDateYtdR();
        }
        _D1312PctHoldingDateYtdR.SetD1312PctHoldingDateYtdRAsString(value);
    }
    
    // Standard Getter
    public int GetD1312PctHoldingDateYtd()
    {
        return _D1312PctHoldingDateYtd;
    }
    
    // Standard Setter
    public void SetD1312PctHoldingDateYtd(int value)
    {
        _D1312PctHoldingDateYtd = value;
    }
    
    // Get<>AsString()
    public string GetD1312PctHoldingDateYtdAsString()
    {
        return _D1312PctHoldingDateYtd.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD1312PctHoldingDateYtdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D1312PctHoldingDateYtd = parsed;
    }
    
    // Standard Getter
    public string GetD131SecurityIndicator()
    {
        return _D131SecurityIndicator;
    }
    
    // Standard Setter
    public void SetD131SecurityIndicator(string value)
    {
        _D131SecurityIndicator = value;
    }
    
    // Get<>AsString()
    public string GetD131SecurityIndicatorAsString()
    {
        return _D131SecurityIndicator.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD131SecurityIndicatorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131SecurityIndicator = value;
    }
    
    // Standard Getter
    public decimal GetD131TotalUnitsYtd()
    {
        return _D131TotalUnitsYtd;
    }
    
    // Standard Setter
    public void SetD131TotalUnitsYtd(decimal value)
    {
        _D131TotalUnitsYtd = value;
    }
    
    // Get<>AsString()
    public string GetD131TotalUnitsYtdAsString()
    {
        return _D131TotalUnitsYtd.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD131TotalUnitsYtdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D131TotalUnitsYtd = parsed;
    }
    
    // Standard Getter
    public decimal GetD131CapitalGainLoss()
    {
        return _D131CapitalGainLoss;
    }
    
    // Standard Setter
    public void SetD131CapitalGainLoss(decimal value)
    {
        _D131CapitalGainLoss = value;
    }
    
    // Get<>AsString()
    public string GetD131CapitalGainLossAsString()
    {
        return _D131CapitalGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD131CapitalGainLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D131CapitalGainLoss = parsed;
    }
    
    // Standard Getter
    public decimal GetD131UnrealGainLoss()
    {
        return _D131UnrealGainLoss;
    }
    
    // Standard Setter
    public void SetD131UnrealGainLoss(decimal value)
    {
        _D131UnrealGainLoss = value;
    }
    
    // Get<>AsString()
    public string GetD131UnrealGainLossAsString()
    {
        return _D131UnrealGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD131UnrealGainLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D131UnrealGainLoss = parsed;
    }
    
    // Standard Getter
    public decimal GetD131UnrealProfitLoss()
    {
        return _D131UnrealProfitLoss;
    }
    
    // Standard Setter
    public void SetD131UnrealProfitLoss(decimal value)
    {
        _D131UnrealProfitLoss = value;
    }
    
    // Get<>AsString()
    public string GetD131UnrealProfitLossAsString()
    {
        return _D131UnrealProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD131UnrealProfitLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D131UnrealProfitLoss = parsed;
    }
    
    // Standard Getter
    public string GetD131OffshoreClass()
    {
        return _D131OffshoreClass;
    }
    
    // Standard Setter
    public void SetD131OffshoreClass(string value)
    {
        _D131OffshoreClass = value;
    }
    
    // Get<>AsString()
    public string GetD131OffshoreClassAsString()
    {
        return _D131OffshoreClass.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD131OffshoreClassAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131OffshoreClass = value;
    }
    
    // Standard Getter
    public string GetD131OverRideFlag()
    {
        return _D131OverRideFlag;
    }
    
    // Standard Setter
    public void SetD131OverRideFlag(string value)
    {
        _D131OverRideFlag = value;
    }
    
    // Get<>AsString()
    public string GetD131OverRideFlagAsString()
    {
        return _D131OverRideFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD131OverRideFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131OverRideFlag = value;
    }
    
    // Standard Getter
    public D131DateOfIssueR GetD131DateOfIssueR()
    {
        return _D131DateOfIssueR;
    }
    
    // Standard Setter
    public void SetD131DateOfIssueR(D131DateOfIssueR value)
    {
        _D131DateOfIssueR = value;
    }
    
    // Get<>AsString()
    public string GetD131DateOfIssueRAsString()
    {
        return _D131DateOfIssueR != null ? _D131DateOfIssueR.GetD131DateOfIssueRAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD131DateOfIssueRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D131DateOfIssueR == null)
        {
            _D131DateOfIssueR = new D131DateOfIssueR();
        }
        _D131DateOfIssueR.SetD131DateOfIssueRAsString(value);
    }
    
    // Standard Getter
    public int GetD131DateOfIssue()
    {
        return _D131DateOfIssue;
    }
    
    // Standard Setter
    public void SetD131DateOfIssue(int value)
    {
        _D131DateOfIssue = value;
    }
    
    // Get<>AsString()
    public string GetD131DateOfIssueAsString()
    {
        return _D131DateOfIssue.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetD131DateOfIssueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D131DateOfIssue = parsed;
    }
    
    // Standard Getter
    public string GetD131IndexFromIssue()
    {
        return _D131IndexFromIssue;
    }
    
    // Standard Setter
    public void SetD131IndexFromIssue(string value)
    {
        _D131IndexFromIssue = value;
    }
    
    // Get<>AsString()
    public string GetD131IndexFromIssueAsString()
    {
        return _D131IndexFromIssue.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD131IndexFromIssueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131IndexFromIssue = value;
    }
    
    // Standard Getter
    public decimal GetD131DeemedGainLoss()
    {
        return _D131DeemedGainLoss;
    }
    
    // Standard Setter
    public void SetD131DeemedGainLoss(decimal value)
    {
        _D131DeemedGainLoss = value;
    }
    
    // Get<>AsString()
    public string GetD131DeemedGainLossAsString()
    {
        return _D131DeemedGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD131DeemedGainLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D131DeemedGainLoss = parsed;
    }
    
    // Standard Getter
    public decimal GetD131DeemedProfitLoss()
    {
        return _D131DeemedProfitLoss;
    }
    
    // Standard Setter
    public void SetD131DeemedProfitLoss(decimal value)
    {
        _D131DeemedProfitLoss = value;
    }
    
    // Get<>AsString()
    public string GetD131DeemedProfitLossAsString()
    {
        return _D131DeemedProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD131DeemedProfitLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D131DeemedProfitLoss = parsed;
    }
    
    // Standard Getter
    public string GetD131HoldingFlag()
    {
        return _D131HoldingFlag;
    }
    
    // Standard Setter
    public void SetD131HoldingFlag(string value)
    {
        _D131HoldingFlag = value;
    }
    
    // Get<>AsString()
    public string GetD131HoldingFlagAsString()
    {
        return _D131HoldingFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD131HoldingFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131HoldingFlag = value;
    }
    
    // Standard Getter
    public string GetD131BondOverride()
    {
        return _D131BondOverride;
    }
    
    // Standard Setter
    public void SetD131BondOverride(string value)
    {
        _D131BondOverride = value;
    }
    
    // Get<>AsString()
    public string GetD131BondOverrideAsString()
    {
        return _D131BondOverride.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD131BondOverrideAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131BondOverride = value;
    }
    
    // Standard Getter
    public string GetD131LrBasis()
    {
        return _D131LrBasis;
    }
    
    // Standard Setter
    public void SetD131LrBasis(string value)
    {
        _D131LrBasis = value;
    }
    
    // Get<>AsString()
    public string GetD131LrBasisAsString()
    {
        return _D131LrBasis.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD131LrBasisAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131LrBasis = value;
    }
    
    // Standard Getter
    public D131BondMaturityDateR GetD131BondMaturityDateR()
    {
        return _D131BondMaturityDateR;
    }
    
    // Standard Setter
    public void SetD131BondMaturityDateR(D131BondMaturityDateR value)
    {
        _D131BondMaturityDateR = value;
    }
    
    // Get<>AsString()
    public string GetD131BondMaturityDateRAsString()
    {
        return _D131BondMaturityDateR != null ? _D131BondMaturityDateR.GetD131BondMaturityDateRAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD131BondMaturityDateRAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D131BondMaturityDateR == null)
        {
            _D131BondMaturityDateR = new D131BondMaturityDateR();
        }
        _D131BondMaturityDateR.SetD131BondMaturityDateRAsString(value);
    }
    
    // Standard Getter
    public string GetD131BondMaturityDate()
    {
        return _D131BondMaturityDate;
    }
    
    // Standard Setter
    public void SetD131BondMaturityDate(string value)
    {
        _D131BondMaturityDate = value;
    }
    
    // Get<>AsString()
    public string GetD131BondMaturityDateAsString()
    {
        return _D131BondMaturityDate.PadRight(8);
    }
    
    // Set<>AsString()
    public void SetD131BondMaturityDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131BondMaturityDate = value;
    }
    
    // Standard Getter
    public string GetD131BondParValueX()
    {
        return _D131BondParValueX;
    }
    
    // Standard Setter
    public void SetD131BondParValueX(string value)
    {
        _D131BondParValueX = value;
    }
    
    // Get<>AsString()
    public string GetD131BondParValueXAsString()
    {
        return _D131BondParValueX.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetD131BondParValueXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131BondParValueX = value;
    }
    
    // Standard Getter
    public decimal GetD131BondParValue()
    {
        return _D131BondParValue;
    }
    
    // Standard Setter
    public void SetD131BondParValue(decimal value)
    {
        _D131BondParValue = value;
    }
    
    // Get<>AsString()
    public string GetD131BondParValueAsString()
    {
        return _D131BondParValue.ToString("F6", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD131BondParValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D131BondParValue = parsed;
    }
    
    // Standard Getter
    public string GetD131BondGainOverride()
    {
        return _D131BondGainOverride;
    }
    
    // Standard Setter
    public void SetD131BondGainOverride(string value)
    {
        _D131BondGainOverride = value;
    }
    
    // Get<>AsString()
    public string GetD131BondGainOverrideAsString()
    {
        return _D131BondGainOverride.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD131BondGainOverrideAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131BondGainOverride = value;
    }
    
    // Standard Getter
    public decimal GetD131UnrealBondGainLoss()
    {
        return _D131UnrealBondGainLoss;
    }
    
    // Standard Setter
    public void SetD131UnrealBondGainLoss(decimal value)
    {
        _D131UnrealBondGainLoss = value;
    }
    
    // Get<>AsString()
    public string GetD131UnrealBondGainLossAsString()
    {
        return _D131UnrealBondGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD131UnrealBondGainLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D131UnrealBondGainLoss = parsed;
    }
    
    // Standard Getter
    public decimal GetD131UnrealBondProfitLoss()
    {
        return _D131UnrealBondProfitLoss;
    }
    
    // Standard Setter
    public void SetD131UnrealBondProfitLoss(decimal value)
    {
        _D131UnrealBondProfitLoss = value;
    }
    
    // Get<>AsString()
    public string GetD131UnrealBondProfitLossAsString()
    {
        return _D131UnrealBondProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD131UnrealBondProfitLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D131UnrealBondProfitLoss = parsed;
    }
    
    // Standard Getter
    public string GetD131AssetUsage()
    {
        return _D131AssetUsage;
    }
    
    // Standard Setter
    public void SetD131AssetUsage(string value)
    {
        _D131AssetUsage = value;
    }
    
    // Get<>AsString()
    public string GetD131AssetUsageAsString()
    {
        return _D131AssetUsage.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD131AssetUsageAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131AssetUsage = value;
    }
    
    // Standard Getter
    public decimal GetD131RealBondGainLoss()
    {
        return _D131RealBondGainLoss;
    }
    
    // Standard Setter
    public void SetD131RealBondGainLoss(decimal value)
    {
        _D131RealBondGainLoss = value;
    }
    
    // Get<>AsString()
    public string GetD131RealBondGainLossAsString()
    {
        return _D131RealBondGainLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD131RealBondGainLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D131RealBondGainLoss = parsed;
    }
    
    // Standard Getter
    public decimal GetD131RealBondProfitLoss()
    {
        return _D131RealBondProfitLoss;
    }
    
    // Standard Setter
    public void SetD131RealBondProfitLoss(decimal value)
    {
        _D131RealBondProfitLoss = value;
    }
    
    // Get<>AsString()
    public string GetD131RealBondProfitLossAsString()
    {
        return _D131RealBondProfitLoss.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetD131RealBondProfitLossAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _D131RealBondProfitLoss = parsed;
    }
    
    // Standard Getter
    public string GetFiller29()
    {
        return _Filler29;
    }
    
    // Standard Setter
    public void SetFiller29(string value)
    {
        _Filler29 = value;
    }
    
    // Get<>AsString()
    public string GetFiller29AsString()
    {
        return _Filler29.PadRight(13);
    }
    
    // Set<>AsString()
    public void SetFiller29AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler29 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD131Key(string value)
    {
        _D131Key.SetD131KeyAsString(value);
    }
    // Nested Class: D131Key
    public class D131Key
    {
        private static int _size = 25;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D131CalSedol, is_external=, is_static_class=False, static_prefix=
        private D131Key.D131CalSedol _D131CalSedol = new D131Key.D131CalSedol();
        
        
        
        
        // [DEBUG] Field: D131ContractNo, is_external=, is_static_class=False, static_prefix=
        private string _D131ContractNo ="";
        
        
        
        
        // [DEBUG] Field: D131RecordCode, is_external=, is_static_class=False, static_prefix=
        private int _D131RecordCode =0;
        
        
        
        
        // [DEBUG] Field: D131RecordCodeX, is_external=, is_static_class=False, static_prefix=
        private D131Key.D131RecordCodeX _D131RecordCodeX = new D131Key.D131RecordCodeX();
        
        
        
        
    public D131Key() {}
    
    public D131Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _D131CalSedol.SetD131CalSedolAsString(data.Substring(offset, D131CalSedol.GetSize()));
        offset += 11;
        SetD131ContractNo(data.Substring(offset, 10).Trim());
        offset += 10;
        SetD131RecordCode(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        _D131RecordCodeX.SetD131RecordCodeXAsString(data.Substring(offset, D131RecordCodeX.GetSize()));
        offset += 2;
        
    }
    
    // Serialization methods
    public string GetD131KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D131CalSedol.GetD131CalSedolAsString());
        result.Append(_D131ContractNo.PadRight(10));
        result.Append(_D131RecordCode.ToString().PadLeft(2, '0'));
        result.Append(_D131RecordCodeX.GetD131RecordCodeXAsString());
        
        return result.ToString();
    }
    
    public void SetD131KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 11 <= data.Length)
        {
            _D131CalSedol.SetD131CalSedolAsString(data.Substring(offset, 11));
        }
        else
        {
            _D131CalSedol.SetD131CalSedolAsString(data.Substring(offset));
        }
        offset += 11;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetD131ContractNo(extracted);
        }
        offset += 10;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD131RecordCode(parsedInt);
        }
        offset += 2;
        if (offset + 2 <= data.Length)
        {
            _D131RecordCodeX.SetD131RecordCodeXAsString(data.Substring(offset, 2));
        }
        else
        {
            _D131RecordCodeX.SetD131RecordCodeXAsString(data.Substring(offset));
        }
        offset += 2;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public D131CalSedol GetD131CalSedol()
    {
        return _D131CalSedol;
    }
    
    // Standard Setter
    public void SetD131CalSedol(D131CalSedol value)
    {
        _D131CalSedol = value;
    }
    
    // Get<>AsString()
    public string GetD131CalSedolAsString()
    {
        return _D131CalSedol != null ? _D131CalSedol.GetD131CalSedolAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD131CalSedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D131CalSedol == null)
        {
            _D131CalSedol = new D131CalSedol();
        }
        _D131CalSedol.SetD131CalSedolAsString(value);
    }
    
    // Standard Getter
    public string GetD131ContractNo()
    {
        return _D131ContractNo;
    }
    
    // Standard Setter
    public void SetD131ContractNo(string value)
    {
        _D131ContractNo = value;
    }
    
    // Get<>AsString()
    public string GetD131ContractNoAsString()
    {
        return _D131ContractNo.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetD131ContractNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131ContractNo = value;
    }
    
    // Standard Getter
    public int GetD131RecordCode()
    {
        return _D131RecordCode;
    }
    
    // Standard Setter
    public void SetD131RecordCode(int value)
    {
        _D131RecordCode = value;
    }
    
    // Get<>AsString()
    public string GetD131RecordCodeAsString()
    {
        return _D131RecordCode.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetD131RecordCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D131RecordCode = parsed;
    }
    
    // Standard Getter
    public D131RecordCodeX GetD131RecordCodeX()
    {
        return _D131RecordCodeX;
    }
    
    // Standard Setter
    public void SetD131RecordCodeX(D131RecordCodeX value)
    {
        _D131RecordCodeX = value;
    }
    
    // Get<>AsString()
    public string GetD131RecordCodeXAsString()
    {
        return _D131RecordCodeX != null ? _D131RecordCodeX.GetD131RecordCodeXAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD131RecordCodeXAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D131RecordCodeX == null)
        {
            _D131RecordCodeX = new D131RecordCodeX();
        }
        _D131RecordCodeX.SetD131RecordCodeXAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D131CalSedol
    public class D131CalSedol
    {
        private static int _size = 11;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D131CoAcLk, is_external=, is_static_class=False, static_prefix=
        private string _D131CoAcLk ="";
        
        
        
        
        // [DEBUG] Field: D131Sedol, is_external=, is_static_class=False, static_prefix=
        private string _D131Sedol ="";
        
        
        
        
    public D131CalSedol() {}
    
    public D131CalSedol(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD131CoAcLk(data.Substring(offset, 4).Trim());
        offset += 4;
        SetD131Sedol(data.Substring(offset, 7).Trim());
        offset += 7;
        
    }
    
    // Serialization methods
    public string GetD131CalSedolAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D131CoAcLk.PadRight(4));
        result.Append(_D131Sedol.PadRight(7));
        
        return result.ToString();
    }
    
    public void SetD131CalSedolAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            SetD131CoAcLk(extracted);
        }
        offset += 4;
        if (offset + 7 <= data.Length)
        {
            string extracted = data.Substring(offset, 7).Trim();
            SetD131Sedol(extracted);
        }
        offset += 7;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD131CoAcLk()
    {
        return _D131CoAcLk;
    }
    
    // Standard Setter
    public void SetD131CoAcLk(string value)
    {
        _D131CoAcLk = value;
    }
    
    // Get<>AsString()
    public string GetD131CoAcLkAsString()
    {
        return _D131CoAcLk.PadRight(4);
    }
    
    // Set<>AsString()
    public void SetD131CoAcLkAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131CoAcLk = value;
    }
    
    // Standard Getter
    public string GetD131Sedol()
    {
        return _D131Sedol;
    }
    
    // Standard Setter
    public void SetD131Sedol(string value)
    {
        _D131Sedol = value;
    }
    
    // Get<>AsString()
    public string GetD131SedolAsString()
    {
        return _D131Sedol.PadRight(7);
    }
    
    // Set<>AsString()
    public void SetD131SedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D131Sedol = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: D131RecordCodeX
public class D131RecordCodeX
{
    private static int _size = 2;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D131RecordCodeX1, is_external=, is_static_class=False, static_prefix=
    private string _D131RecordCodeX1 ="";
    
    
    
    
    // [DEBUG] Field: D131RecordCodeX2, is_external=, is_static_class=False, static_prefix=
    private string _D131RecordCodeX2 ="";
    
    
    
    
public D131RecordCodeX() {}

public D131RecordCodeX(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD131RecordCodeX1(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD131RecordCodeX2(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetD131RecordCodeXAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D131RecordCodeX1.PadRight(1));
    result.Append(_D131RecordCodeX2.PadRight(1));
    
    return result.ToString();
}

public void SetD131RecordCodeXAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD131RecordCodeX1(extracted);
    }
    offset += 1;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD131RecordCodeX2(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetD131RecordCodeX1()
{
    return _D131RecordCodeX1;
}

// Standard Setter
public void SetD131RecordCodeX1(string value)
{
    _D131RecordCodeX1 = value;
}

// Get<>AsString()
public string GetD131RecordCodeX1AsString()
{
    return _D131RecordCodeX1.PadRight(1);
}

// Set<>AsString()
public void SetD131RecordCodeX1AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131RecordCodeX1 = value;
}

// Standard Getter
public string GetD131RecordCodeX2()
{
    return _D131RecordCodeX2;
}

// Standard Setter
public void SetD131RecordCodeX2(string value)
{
    _D131RecordCodeX2 = value;
}

// Get<>AsString()
public string GetD131RecordCodeX2AsString()
{
    return _D131RecordCodeX2.PadRight(1);
}

// Set<>AsString()
public void SetD131RecordCodeX2AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131RecordCodeX2 = value;
}



public static int GetSize()
{
    return _size;
}

}
}
// Set<>String Override function (Nested)
public void SetD131DateTimeStamp(string value)
{
    _D131DateTimeStamp.SetD131DateTimeStampAsString(value);
}
// Nested Class: D131DateTimeStamp
public class D131DateTimeStamp
{
    private static int _size = 14;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D131DateStamp, is_external=, is_static_class=False, static_prefix=
    private string _D131DateStamp ="";
    
    
    
    
    // [DEBUG] Field: D131TimeStamp, is_external=, is_static_class=False, static_prefix=
    private string _D131TimeStamp ="";
    
    
    
    
public D131DateTimeStamp() {}

public D131DateTimeStamp(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD131DateStamp(data.Substring(offset, 6).Trim());
    offset += 6;
    SetD131TimeStamp(data.Substring(offset, 8).Trim());
    offset += 8;
    
}

// Serialization methods
public string GetD131DateTimeStampAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D131DateStamp.PadRight(6));
    result.Append(_D131TimeStamp.PadRight(8));
    
    return result.ToString();
}

public void SetD131DateTimeStampAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 6 <= data.Length)
    {
        string extracted = data.Substring(offset, 6).Trim();
        SetD131DateStamp(extracted);
    }
    offset += 6;
    if (offset + 8 <= data.Length)
    {
        string extracted = data.Substring(offset, 8).Trim();
        SetD131TimeStamp(extracted);
    }
    offset += 8;
}

// Getter and Setter methods

// Standard Getter
public string GetD131DateStamp()
{
    return _D131DateStamp;
}

// Standard Setter
public void SetD131DateStamp(string value)
{
    _D131DateStamp = value;
}

// Get<>AsString()
public string GetD131DateStampAsString()
{
    return _D131DateStamp.PadRight(6);
}

// Set<>AsString()
public void SetD131DateStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131DateStamp = value;
}

// Standard Getter
public string GetD131TimeStamp()
{
    return _D131TimeStamp;
}

// Standard Setter
public void SetD131TimeStamp(string value)
{
    _D131TimeStamp = value;
}

// Get<>AsString()
public string GetD131TimeStampAsString()
{
    return _D131TimeStamp.PadRight(8);
}

// Set<>AsString()
public void SetD131TimeStampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131TimeStamp = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetFiller28(string value)
{
    _Filler28.SetFiller28AsString(value);
}
// Nested Class: Filler28
public class Filler28
{
    private static int _size = 14;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D131Stamp, is_external=, is_static_class=False, static_prefix=
    private string _D131Stamp ="";
    
    
    
    
    // [DEBUG] Field: D131Partly, is_external=, is_static_class=False, static_prefix=
    private string _D131Partly ="";
    
    
    
    
public Filler28() {}

public Filler28(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD131Stamp(data.Substring(offset, 13).Trim());
    offset += 13;
    SetD131Partly(data.Substring(offset, 1).Trim());
    offset += 1;
    
}

// Serialization methods
public string GetFiller28AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D131Stamp.PadRight(13));
    result.Append(_D131Partly.PadRight(1));
    
    return result.ToString();
}

public void SetFiller28AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 13 <= data.Length)
    {
        string extracted = data.Substring(offset, 13).Trim();
        SetD131Stamp(extracted);
    }
    offset += 13;
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD131Partly(extracted);
    }
    offset += 1;
}

// Getter and Setter methods

// Standard Getter
public string GetD131Stamp()
{
    return _D131Stamp;
}

// Standard Setter
public void SetD131Stamp(string value)
{
    _D131Stamp = value;
}

// Get<>AsString()
public string GetD131StampAsString()
{
    return _D131Stamp.PadRight(13);
}

// Set<>AsString()
public void SetD131StampAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131Stamp = value;
}

// Standard Getter
public string GetD131Partly()
{
    return _D131Partly;
}

// Standard Setter
public void SetD131Partly(string value)
{
    _D131Partly = value;
}

// Get<>AsString()
public string GetD131PartlyAsString()
{
    return _D131Partly.PadRight(1);
}

// Set<>AsString()
public void SetD131PartlyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131Partly = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD131RCalcPeriodEndDate(string value)
{
    _D131RCalcPeriodEndDate.SetD131RCalcPeriodEndDateAsString(value);
}
// Nested Class: D131RCalcPeriodEndDate
public class D131RCalcPeriodEndDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D131RCalcYy, is_external=, is_static_class=False, static_prefix=
    private string _D131RCalcYy ="";
    
    
    
    
    // [DEBUG] Field: D131RCalcMm, is_external=, is_static_class=False, static_prefix=
    private string _D131RCalcMm ="";
    
    
    
    
    // [DEBUG] Field: D131RCalcDd, is_external=, is_static_class=False, static_prefix=
    private string _D131RCalcDd ="";
    
    
    
    
public D131RCalcPeriodEndDate() {}

public D131RCalcPeriodEndDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD131RCalcYy(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD131RCalcMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD131RCalcDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD131RCalcPeriodEndDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D131RCalcYy.PadRight(2));
    result.Append(_D131RCalcMm.PadRight(2));
    result.Append(_D131RCalcDd.PadRight(2));
    
    return result.ToString();
}

public void SetD131RCalcPeriodEndDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131RCalcYy(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131RCalcMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131RCalcDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD131RCalcYy()
{
    return _D131RCalcYy;
}

// Standard Setter
public void SetD131RCalcYy(string value)
{
    _D131RCalcYy = value;
}

// Get<>AsString()
public string GetD131RCalcYyAsString()
{
    return _D131RCalcYy.PadRight(2);
}

// Set<>AsString()
public void SetD131RCalcYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131RCalcYy = value;
}

// Standard Getter
public string GetD131RCalcMm()
{
    return _D131RCalcMm;
}

// Standard Setter
public void SetD131RCalcMm(string value)
{
    _D131RCalcMm = value;
}

// Get<>AsString()
public string GetD131RCalcMmAsString()
{
    return _D131RCalcMm.PadRight(2);
}

// Set<>AsString()
public void SetD131RCalcMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131RCalcMm = value;
}

// Standard Getter
public string GetD131RCalcDd()
{
    return _D131RCalcDd;
}

// Standard Setter
public void SetD131RCalcDd(string value)
{
    _D131RCalcDd = value;
}

// Get<>AsString()
public string GetD131RCalcDdAsString()
{
    return _D131RCalcDd.PadRight(2);
}

// Set<>AsString()
public void SetD131RCalcDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131RCalcDd = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD131UCalcPeriodEndDate(string value)
{
    _D131UCalcPeriodEndDate.SetD131UCalcPeriodEndDateAsString(value);
}
// Nested Class: D131UCalcPeriodEndDate
public class D131UCalcPeriodEndDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D131UCalcYy, is_external=, is_static_class=False, static_prefix=
    private string _D131UCalcYy ="";
    
    
    
    
    // [DEBUG] Field: D131UCalcMm, is_external=, is_static_class=False, static_prefix=
    private string _D131UCalcMm ="";
    
    
    
    
    // [DEBUG] Field: D131UCalcDd, is_external=, is_static_class=False, static_prefix=
    private string _D131UCalcDd ="";
    
    
    
    
public D131UCalcPeriodEndDate() {}

public D131UCalcPeriodEndDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD131UCalcYy(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD131UCalcMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD131UCalcDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD131UCalcPeriodEndDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D131UCalcYy.PadRight(2));
    result.Append(_D131UCalcMm.PadRight(2));
    result.Append(_D131UCalcDd.PadRight(2));
    
    return result.ToString();
}

public void SetD131UCalcPeriodEndDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131UCalcYy(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131UCalcMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131UCalcDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD131UCalcYy()
{
    return _D131UCalcYy;
}

// Standard Setter
public void SetD131UCalcYy(string value)
{
    _D131UCalcYy = value;
}

// Get<>AsString()
public string GetD131UCalcYyAsString()
{
    return _D131UCalcYy.PadRight(2);
}

// Set<>AsString()
public void SetD131UCalcYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131UCalcYy = value;
}

// Standard Getter
public string GetD131UCalcMm()
{
    return _D131UCalcMm;
}

// Standard Setter
public void SetD131UCalcMm(string value)
{
    _D131UCalcMm = value;
}

// Get<>AsString()
public string GetD131UCalcMmAsString()
{
    return _D131UCalcMm.PadRight(2);
}

// Set<>AsString()
public void SetD131UCalcMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131UCalcMm = value;
}

// Standard Getter
public string GetD131UCalcDd()
{
    return _D131UCalcDd;
}

// Standard Setter
public void SetD131UCalcDd(string value)
{
    _D131UCalcDd = value;
}

// Get<>AsString()
public string GetD131UCalcDdAsString()
{
    return _D131UCalcDd.PadRight(2);
}

// Set<>AsString()
public void SetD131UCalcDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131UCalcDd = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD131SpareDate(string value)
{
    _D131SpareDate.SetD131SpareDateAsString(value);
}
// Nested Class: D131SpareDate
public class D131SpareDate
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D131SpareYy, is_external=, is_static_class=False, static_prefix=
    private string _D131SpareYy ="";
    
    
    
    
    // [DEBUG] Field: D131SpareMm, is_external=, is_static_class=False, static_prefix=
    private string _D131SpareMm ="";
    
    
    
    
    // [DEBUG] Field: D131SpareDd, is_external=, is_static_class=False, static_prefix=
    private string _D131SpareDd ="";
    
    
    
    
public D131SpareDate() {}

public D131SpareDate(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD131SpareYy(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD131SpareMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD131SpareDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD131SpareDateAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D131SpareYy.PadRight(2));
    result.Append(_D131SpareMm.PadRight(2));
    result.Append(_D131SpareDd.PadRight(2));
    
    return result.ToString();
}

public void SetD131SpareDateAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131SpareYy(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131SpareMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131SpareDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD131SpareYy()
{
    return _D131SpareYy;
}

// Standard Setter
public void SetD131SpareYy(string value)
{
    _D131SpareYy = value;
}

// Get<>AsString()
public string GetD131SpareYyAsString()
{
    return _D131SpareYy.PadRight(2);
}

// Set<>AsString()
public void SetD131SpareYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131SpareYy = value;
}

// Standard Getter
public string GetD131SpareMm()
{
    return _D131SpareMm;
}

// Standard Setter
public void SetD131SpareMm(string value)
{
    _D131SpareMm = value;
}

// Get<>AsString()
public string GetD131SpareMmAsString()
{
    return _D131SpareMm.PadRight(2);
}

// Set<>AsString()
public void SetD131SpareMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131SpareMm = value;
}

// Standard Getter
public string GetD131SpareDd()
{
    return _D131SpareDd;
}

// Standard Setter
public void SetD131SpareDd(string value)
{
    _D131SpareDd = value;
}

// Get<>AsString()
public string GetD131SpareDdAsString()
{
    return _D131SpareDd.PadRight(2);
}

// Set<>AsString()
public void SetD131SpareDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131SpareDd = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD131MainGroupCode(string value)
{
    _D131MainGroupCode.SetD131MainGroupCodeAsString(value);
}
// Nested Class: D131MainGroupCode
public class D131MainGroupCode
{
    private static int _size = 3;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D131GroupPrefix, is_external=, is_static_class=False, static_prefix=
    private string _D131GroupPrefix ="";
    
    
    
    
    // [DEBUG] Field: D131GroupCode, is_external=, is_static_class=False, static_prefix=
    private string _D131GroupCode ="";
    
    
    
    
public D131MainGroupCode() {}

public D131MainGroupCode(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD131GroupPrefix(data.Substring(offset, 1).Trim());
    offset += 1;
    SetD131GroupCode(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD131MainGroupCodeAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D131GroupPrefix.PadRight(1));
    result.Append(_D131GroupCode.PadRight(2));
    
    return result.ToString();
}

public void SetD131MainGroupCodeAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 1 <= data.Length)
    {
        string extracted = data.Substring(offset, 1).Trim();
        SetD131GroupPrefix(extracted);
    }
    offset += 1;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131GroupCode(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD131GroupPrefix()
{
    return _D131GroupPrefix;
}

// Standard Setter
public void SetD131GroupPrefix(string value)
{
    _D131GroupPrefix = value;
}

// Get<>AsString()
public string GetD131GroupPrefixAsString()
{
    return _D131GroupPrefix.PadRight(1);
}

// Set<>AsString()
public void SetD131GroupPrefixAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131GroupPrefix = value;
}

// Standard Getter
public string GetD131GroupCode()
{
    return _D131GroupCode;
}

// Standard Setter
public void SetD131GroupCode(string value)
{
    _D131GroupCode = value;
}

// Get<>AsString()
public string GetD131GroupCodeAsString()
{
    return _D131GroupCode.PadRight(2);
}

// Set<>AsString()
public void SetD131GroupCodeAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131GroupCode = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD131Bf2PctHoldingDateR(string value)
{
    _D131Bf2PctHoldingDateR.SetD131Bf2PctHoldingDateRAsString(value);
}
// Nested Class: D131Bf2PctHoldingDateR
public class D131Bf2PctHoldingDateR
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D131Bf2PctHoldingDateYy, is_external=, is_static_class=False, static_prefix=
    private string _D131Bf2PctHoldingDateYy ="";
    
    
    
    
    // [DEBUG] Field: D131Bf2PctHoldingDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D131Bf2PctHoldingDateMm ="";
    
    
    
    
    // [DEBUG] Field: D131Bf2PctHoldingDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D131Bf2PctHoldingDateDd ="";
    
    
    
    
public D131Bf2PctHoldingDateR() {}

public D131Bf2PctHoldingDateR(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD131Bf2PctHoldingDateYy(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD131Bf2PctHoldingDateMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD131Bf2PctHoldingDateDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD131Bf2PctHoldingDateRAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D131Bf2PctHoldingDateYy.PadRight(2));
    result.Append(_D131Bf2PctHoldingDateMm.PadRight(2));
    result.Append(_D131Bf2PctHoldingDateDd.PadRight(2));
    
    return result.ToString();
}

public void SetD131Bf2PctHoldingDateRAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131Bf2PctHoldingDateYy(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131Bf2PctHoldingDateMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131Bf2PctHoldingDateDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD131Bf2PctHoldingDateYy()
{
    return _D131Bf2PctHoldingDateYy;
}

// Standard Setter
public void SetD131Bf2PctHoldingDateYy(string value)
{
    _D131Bf2PctHoldingDateYy = value;
}

// Get<>AsString()
public string GetD131Bf2PctHoldingDateYyAsString()
{
    return _D131Bf2PctHoldingDateYy.PadRight(2);
}

// Set<>AsString()
public void SetD131Bf2PctHoldingDateYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131Bf2PctHoldingDateYy = value;
}

// Standard Getter
public string GetD131Bf2PctHoldingDateMm()
{
    return _D131Bf2PctHoldingDateMm;
}

// Standard Setter
public void SetD131Bf2PctHoldingDateMm(string value)
{
    _D131Bf2PctHoldingDateMm = value;
}

// Get<>AsString()
public string GetD131Bf2PctHoldingDateMmAsString()
{
    return _D131Bf2PctHoldingDateMm.PadRight(2);
}

// Set<>AsString()
public void SetD131Bf2PctHoldingDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131Bf2PctHoldingDateMm = value;
}

// Standard Getter
public string GetD131Bf2PctHoldingDateDd()
{
    return _D131Bf2PctHoldingDateDd;
}

// Standard Setter
public void SetD131Bf2PctHoldingDateDd(string value)
{
    _D131Bf2PctHoldingDateDd = value;
}

// Get<>AsString()
public string GetD131Bf2PctHoldingDateDdAsString()
{
    return _D131Bf2PctHoldingDateDd.PadRight(2);
}

// Set<>AsString()
public void SetD131Bf2PctHoldingDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131Bf2PctHoldingDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD1312PctHoldingDateYtdR(string value)
{
    _D1312PctHoldingDateYtdR.SetD1312PctHoldingDateYtdRAsString(value);
}
// Nested Class: D1312PctHoldingDateYtdR
public class D1312PctHoldingDateYtdR
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D1312PctHoldingDateYtdYy, is_external=, is_static_class=False, static_prefix=
    private string _D1312PctHoldingDateYtdYy ="";
    
    
    
    
    // [DEBUG] Field: D1312PctHoldingDateYtdMm, is_external=, is_static_class=False, static_prefix=
    private string _D1312PctHoldingDateYtdMm ="";
    
    
    
    
    // [DEBUG] Field: D1312PctHoldingDateYtdDd, is_external=, is_static_class=False, static_prefix=
    private string _D1312PctHoldingDateYtdDd ="";
    
    
    
    
public D1312PctHoldingDateYtdR() {}

public D1312PctHoldingDateYtdR(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD1312PctHoldingDateYtdYy(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD1312PctHoldingDateYtdMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD1312PctHoldingDateYtdDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD1312PctHoldingDateYtdRAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D1312PctHoldingDateYtdYy.PadRight(2));
    result.Append(_D1312PctHoldingDateYtdMm.PadRight(2));
    result.Append(_D1312PctHoldingDateYtdDd.PadRight(2));
    
    return result.ToString();
}

public void SetD1312PctHoldingDateYtdRAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD1312PctHoldingDateYtdYy(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD1312PctHoldingDateYtdMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD1312PctHoldingDateYtdDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD1312PctHoldingDateYtdYy()
{
    return _D1312PctHoldingDateYtdYy;
}

// Standard Setter
public void SetD1312PctHoldingDateYtdYy(string value)
{
    _D1312PctHoldingDateYtdYy = value;
}

// Get<>AsString()
public string GetD1312PctHoldingDateYtdYyAsString()
{
    return _D1312PctHoldingDateYtdYy.PadRight(2);
}

// Set<>AsString()
public void SetD1312PctHoldingDateYtdYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D1312PctHoldingDateYtdYy = value;
}

// Standard Getter
public string GetD1312PctHoldingDateYtdMm()
{
    return _D1312PctHoldingDateYtdMm;
}

// Standard Setter
public void SetD1312PctHoldingDateYtdMm(string value)
{
    _D1312PctHoldingDateYtdMm = value;
}

// Get<>AsString()
public string GetD1312PctHoldingDateYtdMmAsString()
{
    return _D1312PctHoldingDateYtdMm.PadRight(2);
}

// Set<>AsString()
public void SetD1312PctHoldingDateYtdMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D1312PctHoldingDateYtdMm = value;
}

// Standard Getter
public string GetD1312PctHoldingDateYtdDd()
{
    return _D1312PctHoldingDateYtdDd;
}

// Standard Setter
public void SetD1312PctHoldingDateYtdDd(string value)
{
    _D1312PctHoldingDateYtdDd = value;
}

// Get<>AsString()
public string GetD1312PctHoldingDateYtdDdAsString()
{
    return _D1312PctHoldingDateYtdDd.PadRight(2);
}

// Set<>AsString()
public void SetD1312PctHoldingDateYtdDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D1312PctHoldingDateYtdDd = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD131DateOfIssueR(string value)
{
    _D131DateOfIssueR.SetD131DateOfIssueRAsString(value);
}
// Nested Class: D131DateOfIssueR
public class D131DateOfIssueR
{
    private static int _size = 6;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D131DateOfIssueYy, is_external=, is_static_class=False, static_prefix=
    private string _D131DateOfIssueYy ="";
    
    
    
    
    // [DEBUG] Field: D131DateOfIssueMm, is_external=, is_static_class=False, static_prefix=
    private string _D131DateOfIssueMm ="";
    
    
    
    
    // [DEBUG] Field: D131DateOfIssueDd, is_external=, is_static_class=False, static_prefix=
    private string _D131DateOfIssueDd ="";
    
    
    
    
public D131DateOfIssueR() {}

public D131DateOfIssueR(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD131DateOfIssueYy(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD131DateOfIssueMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD131DateOfIssueDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD131DateOfIssueRAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D131DateOfIssueYy.PadRight(2));
    result.Append(_D131DateOfIssueMm.PadRight(2));
    result.Append(_D131DateOfIssueDd.PadRight(2));
    
    return result.ToString();
}

public void SetD131DateOfIssueRAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131DateOfIssueYy(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131DateOfIssueMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131DateOfIssueDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD131DateOfIssueYy()
{
    return _D131DateOfIssueYy;
}

// Standard Setter
public void SetD131DateOfIssueYy(string value)
{
    _D131DateOfIssueYy = value;
}

// Get<>AsString()
public string GetD131DateOfIssueYyAsString()
{
    return _D131DateOfIssueYy.PadRight(2);
}

// Set<>AsString()
public void SetD131DateOfIssueYyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131DateOfIssueYy = value;
}

// Standard Getter
public string GetD131DateOfIssueMm()
{
    return _D131DateOfIssueMm;
}

// Standard Setter
public void SetD131DateOfIssueMm(string value)
{
    _D131DateOfIssueMm = value;
}

// Get<>AsString()
public string GetD131DateOfIssueMmAsString()
{
    return _D131DateOfIssueMm.PadRight(2);
}

// Set<>AsString()
public void SetD131DateOfIssueMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131DateOfIssueMm = value;
}

// Standard Getter
public string GetD131DateOfIssueDd()
{
    return _D131DateOfIssueDd;
}

// Standard Setter
public void SetD131DateOfIssueDd(string value)
{
    _D131DateOfIssueDd = value;
}

// Get<>AsString()
public string GetD131DateOfIssueDdAsString()
{
    return _D131DateOfIssueDd.PadRight(2);
}

// Set<>AsString()
public void SetD131DateOfIssueDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131DateOfIssueDd = value;
}



public static int GetSize()
{
    return _size;
}

}
// Set<>String Override function (Nested)
public void SetD131BondMaturityDateR(string value)
{
    _D131BondMaturityDateR.SetD131BondMaturityDateRAsString(value);
}
// Nested Class: D131BondMaturityDateR
public class D131BondMaturityDateR
{
    private static int _size = 8;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D131BondMaturityDateCcyy, is_external=, is_static_class=False, static_prefix=
    private string _D131BondMaturityDateCcyy ="";
    
    
    
    
    // [DEBUG] Field: D131BondMaturityDateMm, is_external=, is_static_class=False, static_prefix=
    private string _D131BondMaturityDateMm ="";
    
    
    
    
    // [DEBUG] Field: D131BondMaturityDateDd, is_external=, is_static_class=False, static_prefix=
    private string _D131BondMaturityDateDd ="";
    
    
    
    
public D131BondMaturityDateR() {}

public D131BondMaturityDateR(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD131BondMaturityDateCcyy(data.Substring(offset, 4).Trim());
    offset += 4;
    SetD131BondMaturityDateMm(data.Substring(offset, 2).Trim());
    offset += 2;
    SetD131BondMaturityDateDd(data.Substring(offset, 2).Trim());
    offset += 2;
    
}

// Serialization methods
public string GetD131BondMaturityDateRAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D131BondMaturityDateCcyy.PadRight(4));
    result.Append(_D131BondMaturityDateMm.PadRight(2));
    result.Append(_D131BondMaturityDateDd.PadRight(2));
    
    return result.ToString();
}

public void SetD131BondMaturityDateRAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 4 <= data.Length)
    {
        string extracted = data.Substring(offset, 4).Trim();
        SetD131BondMaturityDateCcyy(extracted);
    }
    offset += 4;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131BondMaturityDateMm(extracted);
    }
    offset += 2;
    if (offset + 2 <= data.Length)
    {
        string extracted = data.Substring(offset, 2).Trim();
        SetD131BondMaturityDateDd(extracted);
    }
    offset += 2;
}

// Getter and Setter methods

// Standard Getter
public string GetD131BondMaturityDateCcyy()
{
    return _D131BondMaturityDateCcyy;
}

// Standard Setter
public void SetD131BondMaturityDateCcyy(string value)
{
    _D131BondMaturityDateCcyy = value;
}

// Get<>AsString()
public string GetD131BondMaturityDateCcyyAsString()
{
    return _D131BondMaturityDateCcyy.PadRight(4);
}

// Set<>AsString()
public void SetD131BondMaturityDateCcyyAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131BondMaturityDateCcyy = value;
}

// Standard Getter
public string GetD131BondMaturityDateMm()
{
    return _D131BondMaturityDateMm;
}

// Standard Setter
public void SetD131BondMaturityDateMm(string value)
{
    _D131BondMaturityDateMm = value;
}

// Get<>AsString()
public string GetD131BondMaturityDateMmAsString()
{
    return _D131BondMaturityDateMm.PadRight(2);
}

// Set<>AsString()
public void SetD131BondMaturityDateMmAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131BondMaturityDateMm = value;
}

// Standard Getter
public string GetD131BondMaturityDateDd()
{
    return _D131BondMaturityDateDd;
}

// Standard Setter
public void SetD131BondMaturityDateDd(string value)
{
    _D131BondMaturityDateDd = value;
}

// Get<>AsString()
public string GetD131BondMaturityDateDdAsString()
{
    return _D131BondMaturityDateDd.PadRight(2);
}

// Set<>AsString()
public void SetD131BondMaturityDateDdAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D131BondMaturityDateDd = value;
}



public static int GetSize()
{
    return _size;
}

}

}}