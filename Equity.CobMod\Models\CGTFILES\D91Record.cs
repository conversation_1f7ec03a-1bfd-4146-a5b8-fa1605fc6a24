using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D91Record Data Structure

public class D91Record
{
    private static int _size = 43;
    // [DEBUG] Class: D91Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D91CountryCode, is_external=, is_static_class=False, static_prefix=
    private string _D91CountryCode ="";
    
    
    
    
    // [DEBUG] Field: D91CountryName, is_external=, is_static_class=False, static_prefix=
    private string _D91CountryName ="";
    
    
    
    
    
    // Serialization methods
    public string GetD91RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D91CountryCode.PadRight(3));
        result.Append(_D91CountryName.PadRight(40));
        
        return result.ToString();
    }
    
    public void SetD91RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetD91CountryCode(extracted);
        }
        offset += 3;
        if (offset + 40 <= data.Length)
        {
            string extracted = data.Substring(offset, 40).Trim();
            SetD91CountryName(extracted);
        }
        offset += 40;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD91RecordAsString();
    }
    // Set<>String Override function
    public void SetD91Record(string value)
    {
        SetD91RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD91CountryCode()
    {
        return _D91CountryCode;
    }
    
    // Standard Setter
    public void SetD91CountryCode(string value)
    {
        _D91CountryCode = value;
    }
    
    // Get<>AsString()
    public string GetD91CountryCodeAsString()
    {
        return _D91CountryCode.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetD91CountryCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D91CountryCode = value;
    }
    
    // Standard Getter
    public string GetD91CountryName()
    {
        return _D91CountryName;
    }
    
    // Standard Setter
    public void SetD91CountryName(string value)
    {
        _D91CountryName = value;
    }
    
    // Get<>AsString()
    public string GetD91CountryNameAsString()
    {
        return _D91CountryName.PadRight(40);
    }
    
    // Set<>AsString()
    public void SetD91CountryNameAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D91CountryName = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}