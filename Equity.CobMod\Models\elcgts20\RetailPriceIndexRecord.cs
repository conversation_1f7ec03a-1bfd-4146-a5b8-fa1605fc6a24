using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing RetailPriceIndexRecord Data Structure

public class RetailPriceIndexRecord
{
    private static int _size = 17;
    // [DEBUG] Class: RetailPriceIndexRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: RpiKey, is_external=, is_static_class=False, static_prefix=
    private RpiKey _RpiKey = new RpiKey();
    
    
    
    
    
    // Serialization methods
    public string GetRetailPriceIndexRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_RpiKey.GetRpiKeyAsString());
        
        return result.ToString();
    }
    
    public void SetRetailPriceIndexRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 17 <= data.Length)
        {
            _RpiKey.SetRpiKeyAsString(data.Substring(offset, 17));
        }
        else
        {
            _RpiKey.SetRpiKeyAsString(data.Substring(offset));
        }
        offset += 17;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetRetailPriceIndexRecordAsString();
    }
    // Set<>String Override function
    public void SetRetailPriceIndexRecord(string value)
    {
        SetRetailPriceIndexRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public RpiKey GetRpiKey()
    {
        return _RpiKey;
    }
    
    // Standard Setter
    public void SetRpiKey(RpiKey value)
    {
        _RpiKey = value;
    }
    
    // Get<>AsString()
    public string GetRpiKeyAsString()
    {
        return _RpiKey != null ? _RpiKey.GetRpiKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetRpiKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_RpiKey == null)
        {
            _RpiKey = new RpiKey();
        }
        _RpiKey.SetRpiKeyAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetRpiKey(string value)
    {
        _RpiKey.SetRpiKeyAsString(value);
    }
    // Nested Class: RpiKey
    public class RpiKey
    {
        private static int _size = 17;
        
        // Fields in the class
        
        
        // [DEBUG] Field: RpiDateCc, is_external=, is_static_class=False, static_prefix=
        private int _RpiDateCc =0;
        
        
        
        
        // [DEBUG] Field: RpiDateYymm, is_external=, is_static_class=False, static_prefix=
        private int _RpiDateYymm =0;
        
        
        
        
        // [DEBUG] Field: RpiValue, is_external=, is_static_class=False, static_prefix=
        private int _RpiValue =0;
        
        
        
        
        // [DEBUG] Field: RpiDeleteFlag, is_external=, is_static_class=False, static_prefix=
        private string _RpiDeleteFlag ="";
        
        
        
        
        // [DEBUG] Field: RpiAmendmentDate, is_external=, is_static_class=False, static_prefix=
        private int _RpiAmendmentDate =0;
        
        
        
        
    public RpiKey() {}
    
    public RpiKey(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetRpiDateCc(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetRpiDateYymm(int.Parse(data.Substring(offset, 4).Trim()));
        offset += 4;
        SetRpiValue(int.Parse(data.Substring(offset, 4).Trim()));
        offset += 4;
        SetRpiDeleteFlag(data.Substring(offset, 1).Trim());
        offset += 1;
        SetRpiAmendmentDate(int.Parse(data.Substring(offset, 6).Trim()));
        offset += 6;
        
    }
    
    // Serialization methods
    public string GetRpiKeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_RpiDateCc.ToString().PadLeft(2, '0'));
        result.Append(_RpiDateYymm.ToString().PadLeft(4, '0'));
        result.Append(_RpiValue.ToString().PadLeft(4, '0'));
        result.Append(_RpiDeleteFlag.PadRight(1));
        result.Append(_RpiAmendmentDate.ToString().PadLeft(6, '0'));
        
        return result.ToString();
    }
    
    public void SetRpiKeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetRpiDateCc(parsedInt);
        }
        offset += 2;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetRpiDateYymm(parsedInt);
        }
        offset += 4;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetRpiValue(parsedInt);
        }
        offset += 4;
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetRpiDeleteFlag(extracted);
        }
        offset += 1;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetRpiAmendmentDate(parsedInt);
        }
        offset += 6;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public int GetRpiDateCc()
    {
        return _RpiDateCc;
    }
    
    // Standard Setter
    public void SetRpiDateCc(int value)
    {
        _RpiDateCc = value;
    }
    
    // Get<>AsString()
    public string GetRpiDateCcAsString()
    {
        return _RpiDateCc.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetRpiDateCcAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _RpiDateCc = parsed;
    }
    
    // Standard Getter
    public int GetRpiDateYymm()
    {
        return _RpiDateYymm;
    }
    
    // Standard Setter
    public void SetRpiDateYymm(int value)
    {
        _RpiDateYymm = value;
    }
    
    // Get<>AsString()
    public string GetRpiDateYymmAsString()
    {
        return _RpiDateYymm.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetRpiDateYymmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _RpiDateYymm = parsed;
    }
    
    // Standard Getter
    public int GetRpiValue()
    {
        return _RpiValue;
    }
    
    // Standard Setter
    public void SetRpiValue(int value)
    {
        _RpiValue = value;
    }
    
    // Get<>AsString()
    public string GetRpiValueAsString()
    {
        return _RpiValue.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetRpiValueAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _RpiValue = parsed;
    }
    
    // Standard Getter
    public string GetRpiDeleteFlag()
    {
        return _RpiDeleteFlag;
    }
    
    // Standard Setter
    public void SetRpiDeleteFlag(string value)
    {
        _RpiDeleteFlag = value;
    }
    
    // Get<>AsString()
    public string GetRpiDeleteFlagAsString()
    {
        return _RpiDeleteFlag.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetRpiDeleteFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _RpiDeleteFlag = value;
    }
    
    // Standard Getter
    public int GetRpiAmendmentDate()
    {
        return _RpiAmendmentDate;
    }
    
    // Standard Setter
    public void SetRpiAmendmentDate(int value)
    {
        _RpiAmendmentDate = value;
    }
    
    // Get<>AsString()
    public string GetRpiAmendmentDateAsString()
    {
        return _RpiAmendmentDate.ToString().PadLeft(6, '0');
    }
    
    // Set<>AsString()
    public void SetRpiAmendmentDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _RpiAmendmentDate = parsed;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}

}}
