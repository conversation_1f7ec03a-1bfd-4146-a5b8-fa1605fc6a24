using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Cgt222HsDTO
{// DTO class representing WInitialisedRecord Data Structure

public class WInitialisedRecord
{
    private static int _size = 94;
    // [DEBUG] Class: WInitialisedRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: WNewInitialisedRecord, is_external=, is_static_class=False, static_prefix=
    private WNewInitialisedRecord _WNewInitialisedRecord = new WNewInitialisedRecord();
    
    
    
    
    
    // Serialization methods
    public string GetWInitialisedRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WNewInitialisedRecord.GetWNewInitialisedRecordAsString());
        
        return result.ToString();
    }
    
    public void SetWInitialisedRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 94 <= data.Length)
        {
            _WNewInitialisedRecord.SetWNewInitialisedRecordAsString(data.Substring(offset, 94));
        }
        else
        {
            _WNewInitialisedRecord.SetWNewInitialisedRecordAsString(data.Substring(offset));
        }
        offset += 94;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWInitialisedRecordAsString();
    }
    // Set<>String Override function
    public void SetWInitialisedRecord(string value)
    {
        SetWInitialisedRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public WNewInitialisedRecord GetWNewInitialisedRecord()
    {
        return _WNewInitialisedRecord;
    }
    
    // Standard Setter
    public void SetWNewInitialisedRecord(WNewInitialisedRecord value)
    {
        _WNewInitialisedRecord = value;
    }
    
    // Get<>AsString()
    public string GetWNewInitialisedRecordAsString()
    {
        return _WNewInitialisedRecord != null ? _WNewInitialisedRecord.GetWNewInitialisedRecordAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWNewInitialisedRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WNewInitialisedRecord == null)
        {
            _WNewInitialisedRecord = new WNewInitialisedRecord();
        }
        _WNewInitialisedRecord.SetWNewInitialisedRecordAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWNewInitialisedRecord(string value)
    {
        _WNewInitialisedRecord.SetWNewInitialisedRecordAsString(value);
    }
    // Nested Class: WNewInitialisedRecord
    public class WNewInitialisedRecord
    {
        private static int _size = 94;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WOldInitialisedRecord, is_external=, is_static_class=False, static_prefix=
        private WNewInitialisedRecord.WOldInitialisedRecord _WOldInitialisedRecord = new WNewInitialisedRecord.WOldInitialisedRecord();
        
        
        
        
        // [DEBUG] Field: Filler25, is_external=, is_static_class=False, static_prefix=
        private WNewInitialisedRecord.Filler25 _Filler25 = new WNewInitialisedRecord.Filler25();
        
        
        
        
    public WNewInitialisedRecord() {}
    
    public WNewInitialisedRecord(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _WOldInitialisedRecord.SetWOldInitialisedRecordAsString(data.Substring(offset, WOldInitialisedRecord.GetSize()));
        offset += 94;
        _Filler25.SetFiller25AsString(data.Substring(offset, Filler25.GetSize()));
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetWNewInitialisedRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WOldInitialisedRecord.GetWOldInitialisedRecordAsString());
        result.Append(_Filler25.GetFiller25AsString());
        
        return result.ToString();
    }
    
    public void SetWNewInitialisedRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 94 <= data.Length)
        {
            _WOldInitialisedRecord.SetWOldInitialisedRecordAsString(data.Substring(offset, 94));
        }
        else
        {
            _WOldInitialisedRecord.SetWOldInitialisedRecordAsString(data.Substring(offset));
        }
        offset += 94;
        if (offset + 0 <= data.Length)
        {
            _Filler25.SetFiller25AsString(data.Substring(offset, 0));
        }
        else
        {
            _Filler25.SetFiller25AsString(data.Substring(offset));
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public WOldInitialisedRecord GetWOldInitialisedRecord()
    {
        return _WOldInitialisedRecord;
    }
    
    // Standard Setter
    public void SetWOldInitialisedRecord(WOldInitialisedRecord value)
    {
        _WOldInitialisedRecord = value;
    }
    
    // Get<>AsString()
    public string GetWOldInitialisedRecordAsString()
    {
        return _WOldInitialisedRecord != null ? _WOldInitialisedRecord.GetWOldInitialisedRecordAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWOldInitialisedRecordAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WOldInitialisedRecord == null)
        {
            _WOldInitialisedRecord = new WOldInitialisedRecord();
        }
        _WOldInitialisedRecord.SetWOldInitialisedRecordAsString(value);
    }
    
    // Standard Getter
    public Filler25 GetFiller25()
    {
        return _Filler25;
    }
    
    // Standard Setter
    public void SetFiller25(Filler25 value)
    {
        _Filler25 = value;
    }
    
    // Get<>AsString()
    public string GetFiller25AsString()
    {
        return _Filler25 != null ? _Filler25.GetFiller25AsString() : "";
    }
    
    // Set<>AsString()
    public void SetFiller25AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_Filler25 == null)
        {
            _Filler25 = new Filler25();
        }
        _Filler25.SetFiller25AsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WOldInitialisedRecord
    public class WOldInitialisedRecord
    {
        private static int _size = 94;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler15, is_external=, is_static_class=False, static_prefix=
        private string _Filler15 ="";
        
        
        
        
        // [DEBUG] Field: IFundCode, is_external=, is_static_class=False, static_prefix=
        private string _IFundCode ="";
        
        
        
        
        // [DEBUG] Field: Filler16, is_external=, is_static_class=False, static_prefix=
        private string _Filler16 ="\",";
        
        
        
        
        // [DEBUG] Field: ISedolCode, is_external=, is_static_class=False, static_prefix=
        private string _ISedolCode ="";
        
        
        
        
        // [DEBUG] Field: Filler17, is_external=, is_static_class=False, static_prefix=
        private string _Filler17 =",\"";
        
        
        
        
        // [DEBUG] Field: ITransReference, is_external=, is_static_class=False, static_prefix=
        private string _ITransReference ="";
        
        
        
        
        // [DEBUG] Field: Filler18, is_external=, is_static_class=False, static_prefix=
        private string _Filler18 =",";
        
        
        
        
        // [DEBUG] Field: IBargainDate, is_external=, is_static_class=False, static_prefix=
        private string _IBargainDate ="";
        
        
        
        
        // [DEBUG] Field: Filler19, is_external=, is_static_class=False, static_prefix=
        private string _Filler19 ="\",";
        
        
        
        
        // [DEBUG] Field: IHolding, is_external=, is_static_class=False, static_prefix=
        private decimal _IHolding =0;
        
        
        
        
        // [DEBUG] Field: Filler20, is_external=, is_static_class=False, static_prefix=
        private string _Filler20 =",\"";
        
        
        
        
        // [DEBUG] Field: IMovementDesc, is_external=, is_static_class=False, static_prefix=
        private string _IMovementDesc ="";
        
        
        
        
        // [DEBUG] Field: Filler21, is_external=, is_static_class=False, static_prefix=
        private string _Filler21 ="\",";
        
        
        
        
        // [DEBUG] Field: ICost, is_external=, is_static_class=False, static_prefix=
        private decimal _ICost =0;
        
        
        
        
        // [DEBUG] Field: Filler22, is_external=, is_static_class=False, static_prefix=
        private string _Filler22 =",";
        
        
        
        
        // [DEBUG] Field: IBalance, is_external=, is_static_class=False, static_prefix=
        private decimal _IBalance =0;
        
        
        
        
        // [DEBUG] Field: Filler23, is_external=, is_static_class=False, static_prefix=
        private string _Filler23 =",\"";
        
        
        
        
        // [DEBUG] Field: IHoldingFlag, is_external=, is_static_class=False, static_prefix=
        private string _IHoldingFlag ="";
        
        
        
        
        // [DEBUG] Field: Filler24, is_external=, is_static_class=False, static_prefix=
        private string _Filler24 ="";
        
        
        
        
    public WOldInitialisedRecord() {}
    
    public WOldInitialisedRecord(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller15(data.Substring(offset, 0).Trim());
        offset += 0;
        SetIFundCode(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller16(data.Substring(offset, 0).Trim());
        offset += 0;
        SetISedolCode(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller17(data.Substring(offset, 0).Trim());
        offset += 0;
        SetITransReference(data.Substring(offset, 10).Trim());
        offset += 10;
        SetFiller18(data.Substring(offset, 0).Trim());
        offset += 0;
        SetIBargainDate(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller19(data.Substring(offset, 0).Trim());
        offset += 0;
        SetIHolding(PackedDecimalConverter.ToDecimal(data.Substring(offset, 18)));
        offset += 18;
        SetFiller20(data.Substring(offset, 0).Trim());
        offset += 0;
        SetIMovementDesc(data.Substring(offset, 30).Trim());
        offset += 30;
        SetFiller21(data.Substring(offset, 0).Trim());
        offset += 0;
        SetICost(PackedDecimalConverter.ToDecimal(data.Substring(offset, 18)));
        offset += 18;
        SetFiller22(data.Substring(offset, 0).Trim());
        offset += 0;
        SetIBalance(PackedDecimalConverter.ToDecimal(data.Substring(offset, 18)));
        offset += 18;
        SetFiller23(data.Substring(offset, 0).Trim());
        offset += 0;
        SetIHoldingFlag(data.Substring(offset, 0).Trim());
        offset += 0;
        SetFiller24(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetWOldInitialisedRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler15.PadRight(0));
        result.Append(_IFundCode.PadRight(0));
        result.Append(_Filler16.PadRight(0));
        result.Append(_ISedolCode.PadRight(0));
        result.Append(_Filler17.PadRight(0));
        result.Append(_ITransReference.PadRight(10));
        result.Append(_Filler18.PadRight(0));
        result.Append(_IBargainDate.PadRight(0));
        result.Append(_Filler19.PadRight(0));
        result.Append(_IHolding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler20.PadRight(0));
        result.Append(_IMovementDesc.PadRight(30));
        result.Append(_Filler21.PadRight(0));
        result.Append(_ICost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler22.PadRight(0));
        result.Append(_IBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture));
        result.Append(_Filler23.PadRight(0));
        result.Append(_IHoldingFlag.PadRight(0));
        result.Append(_Filler24.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetWOldInitialisedRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller15(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetIFundCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller16(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetISedolCode(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller17(extracted);
        }
        offset += 0;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetITransReference(extracted);
        }
        offset += 10;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller18(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetIBargainDate(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller19(extracted);
        }
        offset += 0;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetIHolding(parsedDec);
        }
        offset += 18;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller20(extracted);
        }
        offset += 0;
        if (offset + 30 <= data.Length)
        {
            string extracted = data.Substring(offset, 30).Trim();
            SetIMovementDesc(extracted);
        }
        offset += 30;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller21(extracted);
        }
        offset += 0;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetICost(parsedDec);
        }
        offset += 18;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller22(extracted);
        }
        offset += 0;
        if (offset + 18 <= data.Length)
        {
            string extracted = data.Substring(offset, 18).Trim();
            decimal parsedDec;
            if (decimal.TryParse(extracted, System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsedDec))
            SetIBalance(parsedDec);
        }
        offset += 18;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller23(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetIHoldingFlag(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetFiller24(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller15()
    {
        return _Filler15;
    }
    
    // Standard Setter
    public void SetFiller15(string value)
    {
        _Filler15 = value;
    }
    
    // Get<>AsString()
    public string GetFiller15AsString()
    {
        return _Filler15.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller15AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler15 = value;
    }
    
    // Standard Getter
    public string GetIFundCode()
    {
        return _IFundCode;
    }
    
    // Standard Setter
    public void SetIFundCode(string value)
    {
        _IFundCode = value;
    }
    
    // Get<>AsString()
    public string GetIFundCodeAsString()
    {
        return _IFundCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetIFundCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _IFundCode = value;
    }
    
    // Standard Getter
    public string GetFiller16()
    {
        return _Filler16;
    }
    
    // Standard Setter
    public void SetFiller16(string value)
    {
        _Filler16 = value;
    }
    
    // Get<>AsString()
    public string GetFiller16AsString()
    {
        return _Filler16.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller16AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler16 = value;
    }
    
    // Standard Getter
    public string GetISedolCode()
    {
        return _ISedolCode;
    }
    
    // Standard Setter
    public void SetISedolCode(string value)
    {
        _ISedolCode = value;
    }
    
    // Get<>AsString()
    public string GetISedolCodeAsString()
    {
        return _ISedolCode.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetISedolCodeAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ISedolCode = value;
    }
    
    // Standard Getter
    public string GetFiller17()
    {
        return _Filler17;
    }
    
    // Standard Setter
    public void SetFiller17(string value)
    {
        _Filler17 = value;
    }
    
    // Get<>AsString()
    public string GetFiller17AsString()
    {
        return _Filler17.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller17AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler17 = value;
    }
    
    // Standard Getter
    public string GetITransReference()
    {
        return _ITransReference;
    }
    
    // Standard Setter
    public void SetITransReference(string value)
    {
        _ITransReference = value;
    }
    
    // Get<>AsString()
    public string GetITransReferenceAsString()
    {
        return _ITransReference.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetITransReferenceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _ITransReference = value;
    }
    
    // Standard Getter
    public string GetFiller18()
    {
        return _Filler18;
    }
    
    // Standard Setter
    public void SetFiller18(string value)
    {
        _Filler18 = value;
    }
    
    // Get<>AsString()
    public string GetFiller18AsString()
    {
        return _Filler18.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller18AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler18 = value;
    }
    
    // Standard Getter
    public string GetIBargainDate()
    {
        return _IBargainDate;
    }
    
    // Standard Setter
    public void SetIBargainDate(string value)
    {
        _IBargainDate = value;
    }
    
    // Get<>AsString()
    public string GetIBargainDateAsString()
    {
        return _IBargainDate.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetIBargainDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _IBargainDate = value;
    }
    
    // Standard Getter
    public string GetFiller19()
    {
        return _Filler19;
    }
    
    // Standard Setter
    public void SetFiller19(string value)
    {
        _Filler19 = value;
    }
    
    // Get<>AsString()
    public string GetFiller19AsString()
    {
        return _Filler19.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller19AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler19 = value;
    }
    
    // Standard Getter
    public decimal GetIHolding()
    {
        return _IHolding;
    }
    
    // Standard Setter
    public void SetIHolding(decimal value)
    {
        _IHolding = value;
    }
    
    // Get<>AsString()
    public string GetIHoldingAsString()
    {
        return _IHolding.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetIHoldingAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _IHolding = parsed;
    }
    
    // Standard Getter
    public string GetFiller20()
    {
        return _Filler20;
    }
    
    // Standard Setter
    public void SetFiller20(string value)
    {
        _Filler20 = value;
    }
    
    // Get<>AsString()
    public string GetFiller20AsString()
    {
        return _Filler20.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller20AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler20 = value;
    }
    
    // Standard Getter
    public string GetIMovementDesc()
    {
        return _IMovementDesc;
    }
    
    // Standard Setter
    public void SetIMovementDesc(string value)
    {
        _IMovementDesc = value;
    }
    
    // Get<>AsString()
    public string GetIMovementDescAsString()
    {
        return _IMovementDesc.PadRight(30);
    }
    
    // Set<>AsString()
    public void SetIMovementDescAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _IMovementDesc = value;
    }
    
    // Standard Getter
    public string GetFiller21()
    {
        return _Filler21;
    }
    
    // Standard Setter
    public void SetFiller21(string value)
    {
        _Filler21 = value;
    }
    
    // Get<>AsString()
    public string GetFiller21AsString()
    {
        return _Filler21.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller21AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler21 = value;
    }
    
    // Standard Getter
    public decimal GetICost()
    {
        return _ICost;
    }
    
    // Standard Setter
    public void SetICost(decimal value)
    {
        _ICost = value;
    }
    
    // Get<>AsString()
    public string GetICostAsString()
    {
        return _ICost.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetICostAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _ICost = parsed;
    }
    
    // Standard Getter
    public string GetFiller22()
    {
        return _Filler22;
    }
    
    // Standard Setter
    public void SetFiller22(string value)
    {
        _Filler22 = value;
    }
    
    // Get<>AsString()
    public string GetFiller22AsString()
    {
        return _Filler22.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller22AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler22 = value;
    }
    
    // Standard Getter
    public decimal GetIBalance()
    {
        return _IBalance;
    }
    
    // Standard Setter
    public void SetIBalance(decimal value)
    {
        _IBalance = value;
    }
    
    // Get<>AsString()
    public string GetIBalanceAsString()
    {
        return _IBalance.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
    }
    
    // Set<>AsString()
    public void SetIBalanceAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        decimal parsed;
        if (decimal.TryParse(value.Trim(), System.Globalization.NumberStyles.Number, System.Globalization.CultureInfo.InvariantCulture, out parsed)) _IBalance = parsed;
    }
    
    // Standard Getter
    public string GetFiller23()
    {
        return _Filler23;
    }
    
    // Standard Setter
    public void SetFiller23(string value)
    {
        _Filler23 = value;
    }
    
    // Get<>AsString()
    public string GetFiller23AsString()
    {
        return _Filler23.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller23AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler23 = value;
    }
    
    // Standard Getter
    public string GetIHoldingFlag()
    {
        return _IHoldingFlag;
    }
    
    // Standard Setter
    public void SetIHoldingFlag(string value)
    {
        _IHoldingFlag = value;
    }
    
    // Get<>AsString()
    public string GetIHoldingFlagAsString()
    {
        return _IHoldingFlag.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetIHoldingFlagAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _IHoldingFlag = value;
    }
    
    // Standard Getter
    public string GetFiller24()
    {
        return _Filler24;
    }
    
    // Standard Setter
    public void SetFiller24(string value)
    {
        _Filler24 = value;
    }
    
    // Get<>AsString()
    public string GetFiller24AsString()
    {
        return _Filler24.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetFiller24AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler24 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
// Nested Class: Filler25
public class Filler25
{
    private static int _size = 0;
    
    // Fields in the class
    
    
    // [DEBUG] Field: Filler26, is_external=, is_static_class=False, static_prefix=
    private string _Filler26 =",\"";
    
    
    
    
    // [DEBUG] Field: IHoldingSign, is_external=, is_static_class=False, static_prefix=
    private string _IHoldingSign ="";
    
    
    
    
    // [DEBUG] Field: Filler27, is_external=, is_static_class=False, static_prefix=
    private string _Filler27 ="";
    
    
    
    
public Filler25() {}

public Filler25(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetFiller26(data.Substring(offset, 0).Trim());
    offset += 0;
    SetIHoldingSign(data.Substring(offset, 0).Trim());
    offset += 0;
    SetFiller27(data.Substring(offset, 0).Trim());
    offset += 0;
    
}

// Serialization methods
public string GetFiller25AsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_Filler26.PadRight(0));
    result.Append(_IHoldingSign.PadRight(0));
    result.Append(_Filler27.PadRight(0));
    
    return result.ToString();
}

public void SetFiller25AsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller26(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetIHoldingSign(extracted);
    }
    offset += 0;
    if (offset + 0 <= data.Length)
    {
        string extracted = data.Substring(offset, 0).Trim();
        SetFiller27(extracted);
    }
    offset += 0;
}

// Getter and Setter methods

// Standard Getter
public string GetFiller26()
{
    return _Filler26;
}

// Standard Setter
public void SetFiller26(string value)
{
    _Filler26 = value;
}

// Get<>AsString()
public string GetFiller26AsString()
{
    return _Filler26.PadRight(0);
}

// Set<>AsString()
public void SetFiller26AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler26 = value;
}

// Standard Getter
public string GetIHoldingSign()
{
    return _IHoldingSign;
}

// Standard Setter
public void SetIHoldingSign(string value)
{
    _IHoldingSign = value;
}

// Get<>AsString()
public string GetIHoldingSignAsString()
{
    return _IHoldingSign.PadRight(0);
}

// Set<>AsString()
public void SetIHoldingSignAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _IHoldingSign = value;
}

// Standard Getter
public string GetFiller27()
{
    return _Filler27;
}

// Standard Setter
public void SetFiller27(string value)
{
    _Filler27 = value;
}

// Get<>AsString()
public string GetFiller27AsString()
{
    return _Filler27.PadRight(0);
}

// Set<>AsString()
public void SetFiller27AsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _Filler27 = value;
}



public static int GetSize()
{
    return _size;
}

}
}

}}