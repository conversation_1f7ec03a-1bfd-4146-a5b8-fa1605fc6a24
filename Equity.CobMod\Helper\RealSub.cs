﻿// RealSub.cs
using System;
using Equity.CobMod.Models.RealSub;
using EquityProject.CgtabortPGM;
using EquityProject.RealSubDTO;


namespace EquityProject.RealSubPGM
{ 
/// <summary>
/// Implements the logic of the COBOL REALSUB program.
/// This service initializes, processes, and cleans up resources related to
/// Realised Report column selection.
/// </summary>
public class RealSub
{
    // Internal state variables from COBOL's WORKING-STORAGE SECTION
    private string _programName = "REALSUB "; // PIC X(8)
    private bool _quitProgram = false; // Corresponds to W-QUIT-PROGRAM, 88 QUIT-PROGRAM
    private bool _d8WasOpen = false;   // Corresponds to W-D8-STATUS-FLAG, 88 D8-WAS-OPEN
    private bool _d8WasClosed = false; // Corresponds to W-D8-STATUS-FLAG, 88 D8-WAS-CLOSED

    // W-PANEL-ID, W-CRT, W-CURSOR-POSITION, W-INPUT-VALID are UI-related and not directly used in this logic.
    // W-SUB-1, W-SUB-2 are loop counters, handled by <PERSON># for loops.

    // Dependencies (injected or instantiated)
 //   private Cgtfiles _cgtFilesService;
    private Cgtabort _cgtAbortService;
    private D8Record _d8Record; // The parameter record read from file
    private RealSubScreenData _realSubScreenData; // For P-SUBSCRIPT

    // CommonLinkage instance (often passed around, but managed here for simplicity)
    private CommonLinkage _commonLinkage;

    /// <summary>
    /// Initializes a new instance of the <see cref="RealSub"/> class.
    /// </summary>
    /// <param name="cgtFilesService">The service for CGTFILES operations (mock or real).</param>
    /// <param name="cgtAbortService">The service for CGTABORT operations (mock or real).</param>
   /* public RealSub(Cgtfiles cgtFilesService, Cgtabort cgtAbortService)
    {
       // _cgtFilesService = cgtFilesService ?? throw new ArgumentNullException(nameof(cgtFilesService));
        _cgtAbortService = cgtAbortService ?? throw new ArgumentNullException(nameof(cgtAbortService));

        _d8Record = new D8Record();
        _realSubScreenData = new RealSubScreenData();
        _commonLinkage = new CommonLinkage(); // Initialize common linkage
    }*/

    /// <summary>
    /// Executes the main logic of the REALSUB program.
    /// Corresponds to the PROCEDURE DIVISION USING W-SUBSCRIPTS-TABLE.
    /// </summary>
    /// <param name="subscriptsTable">The DTO for W-SUBSCRIPTS-TABLE, used for input/output.</param>
    public void Execute(RealSubParameters subscriptsTable)
    {
        // COBOL: A-MAIN SECTION.
        B_Initialise();
        // COBOL: PERFORM C-PROCESS UNTIL QUIT-PROGRAM.
        // In this COBOL snippet, C-PROCESS sets QUIT-PROGRAM to true immediately,
        // so it will only run once.
        while (!_quitProgram)
        {
            C_Process(subscriptsTable);
        }
        D_End();

        // COBOL: A-EXIT. EXIT PROGRAM. STOP RUN.
        // Method simply returns.
    }

    /// <summary>
    /// Corresponds to COBOL's B-INITIALISE SECTION.
    /// Handles opening the parameter file and reading the D8 record.
    /// </summary>
    private void B_Initialise()
    {
        Console.WriteLine("\n[REALSUB]: B-INITIALISE Section.");

        // COBOL: Open Parameter file
        // COBOL: MOVE OPEN-I-O TO L-FILE-ACTION
        // COBOL: MOVE PARAMETER-FILE TO L-FILE-NAME
        // COBOL: PERFORM X-CALL-CGTFILES
        var cgtFilesLinkage = new CgtFilesLinkage
        {
            FileAction = "IO ", // OPEN-I-O (from 78-level constant)
            FileName = "M-PARAM " // PARAMETER-FILE (from 78-level constant)
        };
        string fileRecordArea = new string(' ', 1870); // L-FILE-RECORD-AREA (PIC X(1870))

      //  _cgtFilesService.CallCgtFiles(cgtFilesLinkage, ref fileRecordArea, _commonLinkage);

        // COBOL: IF SUCCESSFUL ... ELSE IF OPEN-OK ... ELSE PERFORM X7-ABORT
        if (cgtFilesLinkage.InterpretedReturnCode == CgtFileReturnCode.Successful)
        {
            _d8WasClosed = true; // This means *this program* opened it, so it should close it.
            Console.WriteLine("  Parameter file opened successfully (was closed).");
        }
        else if (cgtFilesLinkage.IsOpenOk) // Covers "00", "05", "41"
        {
            _d8WasOpen = true; // This means it was already open, so this program won't close it.
            Console.WriteLine("  Parameter file was already open.");
        }
        else
        {
            X7_Abort(cgtFilesLinkage); // Abort if open fails
        }

        // COBOL: Read Parameter record
        // COBOL: MOVE "CGT" TO L-FILE-RECORD-AREA (key for read)
        // COBOL: MOVE READ-RECORD TO L-FILE-ACTION
        // COBOL: PERFORM X-CALL-CGTFILES
        fileRecordArea = "CGT"; // Key for the read (L-FILE-RECORD-AREA is used for key here)
        cgtFilesLinkage.FileAction = "RD "; // READ-RECORD (from 78-level constant)
        //_cgtFilesService.CallCgtFiles(cgtFilesLinkage, ref fileRecordArea, _commonLinkage);

        // COBOL: IF NOT SUCCESSFUL PERFORM X7-ABORT ELSE MOVE L-FILE-RECORD-AREA TO D8-RECORD
        if (cgtFilesLinkage.InterpretedReturnCode != CgtFileReturnCode.Successful)
        {
            X7_Abort(cgtFilesLinkage); // Abort if read fails
        }
        else
        {
            // In COBOL, L-FILE-RECORD-AREA (the raw string) is moved to D8-RECORD.
            // In C#, we'll ask the mock service to give us its internal D8Record
            // which it has pre-populated for testing.
           // _d8Record = _cgtFilesService.GetMockD8Record();
            // If fileRecordArea contained the actual serialized data, you'd parse it here:
            // _d8Record.FromRawData(fileRecordArea);
            Console.WriteLine("  D8-RECORD read successfully.");
        }

        // COBOL: Initialise Subscripts
        // COBOL: PERFORM VARYING W-SUB-1 FROM 1 BY 1 UNTIL W-SUB-1 > 8
        // COBOL: MOVE D8-REPORT-SELECTION(W-SUB-1) TO P-SUBSCRIPT(W-SUB-1)
        for (int wSub1 = 0; wSub1 < 8; wSub1++) // C# 0-based index for List
        {
            // D8-REPORT-SELECTION is PIC X(1) OCCURS 8
            // P-SUBSCRIPT is PIC X(1) OCCURS 8 (part of RealSubScreenData)
            _realSubScreenData.PSubscripts[wSub1] = _d8Record.ReportSelections[wSub1];
            Console.WriteLine($"  Initialising P-SUBSCRIPT({wSub1 + 1}) with '{_realSubScreenData.PSubscripts[wSub1]}'");
        }

        // COBOL: B-EXIT. EXIT.
    }

    /// <summary>
    /// Corresponds to COBOL's C-PROCESS SECTION.
    /// </summary>
    /// <param name="subscriptsTable">The DTO for W-SUBSCRIPTS-TABLE.</param>
    private void C_Process(RealSubParameters subscriptsTable)
    {
        Console.WriteLine("\n[REALSUB]: C-PROCESS Section.");
        C2_SetReturnLinkage(subscriptsTable);
        _quitProgram = true; // COBOL: MOVE 'Y' TO W-QUIT-PROGRAM. This makes the main loop run only once.
        // COBOL: C-EXIT. EXIT.
    }

    /// <summary>
    /// Corresponds to COBOL's C2-SET-RETURN-LINKAGE SECTION.
    /// Transforms column numbers to correct subscripts.
    /// </summary>
    /// <param name="subscriptsTable">The DTO for W-SUBSCRIPTS-TABLE.</param>
    private void C2_SetReturnLinkage(RealSubParameters subscriptsTable)
    {
        Console.WriteLine("[REALSUB]: C2-SET-RETURN-LINKAGE Section.");
        // COBOL: PERFORM VARYING W-SUB-1 FROM 1 BY 1 UNTIL W-SUB-1 > 8
        for (int wSub1 = 1; wSub1 <= 8; wSub1++) // COBOL 1-based index for loop
        {
            // COBOL: EVALUATE P-SUBSCRIPT(W-SUB-1)
            // P-SUBSCRIPT is PIC X(1), so convert to int for comparison
            // Use int.TryParse for robustness if input might not be numeric
            if (!int.TryParse(_realSubScreenData.PSubscripts[wSub1 - 1].ToString(), out int pSubscriptValue))
            {
                Console.Error.WriteLine($"Warning: P-SUBSCRIPT({wSub1}) contains non-numeric value '{_realSubScreenData.PSubscripts[wSub1 - 1]}'. Skipping.");
                continue;
            }

            switch (pSubscriptValue)
            {
                // COBOL: WHEN 1 MOVE W-SUB-1 TO SUB-1
                case 1: subscriptsTable[1] = wSub1; break;
                case 2: subscriptsTable[2] = wSub1; break;
                case 3: subscriptsTable[3] = wSub1; break;
                case 4: subscriptsTable[4] = wSub1; break;
                case 5: subscriptsTable[5] = wSub1; break;
                case 6: subscriptsTable[6] = wSub1; break;
                // COBOL snippet only shows WHEN 1 to 6. Other values are implicitly ignored.
                default:
                    Console.WriteLine($"  P-SUBSCRIPT({wSub1})='{pSubscriptValue}' is not 1-6. No SUB-X updated.");
                    break;
            }
            Console.WriteLine($"  P-SUBSCRIPT({wSub1})='{pSubscriptValue}' -> SUB-{pSubscriptValue}={wSub1}");
        }
        // COBOL: C2-EXIT. EXIT.
    }

    /// <summary>
    /// Corresponds to COBOL's D-END SECTION.
    /// Handles closing the parameter file if this program opened it.
    /// </summary>
    private void D_End()
    {
        Console.WriteLine("\n[REALSUB]: D-END Section.");
        // COBOL: IF D8-WAS-CLOSED
        if (_d8WasClosed)
        {
            // COBOL: MOVE CLOSE-FILE TO L-FILE-ACTION
            // COBOL: PERFORM X-CALL-CGTFILES
            var cgtFilesLinkage = new CgtFilesLinkage
            {
                FileAction = "CL ", // CLOSE-FILE (from 78-level constant)
                FileName = "M-PARAM " // PARAMETER-FILE (from 78-level constant)
            };
            string fileRecordArea = new string(' ', 1870); // Dummy for L-FILE-RECORD-AREA
            //_cgtFilesService.CallCgtFiles(cgtFilesLinkage, ref fileRecordArea, _commonLinkage);
            Console.WriteLine("  Parameter file closed by REALSUB.");
        }
        else
        {
            Console.WriteLine("  Parameter file not closed by REALSUB (was already open).");
        }
        // COBOL: D-EXIT. EXIT.
    }

    /// <summary>
    /// Corresponds to COBOL's X7-ABORT SECTION.
    /// Calls the CGTABORT subroutine.
    /// </summary>
    /// <param name="cgtFilesLinkage">The CGTFILES linkage containing the error status.</param>
    private void X7_Abort(CgtFilesLinkage cgtFilesLinkage)
    {
        Console.WriteLine("[REALSUB]: X7-ABORT Section.");
        _cgtAbortService.GetGvar().GetCgtfilesLinkage().SetLFileNameAsString(cgtFilesLinkage.FileName);
        _cgtAbortService.GetGvar().GetCgtfilesLinkage().SetLFileActionAsString(cgtFilesLinkage.FileAction);
       
        
       /* var cgtAbortLinkage = new CgtAbortLinkage
        {
            AbortFileStatus = cgtFilesLinkage.RawFileReturnCode,
            AbortProgramName = _programName,
            AbortFileName = cgtFilesLinkage.FileName
        };*/
        _cgtAbortService.Run(_cgtAbortService.GetGvar(), _cgtAbortService.GetIvar());
        // The mock CGTABORTService throws an ApplicationException, which will stop execution here.
    }
}
}
