using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing WiInterface Data Structure

public class WiInterface
{
    private static int _size = 182;
    // [DEBUG] Class: WiInterface, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler351, is_external=, is_static_class=False, static_prefix=
    private string _Filler351 ="CALL INTERFACES=";
    
    
    
    
    // [DEBUG] Field: Filler352, is_external=, is_static_class=False, static_prefix=
    private string _Filler352 ="ELCGTMFD INTERFACE======";
    
    
    
    
    // [DEBUG] Field: WiMfdisp2, is_external=, is_static_class=False, static_prefix=
    private int _WiMfdisp2 =0;
    
    
    
    
    // [DEBUG] Field: WiMfdisp3, is_external=, is_static_class=False, static_prefix=
    private WiMfdisp3 _WiMfdisp3 = new WiMfdisp3();
    
    
    
    
    
    // Serialization methods
    public string GetWiInterfaceAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler351.PadRight(16));
        result.Append(_Filler352.PadRight(24));
        result.Append(_WiMfdisp2.ToString().PadLeft(8, '0'));
        result.Append(_WiMfdisp3.GetWiMfdisp3AsString());
        
        return result.ToString();
    }
    
    public void SetWiInterfaceAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 16 <= data.Length)
        {
            string extracted = data.Substring(offset, 16).Trim();
            SetFiller351(extracted);
        }
        offset += 16;
        if (offset + 24 <= data.Length)
        {
            string extracted = data.Substring(offset, 24).Trim();
            SetFiller352(extracted);
        }
        offset += 24;
        if (offset + 8 <= data.Length)
        {
            string extracted = data.Substring(offset, 8).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWiMfdisp2(parsedInt);
        }
        offset += 8;
        if (offset + 134 <= data.Length)
        {
            _WiMfdisp3.SetWiMfdisp3AsString(data.Substring(offset, 134));
        }
        else
        {
            _WiMfdisp3.SetWiMfdisp3AsString(data.Substring(offset));
        }
        offset += 134;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetWiInterfaceAsString();
    }
    // Set<>String Override function
    public void SetWiInterface(string value)
    {
        SetWiInterfaceAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller351()
    {
        return _Filler351;
    }
    
    // Standard Setter
    public void SetFiller351(string value)
    {
        _Filler351 = value;
    }
    
    // Get<>AsString()
    public string GetFiller351AsString()
    {
        return _Filler351.PadRight(16);
    }
    
    // Set<>AsString()
    public void SetFiller351AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler351 = value;
    }
    
    // Standard Getter
    public string GetFiller352()
    {
        return _Filler352;
    }
    
    // Standard Setter
    public void SetFiller352(string value)
    {
        _Filler352 = value;
    }
    
    // Get<>AsString()
    public string GetFiller352AsString()
    {
        return _Filler352.PadRight(24);
    }
    
    // Set<>AsString()
    public void SetFiller352AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler352 = value;
    }
    
    // Standard Getter
    public int GetWiMfdisp2()
    {
        return _WiMfdisp2;
    }
    
    // Standard Setter
    public void SetWiMfdisp2(int value)
    {
        _WiMfdisp2 = value;
    }
    
    // Get<>AsString()
    public string GetWiMfdisp2AsString()
    {
        return _WiMfdisp2.ToString().PadLeft(8, '0');
    }
    
    // Set<>AsString()
    public void SetWiMfdisp2AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WiMfdisp2 = parsed;
    }
    
    // Standard Getter
    public WiMfdisp3 GetWiMfdisp3()
    {
        return _WiMfdisp3;
    }
    
    // Standard Setter
    public void SetWiMfdisp3(WiMfdisp3 value)
    {
        _WiMfdisp3 = value;
    }
    
    // Get<>AsString()
    public string GetWiMfdisp3AsString()
    {
        return _WiMfdisp3 != null ? _WiMfdisp3.GetWiMfdisp3AsString() : "";
    }
    
    // Set<>AsString()
    public void SetWiMfdisp3AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WiMfdisp3 == null)
        {
            _WiMfdisp3 = new WiMfdisp3();
        }
        _WiMfdisp3.SetWiMfdisp3AsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetWiMfdisp3(string value)
    {
        _WiMfdisp3.SetWiMfdisp3AsString(value);
    }
    // Nested Class: WiMfdisp3
    public class WiMfdisp3
    {
        private static int _size = 134;
        
        // Fields in the class
        
        
        // [DEBUG] Field: WiMfdispIndicator, is_external=, is_static_class=False, static_prefix=
        private string _WiMfdispIndicator ="X";
        
        
        
        
        // [DEBUG] Field: WiMfdispTitle, is_external=, is_static_class=False, static_prefix=
        private WiMfdisp3.WiMfdispTitle _WiMfdispTitle = new WiMfdisp3.WiMfdispTitle();
        
        
        
        
    public WiMfdisp3() {}
    
    public WiMfdisp3(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetWiMfdispIndicator(data.Substring(offset, 1).Trim());
        offset += 1;
        _WiMfdispTitle.SetWiMfdispTitleAsString(data.Substring(offset, WiMfdispTitle.GetSize()));
        offset += 133;
        
    }
    
    // Serialization methods
    public string GetWiMfdisp3AsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_WiMfdispIndicator.PadRight(1));
        result.Append(_WiMfdispTitle.GetWiMfdispTitleAsString());
        
        return result.ToString();
    }
    
    public void SetWiMfdisp3AsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetWiMfdispIndicator(extracted);
        }
        offset += 1;
        if (offset + 133 <= data.Length)
        {
            _WiMfdispTitle.SetWiMfdispTitleAsString(data.Substring(offset, 133));
        }
        else
        {
            _WiMfdispTitle.SetWiMfdispTitleAsString(data.Substring(offset));
        }
        offset += 133;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetWiMfdispIndicator()
    {
        return _WiMfdispIndicator;
    }
    
    // Standard Setter
    public void SetWiMfdispIndicator(string value)
    {
        _WiMfdispIndicator = value;
    }
    
    // Get<>AsString()
    public string GetWiMfdispIndicatorAsString()
    {
        return _WiMfdispIndicator.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetWiMfdispIndicatorAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WiMfdispIndicator = value;
    }
    
    // Standard Getter
    public WiMfdispTitle GetWiMfdispTitle()
    {
        return _WiMfdispTitle;
    }
    
    // Standard Setter
    public void SetWiMfdispTitle(WiMfdispTitle value)
    {
        _WiMfdispTitle = value;
    }
    
    // Get<>AsString()
    public string GetWiMfdispTitleAsString()
    {
        return _WiMfdispTitle != null ? _WiMfdispTitle.GetWiMfdispTitleAsString() : "";
    }
    
    // Set<>AsString()
    public void SetWiMfdispTitleAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_WiMfdispTitle == null)
        {
            _WiMfdispTitle = new WiMfdispTitle();
        }
        _WiMfdispTitle.SetWiMfdispTitleAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: WiMfdispTitle
    public class WiMfdispTitle
    {
        private static int _size = 133;
        
        // Fields in the class
        
        
        // [DEBUG] Field: Filler353, is_external=, is_static_class=False, static_prefix=
        private string _Filler353 ="1ELCGTS20 DUMPING TABLES";
        
        
        
        
        // [DEBUG] Field: WiMfdTableNo, is_external=, is_static_class=False, static_prefix=
        private int _WiMfdTableNo =0;
        
        
        
        
        // [DEBUG] Field: Filler354, is_external=, is_static_class=False, static_prefix=
        private string _Filler354 ="    STAMP:";
        
        
        
        
        // [DEBUG] Field: WiMfdStamp, is_external=, is_static_class=False, static_prefix=
        private string _WiMfdStamp ="";
        
        
        
        
        // [DEBUG] Field: Filler355, is_external=, is_static_class=False, static_prefix=
        private string _Filler355 =" ";
        
        
        
        
    public WiMfdispTitle() {}
    
    public WiMfdispTitle(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetFiller353(data.Substring(offset, 26).Trim());
        offset += 26;
        SetWiMfdTableNo(int.Parse(data.Substring(offset, 2).Trim()));
        offset += 2;
        SetFiller354(data.Substring(offset, 10).Trim());
        offset += 10;
        SetWiMfdStamp(data.Substring(offset, 14).Trim());
        offset += 14;
        SetFiller355(data.Substring(offset, 81).Trim());
        offset += 81;
        
    }
    
    // Serialization methods
    public string GetWiMfdispTitleAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler353.PadRight(26));
        result.Append(_WiMfdTableNo.ToString().PadLeft(2, '0'));
        result.Append(_Filler354.PadRight(10));
        result.Append(_WiMfdStamp.PadRight(14));
        result.Append(_Filler355.PadRight(81));
        
        return result.ToString();
    }
    
    public void SetWiMfdispTitleAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 26 <= data.Length)
        {
            string extracted = data.Substring(offset, 26).Trim();
            SetFiller353(extracted);
        }
        offset += 26;
        if (offset + 2 <= data.Length)
        {
            string extracted = data.Substring(offset, 2).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetWiMfdTableNo(parsedInt);
        }
        offset += 2;
        if (offset + 10 <= data.Length)
        {
            string extracted = data.Substring(offset, 10).Trim();
            SetFiller354(extracted);
        }
        offset += 10;
        if (offset + 14 <= data.Length)
        {
            string extracted = data.Substring(offset, 14).Trim();
            SetWiMfdStamp(extracted);
        }
        offset += 14;
        if (offset + 81 <= data.Length)
        {
            string extracted = data.Substring(offset, 81).Trim();
            SetFiller355(extracted);
        }
        offset += 81;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller353()
    {
        return _Filler353;
    }
    
    // Standard Setter
    public void SetFiller353(string value)
    {
        _Filler353 = value;
    }
    
    // Get<>AsString()
    public string GetFiller353AsString()
    {
        return _Filler353.PadRight(26);
    }
    
    // Set<>AsString()
    public void SetFiller353AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler353 = value;
    }
    
    // Standard Getter
    public int GetWiMfdTableNo()
    {
        return _WiMfdTableNo;
    }
    
    // Standard Setter
    public void SetWiMfdTableNo(int value)
    {
        _WiMfdTableNo = value;
    }
    
    // Get<>AsString()
    public string GetWiMfdTableNoAsString()
    {
        return _WiMfdTableNo.ToString().PadLeft(2, '0');
    }
    
    // Set<>AsString()
    public void SetWiMfdTableNoAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _WiMfdTableNo = parsed;
    }
    
    // Standard Getter
    public string GetFiller354()
    {
        return _Filler354;
    }
    
    // Standard Setter
    public void SetFiller354(string value)
    {
        _Filler354 = value;
    }
    
    // Get<>AsString()
    public string GetFiller354AsString()
    {
        return _Filler354.PadRight(10);
    }
    
    // Set<>AsString()
    public void SetFiller354AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler354 = value;
    }
    
    // Standard Getter
    public string GetWiMfdStamp()
    {
        return _WiMfdStamp;
    }
    
    // Standard Setter
    public void SetWiMfdStamp(string value)
    {
        _WiMfdStamp = value;
    }
    
    // Get<>AsString()
    public string GetWiMfdStampAsString()
    {
        return _WiMfdStamp.PadRight(14);
    }
    
    // Set<>AsString()
    public void SetWiMfdStampAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _WiMfdStamp = value;
    }
    
    // Standard Getter
    public string GetFiller355()
    {
        return _Filler355;
    }
    
    // Standard Setter
    public void SetFiller355(string value)
    {
        _Filler355 = value;
    }
    
    // Get<>AsString()
    public string GetFiller355AsString()
    {
        return _Filler355.PadRight(81);
    }
    
    // Set<>AsString()
    public void SetFiller355AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler355 = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}

}}