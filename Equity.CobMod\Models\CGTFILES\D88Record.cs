using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D88Record Data Structure

public class D88Record
{
    private static int _size = 151;
    // [DEBUG] Class: D88Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D88PrintControl, is_external=, is_static_class=False, static_prefix=
    private string _D88PrintControl ="";
    
    
    
    
    // [DEBUG] Field: D88ReportLine, is_external=, is_static_class=False, static_prefix=
    private string _D88ReportLine ="";
    
    
    
    
    
    // Serialization methods
    public string GetD88RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D88PrintControl.PadRight(1));
        result.Append(_D88ReportLine.PadRight(150));
        
        return result.ToString();
    }
    
    public void SetD88RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 1 <= data.Length)
        {
            string extracted = data.Substring(offset, 1).Trim();
            SetD88PrintControl(extracted);
        }
        offset += 1;
        if (offset + 150 <= data.Length)
        {
            string extracted = data.Substring(offset, 150).Trim();
            SetD88ReportLine(extracted);
        }
        offset += 150;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD88RecordAsString();
    }
    // Set<>String Override function
    public void SetD88Record(string value)
    {
        SetD88RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD88PrintControl()
    {
        return _D88PrintControl;
    }
    
    // Standard Setter
    public void SetD88PrintControl(string value)
    {
        _D88PrintControl = value;
    }
    
    // Get<>AsString()
    public string GetD88PrintControlAsString()
    {
        return _D88PrintControl.PadRight(1);
    }
    
    // Set<>AsString()
    public void SetD88PrintControlAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D88PrintControl = value;
    }
    
    // Standard Getter
    public string GetD88ReportLine()
    {
        return _D88ReportLine;
    }
    
    // Standard Setter
    public void SetD88ReportLine(string value)
    {
        _D88ReportLine = value;
    }
    
    // Get<>AsString()
    public string GetD88ReportLineAsString()
    {
        return _D88ReportLine.PadRight(150);
    }
    
    // Set<>AsString()
    public void SetD88ReportLineAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D88ReportLine = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}