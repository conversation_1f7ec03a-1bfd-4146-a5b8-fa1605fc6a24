using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.CgtfilesDTO
{// DTO class representing D68TrailerRecord Data Structure

public class D68TrailerRecord
{
    private static int _size = 53;
    // [DEBUG] Class: D68TrailerRecord, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: Filler79, is_external=, is_static_class=False, static_prefix=
    private string _Filler79 ="";
    
    
    // 88-level condition checks for Filler79
    public bool IsTrailerFound()
    {
        if (this._Filler79 == "'9999MICRO CGT FUND TRAILER'") return true;
        return false;
    }
    
    
    // [DEBUG] Field: D68TrailerCount, is_external=, is_static_class=False, static_prefix=
    private string _D68TrailerCount ="";
    
    
    
    
    // [DEBUG] Field: Filler80, is_external=, is_static_class=False, static_prefix=
    private string _Filler80 ="";
    
    
    
    
    
    // Serialization methods
    public string GetD68TrailerRecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_Filler79.PadRight(44));
        result.Append(_D68TrailerCount.PadRight(6));
        result.Append(_Filler80.PadRight(3));
        
        return result.ToString();
    }
    
    public void SetD68TrailerRecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 44 <= data.Length)
        {
            string extracted = data.Substring(offset, 44).Trim();
            SetFiller79(extracted);
        }
        offset += 44;
        if (offset + 6 <= data.Length)
        {
            string extracted = data.Substring(offset, 6).Trim();
            SetD68TrailerCount(extracted);
        }
        offset += 6;
        if (offset + 3 <= data.Length)
        {
            string extracted = data.Substring(offset, 3).Trim();
            SetFiller80(extracted);
        }
        offset += 3;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD68TrailerRecordAsString();
    }
    // Set<>String Override function
    public void SetD68TrailerRecord(string value)
    {
        SetD68TrailerRecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetFiller79()
    {
        return _Filler79;
    }
    
    // Standard Setter
    public void SetFiller79(string value)
    {
        _Filler79 = value;
    }
    
    // Get<>AsString()
    public string GetFiller79AsString()
    {
        return _Filler79.PadRight(44);
    }
    
    // Set<>AsString()
    public void SetFiller79AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler79 = value;
    }
    
    // Standard Getter
    public string GetD68TrailerCount()
    {
        return _D68TrailerCount;
    }
    
    // Standard Setter
    public void SetD68TrailerCount(string value)
    {
        _D68TrailerCount = value;
    }
    
    // Get<>AsString()
    public string GetD68TrailerCountAsString()
    {
        return _D68TrailerCount.PadRight(6);
    }
    
    // Set<>AsString()
    public void SetD68TrailerCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D68TrailerCount = value;
    }
    
    // Standard Getter
    public string GetFiller80()
    {
        return _Filler80;
    }
    
    // Standard Setter
    public void SetFiller80(string value)
    {
        _Filler80 = value;
    }
    
    // Get<>AsString()
    public string GetFiller80AsString()
    {
        return _Filler80.PadRight(3);
    }
    
    // Set<>AsString()
    public void SetFiller80AsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _Filler80 = value;
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    
}}