using System;
using System.Text;
using EquityProject.CommonDTO;
namespace EquityProject.Elcgts20DTO
{// DTO class representing D133Record Data Structure

public class D133Record
{
    private static int _size = 23;
    // [DEBUG] Class: D133Record, IsStaticClass: False
    // Fields in the class
    
    
    // [DEBUG] Field: D133Key, is_external=, is_static_class=False, static_prefix=
    private D133Key _D133Key = new D133Key();
    
    
    
    
    // [DEBUG] Field: D133BusinessAsset, is_external=, is_static_class=False, static_prefix=
    private string _D133BusinessAsset ="";
    
    
    
    
    // [DEBUG] Field: D133UpdateCount, is_external=, is_static_class=False, static_prefix=
    private int _D133UpdateCount =0;
    
    
    
    
    // [DEBUG] Field: D133InverseKey, is_external=, is_static_class=False, static_prefix=
    private D133InverseKey _D133InverseKey = new D133InverseKey();
    
    
    
    
    
    // Serialization methods
    public string GetD133RecordAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D133Key.GetD133KeyAsString());
        result.Append(_D133BusinessAsset.PadRight(0));
        result.Append(_D133UpdateCount.ToString().PadLeft(4, '0'));
        result.Append(_D133InverseKey.GetD133InverseKeyAsString());
        
        return result.ToString();
    }
    
    public void SetD133RecordAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            _D133Key.SetD133KeyAsString(data.Substring(offset, 0));
        }
        else
        {
            _D133Key.SetD133KeyAsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD133BusinessAsset(extracted);
        }
        offset += 0;
        if (offset + 4 <= data.Length)
        {
            string extracted = data.Substring(offset, 4).Trim();
            int parsedInt;
            if (int.TryParse(extracted, out parsedInt)) SetD133UpdateCount(parsedInt);
        }
        offset += 4;
        if (offset + 19 <= data.Length)
        {
            _D133InverseKey.SetD133InverseKeyAsString(data.Substring(offset, 19));
        }
        else
        {
            _D133InverseKey.SetD133InverseKeyAsString(data.Substring(offset));
        }
        offset += 19;
    }
    // ToString Override function
    public override string ToString()
    {
        return GetD133RecordAsString();
    }
    // Set<>String Override function
    public void SetD133Record(string value)
    {
        SetD133RecordAsString(value);
    }
    
    
    // Getter and Setter methods
    
    // Standard Getter
    public D133Key GetD133Key()
    {
        return _D133Key;
    }
    
    // Standard Setter
    public void SetD133Key(D133Key value)
    {
        _D133Key = value;
    }
    
    // Get<>AsString()
    public string GetD133KeyAsString()
    {
        return _D133Key != null ? _D133Key.GetD133KeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD133KeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D133Key == null)
        {
            _D133Key = new D133Key();
        }
        _D133Key.SetD133KeyAsString(value);
    }
    
    // Standard Getter
    public string GetD133BusinessAsset()
    {
        return _D133BusinessAsset;
    }
    
    // Standard Setter
    public void SetD133BusinessAsset(string value)
    {
        _D133BusinessAsset = value;
    }
    
    // Get<>AsString()
    public string GetD133BusinessAssetAsString()
    {
        return _D133BusinessAsset.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD133BusinessAssetAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D133BusinessAsset = value;
    }
    
    // Standard Getter
    public int GetD133UpdateCount()
    {
        return _D133UpdateCount;
    }
    
    // Standard Setter
    public void SetD133UpdateCount(int value)
    {
        _D133UpdateCount = value;
    }
    
    // Get<>AsString()
    public string GetD133UpdateCountAsString()
    {
        return _D133UpdateCount.ToString().PadLeft(4, '0');
    }
    
    // Set<>AsString()
    public void SetD133UpdateCountAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        int parsed;
        if (int.TryParse(value.Trim(), out parsed)) _D133UpdateCount = parsed;
    }
    
    // Standard Getter
    public D133InverseKey GetD133InverseKey()
    {
        return _D133InverseKey;
    }
    
    // Standard Setter
    public void SetD133InverseKey(D133InverseKey value)
    {
        _D133InverseKey = value;
    }
    
    // Get<>AsString()
    public string GetD133InverseKeyAsString()
    {
        return _D133InverseKey != null ? _D133InverseKey.GetD133InverseKeyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD133InverseKeyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D133InverseKey == null)
        {
            _D133InverseKey = new D133InverseKey();
        }
        _D133InverseKey.SetD133InverseKeyAsString(value);
    }
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Set<>String Override function (Nested)
    public void SetD133Key(string value)
    {
        _D133Key.SetD133KeyAsString(value);
    }
    // Nested Class: D133Key
    public class D133Key
    {
        private static int _size = 0;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D133Fund, is_external=, is_static_class=False, static_prefix=
        private string _D133Fund ="";
        
        
        
        
        // [DEBUG] Field: D133Sedol, is_external=, is_static_class=False, static_prefix=
        private string _D133Sedol ="";
        
        
        
        
        // [DEBUG] Field: D133FromDate, is_external=, is_static_class=False, static_prefix=
        private D133Key.D133FromDate _D133FromDate = new D133Key.D133FromDate();
        
        
        
        
    public D133Key() {}
    
    public D133Key(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD133Fund(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD133Sedol(data.Substring(offset, 0).Trim());
        offset += 0;
        _D133FromDate.SetD133FromDateAsString(data.Substring(offset, D133FromDate.GetSize()));
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD133KeyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D133Fund.PadRight(0));
        result.Append(_D133Sedol.PadRight(0));
        result.Append(_D133FromDate.GetD133FromDateAsString());
        
        return result.ToString();
    }
    
    public void SetD133KeyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD133Fund(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD133Sedol(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            _D133FromDate.SetD133FromDateAsString(data.Substring(offset, 0));
        }
        else
        {
            _D133FromDate.SetD133FromDateAsString(data.Substring(offset));
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD133Fund()
    {
        return _D133Fund;
    }
    
    // Standard Setter
    public void SetD133Fund(string value)
    {
        _D133Fund = value;
    }
    
    // Get<>AsString()
    public string GetD133FundAsString()
    {
        return _D133Fund.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD133FundAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D133Fund = value;
    }
    
    // Standard Getter
    public string GetD133Sedol()
    {
        return _D133Sedol;
    }
    
    // Standard Setter
    public void SetD133Sedol(string value)
    {
        _D133Sedol = value;
    }
    
    // Get<>AsString()
    public string GetD133SedolAsString()
    {
        return _D133Sedol.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD133SedolAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D133Sedol = value;
    }
    
    // Standard Getter
    public D133FromDate GetD133FromDate()
    {
        return _D133FromDate;
    }
    
    // Standard Setter
    public void SetD133FromDate(D133FromDate value)
    {
        _D133FromDate = value;
    }
    
    // Get<>AsString()
    public string GetD133FromDateAsString()
    {
        return _D133FromDate != null ? _D133FromDate.GetD133FromDateAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD133FromDateAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D133FromDate == null)
        {
            _D133FromDate = new D133FromDate();
        }
        _D133FromDate.SetD133FromDateAsString(value);
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D133FromDate
    public class D133FromDate
    {
        private static int _size = 0;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D133Ccyy, is_external=, is_static_class=False, static_prefix=
        private D133FromDate.D133Ccyy _D133Ccyy = new D133FromDate.D133Ccyy();
        
        
        
        
        // [DEBUG] Field: D133Mm, is_external=, is_static_class=False, static_prefix=
        private string _D133Mm ="";
        
        
        
        
        // [DEBUG] Field: D133Dd, is_external=, is_static_class=False, static_prefix=
        private string _D133Dd ="";
        
        
        
        
    public D133FromDate() {}
    
    public D133FromDate(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        _D133Ccyy.SetD133CcyyAsString(data.Substring(offset, D133Ccyy.GetSize()));
        offset += 0;
        SetD133Mm(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD133Dd(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD133FromDateAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D133Ccyy.GetD133CcyyAsString());
        result.Append(_D133Mm.PadRight(0));
        result.Append(_D133Dd.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD133FromDateAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            _D133Ccyy.SetD133CcyyAsString(data.Substring(offset, 0));
        }
        else
        {
            _D133Ccyy.SetD133CcyyAsString(data.Substring(offset));
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD133Mm(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD133Dd(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public D133Ccyy GetD133Ccyy()
    {
        return _D133Ccyy;
    }
    
    // Standard Setter
    public void SetD133Ccyy(D133Ccyy value)
    {
        _D133Ccyy = value;
    }
    
    // Get<>AsString()
    public string GetD133CcyyAsString()
    {
        return _D133Ccyy != null ? _D133Ccyy.GetD133CcyyAsString() : "";
    }
    
    // Set<>AsString()
    public void SetD133CcyyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        if (_D133Ccyy == null)
        {
            _D133Ccyy = new D133Ccyy();
        }
        _D133Ccyy.SetD133CcyyAsString(value);
    }
    
    // Standard Getter
    public string GetD133Mm()
    {
        return _D133Mm;
    }
    
    // Standard Setter
    public void SetD133Mm(string value)
    {
        _D133Mm = value;
    }
    
    // Get<>AsString()
    public string GetD133MmAsString()
    {
        return _D133Mm.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD133MmAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D133Mm = value;
    }
    
    // Standard Getter
    public string GetD133Dd()
    {
        return _D133Dd;
    }
    
    // Standard Setter
    public void SetD133Dd(string value)
    {
        _D133Dd = value;
    }
    
    // Get<>AsString()
    public string GetD133DdAsString()
    {
        return _D133Dd.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD133DdAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D133Dd = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
    // Nested Class: D133Ccyy
    public class D133Ccyy
    {
        private static int _size = 0;
        
        // Fields in the class
        
        
        // [DEBUG] Field: D133Cc, is_external=, is_static_class=False, static_prefix=
        private string _D133Cc ="";
        
        
        
        
        // [DEBUG] Field: D133Yy, is_external=, is_static_class=False, static_prefix=
        private string _D133Yy ="";
        
        
        
        
    public D133Ccyy() {}
    
    public D133Ccyy(string data)
    {
        if (data.Length < _size) data = data.PadRight(_size);
        // Constructor: Initializes fields from data
        int offset = 0;
        SetD133Cc(data.Substring(offset, 0).Trim());
        offset += 0;
        SetD133Yy(data.Substring(offset, 0).Trim());
        offset += 0;
        
    }
    
    // Serialization methods
    public string GetD133CcyyAsString()
    {
        StringBuilder result = new StringBuilder();
        
        result.Append(_D133Cc.PadRight(0));
        result.Append(_D133Yy.PadRight(0));
        
        return result.ToString();
    }
    
    public void SetD133CcyyAsString(string data)
    {
        if (!string.IsNullOrEmpty(data) && data.Length < _size)
        {
            data = data.PadRight(_size);
        }
        
        int offset = 0;
        
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD133Cc(extracted);
        }
        offset += 0;
        if (offset + 0 <= data.Length)
        {
            string extracted = data.Substring(offset, 0).Trim();
            SetD133Yy(extracted);
        }
        offset += 0;
    }
    
    // Getter and Setter methods
    
    // Standard Getter
    public string GetD133Cc()
    {
        return _D133Cc;
    }
    
    // Standard Setter
    public void SetD133Cc(string value)
    {
        _D133Cc = value;
    }
    
    // Get<>AsString()
    public string GetD133CcAsString()
    {
        return _D133Cc.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD133CcAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D133Cc = value;
    }
    
    // Standard Getter
    public string GetD133Yy()
    {
        return _D133Yy;
    }
    
    // Standard Setter
    public void SetD133Yy(string value)
    {
        _D133Yy = value;
    }
    
    // Get<>AsString()
    public string GetD133YyAsString()
    {
        return _D133Yy.PadRight(0);
    }
    
    // Set<>AsString()
    public void SetD133YyAsString(string value)
    {
        if (string.IsNullOrEmpty(value)) return;
        
        _D133Yy = value;
    }
    
    
    
    public static int GetSize()
    {
        return _size;
    }
    
}
}
}
// Set<>String Override function (Nested)
public void SetD133InverseKey(string value)
{
    _D133InverseKey.SetD133InverseKeyAsString(value);
}
// Nested Class: D133InverseKey
public class D133InverseKey
{
    private static int _size = 19;
    
    // Fields in the class
    
    
    // [DEBUG] Field: D133InverseFundSedolDate, is_external=, is_static_class=False, static_prefix=
    private string _D133InverseFundSedolDate ="";
    
    
    
    
public D133InverseKey() {}

public D133InverseKey(string data)
{
    if (data.Length < _size) data = data.PadRight(_size);
    // Constructor: Initializes fields from data
    int offset = 0;
    SetD133InverseFundSedolDate(data.Substring(offset, 19).Trim());
    offset += 19;
    
}

// Serialization methods
public string GetD133InverseKeyAsString()
{
    StringBuilder result = new StringBuilder();
    
    result.Append(_D133InverseFundSedolDate.PadRight(19));
    
    return result.ToString();
}

public void SetD133InverseKeyAsString(string data)
{
    if (!string.IsNullOrEmpty(data) && data.Length < _size)
    {
        data = data.PadRight(_size);
    }
    
    int offset = 0;
    
    if (offset + 19 <= data.Length)
    {
        string extracted = data.Substring(offset, 19).Trim();
        SetD133InverseFundSedolDate(extracted);
    }
    offset += 19;
}

// Getter and Setter methods

// Standard Getter
public string GetD133InverseFundSedolDate()
{
    return _D133InverseFundSedolDate;
}

// Standard Setter
public void SetD133InverseFundSedolDate(string value)
{
    _D133InverseFundSedolDate = value;
}

// Get<>AsString()
public string GetD133InverseFundSedolDateAsString()
{
    return _D133InverseFundSedolDate.PadRight(19);
}

// Set<>AsString()
public void SetD133InverseFundSedolDateAsString(string value)
{
    if (string.IsNullOrEmpty(value)) return;
    
    _D133InverseFundSedolDate = value;
}



public static int GetSize()
{
    return _size;
}

}

}}